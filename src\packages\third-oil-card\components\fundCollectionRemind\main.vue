<template>
    <view class="uni-popupWrap bg-fff fl-column border-rad-8" style>
        <div class="title te-center font-18 color-333 weight-bold">资金转出前提条件</div>
        <div class="explain font-12 color-333 weight-400">
            1. 该卡的备用金可用余额+昆仑e享卡账户余额不可大于5000元。
            <br />2. 该功能下同一张卡仅允许资金转出一次。如需再次转出，需将加油卡绑定到该账户下后进行转出。
            <br />3. 加油卡备用金账户如有未完成的交易，不允许进行资金转出。
            <br />4. 资金转出时，加油卡备用金可用余额归集至昆仑e享卡账户中，加油卡备用金积分余额会一同归集至积分账户中。
        </div>
    </view>
</template>
<script>
export default {
    name: 'fund-transfer-out',
    components: {},
    data() {
        return {};
    },
    computed: {},
    onLoad() {},
    mounted() {},
    methods: {
        // TODO html测试数据
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.uni-popupWrap {
    background-color: #fff;
    // padding: 15px;
    // width: 75%;
    // height: ;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    border-radius: 8px;
    .title {
        margin-top: 19px;
        margin-bottom: 14px;
        text-align: center;
        height: 23px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }
    .explain {
        // text-align: center;
        margin: 0 auto;
        // width: 85%;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        // margin-right: 20px;
        // margin-left: 20px;
        // margin-bottom: 19px;
    }
    .tips {
        background: #f5f5f5;
        // width: 90%;
        margin: 23rpx auto;
        .tips-content {
            padding: 9px 10px;
            .reminder {
                color: #333333;
                font-weight: bold;
                font-size: 12px;
            }
            .explain2 {
                color: #333333;
                font-size: 12px;
                .span1 {
                    color: #e65023;
                    font-size: 12px;
                }
                .span2 {
                    color: #e64f22;
                    font-size: 12px;
                }
            }
        }
    }
    .btn {
        width: 100%;
        border-top: 1px solid #eee;
        height: 44px;
        border: 0 0 8px 8px;
        display: flex;
        margin-top: 10px;
        flex: 1;
        .allow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #333333;
        }
        .notAllow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            border-right: 1px solid #eee;
        }
    }
}
</style>
