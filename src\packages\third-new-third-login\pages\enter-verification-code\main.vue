<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="one_lick_login">
        <img class="header_icon" src="../../images/back_x.png" :style="TOP" @click="back()" alt />
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>

        <div class="padding_input">
            <div class="main_heading">请输入验证码</div>
            <div class="subheading font-12 color-999">已发送至{{ phoneNum }}</div>
            <ZjCode @returnresult="returnresult" ref="code"></ZjCode>
            <div class="short_message font-14 color-E64F22" v-if="show" @click="resend">重新获取验证码</div>
            <div class="short_message font-14 color-E64F22" v-if="!show">{{ count }}秒后重新获取</div>
        </div>
        <loginRiskControl v-if="realNameDialogFlag || facePop" @verificationPassed="verifSuccess"></loginRiskControl>
        <ImageVerCode v-if="sliderIsShow" :phone="loginParams.mobile" @changeShow="changeShow" @sendSuccess="sendSuccess"></ImageVerCode>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import ZjCode from '../../components/third-verify/main';
import { mapGetters, mapState } from 'vuex';
import platform from '@/s-kit/js/platform';
import loginRiskControl from '../../../../components/loginRiskControl/main.vue';
import ImageVerCode from '../../components/Image-verification-code/main.vue';

// import { Toast } from 'mint-ui';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { ZjCode, ImageVerCode, loginRiskControl },
    name: 'one-click-login',
    data() {
        return {
            // 验证码倒计时
            count: 60,
            // 定时器
            timer: null,
            // 输入的验证码
            smsVerifyCode: '',
            // 手机号
            phoneNum: '',
            imageCheckShow: false,
            //系统导航栏高度
            systemBar: '',
            // 登录参数
            loginParams: {},
            // 是否显示重新获取验证码
            show: false,
            // 滑块验证是否显示
            sliderIsShow: false,
        };
    },
    computed: {
        ...mapState({
            token: state => state.token,
            token3: state => state.token3,
            // #ifdef MP-WEIXIN
            // 风控输入表单是否显示
            realNameDialogFlag: state => state.thirdLogin.realNameDialogFlag,
            // 按钮置灰变亮标识
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            // 风控验证人脸
            facePop: state => state.thirdIndex.facePop,
            // 中转页参数
            officialAccountParams: state => state.location.officialAccountParams,
            // #endif
        }),
        TOP() {
            // 系统导航栏
            return `margin-top: ${14 + Number(this.systemBar)}px`;
        },
    },
    // created() {
    //     this.getSmsEvent();
    // },
    onLoad(options) {
        if (options?.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            this.loginParams = JSON.parse(JSON.stringify(params));
            console.log(params, '验证码页面获取值');
            this.phoneNum = params.mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
        }
        // 倒计时开始
        this.getSmsEvent();
        // 获取系统导航栏高度
        this.getTheSystemNavigationBar();
    },

    watch: {
        // imageCheckShow() {
        //     if (!this.imageCheckShow) {
        //         this.count = 60;
        //         this.show = false;
        //         this.getSmsEvent();
        //     }
        // },
    },
    methods: {
        /**
         * @description  : 获取系统导航栏高度
         * @return        {*}
         */
        getTheSystemNavigationBar() {
            // #ifndef MP-MPAAS
            let systemInfo = uni.getSystemInfoSync();
            console.log('systemInfo---', systemInfo);
            if (platform.isAlipay || platform.isTouTiao) {
                console.log('systemInfo.statusBarHeight----', systemInfo.statusBarHeight);
                this.systemBar = systemInfo.statusBarHeight + Number(this.navH);
            } else {
                this.systemBar = systemInfo.statusBarHeight;
            }
            // #endif
        },
        /**
         * @description  : 重新获取验证码显示滑块
         * @return        {*}
         */
        resend() {
            // this.changeShow(true);
            this.sliderIsShow = true;
            this.imageCheckShow = true;
        },
        /**
         * @description  : 返回上一页
         * @return        {*}
         */
        back() {
            uni.navigateBack({
                detail: 1,
            });
        },
        /**
         * @description  : 关闭滑块验证
         * @return        {*}
         */
        changeShow(val) {
            this.sliderIsShow = false;
        },
        /**
         * @description  : 滑块验证通过发送验证码成功
         * @return        {*}
         */
        sendSuccess() {
            this.sliderIsShow = false;
            this.imageCheckShow = false;
            this.$refs.code.claerCode();
            // 开始倒计时
            this.countFn();
        },
        /**
         * @description  : 验证码输入完成
         * @return        {*}
         */
        returnresult(code) {
            this.smsVerifyCode = code;
            if (code.length == 6) {
                console.log('执行了吗');
                // 执行登录
                this.loginClick();
            }
        },
        /**
         * @description  : 倒计时开始
         * @return        {*}
         */
        getSmsEvent: function () {
            if (this.count !== 60) {
                return;
            }
            // 执行倒计时
            this.countFn();
        },
        /**
         * @description  : 执行倒计时
         * @return        {*}
         */
        countFn() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    console.log('===========');
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        /**
         * @description  : 验证码输入完成后进行登录
         * @return        {*}
         */
        async loginClick() {
            setTimeout(async () => {
                if (this.smsVerifyCode === '') {
                    uni.showToast({
                        title: '请输入短信验证码',
                        icon: 'none',
                        duration: 2000,
                    });
                    return false;
                }
                this.loginParams.messageCode = this.smsVerifyCode;
                this.loginParams.type = '1';
                let res = await this.$store.dispatch('thirdLoginFun', this.loginParams);
                if (!res.success || res.result == 'fail') {
                    this.$refs.code.claerCode();
                } else if (res.data.newMember) {
                    console.log(this.officialAccountParams, '一键登录测试跳转');
                   this.$bus.$emit('callbackLogin', JSON.stringify(res)); //触发自定义事件
                }
            }, 0);
        },
        /**
         * @description  : 风控验证成功
         * @return        {*}
         */
        verifSuccess() {
            this.$store.commit('setRealNameDialogFlag', false);
            console.log('测试执行成功分发了吗');
            this.$store.dispatch('loginSuccessful');
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
};
</script>

<style lang="scss" scoped>
.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;

    .header_icon {
        position: absolute;
        width: 16px;
        height: 16px;
        left: 16px;
    }

    .padding_input {
        padding: 16px;

        .main_heading {
            text-align: center;
            margin-bottom: 6px;
        }

        .subheading {
            text-align: center;
        }

        .short_message {
            margin-top: 12px;
        }
    }
}

.operator {
    margin-bottom: 16px;
    font-size: 12px;
    color: #999;
}

input {
    height: 51px;
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    font-size: 21px;
    font-weight: 600;
    color: #333333;
    line-height: 30px;
    margin-bottom: 12px;
    padding-left: 20px;
    box-sizing: border-box;
}

.agreement_div {
    color: #999999;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.argreement_rules {
    color: #e64f22;
}

.phone_input {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
}
</style>
