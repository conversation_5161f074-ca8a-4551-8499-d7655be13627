<template>
    <div class="content" v-if="show">
        <div class="content_div flex_style" @click="toMarketingDetail()">
            <div class="content_div_left flex_style">
                <img class="left_icon" v-if="activityList.activityType == 19" src="../../../image/marketing-couponIcon.png" alt="" />
                <img class="left_icon" v-if="activityList.activityType == 18" src="../../../image/marketing-share.png" alt="" />
                <img class="left_icon" v-if="activityList.activityType == 20" src="../../../image/marketing-prize.png" alt="" />
                <div class="div_left_text">{{ activityList.title }}</div>
            </div>
            <div class="content_div_right flex_style">
                <div class="div_right_text" v-if="activityList.couponNum">{{ activityList.couponNum }}张券</div>
                <img class="right_icon" src="../../../image/backIcon.png" alt="" />
            </div>
        </div>
    </div>
</template>
<script>
import { businessActivityQuery, businessActivityConfigList } from '../../../js/v3-http/https3/order/index.js';
import { stationListApi } from '../../../js/v3-http/https3/oilStationService/index.js';
import { mapState, mapGetters } from 'vuex';
export default {
    props: {
        text: String,
    },
    data() {
        return {
            num: null,
            oilStation: [],
            activityInfo: [],
            activityList: [],
            show: false,
        };
    },
    computed: {
        ...mapState({
            latV3: state => state.locationV3_app.latV3,
            lonV3: state => state.locationV3_app.lonV3,
        }),
    },
    mounted() {
        if (this.latV3 && this.lonV3) {
            this.getBusinessActivityQuery(this.lonV3, this.latV3);
        } else {
            this.initLocation();
        }
    },
    methods: {
        // 跳转营销详情
        /*营销活动类型：
            18-裂变分享；
            19-领券活动；
            20-抽奖活动；
         */
        toMarketingDetail() {
            // 领券
            let params = {
                flowNo: this.activityList.activityFlowNo,
            };
            if (this.activityList.activityType == 19) {
                // #ifdef MP-MPAAS
                let url = `/pages/couponCollection/main?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
                this.$sKit.layer.cubeMini(url, '4778018036739045');
                // #endif
                // #ifdef MP-WEIXIN
                let url = '/packages/thirdMarketing/pages/couponCollection/main';
                this.$sKit.layer.useRouter(url, params);
                // #endif
                // #ifdef MP-ALIPAY
                let url = '/packages/thirdMarketing/pages/couponCollection/main';
                this.$sKit.layer.useRouter(url, params);
                // #endif
            } else if (this.activityList.activityType == 20) {
                // 抽奖
                // #ifdef MP-MPAAS
                let url = `/pages/luckyDraw/main?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
                this.$sKit.layer.cubeMini(url, '4778018036739045');
                // #endif
                // #ifdef MP-WEIXIN
                let url = '/packages/thirdMarketing/pages/luckyDraw/main';
                this.$sKit.layer.useRouter(url, params);
                // #endif
            } else if (this.activityList.activityType == 18) {
                // 裂变
                // #ifdef MP-WEIXIN
                let url = '/packages/thirdMarketing/pages/fissionSharing/main';
                this.$sKit.layer.useRouter(url, params);
                // #endif
            }
        },
        initLocation() {
            console.log('callback=========location');
            this.$store.dispatch('initLocationV3_app', {
                callback: res => {
                    console.log(res, 'callback======location');
                    if (res.longitude && res.latitude) {
                        this.getBusinessActivityQuery(res.longitude, res.latitude);
                    }
                },
                type: 'onlyLocation',
            });
        },
        // 获取营销入口信息
        async getBusinessActivityQuery(longitude, latitude) {
            if (!longitude || !latitude) return;
            let params = {
                longitude: longitude,
                latitude: latitude,
            };
            let res = await businessActivityQuery(params, { isCustomErr: true });
            if (res.success) {
                if (res.data.activityList && res.data.activityList.length > 0) {
                    this.activityList = res.data.activityList[0];
                    // #ifdef MP-MPAAS
                    if (this.activityList.activityType == 18) {
                        this.show = false;
                    } else {
                        this.show = true;
                    }
                    // #endif
                    // #ifdef MP-WEIXIN
                    this.show = true;
                    // #endif
                    // #ifdef MP-ALIPAY
                    this.show = this.activityList.activityType == 19 ? true : false;
                    // #endif
                }
            } else {
                this.show = false;
            }
            this.$emit('getMarketCouponShow', this.show);
        },
    },
};
</script>
<style scoped lang="scss">
.content {
    width: 100%;
}
.content_div {
    height: 48px;
    width: 100%;
    // margin: 0 15px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 12px;
    // padding: 0 15px;
    padding: 0 15px;
    margin-bottom: 15px;
    justify-content: space-between;
    .content_div_left {
        width: 70%;
        .left_icon {
            width: 24px;
            height: 24px;
            margin-right: 9px;
        }
        .div_left_text {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
        }
    }
    .content_div_right {
        width: 30%;
        justify-content: flex-end;
        .right_icon {
            width: 16px;
            height: 16px;
            margin-left: 2px;
        }
        .div_right_text {
            font-weight: 500;
            font-size: 14px;
            color: #ff4000;
            line-height: 20px;
        }
    }
}
.flex_style {
    display: flex;
    align-items: center;
}
</style>
