// 车生活跳转配置文件
import { subPath } from '../../project.config';
export default {
    // 加油
    oil: {
        v2: '/pages/refuel/refuel-index/refuel-index',
        v3: subPath + '/pages/thirdHome/main',
    },
    // 钱包
    wallet: {
        v2: '/pages/eOilCard/eOilCard',
        v3: subPath + '/pages/thirdHome/main',
    },
    // 钱包 - 开通
    walletOpen: {
        v2: '/pages/eOilCard/eOilCard',
        v3: subPath + '/packages/third-electronic-wallet/pages/wallet-add-form/main',
    },
    // 钱包 - 充值
    walletRecharge: {
        v2: '/pages/eOilCard/eOilCard',
        v3: subPath + '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
    },
    // 油卡 - 列表
    oilCardList: {
        v2: '/pages/eOilCard/eOilCard',
        v3: subPath + '/packages/third-oil-card/pages/my-card/main',
    },
    // 订单 - 列表
    orderList: {
        v2: '/pages/myConsume/consume-list/consume-list',
        v3: subPath + '/pages/thirdHome/main',
    },
    // 发票 - 列表
    invoiceList: {
        v2: '/pages/eInvoice/myInvoice/myInvoice',
        v3: subPath + '/packages/third-invoice/pages/home/<USER>',
    },
    // 订单 - 详情
    orderDetail: {
        v2: '/pages/myConsume/consume-list/consume-list',
        v3: subPath + '/packages/third-order/pages/order-detail/main',
    },
    // 我的
    mine: {
        v2: '/pages/mine/mine',
        v3: subPath + '/pages/thirdHome/main',
    },
    // 电子券 - 列表
    couponList: {
        v2: '/pages/eCoupon/eCoupon',
        v3: subPath + '/packages/third-coupon-module/pages/coupon-list/main',
    },
    // 营销领券
    couponCollection: {
        v2: '/pages/index/index',
        v3: subPath + '/packages/thirdMarketing/pages/couponCollection/main',
        // v3:'/packages/thirdMarketing/pages/couponCollection/main'
    },
};
