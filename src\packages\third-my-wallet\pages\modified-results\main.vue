<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details p-bf bg-fff">
            <zj-navbar title="修改支付密码" :border-bottom="false"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div bg-fff">
                    <!-- <img :src="amount  >  0 ? '../../images/recharge.png' :'../../images/consumption.png'" alt /> -->
                    <div class="fl-column fl-al-jus-cen">
                        <img src="../../images/success_img.png" alt />
                        <span class="amount font-16 color-333 weight-bold">修改成功</span>
                    </div>
                </div>

                <div class="btn_div fl-jus-bet">
                    <div class="finish_verification btn-plain color-E64F22 font-16" @click="backClick()">返回首页</div>
                    <div class="finish_verification primary-btn color-fff font-16" @click="getCharge()">去加油</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
// import { rechargeStatusQuery } from '../api/wallet'
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'modified-results',
    data() {
        return {};
    },
    mounted() {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-bottom: 49px;
            padding-top: 60px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 26px;
            }

            .amount {
                line-height: 23px;
            }

            .text {
                line-height: 23px;
            }
        }
    }
}

.btn_div {
    font-size: 15px;
    display: flex;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
