Component({
    properties: {
        // 这里定义了innerText属性，属性值可以在组件使用时指定
        send_coupon_params: {
            type: Array,
            default: [],
        },
        sign: {
            type: String,
            default: '',
        },
        send_coupon_merchant: {
            type: String,
            default: '',
        },
        suggest_immediate_use: {
            type: Boolean,
            default: false,
        },
    },
    data: {},
    methods: {
        // 这里是一个自定义方法
        sendcoupon: function (res) {
            this.triggerEvent('sendcoupon', res.detail);
        },
        userconfirm: function (res) {
            this.triggerEvent('userconfirm', res.detail);
        },
    },
});
