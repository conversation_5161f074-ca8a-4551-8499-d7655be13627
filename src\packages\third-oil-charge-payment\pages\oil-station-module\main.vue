<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="p-bf fl-column bg-fff">
            <zj-navbar :border-bottom="false" title="网点导航" :height="44"></zj-navbar>
            <div class="navigation-map" id="oil-tab">
                <!-- 地图 -->
                <map
                    :style="'height: calc(100vh - ' + (isIphoneX ? navH + 34 : navH) + 'px - 280px);'"
                    :scale="Number(scale_app)"
                    :markers="showMarkerArrV3_app"
                    show-location
                    :latitude="mapCenterLatV3"
                    :longitude="mapCenterLonV3"
                    @callouttap="clickCallOut"
                    :setting="mapSetting"
                    @markertap="clickMarker"
                    :enable-rotate="false"
                    :enable-overlooking="false"
                >
                    <!-- #ifdef MP-WEIXIN -->
                    <cover-view slot="callout" v-if="isCustomCallout">
                        <cover-view
                            class="marker-view fl-row fl-al-cen"
                            :marker-id="item.id"
                            v-for="item in showMarkerArrV3_app"
                            :key="item.id"
                        >
                            <cover-view class="marker-lable-view fl-row fl-al-cen">
                                <cover-image v-if="item.isFirst" class="navigation" :src="mapImgClosest" alt=""></cover-image>
                                <cover-image v-else class="navigation" :src="mapImg" alt=""></cover-image>
                                <cover-view class="fl-column mar-left-4">
                                    <cover-view class="marker-name weight-600 font-12 color-000">
                                        {{ item.orgName }}
                                    </cover-view>
                                    <cover-view class="fl-row pa-t-5 fl-al-cen">
                                        <cover-view class="marker-text font-10 color-333">距离您</cover-view>
                                        <cover-view class="marker-text font-10 color-E64F22">{{ item.distance }}km </cover-view>
                                        <!-- <cover-view
                                            class="marker-118920 font-10 color-118920"
                                            v-if="item.stationStatus == 20 || item.stationStatus == 10"
                                        >
                                            正常营业
                                        </cover-view>
                                        <cover-view
                                            class="marker-E02020 font-10 color-E02020"
                                            v-if="item.stationStatus == 30 || item.stationStatus == 50"
                                        >
                                            正常营业
                                        </cover-view> -->
                                        <!-- <cover-view class="l-h28 font-10 color-333">， 驾车约</cover-view>
                                    <cover-view class="l-h28 font-10 color-E64F22">4min</cover-view>-->
                                    </cover-view>
                                </cover-view>
                            </cover-view>
                        </cover-view>
                    </cover-view>
                    <!-- #endif -->
                </map>
                <!-- 定位悬停按钮 -->
                <div class="location-icon" @click="clickLocation" :style="{ bottom: iconBottom + 'px' }">
                    <img :src="icon" alt="" />
                </div>
                <div class="dot-div">
                    <navigationDot
                        class="popup-view"
                        ref="navigationDot"
                        @getIconBottom="getIconBottom"
                        @clickNaviStateionFa="clickNaviStateionFa"
                        :hideRefuel="hideRefuel"
                    ></navigationDot>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import navigationDot from '../../components/search-module/navigation-dot.vue';
import projectConfig from '../../../../../project.config';
import zfbMap from '@/mixins/zfb-map';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi, zfbMap],
    components: {
        navigationDot,
    },
    data() {
        return {
            topTab: 'navigation',
            mapHeight: 0,
            mapWidth: 0,
            icon: require('../../image/currentLocation-2.png'),
            mapImgClosest: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/stationClosest.png',
            mapImg: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/station.png',
            iconBottom: 0,
            navH: 63, // 导航高度
            isIphoneX: false, // 是否是苹果x及以上
            mapSetting: {
                // #ifdef MP-MPAAS
                // 手势
                gestureEnable: 1,
                // 比例尺
                showScale: 1,
                // 指南针
                showCompass: 0,
                // 双手下滑
                tiltGesturesEnabled: 1,
                // 交通路况展示
                trafficEnabled: 0,
                // 地图POI信息
                showMapText: 1,
                // #endif
            },
            hideRefuel: false,
            isCustomCallout: false,
            // oilIcon: require('../../image/aircraft.png'),
            mapCtx: null,
        };
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
        //只刷新位置不获取油站
        // this.$store.dispatch("initLocationV3_app", {type: 'onlyLocation',})
        this.isCustomCallout = true;
        // #ifdef MP-MPAAS
        this.getSelectMarkerV3();
        // #endif
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {
        // this.$nextTick(() => {
        // this.setMapHeight();
        // })
        // 获取位置信息并初始化地图中心点
        this.initLocation();
    },
    onLoad(options) {
        // 电子券详情页距离当前油站大于500米时显示的查看更多网点跳转携带的参数(隐藏去加油按钮)
        this.hideRefuel = JSON.parse(decodeURIComponent(options.data));
        let systemInfo = uni.getSystemInfoSync();
        this.stateH = systemInfo.statusBarHeight;
        this.system = systemInfo.system.indexOf('iOS') > -1;
        this.navH = 44 + systemInfo.statusBarHeight;
        let name = 'iPhone X';
        if (systemInfo.model.indexOf(name) > -1) {
            this.isIphoneX = true;
        }
    },
    onReady() {
        // this.setMapHeight()
    },
    methods: {
        /**
         * @description  : 获取icon定位的bottom
         * @param         {*} bottom:
         * @return        {*}
         */
        getIconBottom(bottom) {
            this.iconBottom = bottom;
        },
        /**
         * @description  : 点击油站信息
         * @return        {*}
         */
        clickNaviStateionFa() {
            this.$refs.navigationDot.clickNaviStateion();
        },
        /**
         * @description  : 拿到地理位置的油站信息
         * @return        {*}
         */
        getSelectMarkerV3() {
            this.$cnpcBridge.getValueToNative('Define_Selected_Station', value => {
                if (value) {
                    this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                        marker: JSON.parse(decodeURIComponent(value)),
                        upLocation: true,
                        suc: { scene: 'site' },
                    });
                }
            });
        },
        // setMapHeight() {
        //   // 获取窗口高度
        //   setTimeout(() => {
        //     let infos = uni.createSelectorQuery().in(this).select(".navigation-map")
        //     console.log('infos---',infos)
        //   }, 1000);
        //   // setTimeout(() => {
        //     // uni.createSelectorQuery()
        //     //   .in(this)
        //     //   .select(".navigation-map")
        //     //   .boundingClientRect((data) => {
        //     //     console.log('00000000', data)
        //     //     this.mapHeight = data.height;
        //     //     this.mapWidth = data.width
        //     //     console.log('mapWidth', data.width, data.height)
        //     //   })
        //     //   .exec();
        //   // }, 1000);

        // },
        /**
         * @description  : 对应气泡点击事件
         * @return        {*}
         */
        clickCallOut(e) {
            console.log('e-----', e);

            let markerId = e.detail.markerId;
            let item = this.showMarkerArrV3_app.find(item => {
                return item.id == markerId;
            });
            // #ifdef MP-MPAAS
            this.$cnpcBridge.openLocation({
                latitude: this.selectMarkerV3.latitude,
                longitude: this.selectMarkerV3.longitude,
                name: this.selectMarkerV3.orgName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
            });
            // #endif
            // #ifndef MP-MPAAS
            uni.openLocation({
                latitude: Number(this.selectMarkerV3.latitude),
                longitude: Number(this.selectMarkerV3.longitude),
                name: this.selectMarkerV3.orgName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
            });
            // #endif
        },
        /**
         * @description  : 点击重新定位点击事件
         * @return        {*}
         */
        clickLocation() {
             this.initLocation();
        },
        /**
         * @description  : 定位获取周边油站
         * @return        {*}
         */
        initLocation(){
            // #ifdef MP-MPAAS
            this.$cnpcBridge.checkPermission().then(res=>{
                if (!res.appStatus){
                    this.$cnpcBridge.confirmDialog({
                        title:'位置权限使用说明',
                        message:'根据您的位置信息获取您附近的加油站网点信息服务,是否开启？',
                        confirmBtnText:'去开启'
                    }).then(()=>{
                        this.$cnpcBridge.openPermissions({
                            code: 'location',
                            explain: "位置权限使用说明",
                            detail: "使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。"}).then(res=>{
                            if (res){
                                this.$store.dispatch('initLocationV3_app', {
                                    scene: 'site',
                                });
                            }
                        })
                    })
                } else {
                    //有定位权限
                    this.$store.dispatch('initLocationV3_app', {
                        scene: 'site',
                    });
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            this.$store.dispatch('initLocationV3_app', {
                // #ifdef MP-WEIXIN
                callback: res => {
                    this.$nextTick(() => {
                        this.mapCtx = uni.createMapContext('map');
                        this.mapCtx.moveToLocation({ latitude: res.mapCenterLatV3, longitude: res.mapCenterLonV3 });
                    });
                },
                type: 'initMap',
                // #endif
                scene: 'site',
            });
            // #endif
        },

        /**
         * @description  : 地图marker点击事件
         * @param         {*} e:
         * @return        {*}
         */
        clickMarker(e) {
            console.log('e-----', e);
            //
            let markerId = e.detail.markerId;
            console.log(this.showMarkerArrV3_app, '油站列表');
            let index = this.showMarkerArrV3_app.findIndex(item => {
                return item.stationId == markerId;
            });
            this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                marker: this.showMarkerArrV3_app[index],
                suc: { scene: 'site' },
            });
        },
    },
    computed: {
        ...mapGetters(['showMarkerArrV3_app']),
        ...mapState({
            // 地图缩放比例
            scale_app: state => state.locationV3_app.scale_app,
            mapCenterLatV3: state => state.locationV3_app.mapCenterLatV3,
            mapCenterLonV3: state => state.locationV3_app.mapCenterLonV3,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
        }),
    },
    watch: {
        // #ifdef MP-WEIXIN
        // 地图中心点变化后，地图回到中心点
        '$store.state.locationV3_app.mapCenterLatV3': {
            handler: function (newValue, oldValue) {
                this.$nextTick(() => {
                    this.mapCtx.moveToLocation({ latitude: Number(this.mapCenterLatV3), longitude: Number(this.mapCenterLonV3) });
                });
            },
            deep: true,
        },
        '$store.state.locationV3_app.mapCenterLonV3': {
            handler: function (newValue, oldValue) {
                this.$nextTick(() => {
                    this.mapCtx.moveToLocation({ latitude: Number(this.mapCenterLatV3), longitude: Number(this.mapCenterLonV3) });
                });
            },
            deep: true,
        },
        // #endif
    },
};
</script>
<style lang="stylus" scoped>
@import '~assets/stylus/index.styl'
</style>
<style scoped lang="scss">
@import '../../../../s-kit/css/index.scss';
/* @import url(); 引入css类 */
.navigation-map {
    width: 100%;
    position: relative;
    flex: 1;
    // min-height: 0;
}

.patb10 {
    padding: 10px 0;
}

.dot-div {
    position: absolute;
    bottom: 0;
}

// 地图
map {
    width: 100%;
    .marker-view {
        position: relative;
        height: 51px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;

        .marker-lable-view {
            padding: 0 5px;
            height: 51px;
            background: #fff;
            box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 8px;

            .navigation {
                width: 39px;
                height: 39px;
            }

            .marker-name {
                overflow: visible;
            }

            .marker-text {
                line-height: 14px;
            }

            .pa-t-5 {
                padding-top: 5px;
            }
        }
    }
}

.location-icon {
    display: block;
    position: absolute;
    right: 45rpx;
    width: 40px;
    height: 40px;
    z-index: 999;

    img {
        width: 100%;
        height: 100%;
    }
}
</style>
