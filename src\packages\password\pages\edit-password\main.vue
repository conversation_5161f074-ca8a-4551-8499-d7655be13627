<template>
  <div class="content" v-if="isForget != -1">
    <u-navbar
      :back-icon-size="40"
      :height="44"
      :back-icon-color="pageConfig.titleColor.backIconColor"
      :background="pageConfig.bgColor"
      :title-color="pageConfig.titleColor"
      :back-text="isForget === "0" ? "设置密码" : "忘记密码""
      :back-text-style="pageConfig.titleStyle"
      :border-bottom="false"
    ></u-navbar>
    <div class="cell">
      <div class="cell-title">手机号</div>
      <input
        class="cell-input"
        type="text"
        placeholder="请输入手机号"
        :value="registerLoginInformation.phone"
        disabled
      />
    </div>
    <div class="cell">
      <div class="cell-title">验证码</div>
      <input
        class="cell-input"
        type="text"
        placeholder="请输入验证码"
        v-model="code"
        :focus="sendCodeInputFocus"
        @focus="handleSendCodeInputFocus"
        @blur="handleSendCodeInputBlur"
      />
      <div class="cell-code" @click="clickSendCode">{{sendTest}}</div>
    </div>
    <div class="cell">
      <div class="cell-title">{{isForget === '0' ? '密码' : '新密码'}}</div>
      <input
        class="cell-input"
        :class="{ 'input-focus-cursor': clickPasswordIndex === 1 }"
        type="text"
        @click="clickPassword(1)"
        :password="isShowPassword"
        v-model="password"
        placeholder="请输入密码"
        disabled
      />
      <img
        class="cell-icon"
        @click="clickEye(1)"
        src="/static/eye-none.png"
         v-if="isShowPassword"
        mode="widthFix"
      />
      <img
        class="cell-icon"
        @click="clickEye(1)"
         src="/static/eye-block.png"
        v-else
        mode="widthFix"
      />
    </div>
    <div class="cell">
      <div class="cell-title">确认密码</div>
      <input
        class="cell-input"
        :class="{ 'input-focus-cursor': clickPasswordIndex === 2 }"
        type="text"
        @click="clickPassword(2)"
        :password="isShowDefinePassword"
        v-model="definePassword"
        placeholder="请确认密码"
        disabled
      />
      <img
        class="cell-icon"
        @click="clickEye(2)"
        v-if="isShowDefinePassword"
        src="/static/eye-none.png"
        mode="widthFix"
      />
      <img
        class="cell-icon"
        @click="clickEye(2)"
        v-else
        src="/static/eye-block.png"
        mode="widthFix"
      />
    </div>

    <div class="venicle-btn">
      <div class="venicle-btn-view">
        <div class="venicle-btn-text" @click="clickDefine">确定</div>
      </div>
      <div class="btn-text-sp"></div>
    </div>
    <u-keyboard
      ref="uKeyboard"
      tips="安全键盘"
      mode="number"
      @change="keyboardChange"
      @backspace="keyboardBackspace"
      :dot-enabled="false"
      :mask="false"
      :random="showKeyboard"
      v-model="showKeyboard"
      safe-area-inset-bottom
    ></u-keyboard>
  	<zj-show-modal></zj-show-modal>
	</div>
</template>

<script>
let codeTimer = null;
import {
  mapGetters
} from 'vuex'
import pageConfig from "@/utils/pageConfig.js";
import { forgetPayPass, getCheckCode, setPayPass, setedPayPass } from "@/api/home.js"
export default {
  data () {
    return {
      pageConfig: pageConfig,// 页面配置
      password: '',
      isShowPassword: true,
      definePassword: '',
      isShowDefinePassword: true,
      clickPasswordIndex: 0, // 1 为第一次密码，2 为确认密码
      code: '',
      showKeyboard: false,
      sendTest: '获取验证码',// 验证码文字
      isForget: -1,// 是否是忘记密码
      sendCodeInputFocus: false
    }
  },
  computed: {
    ...mapGetters(['registerLoginInformation'])
  },
  onUnload () {
    clearInterval(codeTimer)
  },
  onLoad (option) {
    if (option.isfor) {
      this.isForget = option.isfor
    }

  },
  methods: {
    handleSendCodeInputBlur (e) {
      this.sendCodeInputFocus = false;
    },
    handleSendCodeInputFocus (e) {
      this.clickPasswordIndex = 0;
      this.showKeyboard = false;
      this.sendCodeInputFocus = true;
    },
    // 小眼睛点击事件
    clickEye (index) {
      if (index == 1) {
        this.isShowPassword = !this.isShowPassword
      } else if (index == 2) {
        this.isShowDefinePassword = !this.isShowDefinePassword
      }
    },
    // 验证码发送点击事件
    async clickSendCode () {
      if (this.sendTest == '重新获取' || this.sendTest == '获取验证码') {
        let downNum = 60
        this.sendTest = downNum + 's'
        await getCheckCode({
          typeCode: this.isForget == '0' ? '50001' : '50002'
        })
        codeTimer = setInterval(() => {
          downNum--
          if (downNum == 0) {
            clearInterval(codeTimer)
            this.sendTest = '重新获取'
          } else {
            this.sendTest = downNum + 's'
          }

        }, 1000)
      }
    },
    // 确认按钮点击事件
    async clickDefine () {
      if (this.sendTest == '获取验证码') return this.$util.noneToast('请先获取验证码')
      if (!/^\d{6}$/.test(this.code)) return this.$util.noneToast('请输入6位验证码')
      if (!/^\d{6}$/.test(this.password)) return this.$util.noneToast('请输入6位密码')
      if (this.password != this.definePassword) return this.$util.noneToast('两次输入的密码不匹配')
      let param = {
        passwordNew: this.$util.payPassEncryption(this.password),
        passwordConfirm: this.$util.payPassEncryption(this.definePassword),
        verifyCode: this.code
      }
      let res
      if (this.isForget == '0') {
        res = await setPayPass(param)
      } else {
        res = await forgetPayPass(param)
      }
      if (res.status == -1) {
        await this.$util.showModal(res.info, true)
      } else {
        await this.$util.showModal('设置成功', true)
        if (this.isForget == '0') {
          let isPassword = await setedPayPass()
          this.$store.commit('setIsSetPassword', isPassword.data)
        }
        uni.navigateBack()
      }

    },
    // 键盘输入事件
    keyboardChange (text) {
      if (this.clickPasswordIndex == 1) {
        this.password = this.password.length >= 6 ? this.password : this.password + text
      } else if (this.clickPasswordIndex == 2) {
        this.definePassword = this.definePassword.length >= 6 ? this.definePassword : this.definePassword + text
      }
    },
    // 安全键盘唤起事件
    clickPassword (index, e) {
      console.log('clickPassword', e);
      this.clickPasswordIndex = index
      this.showKeyboard = true
      this.sendCodeInputFocus = false;
    },
    // 键盘退格事件
    keyboardBackspace () {
      if (this.clickPasswordIndex == 1) {
        this.password = this.password.substring(0, this.password.length - 1)
      } else if (this.clickPasswordIndex == 2) {
        this.definePassword = this.definePassword.substring(0, this.definePassword.length - 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  width: 100%;
  min-height: 100%;
  background: #f6f6f6;
  .cell {
    position: relative;
    z-index: 100000;
    display: flex;
    align-items: center;
    margin-left: 15px;
    width: 345px;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #ffffff;
    border-radius: 5px;
    height: 50px;
    margin-top: 10px;
    .cell-title {
      color: #333333;
      font-size: 15px;
      font-weight: 700;
    }
    .cell-input {
      position: relative;
      text-align: right;
      flex: 1;
      line-height: 50px;
      color: #333333;
    }
    .cell-icon {
      padding-left: 20px;
      width: 16px;
      max-height: 16px;
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .cell-code {
      margin-left: 10px;
      color: $btn-color;
      font-size: 15px;
      width: 78px;
      line-height: 50px;
      text-align: center;
    }
  }
}
.venicle-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  .venicle-btn-view {
    height: 64px;
    width: 100vw;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .venicle-btn-text {
      line-height: 44px;
      width: 345px;
      background-color: $btn-color;
      border-radius: 5px;
      color: #ffffff;
      text-align: center;
      font-weight: 700;
    }
  }
  .btn-text-sp {
    width: 100vw;
    background-color: #ffffff;
    height: env(safe-area-inset-bottom);
  }
}
</style>
