<template>
<uni-shadow-root class="safe-password-index"><view>
  <safepassword id="passwordKeyboardId" :title="title"></safepassword>
</view></uni-shadow-root>
</template>

<script>
import Safepassword from 'plugin://security-utils-plugin/safe-password-v3.vue'
global['__wxVueOptions'] = {components:{'safepassword': Safepassword}}

global['__wxRoute'] = 'safe-password/index'
Component({
    behaviors: ['wx://component-export'],
    export() {
        return this.selectComponent('#passwordKeyboardId');
    },
    properties: {
        // 属性值可以在组件使用时指定
        title: {
            type: String,
            value: '安全键盘',
        },
    },
    lifetimes: {
        attached: function () {
            // 在组件实例进入页面节点树时执行
        },
        ready: function () {
            // 在组件实例进入页面节点树时执行
            console.log('.........', this.selectComponent('#passwordKeyboardId'));
        },
        detached: function () {
            // 在组件实例被从页面节点树移除时执行
        },
    },
    pageLifetimes: {
        // 组件所在页面的生命周期函数
        show: function () {},
        hide: function () {},
        resize: function () {},
    },
    data: {
        // 这里是一些组件内部数据
    },
    methods: {},
});
export default global['__wxComponents']['safe-password/index']
</script>
<style platform="mp-weixin">

</style>