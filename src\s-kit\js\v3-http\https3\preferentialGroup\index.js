import { POST } from '../../index';
//查询申请加入优待证群组审核状态
export const userPreferentialGroupApprovalStatus = (params, config) => {
    return POST('user.preferentialGroup.approvalStatus', params, config);
};
//提交申请加入优待证群组
export const userPreferentialGroupApplyJoin = (params, config) => {
    return POST('user.preferentialGroup.applyJoin', params, config);
};
//同意加入群组
export const userGroupJoin = async (params, config) => {
    return await POST('user.group.join', params, config);
};
//查看用户是否已经加入该群组
export const userGroupIsGroupMember = async (params, config) => {
    return await POST('user.group.isGroupMember', params, config);
};
//获取站内信
export const userGroupNewsList = async (params, config) => {
    return await POST('user.group.newsList', params, config);
};
//获取站内信未读数量
export const contentMessageCodeUnRead = async (params, config) => {
    return await POST('content.messageCode.unRead', params, config);
};
//更新站内信状态
export const userMessageUpdateStatus = async (params, config) => {
    return await POST('user.message.updateStatus', params, config);
};
//删除站内信
export const userMessageDelete = async (params, config) => {
    return await POST('user.message.delete', params, config);
};

//获取站内信详情
export const userGroupNewsDetail = async (params, config) => {
    return await POST('user.group.newsDetail', params, config);
};
