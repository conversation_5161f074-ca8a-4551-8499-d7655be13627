<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 选择地区插件页面 -->
        <div class="view fl-column">
            <zj-navbar :height="44" :title="title"></zj-navbar>
            <div class="city-content f-1 font-16 bg-F7F7FB" :class="{ padBottom: padBottom }">
                <div class="location" v-if="locationPermission">
                    <div>定位失败</div>
                    <div @click="openLoction()">开启定位</div>
                </div>
                <div class="select-area fl-row weight-500">
                    <div class="area-city color-333 weight-500 font-16 fl-row" :class="{ btnSelect: city }" v-if="city" @click="cancel">
                        <div>
                            {{ city.areaName }}
                        </div>
                        <img class="deleteIcon" src="../../images/select-area-icon/deleteIcon.png" alt="" />
                    </div>
                    <div class="area-city color-333 weight-500 font-16" v-if="area">{{ area.areaName }}</div>
                    <div class="select color-E64F22 weight-500 font-16" v-if="!area">请选择</div>
                </div>
                <div class="area-list">
                    <div class="area-item" v-for="(item, index) in dataList" :key="index">
                        <div class="area-item-title">{{ item.upperLetter }}</div>
                        <div class="area-item-list border-rad-8 bg-fff">
                            <div
                                class="area-item-list-area fl-row fl-jus-bet"
                                v-for="(i, idx) in item.areaList"
                                :key="idx"
                                @click="cityFn(i, item)"
                            >
                                <div class="font-14 color-333 weight-600">{{ i.areaName }}</div>
                                <img src="../../images/select-area-icon/arrow-right.png" v-if="num == 0" alt />
                                <img src="../../images/select-area-icon/select.png" v-if="area.areaCode == i.areaCode" alt />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div v-if="area" class="btn" @click="confirmSelection">确认修改</div> -->
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { listByCondition } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import Vue from 'vue';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'selectRegion',
    data() {
        return {
            // pageConfig,
            title: '选择城市',
            city: '',
            area: '',
            dataList: [],
            num: 0,
            show: false,
            flag: '',
            delta: 1,
            province: '',
            selectArea: {},
            locationPermission: false,
        };
    },
    mounted(options) {
        // 判断是在开通昆仑e享卡是跳转进来还是在我的常用地跳转进来
        // this.delta = options.delta;
        // console.log(this.delta, 'onLoad-----')
        this.listByConditionPost(1);
        this.$cnpcBridge.checkPermission().then(res => {
            this.locationPermission = !res.appStatus;
        });
        this.$sKit.mpBP.tracker('首页标题栏', {
            seed: 'titleBiz',
            pageID: 'selectProvincePage',
            home_title_click: 'area',
        });
    },
    computed: {
        padBottom() {
            return this.area;
        },
    },
    methods: {
        /**
         * @description  : 开启定位权限
         * @return        {*}
         */
        openLoction() {
            this.$cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(res => {
                    if (res) {
                        this.locationPermission = false;
                        // 授权成功
                        this.$cnpcBridge.getLocation(location => {
                            this.$cnpcBridge.setValueToNative('Define_Selected_Area', JSON.stringify(location), () => {});
                        });
                    }
                });
        },
        /**
         * @description  : 获取省份
         * @param         {string} type - 获取的是城市还是省份
         * @param         {string} parentAreaCode - 省份编码
         * @return        {*}
         */
        async listByConditionPost(type, parentAreaCode = '', i, item) {
            let params = {
                type: type,
                parentAreaCode: parentAreaCode,
            };
            let res = await listByCondition(params);
            if (res.success) {
                let list = res.data || [];
                if (type == 2 && list.length == 0) {
                    // 当选中没有区的城市，列表替换为只有该城市
                    this.dataList = [item];
                    this.dataList[0].areaList = this.dataList[0].areaList.filter(ele => ele.areaCode == i.areaCode);
                } else {
                    this.dataList = list;
                }
            }
        },
        /**
         * @description  : 根据选中的省份获取城市
         * @return        {*}
         */
        cityFn(i, item) {
            if (this.num == 0) {
                this.city = i;
                console.log(i.areaCode, i, item, 'i.areaCode, i, item');
                this.selectArea.provinceCode = i.areaCode;
                this.selectArea.province = i.areaName;
                this.province = i.areaName;
                this.listByConditionPost(2, i.areaCode, i, item);
                this.num = 1;
                this.$sKit.mpBP.tracker('首页标题栏', {
                    seed: 'titleBiz',
                    pageID: 'selectAreaPage',
                    home_title_click: 'area',
                });
            } else {
                this.area = i;
                console.log(i, 'i.areaCode, i, item');
                this.selectArea.cityCode = i.areaCode;
                this.selectArea.city = i.areaName;
                console.log(this.selectArea, 'this.selectArea');
                this.$cnpcBridge.setValueToNative('Define_Selected_Area', JSON.stringify(this.selectArea), () => {
                    // uni.showToast({
                    //     title: JSON.stringify(this.selectArea),
                    //     icon: 'none',
                    //     duration: 2000
                    // })
                    this.$cnpcBridge.closeMriver();
                });
            }
        },
        /**
         * @description  : 取消选择
         * @return        {*}
         */
        cancel() {
            this.city = '';
            this.area = '';
            this.num = 0;
            this.listByConditionPost(1);
        },
        // confirm() {
        //     console.log('确认');
        // },
    },
    components: {},
};
</script>
<style scoped lang="scss">
.view {
    height: 100%;
    // display: flex;
    // flex-direction: column;
}

.padBottom {
    padding-bottom: 64px !important;
}

.city-content {
    position: relative;
    padding: 0 15px;
    padding-top: 13px;
    .location {
        margin-bottom: 6px;
        display: flex;
        flex-direction: row;
        div:first-child {
            width: auto;
            height: 23px;
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            line-height: 23px;
        }
        div:last-child {
            height: 23px;
            font-size: 16px;
            font-weight: 500;
            margin-left: 10px;
            color: #e64f22;
            line-height: 23px;
        }
    }
    .select-area {
        margin-bottom: 18px;

        .area-city {
            margin-right: 19px;
            position: relative;
        }

        .select {
            color: #e64f22;
            height: 56rpx;
            line-height: 56rpx;
        }
    }
    .btnSelect {
        height: 56rpx;
        padding: 0 16rpx;
        line-height: 56rpx;
        background: #ffffff;
        border: 2rpx solid #e64f22;
        color: #e64f22;
        border-radius: 16rpx;
    }
    .deleteIcon {
        position: absolute;
        width: 26rpx;
        height: 26rpx;
        top: -14rpx;
        right: -14rpx;
    }

    .area-list {
        padding-bottom: 18px;

        .area-item {
            margin-bottom: 18px;

            .area-item-title {
                margin-bottom: 9px;
                padding-left: 15px;
            }

            .area-item-list {
                .area-item-list-area {
                    padding-left: 15px;
                    height: 45px;
                    line-height: 45px;

                    img {
                        float: right;
                        width: 16px;
                        height: 16px;
                        margin-top: 16px;
                        margin-right: 10px;
                    }
                }
            }
        }
    }

    .btn {
        font-size: 15px;
        width: 92%;
        font-weight: 500;
        color: #ffffff;
        height: 44px;
        background: #e64f22;
        border-radius: 8px;
        position: fixed;
        bottom: 20px;
        text-align: center;
        line-height: 44px;
        // top: 0;
        // bottom: 0;
        // transform: translate(-50%, -50%);
    }
}
</style>
