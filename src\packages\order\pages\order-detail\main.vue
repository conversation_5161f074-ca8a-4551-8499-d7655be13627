<template>
    <div class="detail-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="订单详情"
            :back-text-style="pageConfig.titleStyle"
            :custom-back="clickCustomBackBtn"
            :border-bottom="false"
        ></u-navbar>
        <div class="order-detail">
            <div class="head">
                <img class="logo" src="@/static/cnpc-logo.png" alt />
                <div class="order_name">{{ orderInfo.stationName }}</div>
                <div class="order_name1">实付金额</div>
                <div class="order-price flex align-center">
                    <div class="pris">￥</div>
                    <div class="price">{{ orderInfo.realAmount }}</div>
                </div>
            </div>
            <div class="order-info">
                <div class="details flex justify-between">
                    <div>商品说明</div>
                    <div>{{ orderInfo.goodsName }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>订单号</div>
                    <div>{{ orderInfo.tradeNo === undefined ? '' : orderInfo.tradeNo.replace(/(.{4})/g, '$1 ') }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>支付时间</div>
                    <div>{{ orderInfo.tradeTime }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>枪号</div>
                    <div>{{ orderInfo.oilGunNo }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>单价</div>
                    <div>￥{{ orderInfo.unitPrice }}/升</div>
                </div>
                <div class="details flex justify-between">
                    <div>升数</div>
                    <div>{{ orderInfo.quantity }}升</div>
                </div>
                <div class="details flex justify-between">
                    <div>订单金额</div>
                    <div>￥{{ orderInfo.totalAmount }}元</div>
                </div>
                <div class="details flex justify-between">
                    <div>电子券</div>
                    <div>￥{{ orderInfo.couponAmt }}元</div>
                </div>
                <div class="details flex justify-between">
                    <div>折扣金额</div>
                    <div :style="{ color: orderInfo.discountAmount == '0' ? '#fa6606' : '#696969' }"
                        >￥{{ orderInfo.discountAmount }}元</div
                    >
                </div>
                <div class="details flex justify-between">
                    <div>支付方式</div>
                    <div>{{ orderInfo.payType }}</div>
                </div>
            </div>
        </div>
        <div class="invoice-header" v-if="orderInfo.invoiceStatus == 1" @click="selectInvoiceTitle">
            <div class="invoice flex justify-between">
                <div class="title">发票抬头</div>
                <div class="flex headName">
                    <div>{{ titleName }}</div>
                    <img class="moreImg" src="@/static/homeIcon/rightjt.png" alt />
                </div>
            </div>
        </div>
        <div class="btn" @click="clickKaiPiao" v-if="orderInfo.invoiceStatus == 1">
            <div class="btntext">提交</div>
        </div>
        <div v-if="orderInfo.invoiceStatus == 1">
            <div class="horn-text-wrap">
                <img class="horn-img" src="@/static/horn.png" alt />
                <div class="horn-text">温馨提示：</div>
            </div>

            <div class="oil-prompt">· 电子发票的销售方名称与您的昆仑加油卡归属地或交易消费加油站主体一致； </div>
        </div>
        <!-- 开票成功弹窗 -->
        <uni-pop ref="sucpopup" type="center" :maskClick="false">
            <div class="suc-pop-view">
                <div class="suc-pop-content">
                    <img class="suc-pop-icon" src="@/static/homeIcon/success-pay.png" />
                    <div class="suc-pop-title">开票完成</div>
                    <div class="suc-pop-detail">您的开票申请已提交，可在开票历史中查看历史开票</div>
                </div>
                <u-icon
                    @click="clickCloseSucPop"
                    name="close-circle-fill"
                    :custom-style="{
                        marginTop: '40rpx',
                    }"
                    color="#ffffff"
                    size="92"
                ></u-icon>
            </div>
        </uni-pop>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { orderDetail2, billingInvoice } from '@/api/home.js';
import { getInvoiceTitleList } from '@/api/my-center';
import uniPop from '@/components/uni-popup/uni-popup.vue';

export default {
    name: 'orderDetail',
    components: {
        uniPop,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            orderId: '', //订单号
            orderInfo: {},
            titleName: '', //发票抬头
            invoiceDetail: {},
        };
    },
    onLoad(options) {
        this.orderId = options.orderId;
        this.getOrderDetail();
        uni.$on('chooseTitle', data => {
            this.titleName = data.invoicetitle;
            this.invoiceDetail = data;
            console.log(this.invoiceDetail, 'this.invoiceDetail');
        });
    },
    onShareAppMessage() {},
    methods: {
        //获取企业抬头
        async getInvoiceTitle() {
            let res = await getInvoiceTitleList();
            if (res.status == 0 && res.data[0]) {
                this.titleName = res.data[0].invoicetitle;
                this.invoiceDetail = res.data[0]; //企业抬头中第一个企业
            }
        },
        //选择发票抬头
        selectInvoiceTitle() {
            console.log(this.invoiceDetail, 'this.invoiceDetail');
            //
            uni.navigateTo({
                url: `/packages/invoice-center/pages/choose-invoice-title/main?invoiceDetail=${JSON.stringify(this.invoiceDetail)}`,
            });
        },
        async getOrderDetail() {
            let res = await orderDetail2({ orderId: this.orderId });
            if (res.status == 0) {
                this.orderInfo = res.data;
                if (res.data.invoiceStatus == 1) {
                    this.getInvoiceTitle();
                }
            } else {
                this.$util.showModal(res.info, true);
            }
        },
        async clickKaiPiao() {
            try {
                let selectOrderArr = [this.orderInfo];
                if (selectOrderArr[0].invoiceStatus == 2) {
                    this.$util.showModal('该订单已经开过发票了', true);
                    return;
                }
                if (selectOrderArr[0].invoiceStatus == 0 || selectOrderArr[0].invoiceStatus == 3 || selectOrderArr[0].invoiceStatus == 4) {
                    this.$util.showModal('该订单不可开票', true);
                    return;
                }
                if (!this.invoiceDetail.id) {
                    this.$util.showModal('请选择发票抬头', true);
                    return;
                }
                let params = {
                    type: 1,
                    invoiceTitleId: this.invoiceDetail.id,
                    islogo: this.invoiceDetail.islogo,
                    id: this.orderInfo.tradeNo,
                    invoicetitle: this.invoiceDetail.invoicetitle,
                    taxcode: this.invoiceDetail.taxcode ? this.invoiceDetail.taxcode : '',
                    addresstax: this.invoiceDetail.addresstax ? this.invoiceDetail.addresstax : '',
                    telephone: this.invoiceDetail.telephone ? this.invoiceDetail.telephone : '',
                    openingbank: this.invoiceDetail.openingbank ? this.invoiceDetail.openingbank : '',
                    bankaccount: this.invoiceDetail.bankaccount ? this.invoiceDetail.bankaccount : '',
                    businebsscard: this.invoiceDetail.businesscard ? this.invoiceDetail.businesscard : '',
                };
                wx.showLoading({
                    title: '加载中',
                    mask: true,
                });
                let res = await billingInvoice(params);
                if (res.status == 0) {
                    wx.hideLoading();
                    // this.$refs.sucpopup.open()
                    await this.$util.showModal('开票申请已经提交，稍后您将收到发票信息，请到我的发票列表查询！', true);
                    // 1. 获取当前页面栈实例（此时最后一个元素为当前页）
                    let pages = getCurrentPages();

                    // 2. 上一页面实例
                    // 注意是length长度，所以要想得到上一页面的实例需要 -2
                    // 若要返回上上页面的实例就 -3，以此类推
                    let prevPage = pages[pages.length - 2];

                    // 3. 给上一页面实例绑定getValue()方法和参数（注意是$vm）
                    prevPage.$vm.refreshInvoice = true;

                    // 4. 返回上一页面
                    uni.navigateBack({
                        delta: 1, // 返回的页面数
                    });
                } else {
                    wx.hideLoading();
                    uni.showToast({
                        title: res.info,
                        icon: 'none',
                    });
                }
            } catch (error) {
                wx.hideLoading();
            }

            // uni.redirectTo({
            // 	url: '/packages/invoice-center/pages/add-invoice/main?list=' + encodeURIComponent(JSON.stringify(
            // 		selectOrderArr))+'&rechargeOrder=1'+ '&invoiceDetail='+JSON.stringify(this.invoiceDetail)
            // })
        },
        // 点击开票成功关闭弹窗
        clickCloseSucPop() {
            this.$refs.sucpopup.close();
            uni.$emit('uploadPage', {});
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}

.flex {
    display: flex;
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.detail-center {
    width: 100%;
    height: 100%;
    overflow: scroll;
    background: #f6f6f6;
    .order-detail {
        width: 93%;
        margin: 0 auto;
        border-radius: 4px;
        background-color: #fff;
        margin-top: 70rpx;
        .head {
            text-align: center;
            .logo {
                width: 45px;
                height: 45px;
                border: 10px solid #fff;
                margin-top: -21px;
                border-radius: 50%;
            }
            .order_name {
                color: #333333;
                font-size: 30rpx;
            }
            .order_name1 {
                color: #666666;
                font-size: 12px;
                margin-top: 20rpx;
            }
            .order-price {
                color: #333;
                font-weight: 600;
                padding: 20rpx 0;
                justify-content: center;
                .pris {
                    font-size: 40rpx;
                }
                .price {
                    font-size: 60rpx;
                }
            }
        }
        .order-info {
            color: #333333;
            font-size: 13px;
            width: 94%;
            margin: 0 auto;
            padding: 12px 0;
            margin-top: 10rpx;
            border-top: 1px solid #eeeeee;
            .details {
                padding: 6px 0;
            }
        }
    }
    .invoice-header {
        width: 93%;
        margin: 10px auto;
        border-radius: 4px;
        background-color: #fff;
        color: #333333;
        .invoice {
            width: 94%;
            padding: 10px 0;
            margin: 0 auto;
            .title {
                flex: 1;
                font-size: 15px;
                font-weight: 700;
            }
            .headName {
                flex: 2;
                justify-content: flex-end;
                font-size: 12px;
                .moreImg {
                    width: 7px;
                    height: 12px;
                    margin-left: 10px;
                }
            }
        }
    }
    .btn {
        position: fixed;
        left: 0;
        width: 100%;
        bottom: env(safe-area-inset-bottom);
        background-color: #fff;
        text-align: center;
        padding: 9px 0;
        .btntext {
            background-color: #ff8200;
            border-radius: 4px;
            color: #fff;
            font-size: 15px;
            font-weight: 600;
            width: 93%;
            margin: 0 auto;
            padding: 10px 0;
        }
    }
    .horn-text-wrap {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
        margin-top: 10px;
        .horn-text {
            font-size: 28rpx;
            color: #ef8934;
        }
        .horn-img {
            width: 18px;
            height: 16px;
            margin-right: 5px;
        }
    }

    .oil-prompt {
        font-size: 24rpx;
        color: #999999;
        padding-left: 15px;
        padding-right: 15px;
        margin-top: 5px;
    }
    .suc-pop-view {
        display: flex;
        flex-direction: column;
        align-items: center;
        .suc-pop-content {
            border-radius: 10px;
            width: 345px;
            height: 235px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #ffffff;
            .suc-pop-icon {
                width: 90px;
                height: 90px;
            }
            .suc-pop-title {
                margin-top: 15px;
                line-height: 36px;
                font-size: 24px;
                color: #141414;
                font-weight: 700;
            }
            .suc-pop-detail {
                line-height: 36px;
                font-size: 12px;
                color: #141414;
                font-weight: 700;
            }
        }
    }
}
</style>
