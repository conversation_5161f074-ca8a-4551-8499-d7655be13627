<template>
    <div>
        <!-- <div @click="btnClick"
   style="width: 180px;height:44px; background:red"
  >
    立即登录
   </div> -->
        <UPButton @click="btnClick" :scope="scope" :style="{
            height: '44px',
            lineHeight: '44px',
            background: 'linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%)',
            boxShadow: '0px 1px 10px 0px rgba(0, 0, 0, 0.07)',
            textAlign: 'center',
            borderRadius: '8px',
            color: '#fff',
            marginBottom: '12px',
            fontSize: '18px',
            width: '100%'
        }
            ">
            授权登录
        </UPButton>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import Store from '../../store';
import { userLogin } from '../../s-kit/js/v3-http/https3/user.js';
export default {
    data() {
        return {
            scope: 'scope.mobile,scope.auth',
            // scope: "scope.mobile",
            // dynamicBackgroundColor:'linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%)'
        };
    },
    computed: {
        ...mapGetters(['isLogin']),
        ...mapState({
            // 登录按钮置灰变亮
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            // 3.0存储登录信息
            personalInformation3: state => state.personalInformation3,
        }),
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
        this.btnClick = this.$util.throttleUtil(this.btnClick);
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() { },
    methods: {
        btnClick(event, err, result) {
            if (event.errcode == '03') {
                this.$store.dispatch('zjShowModal', {
                    content: '请授权登录以使用能源e站服务',
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return false
            } else {
                this.getUserLogin(event.code);
            }
        },
        /**
     * 云闪付小程序登录
     loginType ：13-云闪付小程序登录；
     authInfo ： 银联认证code；
     * */
        async getUserLogin(authInfo = '') {
            let params = {
                type: '13',
                authInfo,
            };
            let loginRes = await this.$store.dispatch('thirdLoginFun', params);
            this.$emit('loginOver', loginRes);
            // uni.setStorageSync('tokenInfo', res.data);
        },
    },
};
</script>
<style lang="scss" scoped>
/* @import url(); 引入css类 */
.customBtn {}
</style>
