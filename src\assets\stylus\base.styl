/* *全局重置* */
page
  width 100%
  height 100%
  padding 0
  background-color #fff
  font-size 28rpx
  color #333
  overflow-x hidden
  font-family -apple-system-font, 'Helvetica Neue', Helvetica, 'Microsoft YaHei', sans-serif

button
  background transparent
  padding 0
  line-height 1.4285
  font-size 14px
button:after
  content none
button[disabled]:not([type])
  opacity 0.6
image
  width 100%
  vertical-align middle
.container
  width 100%
  padding 10px
  -webkit-box-sizing border-box
  -moz-box-sizing border-box
  box-sizing border-box
.ellipsis
  overflow hidden
  white-space nowrap
  text-overflow ellipsis
.ellipsis-2
  display -webkit-box
  overflow hidden
  white-space normal !important
  text-overflow ellipsis
  word-wrap break-word
  -webkit-line-clamp 2
  -webkit-box-orient vertical
.table-view + .table-view
  margin-top 8px
.table-view-cell
  padding 11px
  background #fff
  position relative
  overflow hidden
.table-view-cell:before
  content ' '
  position absolute
  left 11px
  bottom 0
  right 11px
  border-bottom 1px solid #f3f3f3
.table-view-cell:last-child:before
  content none
.table-view-cell-arrow
  position absolute
  top 50%
  transform translateY(-50%)
  right 14px
  & + .table-view-cell-text
    padding-right 10px
.table-view-cell-arrow:after
  content ' '
  display inline-block
  height 9px
  width 9px
  border-width 1px 1px 0 0
  border-color #888888
  border-style solid
  transform matrix(0.71, 0.71, -0.71, 0.71, 0, 0)
.table-view-cell-text
  display flex
  align-items center
  justify-content space-between
  & + .table-view-cell-text
    padding-top 8px
  .left
    flex-shrink 0
  .center
    padding 0 8px
    flex 1
  .right
    flex-shrink 0
.card
  box-shadow 0 0 4px 0 rgba(0, 0, 0, 0.1)
  border-radius 4px
  overflow hidden
.no-data
  padding 11px 15px
  text-align center
.line-through
  text-decoration line-through
  color #999999
  font-size 12px
