<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="授权说明"></zj-navbar>
            <div class="content padding-16 f-1">
                <div class="text fl-column">
                    <div>
                        本协议是您与昆仑数智科技有限责任公司，及其合作的认证服务机构(阿里云计算有限公司及其服务供应商蚂蚁区块链科技(上海)
                        有限公司)、之间关于认证服务相关内容所订立的协议，请您务必认真阅读本协议，在确认充分了解后慎重决定是否同意本协议。您主动勾选或点击同意后，本协议生效，对您及昆仑数智科技有限责任公司，及其合作的认证服务机构均具有法律约束力。
                    </div>
                    <div>
                        一、为了更好地提供服务，昆仑数智科技有限责任公司研发了认证服务功能，该功能由包括但不限于公安部“互联网+”可信身份认证平台等机构提供核验数据及技术支持。您已将个人身份信息(包括姓名、身份证号、手机号和人脸图片)提交给昆仑数智科技有限责任公司，以及其合作的认证服务机构。
                    </div>
                    <div> 二、 您在认证服务过程中，应提供本人合法、真实、有效、准确并完整的资料。 </div>
                    <div>
                        三、
                        如您不同意授权，请不要点击“下一步”或“同意”，同时您将无法继续使用认证功能，进而也无法继续使用电子加油卡功能，但这并不影响你使用
                        (能源e站)的其他服务。
                    </div>
                    <div>
                        四、 为向您提供身份认证服务，您同意授权平台(能源e站)及其合作的认证服务机构采集您的姓名、身份证号码、身份证图片
                        (使用OCR功能时)人脸信息、语音信息及声纹特征(使用意愿认证功能时)并进行必要的处理，您理解并同意平台及其合作的认证服务机构可以将您在使用本服务时提供的身份信息(姓名、身份证号码)和人脸信息，与法律法规允许或政府机关授权机构存储的信息进行比对核验。同时，为确保发起的身份认证行为来自真实有效设备操作并保障服务的安全稳定运行，认证服务机构需获取您的设备信息及日志信息。如您想要了解认证服务机构如何处理您的个人信息，可点击可信身份认证个人信息处理规则查阅了解更多信息。如您发现认证结果不准确，您可联系中国石油客服
                        (956100) 沟通解决。
                    </div>
                    <div>
                        五、
                        如您发现我们采集、使用您个人信息的行为.违反了法律、行政法规规定或违反了与您的约定，或您发现我们采集、储存的您的个人信息有错误的，您可联系中国石油客服
                        (956100) ，要求删除或更正。
                    </div>
                </div>
                <div class="btn primary-btn border-rad-8 shad-ef color-fff" @click="agreeActivateClick">开通昆仑e享卡</div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zj-old-account v-if="isTransfer" :activate="activate"></zj-old-account>
            <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        </div>
    </div>
</template>
<script>
// #ifdef MP-MPAAS
import appActivateElectronic from './diff-environment/app-activate-electronic-wallet.js';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD
import wxActivateElectronic from './diff-environment/wx-activate-electronic-wallet.js';
import zfbActivateElectronic from './diff-environment/zfb-activate-electronic-wallet.js';
// #endif
// #ifdef H5-CLOUD
import h5ActivateElectronic from './diff-environment/h5-activate-electronic-wallet.js';
// #endif
import { mapState, mapGetters } from 'vuex';
import projectConfig from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appActivateElectronic,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxActivateElectronic,
        zfbActivateElectronic,
        // #endif
        // #ifdef H5-CLOUD
        h5ActivateElectronic
        // #endif
    ],
    name: 'wallet-authorization-description',
    // components: { ZjOldAccount },
    props: {},
    data() {
        return {
            // 用户信息
            personInfo: {},
            // 用于校验下一步的rsa字串
            authInfo: '',
            // 身份认证唯一标识
            verifyUnique: '',
            // 油站位置信息
            locationInfo: '',
            //  // 区分功能限制电子卡迁移成功后返回的到哪个页面
            // activate: '',
        };
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
        ...mapState({
            staffStationId: state => state.staffStationId, // 邀请开卡接口传值
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            backWalletRechange: state => state.wallet.backWalletRechange, // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
            latV3: state => state.locationV3_app.latV3, //风控纬度
            lonV3: state => state.locationV3_app.lonV3, //风控经度
            walletStatus: state => state.wallet.walletStatus, // 昆仑e享卡状态
            walletAddChannel: state => state.wallet.walletAddChannel, //开通昆仑e享卡来源渠道
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    created() {},
    onLoad(options) {
        // 上个页面携带过来的用户填写的表单信息
        this.personInfo = JSON.parse(decodeURIComponent(options.data));
        this.$sKit.mpBP.tracker('e享卡开通', {
            seed: 'eCardActiveBiz',
            pageID: 'authorizeDesPage', // 页面名
            refer: this.personInfo.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
            address: this.personInfo.address,
        });
        console.log(this.personInfo, 'this.personInfo====');
        // 给点击事件设置节流，防止用户频繁点击
        this.agreeActivate = this.$sKit.commonUtil.throttleUtil(this.agreeActivate);
    },
    methods: {
        agreeActivateClick() {
            // 打开人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', true);
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            //去实人认证
            this.$sKit.mpBP.tracker('e享卡开通', {
                seed: 'eCardActiveBiz',
                pageID: 'faceAuthPage', // 页面名
                refer: this.personInfo.refer || '', // 来源
                channelID: projectConfig.clientCode, // C10/C12/C13
                address: this.personInfo.address,
            });
            this.agreeActivate();
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.view {
    .content {
        position: relative;

        .text {
            height: 100%;
            padding-bottom: 54px;
        }

        .btn {
            width: 92%;
            height: 44px;
            position: absolute;
            line-height: 44px;
            bottom: 10px;
        }
    }
}
</style>
