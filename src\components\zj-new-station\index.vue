<template>
    <div class="view">
        <div class="popup-box">
            <div class="popup-content" :style="{ backgroundImage: `url(${popupBg})` }">
                <div class="popup-content-title">诚邀您体验新版客户端</div>
                <div class="popup-content-text"
                    >我们将用很短暂的时间为您进行升级（这不会额外花费您的流量），升级成功后您可以享受更便捷的服务和更多优惠</div
                >
                <div v-if="!isUpgradeFlag" class="popup-content-warning"
                    >当前加油站仅支持新版客户端，如您暂时不便升级，请到收银台支付很抱歉影响您的正常使用</div
                >
                <div v-if="isUpgradeFlag === 'ykcz'" class="popup-content-warning"
                    >当前加油站仅支持新版客户端，如您暂时不便升级，请前往其他加油站进行充值，很抱歉影响您的正常使用</div
                >
                <div v-if="isUpgradeFlag === 'ktdzk'" class="popup-content-warning"
                    >当前地区仅支持新版客户端，如您暂时不便升级，请选择其他地区进行申请电子卡，很抱歉影响您的正常使用</div
                >
                <checkbox-group @change="agreement" class="popup-content-agree">
                    <checkbox value="agreement" :checked="agreeCheckBox" class="popup-content-agree-checkBox" />
                    <div class="popup-content-agree-text">
                        我已阅读并同意
                        <span @click="clickXieyi('5', 'App用户使用协议')">《能源e站用户协议》</span>和
                        <span @click="clickXieyi('2', 'App隐私协议')">《能源e站隐私政策》</span>
                    </div>
                </checkbox-group>
                <div class="popup-content-btn" :class="{ disable: btnDisable }" v-if="!upgradingFlag" @click="accountUpgrade">立即升级</div>
                <div class="popup-content-btn" :class="{ disable: upgradingFlag }" v-if="upgradingFlag">升级中.....</div>
                <div class="popup-content-btn-next" @click="closePopup">以后再说</div>
            </div>
            <div class="close-popup" @click="closePopup">
                <img src="@/static/trirdImage/popupClose.png" alt />
                <div></div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getUserAgreementApi } from '@/api/home.js';
export default {
    name: 'zjNewStation',
    data() {
        return {
            popupBg: require('@/static/trirdImage/popupBg2.png'),
            agreeCheckBox: false,
            upgradingFlag: false,
        };
    },
    computed: {
        btnDisable() {
            return !this.agreeCheckBox;
        },
        ...mapState({
            province: state => state.location.province, // 省
        }),
    },
    props: {
        isUpgradeFlag: {
            type: String,
            default: '',
        },
    },
    methods: {
        // x按钮点击事件 @close
        closePopup() {
            this.$emit('close');
        },
        agreement(e) {
            if (e.detail.value[0] == 'agreement') {
                this.agreeCheckBox = true;
            } else {
                this.agreeCheckBox = false;
            }
        },
        // 立即升级按钮点击事件 @submit
        accountUpgrade() {
            if (!this.agreeCheckBox) {
                return;
            } else {
                this.upgradingFlag = true;
                this.$emit('submit');
            }
        },
        // 点击协议 ,参数传协议类型与协议名称
        //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
        // name传
        // 能源e站隐私协议
        // 用户协议
        // cityName可传地区code
        async clickXieyi(type, name) {
            let params = {
                // 协议类型
                agreementType: type,
                // 协议名称
                agreementName: name,
                // 地市中文名称
                cityName: this.province || '全国',
            };
            let res = await getUserAgreementApi(params);
            if (res.result == 'success') {
                this.$util.viewPdfAgreement(this.$util.imgPath(res.data.Data.fileUrl));
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;

    .popup-box {
        width: 570rpx;
        min-height: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, calc(-50% - 73rpx));
        background: #ffffff;
        border-radius: 16rpx 16rpx;

        .popup-content {
            background-size: cover;
            margin-top: -31rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 264rpx 33rpx 33rpx;
            background-repeat: no-repeat;
            background-size: contain;
            background-position: 0 0;

            .popup-content-title {
                font-size: 36rpx;
                font-weight: 500;
                color: #333333;
                line-height: 46rpx;
                margin-bottom: 12rpx;
            }

            .popup-content-text {
                font-size: 28rpx;
                font-weight: 400;
                color: #999999;
                line-height: 39rpx;
                padding: 0 14rpx;
                margin-bottom: 30rpx;
            }

            .popup-content-warning {
                font-size: 24rpx;
                font-weight: 400;
                color: #e64f22;
                line-height: 39rpx;
                margin-bottom: 31rpx;
            }

            .popup-content-agree {
                width: 100%;
                display: flex;
                align-items: baseline;
                text-align: start;
                margin-bottom: 39rpx;
                .popup-content-agree-checkBox {
                    transform: translate(-4px) scale(0.6);
                    margin-top: -4px
                }

                .popup-content-agree-text {
                    flex: 1;
                    flex-shrink: 0;
                    font-size: 24rpx;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 33rpx;
                    transform: translate(-10rpx);
                    align-self:center;
                    span {
                        color: #e64f22;
                    }
                }
            }

            .popup-content-btn {
                width: 100%;
                height: 88rpx;
                background: #e64f22;
                font-size: 30rpx;
                font-weight: 500;
                color: #ffffff;
                line-height: 88rpx;
                border-radius: 16rpx 16rpx;
            }

            .popup-content-btn-next {
                margin-top: 28rpx;
                font-size: 30rpx;
                font-weight: 400;
                color: #999999;
                line-height: 42rpx;
            }
        }

        .disable {
            opacity: 0.3;
        }

        .close-popup {
            position: absolute;
            height: 58rpx;
            width: 58rpx;
            bottom: -117rpx;
            left: 50%;
            transform: translate(-50%);

            img {
                height: 100%;
                width: 100%;
            }
        }
    }
}
</style>
