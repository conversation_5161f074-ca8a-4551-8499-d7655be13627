const fs = require('fs');
const { readFile, resolve } = require('./util');
const filePath = resolve('../src/router/modules');
module.exports = pagesJson => {
    const indexPages = pagesJson.pages || [];
    const subPackagesPages = pagesJson.subPackages || [];
    let indexRoutes = indexPages.map(item => {
        return {
            path: '/' + item.path,
            meta: {
                title: (item.style && item.style.navigationBarTitleText) || '',
            },
        };
    });
    fs.writeFileSync(`${filePath}/index.js`, `export default ${JSON.stringify(indexRoutes)}`, function (err) {
        if (err) return;
    });
    subPackagesPages.forEach(subItem => {
        let subPackagesRoutes = subItem.pages.map(item => {
            return {
                path: '/' + subItem.root + '/' + item.path,
                meta: {
                    title: (item.style && item.style.navigationBarTitleText) || '',
                },
            };
        });
        fs.writeFileSync(
            `${filePath}/${subItem.root.replace(/\//g, '-')}.js`,
            `export default ${JSON.stringify(subPackagesRoutes)}`,
            function (err) {
                if (err) return;
            },
        );
    });
};
