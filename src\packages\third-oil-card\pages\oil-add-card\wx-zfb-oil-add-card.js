import {
  identityAuthInfo,
  sendMessageCode,
  checkMessageCode,
  realNameAuth,
  realPersonIdentify,
  bindCard,
  basicInfoQuery,
} from '../../../../s-kit/js/v3-http/https3/oilCard/index';
export default {
  onShow () {
    clearTimeout(this.countdownTimer);
  },
  computed: {
    fromInput () {
      return 'color:#999999;font-size:14px;';
    },
    fromInput2 () {
      return 'color:#000000;font-size:14px;';
    },
  },
  onLoad () {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
    // 获取用户非脱敏信息
    this.getIdentityAuthInfoPost();
    // 查询用户详细信息接口(使用脱敏身份证号字段)
    this.getBasicInfoQuery();
    this.getVerificationCode = this.$util.throttleUtil(this.getVerificationCode);
  },

  methods: {
    /**
     * @description  : 获取用户信息（证件号姓名，证件号未脱敏）
     * @return        {*}
     */
    async getIdentityAuthInfoPost () {
      let res = await identityAuthInfo();
      if (res.success) {
        this.current.name = res.data.realName;
        this.identityNo = res.data.identityNo;
      }
    },
    /**
     * @description  : 获取用户脱敏身份证信息
     * @return        {*}
     */
    async getBasicInfoQuery () {
      let res = await basicInfoQuery();
      if (res.success) {
        console.log(res.data, '查询支用户脱敏身份证信息');
        this.current.identity = res.data.idNo;
      }
    },
    /**
     * @description  : 获取验证码
     * @param         {*}name 姓名
     * @param         {*}idType 证件类型
     * @param         {*}idNo 证件号码
     * @param         {*}cardNo 卡号
     * @param         {*}messageType: "7",
     * @return        {*}
     */
    async getVerificationCode () {
      console.log(this, 'current');
      console.log('验证码发送方法触发了吗1');
      if (this.count < 60) return;
      if (this.current.name === '') {
        uni.showToast({
          title: '姓名不能为空',
          icon: 'none',
          duration: 2000,
        });
        return;
      } 
      if (this.current.identitytype === '') {
        uni.showToast({
          title: '请选择证件类型',
          icon: 'none',
          duration: 2000,
        });
        return;
      }
      if (this.current.identity == '') {
        uni.showToast({
          title: '请输入证件号码',
          icon: 'none',
          duration: 2000,
        });
        return;
      } else {
        if (this.$test.idCardComplex(this.identityNo) == false) {
          uni.showToast({
            title: '请输入正确格式身份证号',
            icon: 'none',
          });
          return;
        }
      }
      if (this.current.cardNo == '') {
        uni.showToast({
          title: '请输入油卡卡号',
          icon: 'none',
        });
        return;
      } else {
        if (this.$test.checkJYK(this.current.cardNo) == false) {
          uni.showToast({
            title: '请输入正确格式油卡卡号',
            icon: 'none',
          });
          return;
        }
      }

      let params = {
        name: this.current.name, // 姓名
        idType: this.current.identitytype, // 证件类型
        idNo: this.identityNo, // 证件号码
        cardNo: this.current.cardNo, // 卡号
        messageType: '7',
      };

      console.log('验证码发送方法触发了吗2');
      let res = await sendMessageCode(params);
      console.log(res, '验证码结果');
      if (res.success) {
        console.log(res, '获取验证码');
        this.countdown();
        uni.showToast({
          title: '发送验证码成功',
          icon: 'none',
          duration: 2000,
        });
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none',
          duration: 2000,
        });
      }
    },
    /**
     * @description  : 绑定油卡
     * @param         {*}name 姓名
     * @param         {*}idType 证件类型
     * @param         {*}idNo 证件号码
     * @param         {*}cardNo 卡号
     * @param         {*}captcha 验证码
     * @param         {*}messageType: "7",
     * @return        {*}
     */
    boundOilCard () {
      if (this.current.name === '') {
        uni.showToast({
          title: '姓名不能为空',
          icon: 'none',
          duration: 2000,
        });
        return;
      } 
      if (this.current.identity == '') {
        uni.showToast({
          title: '请输入证件号码',
          icon: 'none',
          duration: 2000,
        });
        return;
      }
      if (this.$test.idCardComplex(this.identityNo) == false) {
        uni.showToast({
          title: '请输入正确格式身份证号',
          icon: 'none',
        });
        return;
      }
      if (this.current.cardNo == '') {
        uni.showToast({
          title: '请输入油卡卡号',
          icon: 'none',
        });
        return;
      } else {
        if (this.$test.enOrNum(this.current.cardNo) == false) {
          uni.showToast({
            title: '请输入正确格式油卡卡号',
            icon: 'none',
          });
          return;
        }
      }
      if (this.current.captcha == '') {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none',
        });
        return;
      }
      if (this.current.captcha.length < 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none',
        });
        return;
      }

      // 打开人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', true)

    },
    enterNavEvent () {
      // 关闭人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', false)
      // 检验验证码
      this.checkMessageCode();
    },
    /**
     * @description  : 输入完验证码后点击保存进行校验手机验证码 验证成功调用实名认证
     * @param         {*}name 姓名
     * @param         {*}idType 证件类型
     * @param         {*}idNo 证件号码
     * @param         {*}cardNo 卡号
     * @param         {*}messageType: "7",
     * @param         {*}messageCode 验证码
     * @return        {*}
     */
    async checkMessageCode () {
      let params = {
        // 姓名
        name: this.current.name,
        // 证件类型
        idType: this.current.identitytype,
        // 证件号码
        idNo: this.identityNo,
        // 卡号
        cardNo: this.current.cardNo,
        // 验证码
        messageCode: this.current.captcha,
        messageType: '7',
      };
      let res = await checkMessageCode(params);
      if (res.success) {
        // 开启实名认证
        params.type = 7;
        params.authInfo = res.data.authInfo;
        console.log(params, '绑定加油卡页面的参数');
        this.$sKit.wxRealPersonAuthentication.startVerification(params).then(response => {
          console.log('链路通过，调用绑定加油卡接口', response);
          if (response.success) {
            // 此处的authInfo 是实人认证接口返回的authInfo
            params.authInfo = response.data.authInfo;
            this.bindFuelCard(params);
          } else {
          }
        });
        // this.realNameAuthentication(res.data.authInfo);
      }
    },
    /**
     * @description  : 绑定加油卡
     * @param         {*}name 姓名
     * @param         {*}idType 证件类型
     * @param         {*}idNo 证件号码
     * @param         {*}cardNo 卡号
     * @param         {*}authInfo 加密字符串
     * @param         {*}type 7
     * @return        {*}
     */
    //
    async bindFuelCard (bindFuelCardValue) {
      let params = {
        // 姓名
        name: bindFuelCardValue.name,
        // 证件类型
        idType: bindFuelCardValue.idType,
        // 证件号码
        idNo: bindFuelCardValue.idNo,
        // 卡号
        cardNo: bindFuelCardValue.cardNo,
        authInfo: bindFuelCardValue.authInfo,
        type: bindFuelCardValue.type,
      };
      console.log(params, 'params====绑卡入口参数');
      let res = await bindCard(params);
      if (res.success) {
        this.$sKit.layer.showToast({
          title: '绑卡成功',
        });
        uni.setStorageSync('refreshCardListFlag', true);
        uni.navigateBack({ delta: 1 });
        // let url = ''
        // let type = "navigateBack";
        // let prevParams = {
        //   // 得到上一页面的实例需要传2 若要返回上上页面的实例就传3，以此类推
        //   delta: 2,
        //   // 返回指定页面的data中的标识，用于在调用接口成功后返回该页面时候，调用指定的函数或更改要返回页面中定义的属性值
        //   refreshListFlag: true
        // }

        // this.$sKit.layer.useRouter(url, params, type, prevParams);
      }
    },
    //倒计时
    countdown () {
      this.count = 60;
      console.log('获取验证码');
      this.codeFlag = !this.codeFlag;
      this.countdownTimer = setInterval(() => {
        this.count--;
        if (this.count == 0) {
          this.codeFlag = !this.codeFlag;
          clearInterval(this.countdownTimer);
          this.count = 60;
        }
        console.log(this.count);
      }, 1000);
    },
    /**
     * @description  : 曾用卡绑定
     * @return        {*}
     */
    toUsedCardBind () {
      let url = '/packages/third-oil-card/pages/used-card-bind/main';
      let params = {};
      let type = 'navigateTo';
      this.$sKit.layer.useRouter(url, params, type);
    },
  },
  beforeDestroy () {
    // this.countdownTimer && clearInterval(this.countdownTimer);
    // clearTimeout(this.countdownTimer);
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },
};
