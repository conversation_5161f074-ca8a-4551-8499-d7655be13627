<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="container bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="注册地"></zj-navbar>
            <div class="fl-column p-LR-16">
                <div class="title weight-600 color-333 font-18 te-center">请完善您的注册地</div>
                <div class="selectArea bg-fff fl-row fl-jus-bet border-rad-8 fl-al-cen p-LR-16">
                    <div class="value color-333 font-14 weight-400"> 注册地 </div>
                    <div class="fl-row fl-al-cen" @click="selectPlaceOfDeposit">
                        <div class="color-999 font-14 weight-400">{{
                            locationStateApp || locationState ? registerAddress : '选择地区'
                        }}</div>
                        <img alt="" src="../../image/right.png" />
                    </div>
                </div>
                <div class="color-fff border-rad-8 font-18 btn-44 primary-btn" @click.stop="savEdit()">保存 </div>
            </div>
            <SelectCity
                v-if="showPopup"
                :provinceCityArray="provincesAndCitiesList"
                :show="showPopup"
                @hideShow="hideShow"
                @sureSelectArea="sureSelectArea"
            ></SelectCity>
            <zj-show-modal> </zj-show-modal>
        </div>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
import { modifyUserInfoApi } from '../../../../s-kit/js/v3-http/https3/user';
import { mapState } from 'vuex';
export default {
    mixins: [publicMixinsApi],
    name: '',
    data() {
        return {
            registerAddressCode: '',
            provincesAndCitiesList: [],
            // 选择开户地区弹窗
            showPopup: false,
            registerAddress: '',
            areaCode: '',
            locationStateApp: false,
        };
    },
    components: {
        SelectCity,
    },
    computed: {
        ...mapState({
            locationState: state => state.locationV3_app.locationState,
            cityCode: state => state.locationV3_app.cityCode,
            cityName: state => state.locationV3_app.cityName,
            provinceV3: state => state.locationV3_app.provinceV3,
            cityV3: state => state.locationV3_app.cityV3,
            district: state => state.locationV3_app.district,
        }),
    },
    onLoad(option) {},
    created() {
        this.savEdit = this.$sKit.commonUtil.throttleUtil(this.savEdit); // 节流
    },
    async mounted() {
        // 获取省市地区
        this.getProvincesCities();
        // 检测用户是否开启了位置信息
        // console.log(this.cityCode, this.cityName, this.provinceV3, this.cityV3,this.district, this.locationState, '测试位置');
        this.locationCheck();
    },

    methods: {
        async locationCheck() {
            // #ifdef MP-MPAAS
            this.closeForm = await this.$cnpcBridge.judgeProtocolCall();
            // 当前APP版本号大于3.6.0
            if (this.closeForm) {
                //检查用户定位权限是否开启
                let locationRes = await this.$cnpcBridge.checkPermission();
                if (locationRes.appStatus && locationRes.osStatus) {
                    // 获取定位
                    this.$cnpcBridge.getLocation(res => {
                        if (res.cityCode && res.province && res.city) {
                            this.locationStateApp = true;
                            this.registerAddressCode = res.cityCode;
                            this.registerAddress = res.province + res.city;
                        }
                    });
                }
            }
            // #endif
            // #ifdef MP-WEIXIN || H5-CLOUD
            // 微信小程序回显开卡地址
            console.log(this.cityV3, this.district, this.locationState, '位置信息');
            if (this.cityV3 && this.district && this.locationState) {
                this.registerAddress = this.cityV3 + this.district;
                this.registerAddressCode = this.cityCode;
            }
            // #endif
            // #ifdef MP-ALIPAY
            my.getLocation({
                type: 1,
                success: async res => {
                    // 直辖市城市编码取的是区编码
                    this.registerAddress = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city)
                        ? res.province + res.district
                        : res.province + res.city;
                    this.registerAddressCode = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city)
                        ? res.districtAdcode
                        : res.cityAdcode;
                },
                fail(err) {},
            });
            // #endif
        },
        /**
         * @description  : 选择开户地
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @return        {*}
         */
        selectPlaceOfDeposit() {
            if (this.provincesAndCitiesList.length > 0) {
                this.showPopup = true;
            } else {
                this.getProvincesCities().then(() => {
                    this.showPopup = true;
                });
            }
        },
        /**
         * @description  : 获取省市区
         * @return        {*}
         */
        async getProvincesCities() {
            return new Promise(async (resolve, reject) => {
                let res = await provinceAndCityList({}, { isload: false });
                if (res.success) {
                    let arr2 = res.data.map(item => {
                        let newItem = {
                            name: item.parentAreaName,
                            code: item.parentAreaCode,
                            city: item.areaList,
                        };
                        return newItem;
                    });
                    this.provincesAndCitiesList = arr2;
                    resolve();
                }
            });
        },
        /**
         * @description  : 关闭选择城市弹窗
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @return        {*}
         */
        hideShow() {
            this.showPopup = false;
        },
        /**
         * @description  : 确定选择当前开户地
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @param         {Object} detail -选择常用地城市和编码
         * @return        {*}
         */
        sureSelectArea({ detail }) {
			this.locationStateApp = true
            this.showPopup = false;
            this.registerAddress = detail.province + detail.city;
            this.registerAddressCode = detail.cityCode;
        },
        async savEdit() {
            if (!this.registerAddress) {
                uni.showToast({
                    title: '请选择注册地区',
                    icon: 'none',
                    mask: true,
                    duration: 2000,
                });
                return;
            }
            let params = {
                type: 8,
                registrationPlace: this.registerAddressCode,
                todoType: 3,
            };
            console.log(params, '修改注册地');
            let res = await modifyUserInfoApi(params);
            if (res && res.success) {
                uni.showToast({
                    title: '保存成功',
                    icon: 'success',
                    mask: true,
                    duration: 2000,
                });
                setTimeout(() => {
                    this.goBack();
                });
            }
        },

        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        //返回首页
        backHome() {
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.container {
    height: 100%;
    .title {
        margin-bottom: 16px;
        margin-top: 16px;
    }
    .selectArea {
        width: 100%;
        height: 44px;
        img {
            width: 16px;
            height: 16px;
        }
    }
    .btn-44 {
        height: 44px;
        margin-top: 16px;
        line-height: 44px;
    }
}
</style>
