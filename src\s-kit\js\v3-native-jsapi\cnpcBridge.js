import config from './../third-config.js';
import { minVersion } from '../../../../project.config.js';
import store from '../../../store/index';
import { bd09_To_Gcj02 } from '../map.js';

const cnpcBridge = {
    mriverToNative(jsonData, callBack) {
        my.call(
            'mriverToNative',
            {
                bizType: jsonData.bizType,
                data: jsonData.data || {},
            },
            res => {
                if (callBack) callBack(res);
            },
        );
    },
    /**
     *获取风控设备指纹
     * @param callback
     */
    getFinger(callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'getFinger',
            },
            res => {
                callback(res);
            },
        );
    },
    /**
     *原生存储数据
     * {"key": "xxx", "value": "12345", "bizType": " storeItem"}
     */
    setValueToNative(key, value, callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'storeItem',
                data: {
                    key: key,
                    value: value,
                },
            },
            res => {
                callback(res);
            },
        );
    },
    /**
     *获取存储数据
     *
     */
    getValueToNative(key, callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'getItem',
                data: {
                    key: key,
                },
            },
            res => {
                if (res && res.value) {
                    callback(res.value);
                } else {
                    callback('');
                }
            },
        );
    },
    /**
     *获取存储数据
     *
     */
    getValueToNativePromise(key) {
        return new Promise((resolve, reject) => {
            cnpcBridge.mriverToNative(
                {
                    bizType: 'getItem',
                    data: {
                        key: key,
                    },
                },
                res => {
                    if (res && res.value) {
                        resolve(res.value);
                    } else {
                        resolve('');
                    }
                },
            );
        });
    },
    /**
     *移除存储数据
     *
     */
    removeValueToNative(key, callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'removeItem',
                data: {
                    key: key,
                },
            },
            res => {
                callback(res);
            },
        );
    },
    /**
     * 获取设备公共参数
     * @param callback
     */
    getCommonArgs() {
        return new Promise((resolve, reject) => {
            cnpcBridge.mriverToNative(
                {
                    bizType: 'commonArgs',
                },
                res => {
                    resolve(res);
                },
            );
        });
    },

    /**
     * 获取缓存大小
     * @param callback
     */
    getCacheSize: function (callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'getCacheSize',
            },
            res => {
                callback(res);
            },
        );
    },
    /**
     * 清除缓存
     * @param callback
     */
    clearCache: function (callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'cleanCache',
            },
            res => {
                callback(res);
            },
        );
    },

    /**
     * 获取定位
     * @param callback
     * @param isRemindAuth 如果设备没有开启定位权限，是否弹框提醒用户设置
     * @param isSelence ；isSelence定位失败是否弹框
     * @param isNeedAll  isNeedAll是否需要地区所有字段
     */
    async getLocation(callback, isRemindAuth, isSelence, isNeedAll) {
        let stateInfo = await cnpcBridge.checkPermission();
        if (stateInfo.appStatus) {
            //如果授权过定位权限，再去获取定位
            cnpcBridge.mriverToNative(
                {
                    bizType: 'locationInfo',
                },
                json => {
                    if (!isNeedAll) {
                        if (json.province == json.city) {
                            json.city = json.district;
                            json.cityCode = json.adCode;
                        } else {
                            //如果是普通地市，取区县编码adCode，后两位替换成零,赋值给citycode
                            if (json.adCode.length > 2) json.cityCode = json.adCode.substring(0, json.adCode.length - 2) + '00';
                        }
                        if (!json.province || json.province == '') {
                            json.province = json.city;
                            json.city = json.district;
                            // json.cityCode = json.adCode;
                            if (json.adCode.length > 2) json.cityCode = json.adCode.substring(0, json.adCode.length - 2) + '00';
                        }
                    }
                    // callback({
                    //     longitude: '104.078063',
                    //     latitude: '30.66664',
                    //     province: '北京市',
                    //     city: '北京市',
                    //     district: '东城区',
                    //     cityCode: '110101',
                    //     adCode: '110101',
                    //     state: '0',
                    // });
                    callback(json);
                },
            );
        } else {
            //没有授权定位，回调
            if (isRemindAuth) {
                //需要弹窗提示
                cnpcBridge
                    .confirmDialog({
                        title: '位置权限使用说明',
                        message: '根据您的位置信息获取您附近的加油站网点信息服务,是否开启？',
                        confirmBtnText: '去开启',
                    })
                    .then(() => {
                        cnpcBridge
                            .openPermissions({
                                code: 'location',
                                explain: '位置权限使用说明',
                                detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                            })
                            .then(res => {
                                if (res) {
                                    cnpcBridge.getLocation(callback);
                                } else {
                                    //没开启 回调默认
                                    callback({
                                        longitude: '116.404421',
                                        latitude: '39.914579',
                                        province: '北京市',
                                        city: '北京市',
                                        district: '东城区',
                                        cityCode: '110101',
                                        adCode: '110101',
                                        state: '0',
                                    });
                                }
                            });
                    });
            } else {
                //直接回调默认城市
                callback({
                    longitude: '116.404421',
                    latitude: '39.914579',
                    province: '北京市',
                    city: '北京市',
                    district: '东城区',
                    cityCode: '110101',
                    adCode: '110101',
                    state: '0',
                });
            }
        }
    },
    /**
     * 梆梆加密
     * @param originalInfo
     * @returns {Promise<any>}
     */
    bangbangEncrypt(originalInfo) {
        return new Promise((resolve, reject) => {
            let jsonData = {
                data: {
                    originalInfo: encodeURIComponent(originalInfo),
                },
            };
            jsonData.bizType = 'bangBangEncrypt';
            cnpcBridge.mriverToNative(jsonData, result => {
                console.log('bangBangEncrypt====', JSON.stringify(result));
                resolve(result);
            });
        });
    },
    /**
     * 梆梆解密
     * @param originalInfo
     * @returns {Promise<any>}
     */
    bangBangDecrypt(originalInfo) {
        return new Promise((resolve, reject) => {
            let jsonData = {
                data: {
                    originalInfo: originalInfo,
                },
            };
            jsonData.bizType = 'bangBangDecrypt';
            cnpcBridge.mriverToNative(jsonData, result => {
                resolve(result);
            });
        });
    },
    // 获取用户的token信息
    // {
    //     "phone": "13260233099",
    //     "password": "",
    //     "savePWD": "0",
    //     "token": "c:app:724AED169D144531AB2F82338448BA82",
    //     "token3": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzEwIiwiaXNzIjoiZ3NtcyIsInRpbWUiOjE2OTY2NzMyMzEyMDQsInVzZXJJZCI6IjE1MTAxMzMwMzAxNzc0In0.G2yyChcTIR9vUTc2dSK36dduRlcMbGNZhrKCw11Uc70",
    //     "gsmsToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQtY29kZSI6IkMxMCIsImdyYW50X3R5cGUiOiJ0b2tlbjJjIiwidXNlcl9pZCI6MTUxMDEzMzAzMDE3NzQsInVzZXJfbmFtZSI6IjEzMjYwMjMzMDk5Iiwic2NvcGUiOlsic2VydmVyIl0sImV4cCI6MTY5NjkzMjQzMSwiYXV0aG9yaXRpZXMiOlsiT1VURVIiXSwianRpIjoiYjA2ZmQ0YWQtNmJhZS00ZDJjLTgwNDMtMzhiMTUzNGMwNTJhIiwicmVhbG5hbWUiOiLlvKDmlrDnhacifQ.1KREpV43udYxUIZ2a1w-nXFHbg22yJEgQiSh0pkcZmE",
    //     "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzEwIiwiZXhwaXJlIjoxNjk3ODgxMDMxMjA2LCJpc3MiOiJnc21zIiwiZ3Ntc1JlZnJlc2hUb2tlbiI6ImV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpqYkdsbGJuUXRZMjlrWlNJNklrTXhNQ0lzSW1keVlXNTBYM1I1Y0dVaU9pSjBiMnRsYmpKaklpd2lkWE5sY2w5cFpDSTZNVFV4TURFek16QXpNREUzTnpRc0luVnpaWEpmYm1GdFpTSTZJakV6TWpZd01qTXpNRGs1SWl3aWMyTnZjR1VpT2xzaWMyVnlkbVZ5SWwwc0ltRjBhU0k2SW1Jd05tWmtOR0ZrTFRaaVlXVXROR1F5WXkwNE1EUXpMVE00WWpFMU16UmpNRFV5WVNJc0ltVjRjQ0k2TVRZNU56STNPREF6TVN3aVlYVjBhRzl5YVhScFpYTWlPbHNpVDFWVVJWSWlYU3dpYW5ScElqb2lNVFJsTXpKbFptRXRPVFZrWlMwME1EUmtMV0l6T0RjdE9EVmlNRFk0TkdWak1EaGlJaXdpY21WaGJHNWhiV1VpT2lMbHZLRG1sckRuaGFjaWZRLldKNjZxeXEybU1VZlFVYnZySHFJMnl5NTBtamJ0eENKUEtaTEJiLTRrenciLCJ0aW1lIjoxNjk2NjczMjMxMjA2LCJ1c2VySWQiOiIxNTEwMTMzMDMwMTc3NCJ9.BE82z0hWwdiA300GuCFmB4BcYqX9fEYuVWu5vitDBKE",
    //     "expiresIn": "1696931831206",
    //     "needBindPhone": "",
    //     "memberNo": "15101330301774",
    //     "bindCardState": "",
    //     "verifyStatus": false
    // }
    getUserTokenInfo() {
        return new Promise((resolve, reject) => {
            cnpcBridge.getValueToNative('UserTokenInfo', value => {
                if (value) {
                    resolve(JSON.parse(decodeURIComponent(value)));
                }
            });
        });
    },
    /**
     * 获取2.0存储的vuex值
     * @returns {Promise<any>}
     */
    getVuex2Info() {
        return new Promise((resolve, reject) => {
            cnpcBridge.getValueToNative('vuex', value => {
                if (value) {
                    resolve(JSON.parse(decodeURIComponent(value)));
                }
            });
        });
    },
    //关闭小程序
    closeMriver(callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'closeMriver',
                data: {
                    appId: config.appId,
                },
            },
            res => {
                if (callback) callback(res);
            },
        );
    },
    //选择油站信息回调

    selectStationCallBack(params, callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'updateStationInfo',
                data: params,
            },
            res => {
                if (callback) callback(res);
            },
        );
    },
    /**
     * showToast提示
     * @param msg
     */
    showToast(msg) {
        let jsonData = {};
        jsonData.bizType = 'showToast';
        jsonData.data = {
            message: msg,
        };
        cnpcBridge.mriverToNative(jsonData);
    },
    /**
     * alert提示框
     * @param message
     */
    alertDialog(message, callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'dialog',
                data: {
                    type: 'alert',
                    message: message,
                },
            },
            res => {
                if (callback) callback();
            },
        );
    },
    /**
     * 带业务类型的提示框 alert提示框
     * @param message
     * @param businessType
     */
    businessTypeAlertDialog(message, businessType = 'LoginExpired', callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'dialog',
                data: {
                    type: 'alert',
                    message: message,
                    businessType: businessType,
                },
            },
            res => {
                if (callback) callback();
            },
        );
    },
    /**
     * confirmDialog
     * @param message
     */
    confirmDialog(data) {
        return new Promise((resolve, reject) => {
            cnpcBridge.mriverToNative(
                {
                    bizType: 'dialog',
                    data: {
                        type: 'confirm',
                        ...data,
                    },
                },
                res => {
                    if (res && JSON.stringify(res) !== '{}') resolve(res);
                    else reject();
                },
            );
        });
    },
    /**
     * 百度导航
     * @param data
     * 传入起始点startlong startlat 和终点的经纬度 endlong  endlat
     */
    baiduNav: function (data) {
        if (!data || !data.startlong || !data.startlat || !data.endlong || !data.endlat) {
            return;
        }
        let jsonData = {
            bizType: 'nav',
            data: data,
        };
        cnpcBridge.mriverToNative(jsonData, null);
    },
    /**
     * 兼容小程序传入参数。
     * @param params
     */
    openLocation: function (params) {
        cnpcBridge.getLocation(res => {
            let newObj = {};
            if (store.state.thirdIndex.isHarmony) {
                newObj = bd09_To_Gcj02(res.longitude, res.latitude);
            } else {
                newObj = res;
            }
            cnpcBridge.baiduNav({
                startlong: Number(newObj.longitude),
                startlat: Number(newObj.latitude),
                endlong: Number(params.longitude),
                endlat: Number(params.latitude),
                name: params.name,
                address: params.address,
            });
        });
    },
    /**
     * 获取app的环境
     * @param callBack
     */
    workspaceName() {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'workspaceName';
            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res.value);
            });
        });
    },
    /**
     * 获取设备支持生物识别的类型
     * @param callback
     */
    getSupportType(getDenied = false) {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'supportType';
            cnpcBridge.mriverToNative(jsonData, resultInfo => {
                // {
                //     "faceID":'1',
                //     "touchID":"1"
                // }
                if (getDenied) {
                    resolve(resultInfo.hasOwnProperty('denied') ? 1 : 0);
                } else {
                    if (resultInfo.hasOwnProperty('touchID') && resultInfo.touchID == '1') {
                        resolve(1);
                    } else if (resultInfo.hasOwnProperty('faceID') && resultInfo.faceID == '1') {
                        resolve(2);
                    } else {
                        resolve(0);
                    }
                }
            });
        });
    },
    /**
     * 面部开始识别
     * @param callBack
     */
    faceIDRe: function (callBack) {
        let jsonData = {};
        jsonData.bizType = 'faceID';
        cnpcBridge.mriverToNative(jsonData, res => {
            callBack(res.result);
        });
    },
    /**
     * 指纹开始识别
     * @param callBack
     */
    fingerPrintRe: function (callBack) {
        let jsonData = {};
        jsonData.bizType = 'fingerPrintRe';
        cnpcBridge.mriverToNative(jsonData, res => {
            callBack(res.result);
        });
    },
    /**
     * 根据类型调用生物识别
     * @param callBack
     */
    async localAuthVerifica(callBack) {
        let supprtType = await cnpcBridge.getSupportType();
        if (!supprtType) {
            //设备不支持人脸或者指纹
            callBack(false);
        } else {
            if (supprtType == '2') {
                //人脸验证
                cnpcBridge.faceIDRe(callBack);
            } else if (supprtType == '1') {
                //指纹验证
                cnpcBridge.fingerPrintRe(callBack);
            }
        }
    },
    /**
     * 阿里人脸采集MetaInfo
     * @param callBack  采集结果回调
     */
    aliMetaInfo() {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'aliMetaInfo';

            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res.metaInfo);
            });
        });
    },
    /**
     * 阿里人脸采集
     * @param certifyId 初始化返回的阿里唯一识别的id
     * @param callBack 采集结果回到
     */
    async aliFaceCollec(certifyId, callBack) {
        let jsonData = {};
        jsonData.bizType = 'faceCollecAli';
        jsonData.data = {
            certifyId: certifyId,
        };
        let info = await cnpcBridge.checkPermission('1');
        if (info && info.appStatus) {
            cnpcBridge.mriverToNative(jsonData, callBack);
        } else {
            cnpcBridge
                .openPermissions({
                    code: 'camera',
                    explain: '访问相机权限使用说明',
                    detail: '用于扫一扫、图片上传、拍照、人脸认证的服务。',
                })
                .then(value => {
                    if (value) {
                        cnpcBridge.mriverToNative(jsonData, callBack);
                    }
                });
        }
    },
    /**
     * 加签字符串
     * @param callBack
     */
    dataAddSign(signStr) {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'dataAddSign';
            jsonData.data = {
                signStr: encodeURIComponent(signStr),
            };
            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res.value);
            });
        });
    },
    /**
     * 打开设置
     * @param callBack
     */
    openPermissionsSetting(code) {
        let jsonData = {};
        jsonData.bizType = 'openPermissions';
        jsonData.data = {
            code: code || 'location',
        };
        cnpcBridge.mriverToNative(jsonData, res => {});
    },
    /**
     * 退出登录
     * @param {} callback
     */
    loginOut() {
        cnpcBridge.mriverToNative(
            {
                bizType: 'logout',
            },
            res => {},
        );
    },
    /**
     * 支付宝支付
     * @param info 预下单字符串
     * @param callback 支付回调
     */
    aliPay: function (info, callback) {
        let jsonData = {};
        jsonData.bizType = 'aliPay';
        jsonData.data = {
            aliPreapy: info,
        };
        cnpcBridge.mriverToNative(jsonData, callback);
    },
    /**
     * 微信支付
     * @param info 预下单字符串
     * @param callback 支付回调
     */
    wxPay: function (info, callback) {
        let jsonData = {};
        jsonData.bizType = 'wechatPay';
        jsonData.data = {
            payinfo: info,
        };
        cnpcBridge.mriverToNative(jsonData, callback);
    },
    /**
     * 用手机浏览器打开url
     * @param url
     */
    mobileBrowsers: function (url) {
        let jsonData = {
            data: { weburl: url },
        };
        jsonData.bizType = 'mobileBrowsers';
        cnpcBridge.mriverToNative(jsonData, () => {});
    },
    /**
     * 判断昆仑银行app是否安装
     * @returns {Promise<any>}
     */
    isHasKLBank: function () {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'isHasKLBank';
            cnpcBridge.mriverToNative(jsonData, result => {
                resolve(result);
            });
        });
    },
    /**
     * 昆仑银行支付
     * @param jsonData
     * @param callBack
     */
    kunlunPay: function (jsonData, callBack) {
        let lastdata = {
            data: jsonData,
            bizType: 'kunlunPay',
        };
        cnpcBridge.mriverToNative(lastdata, callBack);
    },
    /**
     * 银联支付
     * @param unionPayTN
     * @param callBack
     */
    unionPay: function (unionPayTN, callBack) {
        let jsonData = {};
        jsonData.bizType = 'unionPay';
        jsonData.data = {
            unionPayTN: unionPayTN,
        };
        cnpcBridge.mriverToNative(jsonData, callBack);
    },
    /**
     * 鲲鹏支付
     * @param orderStr  预下单字符串
     * @param callBack 支付结果回调
     */
    kpPay: function (orderStr, callBack) {
        let jsonData = {};
        jsonData.bizType = 'kpPay';
        jsonData.data = {
            orderStr: orderStr,
        };
        cnpcBridge.mriverToNative(jsonData, callBack);
    },
    /**
     * 获取手机状态栏高度
     * @param callback
     */
    getBarHeight: function (callback) {
        let jsonData = {};
        jsonData.bizType = 'barHeight';
        cnpcBridge.mriverToNative(jsonData, res => {
            let heightStr = res.height + '';
            callback(parseInt(heightStr));
        });
    },
    /**
     * 打开pdf
     * @param jsonData
     * @param callBack
     */
    checkPDF: function (webUrl, callBack) {
        let jsonData = {};
        jsonData.bizType = 'checkPDF';
        jsonData.data = { webUrl: webUrl };
        cnpcBridge.mriverToNative(jsonData, callBack);
    },

    /*
     * openOldWeb 打开老的一个网页
     * @param data
     */
    openOldWeb(data) {
        let jsonData = {};
        jsonData.bizType = 'openOldWeb';
        jsonData.data = {
            pageUrl: '/middlepage?targetUrl=' + encodeURIComponent(data.pageUrl),
            hasNativeTop: data.hasNativeTop || '0',
        };
        cnpcBridge.mriverToNative(jsonData);
    },
    /**
     * app检查更新
     * @param jsonData
     */
    checkUpdate: function () {
        let jsonData = {};
        jsonData.bizType = 'checkUpdate';
        cnpcBridge.mriverToNative(jsonData, null);
    },
    /**
     * 获取开关项
     * @param
     */
    getSwitch(key, callback) {
        let jsonData = {};
        jsonData.bizType = 'getSwitch';
        jsonData.data = {
            key: key,
        };
        cnpcBridge.mriverToNative(jsonData, res => {
            callback(res.value);
        });
    },

    /**
     * 打开小程序或web
     */
    openModule(data) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'openModule',
                data: data,
            },
            res => {},
        );
    },
    /*
     * 点击埋点
     * @param data
     */
    setClickEvent(name, extParam) {
        let jsonData = {};
        jsonData.bizType = 'setEvent';
        jsonData.data = {
            eventId: name,
            extParam: {
                ...extParam,
            },
        };
        cnpcBridge.mriverToNative(jsonData, res => {});
    },
    /**
     * 获取当前小程序的版本号
     * @param callback
     */
    getMiniVersion(miniAppId) {
        return new Promise(async (resolve, reject) => {
            //当前版本的app是否可以调用此协议
            let isCall = await cnpcBridge.judgeProtocolCall();
            if (isCall) {
                cnpcBridge.mriverToNative(
                    {
                        bizType: 'getMiniVersion',
                        data: {
                            appId: miniAppId,
                        },
                    },
                    res => {
                        resolve(res.value);
                    },
                );
            } else {
                resolve('');
            }
        });
    },
    /**
     * 判断协议是否可以调用
     * @param callback
     * @param protocolVersion 协议在app那个版本之上才可用，跟当前app的版本号进行对比，app版本比version高，才可以执行
     */
    judgeProtocolCall(version) {
        return new Promise(async (resolve, reject) => {
            let protocolVersion = version ? version : minVersion;
            let commonArgs = await cnpcBridge.getCommonArgs();
            let appVersion = commonArgs.appVersion;
            let s1 = appVersion.split('.');
            let s2 = protocolVersion.split('.');
            if (s1.length !== s2.length) {
                if (s1.length < s2.length) {
                    for (let n = 1; n <= s2.length - s1.length; n++) {
                        appVersion += '.0';
                    }
                } else {
                    for (let n = 1; n <= s1.length - s2.length; n++) {
                        protocolVersion += '.0';
                    }
                }
            }
            s1 = appVersion.split('.');
            s2 = protocolVersion.split('.');
            for (let i = 0; i < s1.length; i++) {
                let t1 = Number(s1[i]);
                let t2 = Number(s2[i]);
                if (t1 > t2) {
                    resolve(true);
                } else if (t1 < t2) {
                    resolve(false);
                }
            }
            // true的情况是 两个版本号相同
            resolve(true);
        });
    },
    /*
     * openOldWeb 打开设置页面
     * @param data
     */
    openPermissions(info) {
        return new Promise(async (resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'openPermissions';
            jsonData.data = {
                code: info.code || 'location',
                explain: info.explain || '位置权限使用说明',
                detail: info.detail || '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
            };
            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res.value);
            });
        });
    },
    /*
     * openOldWeb 检查设备权限
     * @param data permission 0为定位权限 1为拍照权限
     * {“osStatus”:true,“appStatus”:false}
     */
    checkPermission(permission) {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'checkPermission';
            jsonData.data = {
                permission: permission || '0',
            };
            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res);
            });
        });
    },
    /*
     * openOldWeb 刷新token
     * {"result":true // true刷新成功，false失败}
     */
    refreshToken() {
        return new Promise((resolve, reject) => {
            let jsonData = {};
            jsonData.bizType = 'refreshToken';
            cnpcBridge.mriverToNative(jsonData, res => {
                resolve(res);
            });
        });
    },
    writeMpaasLog(tag, content) {
        let jsonData = {};

        jsonData.data = {
            tag: tag,
            content: content,
        };

        jsonData.bizType = 'writeMpaasLog';
        cnpcBridge.mriverToNative(jsonData, res => {});
    },
    /**
     *分享
     * webPageUrl：分享网页链接
     * title：网页标题
     * description：网页描述
     * imageUrl：网页位图
     * sceneType：分享类型  0微信好友，1朋友圈，默认0
     */
    wechatShare(data) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'wechatShare',
                data: data,
            },
            res => {},
        );
    },
    /**
     *防截屏
     * isCutScreen: boolean 类型 true禁止截屏，talse支持截屏
     */
    isCutScreen(data) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'isCutScreen',
                data: { isCutScreen: data },
            },
            res => {},
        );
    },
    /**
     * 打开建行钱包主页面
     * @param data
     *  productId: 'CCBWlt',
         scnId: 'Main_CCBWlt_Home',
         ccbPhone: this.userInfo.phone,
         closeBtnColor: '#F96702',
         ccbFlowNo: data.data.ccbFlowNo
     */
    intoCCBWalletPage(data) {
        let params = {
            productId: 'CCBWlt',
            scnId: 'Main_CCBWlt_Home',
            closeBtnColor: '#F96702',
            ...data,
        };
        // cnpcBridge.alertDialog(JSON.stringify(params))
        cnpcBridge.mriverToNative(
            {
                bizType: 'intoCCBWalletPage',
                data: params,
            },
            res => {},
        );
    },
    /**
     * 打开建行钱包主页面
     * @param data
     *  name pdf名，可为空
     *  url 下载链接
     */
    downloadPdf(data, callback) {
        let params = {
            url: data.url,
            name: data.name || '',
        };
        console.log(params, '下载发票');

        // cnpcBridge.alertDialog(JSON.stringify(params))
        cnpcBridge.mriverToNative(
            {
                bizType: 'downloadPdf',
                data: params,
            },
            res => {
                console.log(res, '下载发票2');
                if (callback) callback(res);
            },
        );
    },
    /**
     * 打开建行钱包主页面
     * @param data
     *  filePath 本地文件存储路径
     */
    openFile(data, callback) {
        let params = {
            filePath: data.filePath,
        };
        // cnpcBridge.alertDialog(JSON.stringify(params))
        cnpcBridge.mriverToNative(
            {
                bizType: 'openFile',
                data: params,
            },
            res => {
                if (callback) callback(res);
            },
        );
    },
    /**
     * 初始化建行联合会员
     */
    openDevelopBank(callback) {
        cnpcBridge.mriverToNative(
            {
                bizType: 'openDevelopBank',
            },
            res => {
                if (callback) callback(res);
            },
        );
    },
    /**
     * 小程序授权
     */
    authLogin(data, callback) {
        console.log('小程序授权协议回调', 1);
        cnpcBridge.mriverToNative(
            {
                bizType: 'authLogin',
                data: data,
            },
            res => {
                console.log('小程序授权协议回调', 2);
                if (callback) callback(res);
            },
        );
        // my.call(
        //     'authLogin',
        //     {
        //         data: jsonData.data || {},
        //     },
        //     res => {
        //         if (callBack) callBack(res);
        //     },
        // );
    },
    authLoginSplit(data, callBack) {
        my.call('authLogin', data, res => {
            if (callBack) callBack(res);
        });
    },
};
export default cnpcBridge;
