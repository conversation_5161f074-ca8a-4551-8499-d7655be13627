<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="pageView">
            <zj-navbar :border-bottom="false" title="更换绑定手机号"></zj-navbar>
            <div class="content">
                <div class="step step1" v-if="step == 1">
                    <img src="../../image/error.png" alt />
                    <div class="text">您每年可更换2次手机号；</div>
                    <div class="text">您当前账户下有未使用完的电子券，建议您使用完后进行变更手机号。</div>
                    <div class="btns">
                        <div class="btn1" @click="continuingChanges()">继续变更</div>
                        <div class="btn2">我知道了</div>
                    </div>
                </div>
                <div class="step step2" v-if="step == 2">
                    <img src="../../image/error.png" alt />
                    <div class="title">
                        如您选择继续变更
                        <br />这些电子券将视为自愿放弃！
                    </div>
                    <div class="text">
                        您有电子券与当前手机号绑定，暂不支持变更到新的手机号上。
                        <div class="height-light" @click="goCouponList()">点击查看</div>
                    </div>
                    <div class="btns">
                        <div class="btn1" @click="confirm()">确认变更</div>
                        <div class="btn2" @click="back">我再想想</div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'appeal-result',
    data() {
        return {
            step: 2,
            identityAuthStatus: '',
        };
    },
    onLoad(option) {},
    mounted() {
        this.getuserBasicInfoQuery(); //查询用户详细信息--获取用户认证状态
    },
    methods: {
        // 变更手机号，继续变更
        continuingChanges() {
            this.step = 2;
        },
        //查询用户详细信息--获取用户认证状态
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.identityAuthStatus = res.data.identityAuthStatus;
            }
        },
        // 确认变更
        confirm() {
            let url;
            let params = {};
            let type = 'navigateTo';
            if (this.identityAuthStatus == 15) {
                url = '/packages/third-my-center/pages/real-person/main'; //去实人认证
            } else {
                url = '/packages/third-account/pages/account-phone-verify/main'; //去短信验证码验证
            }
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 返回上一页
        back() {
            uni.navigateBack({
                delta: 1, //返回的页面数
            });
        },
        // 跳转电子券列表
        goCouponList() {
            let url = '/packages/third-account/pages/coupon-list/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
};
</script>

<style lang="scss" scoped>
.pageView {
    width: 100%;
    height: 100%;
    background: #f7f7fb;

    .content {
        padding: 80rpx 32rpx 32rpx;
        width: 100%;

        .step {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 128rpx;
                height: 128rpx;
            }

            .title {
                font-size: 32rpx;
                font-weight: bold;
                color: #333333;
                line-height: 46rpx;
                margin-top: 32rpx;
                text-align: center;
            }

            .text {
                width: 100%;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 43rpx;
                padding: 0 23rpx;

                .height-light {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #e64f22;
                    line-height: 43rpx;
                    display: inline-block;
                }
            }

            .btns {
                display: flex;
                justify-content: space-between;
                width: 100%;
                margin-top: 80rpx;

                div {
                    width: 331rpx;
                    height: 88rpx;
                    font-size: 36rpx;
                    line-height: 88rpx;
                    text-align: center;
                    border-radius: 8px;
                }

                .btn1 {
                    background: #ffffff;
                    border: 1rpx solid #e64f22;
                    color: #e64f22;
                }

                .btn2 {
                    background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                    box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
                    color: #ffffff;
                }
            }
        }

        .step1 {
            .text {
                &:nth-of-type(1) {
                    margin-top: 80rpx;
                }

                &:nth-of-type(2) {
                    margin-top: 27rpx;
                }
            }
        }

        .step2 {
            .text {
                margin-top: 30rpx;
            }
        }

        .step2 {
        }
    }
}
</style>
