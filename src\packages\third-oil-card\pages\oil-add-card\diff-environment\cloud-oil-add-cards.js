import {
    identityAuthInfo,
    sendMessageCode,
    checkMessageCode,
    realNameAuth,
    realPersonIdentify,
    bindCard,
    basicInfoQuery,
} from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { clientCode } from '../../../../../../project.config.js';
import { setCloud } from '../../../../../s-kit/js/setCloud';
export default {
    // #ifdef H5-CLOUD
    data() {
        return {
            isAuth: false,
            faceValueResponse: null,
        };
    },
    computed: {
        fromInput() {
            return 'color:#999999;font-size:14px;';
        },
        fromInput2() {
            return 'color:#000000;font-size:14px;';
        },
    },
    onLoad(options) {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }
        // 获取用户非脱敏信息
        this.getIdentityAuthInfoPost();
        // 查询用户详细信息接口(使用脱敏身份证号字段)
        this.getBasicInfoQuery();
        this.getVerificationCode = this.$util.throttleUtil(this.getVerificationCode);
        console.log('options---', options);
    },
    onShow() {
        if (this.isAuth) {
            this.isAuth = false;
            if (this.faceValueResponse) {
                this.realNameAuthentication(this.faceValueResponse);
            }
            return;
        }
    },
    mounted() {
        //   setTimeout(() => {
        //       let that = this
        //       setCloud("black","0xFF000000","0","0",function (data) {
        //           // 这里的data就是接收到的dataSystemInfo
        //           console.log('在外部接收到的系统信息:', data,data.statusBarHeight);
        //           // 可以在这里进一步处理dataSystemInfo
        //           that.systemBar = data ? Number(data.statusBarHeight) : '';
        //           // 可在此处处理data
        //           that.$store.commit('mSetUpaskSystem',data)
        //       });
        //   }, 1000);
    },
    methods: {
        /**
         * @description  : 获取用户信息（证件号姓名，证件号未脱敏）
         * @return        {*}
         */
        async getIdentityAuthInfoPost() {
            let res = await identityAuthInfo();
            if (res.success) {
                this.current.name = res.data.realName;
                this.identityNo = res.data.identityNo;
            }
        },
        /**
         * @description  : 获取用户脱敏身份证信息
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery();
            if (res.success) {
                console.log(res.data, '查询支用户脱敏身份证信息');
                this.current.identity = res.data.idNo;
            }
        },
        /**
         * @description  : 获取验证码
         * @param         {*}name 姓名
         * @param         {*}idType 证件类型
         * @param         {*}idNo 证件号码
         * @param         {*}cardNo 卡号
         * @param         {*}messageType: "7",
         * @return        {*}
         */
        async getVerificationCode() {
            console.log('验证码发送方法触发了吗1');
            if (this.current.name === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.current.identitytype === '') {
                uni.showToast({
                    title: '请选择证件类型',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.current.identity == '') {
                uni.showToast({
                    title: '请输入证件号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else {
                if (this.$test.idCardComplex(this.identityNo) == false) {
                    uni.showToast({
                        title: '请输入正确格式身份证号',
                        icon: 'none',
                    });
                    return;
                }
            }
            if (this.current.cardNo == '') {
                uni.showToast({
                    title: '请输入油卡卡号',
                    icon: 'none',
                });
                return;
            } else {
                if (this.$test.checkJYK(this.current.cardNo) == false) {
                    uni.showToast({
                        title: '请输入正确格式油卡卡号',
                        icon: 'none',
                    });
                    return;
                }
            }

            let params = {
                name: this.current.name, // 姓名
                idType: this.current.identitytype, // 证件类型
                idNo: this.identityNo, // 证件号码
                cardNo: this.current.cardNo, // 卡号
                messageType: '7',
            };

            console.log('验证码发送方法触发了吗2');
            let res = await sendMessageCode(params);
            console.log(res, '验证码结果');
            if (res.success) {
                console.log(res, '获取验证码');
                this.countdown();
                uni.showToast({
                    title: '发送验证码成功',
                    icon: 'none',
                    duration: 2000,
                });
            } else {
                uni.showToast({
                    title: res.message,
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        /**
         * @description  : 绑定油卡
         * @param         {*}name 姓名
         * @param         {*}idType 证件类型
         * @param         {*}idNo 证件号码
         * @param         {*}cardNo 卡号
         * @param         {*}captcha 验证码
         * @param         {*}messageType: "7",
         * @return        {*}
         */
        boundOilCard() {
            if (this.current.name === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } 
            if (this.current.identity == '') {
                uni.showToast({
                    title: '请输入证件号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.$test.idCardComplex(this.identityNo) == false) {
                uni.showToast({
                    title: '请输入正确格式身份证号',
                    icon: 'none',
                });
                return;
            }
            if (this.current.cardNo == '') {
                uni.showToast({
                    title: '请输入油卡卡号',
                    icon: 'none',
                });
                return;
            } else {
                if (this.$test.enOrNum(this.current.cardNo) == false) {
                    uni.showToast({
                        title: '请输入正确格式油卡卡号',
                        icon: 'none',
                    });
                    return;
                }
            }
            if (this.current.captcha == '') {
                uni.showToast({
                    title: '请输入验证码',
                    icon: 'none',
                });
                return;
            }
            if (this.current.captcha.length < 6) {
                uni.showToast({
                    title: '请输入6位验证码',
                    icon: 'none',
                });
                return;
            }
            // 打开人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', true);
        },
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            // 检验验证码
            this.checkMessageCode();
        },
        /**
         * @description  : 输入完验证码后点击保存进行校验手机验证码 验证成功调用实名认证
         * @param         {*}name 姓名
         * @param         {*}idType 证件类型
         * @param         {*}idNo 证件号码
         * @param         {*}cardNo 卡号
         * @param         {*}messageType: "7",
         * @param         {*}messageCode 验证码
         * @return        {*}
         */
        async checkMessageCode() {
            let params = {
                // 姓名
                name: this.current.name,
                // 证件类型
                idType: this.current.identitytype,
                // 证件号码
                idNo: this.identityNo,
                // 卡号
                cardNo: this.current.cardNo,
                // 验证码
                messageCode: this.current.captcha,
                messageType: '7',
            };
            let res = await checkMessageCode(params);
            if (res.success) {
                // 开启实名认证
                params.type = 7;
                params.authInfo = res.data.authInfo;
                console.log(params, '绑定加油卡页面的参数');
                this.$sKit.h5RealPersonAuthentication.startVerification(params).then(response => {
                    console.log('链路通过，调用绑定加油卡接口', response);
                    if (response) {
                        this.faceValueResponse = response;
                        this.isAuth = true;
                        upsdk.pluginReady(function () {
                            upsdk.createWebView({
                                url: response.certifyUrl,
                                isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                            });
                        });
                    } else {
                    }
                });
            }
        },
        /**
         * @description  : 绑定加油卡
         * @param         {*}name 姓名
         * @param         {*}idType 证件类型
         * @param         {*}idNo 证件号码
         * @param         {*}cardNo 卡号
         * @param         {*}authInfo 加密字符串
         * @param         {*}type 7
         * @return        {*}
         */
        //
        async bindFuelCard(bindFuelCardValue) {
            let params = {
                // 姓名
                name: bindFuelCardValue.name,
                // 证件类型
                idType: bindFuelCardValue.idType,
                // 证件号码
                idNo: bindFuelCardValue.idNo,
                // 卡号
                cardNo: bindFuelCardValue.cardNo,
                authInfo: bindFuelCardValue.authInfo,
                type: bindFuelCardValue.type,
            };
            console.log(params, 'params====绑卡入口参数');
            let res = await bindCard(params);
            if (res.success) {
                this.$sKit.mpBP.tracker('绑定实体卡', {
                    seed: 'bindCardBiz',
                    pageID: 'bindSuccessToast',
                    refer: this.refer,
                    channelID: clientCode,
                });
                this.$sKit.layer.showToast({
                    title: '绑卡成功',
                });
                uni.setStorageSync('refreshCardListFlag', true);
                uni.navigateBack({ delta: 1 });
            }
        },
        //倒计时
        countdown() {
            const TIME_COUNT = 60;
            if (!this.countdownTimer) {
                this.count = TIME_COUNT;
                this.codeFlag = false;
                this.countdownTimer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.codeFlag = true;
                        clearInterval(this.countdownTimer);
                        this.countdownTimer = null;
                    }
                }, 1000);
            }
        },
        /**
         * @description  : 曾用卡绑定
         * @return        {*}
         */
        toUsedCardBind() {
            let url = '/packages/third-oil-card/pages/used-card-bind/main';
            let params = { refer: this.refer };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  :  实人认证
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
         * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
         * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
         * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {string} idNo: 用户身份证号
         * @return        {*}
         */
        realNameAuthentication(faceValue) {
            return new Promise(async (resolve, reject) => {
                let params = {
                    // 认证校验码，初始化实人认证接口返回的数据。
                    authInfo: faceValue.authInfo,
                    // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡；10-资金转出；
                    type: faceValue.type || 1,
                    // 认证接入方式：1—APP接入；2—PC或H5接入；
                    verifyMode: '2',
                    // 当type=1时该字段为必填项。
                    idNo: faceValue.idNo,
                    // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
                    verifyUnique: faceValue.verifyUnique,
                    // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
                    certifyId: faceValue.certifyId,
                    gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
                };
                console.log(params, 'H5传入实人认证接口的参数');
                let res = await realPersonIdentify(params);
                if (res.success && res.data.authInfo) {
                    console.log(res, '实人认证方法接口返回结果');
                    let resParams = {
                        // 姓名
                        name: this.current.name,
                        // 证件类型
                        idType: this.current.identitytype,
                        // 证件号码
                        idNo: this.identityNo,
                        // 卡号
                        cardNo: this.current.cardNo,
                        // 验证码
                        messageCode: this.current.captcha,
                        messageType: '7',
                        authInfo: res.data.authInfo,
                    };
                    this.bindFuelCard(resParams);
                    resolve(res);
                } else {
                }
            });
        },
    },
    beforeDestroy() {
        // this.countdownTimer && clearInterval(this.countdownTimer);
        // clearTimeout(this.countdownTimer);
        if (this.countdownTimer) {
            console.log('微信添加油卡页面销毁钩子======');
            clearInterval(this.countdownTimer);
        }
    },
    // #endif
};
