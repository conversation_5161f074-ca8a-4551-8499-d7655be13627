<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <div class="oil-charge-pay-result fl-column">
            <zj-navbar :border-bottom="false" :height="44" title="加油账单"></zj-navbar>
            <div class="content">
                <div class="station-title">
                    <img alt="" src="../../images/icon-logo-24.png" />
                    {{ result.stationName }}
                </div>
                <div class="mar2412">
                    <oilChargeBillTime ref="child" :resultData="result"></oilChargeBillTime>
                </div>
                <oilChargeBillInfo :resultData="result"></oilChargeBillInfo>
            </div>
            <div class="btn_div p-LR-16 fl-jus-bet">
                <div
                    v-if="orderCommentFlag == '1' && result.orderNo.length >= 20"
                    class="finish_verification btn-plain color-E64F22 opacity font-16"
                    @click="evaluationAction"
                    >去评价</div
                >
                <div v-else class="finish_verification btn-plain color-E64F22 opacity font-16" @click="backMainPage">返回首页</div>
                <div class="btnColor finish_verification color-fff font-16 opacity" @click="openInvoice">去开票</div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import oilChargeBillInfo from '../../components/pay-result/oil-charge-bill-info/index.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import oilChargeBillTime from '../../components/pay-result/oil-charge-bill-time/index.vue';
import { consumeOrderDetailApi } from '../../../../s-kit/js/v3-http/https3/order/index';
export default {
    mixins: [publicMixinsApi],
    components: {
        oilChargeBillTime,
        oilChargeBillInfo,
    },
    data() {
        return {
            //加油账单数据
            result: {},
            //订单编号
            orderNo: '',
            orderCommentFlag: 1,
            orderInfo: {},
            actualPayTotalAmount: 0,
            maskPage: false,
            o2oGoodList: [],
            o2oShoppingList: [],
            o2oShoppingNum: 0,
            o2oBusiness: 0,
            codeImg: true,
        };
    },
    mounted() {},
    onLoad(options) {
        this.orderInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : '';
        this.orderNo = this.orderInfo.orderNo;
        this.$nextTick(() => {
            this.resultPost();
        });
        this.evaluationAction = this.$sKit.commonUtil.throttleUtil(this.evaluationAction);
        this.openInvoice = this.$sKit.commonUtil.throttleUtil(this.openInvoice);
    },
    onShow() {},
    methods: {
        //查询已支付完成的订单详情
        async resultPost() {
            let res = await consumeOrderDetailApi({
                orderNo: this.orderNo,
                stationCode: this.orderInfo.stationCode,
            });
            if (res.success) {
                this.result = res.data || '';
                console.log(JSON.stringify(this.result), '订单信息');
                this.actualPayTotalAmount = Number(res.data.actualPayTotalAmount);
                this.getOrderCommentFlag();
                this.maskPage = true;
            }
        },
        // 去开票
        async openInvoice() {
            let apiParams = {
                orderList: [
                    {
                        stationCode: this.result.stationCode,
                        businessDay: this.orderInfo.businessDay,
                        orderNo: this.result.orderNo,
                    },
                ],
            };
            let res = await beforeMakeInvoiceCheckApi(apiParams);
            if (res && res.success) {
                if (res.data.flag) {
                    let goods = this.result.productName;
                    let params = {
                        orderNoList: [this.result.orderNo],
                        checkAllAmount: this.result.actualPayTotalAmount,
                        type: 'invoice',
                        createTime: this.result.payItemList[0].payTime,
                        orgName: this.result.stationName,
                        goods: goods,
                        refer: 'r33',
                    };
                    this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params, 'redirectTo');
                }
            }
        },
        // 返回首页
        backMainPage() {
            // this.$sKit.layer.useRouter('/pages/thirdhome/main', '','switchTab');
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.backHomeFun();
            // #endif
        },
        // 获取订单去评价状态
        /**
		 * 订单子类型：
		 11—预约加油；
		 13—线上后支付加油(e站加油订单)；
		 23—O2O订单；
		 26—异业订单(能源商城订单)；
		 */
        async getOrderCommentFlag() {
            let params = {
                orderNo: this.result.orderNo,
                stationCode: this.result.stationCode,
                payAmount: this.result.actualPayTotalAmount,
                orderType: this.result.orderType,
                orderSubType: this.result.orderSubType,
                createTime: this.result.payItemList[0].payTime,
            };
            let res = await orderCommentFlag(params);
            if (res.success) {
                this.orderCommentFlag = res.data.commentFlag == '0' ? '2' : '1';
            }
        },
        // 去评价
        evaluationAction() {
            let url = '/packages/third-evaluate/pages/home/<USER>';
            let type = 'redirectTo';
            let params = {
                evaluateType: 'order',
                ...this.result,
                isOil: '1',
            };
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
};
</script>
<style lang="scss" scoped>
.oil-charge-pay-result {
    height: 100%;
    max-height: 100%;
    background-color: #f7f7fb;
    position: relative;
    padding-bottom: 150rpx;

    .content {
        margin: 0 16px;
        flex: 1;
        min-height: 0;
        overflow-y: scroll;
        overflow-x: hidden;
        // overflow: auto;

        .station-title {
            margin-top: 26rpx;
            font-weight: bold;
            font-size: 32rpx;
            color: #000000;
            line-height: 44rpx;
            display: flex;
            img {
                width: 24px;
                height: 24px;
                margin-right: 12rpx;
            }
        }

        .wxCode-box {
            margin-top: 24rpx;
        }
    }

    .mar2412 {
        margin: 24px 0 12px 0;
    }

    .o2o-card {
        .card-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .card-title-left {
                font-weight: bold;
                font-size: 32rpx;
                color: #666666;
                line-height: 45rpx;
            }
            .card-title-right {
                font-size: 26rpx;
                color: #666666;
                line-height: 37rpx;
                display: flex;
                align-items: center;
                padding-right: 8rpx;
                .arrow {
                    line-height: 37rpx;
                    width: 15rpx;
                    height: 15rpx;
                    border-top: 4rpx solid #666;
                    border-right: 4rpx solid #666;
                    transform: rotate(45deg);
                }
            }
        }
    }

    .padL110 {
        padding: 15px 15px 15px 110px;
    }

    .padL15 {
        padding: 15px 15px 15px 15px;
    }

    .bottom-area {
        display: flex;
        flex-direction: row;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 150rpx;
        bottom: 0;
        left: 0;
        right: 0;
        background: #ffffff;
        box-shadow: 0rpx -2rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
        z-index: 9;

        .shopping-cart {
            position: absolute;
            width: 100rpx;
            height: 118rpx;
            left: 0px;
            bottom: 53rpx;

            img {
                width: 100rpx;
                height: 118rpx;
            }

            .shopping-cart-dot {
                width: 42rpx;
                height: 42rpx;
                background: linear-gradient(315deg, #ff634f 0%, #ff000b 100%);
                text-align: center;
                line-height: 42rpx;
                font-size: 24rpx;
                color: #ffffff;
                border: 2rpx solid #ffffff;
                border-radius: 50% 50%;
                position: absolute;
                right: -20rpx;
                top: -20rpx;
            }
        }

        .btn {
            flex: 1;
            height: 44px;
            line-height: 44px;
            font-weight: bold;
            text-align: center;
            border-radius: 8px;
        }
    }
    .btn_div {
        margin-top: 20px;
        font-size: 15px;
        display: flex;
        flex-direction: row;
        position: absolute;
        bottom: 10px;
        width: 100%;
        .finish_verification {
            width: 48%;
            text-align: center;
            border-radius: 8px;
            height: 44px;
            line-height: 44px;
        }

        .finish_verification2 {
            width: 100%;
            text-align: center;
            border-radius: 8px;
            height: 44px;
            line-height: 44px;
        }
        .btnColor {
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        }
    }
}
</style>
