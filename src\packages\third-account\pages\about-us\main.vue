<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="关于我们" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="fl-column fl-al-jus-cen mart16 marb6">
                        <!-- 这是二维码 -->
                        <img src="../../image/head.png" alt="" class="icon-110" />
                        <!-- #ifdef MP-MPAAS  -->
                        <div class="color-333 font-16 weight-500 padd12">能源e站 V{{ appVersion }}</div>
                        <!-- #endif -->
                        <!-- <div class="color-999 font-15 weight-500 padd12">扫描二维码，您的朋友也可下载能源e站</div> -->
                    </div>
                    <!-- <div class="mar16 font-14 color-999 weight-500">删除注销您的账号首先需要满足如下条件：</div> -->
                    <div class="card-default marlr16">
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="makePhoneCall()">
                            <div class="f-1 font-14 weight-400 color-333">联系电话</div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-999">{{ phoneNumber }}</div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="openUrl()">
                            <div class="f-1 font-14 weight-400 color-333">网址</div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-E64F22">{{ webUrl }}</div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="clickXieyi(17)">
                            <div class="f-1 font-14 weight-400 color-333">用户协议</div>
                            <div class="fl-row fl-al-cen">
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 marlr12" @click="clickXieyi(63)">
                            <div class="f-1 font-14 weight-400 color-333">隐私政策</div>
                            <div class="fl-row fl-al-cen">
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
            <!-- #ifdef MP-MPAAS -->
            <div class="marb6 color-999 font-14 te-center" @click="openWebUrl()">ICP备案号:京ICP备14026641号-24A ></div>
            <!-- #endif -->
            <div class="marb30 te-center color-999 font-12">
                <div> 中国石油天然气股份有限公司版权所有</div>
                <!-- #ifdef MP-MPAAS -->
                <div>Copyright @ 2016-2024 PetroChina.All Rights ReserVed</div>
                <!-- #endif -->
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userAgreement } from '../../../../s-kit/js/v3-http/https3/user';
// #ifdef MP-MPAAS
import appAboutUs from './diff-environment/app-about-us';
// #endif
// #ifdef MP-TOUTIAO
import ttAboutUs from './diff-environment/tt-about-us.js';
// #endif
// #ifdef H5-CLOUD
import cloudAboutUs from './diff-environment/cloud-about-us.js';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD || MP-TOUTIAO 
import wxAboutUs from './diff-environment/wx-about-us';
import zfbAboutUs from './diff-environment/zfb-about-us';
// #endif
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    data() {
        return {
            appVersion: '',
            webUrl: 'https://www.kunlunjyk.com',
            phoneNumber: '956100',
        };
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appAboutUs,
        // #endif
        // #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
        wxAboutUs,
        zfbAboutUs,
        // #endif
        // #ifdef H5-CLOUD
        cloudAboutUs,
        // #endif
        // #ifdef MP-TOUTIAO
        ttAboutUs,
        // #endif
    ],
    onLoad(option) { },
    async onShow() {
        // #ifdef MP-MPAAS
        /**
         * @description : 获取app公众参数
         * @return        {*}
         */
        let data = await this.$cnpcBridge.getCommonArgs();
        this.appVersion = data.appVersion;
        // #endif
    },
    methods: {
        /**
         * @description : 跳转工信部
         * @return        {*}
         */
        // #ifdef MP-MPAAS
        openWebUrl() {
            let url = 'https://beian.miit.gov.cn';
            // this.$cnpcBridge.mobileBrowsers(url);
            this.$cnpcBridge.openModule({
                type: 'oldWeb',
                url: url,
                hasNativeTop: '1',
            });
        },

        // #endif
        /**
         * @description : 打开网址
         * @return        {*}
         */
        openUrl() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.openModule({
                type: 'web',
                url: this.webUrl,
            });
            // #endif
            // #ifdef MP-ALIPAY
            uni.navigateTo({
                url: '/mpaas/packages/web-view/pages/home/<USER>' + encodeURIComponent(this.webUrl),
            });
            // #endif
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            uni.navigateTo({
                url: '/packages/web-view/pages/home/<USER>' + encodeURIComponent(this.webUrl),
            });
            // #endif
            // #ifdef H5-CLOUD
            let url = this.webUrl
            upsdk.pluginReady(function () {
                upsdk.createWebView({
                    url: url,
                    isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                });
            });

            // #endif
        },
        /**
         * @description : 拨打电话
         * @return        {*}
         */
        makePhoneCall() {
            uni.makePhoneCall({
                phoneNumber: this.phoneNumber,
            });
        },
        /**
         * @description : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            setTimeout(() => {
                this.$refs.pullDownRefreshRef.stopRefresh();
                console.log('加载完成');
            }, 1000);
            console.log('下拉刷新了');
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.icon-110 {
    width: 110px;
    height: 110px;
}

.icon-40 {
    width: 40px;
    height: 40px;
}

.mart16 {
    margin-top: 16px;
}

.mar16 {
    margin: 16px 16px 0;
}

.marlr16 {
    margin: 0 16px;
}

.marl10 {
    margin-left: 10px;
}

.marlr12 {
    margin: 0 12px;
}

.padd12 {
    padding: 12px 0 12px;
}

.marb6 {
    margin-bottom: 6px;
}

.marb30 {
    margin-bottom: 30px;
}
</style>
