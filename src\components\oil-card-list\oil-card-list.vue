<template>
    <div class="oil-list">
        <div class="oil-item" v-for="(item, idx) in datalist" @click="clickItem(idx)" :key="item">
            <!-- <div class='bg-view'>
				<img class="bg-img" src="@/static/pay-oil-bg.png" mode="">
			</div>
			<div class='oil-view'>
				<div class='header-view'>
					<img class="header-image" src="@/static/cnpc-logo.png" mode="">
				</div>
				<div class='text-view'>
					<div class='card-name'>昆仑加油卡</div>
					<div class='card-code'>{{cardNo(item.cardNo)}}</div>
				</div>
      </div>-->

            <div class="choice-item">
                <div class="left">
                    <img class="header-image" src="@/static/cnpc-logo.png" mode />
                    <div class="info">
                        <div class="info-num">{{ item.cardType == 0 ? '实体加油卡' : '电子加油卡' }}({{ item.cardNo | showLastFour }})</div>
                        <div class="price">剩余：￥{{ item.balance }}</div>
                    </div>
                </div>
                <div class="right">
                    <img src="@/static/images/type-checked.png" v-if="idx == index" alt />
                    <img src="@/static/images/type-unchecked.png" v-else alt />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        datalist: {
            default: [],
            type: Array,
        },
        index: {
            default: -1,
            type: Number,
        },
    },
    computed: {},
    filters: {
        showLastFour(val) {
            if (val) {
                return val.slice(-4);
            }
        },
    },
    methods: {
        clickItem(index) {
            this.$emit('click', index);
        },
        cardNo(code) {
            let codeArr = code.split('');
            let str = '';
            for (let i = 0; i < codeArr.length; i++) {
                if (i % 4 == 0) {
                    if (i != 0) {
                        str += ' ';
                    }
                }
                if (i > 3 && i < 8) {
                    str += '*';
                } else {
                    str += codeArr[i];
                }
            }
            return str;
        },
    },
};
</script>

<style scoped lang="scss">
.oil-list {
    overflow: hidden;
    padding: 24rpx;
    /* .oil-item {
			margin-left: 15px;
			margin-top: 10px;
			position: relative;
			width: 345px;
			.bg-view {
				height: 80px;
				display: flex;
				justify-content: flex-end;
				align-items: flex-end;
				background-color: #01A2E2;
				border-radius: 5px;
				.bg-img {
					height: 80px;
					width: 96px;
				}
			}
			.oil-view {
				position: absolute;
				top: 0;
				left: 0;
				width: 345px;
				height: 80px;
				display: flex;
				align-items: center;
				.header-view {
					background-color: #FFFFFF;
					border-radius: 50%;
					margin-left: 15px;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 44px;
					height: 44px;
					.header-image {
						width: 24px;
						height: 24px;
					}
				}
				.text-view {
					margin-left: 10px;
					.card-name {
						color: #FFFFFF;
						line-height: 24px;
						font-size: 15px;
					}
					.card-code {
						color: #FFFFFF;
						line-height: 24px;
						font-size: 15px;
					}
				}
			}
		} */

    .oil-item {
        margin-bottom: 24rpx;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .choice-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 24rpx;
        border-radius: 16rpx;
        background: #fef0ed;
        .left {
            flex: 1;
            display: flex;
            align-items: center;
            img {
                width: 80rpx;
                height: 80rpx;
                margin-right: 10rpx;
            }
            .info-num {
                font-size: 30rpx;
                margin-bottom: 4rpx;
            }
            .price {
                font-size: 26rpx;
                color: #999;
            }
        }
        .right img {
            width: 40rpx;
            height: 40rpx;
        }
    }
}
</style>
