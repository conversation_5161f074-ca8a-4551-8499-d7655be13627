import { POST, POST2 } from '../../index';
import { api, apiGsms } from '../../../../../../project.config';

// 发票抬头列表
export const invoiceTitleListApi = (params, config) => {
    return POST('invoice.title.list', params, config);
};
// 发票抬头修改
export const invoiceTitleUpApi = (params, config) => {
    return POST('invoice.title.updateOrCreate', params, config);
};
// 发票抬头详情
export const invoiceTitleDetailApi = (params, config) => {
    return POST('invoice.title.detail', params, config);
};
// 发票抬头删除
export const invoiceTitleDeleteApi = (params, config) => {
    return POST('invoice.title.delete', params, config);
};
// 发票红冲
export const invoiceRedFlushApi = (params, config) => {
    return POST('invoice.redFlush', params, config);
};
// 开票
export const invoiceMakeApi = (params, config) => {
    return POST('invoice.make', params, config);
};

// 充电订单开票
export const chargeInvoiceMakeApi = (params, config) => {
    return POST('charge.invoice.make', params, config);
};

// 发票列表
export const invoiceListApi = (params, config) => {
    return POST('invoice.list', params, config);
};
// 发票下载
export const invoiceDownloadApi = (params, config) => {
    return POST('invoice.download', params, config);
};

// 2.0充值订单开票
export const makeRechargeInvoiceAsync = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/app_third/makeRechargeInvoiceAsync', params, config);
    // #endif
    // #ifdef MP-ALIPAY
    return POST2(api + apiGsms + '/v1/makeRechargeInvoiceAsync', params, config);
    // #endif
    // #ifdef MP-WEIXIN
    return POST2('/app/json/third/rechargeInvoice', params, config);
    // #endif
};

// 2.0充值订单发票换开
export const reverseInvoiceAsync = (params, config) => {
    return POST2('app/json/card/reverseInvoiceAsync', params, config);
};

// 判断订单是否可开票接口 /invoice/beforeMakeInvoice/check
export const beforeMakeInvoiceCheckApi = (params, config) => {
    return POST('invoice.beforeMakeInvoice.check', params, config);
};

// 判断订单是否可开票接口 /charge/invoice/beforeMakeInvoice/check
export const chargeBeforeMakeInvoiceCheckApi = (params, config) => {
    return POST('charge.invoice.beforeMakeInvoice.check', params, config);
};

// 发票抬头模糊匹配 /invoice/title/rcmdList
export const invoiceRcmdListkApi = (params, config) => {
    return POST('invoice.title.rcmdList', params, config);
};

// 获取发票抬头模糊匹配详情 /invoice/title/rcmdCard
export const invoiceRcmdCardkApi = (params, config) => {
    return POST('invoice.title.rcmdCard', params, config);
};

// 非登录态扫码开票接口 /invoice/nonLogin/createInvoice
export const createInvoiceApi = (params, config) => {
    return POST('invoice.nonLogin.createInvoice', params, config);
};

// 获取微信一次授权永久插卡组件AuthKey /user/getWeixinInvoiceAuthUrl
export const getWeixinInvoiceAuthUrl = (params, config) => {
    return POST('user.getWeixinInvoiceAuthUrl', params, config);
};

// 获取微信一次授权永久插卡组件授权状态 /user/getWeixinInvoiceAuthInfo
export const getWeixinInvoiceAuthInfo = (params, config) => {
    return POST('user.getWeixinInvoiceAuthInfo', params, config);
};

// 发票抬头解析 /invoice/title/regexInvoiceTitle
export const regexInvoiceTitle = (params, config) => {
    return POST('invoice.title.regexInvoiceTitle', params, config);
};
