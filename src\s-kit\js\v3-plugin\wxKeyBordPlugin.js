import Config from '../../js/third-config.js';
import store from '../../../store/index';
// #ifdef MP-WEIXIN
let passwordKeyboardRef,
    Keyboard_SUCCESS = 0;

function initRef(result, type = 'number') {
    uni.showLoading({
        title: '加载中',
        mask: true,
    });
    passwordKeyboardRef = result;
    // const gsmsToken = store.state.gsmsToken || '';
    // const openid = store.state.openId || '';
    const { gsmsToken, openId, openid } = uni.getStorageSync('tokenInfo') || {};
    console.log(gsmsToken, '========initRef===》gsmsToken');
    console.log(openId, '========initRef===》openId');
    console.log(gsmsToken, openId || openid, type, Config.baseType, '========initRef===');
    return new Promise(async (resolve, reject) => {
        try {
            let initResult = await result.init(gsmsToken, openId || openid, type, Config.baseType); //V3.0纯数字键盘
           
            if (Number(initResult.code) !== Keyboard_SUCCESS) {
                uni.hideLoading();
                uni.showToast({
                    title: initResult.msg,
                    icon: 'none',
                    duration: 2000,
                });
                return reject(initResult);
            } else {
                uni.hideLoading();
                resolve(passwordKeyboardRef);
            }
        } catch (err) {
            uni.hideLoading();
            console.log('wx keybord初始化失败', err);
            reject(err);
        }
    });
}

export default {
    initRef,
};
// #endif
