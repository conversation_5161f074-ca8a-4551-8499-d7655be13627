import Vue from 'vue';
export default {
    // 挂在store到全局Vue原型上
    state: {
        type: '', //模式 http接口报错弹窗  custom自定义插槽弹窗
        // 是否弹出
        show: false,
        //标题
        title: '',
        // 内容
        content: '',
        // 第二行内容
        content2: '',
        // 隐藏取消按钮
        showCancel: true,
        // 取消文本
        cancelText: '',
        // 取消字体颜色
        cancelColor: '#ED4F4E',
        // 取消按钮背景颜色
        cancelBackgroundColor: '',
        // 确定按钮文本
        confirmText: '确定',
        // 确定字体颜色
        confirmColor: '#333333',
        // 确认按钮背景颜色
        confirmBackgroundColor: '',
        // 点击确认的成功函数
        success: null,
        pageUrl: '',
        waitModalList: [],
    },
    mutations: {
        /**
         * @description  : 隐藏弹窗
         * @return        {*}
         */
        hideModal(state) {
            // 小程序导航条页面控制
            // #ifndef H5
            if (state.hideTabBar) {
                wx.showTabBar();
            }
            // #endif
            state.show = false;
        },
        /**
         * @description  : 显示弹窗
         * @return        {*}
         */
        showModal(state, data) {
            state = Object.assign(state, data);
            state.show = true;
            console.log(JSON.stringify(state), '自定义弹窗开关');
        },
        /**
         * @description  : 初始化所有数据
         * @return        {*}
         */
        resetState(state) {
            let defaultValue = {
                type: '', //模式 http接口报错弹窗  custom自定义插槽弹窗
                show: false,
                title: '',
                content: '',
                content2: '',
                showCancel: true,
                cancelText: '',
                cancelColor: '#ED4F4E',
                cancelBackgroundColor: '',
                confirmText: '确定',
                confirmColor: '#333333',
                confirmBackgroundColor: '',
                success: null,
                pageUrl: '',
            };
            // 遍历默认值对象的所有属性
            Object.keys(defaultValue).forEach(key => {
                // 如果 state 对象中存在该属性
                if (state.hasOwnProperty(key)) {
                    // 将 state 对象中该属性的值设置为默认值对象中该属性的值
                    state[key] = defaultValue[key];
                }
            });
        },
        waitShowModal(state, data) {
            if (
                data.title == state.waitModalList[state.waitModalList.length - 1]?.title &&
                data.content == state.waitModalList[state.waitModalList.length - 1]?.content
            )
                return;
            state.show = false;
            state.waitModalList.push(data);
            state.show = true;
            console.log(state.waitModalList, '弹窗列表');
        },
    },
    actions: {
        /**
         * @description  : 触发展示弹窗，将需要的值赋值给vuex
         * @return        {*}
         */
        zjShowModal({ state, commit, dispatch }, option) {
            if (typeof option === 'object') {
                // #ifndef H5
                if (option.hideTabBar) {
                    wx.hideTabBar();
                }
                // #endif
                const pages = getCurrentPages();
                option.pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0];
                if (state.show) {
                    if (state.pageUrl != option.pageUrl) {
                        state.waitModalList = [];
                        commit('resetState');
                        commit('showModal', option);
                    } else {
                        commit('waitShowModal', option);
                    }
                } else {
                    commit('resetState');
                    commit('showModal', option);
                }
            } else {
                throw '配置项必须为对象传入的值为：' + typeof option;
            }
        },
        /**
         * @description  : 关闭弹窗
         * @return        {*}
         */
        zjHideModal({ state, commit, dispatch }) {
            commit('hideModal');
            if (state.waitModalList.length > 0) {
                dispatch('zjShowModal', state.waitModalList[0]);
                state.waitModalList.shift();
                console.log(state, '连续弹窗');
            }
        },
        /**
         * @description  : 确认事件
         * @return        {*}
         */
        zjSuccessModal({ state, commit, dispatch }, res) {
            let cb = state.success;
            let resObj = {
                cancel: false,
                confirm: false,
            };
            res == 'confirm' ? (resObj.confirm = true) : (resObj.cancel = true);
            cb && cb(resObj);
            dispatch('zjHideModal');
        },
    },
};
