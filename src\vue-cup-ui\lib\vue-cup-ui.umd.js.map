{"version": 3, "sources": ["webpack://vue-cup-ui/webpack/universalModuleDefinition", "webpack://vue-cup-ui/webpack/bootstrap", "webpack://vue-cup-ui/./packages/button/src/button.vue?50b1", "webpack://vue-cup-ui/./src/common/common.less", "webpack://vue-cup-ui/./node_modules/@soda/get-current-script/index.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?e5a4", "webpack://vue-cup-ui/./node_modules/@vue/cli-service/lib/commands/build/setPublicPath.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?a681", "webpack://vue-cup-ui/./packages/common/hybrid.js", "webpack://vue-cup-ui/packages/button/src/button.vue", "webpack://vue-cup-ui/./packages/button/src/button.vue?e932", "webpack://vue-cup-ui/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?ab32", "webpack://vue-cup-ui/./packages/button/index.js", "webpack://vue-cup-ui/./src/index.js", "webpack://vue-cup-ui/./node_modules/@vue/cli-service/lib/commands/build/entry-lib.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA,uC;;;;;;;ACAA,uC;;;;;;;ACAA;AACA;AACA;;AAEA;;AAEA;AACA,MAAM,IAA0C;AAChD,IAAI,iCAAO,EAAE,oCAAE,OAAO;AAAA;AAAA;AAAA,oGAAC;AACvB,GAAG,MAAM,EAIN;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;;AAE1D;AACA;AACA,+DAA+D,qBAAqB;AACpF;AACA;;AAEA,qBAAqB,oBAAoB;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,CAAC;;;;;;;;;AC9ED;AAAA;AAAA;AAAue,CAAgB,mgBAAG,EAAC,C;;;;;;;;;;;;ACA3f;;AAEA;AACA;AACA,MAAM,IAAuC;AAC7C,2BAA2B,mBAAO,CAAC,MAA0B;AAC7D;;AAEA;AACA;AACA,wDAAwD,wBAAwB;AAChF;AACA;;AAEA;AACA;AACA,IAAI,qBAAuB;AAC3B;AACA;;AAEA;AACe,sDAAI;;;;;;ACrBnB,0BAA0B,aAAa,0BAA0B,wBAAwB,oBAAoB,4BAA4B,yBAAyB;AAClK;;;;;;ACDA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEO;AACA;;AAEA;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;AACA;;;;;;;;;;;AC7BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC/FqL,CAAgB,qGAAG,EAAC,C;;;;;ACAzM;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;ACjGiG;AACvC;AACL;AACqC;;;AAG1F;AAC6F;AAC7F,gBAAgB,kBAAU;AAC1B,EAAE,kCAAM;AACR,EAAE,MAAM;AACR,EAAE,eAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,gE;;ACnBf;AACA;AACA;AACoC;;AAEpC,UAAQ;AACR,gBAAgB,UAAQ,OAAO,UAAQ;AACvC;;AAEe,8DAAQ,EAAC;;;ACTxB;AACA;AACA;AACA;AAC8B;AACqB;;AAEnD;AACA,EAAE,eAAQ;AACV;;AAEA,wCAAwC;AACxC;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA,EAAE,yBAAQ;AACV,CAAC;;;AC1BuB;AACA;AACT,oFAAG;AACI", "file": "vue-cup-ui.umd.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"vue-cup-ui\"] = factory();\n\telse\n\t\troot[\"vue-cup-ui\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"fb15\");\n", "// extracted by mini-css-extract-plugin", "// extracted by mini-css-extract-plugin", "// addapted from the document.currentScript polyfill by <PERSON>\n// MIT license\n// source: https://github.com/amiller-gh/currentScript-polyfill\n\n// added support for Firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1620505\n\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    define([], factory);\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = factory();\n  } else {\n    root.getCurrentScript = factory();\n  }\n}(typeof self !== 'undefined' ? self : this, function () {\n  function getCurrentScript () {\n    var descriptor = Object.getOwnPropertyDescriptor(document, 'currentScript')\n    // for chrome\n    if (!descriptor && 'currentScript' in document && document.currentScript) {\n      return document.currentScript\n    }\n\n    // for other browsers with native support for currentScript\n    if (descriptor && descriptor.get !== getCurrentScript && document.currentScript) {\n      return document.currentScript\n    }\n  \n    // IE 8-10 support script readyState\n    // IE 11+ & Firefox support stack trace\n    try {\n      throw new Error();\n    }\n    catch (err) {\n      // Find the second match for the \"at\" string to get file src url from stack.\n      var ieStackRegExp = /.*at [^(]*\\((.*):(.+):(.+)\\)$/ig,\n        ffStackRegExp = /@([^@]*):(\\d+):(\\d+)\\s*$/ig,\n        stackDetails = ieStackRegExp.exec(err.stack) || ffStackRegExp.exec(err.stack),\n        scriptLocation = (stackDetails && stackDetails[1]) || false,\n        line = (stackDetails && stackDetails[2]) || false,\n        currentLocation = document.location.href.replace(document.location.hash, ''),\n        pageSource,\n        inlineScriptSourceRegExp,\n        inlineScriptSource,\n        scripts = document.getElementsByTagName('script'); // Live NodeList collection\n  \n      if (scriptLocation === currentLocation) {\n        pageSource = document.documentElement.outerHTML;\n        inlineScriptSourceRegExp = new RegExp('(?:[^\\\\n]+?\\\\n){0,' + (line - 2) + '}[^<]*<script>([\\\\d\\\\D]*?)<\\\\/script>[\\\\d\\\\D]*', 'i');\n        inlineScriptSource = pageSource.replace(inlineScriptSourceRegExp, '$1').trim();\n      }\n  \n      for (var i = 0; i < scripts.length; i++) {\n        // If ready state is interactive, return the script tag\n        if (scripts[i].readyState === 'interactive') {\n          return scripts[i];\n        }\n  \n        // If src matches, return the script tag\n        if (scripts[i].src === scriptLocation) {\n          return scripts[i];\n        }\n  \n        // If inline source matches, return the script tag\n        if (\n          scriptLocation === currentLocation &&\n          scripts[i].innerHTML &&\n          scripts[i].innerHTML.trim() === inlineScriptSource\n        ) {\n          return scripts[i];\n        }\n      }\n  \n      // If no match, return null\n      return null;\n    }\n  };\n\n  return getCurrentScript\n}));\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"", "// This file is imported into lib/wc client bundles.\n\nif (typeof window !== 'undefined') {\n  var currentScript = window.document.currentScript\n  if (process.env.NEED_CURRENTSCRIPT_POLYFILL) {\n    var getCurrentScript = require('@soda/get-current-script')\n    currentScript = getCurrentScript()\n\n    // for backward compatibility, because previously we directly included the polyfill\n    if (!('currentScript' in document)) {\n      Object.defineProperty(document, 'currentScript', { get: getCurrentScript })\n    }\n  }\n\n  var src = currentScript && currentScript.src.match(/(.+\\/)[^/]+\\.js(\\?.*)?$/)\n  if (src) {\n    __webpack_public_path__ = src[1] // eslint-disable-line\n  }\n}\n\n// Indicate to webpack that this file can be concatenated\nexport default null\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('button',{staticClass:\"up-button\",on:{\"click\":_vm.handleClick}},[_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/**\n * Created on 2020/9/3.\n */\n\nconst _agent = navigator.userAgent.toLowerCase();\n\nfunction isFunction(value) {\n    return Object.prototype.toString.call(value) === '[object Function]'\n}\n\nfunction truncatedRespForJSBridge(data) {\n  if (!data) {\n    return data;\n  } else if (data.resultString) {\n    return data.resultString;\n  } else if (data.resultParams) {\n    return data.resultParams;\n  }\n}\n\nexport const isJSBridgeEnv = /\\(securitywebcache\\s([\\d\\.]+)\\)/g.test(_agent);\nexport const isCordovaEnv = /\\(cordova\\s([\\d\\.]+)\\)/g.test(_agent);\n\nexport function callNative(success, fail, plugin, action, paramArray) {\n    let iCanCall = false;\n    if (isCordovaEnv && window.cordova) {\n        iCanCall = true;\n        window.cordova.exec(success, fail, plugin, action, paramArray);\n    } else if (isJSBridgeEnv && window.WebViewJavascriptBridge) {\n        iCanCall = true;\n        window.WebViewJavascriptBridge.callHandler(plugin, action, paramArray, function (data) {\n            isFunction(success) && success(truncatedRespForJSBridge(data))\n        }, function (err) {\n            isFunction(fail) && fail(truncatedRespForJSBridge(err))\n        })\n    }\n    return iCanCall;\n}\n", "<template>\n  <button class=\"up-button\"\n          @click=\"handleClick\">\n    <slot></slot>\n  </button>\n</template>\n\n<script>\n  import {callNative} from \"../../common/hybrid\";\n\n  export default {\n    name: \"UPButton\",\n    props: {\n      scope: String,\n      cardRule:String,\n      timeout: Number\n    },\n    methods: {\n      appletExplicitAuth(success, fail) {\n        let iCanCall = callNative(function (data) {\n          if (typeof success === 'function') {\n            success(data);\n          }\n        }, function (err) {\n          if (typeof fail === 'function') {\n            let cordovaError;\n            if (window.cordova) {\n              switch (window.cordova.errorRetStatus) {\n                case window.cordova.callbackStatus.INVALID_ACTION:\n                  cordovaError = {\n                    errcode: 'c03',\n                    errmsg: 'INVALID_ACTION_EXCEPTION: 插件里面没有此方法！'\n                  }\n                  break;\n\n                case window.cordova.callbackStatus.CLASS_NOT_FOUND_EXCEPTION:\n                  cordovaError = {\n                    errcode: 'c04',\n                    errmsg: 'CLASS_NOT_FOUND_EXCEPTION: 此插件没有实现！'\n                  }\n                  break;\n\n                case window.cordova.callbackStatus.ILLEGAL_ACCESS_EXCEPTION:\n                  cordovaError = {\n                    errcode: 'c02',\n                    errmsg: 'ILLEGAL_ACCESS_EXCEPTION: 无权限访问此插件！'\n                  }\n                  break;\n                default:\n                  break;\n              }\n            }\n            if (cordovaError) {\n              fail(cordovaError)\n            } else {\n              fail(err);\n            }\n          }\n        }, 'UPWebSdk', 'appletExplicitAuth', [{scope: this.scope,cardRule: this.cardRule || ''}]);\n        if (!iCanCall) {\n          setTimeout(() => {\n            this._count++;\n            if (this._count > (this._timeout / 20)) {\n              console.warn('请确定是否运行在云闪付APP中,且成功加载了upsdk.js');\n              fail({\n                errcode: '__ENV__10001',\n                errmsg: '检测到未在云闪付APP中运行或未成功加载upsdk.js'\n              })\n            } else {\n              this.appletExplicitAuth(success, fail)\n            }\n          }, 20)\n        }\n      },\n      handleClick(event) {\n        if (this.btnDisable) return;\n        if (this.timeout && isNaN(parseInt(this.timeout))) {\n          this.$emit('click', event, {\n            errcode: '__ENV__10002',\n            errmsg: '检测到timeout值非法'\n          });\n          return;\n        }\n        this._count = 0;\n        this._timeout = this.timeout || 2000; // 默认2s超时\n        this.btnDisable = true; // 防止多次点击，直到回调中才释放\n        this.appletExplicitAuth(data => {\n          this.btnDisable = false;\n          this.$emit('click', event, null, data);\n        }, err => {\n          this.btnDisable = false;\n          this.$emit('click', event, err);\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .up-button {\n    background: #5A98D2;\n    border-radius: 9px;\n    width: 62px;\n    height: 24px;\n    border: none;\n    font-size: 12px;\n    color: #FFFFFF;\n    letter-spacing: 1px;\n    text-align: center;\n    line-height: 24px;\n    font-weight: bold;\n    font-family: PingFangSC-Semibold;\n  }\n\n  .up-button:focus {\n    outline: none;\n  }\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=script&lang=js&\"", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n        injectStyles.call(\n          this,\n          (options.functional ? this.parent : this).$root.$options.shadowRoot\n        )\n      }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "import { render, staticRenderFns } from \"./button.vue?vue&type=template&id=5cf67f49&scoped=true&\"\nimport script from \"./button.vue?vue&type=script&lang=js&\"\nexport * from \"./button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5cf67f49\",\n  null\n  \n)\n\nexport default component.exports", "/**\n * Created on 2020/6/22.\n */\nimport UPButton from './src/button';\n\nUPButton.install = function(Vue) {\n  Vue.component(UPButton.name, UPButton);\n};\n\nexport default UPButton;\n", "/**\n * Created on 2020/6/22.\n */\n// import './common/resize.js';\nimport './common/common.less';\nimport UPButton from '../packages/button/index.js';\n\nconst components = [\n  UPButton\n]\n\nconst install = function (Vue, opts = {}) {\n  components.forEach(component => {\n    if (install.installed) return\n    Vue.component(component.name, component)\n  })\n}\n\nif (typeof window !== 'undefined' && window.Vue) {\n  install(window.Vue);\n}\n\nexport default {\n  version: '0.1.0',\n  install,\n  UPButton\n}\n", "import './setPublicPath'\nimport mod from '~entry'\nexport default mod\nexport * from '~entry'\n"], "sourceRoot": ""}