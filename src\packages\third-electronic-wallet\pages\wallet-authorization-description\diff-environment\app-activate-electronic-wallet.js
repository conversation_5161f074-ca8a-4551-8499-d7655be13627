import { initRealPersonIdentify, realNameAuth, realPersonIdentify } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { openAccount } from '../../../../../s-kit/js/v3-http/https3/wallet';
import projectConfig from '../../../../../../project.config';
export default {
    methods: {
        /**
         * @description  :  同意开通开通昆仑e享卡
         * @param         {String} realName -用户在上个页面输入的姓名
         * @param         {String} idNo -用户在上个页面输入的身份证号
         * @param         {String} idType -身份证件类型 1:身份证 2:军人身份证件 3:台胞证 4:港澳通行证 5:外国人永久居留身份证 6:护照
         * @param         {String} type - 7-绑卡 1-电子账户注册 2-忘记密码 11-风控校验
         * @param         {String} authInfo - Type=7必传，传入验证码接口传过来的字串
         * @return        {*}
         */
        async agreeActivate() {
            if (!this.isHarmony) {
                let params = {
                    realName: this.personInfo.name,
                    idType: '1',
                    idNo: this.personInfo.idNo,
                    authInfo: '',
                    type: '1',
                    // areaCode: this.locationInfo.cityCode || this.personInfo.areaCode
                    // areaCode: this.personInfo.areaCode
                };
                let res = await realNameAuth(params);
                if (res && res.success) {
                    this.authInfo = res.data.authInfo;
                    this.initFaceAuthentication();
                }
            } else {
                this.$sKit.harmonyRealPersonAuthentication.startVerification(this.personInfo).then(response => {
                    console.log('链路通过，调用卡通电子账户接口', response);
                    if (response) {
                        this.openEwallet(response.data.authInfo);
                    } else {
                    }
                });
            }
        },
        /**
         * @description  :  人脸验证初始化
         * @param         {String} returnUrl -业务回跳地址；PC或H5接入时 必传，APP接入时为空
         * @param         {String} metaInfo -MetaInfo环境参数，需要通过JS获取或SDK获取
         * @param         {String} verifyMode -认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {Function} aliMetaInfo -阿里人脸采集MetaInfo
         * @param         {string} verifyUnique -身份认证唯一标识
         * @param         {string} certifyId -实人认证三方系统的标识
         * @param         {string} certifyUrl -第三方认证地址
         * @param         {Function} zjShowModal -全局自定义弹窗
         * @return        {*}
         */
        async initFaceAuthentication() {
            let params = {
                returnUrl: '',
                metaInfo: await this.$cnpcBridge.aliMetaInfo(),
                verifyMode: '1',
            };
            if (this.walletAddChannel == 'ccb') {
                params.channel = '919';
            }
            let res = await initRealPersonIdentify(params);

            if (res && res.success) {
                this.verifyUnique = res.data.verifyUnique;
                this.$cnpcBridge.aliFaceCollec(res.data.certifyId, async result => {
                    //
                    if (result.status) {
                        //采集成功
                        this.realNameAuthentication(res.data);
                    } else {
                        this.$store.dispatch('zjShowModal', {
                            title: result.msg,
                            confirmText: '确认',
                            success: res => {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                }
                            },
                        });
                    }
                });
            }
        },
        /**
         * @description  :  实人认证
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
         * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
         * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
         * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {string} idNo: 用户身份证号
         * @return        {*}
         */
        async realNameAuthentication(data) {
            let params = {
                type: '1',
                authInfo: this.authInfo,
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                verifyMode: '1',
                idNo: this.personInfo.idNo,
            };
            if (this.walletAddChannel == 'ccb') {
                params.sceneId = data.sceneId;
                params.channel = '919';
            }
            let res = await realPersonIdentify(params);
            if (res.success) {
                // this.queryECardByIdNo(res.data.encryptIdNo);
                this.openEwallet();
            }
        },
        /**
         * @description  :  实人认证
         * @param         {Function} updateExtendFiled: 设置风控字段
         * @param         {Function} openEwallet: 开通电子钱包
         * @param         {string} idNo: 用户身份证号
         * @param         {String} areaCode:  开户地编码
         * @param         {Function} eWalletNormal: 功能限制公共方法
         * @param         {string} activate: 区分功能限制电子卡迁移成功后返回的到哪个页面
         * @param         {string} orgCode: 油站编码
         * @param         {string} inviteCode: 员工邀请码
         *
         * @return        {*}
         */
        async openEwallet(authInfo) {
            // console.log('authInfo--',authInfo)
            console.log(this.personInfo.inviteCode, '员工邀请码');
            uni.showLoading({
                title: '加载中',
            });
            if (!this.isHarmony) {
                let userInfo = await this.$cnpcBridge.getUserTokenInfo();
                try {
                    let that = this;
                    let params = {
                        orgCode: that.personInfo.stationCode,
                        staffENo: that.personInfo.inviteCode,
                        memberNo: userInfo.memberNo,
                        openCardPlace: that.personInfo.areaCode,
                    };
                    // {"extData":{"orgCode":"111111","staffENo":"123","memberNo":"123","openCardPlace":123}}
                    await that.$accountCenter.updateExtendFiled(that.personInfo.idNo);
                    that.$accountCenter.openEwallet(
                        {
                            usedPlace: that.personInfo.areaCode,
                            extParams: JSON.stringify({ extData: JSON.stringify(params) }),
                        },
                        async res => {
                            console.log({ extParams: JSON.stringify({ extData: JSON.stringify(params) }) }, '员工推荐码');
                            console.log(res, 'openEwallet====');
                            uni.hideLoading();
                            if (res.isSuccessed) {
                                this.$sKit.commonUtil.eWalletNormal({
                                    nextFun: () => {
                                        this.$sKit.mpBP.tracker('e享卡开通', {
                                            seed: 'eCardActiveBiz',
                                            pageID: 'sucessPage', // 页面名
                                            refer: this.personInfo.refer || '', // 来源
                                            channelID: projectConfig.clientCode, // C10/C12/C13
                                            address: this.personInfo.address,
                                        });
                                        let url = '';
                                        if (this.walletAddChannel == 'ccb') {
                                            url = '/packages/third-electronic-wallet/pages/wallet-success-ccb/main';
                                        } else {
                                            url = '/packages/third-electronic-wallet/pages/wallet-success/main';
                                        }
                                        let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                        this.$sKit.layer.useRouter(url, {}, type);
                                    },
                                    freezeReasonArr: [9, 10],
                                    cancelCallback: () => {
                                        // 获取用户非脱敏身份信息和获取电子卡迁移数据
                                        this.activate = 'ktqb';
                                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                                    },
                                    walletAddParams: {
                                        refer: 'r14',
                                    },
                                });
                                return;
                            } else {
                                uni.showToast({
                                    title: res.desString,
                                    icon: 'none',
                                    duration: 2000,
                                });
                            }
                        },
                    );
                } catch (error) {
                    uni.hideLoading();
                }
            } else {
                try {
                    uni.showLoading({
                        title: '加载中',
                    });
                    let params = {
                        ...this.personInfo, // 开通钱包流程前面传下来的参数
                        // name: this.beforeParamsForm.name, // 会员姓名
                        // idNo: this.beforeParamsForm.idNo, //  身份证号码（18位证件号）
                        // local: this.beforeParamsForm.local, //  归属地（传国标地市编号，形如：110101）
                        // inviteCode: this.beforeParamsForm.inviteCode, //  邀请码
                        // payPassword: this.beforeParamsForm.payPassword, //  支付密码
                        // confirmPassword: this.beforeParamsForm.confirmPassword, //   确认支付密码
                        // passwordFree: "0", //  免密标志(1—开通免密；0—关闭免密)
                        // freeAmount: this.beforeParamsForm.freeAmount, //  免密金额（元）；passwordFree=1时，该字段为必输项。
                        authInfo: authInfo, //  人脸识别校验码（实人认证接口返回该值）
                    };
                    let res = await openAccount(params);
                    console.log('接口res----开通电子钱包', res.data);
                    uni.hideLoading();
                    if (res.success) {
                        await this.$sKit.commonUtil.eWalletNormal({
                            nextFun: () => {
                                this.$sKit.mpBP.tracker('e享卡开通', {
                                    seed: 'eCardActiveBiz',
                                    pageID: 'sucessPage', // 页面名
                                    refer: this.personInfo.refer || '', // 来源
                                    channelID: projectConfig.clientCode, // C10/C12/C13
                                    address: this.personInfo.address,
                                });
                                let url = '/packages/third-electronic-wallet/pages/wallet-success/main';
                                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                this.$sKit.layer.useRouter(url, {}, type);
                            },
                            freezeReasonArr: [9, 10],
                            cancelCallback: () => {
                                // 获取用户非脱敏身份信息和获取电子卡迁移数据
                                this.activate = 'ktqb';
                                this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                            },
                            walletAddParams: {
                                refer: 'r14',
                            },
                        });
                        return;
                    }
                } catch (error) {
                    uni.hideLoading();
                }
            }
        },
    },
};
