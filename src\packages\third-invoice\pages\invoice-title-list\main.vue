<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 该页面为发票抬头列表内容页面，选择抬头和抬头列表共同用 -->
        <div class="pullDownRefreshView">
            <zj-navbar :border-bottom="false" :title="navbarTitle"></zj-navbar>
            <div class="f-1 mh-0 bg-F7F7FB">
                <zj-pull-down-refresh
                    ref="pullDownRefreshRef"
                    :emptyImage="require('../../image/kt3tt.png')"
                    :showEmpty="false"
                    emptyText="您目前还未添加发票抬头"
                    @refreshPullDown="refreshPullDown"
                >
                    <div class="invoiceHeaderDetails" v-if="!showEmpty">
                        <div class="invoiceList-wrap" v-for="item in invoiceList" :key="item" @click="invoiceDetail(item)">
                            <div class="invoiceList-wrap-left">
                                <div class="showDefault">
                                    <div class="text">{{ item.buyerName }}</div>
                                    <div class="defaultCount" v-if="item.defaultCount">默认抬头</div>
                                </div>
                                <div v-if="item.buyerTaxId" class="orderCode">
                                    {{ item.buyerTaxId || '' }}
                                </div>
                            </div>
                            <div class="invoiceList-wrap-right">
                                <img :src="require('../../image/right.png')" alt />
                            </div>
                        </div>
                        <div :class="{ btn: true, gray: gray }" @click="addHeader"> 添加抬头 </div>
                        <div class="tip-div">发票抬头添加最大数量为5个</div>
                    </div>
                    <div class="invoiceHeaderDetails fl-al-cen" v-else>
                        <img class="noDataImg" src="../../image/fail.png" alt="" />
                        <div class="noDataTips">您未设置发票抬头</div>
                        <div class="btn marginT80" @click="addHeader">立即设置发票抬头</div>
                    </div>
                </zj-pull-down-refresh>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { invoiceTitleListApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'invoiceHeaderDetails',
    data() {
        return {
            invoiceList: [], // 抬头列表
            showEmpty: false,
            navbarTitle: '发票抬头',
            refer: '',
        };
    },
    onLoad(options) {
        if (Object.keys(options).length) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) {
                this.refer = params.refer;
            }
        }
        this.$sKit.mpBP.tracker('电子发票', {
            seed: 'invoiceBiz',
            pageID: 'invoiceHeaderPage',
            refer: this.refer,
            channelID: clientCode,
        });
    },
    onShow() {
        this.invoiceTitleListPost();
    },
    created() {},
    mounted() {},
    computed: {
        /**
         * @description  : 抬头数大于5按钮置灰
         * @return        {*}
         */
        gray() {
            return this.invoiceList.length >= 5;
        },
    },
    methods: {
        /**
         * @description  : 获取数据
         * @return        {*}
         */
        async invoiceTitleListPost() {
            let res = await invoiceTitleListApi({});
            if (res && res.success) {
                this.invoiceList = res.data || [];
                this.$refs.pullDownRefreshRef.stopRefresh();
            }
            if (this.invoiceList.length <= 0) {
                this.showEmpty = true;
            } else {
                this.showEmpty = false;
            }
        },
        /**
         * @description  : 添加发票抬头，跳转添加页面
         * @return        {*}
         */
        addHeader() {
            if (this.invoiceList.length < 5) {
                let url = '/packages/third-invoice/pages/invoice-title-form/main';
                let params = { refer: this.refer, type: 'add' };
                this.$sKit.layer.useRouter(url, params);
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: '发票抬头上限为5个，请删除现有发票抬头重新添加。',
                    confirmText: '确定',
                    confirmColor: '#333',
                    async success(res) {
                        if (res.confirm) {
                        } else if (res.cancel) {
                        }
                    },
                });
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            this.invoiceTitleListPost();
        },
        /**
         * @description  : 抬头详情，跳转发票抬头详情页面
         * @param         {*} item:
         * @return        {*}
         */
        invoiceDetail(item) {
            let params = { headerId: item.headerId, refer: this.refer };
            this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-title-detail/main', params);
        },
    },
};
</script>
<style scoped lang="scss">
.invoiceHeaderDetails {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    background: #f7f7fb;

    .invoiceList-wrap {
        margin-bottom: 10px;
        background: #ffffff;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 30rpx 30rpx 28rpx;

        .invoiceList-wrap-left {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 0;

            .showDefault {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;

                .text {
                    background: #ffffff;
                    border-radius: 8px;
                    height: 20px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #333333;
                    line-height: 20px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 6.5px;
                }

                .defaultCount {
                    width: 49px;
                    flex-shrink: 0;
                    // height: 16px;
                    padding: 2px 0;
                    background: #ffffff;
                    border-radius: 2px;
                    border: 1px solid #e64f22;
                    font-size: 10px;
                    font-weight: 400;
                    color: #e64f22;
                    text-align: center;
                }
            }

            .orderCode {
                width: 100%;
                font-size: 12px;
                font-weight: 400;
                color: #999999;
                margin-top: 9px;
                line-height: 33rpx;
            }
        }

        .invoiceList-wrap-right {
            width: 16px;
            height: 16px;
            margin-right: 19px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .invoiceList-wrap-right-edit {
            width: 24px;
            height: 24px;
            margin-right: 19px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .zjNoData {
        height: 100%;
    }

    .tip-div {
        font-size: 24rpx;
        font-weight: 400;
        color: #666666;
        line-height: 33rpx;
        text-align: center;
        margin-top: 24rpx;
    }

    .btn {
        width: 100%;
        height: 44px;
        line-height: 44px;
        text-align: center;
        margin-top: 10px;
        color: #fff;
        border-radius: 8px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        font-size: 36rpx;
        font-weight: bold;
    }

    .noDataImg {
        width: 128rpx;
        height: 128rpx;
        text-align: center;
        margin-top: 48rpx;
    }

    .noDataTips {
        margin-top: 32rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
    }

    .marginT80 {
        margin-top: 80rpx;
    }
}

.gray {
    background-color: #d6d6d6;
}

.btnBottom {
    position: fixed;
    bottom: calc(40rpx + env(safe-area-inset-bottom));
    left: 15px;
    right: 15px;
}

.slot-content {
    width: 100%;
    // height: 46px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    // line-height: 23px;
    text-align: center;
    margin: 32px auto 23px auto;
}
</style>
