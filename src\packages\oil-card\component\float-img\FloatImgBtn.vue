<template>
    <div>
        <movable-area class="movableArea">
            <movable-view
                class="movableView"
                :position="position"
                :x="x"
                :y="y"
                :direction="direction"
                :damping="damping"
                @click="onBtnClicked"
                @change="onChange"
                @touchend="onTouchend"
            >
                <img src="./img/isPreferentialSmall.png" mode="widthFix" class="iconImage" />
                <!-- <img src="./img/isPreferential_small.png" mode="widthFix" class="iconImage" alt=""> -->
                <!--<button open-type='contact' session-from='' class="contact">联系我们</button>-->
            </movable-view>
        </movable-area>
    </div>
</template>

<script>
export default {
    name: 'FloatImgBtn',
    props: {
        damping: {
            type: Number,
            default: 20,
        },
        direction: {
            type: String,
            default: 'all',
        },
        position: {
            type: Number,
            default: 4,
        },
    },
    methods: {
        onBtnClicked() {
            this.$emit('onFloatBtnClicked');
        },
        onChange(e) {
            if (e.detail.source === 'touch') {
                this.move.x = e.detail.x;
                this.move.y = e.detail.y;
            }
        },
        onTouchend() {
            this.x = this.move.x;
            this.y = this.move.y;
            setTimeout(() => {
                if (this.move.x < this.x2 / 2) this.x = this.x1;
                else this.x = this.x2;
                console.log(this.x, this.y);
            }, 30);
        },
    },
    mounted() {
        console.log(uni.getSystemInfoSync());
        uni.getSystemInfo({
            success: res => {
                this.x1 = 0;
                this.x2 = parseInt(res.windowWidth) - 50;
                this.y1 = 0;
                this.y2 = parseInt(res.windowHeight) - 20;
                setTimeout(() => {
                    if (this.position == 1 || this.position == 2) this.y = parseInt(this.y2 * 0.2);
                    if (this.position == 3 || this.position == 4) this.y = parseInt(this.y2 * 0.7);
                    if (this.position == 1 || this.position == 3) this.x = parseInt(this.x1);
                    if (this.position == 2 || this.position == 4) this.x = parseInt(this.x2);
                    this.move.x = this.x;
                    this.move.y = this.y;
                }, 10);
            },
        });
    },
    data() {
        return {
            x: Math.round(uni.getSystemInfoSync().windowWidth / 2),
            y: Math.round(uni.getSystemInfoSync().windowHeight / 2),
            x1: 0,
            x2: 0,
            y1: 0,
            y2: 0,
            move: {
                x: 0,
                y: 0,
            },
        };
    },
};
</script>

<style lang="scss" scoped>
.movableArea {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
    z-index: 999;

    .movableView {
        pointer-events: auto; //可以点击
        width: 120rpx;
        height: 120rpx;
        padding: 10rpx;
        border-radius: 100%;
        .iconImage {
            display: block;
            width: 120rpx;
        }
        @keyframes iconImage {
            0% {
                -webkit-transform: rotate(0deg);
            }
            25% {
                -webkit-transform: rotate(90deg);
            }
            50% {
                -webkit-transform: rotate(180deg);
            }
            75% {
                -webkit-transform: rotate(270deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        // 客服
        .contact {
            width: 50px;
            height: 50px;
            overflow: hidden;
            position: absolute;
            left: 0px;
            top: 0px;
            border-radius: 100%;
            opacity: 0;
        }
    }
}
</style>
