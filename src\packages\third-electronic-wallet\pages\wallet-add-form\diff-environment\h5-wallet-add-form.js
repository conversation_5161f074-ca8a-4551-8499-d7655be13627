import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
  methods: {
    /**
     * @description  : 点击查看协议
     * @param         {string} type - 协议类型
     * @param         {string} cityName - 城市编码
     * @param         {string} name - 协议名称
     * @return        {*}
     */
    async getAgreeOn() {
      //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
      // name传
      /*
          App用户使用协议
          App隐私协议
          电子钱包开通协议
          App充值协议
      */
      let params = {
        type: '1',
        cityName: '全国',
        name: '昆仑e享卡开通协议',
      };
      let userAgreementRes = await userAgreement(params);
      if (userAgreementRes.success) {
        if (userAgreementRes.data.fileUrl) {
          this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
        }
      } else {
        uni.showToast({ title: userAgreementRes.message || '未找到该协议' });
      }
    },
  }
}
