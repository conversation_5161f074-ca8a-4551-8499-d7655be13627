<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="我的电子券"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <div class="tabs_style p-LR-16 mh-0 fl-row fl-jus-aro fl-al-cen">
                    <div
                        v-for="(tab, index) in tabs"
                        :key="index"
                        :class="{ 'color-E64F22': selectId === tab.id }"
                        class="tab_style"
                        @click="selectClick(tab)"
                    >
                        <div class="title-name font-15 weight-500 te-center">{{ tab.title }}</div>
                        <div
                            :class="{
                                'bg-e64f22': selectId === tab.id,
                                'bg-transparent': selectId !== tab.id,
                            }"
                            class="tabselect_style"
                        ></div>
                    </div>
                </div>
                <div class="coupon-wrap f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="dataList"
                        emptyImage
                        emptyText
                        :showEmpty="showEmpty"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                    >
                        <div class="padding-16">
                            <div v-for="(item, index) in couponUseArray" :key="index">
                                <div class="border-rad-8 bg-coupon" v-if="selectId === 40" @click="usedDetails(item)">
                                    <div class="upperLeft font-10 weight-500 color-fff te-center bg-ff6133">
                                        {{ differentiationType(item.kind) }}
                                    </div>
                                    <div class="content-wrap fl-row">
                                        <div class="left-wrap fl-row">
                                            <div class="content-left fl-column fl-al-jus-cen">
                                                <div v-if="!item.templatePicOss">
                                                    <div class="price fl-al-jus-cen fl-row fl-al-base color-E64F22">
                                                        <!-- <div class="symbol font-14 weight-400" v-if="item.voucherType === '1'">&yen;</div>
                                                                            <div class="font-28 weight-600">{{ item.price }}</div>
                                                                                                    <div v-if="item.voucherType === '2'" class="symbol font-14 weight-400">折</div>-->
                                                        <!-- <div class="symbol" v-html="faceValueFilter(item)"></div> -->
                                                        <div
                                                            v-if="item.couponType && item.couponType != '40'"
                                                            class="symbol font-14 weight-400"
                                                            >&yen;</div
                                                        >
                                                        <div class="font-28 weight-600">
                                                            {{
                                                                item.couponType && item.couponType != '40'
                                                                    ? item.amount
                                                                    : item.discountValue
                                                            }}
                                                        </div>
                                                        <div
                                                            v-if="item.couponType && item.couponType == '40'"
                                                            class="symbol font-14 weight-400"
                                                            >折</div
                                                        >
                                                    </div>
                                                    <div class="font-13 weight-400 color-EB5130">{{ thresholdAmount(item) }}</div>
                                                </div>
                                                <img v-else-if="item.templatePicOss" :src="item.templatePicOss" />
                                            </div>
                                            <div class="content-sx"></div>
                                        </div>
                                        <div class="right-wrap">
                                            <div class="content-right">
                                                <div class="title font-12 color-1E1E1E weight-500">{{ item.title }}</div>
                                                <div class="fl-row fl-jus-bet fl-al-cen">
                                                    <div class="fl-column">
                                                        <div
                                                            class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400 color-FA6400 border-fa6400"
                                                            >{{ item.type == 1 ? '油品券' : '非油券' }}</div
                                                        >
                                                        <div class="time font-12 color-999 weight-400">{{ screen(item) }}</div>
                                                    </div>
                                                    <div class="img-wrap">
                                                        <img src="../../images/used.png" alt />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-rad-8 toTakeEffect" v-if="selectId === 70">
                                    <div class="upperLeft font-10 weight-500 color-fff te-center bg-999">
                                        {{ differentiationType(item.kind) }}
                                    </div>
                                    <div class="content-wrap fl-row">
                                        <div class="left-wrap fl-row">
                                            <div class="content-left fl-column fl-al-jus-cen">
                                                <div v-if="!item.templatePicOss">
                                                    <div class="price fl-row fl-al-base fl-al-jus-cen color-666">
                                                        <!-- <div class="symbol font-14 weight-400" v-if="item.voucherType === '1'">&yen;</div>
                           <div class="font-28 weight-600">{{ item.price }}</div>
                                                   <div v-if="item.voucherType === '2'" class="symbol font-14 weight-400">折</div>-->
                                                        <!-- <div class="symbol" v-html="faceValueFilter(item)"></div> -->
                                                        <div
                                                            v-if="item.couponType && item.couponType != '40'"
                                                            class="symbol font-14 weight-400"
                                                            >&yen;</div
                                                        >
                                                        <div class="font-28 weight-600">
                                                            {{
                                                                item.couponType && item.couponType != '40'
                                                                    ? item.amount
                                                                    : item.discountValue
                                                            }}
                                                        </div>
                                                        <div
                                                            v-if="item.couponType && item.couponType == '40'"
                                                            class="symbol font-14 weight-400"
                                                            >折</div
                                                        >
                                                    </div>
                                                    <div class="font-13 weight-400 color-666">{{ thresholdAmount(item) }}</div>
                                                </div>
                                                <img v-else-if="item.templatePicOss" :src="item.templatePicOss" />
                                            </div>
                                            <div class="content-sx"></div>
                                        </div>
                                        <div class="right-wrap">
                                            <div class="content-right">
                                                <div class="title font-12 color-1E1E1E weight-500">{{ item.title }}</div>
                                                <div class="fl-row fl-jus-bet fl-al-cen">
                                                    <div class="fl-column">
                                                        <div
                                                            class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400 color-666 border-999"
                                                            >{{ item.type == 1 ? '油品券' : '非油券' }}</div
                                                        >
                                                        <div class="time font-12 color-999 weight-400">{{ screen(item) }}</div>
                                                    </div>
                                                    <div class="img-wrap">
                                                        <img src="../../images/expired .png" alt />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
const PAGE_SIZE = 10;
import { couponList } from '../../../../s-kit/js/v3-http/https3/conpon/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 筛选已过期还是未过期
            tabs: [
                { id: 40, title: '已使用' },
                { id: 70, title: '已过期' },
            ],
            selectId: 40,
            couponUseArray: [],
            page: 1,
            topState: 0,
            categoryType: '',
            kind: '',
            sortType: 'distTime',
            orderWay: '3',
            refer: '',
        };
    },
    onLoad(query) {
        if (Object.keys(query).length) {
            let params = JSON.parse(decodeURIComponent(query.data));
            if (params.refer) {
                this.refer = params.refer;
            }
            if (params.type) {
                this.selectId = params.type == 10 ? 40 : params.type == 20 ? 70 : 40;
            }
        }
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        // 获取电子券已使用或已过期列表
        this.couponBiz();
        this.getUsageAndExpirationCouponList();
    },
    methods: {
        couponBiz() {
            if (this.selectId == 40) {
                this.$sKit.mpBP.tracker('优惠券', {
                    seed: 'couponBiz',
                    pageID: 'userCouponListPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            if (this.selectId == 70) {
                this.$sKit.mpBP.tracker('优惠券', {
                    seed: 'couponBiz',
                    pageID: 'gqCouponListPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
        },
        /**
         * @description  : 选择已过期还是已使用
         * @param        {Object} tab -选中的tab
         * @param        {Number} index -选中的下表
         * @param        {Number} index -选中的下表
         * @return        {*}
         */
        selectClick(tab, index) {
            if (this.selectId === tab.id) return;
            this.selectId = tab.id;
            this.couponBiz();
            // 获取电子券已使用或已过期列表
            this.getUsageAndExpirationCouponList({ isInit: true });
        },
        /**
         * @description  : 电子券类型
         * @return        {*}
         */
        getCouponType(val) {
            console.log(val, '电子券类型');
            return val == 10 ? '满减券' : val == 20 ? '计次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
        },
        /**
         * @description  : 区分券类型
         * @return        {*}
         */
        differentiationType(val) {
            console.log(val, '油品非油品------');
            // 券可用品类 1 油品 2 非油品
            return val == 1 ? '现金券' : '优惠券';
        },
        /**
         * @description  : 上拉加载
         * @return        {*}
         */
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getUsageAndExpirationCouponList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            //  重置数据 获取电子券已使用或已过期列表
            this.getUsageAndExpirationCouponList({ isInit: true });
        },
        /**
         * @description  : 获取电子券已使用或已过期列表
         * @param         {*} isInit 等于true的时候重置数据
         * @param         {*} couponArray:电子券数组
         * @param         {*} page:页码
         * @param         {*} totalPage:返回数据的条数
         * @param         {*} pageSize:每页10条
         * @param         {*} couponStatus:查询状态：20-未使用，40-已使用，70-已过期
         * @param         {*} categoryType:券品类：1-油品券2-非油券（不传参默认查询全部券）
         * @param         {*} kind:券类型：0—优惠券；1—现金券；（不传参默认查询全部券）
         * @param         {*} sort:排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
         * @param         {*} order:排序方式：1-升序2-降序3-默认排序
         * @return        {*}
         */
        async getUsageAndExpirationCouponList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    couponUseArray: [],
                    page: 1,
                    topState: 0,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, couponUseArray, totalPage } = this;
            let params = {
                pageNum: this.page,
                pageSize: PAGE_SIZE,
                couponStatus: this.selectId.toString(), // 券类型：优惠券类型必须为10满减券  20记次券 30兑换券 40折扣券"
                categoryType: this.categoryType,
                kind: this.kind,
                sort: this.sortType,
                order: this.orderWay,
            };
            let res = await couponList(params);
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows;
                const currentTimestamp = new Date().getTime();
                // 遍历电子券数组 将返回的时间转成时间戳，与当前时间对比，给券标记已到期标识
                list = list.map(item => {
                    var handelTime = new Date(item.startTime.replace(/-/g, '/'));
                    if (currentTimestamp > Date.parse(handelTime)) {
                        // 到使用日期
                        item.bgColor = true;
                    } else {
                        // 未到使用日期
                        item.bgColor = false;
                    }
                    return item;
                });
                // 将处理好的数组合并到定义的数组，放到页面渲染
                couponUseArray = couponUseArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    couponUseArray,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && page >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = couponUseArray.length <= 0 ? true : false;
            }
        },
        // 处理折和元
        faceValueFilter(item) {
            if (item.couponType && item.couponType == '40') {
                return item.discountValue + '<span style="font-size: 12px;">折</span>';
            } else {
                return (
                    '<span class="font-style" style="font-size: 12px">￥</span>' + `<span style="font-size: 28px;">${item.amount}</span>`
                );
            }
        },
        // 满减券和折扣券说明
        thresholdAmount(item) {
            if (item.thresholdAmount) {
                if (item.couponType && item.couponType == '40') {
                    return '满' + item.thresholdAmount + '元可用';
                } else {
                    return '满' + item.thresholdAmount + '减' + item.amount;
                }
            } else {
                return '无金额门槛';
            }
        },
        // 处理时间(10.08与产品确定列表只展示年月日，详情展示时分秒)
        screen(item) {
            let text = '';
            if (this.selectid == 40) {
                if (item.bgColor) {
                    text = '有效期至' + item.endTime;
                } else {
                    text = '开始日期' + item.startTime;
                }
            } else {
                text = '有效期至' + item.endTime;
            }
            return text;
        },
        // 电子券已使用详情
        usedDetails(item) {
            let url = `/packages/third-coupon-module/pages/coupon-used-detail/main`;
            let params = { ...item, refer: this.refer };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        .tab_style {
            line-height: 20px;
            width: 100%;
            .title-name {
                width: 50%;
                margin: 0 auto;
            }
            .tabselect_style {
                height: 3px;
                border-radius: 1px;
                width: 30px;
                margin: 5px auto 0 auto;
            }
        }
    }
    .type-sort {
        width: 100%;
        height: 44px;
        .sort-item {
            height: 30px;
            line-height: 30px;
            margin-right: 12px;
            padding: 0 6.5px; // 上右下左
        }
        .selectSorted {
            color: #e64f22;
        }
    }
    .reminder {
        width: 100%;
        height: 32px;
        line-height: 32px;
    }
    .coupon-wrap {
        .bg-coupon {
            opacity: 0.3;
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);
            &::before {
                opacity: 0.3;
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                        img {
                            width: 65px;
                            height: 65px;
                            line-height: 65px;
                            margin-top: 23rpx;
                        }
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.5;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    .content-right {
                        // margin-top: -17px;
                        .img-wrap {
                            width: 67px;
                            height: 58px;
                            img {
                                width: 67px;
                                height: 58px;
                                position: absolute;
                                bottom: 0px;
                                right: 0px;
                            }
                        }
                        .title {
                            // line-height: 21px;
                            margin-top: 14px;
                            // margin-bottom: 20px;
                            margin-right: 22px;
                            overflow: hidden;
                            // max-height: 32px;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }
                        .useBtn {
                            width: 77.5px;
                            height: 27px;
                            line-height: 27px;
                            margin-right: 12px;
                        }
                        .time {
                            line-height: 16.5px;
                            margin-top: 6px;
                        }
                        .type-cou {
                            width: 36px;
                            height: 12.5px;
                            line-height: 12.5px;
                            margin-top: 8px;
                        }
                    }
                }
            }
        }
        // 券未生效
        .toTakeEffect {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;
            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                        img {
                            width: 65px;
                            height: 65px;
                            line-height: 65px;
                            margin-top: 23rpx;
                        }
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.3;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    .content-right {
                        // margin-top: -17px;
                        .img-wrap {
                            width: 67px;
                            height: 58px;
                            img {
                                width: 67px;
                                height: 58px;
                                position: absolute;
                                bottom: 0px;
                                right: 0px;
                            }
                        }
                        .title {
                            // line-height: 21px;
                            margin-top: 14px;
                            // margin-bottom: 20px;
                            margin-right: 22px;
                        }
                        .useBtn {
                            width: 77.5px;
                            height: 27px;
                            line-height: 27px;
                            margin-right: 12px;
                        }
                        .time {
                            line-height: 16.5px;
                            margin-top: 6px;
                        }
                        .type-cou {
                            width: 36px;
                            height: 12.5px;
                            line-height: 12.5px;
                            margin-top: 8px;
                        }
                    }
                }
            }
        }
    }
    .btnWrap {
        width: 100%;
        height: 44px;
        line-height: 44px;
        // padding-bottom: 15px;
        margin: 0 auto 10px;
    }
}
</style>
