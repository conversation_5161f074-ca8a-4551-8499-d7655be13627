<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="container bg-F7F7FB">
            <zj-navbar title="忘记登录密码" :border-bottom="false"></zj-navbar>
            <div class="phone">{{ phoneNum }}</div>
            <!-- <div class="tipsInfo">我们将发送验证码到您的手机</div> -->
            <div class="formBox">
                <input
                    v-model="validCode"
                    placeholder="请输入短信验证码"
                    placeholder-class="forgot_pas_inp"
                    type="text"
                    maxlength="6"
                    minlength="6"
                />
                <div class="verificationBtn" @click="sendCode" v-if="show">获取验证码</div>
                <div v-else class="verificationBtn marginRight">{{ count }}秒后再次获取</div>
            </div>
            <div class="btnBox" @click="confirmClick()">确认提交</div>
            <zj-show-modal> </zj-show-modal>
        </div>
    </div>
</template>
<script>
import { messageCodeSend, noLoginVerify, messageCodeVerify } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import { memberInfo } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { mapGetters, mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'forgot-password',
    data() {
        return {
            name: '',
            flag: true,
            codeFlag: true,
            count: 60,
            checkCode: '',
            timer: null,
            show: true,
            validCode: '',
            phone: '',
            phoneNum: '',
            timerSendCode: null,
            messageType: false,
            isClose: 0,
            queryInfo: '',
        };
    },
    onLoad(option) {
        let queryInfo = option.data ? JSON.parse(decodeURIComponent(option.data)) : {};
        console.log(queryInfo, 'queryInfo=====');
        this.queryInfo = queryInfo;
        this.isClose = queryInfo.isClose ? queryInfo.isClose : 0;
    },
    created() {
        clearInterval(this.timer);
        this.sendCode = this.$sKit.commonUtil.throttleUtil(this.sendCode); // 节流
        this.confirmClick = this.$sKit.commonUtil.throttleUtil(this.confirmClick); // 节流
    },
    async mounted() {
        // 获取用户信息
        this.phoneNum = this.memberBaseInfo.phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
    },
    methods: {
        //确认提交事件
        async confirmClick() {
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.validCode) {
                uni.showToast({
                    title: '验证码不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.$sKit.test.checkValidCode(this.validCode)) {
                uni.showToast({
                    title: '验证码格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let params = {
                mobile: '',
                messageCode: this.validCode,
                type: 4,
                useMobile: true,
            };
            let res = await messageCodeVerify(params);
            if (res.success) {
                console.log(res, 'messageCodeVerify=========');
                let url = '/packages/third-my-center/pages/change-login-password/main';
                let params = { authInfo: res.data.authInfo, mobile: this.phone, page: this.queryInfo.page };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },

        /**
         * 发送验证码
         */
        async sendCode() {
            if (!this.show) return;
            if (!this.timerSendCode) {
                let params = {
                    mobile: this.phone,
                    messageType: 4,
                };
                let res = await messageCodeSend(params);
                console.log(res, '发送验证码===========');
                if (res.success) {
                    this.getCode();
                    this.show = false;
                    uni.showToast({
                        title: '验证码发送成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.messageType = true;
                }
                // this.$accountCenter.getVerifyCode({ type: 1 }, res => {
                //     if (res.isSuccessed) {
                //         this.getCode();
                //         this.show = false;
                //         uni.showToast({
                //             title: '验证码发送成功',
                //             icon: 'none',
                //             duration: 2000,
                //         });
                //         this.messageType = true;
                //     } else {
                //         uni.showToast({
                //             title: res.desString || '验证码发送失败，请稍后再试',
                //             icon: 'none',
                //             duration: 2000,
                //         });
                //     }
                // });
                this.timerSendCode = setTimeout(() => {
                    this.timerSendCode = null;
                }, 3000);
            }
        },
        /**
         * 获取验证码倒计时
         */
        getCode() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        //关闭当前小程序
        backEvent() {
            // #ifdef MP-MPAAS
            // this.$cnpcBridge.closeMriver(res => {});
            // #endif
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    components: {},
};
</script>
<style lang="scss" scoped>
.container {
    height: 100%;
    padding: 0 15px;

    .phone {
        text-align: center;
        font-size: 21px;
        line-height: 30px;
        margin-top: 54px;
    }

    .tipsInfo {
        text-align: center;
        font-size: 12px;
        color: #999999;
        margin-bottom: 10px;
    }

    .formBox {
        width: 100%;
        height: 44px;
        background: #f7f7fb;
        margin-bottom: 13px;
        margin-top: 23px;
        // display: flex;
        // flex-direction: row;
        // align-items: center;
        position: relative;

        input {
            width: 100%;
            height: 44px;
            flex: 1;
            border-radius: 8px;
            // border-bottom-left-radius: 8px;
            background: #fff;
            padding-left: 15px;
            box-sizing: border-box;
            // justify-content: flex-start;
        }

        .verificationBtn {
            width: 80px;
            height: 44px;
            text-align: right;
            line-height: 44px;
            // background: #fff;/
            white-space: nowrap;
            // border-top-right-radius: 8px;
            // border-bottom-right-radius: 8px;
            color: #e64f22;
            // padding-right: 13px;
            font-size: 14px;
            position: absolute;
            right: 20px;
            top: 0;
        }
    }

    .btnBox {
        height: 44px;
        margin-top: 12px;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        border-radius: 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        color: #ffffff;
    }
}

.tips_div {
    margin-top: 16px;

    .title {
        color: #818183;
    }

    .text {
        color: #8b8b8d;
        line-height: 21px;
    }
}

.tc_div {
    text-align: center;
}
.marginRight {
    margin-right: 8px;
}
</style>
<style lang="scss">
.forgot_pas_inp {
    font-size: 14px;
    color: #999999;

    line-height: 20px;
}
</style>
