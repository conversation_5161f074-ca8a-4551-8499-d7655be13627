<template>
    <div class="one_lick_login">
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>
        <div class="padding_input">
            <input type="text" maxlength="11" class="input_div" placeholder-class="phone_input" placeholder="请输入手机号" />
            <div class="agreement_div">
                <img v-if="!select" src="../../images/not_selected.png" @click="changeSelect(true)" alt />
                <img v-else src="../../images/selected.png" @click="changeSelect(false)" alt />
                我已阅读并同意能源e站
                <div class="argreement_rules">《用户协议》</div>和
                <div class="argreement_rules">《隐私政策》</div>
            </div>
            <div class="primary-btn2" @click="getCode()">获取验证码</div>
            <div class="help_div help_div_text_right" style="overflow: hidden">
                <div class="help_text width_100" style="text-align: right">遇到问题？</div>
            </div>
        </div>
        <otherWay></otherWay>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import otherWay from '../../components/login-other-way/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'one-click-login',
    components: { otherWay },
    data() {
        return {
            select: false,
        };
    },
    methods: {
        changeSelect(val) {
            this.select = val;
        },
        getCode() {
            this.$sKit.layer.useRouter('/packages/third-login/pages/security-verification/main', {}, 'navigateTo');
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../style/style.scss';
.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
}
.padding_input {
    padding: 16px;
}
.operator {
    margin-bottom: 16px;
}

input {
    height: 51px;
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    font-size: 21px;
    font-weight: 600;
    color: #333333;
    line-height: 30px;
    margin-bottom: 12px;
    padding-left: 20px;
    box-sizing: border-box;
}

.agreement_div {
    margin-bottom: 16px;
    color: #999999;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    img {
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
    }
}
.argreement_rules {
    color: #e64f22;
}
</style>

<style lang="scss">
.phone_input {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
}
</style>
