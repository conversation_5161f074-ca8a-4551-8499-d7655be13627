<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="container bg-F7F7FB">
            <!-- <zj-navbar title="忘记支付密码" :border-bottom="false"></zj-navbar> -->
            <zj-navbar title="忘记支付密码" :border-bottom="false"></zj-navbar>
            <div v-if="!isFaceAuth">
                <div class="phone">{{ phoneNum }}</div>
                <div class="tipsInfo">我们将发送验证码到您的手机</div>
                <div class="formBox">
                    <input
                        v-model="validCode"
                        placeholder="请输入短信验证码"
                        placeholder-class="forgot_pas_inp"
                        type="text"
                        maxlength="6"
                        minlength="6"
                    />
                    <div class="verificationBtn" @click="sendCode" v-if="show">获取验证码</div>
                    <div v-else class="verificationBtn marginRight">{{ count }}秒后再次获取</div>
                </div>
                <div class="btnBox" @click="confirmClick()">确认提交</div>
            </div>
            <div v-else>
                <div class="content_div">
                    <img class="image" src="../../images/real_person.png" alt="" />
                    <div class="tips_div">
                        <div class="font-14 weight-bold title">温馨提示：</div>
                        <div class="font-13 text">1.昆仑e享卡与实体卡移动支付共用同一个支付密码，修改后实体卡线下支付密码不受影响;</div>
                        <div class="font-13 text">2.修改后，能源e站支付宝小程序与微信小程序的线上支付密码也同时变更;</div>
                        <div class="font-13 text">3.为保证您的账号安全需要先完成实人认证。</div>
                    </div>
                </div>
                <div class="btnBox" @click="confirmClick()">确认并开始实人认证</div>
            </div>
        </div>
        <zj-show-modal> </zj-show-modal>
        <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
    </div>
</template>
<script>
// import {
//   accountLogoutApi,
//   messageCodeSendApi,
// } from "@/api/https3/accountService/index.js";
// import pageConfig from "@/utils/pageConfig.js";
// import { mapState, mapGetters } from "vuex";
import { mapGetters, mapState } from 'vuex';
import {
    initRealPersonIdentify,
    realPersonIdentify,
    identityAuthInfo,
    realNameAuth,
} from '../../../../s-kit/js/v3-http/https3/oilCard/index';
// ../../../../../s-kit/js/v3-http/https3/oilCard/index
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'forgot-password',
    data() {
        return {
            name: '',
            flag: true,
            codeFlag: true,
            count: 60,
            checkCode: '',
            timer: null,
            show: true,
            validCode: '',
            phone: '',
            phoneNum: '',
            timerSendCode: null,
            messageType: false,
            isClose: 0,
            isShowContract: false,
            refer: '',
            authInfo: '',
            isFaceAuth: false, //3.6.9及以上版本使用人脸，低版本还继续保持短信验证码
        };
    },
    onLoad(option) {
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        if (params.refer) this.refer = params.refer;
        this.$sKit.mpBP.tracker('忘记支付密码', {
            seed: 'forgetPsdBiz',
            pageID: 'forgetPsdPage', // 页面名
            refer: this.refer, // 来源
            channelID: clientCode, // C10/C12/C13
        });
        this.isClose = queryInfo.isClose ? queryInfo.isClose : 0;
    },
    async created() {
        clearInterval(this.timer);
        // #ifdef MP-MPAAS
        this.isFaceAuth = await this.$cnpcBridge.judgeProtocolCall('3.6.9');
        // #endif
        // // 获取会员等级  包括客户名称，手机号，会员等级
        // // 如果注销前没有打开‘我的’页面，需要调一次
        // this.$store.dispatch("getMembershipLevel", {});
        // this.getCode = this.$util.throttleUtil(this.getCode);

        this.sendCode = this.$sKit.commonUtil.throttleUtil(this.sendCode); // 节流
        this.confirmClick = this.$sKit.commonUtil.throttleUtil(this.confirmClick); // 节流
    },
    async mounted() {
        await this.$cnpcBridge.isCutScreen(true);
        // 获取用户信息
        this.phoneNum = this.memberBaseInfo.phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        ...mapGetters(['memberBaseInfo']),
    },
    methods: {
        // agreeActivate
        async agreeActivate() {
            let res = await identityAuthInfo();
            if (res.success) {
                this.realNameAuthFn(res.data);
            }
        },
        async realNameAuthFn(userInfo) {
            let params = {
                realName: userInfo.realName,
                idType: 1,
                idNo: userInfo.identityNo,
                type: '2',
            };
            let res = await realNameAuth(params);
            if (res && res.success) {
                this.authInfo = res.data.authInfo;
                this.initFaceAuthentication();
            }
        },
        async initFaceAuthentication() {
            let params = {
                returnUrl: '',
                metaInfo: await this.$cnpcBridge.aliMetaInfo(),
                verifyMode: '1',
            };
            let res = await initRealPersonIdentify(params);
            if (res && res.success) {
                this.verifyUnique = res.data.verifyUnique;
                this.$cnpcBridge.aliFaceCollec(res.data.certifyId, async result => {
                    //
                    if (result.status) {
                        //采集成功
                        this.realNameAuthentication(res.data);
                    } else {
                        this.$store.dispatch('zjShowModal', {
                            title: result.msg,
                            confirmText: '确认',
                            success: res => {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                }
                            },
                        });
                    }
                });
            }
        },
        async realNameAuthentication(data) {
            let params = {
                type: '2',
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                verifyMode: '1',
                authInfo: this.authInfo,
            };
            let res = await realPersonIdentify(params);
            if (res.success) {
                uni.showLoading();
                this.$accountCenter.forgotPW({ certifyId: data.certifyId, verifyNo: data.verifyUnique }, res => {
                    uni.hideLoading();
                    if (res.isSuccessed) {
                        this.$sKit.mpBP.tracker('忘记支付密码', {
                            seed: 'forgetPsdBiz',
                            pageID: 'settingPsdsuccessToast', // 页面名
                            refer: this.refer, // 来源
                            channelID: clientCode, // C10/C12/C13
                        });
                        uni.showToast({
                            title: '密码重置成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        if (Number(this.isClose)) {
                            setTimeout(() => {
                                uni.navigateBack({ delta: 1 });
                            }, 2000);
                        } else {
                            // #ifdef MP-MPAAS
                            this.backEvent();
                            // #endif
                        }
                    } else {
                        uni.showToast({
                            title: res.desString || '密码重置失败，请稍后再试',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                });
            }
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            if (this.isFaceAuth){
                this.agreeActivate();
            } else  {
                this.$sKit.commonUtil.nextOilTriggerRisk(2).then(
                    res => {
                        if (!this.timerSendCode) {
                            this.$accountCenter.getVerifyCode({ type: 1 }, res => {
                                if (res.isSuccessed) {
                                    this.getCode();
                                    this.show = false;
                                    uni.showToast({
                                        title: '验证码发送成功',
                                        icon: 'none',
                                        duration: 2000,
                                    });
                                    this.messageType = true;
                                } else {
                                    uni.showToast({
                                        title: res.desString || '验证码发送失败，请稍后再试',
                                        icon: 'none',
                                        duration: 2000,
                                    });
                                }
                            });
                            this.timerSendCode = setTimeout(() => {
                                this.timerSendCode = null;
                            }, 3000);
                        }
                    },
                    () => {},
                );
            }
        },
        //确认提交事件
        confirmClick() {
            this.$sKit.mpBP.tracker('忘记支付密码', {
                seed: 'forgetPsdBiz',
                pageID: 'comfirmSubmitBut', // 页面名
                refer: this.refer, // 来源
                channelID: clientCode, // C10/C12/C13
            });
            if (this.isFaceAuth) {
                this.$store.dispatch('changeFacePop', true);
                return;
            }
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.validCode) {
                uni.showToast({
                    title: '验证码不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.$sKit.test.checkValidCode(this.validCode)) {
                uni.showToast({
                    title: '验证码格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            uni.showLoading({
                title: '加载中',
                mask: true,
            });
            // let _this = this
            this.$accountCenter.forgotPW({ verifyCode: this.validCode }, res => {
                uni.hideLoading();
                //
                if (res.isSuccessed) {
                    this.$sKit.mpBP.tracker('忘记支付密码', {
                        seed: 'forgetPsdBiz',
                        pageID: 'settingPsdsuccessToast', // 页面名
                        refer: this.refer, // 来源
                        channelID: clientCode, // C10/C12/C13
                    });
                    uni.showToast({
                        title: '密码重置成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    if (Number(this.isClose)) {
                        setTimeout(() => {
                            uni.navigateBack({ delta: 1 });
                        }, 1000);
                    } else {
                        // #ifdef MP-MPAAS
                        this.backEvent();
                        // #endif
                    }
                } else {
                    uni.showToast({
                        title: res.desString || '密码重置失败，请稍后再试',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        },

        /**
         * 发送验证码
         */
        sendCode() {
            if (!this.show) return;
            this.$store.dispatch('changeFacePop', true);
        },
        /**
         * 获取验证码倒计时
         */
        getCode() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        //关闭当前小程序
        backEvent() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
        },
    },
    async beforeDestroy() {
        clearInterval(this.timer);
        // #ifdef MP-MPAAS
        if(this.isHarmony){
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        if(this.isHarmony){
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    components: {},
};
</script>
<style lang="scss" scoped>
.container {
    height: 100%;
    padding: 0 15px;

    .phone {
        text-align: center;
        font-size: 21px;
        line-height: 30px;
        margin-top: 54px;
    }

    .tipsInfo {
        text-align: center;
        font-size: 12px;
        color: #999999;
        margin-bottom: 10px;
    }

    .formBox {
        width: 100%;
        height: 44px;
        background: #f7f7fb;
        margin-bottom: 13px;
        margin-top: 43px;
        // display: flex;
        // flex-direction: row;
        // align-items: center;
        position: relative;

        input {
            width: 100%;
            height: 44px;
            flex: 1;
            border-radius: 8px;
            // border-bottom-left-radius: 8px;
            background: #fff;
            padding-left: 15px;
            box-sizing: border-box;
            // justify-content: flex-start;
        }

        .verificationBtn {
            width: 80px;
            height: 44px;
            text-align: right;
            line-height: 44px;
            // background: #fff;/
            white-space: nowrap;
            // border-top-right-radius: 8px;
            // border-bottom-right-radius: 8px;
            color: #e64f22;
            // padding-right: 13px;
            font-size: 14px;
            position: absolute;
            right: 20px;
            top: 0;
        }
    }

    .btnBox {
        height: 44px;
        margin-top: 12px;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        border-radius: 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        color: #ffffff;
    }
}

.tips_div {
    margin-top: 16px;

    .title {
        color: #818183;
    }

    .text {
        color: #333;
        line-height: 21px;
    }
}

.tc_div {
    text-align: center;
}
.image {
    width: 280px;
}
.content_div {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
.marginRight {
    margin-right: 8px;
}
</style>
<style lang="scss">
.forgot_pas_inp {
    font-size: 14px;
    color: #999999;

    line-height: 20px;
}
</style>
