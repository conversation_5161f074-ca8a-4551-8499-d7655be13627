/**
 * @class
 * @name Hkyz
 * @description APP 协议类 - 新版
 *
 * @example
 * uni.$petro.Bridge.hkyz
 */
const jsBridge = {
    /**
     * 桥接函数
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.native();
     * console.log(res);
     */
    native(data = {}) {
        return new Promise((resolve, reject) => {
            console.log(
                'native',
                data.type,
                JSON.stringify({
                    method: data.method || '',
                    ...(data.data || {}),
                }),
            );
            try {
                my.call(
                    data.type || '',
                    {
                        method: data.method || '',
                        ...(data.data || {}),
                    },
                    res => {
                        console.log('result', JSON.stringify(res));
                        resolve(res);
                    },
                );
            } catch (err) {
                console.log('err', JSON.stringify(err));
                reject(err);
            }
        });
    },

    /**
     * 关闭小程序
     * @param data
     * @param data.appid 小程序appid，不传默认为当前小程序appid
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.closeMriver();
     * console.log(res);
     */
    async closeMriver(appid) {
        let data = {
            appid: appid,
        };
        return await jsBridge.native({
            type: 'closeMriver',
            data,
        });
    },

    /**
     * 唤起导航
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.nav();
     * console.log(res);
     */
    async nav(data = {}) {
        return await jsBridge.native({
            type: 'nav',
            data,
        });
    },

    /**
     * 获取设备支持的生物识别类型
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.supportType();
     * console.log(res);
     */
    async supportType(data = {}) {
        return await jsBridge.native({
            type: 'supportType',
            data,
        });
    },

    /**
     * 获取app的环境
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.workspaceName();
     * console.log(res);
     */
    async workspaceName(data = {}) {
        return await jsBridge.native({
            type: 'workspaceName',
            data,
        });
    },

    /**
     * 用手机浏览器打开url
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.mobileBrowsers();
     * console.log(res);
     */
    async mobileBrowsers(data = {}) {
        return await jsBridge.native({
            type: 'mobileBrowsers',
            data,
        });
    },
    /**
     * 获取状态栏高度
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.barHeight();
     * console.log(res);
     */
    async barHeight(data = {}) {
        return await jsBridge.native({
            type: 'barHeight',
            data,
        });
    },

    /**
     * 检查权限
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.checkPermission();
     * console.log(res);
     */
    async checkPermission(data = {}) {
        return await jsBridge.native({
            type: 'checkPermission',
            data,
        });
    },

    /**
     * 检查APP更新
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.checkUpdate();
     * console.log(res);
     */
    async checkUpdate(data = {}) {
        return await jsBridge.native({
            type: 'checkUpdate',
            data,
        });
    },

    /**
     * 获取小程序版本号
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getMiniVersion();
     * console.log(res);
     */
    async getMiniVersion(data = {}) {
        return await jsBridge.native({
            type: 'getMiniVersion',
            data,
        });
    },

    /**
     * 刷新令牌
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.refreshToken();
     * console.log(res);
     */
    async refreshToken(data = {}) {
        return await jsBridge.native({
            type: 'refreshToken',
            data,
        });
    },

    /**
     * 微信分享配置
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.wechatShare();
     * console.log(res);
     */
    async wechatShare(data = {}) {
        return await jsBridge.native({
            type: 'wechatShare',
            data,
        });
    },

    /**
     * 获取令牌
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getToken();
     * console.log(res);
     */
    async getToken(data = {}) {
        return await jsBridge.native({
            type: 'getToken',
            data,
        });
    },

    /**
     * 获取中台令牌
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getGsmsToken();
     * console.log(res);
     */
    async getGsmsToken(data = {}) {
        return await jsBridge.native({
            type: 'getGsmsToken',
            data,
        });
    },

    /**
     * 获取会员编码
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getUserId();
     * console.log(res);
     */
    async getUserId(data = {}) {
        return await jsBridge.native({
            type: 'getUserId',
            data,
        });
    },

    /**
     * 获取风控设备指纹 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getFinger();
     * console.log(res);
     */
    async getFinger(data = {}) {
        return await jsBridge.native({
            type: 'getFinger',
            data,
        });
    },

    /**
     * 支付中心SDK *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.paymentCenter();
     * console.log(res);
     */
    async paymentCenter(data = {}) {
        return await jsBridge.native({
            type: 'paymentCenter',
            data,
        });
    },

    /**
     * 账户中心SDK *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.accountCenter();
     * console.log(res);
     */
    async accountCenter(data = {}) {
        return await jsBridge.native({
            type: 'accountCenter',
            data,
        });
    },

    /**
     * 设备信息 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.commonArgs();
     * console.log(res);
     */
    async commonArgs(data = {}) {
        return await jsBridge.native({
            type: 'commonArgs',
            data,
        });
    },

    /**
     * 定位 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.locationInfo();
     * console.log(res);
     */
    async locationInfo(data = {}) {
        return await jsBridge.native({
            type: 'locationInfo',
            data,
        });
    },

    /**
     * 面部识别(IOS) *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.faceID();
     * console.log(res);
     */
    async faceID(data = {}) {
        return await jsBridge.native({
            type: 'faceID',
            data,
        });
    },

    /**
     * 指纹识别 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.fingerPrintRe();
     * console.log(res);
     */
    async fingerPrintRe(data = {}) {
        return await jsBridge.native({
            type: 'fingerPrintRe',
            data,
        });
    },

    /**
     * 阿里人脸获取aliMetaInfo *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.aliMetaInfo();
     * console.log(res);
     */
    async aliMetaInfo(data = {}) {
        return await jsBridge.native({
            type: 'aliMetaInfo',
            data,
        });
    },

    /**
     * 阿里人脸图像采集 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.faceCollecAli();
     * console.log(res);
     */
    async faceCollecAli(data = {}) {
        return await jsBridge.native({
            type: 'faceCollecAli',
            data,
        });
    },

    /**
     * 微信支付 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.wechatPay();
     * console.log(res);
     */
    async wechatPay(data = {}) {
        return await jsBridge.native({
            type: 'wechatPay',
            data,
        });
    },

    /**
     * 支付宝支付 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.aliPay();
     * console.log(res);
     */
    async aliPay(data = {}) {
        return await jsBridge.native({
            type: 'aliPay',
            data,
        });
    },

    /**
     * 清除缓存 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.cleanCache();
     * console.log(res);
     */
    async cleanCache(data = {}) {
        return await jsBridge.native({
            type: 'cleanCache',
            data,
        });
    },

    /**
     * 统计缓存 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getCacheSize();
     * console.log(res);
     */
    async getCacheSize(data = {}) {
        return await jsBridge.native({
            type: 'getCacheSize',
            data,
        });
    },

    /**
     * 获取智能开关的值 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getSwitch();
     * console.log(res);
     */
    async getSwitch(data = {}) {
        return await jsBridge.native({
            type: 'getSwitch',
            data,
        });
    },

    /**
     * 设置mPaas埋点事件 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.setEvent();
     * console.log(res);
     */
    async setEvent(data = {}) {
        return await jsBridge.native({
            type: 'setEvent',
            data,
        });
    },

    /**
     * 打开权限设置 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.openPermissions();
     * console.log(res);
     */
    async openPermissions(data = {}) {
        return await jsBridge.native({
            type: 'openPermissions',
            data,
        });
    },

    /**
     * 获取图片 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getPhoto();
     * console.log(res);
     */
    async getPhoto(data = {}) {
        return await jsBridge.native({
            type: 'getPhoto',
            data,
        });
    },

    /**
     * 打开第三方APP *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.openApp();
     * console.log(res);
     */
    async openApp(data = {}) {
        return await jsBridge.native({
            type: 'openApp',
            data,
        });
    },

    /**
     * 打电话 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.callPhone();
     * console.log(res);
     */
    async callPhone(data = {}) {
        return await jsBridge.native({
            type: 'callPhone',
            data,
        });
    },

    /**
     * 扫一扫 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.scan();
     * console.log(res);
     */
    async hkScan(data = {}) {
        return await jsBridge.native({
            type: 'hkScan',
            data,
        });
    },

    /**
     * 通过用户id获取手机号 *需要申请白名单
     * @param data
     *
     * @example
     *
     * const res =  uni.$petro.Bridge.hkyz.getPhoneByUserId();
     * console.log(res);
     */
    async getPhoneByUserId(data = {}) {
        return await jsBridge.native({
            type: 'getPhoneByUserId',
            data,
        });
    },
};

export default jsBridge;
