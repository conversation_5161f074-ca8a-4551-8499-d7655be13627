<template>
    <view>
        <button
            v-if="layer"
            class="layer-btn"
            open-type="getPhoneNumber"
            scope="phoneNumber"
            @getphonenumber="onGetAuthorize"
            @onError="onAuthError"
        >
            <slot></slot>
        </button>
        <!--        <root-portal :enable="true">-->
        <!--            <view class="dialog-login" v-if="visible">-->
        <!--                <view class="dialog-login-main">-->
        <!--                    <view class="dialog-login-content">-->
        <!--                        <view class="title">{{ type === 1 ? '支付宝授权登录' : '注册' }}</view>-->
        <!--                        <view class="center">-->
        <!--                            <view class="subtitle">授权手机号</view>-->
        <!--                            <view class="phone">{{ phone || '获取手机号失败' }}</view>-->
        <!--                        </view>-->
        <!--                        -->
        <!--                    </view>-->
        <!--                    <view class="dialog-close" @click="close"></view>-->
        <!--                </view>-->
        <!--            </view>-->
        <!--        </root-portal>-->
        <!--        <button class="btn" @click="login(true)">一键{{ type === 1 ? '登录' : '注册' }}</button>-->
        <root-portal :enable="true">
            <zj-unrealized-authentication
                v-if="rnAuthVisible"
                @realNameDialogClose="rnAuthClose"
                @realNameInfo="startRnAuth"
            ></zj-unrealized-authentication>
        </root-portal>
        <root-portal :enable="true">
            <zj-agreement @enterNavEvent="startRlAuth"></zj-agreement>
        </root-portal>
    </view>
</template>
<script>
import { userLogin, userPhone } from '@/s-kit/js/v3-http/https3/user';
import {
    basicInfoQuery,
    identityAuthInfo,
    initRealPersonIdentify,
    realNameAuth,
    realPersonIdentify,
} from '@/s-kit/js/v3-http/https3/oilCard';
import Store from '@/store';
import debounce from 'uview-ui/libs/function/debounce';
import { getCppeiLoginInfo, loginToGsms, upgradeV3, upgradeV3status } from '../../s-kit/js/v3-http/https3/user';
import projectConfig from '../../../project.config';

const environment = {
    '/gsms-dev/': 'http://************:30120',
    '/gsms-qas/': 'http://************:30120',
    '/gsms-sit/': 'http://************:30120',
    '/gsms-uat/': 'http://***********:30120',
    '/gsms-mpaas-default/': 'http://*************:20100',
    '/gsms-mpaas-sit/': 'http://*************:20100',
};

export default {
    mixins: [],
    data() {
        return {
            visible: false, // 弹窗显隐
            phone: '', // 授权手机号
            type: 1, // 1 登录 0 注册
            rnAuthVisible: false, // 实名认证弹窗显隐
        };
    },
    props: {
        // 作为button组件层包裹使用形式
        layer: {
            type: Boolean,
            default: false,
        },
        // 静默一键登录
        silence: {
            type: Boolean,
            default: false,
        },
        // 非3.0账号升级为3.0账号 1 需要试点 2 强制升级 0 不做升级逻辑处理
        autoV3: {
            type: Boolean,
            default: false,
        },
    },
    async mounted() {},
    methods: {
        /**
         * 授权一键登录/获取手机号
         * @param e
         * @returns {Promise<void>}
         */
        async onGetAuthorize(e) {
            this.$sKit.mpBP.tracker('登录/注册', {
                seed: 'loginOrRegistBiz',
                pageID: 'loginPop-up', // 页面名
                channelID: projectConfig.clientCode, // C10/C12/C13
            });
            if (e.detail?.errMsg !== 'getPhoneNumber:ok') {
                console.warn('用户拒绝授权');
                return;
            }
            this.login();
        },
        /**
         * 授权异常
         * @param err
         */
        onAuthError(err) {
            console.warn(err);
        },
        /**
         * 用户登录
         * @param silence 是否静默登录/注册
         * @returns {Promise<void>}
         */
        async login(silence = true) {
            debounce(
                async _ => {
                    try {
                        uni.showLoading({ mask: true });
                        console.log('静默登录', silence || this.silence);
                        if (silence || this.silence) {
                            await this.onLogin();
                            uni.hideLoading();
                            return;
                        }
                        await this.onAuthPhone();
                        this.visible = true;
                    } catch (err) {
                        console.error('登录失败', err);
                    } finally {
                        console.log('登录结束');
                        uni.hideLoading();
                    }
                },
                500,
                true,
            );
        },
        /**
         * 授权同步获取小程序密文手机号
         * @returns {Promise<unknown>}
         */
        getPhoneNumberSync() {
            return new Promise((resolve, reject) => {
                my.getPhoneNumber({
                    success: res => {
                        resolve(JSON.parse(res.response));
                    },
                    fail: err => {
                        console.log('getPhoneNumber fail', err);
                        reject(err);
                    },
                });
            });
        },
        /**
         * 静默授权获取小程序authBaseCode
         * @returns {any|Promise<unknown>}
         */
        getAuthBaseCodeSync() {
            return new Promise((resolve, reject) => {
                my.getAuthCode({
                    scopes: ['auth_base'],
                    success: res => {
                        resolve(res.authCode);
                    },
                    fail: err => {
                        console.log('getAuthBaseCode fail', err);
                        reject(err);
                    },
                });
            });
        },
        /**
         * 获取用户登录数据
         * @param authInfo
         * @param phoneCipher
         * @returns {Promise<unknown>}
         */
        async getUserLogin(authInfo = '', phoneCipher = { response: '', sign: '' }) {
            return await userLogin({
                loginType: 10,
                authInfo,
                phoneCiphertext: phoneCipher.response,
                sign: phoneCipher.sign,
                ignoreCppeiToken: true,
            });
        },
        /**
         * 获取用户登录授权手机号
         * @param phoneCipher
         * @returns {Promise<unknown>}
         */
        async getUserPhone(phoneCipher = { response: '', sign: '' }) {
            return await userPhone({
                phoneCiphertext: phoneCipher.response,
                sign: phoneCipher.sign,
            });
        },
        /**
         * 获取用户登录手机号
         * @returns {Promise<void>}
         */
        async onAuthPhone() {
            const phoneCipher = await this.getPhoneNumberSync();
            const { success, data } = await this.getUserPhone(phoneCipher);
            if (!success) {
                throw new Error('获取用户手机号失败');
            }
            this.phone = data.phone;
        },
        /**
         * 用户登录
         * @returns {Promise<void>}
         */
        async onLogin() {
            const [authBaseCode, phoneCipher] = await Promise.all([this.getAuthBaseCodeSync(), this.getPhoneNumberSync()]);
            const { success, data } = await this.getUserLogin(authBaseCode, phoneCipher);

            if (!success) {
                this.close();
                this.rnAuthClose();
                Store.dispatch('changeFacePop', false);
                throw new Error('获取用户登录信息失败');
            }
            this.$store.commit('setNewMember', data.newMember);
            if (!data.newMember){
                let res2 = await getCppeiLoginInfo({}, { isCustomErr: true });
                if (res2.success && res2.data.cppeiLoginInfo){
                    data.cppeiLoginInfo = JSON.parse(res2.data.cppeiLoginInfo || null);
                }
            }
            // data.cppeiLoginInfo = JSON.parse(data.cppeiLoginInfo || null);
            data.postLoginActionJsonStr = JSON.parse(data.postLoginActionJsonStr || null);

            // data.postLoginActionJsonStr.rlyzFlag = true;
            if (data.postLoginActionJsonStr?.rlyzFlag) {
                this.accessToken = data.accessToken;
                const basicInfo = await basicInfoQuery({}, { accessToken: data.accessToken });

                if (!basicInfo?.success) {
                    throw new Error('获取实名信息失败');
                }
                console.log('实名认证', basicInfo);
                // 实名认证

                // basicInfo.data.identityAuthStatus = 13;
                if (Number(basicInfo?.data?.identityAuthStatus) === 13) {
                    // 打开实名认证的表单弹窗
                    this.rnAuthVisible = true;
                    const rnAuth = await new Promise((resolve, reject) => {
                        this.authPass = { resolve, reject };
                    });
                    if (!rnAuth) {
                        console.log('测试执行到这里了吗');
                        throw new Error('实名认证未通过');
                    }
                }

                // 获取用户隐私信息
                const authInfo = await identityAuthInfo({}, { accessToken: data.accessToken });
                if (!authInfo?.success) {
                    throw new Error('获取用户隐私信息失败');
                }

                await this.startRnAuth(authInfo.data);

                // 人脸识别
                Store.dispatch('changeFacePop', true);
                const rlAuth = await new Promise((resolve, reject) => {
                    this.authPass = { resolve, reject };
                });
                if (!rlAuth) {
                    this.close();
                    this.rnAuthClose();
                    Store.dispatch('changeFacePop', false);
                    throw new Error('人脸识别未通过');
                }

                // 实名实人验证
                const rrAuth = await this.startRrAuth(rlAuth);
                if (!rrAuth?.success) {
                    this.close();
                    this.rnAuthClose();
                    Store.dispatch('changeFacePop', false);
                    throw new Error('实名实人验证失败');
                }
            }
            // 升级3.0账号
            if (!data.newMember && this.autoV3) {
                console.log('升级3.0账号');
                uni.showLoading({ mask: true });
                await this.autoUpgradeV3(data.memberNo, data.gsmsToken, data.cppeiLoginInfo?.phoneToken);
                const upAuth = await new Promise((resolve, reject) => {
                    this.authPass = { resolve, reject };
                });
                uni.hideLoading();
                if (!upAuth) {
                    throw new Error('升级账号未通过');
                } else {
                    Object.assign(data, upAuth, { newMember: true });
                }
            }

            uni.setStorageSync('tokenInfo', data);
            if (data.newMember) {
                uni.setStorageSync('v3token', data.accessToken);
            }
            if (data.postLoginActionJsonStr?.longTimeNotLogin) {
                this.$store.commit('setLongTimeNotLogin', data.postLoginActionJsonStr.longTimeNotLogin);
            }
            this.$sKit.mpBP.tracker('登录/注册', {
                seed: 'loginOrRegistBiz',
                pageID: 'loginSucessToast', // 页面名
                channelID: projectConfig.clientCode, // C10/C12/C13
            });
            this.$emit('success', { v3: data.newMember });
        },
        /**
         * 关闭一键登录弹窗
         */
        close() {
            this.visible = false;
            this.$emit('onClose');
        },
        /**
         * 实名认证
         * @param val
         */
        async startRnAuth(val) {
            const { success, data } = await realNameAuth(
                {
                    realName: val.realName || val.name,
                    idType: val.identityType || 1,
                    idNo: val.identityNo || val.idNumber,
                    type: 11, // 11 风控校验
                },
                { accessToken: this.accessToken, is_error: true },
            );
            if (!success) {
                this.close();
                this.rnAuthClose();
                Store.dispatch('changeFacePop', false);
                this.authPass?.reject(false);
                return;
            }
            this.authInfo = data.authInfo;
            this.rnAuthVisible = false;
            this.authPass?.resolve(data);
        },
        /**
         * 关闭实名认证弹窗
         */
        rnAuthClose() {
            this.rnAuthVisible = false;
        },
        /**
         * 人脸识别
         */
        async startRlAuth() {
            console.log('开始人脸识别');
            let result = await initRealPersonIdentify(
                {
                    returnUrl: '/pages/union/union',
                    metaInfo: '',
                    verifyMode: '2',
                },
                { accessToken: this.accessToken },
            );
            console.log('人脸识别', result);
            if (!result?.success || !result?.data?.certifyId || !result?.data?.certifyUrl) {
                this.close();
                this.rnAuthClose();
                Store.dispatch('changeFacePop', false);
                this.authPass?.reject(false);
                return;
            }
            Store.dispatch('changeFacePop', false);
            const { verifyUnique, certifyUrl, certifyId } = result?.data || {};
            my.call('startBizService', {
                name: 'open-certify',
                param: JSON.stringify({
                    certifyId,
                    url: certifyUrl,
                }),
                success: res => {
                    if (Number(res.resultStatus) === 9000) {
                        this.authPass?.resolve({
                            verifyUnique,
                            certifyId,
                        });
                        return;
                    }
                    this.authPass?.reject(false);
                },
                fail: err => {
                    this.authPass?.reject(false);
                },
                complete: res => {
                    console.log('complete', res);
                },
            });
        },
        /**
         * 实人实名验证
         */
        startRrAuth(data) {
            let params = {
                type: 11, // 风控验证
                authInfo: this.authInfo || '',
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                verifyMode: '2', // 认证接入方式 1 APP 2 PC/H5/小程序
            };
            return realPersonIdentify(params, { accessToken: this.accessToken });
        },
        /**
         * 升级3.0账号
         */
        async autoUpgradeV3(memberNo, gsmsToken, token) {
            const { Value } = await upgradeV3(
                {
                    userid: memberNo,
                    gsmsToken,
                },
                {
                    token,
                    isload: false,
                },
            );
            if (!Value) {
                this.authPass?.reject(false);
                return;
            }
            const pollingTaskId = this.createPollingTask(async () => {
                const { Value, Data } = await upgradeV3status(
                    {
                        userid: memberNo,
                    },
                    {
                        token,
                        isload: false,
                    },
                );
                console.log(Value, Data);
                const success = Value && Data?.newMemberFlag === 1;
                if (success) {
                    const { Value, Data } = await loginToGsms(
                        {
                            auth: 'zfb',
                            gsmsUri: environment['/gsms-mpaas-' + projectConfig?.workspaceid + '/'] || '',
                        },
                        {
                            token,
                            isload: false,
                        },
                    );
                    if (!Value) {
                        this.authPass?.reject(false);
                        return;
                    }
                    this.authPass?.resolve(Data);
                }
                return success;
            }, 3000);
        },
        // 轮询任务
        createPollingTask(pollingFunction, stopCondition = 3000, intervalTime = 3000) {
            let pollingId = null;
            if (typeof stopCondition === 'number') {
                intervalTime = stopCondition;
                stopCondition = null;
            }

            const poll = async () => {
                try {
                    const result = await pollingFunction();

                    if (result === true || (stopCondition && (await stopCondition(result)))) {
                        clearInterval(pollingId);
                    }
                } catch (error) {
                    console.error('轮询过程中发生错误:', error);
                }
            };

            poll();

            pollingId = setInterval(async () => {
                await poll();
            }, intervalTime);

            return pollingId;
        },
    },
};
</script>
<style lang="scss" scoped>
.layer-btn {
    background: none;
    border: none;
    display: initial;

    &:active {
        background: none;
        border: none;
        opacity: 1;
    }
}

.dialog-login {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;

    &-main {
        background: rgba(0, 0, 0, 0);
        position: absolute;
        left: 50%;
        top: 50%;
        width: 80%;
        transform: translate(-50%, -50%);
    }

    &-content {
        border-radius: 16rpx;
        background: #fff;
        padding: 40rpx 24rpx;
        text-align: center;

        .title {
            font-size: 36rpx;
            color: #333;
            font-weight: bold;
        }

        .subtitle {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 6rpx;
        }

        .center {
            background: #f5f5f5;
            border-radius: 8rpx;
            padding: 40rpx;
            margin: 40rpx 0;
        }

        .phone {
            font-size: 50rpx;
            color: #333;
            font-weight: bold;
        }

        .btn {
            background: #f96702;
            border-radius: 8rpx;
            color: #fff;
            font-size: 36rpx;
            line-height: 2.4;

            &:active {
                opacity: 0.7;
            }
        }

        .changePhone {
            font-size: 28rpx;
            color: #4b6b99;
        }
    }

    .dialog-close {
        z-index: 9999;
        width: 60rpx;
        height: 60rpx;
        margin: 40rpx auto;
        background: url('https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/phoneAlert/round_close.png');
        background-size: 60rpx 60rpx;
    }
}
</style>
