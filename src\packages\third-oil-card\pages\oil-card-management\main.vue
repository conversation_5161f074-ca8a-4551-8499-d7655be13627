<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="remaining_div fl-column bg-f2" :claas="{ 'bg-none': isHarmony }">
            <div class="remaining_div_top bg-fff">
                <div class="card_bg" v-if="cardInfoDetail.imgUrl">
                    <img class="card_bg_img" :src="cardInfoDetail.imgUrl" alt mode="widthFix" />
                    <img class="mask_img" :src="cardInfoDetail.imgUrl" />
                </div>
                <div class="card_bg" v-else>
                    <img class="card_bg_img" src="../../images/card-default.png" alt />
                    <img class="mask_img" src="../../images/mask2.png" />
                </div>
                <zj-navbar
                    :background="{ background: 'rgba(255, 255, 255, 0)' }"
                    titleColor="#fff"
                    title="实体卡"
                    :border-bottom="false"
                ></zj-navbar>
            </div>
            <!-- <div class="bg-fff null_div"></div> -->
            <div class="scroll_div" :style="{ height: topNavbarHeight + 'px' }"></div>
            <div class="f-1 mh-0 fl-column">
                <zj-pull-down-refresh class="f-1" style="z-index: 10" @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="content_div fl-column mh-0">
                        <div class="card_top">
                            <div class="card_div">
                                <div class="card_div_top">
                                    <img class="card_img" src="../../images/card-default.png" alt mode="widthFix" />
                                    <div class="cardInfo fl-row fl-jus-bet fl-al-cen">
                                        <div class="cardNum font-16 weight-bold color-fff">
                                            <!-- <div>卡号:</div> -->
                                            <div class="cardNumNo">{{ thirdCardItem.cardNo }}</div>
                                        </div>
                                        <div class="address font-12 weight-400 color-fff">{{ thirdCardItem.address || '' }}</div>
                                    </div>
                                </div>
                                <div class="div_content">
                                    <div class="card_info bg-fff">
                                        <div class="card_info_top fl-row fl-jus-bet fl-al-cen">
                                            <span class="card_name font-18 color-333 weight-bold">实体卡</span>
                                            <div class="fl-row fl-al-cen">
                                                <div @click="toEenjoyCard()" class="frozen_amount font-14 color-FF6B2C">
                                                    转出到昆仑e享卡
                                                </div>
                                                <img src="../../images/light-right.png" alt />
                                            </div>
                                        </div>
                                        <div class="card_info_bot fl-row fl-jus-bet fl-al-cen">
                                            <div class="fl-column fl-wrap">
                                                <span class="font-15 color-999 title">卡余额(元)</span>
                                                <span class="font-20 color-333 amount font-style">
                                                    {{ cardInfoDetail ? regexHandleNum(cardInfoDetail.balance) : '' }}
                                                </span>
                                            </div>
                                            <!-- #ifndef H5-CLOUD -->
                                            <div @click="entityCardRecharge" class="recharge_btn font-15">充值</div>
                                            <!-- #endif -->
                                        </div>
                                        <div class="card_info_bot fl-row fl-jus-bet f-1">
                                            <div class="fl-column fl-al-sta">
                                                <div class="card_info_text font-15 color-999 weight-400">备用金</div>
                                                <div class="card_info_num font-20 color-333 font-style">
                                                    {{ cardInfoDetail ? regexHandleNum(cardInfoDetail.cash) : '' }}
                                                </div>
                                            </div>
                                            <div class="fl-column fl-al-sta">
                                                <div class="card_info_text font-15 color-999 weight-400">卡积分</div>
                                                <div class="card_info_num font-20 color-333 font-style">
                                                    {{ cardInfoDetail ? regexHandleNum(cardInfoDetail.pointBalance) : '' }}
                                                </div>
                                            </div>
                                            <div class="fl-column fl-al-sta">
                                                <div class="card_info_text font-15 color-999 weight-400">备用金积分</div>
                                                <div class="card_info_num font-20 color-333 font-style">
                                                    {{ cardInfoDetail ? regexHandleNum(cardInfoDetail.credit) : '' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="content_bottom_wrap padding-16">
                                        <div class="content_bottom bg-fff fl-column">
                                            <!-- #ifndef H5-CLOUD -->
                                            <div class="content_item fl-row fl-jus-bet fl-al-cen" @click="fuelCardJump('czjl')">
                                                <div class="fl-row fl-al-cen">
                                                    <img src="../../images/recharge_record.png" alt />
                                                    <div class="content_item_text">本卡充值记录</div>
                                                </div>
                                                <div class="arroe-right-small"></div>
                                            </div>
                                            <!-- #endif -->
                                            <div class="line_bottom"></div>
                                            <div class="content_item fl-row fl-jus-bet fl-al-cen" @click="fuelCardJump('xfjl')">
                                                <div class="fl-row fl-al-cen">
                                                    <img src="../../images/records_consumption.png" alt />
                                                    <div class="content_item_text">本卡消费记录</div>
                                                </div>
                                                <div class="arroe-right-small"></div>
                                            </div>
                                            <div class="line_bottom"></div>
                                            <div class="content_item fl-row fl-jus-bet fl-al-cen">
                                                <div class="fl-row">
                                                    <div class="content_item_text2"
                                                        >本卡归属地 <div class="address">{{ thirdCardItem.address }}</div></div
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="content_bottom_wrap p-LR-16">
                                        <div class="content_bottom bg-fff fl-column">
                                            <div class="content_item fl-row fl-jus-bet fl-al-cen" @click="ImputationJump('zjgj')">
                                                <div class="fl-row fl-al-cen">
                                                    <div class="content_item_text">未绑定加油卡资金归集</div>
                                                </div>
                                                <div class="arroe-right-small"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
            <!-- #ifdef MP-MPAAS  -->
            <div v-if="isHarmony">
                <keyboard-plugin></keyboard-plugin>
            </div>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <cloudKeyBordPlugin ref="cloudKeyboardRef"></cloudKeyBordPlugin>
            <!-- #endif -->

            <div class="btn-plain-wrap">
                <div class="btn-plain border-rad-8 color-E64F22" @click="unbindCard">解除绑定</div>
            </div>
            <zj-show-modal>
                <FundTransferTut v-if="!modelRemindFlag" :cardDetailsObj="cardInfoDetail" ref="FundTransferTut"></FundTransferTut>
                <fundCollectionRemind v-if="modelRemindFlag" ref="fundCollectionRemind"></fundCollectionRemind>
            </zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { bindCardInfo, unBindCard, rollout } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import FundTransferTut from '../../components/fund-transfer-out/main.vue';
import fundCollectionRemind from '../../components/fundCollectionRemind/main.vue';

// #ifdef MP-MPAAS
import appOilCardManagement from './diff-environment/app-oil-card-management.js';
// #endif

// #ifndef MP-MPAAS || H5-CLOUD
import wxOilCardManagement from './diff-environment/wx-oil-card-management.js';
import zfbOilCardManagement from './diff-environment/zfb-oil-card-management';
// #endif
// #ifdef H5-CLOUD
import cloudOilCardManagement from './diff-environment/cloud-oil-card-management.js';
import cloudKeyBordPlugin from '../../../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue';
// #endif
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    components: {
        FundTransferTut,
        fundCollectionRemind,
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin,
        // #endif
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appOilCardManagement,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxOilCardManagement,
        zfbOilCardManagement,
        // #endif
        // #ifdef H5-CLOUD
        cloudOilCardManagement,
        // #endif
    ],
    name: 'oil-card-management',
    data() {
        // const currentDate = this.getDate({
        //   format: true,
        // });
        return {
            // 获取系统信息，
            systemInfo: '',
            // 顶部Navbar高度
            topNavbarHeight: '',
            // 状态栏高度
            statusBarHeight: '',
            // 当前实体卡信息对象
            thirdCardItem: {},
            // 油卡备用金/余额/卡积分/备用金积分对象
            cardInfoDetail: {},
            // uni.navigateBack 需要的参数
            prveDataObject: {
                refreshListFlag: false,
            },
            // 密码键盘初始化实例的对象
            passwordKeyboardRef: {},
            // 密码长度
            passwordlength: '',
            // 解绑时输入的密码
            password: '',
            // 控制资金归集提示框展出
            modelRemindFlag: false,
            passWordOpenVal: {},
        };
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    created() {
        // 获取系统宽高信息，根据公式转化为rpx
        this.bgHrightStyle();
    },
    onShow() {
        // 读取本地缓存的数据，当value为true时，用来调用油卡管理数据
        const value = uni.getStorageSync('refreshCardManagement');
        // 移除本地缓存
        uni.removeStorageSync('refreshCardManagement');
        if (value) {
            // 获取已绑定的加油卡信息(备用金/余额/卡积分/备用金积分)
            this.getBindCardInfo();
        }
    },
    onLoad(options) {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$nextTick(async () => {
                let result = await this.$sKit.keyBordPlugin.initRef();
                this.$store.commit('setAccountDataPlugin', result);
                this.passwordKeyboardRef = result;
            });
        }
        // #endif
        // #ifdef MP-WEIXIN
        let result = this.selectComponent('#passwordKeyboardId');
        this.$sKit.keyBordPlugin.initRef(result);
        this.passwordKeyboardRef = result;
        console.log(this.passwordKeyboardRef, '微信油卡管理页面键盘初始化实例');
        // #endif
        // #ifdef MP-ALIPAY
        this.$nextTick(() => {
            let result = this.$refs['handlePasswordKeyboardRef'];
            console.log('keybord初始化', result);
            this.$sKit.keyBordPlugin.initRef(result);
            this.passwordKeyboardRef = this.$refs['handlePasswordKeyboardRef'];
            console.log(this.passwordKeyboardRef, '支付宝油卡管理页面键盘初始化实例');
        });
        // #endif
        // #ifdef H5-CLOUD
        this.$nextTick(async () => {
            // 获取键盘的实例
            let result = await this.$refs.cloudKeyboardRef.initRef();
            // console.log('result',result)
            this.$store.commit('setAccountDataPlugin', result);
            this.passwordKeyboardRef = result;
        });
        // #endif

        // 上个页面点击油卡携带到油卡管理的卡信息
        this.thirdCardItem = JSON.parse(decodeURIComponent(options.data));
        console.log(this.thirdCardItem, 'thirdCardItem');
        // 获取已绑定的加油卡信息(备用金/余额/卡积分/备用金积分)
        this.getBindCardInfo();
    },
    methods: {
        /**
         * @description  : 将一个数字转换为千位分隔符格式的字符串。
         * @return        {*}
         */
        regexHandleNum(num) {
            let str = String(num || 0);
            return str.replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 3是千分位，4是万分位
        },
        /**
         * @description  : 获取系统信息
         * @return        {*}
         */
        bgHrightStyle() {
            // 获取系统宽高信息，根据公式转化为rpx
            let systemInfo = uni.getSystemInfoSync();
            // #ifdef MP-ALIPAY
            this.topNavbarHeight = systemInfo.statusBarHeight + systemInfo.titleBarHeight;
            // #endif
            this.topNavbarHeight = systemInfo.statusBarHeight + 44;
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            // 获取当前绑定卡的信息
            this.getBindCardInfo();
        },
        /**
         * @description  : 获取油卡备用金/余额/卡积分/备用金积分
         * @param        {String}cardNo:卡号
         * @return       {*}
         */
        async getBindCardInfo() {
            // this.prveDataObject.refreshListFlag;
            let params = {
                cardNo: this.thirdCardItem.cardNo,
            };
            let res = await bindCardInfo(params);
            if (res.success) {
                // 油卡备用金/余额/卡积分/备用金积分对象
                this.cardInfoDetail = res.data;
                this.cardInfoDetail.cardNo = this.thirdCardItem.cardNo;
                // 停止下拉刷新
                this.$refs.pullDownRefreshRef.stopRefresh();
            }
        },
        /**
         * @description  : 资金转出
         * @param         {Function} zjShowModal -全局报错弹窗
         * @return        {*}
         */
        toEenjoyCard() {
            this.modelRemindFlag = false;
            if (Number(this.cardInfoDetail.cash) == 0 && Number(this.cardInfoDetail.credit) == 0) {
                // this.$dialog.alert({ message: '暂无可归集的资金' }).then(() => {
                this.$store.dispatch('zjShowModal', {
                    confirmText: '确认',
                    cancelColor: '#666',
                    content: '暂无可归集的资金',
                    success: res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                        }
                    },
                });
            } else {
                let _this = this;
                this.$store.dispatch('zjShowModal', {
                    confirmText: '允许',
                    cancelText: '不允许',
                    confirmColor: '#E64F22',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            this.$store.dispatch('zjShowModal', {
                                confirmText: '允许',
                                cancelText: '不允许',
                                confirmColor: '#E64F22',
                                cancelColor: '#666',
                                content: '确认执行此操作吗？',
                                success: res => {
                                    if (res.confirm) {
                                        console.log('用户点击确定');
                                        // 确认进行资金转出
                                        this.fundTransferMethod();
                                    } else if (res.cancel) {
                                        console.log('用户点击取消');
                                    }
                                },
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        /**
         * @description  : 实体卡充值
         * @return        {*}
         */
        entityCardRecharge() {
            console.log('触发充值');
            let url = '/packages/third-oil-card/pages/oil-card-recharge/main';
            let params = { ...this.thirdCardItem, refer: 'r21' };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 跳转资金归集页面
         * @param         {*} val:跳转某个页面的参数
         * @return        {*}
         */
        ImputationJump(val) {
            let _this = this;
            _this.modelRemindFlag = true;
            this.$store.dispatch('zjShowModal', {
                confirmText: '确定',
                cancelText: '取消',
                confirmColor: '#E64F22',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let url = '/packages/third-oil-card/pages/oil-card-cashsweep/main';
                        let params = {};
                        let type = 'navigateTo';
                        this.$sKit.layer.useRouter(url, params, type);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        /**
         * @description  : 跳转充值记录或消费记录
         * @param         {*} val:跳转某个页面的参数
         * @return        {*}
         */
        fuelCardJump(val) {
            console.log(val, '跳转充值记录或消费记录');
            let url = '';
            if (val == 'czjl') {
                url = '/packages/third-order/pages/home/<USER>';
            } else if (val == 'xfjl') {
                url = '/packages/third-order/pages/home/<USER>';
            }
            if (JSON.stringify(this.thirdCardItem) !== '{}') {
                let params = {
                    navActive: val == 'czjl' ? 2 : 1, // 1是消费 2是充值
                    secondNavActive: 4,
                    refuelCardAccountNo: this.thirdCardItem.cardNo, // 卡号
                    refer: 'r49',
                };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        /**
         * @description  : 解除绑定
         * @param         {*}cardNo:卡号
         * @param         {*}password:密码
         * @return        {*}
         */
        unbindCard() {
            let params = {
                cardNo: this.thirdCardItem.cardNo,
            };

            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                confirmColor: '#E64F22',
                cancelColor: '#666',
                content: '确认执行此操作吗？',
                success: res => {
                    if (res.confirm) {
                        console.log('油卡解绑用户点击确定');
                        unBindCard(params).then(res => {
                            if (res.success) {
                                uni.showToast({
                                    title: '解绑成功',
                                    icon: 'none',
                                    duration: 2000,
                                });
                                uni.setStorageSync('refreshCardListFlag', true);
                                //  改成同步存储试试
                                // uni.setStorage('refreshCardListFlag', true)
                                this.goBack();
                            }
                        });
                    } else if (res.cancel) {
                        console.log('油卡解绑用户点击取消');
                    }
                },
            });
        },
        /**
         * @description  : 返回首页和上一个页面
         * @return        {*}
         */
        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                uni.reLaunch({
                    url: '/pages/thirdHome/main',
                });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;
    display: flex;

    // overflow: hidden;
    .remaining_div_top {
        height: 255px;
        width: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;

        .card_bg {
            // overflow: hidden;

            .card_bg_img {
                height: 255px;
                width: 140%;
                height: 165%;
                -webkit-filter: blur(28.5px);
                filter: blur(28.5px);
                position: absolute;
                top: -50px;
                left: -90px;
            }

            .mask_img {
                width: 100%;
                height: 100%;
                position: relative;
                top: 0;
            }
        }
    }

    .content_div {
        flex: 1;
        position: absolute;
        width: 100%;
        z-index: 10;

        // bottom: 10px;
        // background: #f2f3f5;
        .card_top {
            .card_div {
                padding-top: 15px;
                position: relative;

                .card_div_top {
                    display: flex;
                    width: 100%;
                    padding: 18px 18px 0;
                    position: absolute;
                    z-index: 2;

                    .card_img {
                        width: 100%;
                        height: 412rpx;
                        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
                        border-radius: 8px 8px 8px 8px;
                    }

                    .card_code {
                        width: 32px;
                        height: 32px;
                        position: absolute;
                        bottom: 16px;
                        right: 20px;
                    }

                    .cardInfo {
                        height: 44px;
                        background: rgba(0, 0, 0, 0.2);
                        border-radius: 0px 0px 8px 8px;
                        backdrop-filter: blur(10px);
                        position: absolute;
                        bottom: 0;
                        left: 18px;
                        right: 18px;

                        .cardNum {
                            margin-left: 15px;
                            display: flex;

                            .cardNumNo {
                                // margin-top: 4px;
                                height: 17px;
                            }
                        }

                        .address {
                            // height: 17px;
                            margin-right: 15px;
                            max-width: 270rpx;
                            margin-right: 5px;
                            // margin-top: 3px;
                        }
                    }
                }

                .card_info {
                    padding: 150px 32px 19px 32px;

                    .card_info_top {
                        .card_name {
                            line-height: 25px;
                        }

                        .frozen_amount {
                            line-height: 20px;
                        }

                        img {
                            width: 14px;
                            height: 14px;
                            line-height: 20px;
                        }
                    }

                    .card_info_bot {
                        margin-top: 12px;

                        .card_info_text {
                            line-height: 22px;
                        }

                        .card_info_num {
                            line-height: 28px;
                        }

                        div {
                            .title {
                                line-height: 21px;
                            }

                            .amount {
                                line-height: 28px;
                                margin-top: 3px;
                            }
                        }

                        .recharge_btn {
                            width: 78px;
                            height: 37px;
                            border-radius: 200px;
                            border: 1px solid #ff6b2c;
                            color: #ff6b2c;
                            text-align: center;
                            line-height: 37px;
                        }
                    }
                }

                .content_bottom_wrap {
                    background: #f2f3f5;

                    .content_bottom {
                        width: 100%;
                        border-radius: 16rpx;

                        .content_item {
                            width: 100%;
                            min-height: 44px;
                            padding: 22rpx 0;

                            .content_item_text {
                                font-size: 30rpx;
                                color: #000000;
                                line-height: 42rpx;
                                margin-left: 17px;
                            }

                            .content_item_text2 {
                                font-size: 30rpx;
                                color: #000000;
                                line-height: 42rpx;
                                margin-left: 41rpx;
                                display: flex;
                                align-items: baseline;

                                .address {
                                    font-size: 24rpx;
                                    color: #333333;
                                    line-height: 42rpx;
                                    margin-right: 19px;
                                    margin-left: 8px;
                                }
                            }

                            img {
                                width: 24px;
                                height: 24px;
                                margin-left: 16px;
                            }

                            .arroe-right-small {
                                margin-right: 19px;
                            }
                        }
                    }
                }
            }
        }
    }

    .btn-plain-wrap {
        background-image: #fff;
        padding: 0 16px;

        .btn-plain {
            width: 100%;
            // position: absolute;
            height: 44px;
            line-height: 44px;
            // bottom: 0px;
            margin: 10px 0;
        }
    }
}

.div_bot {
    padding: 32px 0 22px 0;

    span {
        text-align: center;
    }
}

.bg-f2 {
    background: #f2f3f5;
}

// .null_div {
//     width: 100%;
//     height: 200px;
//     position: absolute;
//     top: 510rpx;
// }

.div_content {
    width: 100%;
    position: absolute;
    top: 116px;
}
</style>
