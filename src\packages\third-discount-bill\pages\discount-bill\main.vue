<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <div class="pageMpaas">
        <div class="remaining_div fl-column">
            <div class="remaining_div_top">
                <div class="card_bg">
                    <img class="mask_img" src="../../image/car-bg.png" />
                </div>
                <zj-navbar :background="{ background: 'rgba(255, 255, 255, 0)' }" title="我的优惠账单" :border-bottom="false"> </zj-navbar>
            </div>
            <div class="scroll_div" :style="{ height: topNavbarHeight + 'px' }"></div>
            <div class="content_div fl-column">
                <div class="card_top">
                    <div class="card_div">
                        <div class="card_div_top">
                            <img class="card_img" src="../../image/header-car.png" alt />
                            <div class="cardInfo p-bf fl-column fl-al-cen">
                                <!-- 会员头像 -->
                                <div class="card_header_img">
                                    <img class="p-bf" src="../../image/header.png" alt />
                                </div>
                                <!-- 昵称 -->
                                <div class="card_header_text font-15 weight-600">{{ memberData.alias || '会员名称' }}</div>
                                <!-- 注册天数 -->
                                <div class="card_day font-10">今天是我们在一起的第{{ daysDateCount.upgradeDayCount }}天哦</div>
                                <!-- 累计已省金额 -->
                                <div class="font-14 card_amountLab">
                                    <img src="../../image/bg-amount.png" alt="" class="card_bgimg" />
                                    <span style="position: relative; z-index: 99" class="color-fff font-12">累计已省</span>
                                </div>
                                <div class="font-28 color-333 weight-600"><span class="font-14">￥</span>{{ totalDiscounts }}</div>
                                <!-- 升级3.0会员的日期到当前日期 -->
                                <div class="font-11 card_date">{{
                                    `${formatTimeFun(daysDateCount.upgradeDate, true)}  -  ${nowDateT}`
                                }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card_charts">
                    <div class="card_charts_box">
                        <div class="canvas_text te-left font-12 color-333 weight-bold">
                            月度已省金额对比
                            <img src="../../image/tips.png" alt="" class="tipsImg" />
                        </div>
                        <div class="card_canvas_box" v-if="emptyCharts">
                            <canvas
                                canvas-id="afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL"
                                id="afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL"
                                :width="cWidth * pixelRatio"
                                :height="cHeight * pixelRatio"
                                class="charts"
                                @touchstart="touchstart"
                                @touchmove="touchmove"
                                @touchend="touchend"
                            ></canvas>
                        </div>
                        <div v-else class="kt-box">
                            <img src="../../image/ktyhzd-charts.png" alt="" class="charts_ktimg" />
                            <div class="chart-kt-text">最近6个月无交易记录</div>
                        </div>
                    </div>
                </div> 
            </div>
            <div class="list_search">
                <div class="list_box bg-fff">
                    <div class="list_header bg-fff">
                        <div class="list_header-left font-12">
                            <div class="dateTime" @click="dateHandle">{{ incomeDate }}</div>
                            <div class="arrow-down-333" @click="dateHandle" style="margin-left: 5px; margin-top: -2px"></div>
                        </div>
                        <div class="list_header-right font-12 weight-bold">
                            <span>已省：</span>
                            <span class="font-14"><span class="font-12">&yen;</span>{{ MonthMothtotalDiscounts }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="f-1 mh-0 bg-F7F7FB list_data">
                <div class="list_data_box">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="dataList"
                        emptyText="暂无优惠账单"
                        :showEmpty="showEmpty"
                        :emptyImage="require('../../image/kt4dd.png')"
                        :widthImg="150"
                        :heightImg="125"
                        @scrolltolower="scrolltolower"
                        @refreshPullDown="refreshPullDown"
                    >
                        <div class="list_data_li" v-for="(item, index) in preferentialArray" :key="index" @click="orderDetailsCar(item)">
                            <div class="list_data_top">
                                <div class="fl-row">
                                    <template v-if="item.productInfoList.length === 1">
                                        <div v-for="(item1, index1) in item.productInfoList" :key="index1" class="fl-row">
                                            <div v-if="item1.isOilProduct" class="commodity">
                                                <img class="p-bf" src="../../image/order_oils.png" alt />
                                            </div>
                                            <div v-else class="commodity">
                                                <!--当前是一个非油，修改只展示固定非油-->
                                                <img class="p-bf" src="../../image/order_oil-1.png" alt />
                                            </div>
                                            <div class="font-12 weight-bold fl-row fl-al-cen title">{{ item1.productName }}</div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <!-- 只有油品，没有非油，展示两个或一个油品 -->
                                        <div v-if="item.oilsAry.length > 0 && item.nonOilAry.length === 0" class="fl-row">
                                            <div v-for="(itemOil, indexOil) in item.oilsAry.slice(0, 2)" :key="indexOil">
                                                <div class="commodity">
                                                    <img class="p-bf" src="../../image/order_oils.png" alt />
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 只有非油，没有油品，展示前两位 -->
                                        <div v-if="item.nonOilAry.length > 0 && item.oilsAry.length === 0" class="fl-row">
                                            <div v-for="(itemNoOil, indexNoOil) in item.nonOilAry.slice(0, 2)" :key="indexNoOil">
                                                <div class="commodity">
                                                    <img v-if="indexNoOil == 0" class="p-bf" src="../../image/order_oil-1.png" alt />
                                                    <img v-if="indexNoOil == 1" class="p-bf" src="../../image/order_oil-2.png" alt />
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 既有非油，又有油品 -->
                                        <div v-if="item.nonOilAry.length > 0 && item.oilsAry.length > 0" class="fl-row">
                                            <div v-for="(itemOil, indexOil) in item.oilsAry.slice(0, 1)" :key="indexOil">
                                                <div class="commodity">
                                                    <img class="p-bf" src="../../image/order_oils.png" alt />
                                                </div>
                                            </div>
                                            <div v-for="(itemNoOil, indexNoOil) in item.nonOilAry.slice(0, 1)" :key="indexNoOil">
                                                <div class="commodity">
                                                    <!--当前是一个非油，修改只展示固定非油-->
                                                    <img class="p-bf" src="../../image/order_oil-1.png" alt />
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <div class="font-12 fl-column fl-al-end">
                                    <div class="color-333">&yen;{{ item.actualPayTotalAmount }}</div>
                                    <div class="color-999 mar-top-8">{{ formatTimeFun(item.businessDay) }}</div>
                                </div>
                            </div>
                            <div class="list_data_bottom bg-F7F7FB fl-row font-12">
                                <div class="fl-row list_data_bottom_right">
                                    <div
                                        v-for="(items, indexs) in item.setData"
                                        :key="indexs"
                                        class="list_data_bottom_right_box fl-column"
                                        :class="{}"
                                    >
                                        <div class="fl-column fl-al-jus-cen">
                                            <span class="color-999">{{ items.name }}</span>
                                            <span class="color-333">&yen;{{ items.value }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="list_data_bottom_left">
                                    <div class="fl-column fl-al-jus-cen list_data_bottom_left_box">
                                        <span class="color-333">合计已省</span>
                                        <span style="color: #e64f22">&yen;{{ item.totalDiscountAmount }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <SetSelectDate
                v-if="dateShow1"
                @hideShow="hideShow"
                @sureSelectDateTime="incomebindDateChange"
                :dateYearsOrMonth="dateYearsOrMonth"
                :inDate="inDate"
            ></SetSelectDate>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import SetSelectDate from '../../../../s-kit/components/layout/zj-setSelectDate/zj-setSelectDate.vue';
// import uCharts from '@qiun/ucharts';
import uCharts from '../../../../s-kit/js/u-charts.min';
import { basicInfoQuery } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import {
    getMemberUpgradedDayCount,
    getTotalDiscounts,
    getDiscountsAllMonthsNew,
    getDsicountsByMonth,
} from '../../../../s-kit/js/v3-http/https3/order/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        const incomecurrentDate = this.getDate({
            format: 1,
        });
        const incomecurrentDates = this.getDate({
            format: 0,
        });
        const nowDate = this.getDate({
            format: 2,
        });
        const nowDate1 = this.getDate({
            format: 3,
        });
        return {
            // 当前选中时间的累计金额
            MonthMothtotalDiscounts: '0.00',
            // 下拉展示时间
            incomeDate: incomecurrentDate,
            // 当前选中的时间
            inDate: incomecurrentDates,
            nowDateT: nowDate,
            nowDates: nowDate1,
            // 累计已省总金额
            totalDiscounts: '0.00',
            // 3.0会员升级天数和日期
            daysDateCount: {},
            // 当前用户个人信息
            memberData: {},
            //loading 只加载一次
            isload: true,
            // 控制日期选择框是否展示
            dateShow1: false,
            // 获取系统宽高信息
            topNavbarHeight: '',
            uChartsInstance: {},
            // canvas的宽度
            cWidth: 0,
            // canvas的高度
            cHeight: 0,
            // 设备像素比
            pixelRatio: 1,
            // 是否展示空态标识
            showEmpty: false,
            // 优惠账单数组
            preferentialArray: [],
            // 页码
            page: 1,
            // 排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值）
            sortType: 'distTime',
            // 排序方式：1-升序2-降序3-默认排序
            orderWay: '3',
            // 跳转订单详情的订单详情数据
            routerParams: {},
            //柱状图数据
            dataCanves: [],
            dateYearsOrMonth: [],
            emptyCharts: true,
            refer: '',
            HarmonyWidthShow: false
        };
    },
    onReady() {
        let that = this;
        // #ifdef MP-WEIXIN || H5-CLOUD
        uni.createSelectorQuery()
            .in(this)
            .select('.card_canvas_box')
            .boundingClientRect(data => {
                that.cWidth = data.width;
                that.cHeight = data.height;
                that.getServerData();
            })
            .exec();
        // #endif
        // #ifndef MP-WEIXIN || H5-CLOUD
        //这里的第一个 750 对应 css .charts 的 width1
        my.createSelectorQuery()
            .select('.card_canvas_box')
            .boundingClientRect()
            .exec(res => {
                that.cWidth = (res[0].width / res[0].width) * my.getSystemInfoSync().windowWidth;
                //这里的 500 对应 css .charts 的 height
                that.cHeight = (res[0].height / res[0].width) * my.getSystemInfoSync().windowWidth;
                that.pixelRatio = my.getSystemInfoSync().pixelRatio;
                that.isExpand(my.getSystemInfoSync().windowWidth)
                that.getServerData();                
            });
        // #endif
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.refer = params.myRefer;
            }
        }
        this.$sKit.mpBP.tracker('优惠账单', {
            seed: 'billBiz',
            pageID: 'myBillPage',
            refer: this.refer,
            channelID: clientCode,
        });
    },
    onShow() {},
    mounted() {
        // 获取系统信息
        this.bgHrightStyle();
        // 获取用户个人信息
        this.getBasicInfoQuery();
        // 获取会员升级天数和日期
        this.getDayCount();
        // 获取累计已省总金额
        this.getCumulativeAmount();
        // 获取优惠账单列表
        this.getPreferentialList();
    },
    methods: {
        /**
         * @description  : 判断鸿蒙系统是否是展开屏幕
         * @param         {Object} widthData:当前屏幕宽度
         * @return        {*}
         */
        isExpand(widthData) {
            if (widthData >= 700) {
                this.HarmonyWidthShow = true
            } else {
                this.HarmonyWidthShow = false
            }
        },
        resizeCallback(e) {
            let that = this;
            // #ifndef MP-WEIXIN || H5-CLOUD
            //这里的第一个 750 对应 css .charts 的 width1
            my.createSelectorQuery()
                .select('.card_canvas_box')
                .boundingClientRect()
                .exec(res => {
                    that.cWidth = (res[0].width / res[0].width) * my.getSystemInfoSync().windowWidth;
                    //这里的 500 对应 css .charts 的 height
                    that.cHeight = (res[0].height / res[0].width) * my.getSystemInfoSync().windowWidth;
                    that.pixelRatio = my.getSystemInfoSync().pixelRatio;
                    that.isExpand(e.detail.size.windowWidth)
                    that.getServerData();                    
                });
            // #endif
        },
        dateHandle() {
            this.dateShow1 = true;
            this.$sKit.mpBP.tracker('优惠账单', {
                seed: 'billBiz',
                pageID: 'dateChooseBut',
                refer: this.refer,
                channelID: clientCode,
            });
        },
        getYearsAndMonths() {
            let sdata = this.daysDateCount.upgradeDate.split('-');
            let edata = this.nowDates.split('-');
            let start = sdata[0] + '-' + sdata[1] + '-' + '1';
            let end = edata[0] + '-' + edata[1] + '-' + '1';
            let startArr = start.split('-');
            let startYear = parseInt(startArr[0]);
            let startMonth = parseInt(startArr[1]);
            let newDates = new Date(start.replace(/-/g, '/'));
            let endDates = new Date(end.replace(/-/g, '/'));
            let result = [];
            while (newDates.getTime() <= endDates.getTime()) {
                result.push({ year: startYear, month: startMonth });
                if (startMonth === 12) {
                    startMonth = 0;
                    startYear++;
                }
                startMonth++;
                newDates = new Date(`${startYear}-${startMonth}-1`.replace(/-/g, '/'));
            }
            return result;
        },
        /**
         * @description : 获取时间
         * @return        {*}
         */
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            if (type === 'start') {
                month = month - 2;
                if (month <= 0) {
                    month = 12 + month;
                    year = year - 1;
                }
            }

            day = day > 9 ? day : '0' + day;
            month = month > 9 ? month : '0' + month;
            if (type.format == 1) {
                return `${year}年${month}月`;
            } else if (type.format == 2) {
                return (this.nowDateT = `${year}年${month}月${day}日`);
            } else if (type.format == 3) {
                return `${year}-${month}-${day}`;
            } else {
                return `${year}-${month}`;
            }
        },
        // 处理时间只展示年月日
        formatTimeFun(time, type = false) {
            if (!time) {
                return;
            }
            if (type == false) {
                let date = time.split(' ')[0];
                return date;
            } else {
                let date = time.split(' ')[0];
                date = `${date.split('-')[0]}年${date.split('-')[1]}月${date.split('-')[2]}日`;
                return date;
            }
        },
        // 处理时间只展示年月日
        /**
         * @description  : 优惠账单已省总金额
         * @return        {*}
         */
        async getCumulativeAmount() {
            let res = await getTotalDiscounts();
            if (res.success) {
                this.totalDiscounts = res.data;
            }
        },
        /**
         * @description : 获取会员升级天数和日期
         * @return        {*}
         */
        async getDayCount() {
            let res = await getMemberUpgradedDayCount();
            if (res.success) {
                this.daysDateCount = res.data;
                this.dateYearsOrMonth = this.getYearsAndMonths();
            }
        },
        /**
         * @description : 查询用户基本信息
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery({ isload: this.isload });
            if (res.success) {
                if (this.memberData == res.data) return;
                this.memberData = res.data;
            }
        },
        /**
         * @description  : 计算路由高度
         * @return        {*}
         */
        bgHrightStyle() {
            // 获取系统宽高信息，根据公式转化为rpx

            let systemInfo = uni.getSystemInfoSync();
            // #ifdef MP-ALIPAY
            this.topNavbarHeight = systemInfo.statusBarHeight + systemInfo.titleBarHeight;
            // #endif
            // #ifndef MP-ALIPAY || H5-CLOUD
            this.topNavbarHeight = systemInfo.statusBarHeight + 44;
            // #endif
            // #ifdef H5-CLOUD
            let systemBar = getApp().globalData.systemBar;
            // 获取系统状态栏的高度
            this.topNavbarHeight = Number(systemBar) + Number(44);
            // #endif
        },
        /**
         * @description  : 上拉加载
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getPreferentialList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            // 重置数据获取优惠账单列表
            this.getPreferentialList({ isInit: true });
        },
        /**
         * @description  : 获取优惠账单列表
         * @param         {*} isInit 等于true的时候重置数据
         * @param         {*} preferentialArray:优惠账单数组
         * @param         {*} page:页码
         * @param         {*} totalPage:返回数据的条数
         * @param         {*} pageSize:每页10条
         * @param         {*} sort:排序字段：
         * @param         {*} order:排序方式：1-升序2-降序3-默认排序
         * @return        {*}
         */
        async getPreferentialList({ isInit = false } = {}) {
            // 重置页码
            if (isInit) {
                this.preferentialArray = [];
                this.page = 1;
                this.$refs.dataList.loadStatus = 'loading';
            }
            let params = {
                month: this.inDate,
                pageNum: this.page,
                pageSize: PAGE_SIZE,
                // sort: this.sortType, //排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
                // order: this.orderWay, //排序方式：1-升序2-降序3-默认排序
            };
            // 优惠账单展示顺序：优惠券couponDiscountAmount，支付折扣payDiscountAmount，积分discountPointAmount，活动促销activityDiscountAmount, 其他优惠otherDiscountAmount
            let res = await getDsicountsByMonth(params);
            this.$refs.dataList.stopRefresh();
            if (res.success) {
                this.MonthMothtotalDiscounts = res.data.monthlyTotalDiscountAmount;
                let arr = res.data.rows || [];
                arr = arr.map((item, index) => {
                    item.oilsAry = [];
                    item.nonOilAry = [];
                    item.productInfoList.forEach(itemo => {
                        if (itemo.isOilProduct) {
                            // 油品
                            item.oilsAry.push(itemo);
                        } else {
                            // 非油品
                            item.nonOilAry.push(itemo);
                        }
                    });
                    item.setData = this.setNonZero(item.discountList);
                    return item;
                });
                this.preferentialArray = this.preferentialArray.concat(arr);
                if (this.page >= res.data.pageSum) {
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.page++;
            }
            if (this.preferentialArray.length == 0) {
                this.showEmpty = true;
            } else {
                this.showEmpty = false;
            }
        },
        /**
         * @description  : 过滤不为零的优惠种类
         * @param         {Object} data:账单数据
         * @return        {*}
         */
        setNonZero(data) {
            let setData = [];
            let filterAryPay = data.filter(item => {
                return item.payMethodName != '其他优惠';
            });
            let otherAry = data.filter(item => {
                return item.payMethodName == '其他优惠';
            });
            let aryPayData = filterAryPay.map(item => {
                return { value: item.payAmount, name: item.payMethodName };
            });
            let aryOtherData = otherAry.map(item => {
                return { value: item.payAmount, name: item.payMethodName };
            });
            let arys = [];
            aryPayData.forEach((item, index) => {
                if (item.value != 0) {
                    arys.push(item);
                }
            });
            if (arys.length <= 3) {
                arys.forEach((items, indexs) => {
                    setData.push(items);
                });
            }
            if (arys.length >= 4) {
                setData = arys.slice(0, 2).concat(aryOtherData);
            }
            return setData;
        },
        /**
         * @description  : 订单详情跳转
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        orderDetailsCar(item) {
            // 订单详情跳转
            let params = item;
            let url = '/packages/third-order/pages/order-detail/main';
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 柱状图series数据项
         * @return        {*}
         */
        async getServerData() {
            //模拟从服务器获取数据时的延时
            let res = await getDiscountsAllMonthsNew();
            if (res.success) {
                let data = res.data;
                if (data.length < 6) {
                    let lengthIndex = 6 - data.length;
                    let ary = [];
                    for (let i = 0; i < lengthIndex; i++) {
                        ary.push({ month: '', discounts: '' });
                    }
                    data = data.concat(ary);
                }
                // console.log(11111111111, res.data)
                // 在能源浪潮中蜕变、领航，精准交付，版图纵横，再书华章，岁岁欢颜，恭贺新禧
                if (data.length > 0) {
                    this.emptyCharts = true;
                } else {
                    this.emptyCharts = false;
                }
                let month = data.map(item => {
                    let month = item.month ? item.month.split('-')[1] + '月' : '';
                    return month;
                });
                let yearStr = this.nowDateT.split('年');
                let monthStr = yearStr[1].split('月');
                let yearMonth = yearStr[0] + '-' + monthStr[0];
                let discounts = data.map((item, index) => {
                    return {
                        value: item.discounts,
                        color: yearMonth == item.month ? '#e14541' : '#D3A884',
                    };
                });
                this.dataCanves = data;
                let resData = {
                    categories: month,
                    series: [
                        {
                            name: '金额',
                            data: discounts,
                            formatter: function (value, index) {
                                return value !== '' ? '¥' + value : ''; // 在每个数值后面添加单位
                            },
                        },
                    ],
                };
                this.drawCharts('afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL', resData);
            }
        },
        /**
         * @description  : 柱状图option数据项
         * @param         {String} id:柱状图id
         * @param         {Object} data:柱状图series数据
         * @return        {*}
         */
        drawCharts(id, data) {
            // #ifdef MP-WEIXIN || H5-CLOUD
            const ctx = uni.createCanvasContext(id);
            // #endif
            // #ifndef MP-WEIXIN || H5-CLOUD
            //这里的第一个 750 对应 css .charts 的 width1
            const ctx = my.createCanvasContext(id);
            // #endif
            this.uChartsInstance[id] = new uCharts({
                touchMoveLimit: 24,
                enableScroll: true,
                animation: true,
                background: '#FFFFFF',
                categories: data.categories,
                context: ctx,
                extra: {
                    tooltip: {
                        showBox: false,
                    },
                    column: {
                        type: 'group', // 'group'分组柱状
                        width: this.HarmonyWidthShow ? 20 : 12, // 柱状图每个柱子的图形宽度
                        activeBgColor: '#000000', // 当前点击柱状图的背景颜色
                        activeBgOpacity: 0, // 当前点击柱状图的背景颜色透明度
                        barBorderRadius: [5, 5, 0, 0], // 自定义4个圆角半径[左上,右上,右下,左下]
                    },
                },
                height: this.cHeight * this.pixelRatio,
                legend: {
                    show: false, // 图例是否开启
                },
                padding: this.HarmonyWidthShow ? [25, 0, 15, 0] : [15, 0, 5, 0],
                pixelRatio: this.pixelRatio,
                series: data.series,
                type: 'column',
                fontColor: '#D3A884',
                fontSize: this.HarmonyWidthShow ? 20 : 12,
                width: this.cWidth * this.pixelRatio,
                dataLabel: true, // 显示数据标签
                xAxis: {
                    disableGrid: true, // 不绘制纵向网格(即默认绘制网格)
                    axisLine: false, // 绘制坐标轴轴线
                    fontColor: '#D3A884',
                    fontSize: this.HarmonyWidthShow ? 20 : 12,
                    scrollShow: false,
                    itemCount: 6,
                },
                yAxis: {
                    data: [{ min: 0 }],
                    disabled: true, // 不绘制Y轴
                    disableGrid: true, // 不绘制横向向网格(即默认绘制网格)
                    fontColor: 12,
                },
            });
            // 默认显示最新六个月的数据
            if (this.dataCanves.length >= 6) {
                let itemIndex = this.uChartsInstance[id].opts.chartData.xAxisData.xAxisPoints[1] * (this.dataCanves.length - 1 - 5);
                this.uChartsInstance[id].translate(-itemIndex);
            }
        },
        touchstart(e) {
            this.uChartsInstance[e.target.id].scrollStart(e);
        },
        touchmove(e) {
            this.uChartsInstance[e.target.id].scroll(e);
        },
        touchend(e) {
            this.uChartsInstance[e.target.id].scrollEnd(e);
            this.uChartsInstance[e.target.id].touchLegend(e);
            this.uChartsInstance[e.target.id].showToolTip(e);
        },
        /**
         * @description  : 关闭选择时间弹窗
         * @return        {*}
         */
        hideShow() {
            this.dateShow1 = false;
            this.dateShow2 = false;
        },
        /**
         * @description  : 收入明细选择时间并请求接口
         * @param         {*} time:选择时间
         * @return        {*}
         */
        incomebindDateChange(time) {
            this.dateShow1 = false;
            time.month = time.month > 9 ? time.month : '0' + time.month;
            this.incomeDate = time.year + '年' + time.month + '月';
            this.inDate = time.year + '-' + time.month;
            let dates = this.dataCanves.filter(item => {
                return item.month === this.inDate;
            });
            // 设置柱状图高亮显示，并支持联动效果
            this.setLinkageAnimation(this.inDate);
            this.getPreferentialList({ isInit: true });
        },
        /**
         * @description  : 设置联动动画，柱状图高亮显示选择的日期
         * @param         {*} time:选择时间
         * @return        {*}
         */
        setLinkageAnimation(time) {
            let scrollIndex = 0;
            let discounts = this.dataCanves.map((item, index) => {
                if (index >= 0 && index <= 5) {
                    item.scrollNumber = 0;
                } else {
                    item.scrollNumber =
                        this.uChartsInstance['afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL'].opts.chartData.xAxisPoints[1] * (index - 5);
                }
                if (item.month == time) {
                    scrollIndex = item.scrollNumber;
                }
                return {
                    value: item.discounts,
                    color: time == item.month ? '#e14541' : '#D3A884',
                };
            });
            let series = [
                {
                    name: '金额',
                    data: discounts,
                    formatter: function (value, index) {
                        return value !== '' ? '¥' + value : ''; // 在每个数值后面添加单位
                    },
                },
            ];
            this.uChartsInstance['afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL'].translate(-scrollIndex);
            this.uChartsInstance['afMCYQMEmXXVAjNQFJvvfxbLSHuxNEOL'].updateData({
                series: series,
            });
        },
    },
    computed: {},
    components: { SetSelectDate },
};
</script>
<style scoped lang="scss">
.containerWrap {
    padding: 32rpx 32rpx 0;
    .tips {
        font-size: 24rpx;
        font-weight: 400;
        line-height: 33rpx;
        color: #333333;
        margin-left: 26rpx;
        margin-bottom: 24rpx;
        display: flex;

        .tipsRight {
            display: flex;
            flex-direction: column;
        }

        .color {
            color: #e64f22;
        }
    }
}
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;
    background: #f7f7fb;
    padding-bottom: 10px;
    .remaining_div_top {
        height: 200px;
        width: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;

        .card_bg {
            height: 200px;

            .mask_img {
                width: 100%;
                height: 100.5%;
                position: relative;
                top: 0;
            }
        }
    }

    .scroll_div {
        width: 100%;
    }

    .content_div {
        width: 100%;
        z-index: 10;

        .card_top {
            .card_div {
                .card_div_top {
                    width: 100%;
                    // padding: 0 18px;
                    height: 180px;
                    position: relative;
                    display: flex;
                    justify-content: center;

                    .card_img {
                        width: 342px;
                        height: 180px;
                        border-radius: 16px;
                    }

                    .cardInfo {
                        border-radius: 0px 0px 16px 16px;
                        position: absolute;
                        top: 0;

                        .card_header_img {
                            width: 40px;
                            height: 40px;
                            border-radius: 50px;
                            margin-top: 5px;
                        }

                        .card_header_text {
                            background: linear-gradient(219.83deg, #6b3214 45.42%, #d16227 33.42%, #6b3214 85.55%);
                            background-clip: text;
                            -webkit-background-clip: text;
                            color: transparent;
                            margin-top: 5px;
                        }

                        .card_day {
                            color: #a36b3e;
                            margin-top: 5px;
                        }

                        .card_amountLab {
                            position: relative;
                            width: 77px;
                            height: 24px;
                            display: flex;
                            justify-content: center;
                            line-height: 20px;
                            margin-top: 10px;
                        }

                        .card_bgimg {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                        }

                        .card_date {
                            color: #ad815e;
                            margin-top: 8px;
                        }
                    }
                }
            }
        }

        .card_charts {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 7px;

            .card_charts_box {
                width: 342px;
                height: 140px;
                background: #ffffff;
                border-radius: 8px;
                position: relative;
                .kt-box {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 342px;
                    height: 140px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    .charts_ktimg {
                        width: 110px;
                        height: 110px;
                    }
                    .chart-kt-text {
                        font-size: 12px;
                        color: #666666;
                    }
                }

                .canvas_text {
                    width: 100%;
                    margin: 16px 0 10px 16px;
                    .tipsImg {
                        width: 110px;
                        height: 31px;
                        position: absolute;
                        right: 15px;
                        top: 13px;
                    }
                }
                .card_canvas_box {
                    width: 100%;
                    height: 100px;
                    .charts {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
    .list_search {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 7px;
        .list_box {
            width: 342px;
            height: auto;
            background: #ffffff;
            border-radius: 8px 8px 0px 0px;
            .list_header {
                padding: 0 16px;
                display: flex;
                justify-content: space-between;
                padding-top: 12px;
                padding-bottom: 12px;
                border-radius: 8px;

                .list_header-left {
                    font-weight: bold;
                    color: #333333;
                    display: flex;
                    align-items: center;
                    line-height: 14px;

                    img {
                        margin-left: 5px;
                        width: 16px;
                        height: 16px;
                    }
                }

                .list_header-right {
                    span:first-child {
                        color: #e64f22;
                    }

                    span:last-child {
                        color: #e64f22;
                        font-family: 'HarmonyOS_Sans_Condensed_Medium';
                    }
                }
            }
        }
    }
    .list_data {
        width: 100%;
        display: flex;
        justify-content: center;
        .list_data_box {
            width: 342px;
            background: #ffffff;
            border-radius: 0px 0px 8px 8px;
            .list_data_li {
                padding: 8px 16px;
                overflow: hidden;
                margin-bottom: 15px;
                .list_data_top {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 6px;
                    .commodity {
                        width: 44px;
                        height: 44px;
                        margin-right: 5px;
                    }
                    .title {
                        max-width: 170px;
                    }
                }

                .list_data_bottom {
                    width: 100%;
                    height: 52px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;

                    .list_data_bottom_right {
                        width: calc(100% - 70px);
                        border-right: 1px solid #d0d0de;
                        .list_data_bottom_right_box {
                            height: 100%;
                            width: 33.3%;
                        }
                        .widhtMax {
                            min-width: 50px;
                        }
                        .marlset {
                            margin-left: 20px;
                        }
                    }

                    .list_data_bottom_left {
                        width: 90px;

                        .list_data_bottom_left_box {
                            height: 100%;
                        }
                    }
                }
            }
        }
    }

    .marl12 {
        margin-left: 12px;
    }
}
</style>
