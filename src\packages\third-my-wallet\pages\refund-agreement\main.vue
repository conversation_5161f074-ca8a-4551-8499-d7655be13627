<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="position: relative; background: #ffffff">
        <zj-navbar title="退款协议" :border-bottom="false"></zj-navbar>
        <div class="title-tip">
            <div class="title-text">您好，系统将按下列规则为您办理退款。</div>
        </div>
        <div class="text-box">
            <div class="text">
                1. 充值订单仅支持余额全部退，按原充值订单逐笔退款； <br />
                2. 充值订单有“迁移进、归集进、充值卡充值” 的方式不支持原路退款；<br />
                3. 账户有预授权、o2o等未完成的订单，将无法申请退款；<br />
                4. 账户为冻结等异常状态，将无法申请退款；<br />
                5. 享受第三方平台充值优惠的部分不支持退款，按实付金额退款；<br />
                6. 退款申请通过后，我们将于7个工作日内按充值方式原路退回；<br />
                7. 如退款余额充值时间超过80天，系统无法原路退回，需要到客户服务网点办理退款；<br />
                8. 充值订单常用地必须与当前昆仑e享卡常用地保持一致；<br />
                9. 退款申请受理期间，账户将会被冻结，不再允许其他操作;可以随时撤销申请，账户自动解冻；<br />
                10. 退款规则：仅支持全额退款，每日限1次，每月限2次，每年限5次（自然年）；<br />
                11. 如有疑问，敬请咨询956100。<br />
            </div>
        </div>
        <div class="btn-box">
            <div class="btn" @click="toRefundApplication"> 申请退款 </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    methods: {
        // 去申请退款
        toRefundApplication() {
            let url = '/packages/third-my-wallet/pages/refund-application/main';
            this.$sKit.layer.useRouter(url);
        },
    },
};
</script>

<style lang="scss" scoped>
.title-tip {
    height: 64rpx;
    background: #fff7dc;
    padding-left: 49rpx;
    .title-text {
        font-size: 24rpx;
        color: #333333;
        line-height: 64rpx;
    }
}
.text-box {
    padding: 32rpx 30rpx 0;

    .text {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
    }
}
.btn-box {
    position: absolute;
    bottom: calc(env(safe-area-inset-bottom) + 32rpx);
    right: 32rpx;
    left: 32rpx;
    height: 88rpx;
    background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
    text-align: center;
    border-radius: 16rpx;

    .btn {
        line-height: 88rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
    }
}
</style>
