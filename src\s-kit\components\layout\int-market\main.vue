<template>
    <div class="box">
        <img alt="" src="../../../image/intMarket.png" />
        <div class="text">{{ intMarketObj.userCopywriting || '' }}</div>
    </div>
</template>
<script>
import { recNonOilGoods } from '../../../js/v3-http/https3/oilStationService/index';
import { clientCode } from '../../../../../project.config';
export default {
    name: 'intMarket',
    props: {
        stationCode: {
            default: '',
            type: String,
        },
        pageID: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            intMarketObj: '',
        };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
        async getIntMarketText() {
            let res = await recNonOilGoods({ stationCode: this.stationCode }, { isload: false, isCustomErr: true });
            if (res && res.success) {
                this.intMarketObj = res.data;
                this.$emit('getIntMarketText', this.intMarketObj.userCopywriting || '');
                this.intMarketBiz();
            } else {
                this.$emit('getIntMarketText', false);
            }
        },
        intMarketBiz() {
            if (this.intMarketObj.userCopywriting && this.pageID) {
                let obj = {
                    stationcode: this.intMarketObj.stationCode,
                    marketingCode: this.intMarketObj.activityBasisId,
                    productCode: this.intMarketObj.productNo,
                    marketingContent: this.intMarketObj.userCopywriting,
                };
                let bizContent = JSON.stringify(obj).replace(/,/g, ' ');
                this.$sKit.mpBP.tracker('AI营销助手', {
                    seed: 'smart_marketing',
                    pageID: this.pageID,
                    channelID: clientCode,
                    dateType: 'exposure',
                    content: bizContent,
                });
            }
        },
    },
    watch: {
        stationCode: {
            handler(val, oldVal) {
                if (val) {
                    this.getIntMarketText();
                }
            },
            immediate: true,
        },
        pageID: {
            handler(val, oldVal) {
                this.intMarketBiz();
            },
            immediate: true,
        },
    },
};
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;

    img {
        width: 100%;
        max-width: 702rpx;
        height: 200rpx;
    }
    .text {
        position: absolute;
        width: 600rpx;
        max-height: 64rpx;
        bottom: 110rpx;
        left: 50%;
        margin-left: -300rpx;

        color: #ff7414;

        font-weight: bold;
        font-size: 28rpx;
        line-height: 32rpx;
        text-align: center;

        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
}
</style>
