<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <view class="tabar-page-class">
        <personalCenter ref="personal"></personalCenter>
        <zj-show-modal></zj-show-modal>
    </view>
</template>
<script>
import personalCenter from '../../s-kit/first/third-personal/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    components: {
        personalCenter,
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
        console.log('llll-我的');
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {
        my.on('pageWillShow', res => {
            this.$refs.personal.refreshPullDown();
        });
    },
    methods: {},
    //   onPullDownRefresh() {
    //     uni.showToast({
    //       title:'刷新'
    //     })
    //     uni.stopPullDownRefresh();
    //   // 页面被下拉
    //  },
    onPullRefresh() {
        uni.showToast({
            title: '刷新',
        });
    },
};
</script>
<style scoped lang="scss">
.tabar-page-class {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
</style>
