// #ifdef MP-WEIXIN
import { palmPayOpen, palmPayClose, palmPayGetFlag } from '../../../../../s-kit/js/v3-http/https3/wallet';
import { mapGetters } from 'vuex';
export default {
    onLoad() {
        let result = this.selectComponent('#passwordKeyboardId');
        this.$sKit.keyBordPlugin.initRef(result);
        this.passwordKeyboardRef = result;
        this.getPalmPayGetFlag();
    },
    methods: {
        // 输入密码
        showKeyboard() {
            if (this.openOrNot) {
                this.turnOffPalmPrintPayment();
                return;
            }
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                pwd => {
                    this.originalPasswordLength = this.passwordKeyboardRef.getLength('password_unique1');
                    this.originalPassword = this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    if (this.originalPasswordLength == 6) {
                        this.activatePalmPrintPayment();
                    }
                },
                () => {},
            );
        },
        // 开通掌纹支付
        async activatePalmPrintPayment() {
            if (this.originalPasswordLength != 6) {
                uni.showToast({
                    title: '请输入6位密码',
                    icon: 'none',
                });
                return;
            }
            let params = {
                openId: this.openId,
                payPassword: this.originalPassword,
            };
            let res = await palmPayOpen(params);
            if (res.success) {
                this.getPalmPayGetFlag();
            }
        },
        // 查询开通结果
        async getPalmPayGetFlag() {
            let res = await palmPayGetFlag();
            if (res.success) {
                if (res.data.palmPaymentFlag) {
                    this.openOrNot = true;
                } else {
                    if (this.officialAccountParams == 'brushPalm') {
                        this.hideHand = true;
                        setTimeout(() => {
                            this.hideHand = false;
                        }, 3000);
                    }
                }
            }
        },
        // 关闭掌纹支付
        async turnOffPalmPrintPayment() {
            let res = await palmPayClose();
            if (res.success) {
                this.openOrNot = false;
            }
        },
    },
    computed: {
        ...mapGetters(['openId']),
    },
};
// #endif
