let events = require('events');
let fs = require('fs');
const addMixinsStr = (pageStr, mixinsName) => {
    if (!mixinsName) throw '未检测到mixins名称';
    if (pageStr.indexOf(mixinsName) == -1) {
        const mixStr1 = `import ${mixinsName} from '@/s-kit/js/public-mixins-api.js';\n`;
        const mixStr2 = `\n    mixins: [${mixinsName}],`;
        const exportDefaultStr = 'export default {';
        const exportDefaultIndex1 = pageStr.indexOf(exportDefaultStr);
        const mixinsStr = 'mixins: [';

        // 在export default所在行之前添加导入语句
        const pageStr2 = pageStr.slice(0, exportDefaultIndex1) + mixStr1 + pageStr.slice(exportDefaultIndex1);

        // 更新下标位置
        const exportDefaultIndex2 = pageStr2.indexOf(exportDefaultStr);
        if (pageStr2.indexOf(mixinsStr) == -1) {
            // 在export default所在行之后添加mixins配置
            const pageStr3 =
                pageStr2.slice(0, exportDefaultIndex2 + exportDefaultStr.length) +
                mixStr2 +
                pageStr2.slice(exportDefaultIndex2 + exportDefaultStr.length);

            return pageStr3;
        } else {
            const mixinsIndex = pageStr2.indexOf(mixinsStr);
            const pageStr4 =
                pageStr2.slice(0, mixinsIndex + mixinsStr.length) + mixinsName + ', ' + pageStr2.slice(mixinsIndex + mixinsStr.length);
            return pageStr4;
        }
    } else {
        return pageStr;
    }
};
const addMixins = new events.EventEmitter();
addMixins.on('add', (pageStr, mixinsName) => {
    let root = './src/';
    let pageObj = JSON.parse(pageStr);
    // console.log('pageObj--',pageObj.subPackages)
    let pages = pageObj.pages;
    let subPackages = pageObj.subPackages;
    // 主包页面修改
    for (let i = 0; i < pages.length; i++) {
        // 跳过首页
        if (pages[i].path === 'main') {
            continue;
        } else if (pages[i].path.indexOf('third') !== -1) {
            let path = `${root}${pages[i].path}.vue`;
            let vueStr = fs.readFileSync(path, 'utf-8');
            vueStr = addMixinsStr(vueStr, mixinsName);
            fs.writeFileSync(path, vueStr);
        }
        // console.log(vueStr)
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // 分包页面修改
    for (let i = 0; i < subPackages.length; i++) {
        let subItem = subPackages[i];
        //只有3.0的分包才进行混入
        if (subItem.root.indexOf('third') === -1) continue;
        for (let j = 0; j < subItem.pages.length; j++) {
            // 跳过首页
            if (subItem.pages[j].path === 'main') continue;
            let subPages = subItem.pages[j];
            let path = `${root}${subItem.root}/${subPages.path}.vue`;
            let vueStr = fs.readFileSync(path, 'utf-8');
            vueStr = addMixinsStr(vueStr, mixinsName);
            fs.writeFileSync(path, vueStr);
            // console.log(vueStr)
        }
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // console.log(pageObj)
});
module.exports = addMixins;
