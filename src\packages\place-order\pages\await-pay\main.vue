<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="待支付"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
            :custom-back="clickCustomBackBtn"
        ></u-navbar>
        <div class="order-view" v-if="awaitData">
            <div class="order-title-view">
                <!-- <img class="order-icon" src="@/static/await-pay.png" mode=""> -->
                <div class="order-title">订单信息</div>
            </div>
            <div class="order-cell" v-for="(item, index) in orderInfo" :key="index">
                <div class="order-cell-title">{{ item.title }}</div>
                <div class="order-cell-detail">{{ item.detail }}</div>
            </div>
        </div>
        <div class="order-view" v-if="awaitData">
            <div class="order-title-view">
                <div class="order-title">商品信息</div>
            </div>
            <div class="order-cell" v-for="(item, index) in commodityInfo" :key="index">
                <div class="order-cell-title">{{ item.title }}</div>
                <div class="order-cell-detail">{{ item.detail }}</div>
            </div>
        </div>
        <div class="order-view" v-if="awaitData">
            <div class="order-title-view">
                <div class="order-title">优惠信息</div>
            </div>
            <div class="order-cell" v-for="(item, index) in discountInfo" :key="index">
                <div class="order-cell-title">{{ item.title }}</div>
                <div class="order-cell-detail">{{ item.detail }}</div>
            </div>
        </div>
        <div class="order-view pb-0" v-if="awaitData">
            <div class="order-title-view">
                <div class="order-title">需付款</div>
                <div style="flex: 1"></div>
                <div class="order-fk-detail">￥{{ awaitData.realAmt }}</div>
            </div>
        </div>
        <div class="order-down" v-if="awaitData">
            <div class="order-text">该订单将于</div>
            <div class="order-down-text">{{ downText }}</div>
            <div class="order-text">秒后失效，请尽快支付</div>
        </div>
        <div class="add-footer">
            <div class="footer-btn" @click="clickPay">立即支付</div>
        </div>
        <!-- 支付密码弹窗 -->
        <payment-password
            :show="isShowPasswordBox"
            :showError="isShowPaymentPasswordErrorBox"
            :amount="awaitData.realAmt"
            @reEnter="handleReEnterPaymentPassword"
            @forgetPwd="handleForgetPaymentPassword"
            @close="handleClosePasswordBox"
            @completed="oilCardPay"
            @update:showError="this.isShowPaymentPasswordErrorBox = $event"
        />
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
let timer;
import pageConfig from '@/utils/pageConfig.js';
import paymentPassword from '@/components/payment-password/payment-password.vue';
import { cardPay, waitOrderPay, waitPayOrder } from '@/api/home.js';
const payModeWX = 'wx';
const payModeOilCard = 'oilCard';

export default {
    components: {
        paymentPassword,
    },
    data() {
        return {
            pageConfig: pageConfig,
            awaitData: null, // 未完成订单数据
            downText: 0,
            isShowPasswordBox: false, // 是否显示自定义密码输入弹窗
            isShowPaymentPasswordErrorBox: false, // 是否显示支付密码输入错误弹窗
            payAmount: 0,

            orderInfo: [
                {
                    title: '支付创建时间',
                    detail: '',
                },
                {
                    title: '支付方式',
                    detail: '',
                },
                {
                    title: '油站',
                    detail: '',
                },
            ],
            commodityInfo: [
                {
                    title: '油枪',
                    detail: '',
                },
                {
                    title: '油品',
                    detail: '',
                },
                {
                    title: '加油量',
                    detail: '',
                },
                {
                    title: '油价',
                    detail: '',
                },
            ],
            discountInfo: [
                {
                    title: '优惠券',
                    detail: '',
                },
            ],
        };
    },
    async onLoad(options) {
        this.awaitData = JSON.parse(decodeURIComponent(options.data));
        this.payMode = options.payMode;
        if (typeof options.payAmount !== 'undefined') {
            this.payAmount = options.payAmount;
        }
        this.downText = this.awaitData.remainTime;

        let payMode;
        if (this.awaitData.payMode == 'wx') {
            payMode = '微信支付';
        } else if (this.awaitData.payMode == 'oilCard') {
            payMode = `油卡支付`;
        }
        this.orderInfo[0].detail = this.awaitData.orderTime;
        this.orderInfo[1].detail = payMode;
        this.orderInfo[2].detail = this.awaitData.stationName;

        this.commodityInfo[0].detail = this.awaitData.oilGun + '号枪';
        this.commodityInfo[1].detail = isNaN(Number(this.awaitData.oils)) ? this.awaitData.oils : this.awaitData.oils + '#';
        this.commodityInfo[2].detail = this.awaitData.quantity;
        this.commodityInfo[3].detail = this.awaitData.unitPrice + '元/升';

        this.discountInfo[0].detail = '-￥' + this.awaitData.otherAmt;
        timer = setInterval(async () => {
            this.downText--;
            if (this.downText == 0) {
                clearInterval(timer);
                await this.$util.showModal('支付超时', true);
                uni.$emit('confirmBack');
                uni.navigateBack();
            }
        }, 1000);
    },
    onUnload() {
        if (timer) {
            clearInterval(timer);
        }
    },
    methods: {
        clickCustomBackBtn() {
            uni.$emit('confirmBack', {
                downNum: this.downText,
                payMode: this.payMode,
            });
            uni.navigateBack();
        },
        async clickPay() {
            // clearInterval(timer)
            let res = await waitOrderPay({ orderNo: this.awaitData.orderNo });
            this._cardPayRes = res;
            if (this.payMode == payModeWX) {
                const _this = this;
                try {
                    await this.wxPay(res);
                    this.handlePaySuccess(res);
                } catch (err) {
                    _this.handlePayFail(true);
                }
            } else if (this.payMode == payModeOilCard) {
                this.isShowPasswordBox = true;
            }
        },
        async handlePayFail(ispay) {
            if (!ispay) {
                let res = await waitPayOrder();
                console.log(res.data.remainTime);
                uni.$emit('confirmBack', {
                    downNum: res.data ? res.data.remainTime : null,
                    payMode: this.payMode,
                });
                uni.navigateBack();
            }
        },
        handlePaySuccess(res) {
            uni.redirectTo({
                url: '/packages/place-order/pages/pay-result/main?orderid=' + res.data.orderNo,
            });
        },
        wxPay(res) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: res.data.timeStamp,
                    nonceStr: res.data.nonceStr,
                    package: res.data.prepay,
                    signType: res.data.signType,
                    paySign: res.data.paySign,
                    success: resolve,
                    fail: reject,
                });
            });
        },
        /**
         * 关闭密码框
         */
        handleClosePasswordBox() {
            this.isShowPasswordBox = false;
            this.handlePayFail(true);
        },
        // 重新输入支付密码
        handleReEnterPaymentPassword() {
            this.isShowPasswordBox = true;
        },
        // 忘记支付密码
        handleForgetPaymentPassword() {
            uni.navigateTo({
                url: '/packages/password/pages/edit-password/main?isfor=1',
            });
        },
        // 油卡支付
        async oilCardPay({ value }) {
            const encryptedPassword = this.$util.payPassEncryption(value);
            const { idNum, idType, orderNo, cardNo } = (this._cardPayRes || {}).data;
            const { status, info } = await cardPay(
                {
                    cardNo,
                    idNum, //证件号
                    idType, //证件类型
                    orderNo, //订单号
                    payPassword: encryptedPassword,
                    timestamp: Date.now(), //时间戳
                    nonce: this.$util.generateUUID(), //随机字串
                },
                null,
                () => {
                    this.isShowPasswordBox = false;
                },
            );

            if (status === 0) {
                // 验证成功
                this.handlePaySuccess(this._cardPayRes);
            } else {
                // 验证失败
                if (info == '密码错误') {
                    this.isShowPaymentPasswordErrorBox = true;
                } else {
                    this.$util.showModal(info, true);
                }
            }
            this.isShowPasswordBox = false;
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
    .order-view {
        background-color: #ffffff;
        margin-top: 10px;
        border-radius: 5px;
        margin-left: 15px;
        width: 345px;
        padding: 0 10px;
        padding-bottom: 6px;
        .order-title-view {
            display: flex;
            align-items: center;
            height: 44px;
            .order-icon {
                height: 16px;
                width: 16px;
                margin-right: 5px;
            }
            .order-title {
                font-size: 15px;
                font-weight: 700;
            }
            .order-fk-detail {
                color: $btn-color;
                font-weight: 700;
                font-size: 30rpx;
            }
        }

        .order-cell {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #333333;
            .order-cell-title {
                line-height: 30px;
            }
            .order-cell-detail {
                line-height: 30px;
            }
        }
    }
    .pb-0 {
        padding-bottom: 0;
    }
    .order-down {
        background-color: #ffffff;
        margin-top: 10px;
        border-radius: 5px;
        margin-left: 15px;
        width: 345px;
        padding-left: 30px;
        display: flex;
        align-items: center;
        .order-text {
            line-height: 44px;
            font-size: 15px;
            font-weight: 700;
            color: #333333;
        }
        .order-down-text {
            margin-left: 2px;
            margin-right: 2px;
            color: $btn-color;
            line-height: 44px;
            font-size: 17px;
            font-weight: 700;
        }
    }
    .add-footer {
        width: 100%;
        background: #ffffff;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 1;
        padding-bottom: env(safe-area-inset-bottom);

        .footer-btn {
            margin: 10px 15px;
            width: 345px;
            height: 44px;
            background: $btn-color;
            border-radius: 5px;
            font-size: 15px;
            text-align: center;
            font-weight: bold;
            color: #ffffff;
            line-height: 44px;
            margin: 12px auto 9px;
        }
    }
}
</style>
