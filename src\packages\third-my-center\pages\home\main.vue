<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="content">
            <personalCenter pageType="page"></personalCenter>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import personalCenter from '../../../../s-kit/first/third-personal/main.vue';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    onLoad(option) { },
    onShow() { },
    methods: {},
    computed: {},
    components: {
        personalCenter,
    },
};
</script>
<style scoped lang="scss"></style>
