<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 自助开票页面 -->
        <div class="pullDownRefreshView">
            <zj-navbar :border-bottom="false" title="自助打印"></zj-navbar>
            <div class="f-1 mh-0 bg-F7F7FB">
                <div class="print-auto">
                    <div class="canvas-box">
                        <canvas class="qrcode" id="qrcode" canvas-id="qrcode"></canvas>
                        <div class="tips-txt">请在自助开票设备上扫描二维码打印电子发票</div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import wxbarcode from 'uniapp-qrcode';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    // 自动打印
    onLoad(options) {
        let data = JSON.parse(decodeURIComponent(options.data));
        this.invoiceDownloadPost(data);
    },
    methods: {
        /**
         * @description  : 获得生成二维码所需数据,并生成二维码
         * @param         {*} pageInfo:
         * @return        {*}
         */
        async invoiceDownloadPost(data) {
            setTimeout(() => {
                wxbarcode.qrcode('qrcode', data.printCode, 460, 460);
            }, 50);
        },
    },
};
</script>

<style lang="scss" scoped>
.print-auto {
    padding: 32rpx;
    background: #f7f7fb;

    .canvas-box {
        width: 100%;
        height: 404px;
        background: #ffffff;
        border-radius: 8px;
        padding-top: 70px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode {
            display: inline-block;
            width: 230px;
            height: 230px;
        }

        .tips-txt {
            margin-top: 25px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: 23px;
        }
    }
}
</style>
