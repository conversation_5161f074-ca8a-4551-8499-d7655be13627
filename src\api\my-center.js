import { POST, GET } from './httpKit.js';

/**发票设置相关接口 */
//获取企业发票抬头列表
export const getCompanyTitleListPost = params => POST('/app/json/invoice/getCompanyTitleList', params);
//获取个人发票抬头列表
export const getPersonalTitleListPost = params => POST('/app/json/invoice/getPersonalTitleList', params);
// 重新发送发票接口
export const sendInvoiceAgain = params => POST('/app/json/invoice/sendInvoiceAgain', params);
// 合并后的发票抬头列表
export const getInvoiceTitleList = params => POST('/app/json/invoice/getInvoiceTitleList', params);
// 抬头详情
export const getTitleDetails = params => POST('/app/json/invoice/getTitleDetails', params);

//删除发票抬头
export const delInvoiceTitlePost = params => POST('/app/json/invoice/deleteInvoiceTitle', params);

//开票历史列表
export const getBillingHistoryListPost = params => POST('/app/json/invoice/getBillingList', params);
// 删除开票历史
export const deleteInvoice = params => POST('/app/json/invoice/deleteInvoice', params);
//发票详情接口
export const getBillingDetails = params => POST('/app/json/invoice/getBillingDetail', params);

//第三方获取用户信息
export const getOtherUserInfoPost = params => POST('/app/json/member/getUserInfo', params);

//添加油卡获取验证码接口
export const getOilCardPhoneCodePost = params => POST('/app/json/cnpc_card/sendPhoneCode', params);
//添加油卡提交接口
// export const getOilCardSubmitPost = params => POST('/app/json/cnpc_card/binding', params);
export const getOilCardSubmitPost = (params, config) => {
    return POST('/app/json/cnpc_card/binding', params, config);
};
// 获取常用邮箱
export const getUserEmail = params => POST('/app/json/member/getUserEmail', params);
// 下载发票
export const downInvoice = (url, params) => GET(url, params);

// 发票抬头二维码
export const getInvoiceQrCodeApi = params => POST('/app/json/invoice/getInvoiceQrCode', params);

// 自动打印二维码
export const getInvoicePrintQrCodeApi = params => POST('/app/json/invoice/getInvoicePrintQrCode', params);
// 发票红冲
export const reverseInvoiceApi = params => POST('/app/json/invoice/reverseInvoice', params);

// 加油卡资金消费记录/积分消费记录
export const getCardConsumeDetailsApi = (params, config) =>
    POST('/app/json/cnpc_card/getCardConsumeDetails', params, config, true, false, () => {
        console.log('error');
    });
//获取用户昵称头像信息更新接口
export const saveUserInfoApi = params => POST('/app/json/member/syncUserInfo', params);
