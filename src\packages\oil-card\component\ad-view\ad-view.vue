<template>
    <div class="ad-view" v-if="visible">
        <div class="full-div">
            <img class="ad-img-class" src="@/static/isPreferential.png" @click="enterNav()" />
            <div class="close-tbn-3" @click="close">
                <img src="@/static/X.png" />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ad-view',
    components: {},
    props: {
        oilMoneyInfo: {
            type: Number,
        },
    },
    data() {
        return {
            visible: true,
        };
    },
    onLoad() {},
    computed: {},
    methods: {
        close() {
            this.$emit('closeEvent');
        },
        enterNav() {
            this.$emit('enterNavEvent');
        },
    },
};
</script>

<style lang="scss" scoped>
.ad-view {
    position: absolute;
    bottom: 0;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 990;
    background-color: rgba(0, 0, 0, 0.6);
    .full-div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column;
        .ad-img-class {
            object-fit: contain;
            // background: repeat no-repeat;
            width: 50%;
        }
        .close-tbn-3 {
            position: relative;
            z-index: 10;
            margin-top: 30px;
            height: 0px;
            img {
                display: block;
                width: 25px;
                height: 25px;
                border: 1px solid white;
                border-radius: 50%;
            }
        }
    }
}
</style>
