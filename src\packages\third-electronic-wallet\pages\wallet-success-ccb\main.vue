<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" v-if="walletStatus.status">
        <zj-navbar :height="44" title="开通成功"></zj-navbar>
        <div class="content-view">
            <img class="success-img" src="../../images/successGreen.png" alt="" />
            <div class="success-text">您已成功开通昆仑e享卡</div>
            <div class="success-btn" @click="success()">完成</div>
        </div>
        <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { initRealPersonIdentify, realPersonIdentify } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { getEncryptInfo } from '../../../../s-kit/js/v3-http/https3/user.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'selectRegion',
    data() {
        return {};
    },
    onLoad(options) {
        if (options.data) {
            let optionsData = JSON.parse(decodeURIComponent(options.data));
            if (optionsData.type && optionsData.type === 'ccbH5') {
                this.intoCCBWalletPage()
                    .then(() => {
                        this.$cnpcBridge.closeMriver();
                    })
                    .catch(() => {});
                return;
            }
        }
        this.$store.dispatch('getSetWalletStatus', {
            callback: res => this.initJudgment(res),
        });
    },
    computed: {
        // 获取昆仑e享卡钱包状态
        ...mapGetters(['walletStatus']),
    },
    methods: {
        initJudgment(res) {
            if (res.status) {
                this.$store.commit('setWalletAddChannel', '');
            } else {
                this.$store.commit('setWalletAddChannel', 'ccb');
                let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                let params = {};
                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        success() {
            // 打开人脸认证协议弹窗，实人信息与建行对比
            this.$store.dispatch('changeFacePop', true);
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.initFaceAuthentication();
            // 跳转建行h5
        },
        /**
         * @description  :  人脸验证初始化
         * @param         {String} returnUrl -业务回跳地址；PC或H5接入时 必传，APP接入时为空
         * @param         {String} metaInfo -MetaInfo环境参数，需要通过JS获取或SDK获取
         * @param         {String} verifyMode -认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {Function} aliMetaInfo -阿里人脸采集MetaInfo
         * @param         {string} verifyUnique -身份认证唯一标识
         * @param         {string} certifyId -实人认证三方系统的标识
         * @param         {string} certifyUrl -第三方认证地址
         * @param         {Function} zjShowModal -全局自定义弹窗
         * @return        {*}
         */
        async initFaceAuthentication() {
            let params = {
                returnUrl: '',
                metaInfo: await this.$cnpcBridge.aliMetaInfo(),
                verifyMode: '1',
                compareFlag: '1',
                channel: '919',
            };
            let res = await initRealPersonIdentify(params);

            if (res && res.success) {
                this.$cnpcBridge.aliFaceCollec(res.data.certifyId, async result => {
                    //
                    if (result.status) {
                        //采集成功
                        this.realNameAuthentication(res.data);
                    } else {
                        this.$store.dispatch('zjShowModal', {
                            title: result.msg,
                            confirmText: '确认',
                            success: res => {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                }
                            },
                        });
                    }
                });
            }
        },
        /**
         * @description  :  实人认证
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
         * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
         * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
         * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {string} idNo: 用户身份证号
         * @return        {*}
         */
        async realNameAuthentication(data) {
            let params = {
                type: '14',
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                compareFlag: '1',
                verifyMode: '1',
                channel: '919',
                sceneId: data.sceneId,
            };
            let res = await realPersonIdentify(params);
            if (res.success) {
                // 跳转建行h5
                this.intoCCBWalletPage(res);
            }
        },
        /**
         *
         */
        async intoCCBWalletPage(res) {
            return new Promise(async (resolve, reject) => {
                let phoneInfo = await getEncryptInfo();
                if (phoneInfo.success && phoneInfo.data && phoneInfo.data.phone) {
                    let phone = phoneInfo.data.phone;
                    this.$cnpcBridge.openDevelopBank(result => {
                        if (result.status) {
                            this.$cnpcBridge.intoCCBWalletPage({
                                ccbPhone: phone,
                                ccbFlowNo: res ? res.data.ccbFlowNo : '',
                            });
                            resolve();
                        } else {
                            uni.showToast({
                                title: result.msg || '系统繁忙~请稍后再试~',
                                icon: 'none',
                            });
                            reject();
                        }
                    });
                } else {
                    uni.showToast({
                        title: '获取加密手机号失败',
                        icon: 'none',
                    });
                    reject();
                }
            });
        },
    },
};
</script>
<style scoped lang="scss">
.content-view {
    width: 100%;
    height: 100%;
    background: #f7f7fb;
    display: flex;
    flex-direction: column;
    align-items: center;

    .success-img {
        height: 128rpx;
        width: 128rpx;
        margin-top: 80rpx;
    }

    .success-text {
        margin-top: 32rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        line-height: 46rpx;
    }

    .success-btn {
        margin-top: 80rpx;
        width: 686rpx;
        height: 88rpx;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        text-align: center;
        font-weight: bold;
        font-size: 36rpx;
        color: #ffffff;
        line-height: 88rpx;
        border-radius: 16rpx;
    }
}
</style>
