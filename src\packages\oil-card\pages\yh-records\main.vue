<template>
    <div class="view">
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="优惠合同"
            :back-text-style="pageConfig.titleStyle"
        ></u-navbar>
        <div class="contract_not" v-if="oldRuleObj == null">
            <img src="@/static/not_cardcontract.png" alt />
            <span>暂未绑定优惠合同</span>
            <span>请咨询加油站员工，了解更多信息</span>
        </div>
        <div class="contract_have" v-if="oldRuleObj != null && oldRuleObj !== ''">
            <span class="title_class">您的加油卡已绑定以下优惠折扣</span>
            <div class="contract_item_class">
                <span class="name_class">{{ oldRuleObj.oldCtName }}</span>
            </div>
            <span v-if="!oldRuleObj.oldRuleSet || !oldRuleObj.oldRuleSet.defResult">油卡优惠合同或优惠规则已过期，详情请咨询站点</span>
            <div class="contract_item_class" v-else>
                <div>生效时间：{{ oldRuleObj.oldRuleSet.beginTime + '—' + oldRuleObj.oldRuleSet.endTime }}</div>
                <div>默认优惠条件：{{ oldRuleObj.oldRuleSet.defTradeType }}</div>
                <!-- <div>默认优惠内容：{{oldRuleObj.oldRuleSet.defResult}}</div> -->
                <div>默认优惠内容：{{ oldRuleObj.oldDescription }}</div>
                <div>特殊优惠：详情请拨打956100热线电话查询或咨询站点</div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
// 优惠合同
import pageConfig from '@/utils/pageConfig.js';
// import myPrice from '@/components/price/price.vue';
import { getContractInfo } from '@/api/home.js';

export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            oldRuleObj: null,
        };
    },
    async onLoad(info) {
        console.log(info);
        const res = await getContractInfo({
            cardNo: info.cardNo,
        });
        console.log('优惠合同', JSON.stringify(res));
        if (res.status == 0) {
            if (res.data.oldCtNo && res.data.oldCtNo != '0') {
                //有优惠合同
                this.oldRuleObj = res.data;
            } else this.oldRuleObj = null;
        }
        console.log(this.oldRuleObj);
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    background: #fff;
    padding: 10rpx;
    min-height: 100vh;
    font-size: 32rpx;
}

.contract_have {
    font-size: 29rpx;
    padding: 10rpx 14rpx;
    line-height: 55rpx;
    .title_class {
        color: #999999;
    }
}

.contract_not {
    width: 100%;
    margin-top: 40%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 30px;
    }
    span {
        font-size: 32rpx;
    }
}

.name_class {
    color: red;
}

// .main {
//   font-size: 29rpx;
//   padding: 6rpx 20rpx;
// }
// .contract {
//   padding: 24rpx ;
//   background: #fff;
//   padding-left: 15rpx;
//   font-size: 35rpx;
//   color: #F96702;
//   margin: 12rpx;
//   border-radius: 16rpx;
//   margin-bottom: 24rpx;
// }
</style>
