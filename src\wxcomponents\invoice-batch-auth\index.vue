<template>
<uni-shadow-root class="invoice-batch-auth-index"><view>
    <invoice-batch-auth :auth-key="authKey" @success="success" @fail="fail"></invoice-batch-auth>
</view></uni-shadow-root>
</template>

<script>
import InvoiceBatchAuth from 'plugin://wx-invoice/batch-auth.vue'
global['__wxVueOptions'] = {components:{'invoice-batch-auth': InvoiceBatchAuth}}

global['__wxRoute'] = 'invoice-batch-auth/index'

Component({
    properties: {
        authKey: {
            type: String,
            default: '',
        },
    },
    methods: {
        success(res) {
            console.log('plugin onSuccess:', res);
            this.triggerEvent('success', res.detail);
        },
        fail(err) {
            console.log('plugin onFail:', err);
            this.triggerEvent('fail', err.detail);
        },
    },
});
// interface SuccessResp {
//     ret: number;
//     errMsg: string;
//     out_user_id: string;
// }

// interface FailResp {
//     statusCode: number;
//     errMsg: string;
// }
export default global['__wxComponents']['invoice-batch-auth/index']
</script>
<style platform="mp-weixin">
button {
    -webkit-tap-highlight-color: transparent;
    background-color: #f8f8f8;
    border-radius: 5px;
    box-sizing: border-box;
    color: #000;
    cursor: pointer;
    display: block;
    font-size: 18px;
    line-height: 2.55555556;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    padding-left: 14px;
    padding-right: 14px;
    position: relative;
    text-align: center;
    text-decoration: none;
}

.auth--weui-btn-area .auth--weui-btn {
    margin-bottom: 11px;
    margin-top: 11px;
}
</style>