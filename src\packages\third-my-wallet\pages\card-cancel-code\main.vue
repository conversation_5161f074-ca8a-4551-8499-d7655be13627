<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="container bg-F7F7FB">
            <zj-navbar title="昆仑e享卡注销" :border-bottom="false"></zj-navbar>
            <!-- <div class="phone">{{ memberData.phone }}</div> -->
            <div class="phone">{{ phone }}</div>

            <div class="formBox" style="display: flex">
                <input v-model="checkCode" placeholder="请输入短信验证码" placeholder-class="forgot_pas_inp" type="number" maxlength="6" />
                <div v-if="show" class="verificationBtn" @click="sendCode">获取验证码</div>
                <div v-else class="verificationBtn">{{ count }}秒后再次获取</div>
            </div>
            <div class="btnBox" @click="confirmModification()">确认提交</div>
            <div class="tips_div">
                <div class="font-14 color-333 title">温馨提示：您需要进行短信验证才可完成注销</div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { memberInfo } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { messageCodeSend, accountLogout } from '../../../../s-kit/js/v3-http/https3/wallet';
import Vue from 'vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'forgot-password',
    data() {
        return {
            name: '',
            flag: true,
            codeFlag: true,
            count: 60,
            checkCode: '',
            timer: null,
            disabled: false,
            content: '获取验证码',
            show: true,
            phone: '',
            timerSendCode: null,
            messageType: false,
        };
    },
    created() {
        clearInterval(this.timer);
        // this.sendCode = this.$sKit.commonUtil.throttleUtil(this.sendCode); // 节流
    },
    async mounted() {
        // #ifdef MP-MPAAS
        // 获取用户信息
        let userInfo = await this.$cnpcBridge.getUserTokenInfo();
        this.phone = userInfo.phone;
        // #endif
        // #ifndef MP-MPAAS
        // //会员中心基本信息
        this.getmemberInfo();
        // #endif
    },
    computed: {},
    methods: {
        // #ifndef MP-MPAAS
        // 会员中心基本信息
        async getmemberInfo() {
            let res = await memberInfo({ isload: this.isload });
            if (res.success) {
                this.phone = res.data.phone;
            }
        },
        // #endif
        //发送验证码
        sendCode() {
            if (!this.show) return;
            if (!this.timerSendCode) {
                // #ifdef MP-MPAAS
                this.$accountCenter.getVerifyCode({ type: 2 }, res => {
                    if (res.isSuccessed) {
                        this.getCode();
                        this.show = false;
                        uni.showToast({
                            title: '验证码发送成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        this.messageType = true;
                    } else {
                        uni.showToast({
                            title: res.desString || '验证码发送失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                });
                // #endif
                // #ifdef MP-ALIPAY
                // 获取验证码
                this.aliGetCode();
                // #endif
                // #ifdef MP-WEIXIN
                this.wxGetCode();
                // #endif
                this.timerSendCode = setTimeout(() => {
                    this.timerSendCode = null;
                }, 3000);
            }
        },
        // #ifdef MP-ALIPAY
        // 获取验证码
        async aliGetCode() {
            console.log('发送验证码=========');
            let res = await messageCodeSend({
                messageType: '8',
            });
            if (res.success) {
                this.getCode();
                this.show = false;
                uni.showToast({
                    title: '发送验证码成功',
                    icon: 'none',
                    duration: 2000,
                });
                this.messageType = true;
            }
        },
        // #endif
        // #ifdef MP-WEIXIN
        // 获取验证码
        async wxGetCode() {
            console.log('发送验证码=========');
            let res = await messageCodeSend({
                messageType: '8',
            });
            if (res.success) {
                this.getCode();
                this.show = false;
                uni.showToast({
                    title: '发送验证码成功',
                    icon: 'none',
                    duration: 2000,
                });
                this.messageType = true;
            }
        },
        // #endif
        //倒计时
        getCode() {
            console.log('倒计时开始=======');
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        //注销事件
        confirmModification() {
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.checkCode == '') {
                uni.showToast({
                    title: '请输入验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.checkCode.length < 6) {
                uni.showToast({
                    title: '请输入6位验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // #ifdef MP-MPAAS
            uni.showLoading({});
            this.$accountCenter.cancelEwallet({ verifyCode: this.checkCode }, res => {
                uni.hideLoading();
                this.$store.dispatch('getSetWalletStatus');
                if (res.isSuccessed) {
                    this.toSuccess(res);
                } else {
                    this.toFail(res);
                }
            });
            // #endif
            // #ifdef MP-ALIPAY
            // 电子账户注销接口 /account/logout  app走sdk
            this.toAliAccountLogout();
            // #endif
            // #ifdef MP-WEIXIN
            // 电子账户注销接口 /account/logout  app走sdk
            this.toWXAccountLogout();
            // #endif
        },
        // #ifdef MP-ALIPAY
        // 电子账户注销接口 /account/logout  app走sdk
        async toAliAccountLogout() {
            let res = await accountLogout(
                {
                    type: '8',
                    messageCode: this.checkCode,
                },
                { isCustomErr: true },
            );
            if (res.success) {
                this.toSuccess(res);
            } else {
                this.toFail(res);
            }
        },
        // #endif
        /*注销事件 */
        // #ifdef MP-WEIXIN
        // 电子账户注销接口 /account/logout  app走sdk
        async toWXAccountLogout() {
            let res = await accountLogout(
                {
                    type: '8',
                    messageCode: this.checkCode,
                },
                { isCustomErr: true },
            );
            if (res.success) {
                this.toSuccess(res);
            } else {
                this.toFail(res);
            }
        },
        // #endif

        /*成功跳转页面 */
        toSuccess(res) {
            this.$sKit.layer.useRouter(
                '/packages/third-my-wallet/pages/card_cancel_results/main',
                { alertInfo: res, type: 'success' },
                'navigateTo',
            );
        },
        /*失败跳转页面 */
        toFail(res) {
            this.$sKit.layer.useRouter(
                '/packages/third-my-wallet/pages/card_cancel_results/main',
                { alertInfo: res, type: 'fail' },
                'navigateTo',
            );
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    components: {},
};
</script>
<style lang="scss" scoped>
.container {
    height: 100%;
    padding: 0 15px;

    .phone {
        text-align: left;
        font-size: 21px;
        line-height: 30px;
        margin-top: 40px;
    }

    .tipsInfo {
        text-align: left;
        font-size: 12px;
        color: #999999;
        margin-bottom: 10px;
    }

    .formBox {
        width: 100%;
        height: 44px;
        background: #fff;
        margin-bottom: 13px;
        margin-top: 40px;
        // display: flex;
        // flex-direction: row;
        // justify-content: space-between;
        // align-items: center;
        position: relative;

        input {
            width: 200px;
            height: 44px;
            border-radius: 8px;
            background: #fff;
            padding-left: 15px;
            box-sizing: border-box;
            // justify-content: flex-start;
        }

        .verificationBtn {
            width: 80px;
            height: 44px;
            text-align: right;
            line-height: 44px;
            // background: #fff;
            white-space: nowrap;
            // border-top-right-radius: 8px;
            // border-bottom-right-radius: 8px;
            color: #e64f22;
            // padding-right: 13px;
            font-size: 14px;
            position: absolute;
            right: 20px;
            top: 0;
            z-index: 2;
        }
    }

    .btnBox {
        height: 44px;
        margin-top: 16px;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        border-radius: 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        color: #ffffff;
    }
}

.tips_div {
    margin-top: 16px;
}
</style>
<style lang="scss">
.forgot_pas_inp {
    font-size: 14px;
    color: #999999;

    line-height: 20px;
}
</style>
