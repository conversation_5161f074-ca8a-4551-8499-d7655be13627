import { paycard, userAgreement } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import projectConfig, { mixRecharge } from '../../../../../../project.config';
export default {
    // #ifdef MP-WEIXIN
    mounted() {
        return;
        this.$sKit.wxPayPlugin.initPayPlugin();
    },
    methods: {
        // 获取地区充值方式的信息
        getAreaRecharge() {},
        /**
         * @description  : 微信支或支付宝充值
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {String} cardNo -卡号
         * @param         {String} money -金额
         * @param         {String} cardType -卡类型
         * @return        {*}
         */
        async confirmRecharge() {
            // 校验是否同意协议，是否选择支付方式，根据支付方式调用不同的支付逻辑
            if (!this.agreementFlag) {
                uni.showToast({
                    title: '请先勾选同意充值协议',
                    icon: 'none',
                });
                return;
            }
            if (this.paymentSelectIndex == '-1') {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                });
                return;
            }
            if (this.payType == 'card') {
                this.stopRecharge()
                return;
            }
            let money = this.rechargeMoney();
            if (!money) return;
            let params = {
                cardNo: this.thirdCardItemRecharge.cardNo,
                amount: this.money,
                cardType: '0',
            };
            let res = await paycard(params);
            uni.requestPayment({
                provider: 'wxpay',
                timeStamp: res.data.timeStamp,
                nonceStr: res.data.nonceStr,
                package: res.data.prepay,
                signType: res.data.signType,
                paySign: res.data.paySign,
                success: () => {
                    // 倒计时时间
                    this.nums = 6;
                    this.timer = setInterval(() => {
                        // 每次减一
                        this.nums--;
                        // 打开倒计时弹窗
                        this.popupShow = true;
                        if (this.nums == 0) {
                            this.$sKit.mpBP.tracker('充值', {
                                seed: 'rechargeBiz',
                                pageID: 'cardsucessToast', // 页面名
                                refer: this.thirdCardItemRecharge.refer || '', // 来源
                                channelID: 'C12', // C10/C12/C13
                                czMoney: this.money,
                                address: this.thirdCardItemRecharge.address, // 归属地
                            });
                            clearInterval(this.timer);
                            this.popupShow = false;
                            this.$store.dispatch('zjShowModal', {
                                content: '充值成功',
                                confirmText: '确定',
                                success(res) {
                                    uni.navigateBack({ detail: 1 });
                                    // 存储本地的用来刷新卡信息的标识
                                    uni.setStorageSync('refreshCardManagement', true);
                                },
                            });
                        }
                    }, 1000);
                },
                fail: function (err) {},
            });
        },
        /**
         * @description  : 匹配充值金额
         * @param         {String} moneyType -匹配充值类型的type
         * @return        {*}
         */
        rechargeMoney() {
            let money = '';
            if (this.moneyType == '1') {
                money = '200';
            } else if (this.moneyType == '2') {
                money = '300';
            } else if (this.moneyType == '3') {
                money = '500';
            } else if (this.moneyType == '4') {
                money = '800';
            } else if (this.moneyType == '5') {
                money = '1000';
            } else if (this.moneyType == '6') {
                money = '2000';
            } else {
                money = this.money;
            }
            if (!money) {
                uni.showToast({
                    title: '请输入充值金额',
                    icon: 'none',
                });
                return;
            }
            if (money < (mixRecharge || 1)) {
                uni.showToast({
                    title: '充值金额不能小于一元',
                    icon: 'none',
                });
                return;
            }
            return money;
        },
        /**
         * @description  : 点击查看协议
         * @param         {string} type -协议类型
         * @param         {string} cityName -城市编码
         * @param         {string} name -协议名称
         * @param         {Function} checkPDF -打开协议
         * @return        {*}
         */
        async goToagreement() {
            let params = {
                type: '1',
                cityName: '全国',
                name: 'App充值协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // uni.navigateTo({
                    //     url: '/packages/web-view/pages/home/<USER>' + encodeURIComponent(userAgreementRes.data.fileUrl),
                    // });
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            } else {
                uni.showToast({ title: userAgreementRes.message });
            }
        },
    },
    // #endif
};
