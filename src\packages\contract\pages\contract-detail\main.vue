<template>
    <div class="contract-detail-class">
        <z-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="合同详情"
            :back-text-style="pageConfig.titleStyle"
        ></z-navbar>
        <div class="item" v-for="(item, index) of itemDetail.detail" :key="index">
            <p>规则{{ index + 1 }}: {{ item.name }}</p>
            <p>梯度优惠条件/周期消费数据计算条款：{{ item.startTerms || '无' }}</p>
            <p>生效时间：{{ item.beginTime }} - {{ item.endTime }}</p>
            <p>默认优惠条件：{{ item.defTradeType }}</p>
            <p>默认优惠内容：{{ item.defResult }}</p>
            <p>合同名称：{{ item.mainJudgeName }}</p>
            <span>特殊优惠：</span>
            <div v-if="item.appRuleTexts.length">
                <p v-for="(smallItem, index) of item.appRuleTexts"> {{ index + 1 }}、{{ smallItem }} </p>
            </div>
            <span v-else>详情请拨打956100热线电话查询或咨询站点</span>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import {} from '@/api/home.js';

export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            itemDetail: null,
        };
    },
    onLoad(info) {
        this.itemDetail = JSON.parse(decodeURIComponent(info.htInfo));
        this.itemDetail.detail = this.itemDetail.detail ? this.itemDetail.detail : [];
        this.itemDetail.detail.forEach(item => {
            let appRuleTexts1 = item.appRuleTexts;
            if (!(appRuleTexts1 instanceof Array)) {
                item.appRuleTexts = appRuleTexts1 ? JSON.parse(appRuleTexts1) : [];
            }
        });
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.contract-detail-class {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    position: relative;
    display: flex;
    flex-direction: column;
    .item {
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 30px;
        p {
            padding: 3px 0;
        }
    }
}
</style>
