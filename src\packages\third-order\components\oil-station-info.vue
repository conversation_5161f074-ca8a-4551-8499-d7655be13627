<template>
    <div class="container">
        <div class="container-title">
            <img src="../image/logo.png" alt="" />
            <p class="single-line">{{ order.o2oName || '' }}</p>
        </div>
        <div class="container-time">
            <span v-if="order.stationStatus == '20'"> 正常营业 </span>
            <span v-if="order.stationStatus == '30' || order.stationStatus == '50'"> 暂停营业 </span>
            <span v-if="order.o2oBusinessStartTime && order.o2oBusinessEndTime">
                |&nbsp;&nbsp;{{ order.o2oBusinessStartTime }}-{{ order.o2oBusinessEndTime }}
            </span>
            <img src="../image/icon_nav.png" alt="" @click="handleNaviStateion(order)" />
        </div>
    </div>
</template>

<script>
import { bd09_To_Gcj02 } from '../../../s-kit/js/map';
export default {
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {
        // 点击打开导航
        handleNaviStateion(orderInfo) {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.openLocation({
                latitude: orderInfo.latitude,
                longitude: orderInfo.longitude,
                name: '',
                address: '',
            });
            // #endif
            // #ifndef MP-MPAAS
            let { longitude, latitude } = bd09_To_Gcj02(orderInfo.longitude, orderInfo.latitude);
            uni.openLocation({
                latitude: Number(latitude),
                longitude: Number(longitude),
                name: orderInfo.o2oName,
                address: orderInfo.address,
            });
            // #endif
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    &-title {
        display: flex;
        flex-direction: row;
        align-items: center;

        img {
            width: 24px;
            height: 24px;
        }
        p {
            font-weight: 500;
            margin-left: 6px;
        }
    }
    &-time {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-left: 30px;
        span {
            color: #666666;
            font-size: 13px;
        }
        img {
            width: 20px;
            height: 20px;
        }
    }
}
</style>
