
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <!--   引入该JS，全局注入getMetaInfo方法   -->
        <script
            type="text/javascript"
            src="https://cn-shanghai-aliyun-cloudauth.oss-cn-shanghai.aliyuncs.com/web_sdk_js/jsvm_all.js"
        ></script>
        <script type="text/javascript" src="https://appx/web-view.min.js"></script>
        <!-- // 如该 H5 页面需要同时在非 mPaaS 客户端内使用，为避免该请求 404，可参考以下写法 // 请尽量在 html 头部执行以下脚本 -->
        <script>
            if (navigator.userAgent.indexOf('AlipayClient') > -1 || navigator.userAgent.indexOf('mPaaSClient') > -1) {
                document.writeln('<script src="https://appx/web-view.min.js"' + '>' + '<' + '/' + 'script>');
            }
            // 判断是否运行在小程序环境里
            my.getEnv(function (res) {
              console.log(res.miniprogram); // true
            });
            // 在调用实人认证服务端发起认证请求时需要传入该MetaInfo值
            var MetaInfo = window.getMetaInfo();
            // 网页向小程序 postMessage 消息
            my.postMessage({'metaMessageInfo': MetaInfo});
            my.navigateBack({})
        </script>
        <title>扫脸认证</title>
    </head>
    <body>
       
    </body>
</html>
