import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
    methods: {
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        async selectStation() {
            let locationRes = await this.$cnpcBridge.checkPermission();
            console.log(locationRes, 'locationRes定位权限开启了吗');
            if (locationRes.appStatus) {
                let url = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
                let params = {
                    type: 'ekl', //昆仑e享卡
                };
                let type = 'navigateTo'; // 默认  uni.navigateTo({})
                this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                    if (res) {
                        this.$sKit.layer.useRouter(url, params, type);
                    }
                });
            } else {
                this.$cnpcBridge
                    .openPermissions({
                        code: 'location',
                        explain: '位置权限使用说明',
                        detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                    })
                    .then(res => {
                        if (res) {
                            console.log(res, '开通e享卡页面开启定位权限res');
                            let url = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
                            let params = {
                                type: 'ekl', //昆仑e享卡
                            };
                            let type = 'navigateTo'; // 默认  uni.navigateTo({})
                            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                                if (res) {
                                    this.$sKit.layer.useRouter(url, params, type);
                                }
                            });
                        }
                    });
            }
        },
        /**
         * @description  : 点击查看协议
         * @param         {string} type - 协议类型
         * @param         {string} cityName - 城市编码
         * @param         {string} name - 协议名称
         * @return        {*}
         */
        async getAgreeOn() {
            //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
            // name传
            /*
                App用户使用协议
                App隐私协议
                电子钱包开通协议
                App充值协议
            */
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: '1',
                    cityName: res.cityCode,
                    name: '昆仑e享卡开通协议',
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    }
                } else {
                    uni.showToast({ title: userAgreementRes.message || '未找到该协议' });
                }
            });
        },
    },
};
