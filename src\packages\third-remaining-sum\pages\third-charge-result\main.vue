<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details p-bf bg-F7F7FB">
            <zj-navbar title="昆仑e享卡充值" :border-bottom="false"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div bg-fff">
                    <!-- <img :src="amount  >  0 ? '../../images/recharge.png' :'../../images/consumption.png'" alt /> -->
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'success'">
                        <img src="../../images/success_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">您的操作已完成</div>
                        <div class="font-12 color-999 text">如已支付，请查看充值是否到账</div>
                    </div>
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'fail'">
                        <img src="../../images/fail_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">系统仍未查询到充值订单</div>
                        <div class="amount font-16 color-333 weight-bold">建议您联系956100客服</div>
                    </div>
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'waiting'">
                        <img src="../../images/wait_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">请根据实际情况</div>
                        <div class="amount font-16 color-333 weight-bold">点击下面按钮</div>
                    </div>
                </div>
                <div class="btn_div" v-if="status == 'fail'">
                    <div class="finish_verification2 color-E64F22 bg-fff" @click="backClick">返回</div>
                </div>
                <div class="btn_div fl-jus-bet" v-else-if="status == 'success'">
                    <div class="finish_verification btn-plain color-E64F22 font-16" @click="backClick2()">返回</div>
                    <div class="finish_verification primary-btn color-fff font-16" @click="backClick()">继续充值</div>
                </div>
                <div class="btn_div fl-jus-bet" v-else>
                    <div class="finish_verification btn-plain color-E64F22 font-16" @click="backClick()">未支付</div>
                    <div class="finish_verification primary-btn color-fff font-16" @click="finishBtn()">支付完成</div>
                </div>
                <zj-marketing-coupon v-if="status == 'success'"></zj-marketing-coupon>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
        <div v-if="maskPage">
            <zjMarket
                marketType="screenMask"
                ref="maskBanner"
                :payDoneMoney="paydonemoney"
                :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
                spaceType="recharge_page"
            ></zjMarket>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { rechargeStatusQuery } from '../../../../s-kit/js/v3-http/https3/wallet';
import zjMarket from '../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-charge-result',
    components: {
        // ZjOldAccount
        zjMarket,
    },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    data() {
        return {
            status: '',
            orderId: '',
            flag: 0,
            payType: '',
            paydonemoney: '',
            maskPage: false,
            refer: '',
            addressName: '',
        };
    },
    onLoad(option) {
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        this.orderId = queryInfo.orderId;
        this.payType = queryInfo.payType || '';
        this.refer = queryInfo.refer;
        this.addressName = queryInfo.addressName;
    },
    mounted() {
        setTimeout(() => {
            this.toResult(0);
        }, 1000);
    },
    methods: {
        /**
         * @description  : 查询充值结果
         * @param         {String} orderId -订单id
         * @return        {*}
         */
        async toResult(val) {
            //orderId  订单id
            // this.orderId
            uni.showLoading({});
            let res = await rechargeStatusQuery({ orderId: this.orderId }, { isCustomErr: true });
            uni.hideLoading();
            if (res.success && res.data.orderStatus == 4) {
                this.paydonemoney = Number(res.data.rechargeAmt);
                this.$sKit.mpBP.tracker('充值', {
                    seed: 'rechargeBiz',
                    pageID: 'eCardsucessPage', // 页面名
                    refer: this.refer || '', // 来源
                    channelID: clientCode, // C10/C12/C13
                    czMoney: this.paydonemoney,
                    address: this.addressName,
                });
                //4-充值成功；5-充值失败
                clearTimeout();
                this.status = 'success';
                this.$sKit.commonUtil.refreshWallet();
                this.maskPage = true;
                return;
            } else {
                clearTimeout();
                if (!this.flag) {
                    setTimeout(() => {
                        this.toResult();
                    }, 3000);
                    this.flag = 1;
                } else {
                    if (this.flag == 1) {
                        this.status = 'waiting';
                    } else {
                        this.status = 'fail';
                    }
                }
            }
        },
        //返回
        backClick() {
            if (this.payType == 'orderPay') {
                this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/third-wallet-recharge/main', { refer: 'r15' });
            } else {
                uni.navigateBack({ delta: 1 });
            }
        },
        //支付完成
        finishBtn() {
            this.flag = 2;
            this.toResult();
        },
        //返回
        backClick2() {
            let url = '';
            uni.setStorageSync('refreshWalletBlanaceFlag', true);
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver();
            // #endif
            // #ifdef MP-WEIXIN || H5-CLOUD
            url = '/pages/thirdHome/main';
            this.$sKit.layer.useRouter(url, { curTabIndex: 1 }, 'reLaunch');
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/thirdHome/main';
            this.$sKit.layer.useRouter(url, { qKey: 'wallet' }, 'reLaunch');
            // #endif
        },
        // getCharge () {
        //   uni.navigateBack({ delta: 2 })
        // }
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-bottom: 60px;
            padding-top: 60px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 12px;
            }

            .amount {
                line-height: 23px;
            }

            .text {
                line-height: 23px;
            }
        }
    }
}

.btn_div {
    margin-top: 20px;
    font-size: 15px;
    display: flex;
    flex-direction: row;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
