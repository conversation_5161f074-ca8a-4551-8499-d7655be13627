
<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="确认订单"></zj-navbar>
            <div class="p-bf bg-F7F7FB padding-16 f-1">
                <div>
                    <div class="money bg-fff border-rad-8">
                        <div class="title font-15 weight-400 color-333 te-center">{{ payType == 15 ? '待付积分' : '待付金额' }} </div>
                        <div class="fl-row num fl-jus-cen fl-al-base font-style">
                            <div class="font-36 weight-600 color-333" v-if="payType != 15">&yen;</div>
                            <div class="font-50 weight-600 color-333">
                                {{ payType != 15 ? actualPayTotalAmount : accumulatedPoint }}
                                <span v-if="payType == 15" class="order-amount-price-tag font-36">积分</span>
                            </div>
                        </div>
                    </div>
                    <div
                        :class="{ btnColor: isCanClickPay_paymentCode, 'bg-opacity-288': !isCanClickPay_paymentCode }"
                        class="confirmBtn primaryBtn color-fff fl-row fl-al-jus-cen confirmRecharge border-rad-8"
                        @click="confirmPay()"
                    >
                        <div>确认支付</div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <loginRiskControl v-if="realNameDialogFlag || facePop" @verificationPassed="verifSuccess"></loginRiskControl>
        </div>
    </div>
</template>

<script>
import checkFKArgs from '../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import { statuslOrderApi, savedOrderApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { mapState, mapGetters } from 'vuex';
import projectConfig from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'confir-code-order',
    components: {},
    props: {},
    data() {
        return {
            // 付款码支付方式支付信息
            codePayMentInfo: {},
            // 付款码查询到的订单信息
            codeOrderInfo: {},
            // 待支付金额
            actualPayTotalAmount: 0,
            // 支付类型 1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；6：加油卡；7：信用账户；8：云闪付；9：和包支付；10：翼支付；11：优惠券；12：礼品卡；13：油币；14：能源币；15：积分；29：现金；
            payType: 0,
            //积分支付 积分数量展示
            accumulatedPoint: 0,
            // 查询订单定时器Timer
            requestStatusInterval: null,
            // 每隔多少秒查询一次
            pollTime: 3000,
            // 开始时间
            beginTime: 0,
            // 支付信息
            payTypeInfo: null,
            //支付成功后的订单详情
            orderDetail: {},

            //未实人认证加油时需要弹出输入身份证号和姓名的弹窗
            realNameDialogFlag: false,
            // 是否能点击确认支付
            isCanClickPay_paymentCode: true,
        };
    },
    computed: {
        ...mapGetters(['walletInfo']),
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
            facePop: state => state.thirdIndex.facePop,
        }),
    },
    created() {},
    onLoad(options) {
        // 设置节流，防止用户在短时间内频繁点击
        this.confirmPay = this.$sKit.commonUtil.throttleUtil(this.confirmPay, 3000);
        // 确定订单页面携带过来的数据
        let info = JSON.parse(decodeURIComponent(options.data));
        // 付款码支付订单信息
        this.codeOrderInfo = info.orderInfo;
        // 付款码支付信息
        this.codePayMentInfo = info.payTypeInfo;
        // 待支付金额
        this.actualPayTotalAmount = this.codeOrderInfo.payMoney;
        this.$sKit.mpBP.tracker('付款码', {
            seed: 'fkCodeBiz',
            pageID: 'paidOrderPage', // 页面名
            refer: info.orderInfo.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
            address: this.cityName,
        });
    },
    methods: {
        /**
         * @description  : 确认支付
         * @param         {String} stationCode -站编码
         * @param         {String} bizOrderNo -业务订单编号
         * @param         {String} rcvAmt -应收总金额
         * @param         {String} realAmt -支付金额
         * @param         {String} payType -支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；
         * @param         {String} bizDay -营业日
         * @param         {String} extendFiled -风控字段（json字符串透传，不校验里面内容）
         * @param         {Array} insidePayList -账户预支付场景：内部支付方式列表(组合支付)
         * @param         {String} accountNo -昆仑e享卡编码
         * @param         {Function} biometricPay -生物识别
         * @param         {Function} rposCodePay -发起pos付款码支付
         * @param         {Function} resStatus -查询支付状态
         * @param         {Function} oilTriggerRisk -是否进行过实名认证
         * @return        {*}
         */
        async confirmPay(isAuth = false) {
            if (!this.isCanClickPay_paymentCode) return;
            this.isCanClickPay_paymentCode = false;
            try {
                //TODO 待测试
                //         String stationCode 是 站编码
                //         String bizOrderNo 是 业务订单编号
                //         String rcvAmt 是 应收总金额
                //         String realAmt 是 支付金额
                //         String payType 是 支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；
                //         String bizDay 否 营业日
                //         String extendFiled 是 风控字段（json字符串透传，不校验里面内容）
                //         Array insidePayList 是 账户预支付场景：内部支付方式列表(组合支付)
                let paras = {
                    stationCode: this.codeOrderInfo.stationCode,
                    bizOrderNo: this.codeOrderInfo.orderId,
                    rcvAmt: this.codeOrderInfo.orderAmount + '',
                    realAmt: this.codeOrderInfo.payMoney + '',
                    payType: this.codePayMentInfo.payType + '',
                    bizDay: this.codeOrderInfo.businessDay,
                    extendFiled: JSON.stringify(await checkFKArgs.getFKArgs('sdk', isAuth)),
                    insidePayList: [],
                    accountNo: this.walletInfo.ewalletNo || '',
                };
                uni.showLoading({
                    title: '支付中...',
                    mask: true,
                });
                // 判断当前加油卡或者是昆仑e享卡 5是昆仑e享卡 6是加油卡
                if (this.codePayMentInfo.payType == '6' || this.codePayMentInfo.payType == '5') {
                    paras.cardIdx = this.codePayMentInfo.cardSequence + '';
                    // 加油卡传卡号
                    if (this.codePayMentInfo.payType == '6') paras.fuelCardNo = this.codePayMentInfo.cardNo;
                }
                // 是否有电子券
                if (this.codeOrderInfo.couponList && this.codeOrderInfo.couponList.length > 0) {
                    paras.insidePayList.push({
                        payType: '11',
                        payAmt: this.codeOrderInfo.couponDiscount + '',
                        couponNo: this.codeOrderInfo.couponList[0].couponNo,
                        couponTemplateNo: this.codeOrderInfo.couponList[0].couponTemplateNo,
                    });
                }

                let obj = await this.$sKit.commonUtil.biometricPay(this.codeOrderInfo.orderId, this.codeOrderInfo.payMoney, !isAuth);
                Object.assign(paras, obj);

                // this.$LoadingNew.open();
                this.$paymentCenter.rposCodePay({ paramsJsonStr: encodeURIComponent(JSON.stringify(paras)) }, res => {
                    if (this.$paymentCenter.resStatus(res)) {
                        let URL = `/packages/third-scan-code-payment/pages/query-payment-results/main`;
                        let params = this.codeOrderInfo;
                        let type = 'redirectTo';
                        this.$sKit.layer.useRouter(URL, params, type);
                        uni.hideLoading();
                    } else if (this.$paymentCenter.resAuthStatus(res)) {
                        this.isCanClickPay_paymentCode = true;
                        //需要实人认证
                        this.$sKit.commonUtil.oilTriggerRisk().then(res => {
                            uni.hideLoading();
                            // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                            if (res == 13) {
                                // 打开实人认证的表单弹窗
                                this.realNameDialogFlag = true;
                            } else {
                                // 如果不是13的时候认证成功后唤起密码键盘支付
                                this.confirmPay(true);
                            }
                        });
                    } else {
                        this.isCanClickPay_paymentCode = true;
                        uni.hideLoading();
                        uni.showToast({ title: res.msg || '支付失败' });
                    }
                });
            } catch (error) {
                uni.hideLoading();
            } finally {
                // uni.hideLoading();
            }
        },
        /**
         * @description  : 实人认证弹窗的确认事件
         * @param         {String} name -姓名
         * @param         {String} idNumber -身份证号
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameInfo(val) {
            this.$sKit.commonUtil
                .triggerRiskAuth(val.name, val.idNumber)
                .then(res => {
                    this.realNameDialogFlag = false;
                    this.confirmPay(true);
                })
                .catch(err => {
                    uni.showToast({ title: err });
                });
        },
        /**
         * @description  : 关闭实人认证弹窗
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameDialogClose() {
            this.realNameDialogFlag = false;
        },
        /**
         * @description  : 查询已支付完成的订单详情
         * @param         {String} orderNo -打开获关闭弹窗
         * @return        {*}
         */
        getSavedOrderDetail() {
            return;
            let params = {
                orderNo: this.codeOrderInfo.orderId,
            };
            savedOrderApi(params, { isload: false }).then(res => {
                if (res.data) {
                    // 查询到支付结果后变为false 防止递归继续查询
                    this.stopRecursionFlag = false;
                    this.orderDetail = res.data;
                    let url = '/packages/third-scan-code-payment/pages/code-order-payment-code-results/main';
                    let params = res.data;
                    let type = 'redirectTo'; // 默认  uni.navigateTo({})
                    this.$sKit.layer.useRouter(url, params, type);
                    uni.hideLoading();
                } else {
                    uni.hideLoading();
                }
            });
        },
        /**
         * @description  : 查询订单状态
         * @param         {String} orderNo -打开获关闭弹窗
         * @param         {String} stationCode -油站编码
         * @return        {*}
         */
        queryOrderPayStatus() {
            return;
            // uni.showLoading({
            //     title: '加载中',
            //     mask: true,
            // });
            if (this.stopRecursionFlag) {
                statuslOrderApi(
                    {
                        stationCode: this.codeOrderInfo.stationCode,
                        orderNo: this.codeOrderInfo.orderId,
                    },
                    { isload: false },
                )
                    .then(result => {
                        if (result.success) {
                            if (result.data.orderPayStatus == 4) {
                                //订单支付状态 1待支付；2支付失败；3部分支付；4全部支付  13 部分退款  14 全部退款
                                //成功要跳转
                                this.getSavedOrderDetail();
                            } else {
                                // 清除定时器
                                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                                // 查询一分钟后如果未查询到订单弹出提示
                                if (new Date().getTime() - this.beginTime > 60 * 1000) {
                                    // this.$LoadingNew.close();
                                    // this.$dialog.alert({ message: '未查询到支付结果，请去我的订单中查看订单状态！' });
                                    uni.showToast({ title: '未查询到支付结果，请去我的订单中查看订单状态！' });
                                    uni.hideLoading();
                                    return;
                                }
                                this.requestStatusInterval = setTimeout(() => {
                                    this.queryOrderPayStatus();
                                }, this.pollTime);
                            }
                        } else {
                            if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                            this.requestStatusInterval = setTimeout(() => {
                                this.queryOrderPayStatus();
                            }, this.pollTime);
                        }
                    })
                    .catch(error => {
                        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                        // 查询一分钟后如果未查询到订单弹出提示
                        if (new Date().getTime() - this.beginTime > 60 * 1000) {
                            // this.$LoadingNew.close();
                            // this.$dialog.alert({ message: '未查询到支付结果，请去我的订单中查看订单状态！' });
                            uni.hideLoading();
                            uni.showToast({ title: '未查询到支付结果，请去我的订单中查看订单状态！' });
                        } else {
                            this.requestStatusInterval = setTimeout(() => {
                                this.queryOrderPayStatus();
                            }, this.pollTime);
                        }
                    });
            } else {
                uni.hideLoading();
            }
        },
    },
    filter: {},
    watch: {},
};
</script>

<style lang="scss" scoped>
.view {
    .money {
        width: 100%;
        height: 212px;
        display: inline-block;

        .title {
            margin-top: 60px;
            line-height: 22px;
        }

        .num {
            line-height: 50px;
            margin-top: 16px;
        }
    }

    .money-result {
        width: 100%;
        height: 350px;
        position: relative;
        // 加这行代码是为了解决子元素添加margin-top影响父元素；上下盒子添加外边距重叠问题
        display: inline-block;
        margin-bottom: 16px;

        .title {
            width: 64px;
            height: 64px;
            margin-top: 50px;
            margin: 60px auto 0;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .num {
            line-height: 50px;
            margin-top: 16px;
            margin-bottom: 30px;
        }

        .text {
            margin-bottom: 15px;
        }

        .text2 {
            margin-bottom: 42.5px;
        }
    }

    .confirmBtn {
        margin-top: 12px;
        width: 100%;
        height: 44px;
    }
    .primaryBtn {
        // background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        text-align: center;
    }
    .btnColor {
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }
    .confirmPaymentBg {
        background-color: lightgray;
    }
}
</style>
