<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <zj-navbar title="实人认证结果" :border-bottom="false" :customBack="closeEvent"></zj-navbar>

        <div class="content_div" :style="{ top: topHeight + 'px' }">
            <img class="icon_img" src="../../image/success_img.png" alt="" />
            <span class="title">认证成功</span>
            <span class="text">您每月仅限使用实人认证登录{{ limitLoginTimes }}次，剩余{{ leftLoginTimes }}次</span>
            <span class="text">为不影响您的使用，请更换绑定手机号</span>
            <div class="btn_div">
                <div class="btn btn_left" @click="unchangingChange">暂不变更</div>
                <div class="btn btn_right" @click="confirmChanges">确认变更</div>
            </div>
        </div>

        <zj-show-modal>
            <div class="fl-column fl-al-jus-cen font-14 color-333 weight-400" v-if="isMore">
                <div class="fl-row fl-al-cen">1、{{ frequency }}</div>
                <div class="fl-row fl-al-cen" v-if="couponHave">2、您当前账户下有未使用完的电子券，建议您使用完后进行变更手机号。</div>
            </div>
            <div class="fl-column fl-al-jus-cen font-14 color-333 weight-400" v-else>
                <div class="fl-row fl-al-cen">您今年已变更手机号 2次，剩余更换次数为0，无法变更手机号。</div>
            </div>
        </zj-show-modal>
    </div>
</template>
<script>
import { userModifyPhoneTimesQuery, userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { appJsonCouponUnusedcoupons } from '../../../../s-kit/js/v3-http/https3/wallet';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            topHeight: 0,
            popShow: false,
            token: '',
            couponHave: '',
            frequency: '',
            limitLoginTimes: '',
            leftLoginTimes: '',
            identityAuthStatus: '',
            isMore: true,
        };
    },
    onLoad(option) {
        this.limitLoginTimes = JSON.parse(decodeURIComponent(option.data)).limitLoginTimes;
        this.leftLoginTimes = JSON.parse(decodeURIComponent(option.data)).leftLoginTimes;
    },
    created() {},
    async mounted() {
        // #ifdef MP-MPAAS
        let userInfo = await this.$cnpcBridge.getUserTokenInfo(); // 获取用户信息
        this.token = userInfo.token;
        this.getuserModifyPhoneTimesQuery(); //查询用户修改手机号次数是否超限
        this.getuserBasicInfoQuery(); //查询用户详细信息--获取用户认证状态
        this.getcouponList(); //查询有没有未使用的电子券
        // #endif
    },

    methods: {
        //关闭当前小程序
        closeEvent() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
        },
        //查询用户详细信息--获取用户认证状态
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.identityAuthStatus = res.data.identityAuthStatus;
            }
        },
        //查询用户修改手机号次数是否超限
        async getuserModifyPhoneTimesQuery() {
            let res = await userModifyPhoneTimesQuery();
            if (res && res.success) {
                this.isMore = !res.data.result;
                if (this.isMore) {
                    this.frequency = res.data.msg;
                }
            }
        },
        //查询有没有未使用的电子券
        async getcouponList() {
            let params = {
                pageNum: 1,
                pageSize: 10,
                categoryAlias: '',
                token: this.token,
                sortName: '',
                sortType: '',
            };

            let res = await appJsonCouponUnusedcoupons(params);
            if (res && res.data) {
                this.couponHave = res.data.length > 0 ? true : false;
            }
        },
        // 暂不变更事件
        unchangingChange() {
            this.closeEvent();
        },
        // 确认变更
        confirmChanges() {
            let url;
            let params = {};
            let type = 'navigateTo';
            let confirmText, cancelText;
            if (this.isMore) {
                confirmText = '我知道了';
                cancelText = '继续变更';
            } else {
                cancelText = '';
                confirmText = '确认';
            }
            let _this = this;
            this.$store.dispatch('zjShowModal', {
                confirmText: confirmText,
                cancelText: cancelText,
                cancelColor: '#333333',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击取消');
                    } else if (res.cancel) {
                        if (_this.isMore) {
                            if (_this.couponHave) {
                                //判断用户有无未使用的电子券
                                url = '/packages/third-account/pages/change-phone-result/main'; //去电子券提示页
                            } else {
                                if (_this.identityAuthStatus == 15) {
                                    url = '/packages/third-my-center/pages/real-person/main'; //去实人认证
                                } else {
                                    url = '/packages/third-account/pages/account-phone-verify/main'; //去短信验证码验证
                                }
                            }

                            _this.$sKit.layer.useRouter(url, params, type);
                        }
                    }
                },
            });
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
};
</script>
<style scoped lang="scss">
.content_div {
    padding: 0 15px;
    padding-top: 40px;
    // position: absolute;
    // right: 0;
    // left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .icon_img {
        width: 64px;
        height: 64px;
    }
    .title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
        padding: 16px 0;
    }
    .text {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 18px;
    }
    .btn_div {
        width: 100%;
        display: flex;
        margin-top: 37px;
        justify-content: space-between;
    }
}

.btn {
    width: 48%;
    height: 44px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 44px;
}
.btn_left {
    color: #e64f22;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e64f22;
}
.btn_right {
    color: #ffffff;
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0px 1 10px 0px rgba(0, 0, 0, 0.07);
    border-radius: 8px;
}
.confirm_btn {
    font-weight: bold;
}

.pop_div {
    width: 100%;
    height: 100%;
    position: relative;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 999;
    background: rgba(0, 0, 0, 0.6);
}

.pop_div_content {
    width: 280px;
    background: #fff;
    border-radius: 10px;
    position: absolute;
    top: 50%;
    left: 50%;

    transform: translate(-50%, -50%);
}
.pop_div_content_top {
    padding: 26px 20px;
    font-size: 14px;
    text-align: center;
    div {
        line-height: 20px;
    }
}

.pop_div_content_bottom {
    border-top: 1px solid #efeff4;
    display: flex;
    width: 100%;
    height: 45px;
    flex-direction: row;
    justify-content: space-between;
}
.pop_btn {
    text-align: center;
    line-height: 45px;
    height: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.cancel_btn {
    border-right: 2rpx solid #efeff4;
}

.icon-back {
    border: 1px solid #333;
    border-width: 0px 1px 1px 0px;
    display: inline-block;
    padding: 5px;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.nav_div {
    width: 100%;
    height: 44px;
    background: #fff;
}
</style>
