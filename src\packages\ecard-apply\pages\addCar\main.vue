<template>
    <div class="view">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :back-text="flag === '1' ? '编辑车辆' : '添加车辆'"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- 车牌号 -->
        <view class="content">
            <div style="background: #fff; margin-top: 20px; padding-bottom: 14px">
                <div>
                    <div class="left setpadding">
                        车牌号码
                        <div class="danger">*</div>
                    </div>
                    <CarNumberPlateNew class="garNumberPlateNew" ref="carNo" @markCar="markCar"></CarNumberPlateNew>
                </div>
            </div>
            <!-- 中间表单 -->
            <div style="background: #fff; margin-top: 10px">
                <!-- 油品品号 -->
                <div class="section-box">
                    <div class="left">
                        油品品号
                        <div class="danger">*</div>
                    </div>
                    <div @click="clickOilProductNo" class="right">
                        <input disabled class="center" placeholder="请选择油品品号" v-model.trim="mydata.productNoLabel" />
                    </div>

                    <!-- 点击油品品号弹出的下拉选 -->
                    <u-select v-model="selectShow" @confirm="productNoConfirm" :list="multiSelector"></u-select>
                    <u-icon class="uicon-arrow-right" name="arrow-right" @click="clickOilProductNo" v-if="selectShow === false"></u-icon>
                    <u-icon class="uicon-arrow-right" name="arrow-down" @click="clickOilProductNo" v-if="selectShow"></u-icon>
                </div>
                <!-- 车牌颜色 -->
                <div class="section-box">
                    <div class="left">
                        车牌颜色
                        <div class="danger">*</div>
                    </div>
                    <div @click="clickLicensePlateColor" class="right">
                        <input class="center" disabled placeholder="请选择车牌颜色" v-model.trim="mydata.licensePlateColorLabel" />
                    </div>
                    <!-- 点击车牌颜色弹出的下拉选 -->
                    <u-select v-model="licensePlateColorShow" @confirm="selectProductNo" :list="carNoColors"></u-select>
                    <u-icon
                        class="uicon-arrow-right"
                        name="arrow-right"
                        @click="clickLicensePlateColor"
                        v-if="licensePlateColorShow === false"
                    ></u-icon>
                    <u-icon
                        class="uicon-arrow-right"
                        name="arrow-down"
                        @click="clickLicensePlateColor"
                        v-if="licensePlateColorShow"
                    ></u-icon>
                </div>
            </div>
        </view>
        <!-- 设为默认车辆 -->
        <div class="center defaultVehicle">
            <div class="defaultVehicle-txt">设为默认车辆</div>
            <u-switch class="right" @change="setDefaultVehicle" v-model="checked"></u-switch>
        </div>
        <!-- 底部保存按钮 -->
        <div class="selectOrAddCar">
            <view @click="selectCar">
                <div class="car sett">保存</div>
            </view>
            <view v-if="flag === '1'" @click="deleteCard">
                <div class="car setDelete">删除</div>
            </view>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
// import {
//   getCarList,
//   editCarInfo,
//   setDefaultCar,
//   deteleDefdaultCar,
// } from "@/api/home.js";
import { addLicensePlate, editLicensePlate, deleteLicensePlate } from '@/api/anEcardToOpenCard.js';
import CarNumberPlateNew from '../../components/CarNumberPlateNew.vue';
import pageConfig from '@/utils/pageConfig.js';
export default {
    name: 'addCar',
    components: {
        CarNumberPlateNew,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            selectShow: false, // 选择油品品号
            licensePlateColorShow: false, //  选择车牌颜色
            checked: false, // 默认选择车辆
            cardNo: '', // 车牌号
            // 油品品号 与 车牌颜色的  value值和label值
            mydata: {
                productNoLabel: '',
                productNoValue: '',
                licensePlateColorLabel: '',
                licensePlateColorValue: '',
            },
            multiSelector: [
                { value: '300668', label: '92#汽油' },
                { value: '300667', label: '95#汽油' },
                { value: '300684', label: '98#汽油' },
                { value: '300644', label: '0#柴油' },
                { value: '300568', label: '-10#柴油' },
                { value: '300567', label: '-20#柴油' },
                { value: '300575', label: '-35#柴油' },
            ],
            carNoColors: [
                { value: '0', label: '蓝色' },
                { value: '1', label: '黄色' },
                { value: '2', label: '黑色' },
                { value: '3', label: '白色' },
                { value: '4', label: '渐变绿色' },
                { value: '5', label: '黄绿双拼色' },
                { value: '6', label: '蓝白渐变色' },
                { value: '11', label: '绿色' },
                { value: '12', label: '红色' },
                { value: '9', label: '未确定' },
            ],
            isDefault: '', //  设置默认车辆
            flag: '', //  给u-navbar 做标识
            cardId: '', // 车辆id \
        };
    },
    computed: {
        // ...mapState({
        //   selectMarker: (state) => state.location.selectMarker,
        // }),
        ...mapGetters(['cardList', 'isHaveEntityCard', 'cardTopinfo']),
        ...mapState({}),
    },
    onLoad(item) {
        // console.log(JSON.parse(item.item), 'CCCCCCCCCC');
        if (JSON.stringify(item) !== '{}') {
            this.editCard(item);
        }
    },
    methods: {
        // 点击油品品号控制显隐
        clickOilProductNo() {
            this.selectShow = !this.selectShow;
        },
        // 点击油品品号拿到的值
        productNoConfirm(e) {
            this.mydata.productNoLabel = e[0].label;
            this.mydata.productNoValue = e[0].value;
        },
        // 点击车牌颜色控制显隐
        clickLicensePlateColor() {
            this.licensePlateColorShow = !this.licensePlateColorShow;
        },
        // 点击车牌颜色拿到的值
        selectProductNo(e) {
            this.mydata.licensePlateColorLabel = e[0].label;
            this.mydata.licensePlateColorValue = e[0].value;
        },
        // 选择默认车辆
        setDefaultVehicle(e) {
            e === true ? (this.isDefault = 1) : (this.isDefault = 0);
        },
        // 保存车辆
        selectCar() {
            if (this.mydata.productNoValue == '') {
                uni.showToast({
                    title: '请选择油品品号',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.mydata.licensePlateColorValue == '') {
                uni.showToast({
                    title: '请选择车牌颜色',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.cardNo == '') {
                uni.showToast({
                    title: '车牌号不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            const carNoValid =
                /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{6})|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/.test(
                    this.cardNo,
                );
            if (!carNoValid) {
                uni.showToast({
                    title: '车牌号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.cardId) {
                let params = {
                    token: this.$store.state.token,
                    // id: "", // 车辆id（车辆表主键id，新增车辆时传空）
                    carNo: this.cardNo, // 车牌号
                    carNoColor: this.mydata.licensePlateColorValue, // 车牌颜色
                    oilNo: this.mydata.productNoValue, // 常用油品
                    isDefault: this.isDefault === 1 ? 1 : 0, //  是否默认（1:默认 0:非默认）
                };
                addLicensePlate(this.cardId === '' ? params : editParams).then(res => {
                    if (res.status === 0) {
                        console.log(res, '添加成功');
                        uni.navigateBack({
                            //返回
                            delta: 1,
                        });
                    }
                });
            } else {
                let params = {
                    token: this.$store.state.token,
                    carId: this.cardId, // 车辆id
                    carNo: this.cardNo, // 车牌号
                    carNoColor: this.mydata.licensePlateColorValue, // 车牌颜色
                    oilNo: this.mydata.productNoValue, // 常用油品
                    isDefault: this.isDefault === 1 ? 1 : 0, //  是否默认（1:默认 0:非默认）
                };

                editLicensePlate(params).then(res => {
                    if (res.status === 0) {
                        // 路由跳转
                        uni.navigateBack({
                            //返回
                            delta: 1,
                        });
                    }
                });
            }
            // this.backPageRefresh();
        },
        // 返回刷新
        backPageRefresh() {
            // ;
            // // 获取当前页上的栈（数组形式）
            // var pages = getCurrentPages();
            // //上一页面
            // var prevPage = pages[pages.length - 2]; // （pages.length - 3 上上页 pages.length - 1当前页，以此类推）
            // prevPage.setData({
            //   //直接给上一个页面赋值
            //   chooseName: name, // 注意： name必须是A页面data定义过得字段
            // });
            // // 返回上一页
            // wx.navigateBack({ delta: 1 });

            var pages = getCurrentPages();
            var prevPage = pages[pages.length - 2];
            // #ifdef MP-WEIXIN
            // 上边这行注释别删！！！！！！！！！！！！！！！！！！！！！
            console.log(prevPage, 'prevPageprevPageprevPage');
            prevPage.$vm.testdata.productNoValue = this.mydata.productNoValue;
            prevPage.$vm.testdata.licensePlateColorValue = this.mydata.licensePlateColorValue;
            // #endif
            uni.navigateBack({
                //返回
                delta: 1,
            });
        },
        // 车牌号
        markCar(e) {
            this.cardNo = e;
            console.log(e, 'KKKKKKKKKKKKKKKKKKKKK');
        },
        // 整理编辑车辆的数据
        editCard(item) {
            // 唯一标识 抬头是否显示编辑车辆 与  是否显示删除按钮
            this.flag = '1';
            // 接受路由跳转传递过来的参数
            let itemObj = JSON.parse(item.item);
            // 编辑时的车辆ID  做编辑或者删除时使用
            this.cardId = itemObj.carId;

            // 车牌号

            itemObj.carNo.split('').map((item, index) => {
                this.$refs.carNo.curNumberPlate.splice(index, 1, item);
            });
            console.log(this.$refs.carNo.curNumberPlate, 'this.$refs.carNo.curNumberPlate');
            // 是不是默认车辆
            itemObj.isDefault === 1 ? (this.isDefault = 1) : (this.isDefault = 0);
            itemObj.isDefault === 1 ? (this.checked = true) : (this.checked = false);
            // 回显遍历赋值油品品号
            this.multiSelector.map(ite => {
                if (ite.value === itemObj.oilNo) {
                    this.mydata.productNoLabel = ite.label;
                    this.mydata.productNoValue = ite.value;
                }
            });
            // 回显遍历赋值车牌颜色
            this.carNoColors.map(ite => {
                if (ite.value === itemObj.carNoColor) {
                    this.mydata.licensePlateColorValue = ite.value;
                    this.mydata.licensePlateColorLabel = ite.label;
                }
            });
        },
        // 删除车辆
        deleteCard() {
            // this.modeShow = true;
            // wx.showModal({
            //   title: '提示',
            //   content: '您确定要删除当前车辆信息吗',
            //   confirmText: '确定',
            //   confirmColor: '#FF8200',
            //   showCancel: true,
            //   success: function (res) {
            //     if (res.confirm) {
            //
            //
            //     } else if (res.cancel) {
            //       // 目前什么也不做处理
            //     }
            //   }
            // })
            uni.showModal({
                title: '提示',
                content: '您确定要删除当前车辆信息吗',
                confirmColor: '#FF8200',
                showCancel: true,
                success: res => {
                    if (res.confirm) {
                        let params = {
                            token: this.$store.state.token,
                            carId: this.cardId,
                        };
                        console.log('AAAAA删除车辆');
                        deleteLicensePlate(params).then(res => {
                            if (res.status === 0) {
                                uni.navigateBack({
                                    //返回
                                    delta: 1,
                                });
                            }
                        });
                    } else {
                        console.log('AAAAA删除车辆取消');
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    .content {
        margin: 10px;
        .section-box {
            border-radius: 4px;
            padding: 24rpx;
            border-top: solid 2rpx #eee;
            display: flex;
            &.title {
                font-size: 32rpx;
                border-top: none;
            }
            .left {
                font-size: 34rpx;
                width: 180rpx;
                display: flex;
                div {
                    color: #f43d45;
                }
            }
            .right {
                flex: 1;
                justify-content: flex-end;
                text-align: right;
                padding-right: 20px;
            }
            .type {
                display: flex;
                div:first-child {
                    margin-right: 56rpx;
                }
                img {
                    display: inline-block;
                    width: 44rpx;
                    height: 44rpx;
                    margin-right: 8rpx;
                }
            }

            .oil-money-info {
                color: $btn-color;
                // font-size: $font14;
                font-size: 17px;
                // color: #4b6b99;
            }
        }
        .setpadding {
            padding-bottom: 5px;
        }
        .left {
            font-size: 15px;
            color: #333;
            width: 30%;
            display: flex;
            align-items: center;
            min-width: 80px;
            .danger {
                padding-top: 4px;
            }
            div {
                color: #f43d45;
            }
        }
    }
    .defaultVehicle {
        background: #fff;
        padding: 0 8px;
        margin: 10px;
        flex: 1;
        height: 88rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        .defaultVehicle-txt {
            font-size: 16px;
            color: #333;
            //padding-bottom: 3px;
        }
        .defaultVehicle-carNo {
            font-size: 13px;
            color: #999999;
        }
    }

    .selectOrAddCar {
        position: absolute;
        width: 100%;
        padding: 10px;
        border-radius: 4px;
        .car {
            width: 100%;
            height: 46px;
            margin: 0 auto;
            font-size: 16px;
            background: #f96702;
            text-align: center;
            line-height: 46px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .sett {
            color: #fff;
        }
        .setDelete {
            background: #00000000;
            border: 1px solid #f96702;
        }
    }
}
</style>
