<template>
    <view class="dialog-login" v-if="dialogShow">
        <view class="dialog-login-main">
            <view class="dialog-login-content">
                <view class="title">{{ type === 1 ? '支付宝授权登录' : '注册' }}</view>
                <view class="center">
                    <view class="subtitle">授权手机号</view>
                    <view class="phone">{{ phone || '获取手机号失败' }}</view>
                </view>
                <button class="btn" v-if="1" @click="subAction">一键{{ type === 1 ? '登录' : '注册' }}</button>
                <button class="btn" v-else open-type="getAuthorize" @onGetAuthorize="onGetAuthorize" scope="userInfo">
                    一键{{ type === 1 ? '登录' : '注册' }}
                </button>
            </view>
            <view class="dialog-close" @click="closeDialog"></view>
        </view>
    </view>
</template>
<script>
import { loginBindAccount, loginRegisterByPhone, loginToGsms } from '@/s-kit/js/v3-http/https3/user';
import projectConfig from '../../../project.config';

const environment = {
    '/gsms-dev/': 'http://************:30120',
    '/gsms-qas/': 'http://************:30120',
    '/gsms-sit/': 'http://************:30120',
    '/gsms-uat/': 'http://***********:30120',
    '/gsms-mpaas-default/': 'http://*************:20100',
    '/gsms-mpaas-sit/': 'http://*************:20100',
};
export default {
    mixins: [],
    data() {
        return {
            dialogShow: false,
            silence: false, // 静默一键登录
            type: 0, // 1 登录 2 注册
            phone: '',
            authSetting: {},
        };
    },
    props: {
        changePhone: true,
        // 更换其他手机号
        onChangePhone() {
            // my.navigateTo({url: ''})
        },
    },
    async mounted() {
        let { authSetting } = await this.getSettingSync();
        this.authSetting = authSetting;
    },
    methods: {
        // 获取授权状态
        async getSettingSync() {
            return new Promise((resolve, reject) => {
                my.getSetting({
                    success: res => {
                        resolve(res);
                    },
                    fail(err) {
                        reject(err);
                    },
                });
            });
        },
        // 授权同步获取小程序密文手机号
        getPhoneNumberSync() {
            return new Promise((resolve, reject) => {
                my.getPhoneNumber({
                    success: res => {
                        resolve(res.response);
                    },
                    fail: res => {
                        console.log('getPhoneNumber fail', res);
                        resolve('');
                    },
                });
            });
        },
        // 授权获取小程序静默authBaseCode
        getAuthBaseCodeSync(refresh = false) {
            const authCode = uni.getStorageSync('authBaseCode');
            if (!refresh && authCode) return authCode;
            return new Promise((resolve, reject) => {
                my.getAuthCode({
                    scopes: ['auth_base'], // 静默授权
                    success: res => {
                        uni.setStorageSync('authBaseCode', res.authCode);
                        resolve(res.authCode);
                    },
                    fail: err => {
                        console.log(err);
                        console.log('getAuthBaseCodeAsync_fail');
                        reject(err);
                    },
                });
            });
        },
        /**
         * 授权用户信息获取小程序authCode
         * @param refresh 强制刷新 authCode 和 authInfo
         * @param onlyRefreshCode 仅刷新 authCode
         * @returns {Promise<any>}
         */
        async getAuthAllCodeSync(refresh = false, onlyRefreshCode = false) {
            const authCode = uni.getStorageSync('authAllCode');
            const authInfo = uni.getStorageSync('authInfo');
            if (!onlyRefreshCode && !refresh && authCode) {
                // if (!authInfo || (authInfo && !authInfo.certNo)) await this.getAlipayUserInfos(authCode);
                return authCode;
            }
            return new Promise((resolve, reject) => {
                my.getAuthCode({
                    scopes: ['auth_base', 'auth_user'], // 聚合用户信息授权 ， auth_userinfo 为历史版本获取会员信息 https://opendocs.alipay.com/open/220/105337 暂不会用到
                    // 需主体 AppId 上申请聚合的权限，scope只传auth_user就行，等聚合权限申请完成，自动会出现姓名➕头像➕性别➕昵称的授权提醒 !important
                    success: async res => {
                        uni.setStorageSync('authBaseCode', res.authCode);
                        uni.setStorageSync('authUserCode', res.authCode);
                        uni.setStorageSync('authAllCode', res.authCode);
                        if (!onlyRefreshCode && (refresh || !authInfo || (authInfo && !authInfo.certNo))) {
                            // await this.getAlipayUserInfos(res.authCode);
                        }
                        resolve(res.authCode);
                    },
                    fail: err => {
                        console.log(err);
                        console.log('getAuthAllCodeSync_fail');
                        my.hideLoading();
                        reject(err);
                    },
                });
            });
        },
        // 根据手机号获取用户信息
        async getUserInfoByPhone(phone) {
            try {
                // let { data } = await getUserInfoByPhone({ phone });
                // if (!data.Value) {
                //     return my.showToast({
                //         content: data.Info,
                //     });
                // }
                // const authInfo = uni.getStorageSync('authInfo');
                // data.Data.phoneToken = authInfo.phoneToken;
                // this.encUserInfo = data.Data;
            } catch (err) {}
        },
        // 刷新权限状态
        async refreshAuthSetting() {
            let { authSetting } = await this.getSettingSync();
            this.authSetting = authSetting;
        },
        // 聚合授权 - 不弹窗静默登录/注册
        async onSilenceAction(callback) {
            this.silence = true;
            if (!this.phone) {
                await this.getAuthAllCodeSync(true);
                const authInfo = uni.getStorageSync('authInfo');
                if (!authInfo) return;
                this.type = authInfo.isRegistered ? 1 : 2;
                this.phone = authInfo.mobile;
                if (authInfo.isRegistered) await this.getUserInfoByPhone(authInfo.mobile);
                this.subAction(null, callback, false);
            } else {
                this.subAction(null, callback, false);
            }
        },
        // 手机号授权 - 不弹窗静默登录/注册
        async onSilenceOnlyPhoneAction(callback) {
            this.silence = true;
            if (!this.phone) await this.getEncPhone(await this.getPhoneNumberSync());
            this.subAction(null, callback, false);
        },
        // 手机号授权 - 打开一键登录弹窗
        async openDialog(callback, tusk = true) {
            this.silence = !this.authSetting.phoneNumber;
            if (!this.phone) {
                await this.getEncPhone(await this.getPhoneNumberSync());
                if (!this.authSetting.phoneNumber) return this.subAction(null, callback, tusk);
                return;
            }
            this.dialogShow = true;
        },
        // 手机号授权- 一键登录
        async subAction(e, callback, tusk = true) {
            if (this.type !== 1) await this.register();
            if (!this.encUserInfo) return;
            if (tusk) {
                await this.loginBindAccount();
            } else {
                await this.loginBindAccount('', false);
            }
            callback && callback();
        },
        // 授权手机号用户基本信息
        async onGetAuthorize() {
            this.subAction();
        },
        // 注册
        register() {
            console.log('注册 暂不开放');
        },
        // 解密手机号
        async getEncPhone(encData) {
            try {
                let data = await loginRegisterByPhone({
                    phoneEnc: encData,
                });

                // 后台不改提示，只能前端兼容处理
                if (data.Info.trim() === '登录失败') data.Info = '登录失败，请您重新登录';
                if (!data.Value) return my.showToast({ content: data.Info });

                if (!data.Data.phone) return my.showToast({ content: '系统接口繁忙，请稍候重试！' });
                if (!data.Data.isRegistered) {
                    // 注册
                    this.type = 2;
                    this.phone = data.Data.phone;
                    this.dialogShow = !this.silence;
                } else {
                    // 登录
                    this.type = 1;
                    this.phone = data.Data.phone;
                    this.dialogShow = !this.silence;
                }
                this.encUserInfo = data.Data;
            } catch (err) {
                console.error(err);
            }
        },
        // 手机号授权 - 支付宝绑定门户账号
        async loginBindAccount(authCode = '', turnTask = true) {
            if (!authCode) await this.getAuthBaseCodeSync(true);
            if (!this.encUserInfo) return;
            try {
                let data = await loginBindAccount({
                    userid: this.encUserInfo.userid,
                });
                if (!data.Value || !data.Data) return my.showToast({ content: '绑定异常，请刷新页面重试' });

                this.encUserInfo.openid = data.Data;
                // 缓存token
                uni.setStorageSync('token', this.encUserInfo.phoneToken || this.dncUserInfo.phoneToken);
                // 缓存用户信息
                uni.setStorageSync('userInfo', this.encUserInfo);
                // 单独缓存openid
                uni.setStorageSync('openid', this.encUserInfo.openid);
                // 缓存登录周期
                uni.setStorageSync('loginCycle', new Date().getTime() / 1000);
                this.userInfo = this.encUserInfo;

                // 换取v3token
                let v3Result = await loginToGsms({
                    auth: 'zfb',
                    gsmsUri: environment['/gsms-mpaas-' + projectConfig.workspaceid + '/'] || '',
                });

                if (v3Result.Value && v3Result.Data.accessToken) {
                    uni.setStorageSync('v3token', v3Result.Data.accessToken);
                    uni.setStorageSync('tokenInfo', v3Result.Data);
                    this.$emit('onLoginSuccess', { type: 'v3' });
                    return;
                }
                this.$emit('onLoginSuccess', { type: 'v2' });
            } catch (err) {}
        },
        // 关闭一键登录弹窗
        closeDialog() {
            this.dialogShow = false;
            this.$emit('onClose');
        },
    },
};
</script>
<style scoped lang="scss">
.dialog-login {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;

    &-main {
        background: rgba(0, 0, 0, 0);
        position: absolute;
        left: 50%;
        top: 50%;
        width: 80%;
        transform: translate(-50%, -50%);
    }

    &-content {
        border-radius: 16rpx;
        background: #fff;
        padding: 40rpx 24rpx;
        text-align: center;

        .title {
            font-size: 36rpx;
            color: #333;
            font-weight: bold;
        }

        .subtitle {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 6rpx;
        }

        .center {
            background: #f5f5f5;
            border-radius: 8rpx;
            padding: 40rpx;
            margin: 40rpx 0;
        }

        .phone {
            font-size: 50rpx;
            color: #333;
            font-weight: bold;
        }

        .btn {
            background: #f96702;
            border-radius: 8rpx;
            color: #fff;
            font-size: 36rpx;
            margin-bottom: 40rpx;
            line-height: 2.4;
        }

        .changePhone {
            font-size: 28rpx;
            color: #4b6b99;
        }
    }

    .dialog-close {
        z-index: 9999;
        width: 60rpx;
        height: 60rpx;
        margin: 40rpx auto;
        background: url('https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/phoneAlert/round_close.png');
        background-size: 60rpx 60rpx;
    }
}
</style>
