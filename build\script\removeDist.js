const fs = require('fs');
const path = require('path');

/**
 * 删除 dist 目录的函数
 * @returns {void}
 * @throws {Error} 如果在删除过程中出现错误，会抛出相应的错误信息
 */
function removeDist() {
    try {
        // 获取 dist 目录的绝对路径
        const distPath = path.resolve(__dirname, '../../dist');
        console.log('distPath 是否存在', distPath);

        // 检查 dist 目录是否存在
        fs.accessSync(distPath, fs.constants.F_OK);

        // 如果目录存在，则删除它
        deleteDirectory(distPath);
        console.log('dist 目录已成功删除。');
    } catch (err) {
        if (err.code === 'ENOENT') {
            console.log('dist 目录不存在，无需删除。');
        } else {
            throw new Error(`删除 dist 目录时出现错误: ${err.message}`);
        }
    }
}

// 递归删除目录的函数
function deleteDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
        fs.readdirSync(dirPath).forEach((file) => {
            const curPath = path.join(dirPath, file);
            if (fs.lstatSync(curPath).isDirectory()) {
                // 如果是子目录，则递归调用删除函数
                deleteDirectory(curPath);
            } else {
                // 如果是文件，则直接删除
                fs.unlinkSync(curPath);
            }
        });
        // 删除空目录
        fs.rmdirSync(dirPath);
    }
}

module.exports = removeDist;