<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="account_cancellation_div bg-F7F7FB">
            <zj-navbar title="昆仑e享卡注销" :border-bottom="false"></zj-navbar>
            <div class="account_cancellation_content">
                <div class="logo_div">
                    <img src="../../images/card_img.png" alt />
                    <!-- <div>将{{phone}}所绑定的账号注销</div> -->
                </div>
                <div class="rule">
                    <div class="title font-14 color-999">注销后，昆仑e享卡以下权限将视为自动放弃</div>
                    <div class="item_div fl-row">
                        <div class="item_div_left">
                            <img src="../../images/step1.png" alt />
                        </div>
                        <div class="item_div_right">
                            <div>历史交易的退换货；</div>
                            <div>开具结余发票；</div>
                            <div>昆仑e享卡其他维权权益。</div>
                        </div>
                    </div>
                </div>
                <div class="rule">
                    <div class="title font-14 color-999">提交申请前，您需要满足以下条件</div>
                    <div class="item_div fl-row">
                        <div class="item_div_left">
                            <img src="../../images/step2.png" alt />
                        </div>
                        <div class="item_div_right">
                            <div>昆仑e享卡未被冻结；</div>
                            <div>昆仑e享卡历史交易已结清；</div>
                            <div>昆仑e享卡账户余额为0.00元。</div>
                        </div>
                    </div>
                </div>
                <div class="btn_div">
                    <!-- <div class="agreement_div fl-row">
          <div class="img_div">
            <img v-if="!selectImg" src="../../images/not_selected.png" @click="changeSelect(true)" alt />
            <img v-else src="../../images/selected.png" @click="changeSelect(false)" alt />
          </div>
          <div class="text_div fl-row">

            同意
            <div @click="checkAgreement()">《电子账户注销协议》</div>
          </div>
        </div>-->
                    <div class="cancel_btn" @click="cancellationBtn()">立即注销</div>
                    <div class="tips_div font-14 color-333"
                        >温馨提示：昆仑e享卡每年最多可注销{{ cancelInfo.limitCancelTimes || 0 }}次，您还剩{{
                            cancelInfo.leftCancelTimes || 0
                        }}次</div
                    >
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
// import { mapState } from "vuex";
// import { bindCardList } from ".././api/cardApi.js";
// import { messageCodeSend, accountLogout } from "../../../../s-kit/js/v3-http/https3/wallet"
import { userAgreement } from '../../../../s-kit/js/v3-http/https3/user';
import { accountLeftCancelTimesQuery } from '../../../../s-kit/js/v3-http/https3/wallet';
import { mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            cardList: null,
            selectImg: false,
            walletInfoData: '',
            cancelInfo: '',
        };
    },
    computed: {
        ...mapGetters(['walletInfo']),
    },

    onLoad(option) {
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        this.cancelInfo = queryInfo.cancelInfo;
    },
    async mounted() {
        //查看当前电子账户余额及相关信息
        await this.$store.dispatch('getAccountBalanceAction');
        this.walletInfoData = this.walletInfo;
        console.log(this.walletInfoData);
        // this.getaccountLeftCancelTimesQuery()// 获取注销次数
    },
    methods: {
        // 获取注销次数
        // async getaccountLeftCancelTimesQuery () {
        //   let res = await accountLeftCancelTimesQuery()
        //   if (res && res.success) {
        //     this.cancelInfo = res.data
        //   }
        // },
        // 勾选协议
        changeSelect(val) {
            this.selectImg = val;
            console.log(val);
        },
        /**
         * 同意注销事件
         */
        cancellationBtn() {
            this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/card-cancel-code/main', {}, 'navigateTo');
        },
    },
};
</script>
<style lang="scss" scoped>
.account_cancellation_div {
    width: 100%;
    height: 100%;
    padding: 0 15px;

    .account_cancellation_content {
        .logo_div {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 34px;

            img {
                width: 64px;
                height: 64px;
            }
        }

        .rule {
            margin-top: 10px;
            margin-bottom: 5px;

            .title {
                // font-size: $font-size-medium;
                // color: $color-text-l;
            }

            .item_div {
                margin-top: 10px;

                .item_div_left {
                    img {
                        width: 40px;
                        height: 40px;
                        margin-right: 13px;
                    }
                }

                .item_div_right {
                    div {
                        color: #333;
                        font-size: 14px;
                        display: block;
                        line-height: 25px;
                    }
                }
            }
        }
    }
}

.title {
    // font-size: $font-size-medium;
    // color: $color-text-l;
    margin-top: 15px;
}

.btn_div {
    width: 100%;
    margin-top: 36px;

    .agreement_div {
        .text_div {
            color: #999999;
            font-size: 12px;
            line-height: 17px;

            div {
                color: #e64f22;
            }
        }

        .img_div {
            width: 13px;
            height: 13px;
            margin-right: 5px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .cancel_btn {
        height: 44px;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        line-height: 44px;
        text-align: center;
        border-radius: 8px;
        margin-top: 12px;
    }

    .tips_div {
        line-height: 28px;
        margin-top: 12px;
    }
}
</style>
