<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <div class="tabar-page-class">
        <third-order ref="order" pageType="cube" :refer="refer"></third-order>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import thirdOrder from '../../s-kit/first/third-order/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            refer: 'r48',
        };
    },
    components: {
        thirdOrder,
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() { },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {
        // #ifdef MP-MPAAS
        my.on('pageWillShow', res => {
            this.$store.dispatch('getSetWalletStatus');
            this.$refs.order.dateReset('onShow');
        });
        // #endif
    },
    methods: {},
};
</script>
<style scoped lang="scss">
.tabar-page-class {
    height: 100vh;
}
</style>
