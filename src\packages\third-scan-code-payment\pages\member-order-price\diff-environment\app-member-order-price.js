import { mapState } from 'vuex';
import checkFKArgs from '../../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import projectConfig from '../../../../../../project.config';
import post from '../../../../../s-kit/js/v3-http/post';
export default {
    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
            mpaasAccountPlugin: state => state.thirdIndex.mpaasAccountDataPlugin,
        }),
    },
    mounted() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$sKit.mpaasPayPlugin?.init();
            this.$nextTick(async () => {
                const accountPluginRef = this.$refs.accountPlugin;
                this.$sKit.accountPlugin.initRef(accountPluginRef);
            });
        }
        // #endif
    },
    methods: {
        /**
         * @description  : 确认支付
         * @param         {String} payOrderNo -支付中心订单号
         * @param         {String} amount -需要冻结的金额
         * @param         {String} extendFiled -风控参数
         * @param         {Function} getFKArgs -设置风控参数
         * @param         {String} accountNo -昆仑e享卡编号
         * @param         {String} bizOrderNo -业务中心订单号
         * @param         {Function} biometricPay -生物识别验证
         * @param         {Function} rposPay -发起pos支付
         * @param         {Function} resStatus -支付状态
         * @param         {Function} resAuthStatus -是否触发风控
         * @param         {Function} oilTriggerRisk -实人认证
         * @return        {*}
         */
        async confirmPay(isAuth = false) {
            try {
                let res = await this.rposPayFun();
                uni.hideLoading();
                if (this.$paymentCenter.resStatus(res)) {
                    console.log('会员码支付成功', JSON.stringify(res));
                    this.active = 2;
                    this.$sKit.mpBP.tracker('会员码', {
                        seed: 'memberCodeBiz',
                        pageID: 'orderCompletePage', // 页面名
                        refer: this.payMentInfo.refer || '', // 来源
                        channelID: projectConfig.clientCode, // C10/C12/C13
                        stationCode: this.payMentInfo.stationCode, //站点编码
                        address: this.cityName,
                    });
                } else if (this.$paymentCenter.resAuthStatus(res)) {
                } else {
                    uni.showToast({ title: res.msg || '支付失败' });
                }
            } catch (error) {
                uni.hideLoading();
            } finally {
            }
        },
        rposPayFun(isAuth) {
            return new Promise(async (resolve, reject) => {
                if (this.isHarmony) {
                    let params = {
                        payOrderNo: this.payMentInfo.payOrderNo,
                        amount: this.actualPayTotalAmount || 0 + '',
                        extendFiled: await post.addExtendFiled('plugin'),
                    };
                    console.log('会员码支付1');
                    console.log(this.mpaasAccountPlugin, '会员码支付账户插件');

                    console.log(this.$sKit.mpaasPayPlugin, '会员码支付支付插件');
                    try {
                        this.$sKit.mpaasPayPlugin
                            .RposPay(params, this.mpaasAccountPlugin)
                            .then(res => {
                                console.log(res, '会员码支付2');
                                resolve(res);
                            })
                            .catch(err => {
                                console.log(err, '会员码支付3');
                                resolve(err);
                            });
                    } catch (err) {
                        console.log(err, '会员码支付4');
                    }
                } else {
                    let params = {
                        payOrderNo: this.payMentInfo.payOrderNo,
                        amount: this.actualPayTotalAmount || 0 + '',
                        extendFiled: JSON.stringify(await checkFKArgs.getFKArgs('sdk', isAuth)),
                        accountNo: this.walletInfo.ewalletNo || '',
                        bizOrderNo: this.payMentInfo.orderNo, // 业务中心订单号
                    };
                    // 查询生物识别开通状态
                    let obj = await this.$sKit.commonUtil.biometricPay(this.payMentInfo.payOrderNo, this.actualPayTotalAmount, !isAuth);
                    Object.assign(params, obj);
                    uni.showLoading({
                        title: '唤起中',
                        mask: true,
                    });
                    this.$paymentCenter.rposPay({ paramsJsonStr: encodeURIComponent(JSON.stringify(params)) }, res => {
                        resolve(res);
                    });
                }
            });
        },
        /**
         * @description  : 关闭小程序
         * @return        {*}
         */
        closeEvent() {
            // 当前方法后续小程序使用
            this.$store.commit('setMemberFun', true);
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
    },
};
