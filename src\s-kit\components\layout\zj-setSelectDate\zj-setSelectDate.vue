<template>
    <div>
        <!--自定义积分能源币时间选择-->
        <div class="cc_area_mask" v-if="show == true"></div>
        <div :class="'cc_area_view ' + (show ? 'show' : 'hide')">
            <div class="cc_area_view_btns">
                <text class="cc_area_view_btn_cancle font_color" @tap="handleNYZAreaCancle">取消</text>
                <text>选择时间</text>
                <text class="cc_area_view_btn_sure font_color" @tap="handleNYZAreaSelect">确定</text>
            </div>
            <picker-view v-if="visible" :indicator-style="indicatorStyle" :value="value" @change="bindChange" class="cc_area_pick_view">
                <picker-view-column>
                    <view class="item" v-for="(item,index) in years" :key="index">{{item}}年</view>
                </picker-view-column>
                <picker-view-column>
                    <view class="item" v-for="(item,index) in months" :key="index">{{item}}月</view>
                </picker-view-column>
            </picker-view>
        </div>
    </div>
</template>

<script>
import { settime } from '../../../js/v3-plugin/fp/wx/storage/storage';
export default {
    props: {
        dateYearsOrMonth: {
            type: Array,
            default: [],
        },
        inDate: {
            type: String,
            default: ''
        }
    },
    data() {
        const date = new Date()
        let years = []
        const year = date.getFullYear()
        let months = []
        const month = date.getMonth() + 1
        let initObj = {}
        this.dateYearsOrMonth.forEach((item, i) => {
            initObj[item.year] = []
        })
        let objKey = Object.keys(initObj)
        years = objKey
        objKey.forEach((item, i) => {
            this.dateYearsOrMonth.forEach((items, is) => {
                if (items.year == item) {
                    initObj[item].push(items.month)
                }
            })
        })
        months = initObj[year]
        return {
            title: 'picker-view',
            years,
            year,
            months,
            month,
            value: [year.length - 1, month.length - 1],
            visible: true,
            indicatorStyle: `height: 50px;`,
            show: true,
            initObj: initObj
        }
    },

    components: {},
    mounted() {
        this.$nextTick(() => {
            this.value = [this.years.length - 1, this.months.length - 1];
        });
    },
    methods: {
        bindChange: function (e) {
            const val = e.detail.value
            this.year = this.years[val[0]]            
            this.months = this.initObj[this.year]
            if (this.value[0] != val[0]) {
                this.value = [val[0], 0];
                this.month = this.months[0]
            } else if (this.value[1] != val[1]) {
                this.value = [val[0], val[1]];
                this.month = this.months[val[1]]
            }
        },
        handleNYZAreaSelect: function (e) {
            this.$emit('sureSelectDateTime', {
                year: this.year,
                month: this.month,
            });
        },

        /**
         * 取消按钮的点击事件
         */
        handleNYZAreaCancle: function (e) {
            this.$emit('hideShow');
        },
    },
};
</script>
<style scoped lang="scss">
.cc_area_view {
    width: 100%;
    position: fixed;
    bottom: -1000px;
    left: 0px;
    background-color: #fff;
    z-index: 21;
    transition: all 0.3s;
}

.cc_area_pick_view {
    height: 200px;
    text-align: center;
    width: 100%;
}

.cc_area_colum_view {
    line-height: 35px;
    text-align: center;
    font-size: 28upx;
}

.hide {
    bottom: -1000upx;
    transition: all 0.3s;
}

.show {
    bottom: 0upx;
    transition: all 0.3s;
}

.cc_area_view_btns {
    background-color: #fff;
    border-bottom: 1px solid #eeeeee;
    font-size: 30upx;
    padding: 18upx 0upx;
    display: flex;
    justify-content: space-between;
}

.cc_area_view_btns > text {
    // display: inline-block;
    // word-spacing: 4upx;
    // letter-spacing: 4upx;
}

.cc_area_view_btn_cancle {
    color: #939393;
    padding-right: 20upx;
    padding-left: 25upx;
}

.cc_area_view_btn_sure {
    float: right;
    padding-left: 20upx;
    padding-right: 25upx;
}

.cc_area_mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(28, 28, 28, 0.6);
    position: absolute;
    top: 0upx;
    left: 0upx;
    z-index: 20;
}

.font_color {
    color: #e64f22;
}

.picker-view {
    width: 100%;
    height: 300px;
    margin-top: 10px;
}

.item {
    line-height: 50px;
    text-align: center;
}
</style>
