import { POST, POST2 } from '../../index';

// 校验是否可以再来一单并加入购物车/o2o/anotherOrderCheck接口
export const anotherOrderCheckApi = (params, config) => {
    return POST('o2o.anotherOrderCheck', params, config);
};

// 取消提货/o2o/cancelPickUp接口
export const cancelPickUpApi = (params, config) => {
    return POST('o2o.cancelPickUp', params, config);
};

/**
 * @description: 查询O2O订单详情
 * @param { orderNo }  订单编号
 * @param { stationCode }  网点编码
 * @param { status }  状态（已完成=4，已取消=5，待支付=1，待提货=8，已退款=9）
 */
export const detail = (params, config) => {
    return POST('o2o.incompleteOrder.detail', params, config);
};

// 购物车-查询购物车列表/o2o/shopping/goods
export const shoppingGoodsApi = (params, config) => {
    return POST('o2o.shopping.goods', params, config);
};

// 购物车-加购/更新商品/o2o/shopping/add接口
export const shoppingAddApi = (params, config) => {
    return POST('o2o.shopping.add', params, config);
};

// 购物车-删除商品/o2o/shopping/delete接口
export const shoppingDeleteApi = (params, config) => {
    return POST('o2o.shopping.delete', params, config);
};

// 推荐商品 o2o/pickOfTheWeek接口
export const pickOfTheWeekApi = (params, config) => {
    return POST('o2o.pickOfTheWeek', params, config);
};

// 查询门店库存接口 /o2o/product/queryStkCount
export const queryStkCount = (params, config) => {
    return POST('o2o.product.queryStkCount', params, config);
};

// 根据站点批量查询门店信息接口 /o2o/product/queryStoreInfoListByCodeList
export const queryStoreInfoListByCodeList = (params, config) => {
    return POST('o2o.product.queryStoreInfoListByCodeList', params, config);
};

/**
 * @description: 用户主动收获(包裹签收)
 * @param {*} stationCode 网点编码
 * @param {*} orderNo 销售订单号
 * @return {*}
 */
export const confirmGoods = (params, config) => {
    return POST('o2o.welfareCards.confirmGoods', params, config);
};
/**
 * @description: 获取能源锦鲤直销订单物流信息
 * @param {*} orderNo 销售订单号
 * @return {*}
 */
export const getDeliveryInfo = (params, config) => {
    return POST('o2o.welfareCards.logisticsInfo', params, config);
};
