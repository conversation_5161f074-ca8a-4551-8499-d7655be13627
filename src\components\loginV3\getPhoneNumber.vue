<template>
    <div>
        <div class="get-phone-number" v-if="!coverMap">
            <div class="slot-container" @click="getPhoneNumber" v-if="isLogin">
                <slot></slot>
            </div>
            <button
                v-else
                open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber"
                hover-class="none"
                :phone-number-no-quota-toast="false"
            >
                <slot></slot>
            </button>
        </div>
        <cover-view v-else>
            <cover-view class="slot-container" @click="getPhoneNumber" v-if="isLogin">
                <slot></slot>
            </cover-view>
            <button
                v-else
                open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber"
                hover-class="none"
                :phone-number-no-quota-toast="false"
            >
                <slot></slot>
            </button>
        </cover-view>
        <!-- <Login v-if="loginIsShow" @change="change" :btnType="this.btnType" :params="params"></Login> -->
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { setedPayPass } from '../../api/home';
import Store from '../../store';
import projectConfig from '../../../project.config';
// import Login from '@/packages/setting/pages/login/main.vue'
export default {
    name: 'getPhoneNumber',
    props: {
        coverMap: {
            default: false,
            type: Boolean,
        },
        btnType: {
            default: 'normal',
            type: String,
        },
        curTabIndex: {
            default: '',
            type: String,
        },
        params: {
            default: {},
            type: Object,
        },
    },
    data() {
        return {
            loginIsShow: false,
            lastTapTime: 0,
        };
    },
    mounted() {
        console.log(this.isLogin, 'isLoginisLoginisLogin');
        this.getPhoneNumber = this.$util.throttleUtil(this.getPhoneNumber);
    },
    computed: {
        ...mapGetters(['isLogin']),
        ...mapState({
            // 存储外部跳转进来携带的参数
            officialAccountParams: state => state.location.officialAccountParams,
            // 登录按钮置灰变亮
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            // 3.0存储登录信息
            cityName: state => state.locationV3_app.cityName,
            loginTemporaryCode: state => state.thirdLogin.loginTemporaryCode,
        }),
    },
    methods: {
        /**
         * @description  : 一键登录按钮自带方法
         * @return        {*}
         */
        async getPhoneNumber(e) {
            // e.mp.detail.errno = '1400001';
            // console.log((e.mp.detail.errno = '1400001'), '测试');
            this.$store.commit('setLoginLoading', true);
            let userId = uni.getStorageSync('userId');
            let tokenInfo = uni.getStorageSync('tokenInfo');
            if (!this.loginButtonGrayedOut) return;
            // 按钮置灰
            this.$store.commit('setLoginButtonGrayedOut', false);
            // 判断当前是否可以进行一键登录
            let notYJLogin = e?.mp?.detail?.errno == '1400001';
            // 登录失败时点击立即登录需要再次调用自动登录接口
            if (!this.loginTemporaryCode) {
                // 如果不存在临时码
                let res = await this.$store.dispatch('callAutomaticLoginAgain', notYJLogin);
                if (res.success) {
                    this.$emit('loginOver', res);
                } else {
                    await this.nextLogin(e, notYJLogin);
                }
            } else {
                await this.nextLogin(e, notYJLogin);
            }
        },
        async nextLogin(e, notYJLogin) {
            // 用户拒绝授权获取手机号进行一键登录
            if (e.mp.detail.errMsg === 'getPhoneNumber:fail user deny' || e.mp.detail.errMsg === 'privacy permission is not authorized') {
                this.$store.commit('setLoginLoading', false);
                // 按钮变亮
                this.$store.commit('setLoginButtonGrayedOut', true);
				// 用户拒绝授权后隐藏登录中状态，显示授权登录
				this.$store.commit('setLoginLoading', false);
                return;
            }
            this.$sKit.mpBP.tracker('登录/注册', {
                seed: 'loginOrRegistBiz',
                pageID: 'loginPop-up', // 页面名
                channelID: projectConfig.clientCode, // C10/C12/C13
            });
            if (this.curTabIndex) {
                // 定义一个对象来映射不同的索引值到字符串
                const indexMap = {
                    1: 'qb',
                    2: 'dd',
                    3: 'wd',
                };
                // 获取对应的参数值
                const paramValue = indexMap[this.curTabIndex] || '';
                // 调用 dispatch 方法
                this.$store.dispatch('setOfficialAccountParams', paramValue);
            }
            // 未登录状态切点击一键授权按钮报错等于指定错误码时，使用手机号验证码登录
            let tokenInfo = uni.getStorageSync('tokenInfo');
            if (!tokenInfo?.accessToken && notYJLogin) {
                // 不能一键登录执行验证码登录
                let url = '/packages/third-new-third-login/pages/login/main';
                let params = {};
                this.$sKit.layer.useRouter(url, params);
                // 按钮变亮
                this.$store.commit('setLoginButtonGrayedOut', true);
                return;
            }
            e.type = '11';
            let res = await this.$store.dispatch('init', e);
            this.$emit('loginOver', res);
        },
    },

    components: {
        // Login
    },
    onUnload() {
        this.loginIsShow = false;
    },
};
</script>

<style lang="scss" scoped>
.slot-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
</style>
