<template>
    <div class="view">
        <div class="popup-box">
            <div class="popup-content" :style="{ backgroundImage: `url(${popupBg})` }">
                <div class="popup-content-title">您有一张待迁移的电子卡</div>
                <div class="popup-content-text">
                    {{
                        `您${
                            electronicCardData.phone ? electronicCardData.phone.replace(electronicCardData.phone.substr(3, 4), '****') : ''
                        }账号下有一张绑定您身份信息的电子卡，
          卡号为${electronicCardData.cardNo ? electronicCardData.cardNo.replace(electronicCardData.cardNo.substr(4, 8), '****') : ''}，
          系统将为您迁移至当前账号下(您需要迁移后才可以正常使用相关功能，您原有的权益和资金不会受到任何影响)。`
                    }}
                </div>
                <div class="popup-content-btn" @click="accountUpgrade">确认迁移</div>
                <div class="popup-content-btn-next" @click="closePopup">以后再说</div>
            </div>
            <div class="close-popup" @click="closePopup">
                <img :src="popupClose" alt />
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    name: 'zjNewStation',
    data() {
        return {
            popupBg: require('../../../image/popupBg2.png'),
            popupClose: require('../../../image/popupClose.png'),
        };
    },
    props: {
        activate: String,
    },
    created() {},
    mounted() {},
    computed: {
        btnDisable() {
            return !this.agreeCheckBox;
        },
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            electronicCardData: state => state.wallet.electronicCardData, // 电子卡卡号和手机号
        }),
    },
    methods: {
        // x按钮点击事件 @close
        closePopup() {
            // this.$emit("close");

            let params = { flag: false, activate: this.activate };
            this.$store.commit('mSetIsTransfer', params);
        },
        // 立即升级按钮点击事件 @submit
        accountUpgrade() {
            this.$store.dispatch('electronicCardMigration', { flag: false, activate: '' });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;

    .popup-box {
        width: 570rpx;
        height: 730rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, calc(-50% - 73rpx));
        background: #ffffff;
        border-radius: 16rpx 16rpx;

        .popup-content {
            background-size: cover;
            position: absolute;
            left: 0;
            right: 0;
            top: -31rpx;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 264rpx 33rpx 49rpx;

            .popup-content-title {
                font-size: 32rpx;
                font-weight: 500;
                color: #333333;
                line-height: 46rpx;
                margin-bottom: 12rpx;
            }

            .popup-content-text {
                font-size: 28rpx;
                font-weight: 400;
                color: #999999;
                line-height: 39rpx;
                padding: 0 14rpx;
                margin-bottom: 30rpx;
            }

            .popup-content-warning {
                font-size: 24rpx;
                font-weight: 400;
                color: #e64f22;
                line-height: 39rpx;
                margin-bottom: 31rpx;
            }

            .popup-content-agree {
                width: 100%;
                display: flex;
                align-items: center;
                text-align: start;
                margin-bottom: 39rpx;
                .popup-content-agree-checkBox {
                    transform: translate(-4px) scale(0.6);
                }

                .popup-content-agree-text {
                    flex: 1;
                    flex-shrink: 0;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 33rpx;
                    transform: translate(-10rpx);
                    span {
                        color: #e64f22;
                    }
                }
            }

            .popup-content-btn {
                width: 100%;
                height: 88rpx;
                background: #e64f22;
                font-size: 30rpx;
                font-weight: 500;
                color: #ffffff;
                line-height: 88rpx;
                border-radius: 16rpx 16rpx;
            }

            .popup-content-btn-next {
                margin-top: 28rpx;
                font-size: 30rpx;
                font-weight: 400;
                color: #999999;
                line-height: 42rpx;
            }
        }

        .disable {
            opacity: 0.3;
        }

        .close-popup {
            position: absolute;
            height: 58rpx;
            width: 58rpx;
            bottom: -117rpx;
            left: 50%;
            transform: translate(-50%);

            img {
                height: 100%;
                width: 100%;
            }
        }
    }
}
</style>
