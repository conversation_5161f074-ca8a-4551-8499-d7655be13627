<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="扫码支付"></zj-navbar>
            <div class="p-bf bg-F7F7FB padding-16 f-1">
                <div class="money bg-fff border-rad-8">
                    <div class="title">
                        <img src="../../images/success-green.png" alt />
                    </div>
                    <div class="fl-row num fl-jus-cen fl-al-base font-style">
                        <div class="font-36 weight-600 color-333">¥</div>
                        <div class="font-50 weight-600 color-333">{{ paymentDetails.payAmount }}</div>
                    </div>
                    <div class="fl-column fl-al-jus-cen font-14 color-333 weight-400">
                        <div class="text">订单编号：{{ paymentDetails.orderNo }}</div>
                        <div class="text">下单时间：{{ paymentDetails.payTime }}</div>
                        <div class="text2">支付方式：{{ paymentDetails.payMethodName }}</div>
                    </div>
                </div>
                <div class="btn_div fl-row fl-jus-bet font-15">
                    <div class="finish_verification te-center border-rad-8 btn-plain color-E64F22 font-16" @click="queryOrderDetail()"
                        >查询订单详情</div
                    >
                    <div class="finish_verification te-center border-rad-8 primary-btn color-fff font-16" @click="closeEvent()">完成</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zjMarket
                marketType="screenMask"
                ref="maskBanner"
                :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
                spaceType="payment_page"
            ></zjMarket>
        </div>
    </div>
</template>

<script>
import zjMarket from '../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import { mapState, mapGetters } from 'vuex';
import projectConfig from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'code-order-payment-results',
    components: { zjMarket },
    props: {},
    data() {
        return {
            // 支付完成详细信息
            paymentDetails: {},
            // 支付信息路由参数(需求变更 目前未使用)
            payInfo: {},
        };
    },
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
        }),
    },
    created() {},
    onLoad(options) {
        let detailObj = JSON.parse(decodeURIComponent(options.data));
        this.payInfo = detailObj;
        // 支付方式数组的第一项
        this.paymentDetails = detailObj.payItemList[0];
        // 支付订单
        this.paymentDetails.orderNo = detailObj.orderNo;
        this.$sKit.mpBP.tracker('付款码', {
            seed: 'fkCodeBiz',
            pageID: 'orderCompletePage', // 页面名
            refer: detailObj.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
            payMethod: this.paymentDetails.payMethodName,
            xfAmount: this.paymentDetails.payAmount,
            address: this.cityName,
        });
    },
    methods: {
        // 确认支付
        queryOrderDetail() {
            console.log('支付成功后查询订单详情');
            let url = '/packages/third-order/pages/order-detail/main';
            let params = this.payInfo;
            let type = 'redirectTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 关闭小程序   // 支付成功返回
        closeEvent() {
            // this.$store.commit('setPayCodeFun', true);
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
    },
    filter: {},
    watch: {},
};
</script>

<style lang="scss" scoped>
.view {
    .money {
        width: 100%;
        height: 350px;
        position: relative;
        // 加这行代码是为了解决子元素添加margin-top影响父元素；上下盒子添加外边距重叠问题
        display: inline-block;
        margin-bottom: 16px;

        .title {
            width: 64px;
            height: 64px;
            margin-top: 50px;
            margin: 60px auto 0;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .num {
            line-height: 50px;
            margin-top: 16px;
            margin-bottom: 30px;
        }

        .text {
            margin-bottom: 15px;
        }

        .text2 {
            margin-bottom: 42.5px;
        }
    }

    .btn_div {
        .finish_verification {
            width: 48%;
            height: 44px;
            line-height: 44px;
        }
    }
}
</style>
