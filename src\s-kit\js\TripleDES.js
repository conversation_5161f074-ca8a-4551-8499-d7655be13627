import CryptoJS from 'crypto-js';
import * as iconv from 'iconv-lite';

export function keyMD5Encode(key) {
    let keyGB2312 = iconv.encode(key, 'GB2312').toString();
    console.log('keyGB2312', keyGB2312);

    let md5 = CryptoJS.MD5(keyGB2312).toString();
    console.log('md5', md5);

    function hexStringToUint8Array(hexString) {
        hexString = hexString.replace(/^0+/, '');
        if (hexString.length % 2 !== 0) {
            throw new Error('Invalid hexadecimal string length');
        }
        const len = hexString.length / 2;
        const uint8Array = new Uint8Array(len);
        for (let i = 0; i < len; ++i) {
            uint8Array[i] = parseInt(hexString.substr(i * 2, 2), 16);
        }
        return uint8Array;
    }

    const mdBytes = hexStringToUint8Array(md5);

    console.log('mdBytes', mdBytes);

    let mdPadding = new Uint8Array(24);
    mdPadding.set(mdBytes, 0);
    return mdPadding;
}

export function tripleDESToBase64(plainText, key) {
    const keyBytes = keyMD5Encode(key);
    const keyTripleDES = CryptoJS.lib.WordArray.create(keyBytes);
    return CryptoJS.TripleDES.encrypt(plainText, keyTripleDES, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    }).toString();
}

// const encryptStr = tripleDESToBase64(str, key);
// console.log(encryptStr);
