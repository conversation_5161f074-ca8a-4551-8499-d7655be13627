import { mapGetters, mapState } from 'vuex';
export default {
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        giftCardQueryCodeFun(giftCardNo) {
            return new Promise((resolve, reject) => {
                uni.showLoading();
                if (this.isHarmony) {
                } else {
                    this.$accountCenter.giftCardQueryCode({ cardNo: giftCardNo }, res => {
                        uni.hideLoading();
                        if (res.isSuccessed) {
                            resolve(res.desString);
                        } else {
                            reject(res.desString);
                        }
                    });
                }
            });
        },
    },
};
