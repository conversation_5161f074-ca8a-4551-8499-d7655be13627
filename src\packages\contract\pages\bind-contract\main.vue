<template>
    <div class="view">
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="绑定合同"
            :back-text-style="pageConfig.titleStyle"
        ></u-navbar>
        <div class="main">恭喜你获得加油卡优惠折扣，可选择以下折扣</div>
        <div class="contract" @click="toDetail(oldData)" v-if="oldData && oldData.oldCtNo && oldData.oldCtNo != 0">
            <div class="contract-info">
                <div class="contract-name">{{ oldData.ctName }}</div>
                <div class="name">《折扣优惠协议和规则》</div>
                <div class="date">有效期至{{ oldData.expireDate }}</div>
            </div>
            <div class="used">正在使用</div>
        </div>
        <div class="contract" @click="toDetail(newData)">
            <div class="contract-info">
                <div class="contract-name">{{ newData.ctName }}</div>
                <div class="name">《折扣优惠协议和规则》</div>
                <div class="date">有效期至{{ newData.expireDate }}</div>
            </div>
            <div class="is-use" @click.stop="clickUse">使用</div>
        </div>
        <div class="btn-box">
            <div class="btn" @click="back">放弃优惠</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { commitDisContract } from '@/api/home.js';

export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            newData: null,
            oldData: null,
            contractInfo: null,
        };
    },
    async onLoad(option) {
        this.contractInfo = JSON.parse(decodeURIComponent(option.htInfo));
        this.newData = {
            ctName: this.contractInfo.newCtName || '',
            expireDate: this.contractInfo.newCtExpiredTime || '',
            detail: this.contractInfo.newList || [],
            newCtNo: this.contractInfo.newCtNo,
        };
        this.oldData = {
            ctName: this.contractInfo.oldCtName || '',
            expireDate: this.contractInfo.oldCtExpiredTime || '',
            detail: this.contractInfo.oldList || [],
            oldCtNo: this.contractInfo.oldCtNo,
        };
    },
    methods: {
        back() {
            uni.navigateBack();
        },
        clickUse() {
            this.$util.showModal('是否确认使用该优惠', false).then(res => {
                let params = {
                    cardUserId: this.$mp.query.cardUserId,
                    tradeId: this.$mp.query.tradeId,
                    amount: this.$mp.query.amount,
                    businessDate: this.$mp.query.businessDate,
                };
                commitDisContract(params).then(res => {
                    if (res.status == 0) {
                        this.$util.showModal('合同绑定成功', true).then(() => {
                            uni.navigateBack();
                        });
                    }
                });
            });
        },
        toDetail(htInfo) {
            let obj = encodeURIComponent(JSON.stringify(htInfo));
            uni.navigateTo({
                url: `/packages/contract/pages/contract-detail/main?htInfo=${obj}`,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #eee;
    padding: 10rpx;
    min-height: 100vh;
}

.main {
    font-size: 30rpx;
}

.contract {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx;
    background: #fff;
    margin-bottom: 30rpx;
    .contract-info {
        .contract-name {
            font-size: 43rpx;
            color: #f96702;
            margin-left: 25rpx;
        }
        .name {
            font-size: 32rpx;
            color: blue;
            padding-top: 14rpx;
        }
        .date {
            font-size: 32rpx;
            margin-left: 25rpx;
            color: #666;
            padding-top: 14rpx;
        }
    }
    .used {
        width: 154rpx;
        background: #c9caca;
        font-size: 30rpx;
        color: #fff;
        border-radius: 8rpx;
        text-align: center;
        line-height: 66rpx;
        height: 66rpx;
        padding: 0 10rpx;
    }
    .is-use {
        width: 154rpx;
        background: #f96702;
        font-size: 30rpx;
        color: #fff;
        border-radius: 8rpx;
        text-align: center;
        line-height: 66rpx;
        height: 66rpx;
        padding: 0 10rpx;
        &.used {
            background: #999;
            color: #fff;
        }
    }
}

.btn-box {
    padding: 20rpx 10rpx;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    .btn {
        background: #f96702;
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 8rpx;
        text-align: center;
        color: #fff;
        font-size: 30rpx;
    }
}
</style>
