<template>
    <div class="advertisingPopups" v-if="visbale">
        <picker-view @change="bindPickerChange" :indicator-style="none" :value="value" class="picker-view">
            <div class="textStyle">
                <div @click.stop="cancelSelect" style="color: #333">取消</div>
                <div @click.stop="selectComplete" style="color: #007aff">完成</div>
            </div>
            <picker-view-column>
                <view class="item" v-for="(item, index) in productCodeAndNameList" :key="index">{{ item.productName }}</view>
            </picker-view-column>
        </picker-view>
    </div>
</template>

<script>
export default {
    name: 'helloWord',
    props: {
        oilPopWindowFlag: {
            type: Boolean,
            default: true,
        },
        productCodeAndNameList: {
            type: Array,
            default: false,
        },
    },
    data() {
        return {
            params: null,
            visbale: false,
        };
    },
    watch: {
        oilPopWindowFlag: {
            handler(val) {
                this.visbale = val;
            },
        },
    },
    onLoad() {
        console.log('8888888');
    },
    mounted() {
        //
        //
    },
    computed: {},
    methods: {
        // 关闭弹窗
        // closeDialog () {
        //   this.$parent.balanceInsufficient = false
        //   this.$parent.modelDialogFlag = false // 关闭充值提醒弹窗
        //   this.$parent.insufficientFuelBalanceFlag = false // 关闭充值提醒弹窗
        // },
        // 油品选择
        bindPickerChange(e) {
            const val = e.detail.value;
            console.log(e.detail.value, 'e.detail.value');
            console.log(this.productCodeAndNameList[val].productNo, this.productCodeAndNameList[val].productName, '测试油品选中结果');
            this.params = {
                // 油品编码
                fuelType: this.productCodeAndNameList[val].productNo,
                // 油品名字
                fuelName: this.productCodeAndNameList[val].productName,
            };
        },
        // 选择完成
        selectComplete() {
            console.log('this.visbale', this.visbale);
            if (!this.params) {
                this.params = {
                    // 油品编码
                    fuelType: this.productCodeAndNameList[0].productNo,
                    // 油品名字
                    fuelName: this.productCodeAndNameList[0].productName,
                };
            }
            this.$emit('selectComplete', JSON.stringify(this.params));
            this.visbale = false;
        },
        // 取消选择
        cancelSelect() {
            this.params = {
                // 油品编码
                fuelType: this.productCodeAndNameList[0].productNo,
                // 油品名字
                fuelName: this.productCodeAndNameList[0].productName,
            };
            console.log(this.params, 'this.params');
            this.$emit('cancelSelect', this.params);
            this.visbale = false;
            console.log(this.params.fuelType, this.params.fuelName, 'fuelType');
        },
    },
};
</script>

<style lang="scss" scoped>
.advertisingPopups {
    position: fixed;
    bottom: 0;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    z-index: 99999;
    background-color: rgba(0, 0, 0, 0.6);
    .picker-view {
        position: absolute;
        bottom: 0;
        width: 750rpx;
        height: 600rpx;
        margin-top: 20rpx;
        background: #ffffff;
        display: flex;
        flex-direction: column;

        .textStyle {
            // position: absolute;
            // z-index: 9;
            width: 100%;
            height: 45px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 14px;
            line-height: 45px;
            font-size: 17px;
            div:nth-child(2) {
                color: #007aff;
            }
        }
    }
    .item {
        line-height: 36px;
        text-align: center;
    }
}
</style>
