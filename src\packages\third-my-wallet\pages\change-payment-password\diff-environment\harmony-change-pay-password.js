import { mapState, mapGetters } from 'vuex';
export default {
  // #ifdef MP-MPAAS
   onLoad () {
    this.$nextTick(() => {
      this.getPlugin()
    });
  },
  mounted () { 
    
  },
  methods: {
    async getPlugin() {
      let result = await this.$sKit.keyBordPlugin.initRef();
      this.$store.commit('setAccountDataPlugin', result);
      this.passwordKeyboardRef = result
    }
  },
  // #endif
};
