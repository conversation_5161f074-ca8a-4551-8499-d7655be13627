var req=require('./collection/collection.js')
var storage=require('./storage/storage.js')
var validate = require('./utils/validate.js')
let custid;
let url;

function getFingerPrint(openId,callback) {
  let dfp = storage.getdfp();
  let time = storage.gettime();
  if (dfp != null && dfp != undefined && dfp != "null" && validate.validateTime(time)) {
    callback.success(dfp);
  } else {
    req.getCollection(openId,custid,url,callback);
  }
}

function setCustID(cust) {
  custid=cust;
}

function setUrl(url1) {
  url=url1;
}


module.exports = {
  getFingerPrint: getFingerPrint,
  setCustID: setCustID,
  setUrl: setUrl,
}
