<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="gift_content">
            <zj-navbar :height="44" title="绑定礼品卡"></zj-navbar>
            <div class="title">绑定您的中国石油礼品卡</div>
            <div class="step_div fl-row fl-al-cen fl-jus-cen">
                <div class="step_left fl-row fl-al-cen">
                    <img class="icon" v-if="!cardKey" src="../../images/step1.png" alt="" />
                    <img class="icon" v-else src="../../images/step1_finish.png" alt="" />
                    <span class="step_title">输入卡号密码</span>
                </div>
                <div class="line_div"></div>
                <div class="step_left fl-row fl-al-cen">
                    <img class="icon" v-if="cardKey" src="../../images/step2.png" alt="" />
                    <img class="icon" v-else src="../../images/step2_unselect.png" alt="" />
                    <span>确认绑定信息</span>
                </div>
            </div>
            <div class="card_keys">
                <div class="card_keys_title">卡密</div>
                <input
                    class="keys_input"
                    placeholder-class="keys_input_placeholder"
                    placeholder="请输入卡密"
                    v-model="cardKey"
                    type="text"
                />
                <div class="primary-btn button" @click="bindGiftCard">确定</div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
// #ifndef MP-MPAAS
import { giftCardBind } from '../../../../s-kit/js/v3-http/https3/giftCard/index';
// #endif
// #ifdef MP-MPAAS
import { exchangeToGiftCard } from '../../../../s-kit/js/v3-http/https3/giftCard/index';
// #endif
import { clientCode } from '../../../../../project.config';
import { mapGetters, mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { baseType, encryptionKey3DES } from '../../../../../project.config';
import appBinding from './diff-environment/app-binding';
import wxBinding from './diff-environment/wx-binding';
import zfbBinding from './diff-environment/zfb-binding';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appBinding,
        // #endif
        // #ifdef MP-WEIXIN
        wxBinding,
        // #endif
        // #ifdef MP-ALIPAY
        zfbBinding,
        // #endif
    ],
    data() {
        return {
            cardKey: '',
            refer: '',
        };
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) {
                this.refer = params.refer;
            }
            this.$sKit.mpBP.tracker('绑定礼品卡', {
                seed: 'bindGiftCardBiz',
                pageID: 'bindGiftPage',
                refer: this.refer,
                channelID: clientCode,
                address: this.cityName,
            });
        }
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {},
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {},
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
        }),
    },
    methods: {
        // 绑定礼品卡
        // 用户可以根据卡密绑定他人的礼品卡
        async bindGiftCard() {
            if (this.cardKey == '') {
                this.$util.tipsToastNoicon('请输入卡密');
                return;
            }
            // 充值卡兑换礼品卡接口

            if (this.cardKey.length === 19) {
                let cardKeyStr = this.$sKit.layer.encryptText(this.cardKey, encryptionKey3DES);
                console.log(cardKeyStr, 'cardKeyStr===加密字符串');
                let params = {
                    cardPwd: cardKeyStr,
                };
                let res = await exchangeToGiftCard(params);
                if (res.success) {
                    uni.showToast({
                        icon: 'none',
                        title: '绑定成功',
                    });
                    let url = '/packages/third-gift-card/pages/card-list/main';
                    this.$sKit.layer.useRouter(url);
                }
            } else if (this.cardKey.length === 16) {
                // 绑定礼品卡
                this.bindGiftCardFun()
                    .then(res => {
                        this.$sKit.mpBP.tracker('绑定礼品卡', {
                            seed: 'bindGiftCardBiz',
                            pageID: 'bindSuccessToast',
                            refer: this.refer,
                            channelID: clientCode,
                            address: this.cityName,
                        });
                        this.$store.dispatch('zjShowModal', {
                            title: '绑定成功',
                            confirmText: '确认',
                            confirmColor: '#E64F22',
                            success: res => {
                                if (res.confirm) {
                                    uni.navigateBack({
                                        delta: 1, //返回的页面数
                                    });
                                } else if (res.cancel) {
                                }
                            },
                        });
                    })
                    .catch(res => {
                        this.$util.tipsToastNoicon(res || '绑定失败');
                    });
            } else {
                uni.showToast({
                    title: '卡密输入有误',
                    mask: true,
                });
                return;
            }

            // #ifndef MP-MPAAS
            // * type	是	礼品卡绑定类型：
            // 1—卡密绑卡；
            // 2—卡号绑卡；
            // password	否	卡密，type=1时该字段为必填项

            // let params = {
            //     type: 1,
            //     password: this.cardKey,
            // };
            // let res = await giftCardBind(params);
            // if (res.success) {
            //     this.$store.dispatch('zjShowModal', {
            //         title: '绑定成功',
            //         confirmText: '确认',
            //         confirmColor: '#E64F22',
            //         success: res => {
            //             if (res.confirm) {
            //             } else if (res.cancel) {
            //             }
            //         },
            //     });
            // } else {
            //     this.$util.tipsToastNoicon('绑定失败');
            // }
            // #endif
        },
    },
};
</script>
<style lang="scss">
.keys_input_placeholder {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    text-align: left;
    font-style: normal;
}
</style>
<style scoped lang="scss">
/* @import url(); 引入css类 */

.gift_content {
    height: 100%;

    .title {
        font-weight: 500;
        font-size: 16px;
        color: #000000;
        line-height: 23px;
        text-align: center;
        font-style: normal;
        margin-top: 25px;
    }

    .step_div {
        margin-top: 15px;

        .line_div {
            width: 38px;
            height: 1px;
            margin: 0 15px;
            border-bottom: 1px dashed #ff6133;
        }

        .step_left {
            .icon {
                width: 20px;
                height: 22px;
                margin-right: 5px;
            }

            .step_title {
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                font-style: normal;
            }
        }
    }

    .card_keys {
        padding: 0 16px;
        margin-top: 30px;

        .card_keys_title {
            font-weight: 500;
            font-size: 16px;
            color: #000000;
            line-height: 23px;
            text-align: left;
            font-style: normal;
        }

        .keys_input {
            width: 100%;
            height: 40px;
            margin-top: 12px;
            border-radius: 4px;
            padding-left: 12px;
            box-sizing: border-box;
            border: 1px solid #dfdfed;
        }

        .button {
            width: 100%;
            margin-top: 25px;
            height: 44px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 18px;
            color: #ffffff;
            line-height: 44px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
