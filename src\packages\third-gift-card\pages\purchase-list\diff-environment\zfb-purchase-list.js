// #ifdef MP-ALIPAY
import { mapState, mapGetters } from 'vuex';
export default {
    onLoad(options) {
        this.$nextTick(() => {
            let result = this.$refs['handlePasswordKeyboardRef'];
            this.$sKit.keyBordPlugin.initRef(result);
            this.$store.commit('setAccountDataPlugin', result);
            this.passwordKeyboardRef = result;
        });
    },
    computed: {
        ...mapState({
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    methods: {
        giftCardQueryCodeFun(giftCardNo) {
            return new Promise((resolve, reject) => {
                this.passwordKeyboardRef.openKeyboard(
                    'password_unique1',
                    6,
                    async pwd => {},
                    () => {
                        uni.showLoading();
                        this.accountDataPlugin
                            .getGiftCardCipher('password_unique1', giftCardNo)
                            .then(res => {
                                uni.hideLoading();
                                if (res.code == 0) {
                                    console.log(res, '账户插件返回');
                                    resolve(res.msg);
                                } else {
                                    reject(res.msg);
                                }
                            })
                            .catch(res => {
                                uni.hideLoading();
                                reject(res.msg);
                            });
                    },
                );
            });
        },
    },
};
// #endif
