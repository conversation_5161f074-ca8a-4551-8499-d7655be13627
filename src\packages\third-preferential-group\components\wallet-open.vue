<template>
    <div class="wallet-open">
        <div class="img">
            <img :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" />
            <!-- <img v-else src="../images/card-default2.png" alt="" /> -->
        </div>
        <div class="text-content">
            <div class="title">您已开通昆仑e享卡，如想加入优待证专属群组点击“申请加入优待证专属群组”按钮进行申请</div>
            <!-- <div class="title-text">如您是退伍军人</div>
    <div class="title-text" style="margin-bottom: 40px;">可点击"申请加入群组"按钮申请加入退伍军人群组</div> -->
            <div class="text">温馨提示：</div>
            <div class="text" style="margin-bottom: 40px"
                >申请加入群组后，我们会对您的优待证持证人身份进行审核，审核通过后，我们会发送站内信邀请您加入优待证群组，享受专属优惠。</div
            >
            <div class="btn primary-btn" @click="userJoin">申请加入优待证群组</div>
        </div>
    </div>
</template>

<script>
import { currentUsed } from '../../../s-kit/js/v3-http/https3/user';
import { mapGetters } from 'vuex';

export default {
    data() {
        return {
            skinCurrentId: [],
        };
    },
    computed: {
        ...mapGetters(['walletSkin']),
    },
    mounted() {
        this.$store.dispatch('getCurrentImg', () => {});
    },
    watch: {
        '$store.state.wallet.walletSkin': {
            handler(val, oldVal) {},
            deep: true,
        },
    },
    methods: {
        //申请加入优待证群组
        userJoin() {
            this.$emit('userJoin', 1);
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet-open {
    padding-top: 16px;
    text-align: center;
    .img {
        margin: 0 auto;
        // width: 100%;
        // height: 181px;
        width: 342px;
        height: 216px;
        margin-bottom: 20px;
        border-radius: 16px;
        overflow: hidden;
        // background: rgba(0, 0, 0, 0.5);
        // box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.5);
        img {
            width: 100%;
            height: 100%;
        }
    }

    .text-content {
        padding: 0 15px;
        
        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            line-height: 23px;
            text-align: center;
            margin-bottom: 16px;
        }
        .title-text {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
            text-align: center;
        }
        .text {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            line-height: 17px;
        }
        .btn {
            height: 44px;
            border-radius: 8px;
            line-height: 44px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 60px;
        }
    }
}
</style>
