<template>
    <div class="coupon-list-view">
        <div
            class="coupon-list-item"
            :class="{ unavailable: !available }"
            v-for="(item, index) in datalist"
            :key="index"
            @click="clickItem(index)"
        >
            <!-- <img
        :src="
          selectIndex == index && ispop
            ? '/static/homeIcon/coupon-select.png'
            : '/static/homeIcon/coupon-gray-border.png'
        "
        class="coupon-list-bgimage"
      >-->
            <img src="@/static/coupon-bg.png" alt class="coupon-list-bgimage" />
            <div class="coupon-conten-view">
                <div class="coupon-title-view">
                    <div class="coupon-condition-bg">
                        <!-- <img
              :src="item.bizType == '5' ? '/static/jiaobiao-yp.png' : '/static/jiaobiao-fy.png'"
              alt
              class="jiaobiao-bg"
            />-->
                        <img src="@/static/jiaobiao-yp.png" class="jiaobiao-bg" v-if="item.bizType == '5'" alt />
                        <img src="@/static/jiaobiao-fy.png" class="jiaobiao-bg" v-else alt />
                        <div class="coupon-type-text">{{ item.bizTypeText }}</div>
                        <div class="coupon-available-bg" v-if="!available"></div>
                    </div>
                    <div class="coupon-price-view unavailable-coupon-top">
                        <!-- <div class="coupon-price-icon">￥</div> -->
                        <!-- <div class="coupon-price-text">{{ item.faceValue }}</div> -->
                        <span class="coupon-price-text-big">{{ item.couponType == '10' ? item.faceValue : item.discount }}</span>
                        <span class="coupon-price-icon-small">{{ item.couponType == '10' ? '元' : '折' }}</span>
                    </div>
                    <!-- <div class="coupon-price-condition unavailable-coupon-bottom">
            {{ item.bizTypeText }}
          </div>-->
                    <!-- <div class="coupon-condition-bg">
            {{ item.bizTypeText }}
          </div>-->
                </div>
                <div class="coupon-price-detail">
                    <div class="coupon-price-title ellipsis unavailable-coupon-top">{{ item.typeTitle }}</div>
                    <div class="coupon-price-time unavailable-coupon-bottom">
                        <!-- {{ item.couEndDate }}到期 -->
                        {{ item.typeName }}
                    </div>
                </div>
                <div class="right-txt">
                    {{ item.couEndDate }}
                    <span v-if="available">过期</span>
                </div>
                <div class="coupon-icon">
                    <img src="@/static/used-img.png" alt class="img" v-if="selectIndex == 1" />
                    <img src="@/static/time-out.png" alt class="img" v-if="selectIndex == 2" />
                </div>
                <!-- <div class="coupon-use-btn" v-if="available">立即使用</div>
        <img
          v-if="ispop"
          class="coupon-image-icon"
          :src="
            selectIndex == index
              ? '/static/select-icon.png'
              : '/static/homeIcon/normal.png'
          "
        >-->
                <!-- <div v-else class="coupon-use-text">立即使用</div> -->
            </div>
        </div>
    </div>
</template>

<script>
import myPrice from '@/components/price/price.vue';
export default {
    components: {
        myPrice,
    },
    props: {
        ispop: {
            type: Boolean,
            default: false,
        },
        datalist: {
            default: [],
            type: Array,
        },
        index: {
            default: -1,
            type: Number,
        },
        available: {
            default: true,
            type: Boolean,
        },
    },
    computed: {
        selectIndex() {
            return this.index;
        },
    },
    data() {
        return {};
    },
    mounted() {
        console.log('selectIndex', this.selectIndex);
    },
    methods: {
        clickItem(index) {
            this.$emit('click', index);
        },
    },
};
</script>

<style lang="scss" scoped>
.coupon-list-view {
    margin-left: 15px;
    width: 345px;
    overflow: hidden;
    padding-bottom: 15px;

    .coupon-list-item {
        margin-top: 10px;
        position: relative;

        .coupon-list-bgimage {
            width: 345px;
            height: 90px;
            display: block;
        }

        .coupon-icon {
            position: absolute;
            top: 0;
            right: 0;
            width: 102rpx;
            height: 95rpx;

            .img {
                width: 100%;
                height: 100%;
                display: block;
            }
        }

        .coupon-conten-view {
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            width: 345px;
            height: 90px;
            align-items: center;

            .coupon-title-view {
                // display: flex;
                // align-items: center;
                // flex-direction: column;
                width: 90px;

                .coupon-price-view {
                    // color: $btn-color;
                    padding-top: 10px;
                    color: #fe3d2d;
                    text-align: center;
                    font-weight: 500;
                    width: 100%;

                    // align-items: flex-end;
                    // .coupon-price-icon {
                    //   font-size: 15px;
                    //   padding-bottom: 1px;
                    // }
                    // .coupon-price-text {
                    //   font-size: 24px;
                    // }
                    .coupon-price-text-big {
                        font-size: 52rpx;
                    }

                    .coupon-price-icon-small {
                        padding-left: 2px;
                        padding-bottom: 3px;
                        font-size: 30rpx;
                        font-weight: 400;
                        align-self: flex-end;
                    }
                }

                .coupon-condition-bg {
                    width: 119rpx;
                    height: 39rpx;
                    position: relative;
                    margin-top: -30px;
                    margin-left: 50%;
                    transform: translateX(-50%);
                    .coupon-available-bg {
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background-color: rgba(250, 229, 231, 0.6);
                    }
                    .jiaobiao-bg {
                        width: 100%;
                        height: 100%;
                    }

                    .coupon-type-text {
                        font-size: 12px;
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        color: #fff;
                        width: 100%;
                        text-align: center;
                        line-height: 39rpx;
                    }
                }

                // .coupon-price-condition {
                //   color: $btn-color;
                //   padding-top: 3.5px;
                //   font-size: 12px;
                // }
            }

            .coupon-price-detail {
                // margin-left: 10px;
                margin-left: 30px;
                flex: 1;

                .coupon-price-title {
                    font-size: 16px;
                    color: #333333;
                    font-weight: 700;
                    line-height: 30px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                    width: 240rpx;
                }

                .coupon-price-time {
                    color: #909090;
                    font-size: 24rpx;
                    line-height: 60rpx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                    width: 240rpx;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                }
            }

            .right-txt {
                color: #909090;
                font-size: 12px;
                margin-right: 5px;
                // padding-top: 20rpx;
            }

            // .coupon-use-btn{
            //   width: 120rpx;
            //   line-height: 48rpx;
            //   border: 1rpx solid #FF8200;
            //   border-radius: 6rpx;
            //   background-color: #FFF6EC;
            //   font-size: 24rpx;
            //   color: #FF8200;
            //   text-align: center;
            //   margin-right: 20rpx;
            // }
            .coupon-image-icon {
                width: 20px;
                height: 20px;
                display: block;
                margin-right: 15px;
            }

            .coupon-use-text {
                box-sizing: border-box;
                color: $btn-color;
                border: 0.5px solid $btn-color;
                line-height: 24px;
                width: 60px;
                text-align: center;
                font-size: 12px;
                border-radius: 3px;
                margin-right: 15px;
            }
        }

        &.unavailable {
            background: rgba(255, 255, 255, 0.35) !important;
            z-index: 10;

            .unavailable-coupon-top {
                color: #cacaca !important;
            }

            .unavailable-coupon-bottom {
                color: #d7d7d7 !important;
            }
        }
    }
}
</style>
