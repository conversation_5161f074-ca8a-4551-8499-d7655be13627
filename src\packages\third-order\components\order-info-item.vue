<template>
    <div class="cards">
        <!-- orderStatus: 状态（已完成=4，已取消=5，，外送-待收货=6，自提-待提货=8）-->

        <!-- 自提订单 -->
        <div class="order" v-if="order.orderSubType == 23">
            <!-- orderStatus： 4 已完成状态显示提货凭证 -->
            <div class="order-info" v-if="orderStatus == 4">
                <div class="order-info-proof">提货凭证</div>
                <div class="items-1">
                    <span>收货人</span>
                    <span>{{ order.consignee || '' }} {{ order.consigneePhone || '' }}</span>
                </div>
                <div class="items-2">
                    <span class="left">车牌号</span>
                    <div class="right">{{ $sKit.layer.formatVehicleNum(order.licensePlate) }}</div>
                </div>
            </div>

            <!--  orderStatus： 除了4和9，其他状态显示预计提货时间 -->
            <div class="order-info" v-else>
                <div class="items-1">
                    <span>收货人</span>
                    <span>{{ order.consignee || '' }} {{ order.consigneePhone || '' }}</span>
                </div>
                <div class="items-2">
                    <span class="left">车牌号</span>
                    <div class="right">
                        {{ $sKit.layer.formatVehicleNum(order.licensePlate) }}
                        <!-- <span class="car" @click.stop="handleDeliveryCar">送货到车</span> -->
                    </div>
                </div>

                <div class="items-1">
                    <span>预计提货时间</span>
                    <span>{{ order.expectPickUpTime || '' }}</span>
                </div>
            </div>

            <!-- 商品列表 -->
            <div class="order-details" v-for="(item, index) in order.orderItems" :key="index">
                <!-- 商品信息 -->
                <div class="order-details-item">
                    <div class="order-details-item-logo">
                        <img :src="item.imgUrl" alt="" />
                    </div>
                    <div class="order-details-item-content fl-column fl-jus-aro">
                        <div class="order-details-item-content-title single-line">
                            {{ item.productName || '' }}
                        </div>

                        <div class="order-details-item-content-info">
                            <div class="fl-row fl-jus-bet">
                                <div class="order-details-item-content-info-newPrice">￥{{ item.unitPrice || '' }} </div>
                                <div class="order-details-item-content-info-num">x{{ item.productQty || '' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品信息汇总 -->
                <div class="order-other">
                    <div class="items-2">
                        <div class="left">商品编号</div>
                        <div class="right">
                            {{ item.productNo || '' }}
                            <p class="right-btn" @click="handleCopy(item.productNo)">复制</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="items-1 page-allpadding">
                <span>订单金额</span>
                <span>{{ order.orderTotalAmount || 0 }}元</span>
            </div>
        </div>

        <!-- 外送订单 -->
        <div class="order" v-else>
            <!-- orderStatus： 4 已完成状态显示提货凭证 -->
            <div class="order-info" v-if="orderStatus == 4">
                <div class="order-info-proof">提货凭证</div>
                <div class="items-1">
                    <span>收货人</span>
                    <span>{{ order.consignee || '' }} {{ order.consigneePhone || '' }}</span>
                </div>
                <div class="items-1">
                    <span>收货地址</span>
                    <span class="address">四川省成都市青羊区四川国际大厦</span>
                </div>
            </div>

            <!--  orderStatus： 除了4和9，其他状态显示预计提货时间 -->
            <div class="order-infoes" v-else>
                <!-- 骑手配送地图展示  已取消订单不展示骑手地图-->
                <div class="order-info-map" v-if="orderStatus === 10">
                    <!-- <h2>骑手地图展示</h2> -->
                    <DeliveryMap></DeliveryMap>
                </div>

                <div class="items-1 page-padding">
                    <span>收货人</span>
                    <span>{{ order.consignee || '' }} {{ order.consigneePhone || '' }}</span>
                </div>
                <div class="items-1 page-padding">
                    <span>收货地址</span>
                    <span class="address">{{ order.consigneeAddress || '' }}</span>
                </div>
                <div class="items-1 page-padding">
                    <span>预计配送时间</span>
                    <span>{{ order.expectPickUpTime || '' }}</span>
                </div>
            </div>

            <!-- 商品列表 -->
            <div class="order-details" v-for="(item, index) in order.orderItems" :key="index">
                <!-- 商品信息 -->
                <div class="order-details-item">
                    <div class="order-details-item-logo">
                        <img :src="item.imgUrl" alt="" />
                    </div>
                    <div class="order-details-item-content fl-column fl-jus-aro">
                        <div class="order-details-item-content-title single-line">
                            {{ item.productName || '' }}
                        </div>

                        <div class="order-details-item-content-info">
                            <div class="fl-row fl-jus-bet">
                                <div class="order-details-item-content-info-newPrice">￥{{ item.unitPrice || '' }} </div>
                                <div class="order-details-item-content-info-num">x{{ item.productQty || '' }}</div>
                            </div>
                        </div>
                        <!-- 申请售后  满足售后条件的商品才能申请售后 -->
                        <div class="order-details-item-content-apply" v-if="item.afterSales">
                            <p class="order-details-item-content-apply-btn" @click="handleSalesService(item)">申请售后</p>
                        </div>
                    </div>
                </div>

                <!-- 商品信息汇总 -->
                <div class="order-other">
                    <div class="items-2">
                        <div class="left">商品编号</div>
                        <div class="right">
                            {{ item.productNo || '' }}
                            <p class="right-btn" @click="handleCopy(item.productNo)">复制</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="items-1 page-allpadding">
                <span>订单金额</span>
                <span>{{ order.orderTotalAmount || 0 }}元</span>
            </div>
        </div>

        <!-- 订单详细信息展示 -->
        <div class="master">
            <div class="items-1">
                <span>创建时间</span>
                <span>{{ order.createTime || '' }}</span>
            </div>
            <div class="items-2">
                <div class="left">订单编号</div>
                <div class="right">
                    {{ order.orderNo || '' }}
                    <p class="right-btn" @click="handleCopy(order.orderNo)">复制</p>
                </div>
            </div>
            <div class="items-1">
                <span>支付渠道</span>
                <span>{{ order.payChannel || '' }}</span>
            </div>
            <div class="items-1">
                <span>订单类型</span>
                <span>{{ getOrderType(order.orderSubType) }}</span>
            </div>
            <!-- <div class="items-1">
                <span>优惠总额</span>
                <span>￥{{ order.discountTotalAmount || '0' }}</span>
            </div> -->
            <!-- <div class="items-1">
                <span>订单金额</span>
                <span>{{ order.orderTotalAmount || 0 }}元</span>
            </div> -->
            <div class="cell">
                <div class="cell-title">
                    <span class="cell-title-left">优惠金额</span>
                    <span class="cell-title-right">{{ order.discountTotalAmount || 0 }}元</span>
                </div>
                <div class="cell-list" v-for="(v, k) in order.discountList" :key="k">
                    <span>{{ v.payMethodName || '' }}</span>
                    <span>{{ v.payAmount + '元' }}</span>
                </div>
            </div>
            <div class="cell">
                <div class="cell-title">
                    <span class="cell-title-left">实付金额</span>
                    <span class="cell-title-right">{{ order.actualPayTotalAmount || 0 }}元</span>
                </div>
                <div class="cell-list" v-for="(v, k) in order.payDetailList" :key="k">
                    <span>{{ v.payMethodName || '' }}</span>
                    <span>{{ v.payAmount + '元' }}</span>
                </div>
            </div>
            <div class="items-1" v-if="order.orderSubType == 36">
                <span>配送费</span>
                <span>￥{{ order.deliveryMoney || '0' }}</span>
            </div>
            <div class="items-1">
                <span>开票状态</span>
                <span>{{ getInvoiceFlag(order.invoiceFlag) }}</span>
            </div>
            <!-- <div class="items-1">
                <span>支付折扣</span>
                <span>￥{{ order.receivedTotalAmount || '0' }}</span>
            </div> -->
            <!-- <div class="items-3">
                <div class="title">支付明细</div>
                <div class="content" v-for="(item, index) in order.payItems" :key="index">
                    <span>{{ item.payMethodName || '' }}</span>
                    <span>￥{{ item.payAmount || '0' }}</span>
                </div>
            </div> -->
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
export default {
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
        orderStatus: {
            type: Number,
            default: 4,
        },
    },
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:被复制的内容
         * @return        {*}
         */
        handleCopy(value) {
            console.log('🚀 ~ file: order-info-item.vue:168 ~ copy ~ value:');
            // uni.setClipboardData({
            //     data: String(value), //要被复制的内容
            //     success: () => {
            //         this.$sKit.layer.showToast({
            //             title: '复制成功',
            //         });
            //     },
            // });
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },

        /**
         * @description  : 匹配回显订单类型
         * @param         {*} flag:
         * @return        {*}
         */
        getOrderType(flag) {
            if (flag == 11) {
                return 'e享加油';
            } else if (flag == 13) {
                return '后支付加油';
            } else if (flag == 23) {
                return 'e享购';
            } else {
                return '';
            }
        },
        /**
         * @description  : 匹配回显订单发票状态文本
         * @param         {*} flag:
         * @return        {*}
         */
        getInvoiceFlag(flag) {
            if (flag == 0) {
                return '未开票';
            } else if (flag == 1) {
                return '已开票';
            } else if (flag == 2) {
                return '已提交';
            } else if (flag == 3) {
                return '不可开票';
            } else if (flag == 4) {
                return '不可开票';
            } else if (flag == 5) {
                return '不可开票';
            } else if (flag == 6) {
                return '不可开票';
            } else if (flag == 7) {
                return '不可开票';
            } else {
                return '';
            }
        },
        /**
         * @description: 送货到车
         * @return {*}
         */
        handleDeliveryCar() {
            console.log('🚀 ~ file: order-info-item.vue:193 ~ handleDeliveryCar ~ handleDeliveryCar:');
            this.$store.dispatch('zjShowModal', {
                title: '请确认您的车牌号',
                content: '京A 884H7',
                confirmText: '确认并通知店员',
                cancelText: '更换',
                confirmColor: '#FF8200',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        // uni.navigateTo({
                        //     url: '/packages/o2o-order/pages/checkout-counter/main',
                        // });
                        console.log('用户点击确定');
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },

        /**
         * @description: 申请售后
         * @param {*} item
         * @return {*}
         */
        handleSalesService(item) {
            this.$store.dispatch('zjShowModal', {
                title: '',
                content: '请将售后商品送至购买网点，工作人员审核后，发起退款。',
                confirmText: '确认',
                confirmColor: '#FF8200',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.cards {
    .order {
        background-color: #fff;
        // padding: 10px;
        border-radius: 10px;
        overflow: hidden;

        &-info {
            border-bottom: 1px dashed #dddddd;
            margin-bottom: 10px;
            padding: 10px;

            &-proof {
                height: 30px;
                line-height: 30px;
                color: #000000;
                font-weight: 500;
                font-size: 16px;
            }

            // 骑手地图
            &-map {
                width: 100%;
                height: 6rem;
                // background-color: lightcyan;
                margin-bottom: 10px;
                box-sizing: border-box;
            }
        }

        &-infoes {
            border-bottom: 1px dashed #dddddd;
            margin-bottom: 10px;
            padding-bottom: 10px;

            &-proof {
                height: 30px;
                line-height: 30px;
                color: #000000;
                font-weight: 500;
                font-size: 16px;
            }

            // 骑手地图
            &-map {
                width: 100%;
                height: 6rem;
                // background-color: lightcyan;
                margin-bottom: 10px;
                box-sizing: border-box;
            }
        }

        &-details {
            width: 100%;
            padding: 0 10px;

            &-item {
                width: 100%;
                height: 100%;
                // margin-top: 10px;
                display: flex;
                justify-content: space-between;

                &-logo {
                    padding: 5px;
                    border: 1px solid #f2f2f2;
                    border-radius: 5px;

                    img {
                        width: 70px;
                        height: 70px;
                        border-radius: 5px;
                    }
                }

                &-content {
                    width: 73%;
                    padding: 5px 0;
                    justify-content: space-around;

                    &-title {
                        margin-top: 5px;
                        font-size: 14px;
                        font-weight: bold;
                    }

                    &-info {
                        margin: 5px 0;

                        &-newPrice {
                            font-size: 14px;
                            color: #ff4000;
                        }

                        &-num {
                            font-weight: bold;
                        }
                    }

                    &-apply {
                        width: 100%;
                        display: flex;
                        justify-content: flex-end;

                        &-btn {
                            width: 70px;
                            height: 24px;
                            font-size: 13px;
                            text-align: center;
                            line-height: 24px;
                            border-radius: 2px;
                            border: 1px solid #333333;
                        }
                    }
                }
            }
        }

        &-other {
            margin-top: 10px;
            // border: 1px solid red;
        }
    }

    .master {
        background-color: #fff;
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0 20px 0;
    }
}

.page-padding {
    padding: 0 10px;
}

.page-allpadding {
    padding: 0 10px 10px 10px;
}

.items-1 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    width: 100%;

    span:nth-child(1) {
        color: #333333;
    }

    span:nth-child(2) {
        color: #666666;
    }

    .address {
        display: inline-block;
        text-align: right;
        width: 240px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.items-2 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    width: 100%;

    .left {
        color: #333333;
    }

    .right {
        color: #666666;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-btn {
            padding: 1px 2px;
            // height: 16px;
            line-height: 16px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #999999;
            text-align: center;
            color: #666 !important;
            font-size: 10px;
            margin-left: 5px;
        }
    }

    .car {
        color: #e64f22 !important;
        border: 1px solid #e64f22 !important;
    }
}

.items-3 {
    line-height: 30px;

    .content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-left: 15px;
        color: #666666;
    }
}

.cell {
    &-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 66rpx;
    }

    &-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 30rpx;
        color: #666666;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 66rpx;
    }
}
</style>
