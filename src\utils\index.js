import validation from './test';
import Store from '../store/index';
import lodashCloneDeep from 'lodash.clonedeep';

import CryptoJS from 'crypto-js';
import CONFIG from './config';

// 查看pdf协议
function viewPdfAgreement(url) {
    // if (uni.getSystemInfoSync().platform == "android") {
    wx.downloadFile({
        url: url, //示例的url地址
        success: function (resinfo) {
            console.log('pdf协议文件已下载');
            let path = resinfo.tempFilePath;
            console.log(path, resinfo);
            wx.openDocument({
                filePath: path,
                fileType: 'pdf',
                success: function (rest) {
                    console.log('打开文件成功');
                    console.log(rest);
                },
                fail: function (error) {
                    wx.showToast({
                        icon: 'none',
                        title: '未找到该协议',
                    });
                },
            });
        },
        fail: function (err) {
            console.log('fail');
            console.log(err);
            wx.showToast({
                icon: 'none',
                title: '未找到该协议',
            });
        },
    });
    // } else {
    //   uni.navigateTo({
    //     url:
    //       "/packages/web-view/pages/home/<USER>" +
    //       encodeURIComponent(url),
    //   });
    // }
}
// 判断接口返回图片路径是否有http，有返回，没有拼接域名返回
function imgPath(path) {
    let pathCopy = path || '';
    if (pathCopy.slice(0, 4) === 'http' || !path) {
        return path;
    } else {
        return CONFIG.splicBaseUrl + path;
    }
}
// 支付密码加密
function payPassEncryption(password) {
    const md5 = str => CryptoJS.MD5(str).toString().toUpperCase();
    let key = 'HHFkg7l7ZrbETlL1pjaFGakZUldkMDe4';
    let keyHex = CryptoJS.enc.Utf8.parse(key);
    let encrypted = CryptoJS.AES.encrypt(md5(password), keyHex, {
        iv: [],
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    });

    let encryptData = encrypted.ciphertext.toString().toUpperCase();
    return encryptData;
}

// 检查手机号码是否正确
function checkMobile(str) {
    let reg = /^1[23456789]\d{9}$/;
    if (reg.test(str)) {
        return true;
    }
    return false;
}

function formatNumber(n) {
    const str = n.toString();
    return str[1] ? str : `0${str}`;
}

// 时间戳转时间格式
function formatTime(_date, type) {
    const date = new Date(_date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    const t1 = [year, month, day].map(formatNumber).join('-');
    const t2 = [hour, minute, second].map(formatNumber).join(':');

    if (type === 'date') {
        return t1;
    } else {
        return `${t1} ${t2}`;
    }
}

// 时间格式转时间戳
function timeChangetype(stringTime) {
    var timestamp2 = Date.parse(new Date(stringTime));
    return timestamp2;
}

function setStorage(key, value) {
    wx.setStorageSync(key, value);
}

function getStorage(key) {
    return wx.getStorageSync(key);
}

function removeStorage(key) {
    return wx.removeStorageSync(key);
}

function getLocation() {
    return new Promise((resolve, reject) => {
        wx.getLocation({
            complete: function (res) {
                resolve(res);
            },
        });
    });
}

function deepCopy(p, c) {
    c = c || {};
    for (var i in p) {
        if (typeof p[i] === 'object') {
            c[i] = p[i].constructor === Array ? [] : {};
            deepCopy(p[i], c[i]);
        } else {
            c[i] = p[i];
        }
    }
    return c;
}

function cloneDeep(v) {
    return lodashCloneDeep(v);
}

function noneToast(msg) {
    return new Promise(function (resolve, reject) {
        wx.showToast({
            title: msg,
            icon: 'none',
            duration: 2000,
            success: resolve,
        });
    });
}

function tipsToastNoicon(msg) {
    return new Promise(function (resolve, reject) {
        wx.showToast({
            title: msg,
            icon: 'none',
        });
    });
}

function successToast(msg) {
    return new Promise(function (resolve, reject) {
        wx.showToast({
            title: msg,
            icon: 'success',
            duration: 2000,
            success: resolve,
        });
    });
}

function warningToast(msg) {
    return new Promise(function (resolve, reject) {
        wx.showToast({
            title: msg,
            duration: 2000,
            image: '/static/icons/icon_error.png',
            success: resolve,
        });
    });
}

function showModal(msg, hideCancel, confirmColor, cancelColor, confirmText = '确定', cancelText = '取消') {
    return new Promise(function (resolve, reject) {
        uni.showModal({
            title: '提示',
            content: msg,
            confirmColor: confirmColor || '#FF8200',
            cancelColor: cancelColor || '#666666',
            showCancel: !hideCancel,
            confirmText: confirmText,
            cancelText: cancelText,
            success: res => {
                if (res.confirm) {
                    resolve(res);
                } else if (res.cancel) {
                    return reject(res);
                }
            },
        });
    });
}

/**
 * 获取元素属性值
 * @param el
 * @returns {Promise}
 */
function getEleRect(el) {
    return new Promise(resolve => {
        wx.createSelectorQuery()
            .select(el)
            .boundingClientRect(rect => {
                resolve(rect);
            })
            .exec();
    });
}

function getEleRectAll(els) {
    return new Promise(resolve => {
        wx.createSelectorQuery()
            .selectAll(els)
            .boundingClientRect(function (rects) {
                resolve(rects);
            })
            .exec();
    });
}

function scanCode() {
    return new Promise((resolve, reject) => {
        uni.scanCode({
            success: function (res) {
                console.log('扫码结果', res);
                if (res.scanType === 'UPC_E') {
                    warningToast('扫码失败');
                } else {
                    resolve(res);
                }
            },
        });
    });
}

function validatePhone(phone) {
    if (phone === '' || !/^1(3|4|5|7|8|9)\d{9}$/.test(phone)) {
        warningToast('手机号不正确');
        return false;
    } else {
        return true;
    }
}

function randomString(len) {
    len = len || 32;
    let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    let maxPos = $chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

function rmCode(str = '') {
    // eslint-disable-next-line no-useless-escape
    const pattern = /[`~!@#%$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g;
    return str.replace(pattern, '');
}

function getUrlParam(name, url) {
    let reg = new RegExp('([?]|&)' + name + '=([^&]*)(&|$)');
    let r = url.substr(1).match(reg);
    if (r != null) {
        return decodeURIComponent(r[2]);
    }
    return '';
}

function addUrlParams(url) {
    if (url.includes('?') && url.includes('&')) {
        return url.indexOf('?') > -1 ? `${url}&` : `${url}?`;
    } else {
        return url;
    }
}

// 添加单位，如果有rpx，%，px等单位结尾或者值为auto，直接返回，否则加上rpx单位结尾
function addUnit(value = 'auto', unit = 'rpx') {
    value = String(value);
    // 用uView内置验证规则中的number判断是否为数值
    return validation.number(value) ? `${value}${unit}` : value;
}

// 获取全局配置
function globalConfig() {
    try {
        if (Store.state && Store.state.globalConfig && typeof Store.state.globalConfig !== 'object') {
            return JSON.parse(Store.state.globalConfig);
        } else {
            return {};
        }
    } catch (e) {
        console.log(e);
    }
}

function getCurrentPageUrl() {
    /*eslint no-undef: 0*/
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    return `/${currentPage.route}`;
}

function getCurrentPageArgs() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;
    let args = '';
    for (let key in options) {
        const value = options[key];
        args += `${key}=${value}&`;
    }
    args = args.substring(0, args.length - 1);
    return args;
}

function getCurrentPageUrlWithArgs() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const url = currentPage.route;
    const options = currentPage.options;
    let urlWithArgs = `/${url}?`;
    for (let key in options) {
        const value = options[key];
        urlWithArgs += `${key}=${value}&`;
    }
    urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1);
    return urlWithArgs;
}

function throttle(func, delay) {
    let timer;
    return function (...args) {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

function isArray(arr) {
    return Object.prototype.toString.call(arr) === '[object Array]';
}

// 深度克隆
function deepClone(obj) {
    // 对常见的“非”值，直接返回原来值
    if ([null, undefined, NaN, false].includes(obj)) return obj;
    if (typeof obj !== 'object' && typeof obj !== 'function') {
        //原始类型直接返回
        return obj;
    }
    var o = isArray(obj) ? [] : {};
    for (let i in obj) {
        if (obj.hasOwnProperty(i)) {
            o[i] = typeof obj[i] === 'object' ? deepClone(obj[i]) : obj[i];
        }
    }
    return o;
}

function replaceParamVal(url, paramName, replaceVal) {
    let sIdx = url.indexOf('?') + 1;
    let href = url.substr(0, sIdx);
    let sParams = url.substr(sIdx);
    let GETs = sParams.split('&');
    let GET = {};
    for (let i = 0; i < GETs.length; i++) {
        let tmp_arr = GETs[i].split('=');
        let key = tmp_arr[0];
        if (paramName === key) {
            GET[key] = replaceVal;
        } else {
            GET[key] = tmp_arr[1];
        }
    }
    for (let key in GET) {
        if (GET[key] && GET[key] !== 'undefined') {
            href += `${key}=${GET[key]}&`;
        }
    }
    return href.slice(0, href.length - 1);
}

function formatJsonData(str) {
    if (typeof str !== 'string') return '';
    return str.substring(str.indexOf('{'), str.lastIndexOf('}') + 1);
}

// 保留两位小数
function toDecimal2(x) {
    if (!x) {
        return '0.00';
    }
    let s = x.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    if (s.length > rs + 3) {
        let f = s.substring(0, rs + 3);
        return f; //parseFloat(f) + 0.01;
    }
    return s;
}

/**
 * 更换h5路径里的token
 * @param url
 * @param token
 */
function replaceVuePathToken(url, token) {
    if (!url) return;
    if (url.includes('path')) {
        let path = this.getUrlParam('path', url);
        let resultPath = '';
        if (path.includes('token')) {
            resultPath = this.replaceParamVal(path, 'token', token);
        } else if (token) {
            if (path.includes('?')) {
                resultPath = `${path}&token=${token}`;
            } else {
                resultPath = `${path}?token=${token}`;
            }
        }
        url = this.replaceParamVal(url, 'path', encodeURIComponent(resultPath));
        console.log('replaceVuePathToken', url);
    }
    return url;
}

// 车牌号校验
function isLicenseNo(str) {
    return /^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][1-9DF][1-9ABCDEFGHJKLMNPQRSTUVWXYZ]\d{3}[1-9DF]|[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][\dABCDEFGHJKLNMxPQRSTUVWXYZ]{4}[\dABCDEFGHJKLNMxPQRSTUVWXYZ学警])$/.test(
        str,
    );
}

// 无图标的提示
function nomalToast(msg) {
    return new Promise(function (resolve, reject) {
        wx.showToast({
            title: msg,
            duration: 2000,
            icon: 'none',
            success: resolve,
        });
    });
}

/**
 * 数组元素交换位置
 * @param {array} arr 数组
 * @param {number} index1 交换项1的索引
 * @param {number} index2 交换项2的索引
 * index1和index2分别是两个数组的索引值，即是两个要交换元素位置的索引值，如1，5就是数组中下标为1和5的两个元素交换位置
 */
function swapArray(arr, index1, index2) {
    arr[index1] = arr.splice(index2, 1, arr[index1])[0];
    return arr;
}

/**
 * 数组元素交换位置 深拷贝版
 * @param {array} arr 数组
 * @param {number} index1 交换项1的索引
 * @param {number} index2 交换项2的索引
 * index1和index2分别是两个数组的索引值，即是两个要交换元素位置的索引值，如1，5就是数组中下标为1和5的两个元素交换位置
 */
function deepCloneSwapArray(arr, index1, index2) {
    let deepArr = cloneDeep(arr);
    deepArr[index1] = deepArr.splice(index2, 1, deepArr[index1])[0];
    return deepArr;
}

/* 判断是否是整数 */
function isInteger(obj) {
    return Number.isInteger(obj);
}

// 延时器 time毫秒数
const delay = time => new Promise(resolve => setTimeout(resolve, time));

// 获取唯一标识符UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

// 获取随机数
function getTYUUID(str) {
    return str.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

//uni.requestSubscribeMessage  订阅消息  参数为模板id数组  具体请看微信小程序文档
function requestSubscribeMessage(tmplIds) {
    return new Promise((resolve, reject) => {
        uni.requestSubscribeMessage({
            tmplIds,
            success: res => {
                console.log('+++++++++++++++++++++++++++ 订阅消息调用api成功 ++++++++++++++++++++++++++++++');
                console.log(res);
                resolve(res);
            },
            fail: error => {
                console.log('+++++++++++++++++++++++++++ 订阅消息调用api失败 ++++++++++++++++++++++++++++++');
                console.log(error);
                resolve(error);
            },
        });
    });
}

// traceid 获取随机6位
function guid() {
    return 'xxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

function throttleUtil(func, wait = 2000, type = 1) {
    //节流， type 1马上执行 2 隔一段时间执行
    let previous = type == 1 ? 0 : Date.now();
    return function () {
        let now = Date.now();
        let context = this;
        let args = arguments;
        if (now - previous > wait) {
            func.apply(context, args);
            previous = now;
        }
    };
}

export default {
    requestSubscribeMessage,
    payPassEncryption,
    formatTime,
    timeChangetype,
    setStorage,
    getStorage,
    removeStorage,
    getLocation,
    successToast,
    tipsToastNoicon,
    noneToast,
    warningToast,
    showModal,
    deepCopy,
    cloneDeep,
    getEleRect,
    getEleRectAll,
    validatePhone,
    randomString,
    rmCode,
    getUrlParam,
    addUrlParams,
    addUnit,
    globalConfig,
    getCurrentPageUrl,
    getCurrentPageArgs,
    getCurrentPageUrlWithArgs,
    throttle,
    scanCode,
    deepClone,
    replaceParamVal,
    formatJsonData,
    toDecimal2,
    replaceVuePathToken,
    isLicenseNo,
    nomalToast,
    swapArray,
    deepCloneSwapArray,
    isInteger,
    delay,
    generateUUID,
    getTYUUID,
    guid,
    throttleUtil,
    imgPath,
    viewPdfAgreement,
};
