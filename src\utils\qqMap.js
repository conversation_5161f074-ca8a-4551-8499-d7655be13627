/**
 * Created by <PERSON><PERSON><PERSON> on 2019/6/6.
 */
let QQMapWX = require('@/utils/qqmap-wx-jssdk.min');
let qqmapsdk = new QQMapWX({
    key: 'WQEBZ-VTP3F-BBCJR-J5GZP-XZHCQ-LRF4E', // 必填
});
export const reverseGeo = location => {
    return new Promise((resolve, reject) => {
        qqmapsdk.reverseGeocoder({ location: { ...location }, success: resolve, fail: reject });
    });
};
/**
 * 根据关键字获取相关的地址信息
 * @param {*} keyword 关键字
 * @param {*} otherOptions 其他配置 具体参考: https://lbs.qq.com/miniProgram/jsSdk/jsSdkGuide/methodGetsuggestion
 * @returns
 */
export const getSuggestion = (keyword, otherOptions = {}) => {
    return new Promise((resolve, reject) => {
        qqmapsdk.getSuggestion({
            keyword,
            ...otherOptions,
            success: resolve,
            fail: reject,
        });
    });
};
/**
 * 使用地图接口来计算距离
 * @param from 当前用户的定位算
 * @param to 目的地
 * @returns
 */
export const calculateDuration = (from, to) => {
    return new Promise((resolve, reject) => {
        qqmapsdk.calculateDistance({
            mode: 'driving', //可选值：'driving'（驾车）、'walking'（步行），不填默认：'walking',可不填
            from: from, // 从当前用户的定位算 //若起点有数据则采用起点坐标，若为空默认当前地址
            to: to, //终点坐标
            success: resolve,
            fail: reject,
        });
    });
};

/**
 * 将腾讯地图的经纬度转为百度的经纬度
 * @param {*} lng 腾讯地图对应的坐标 经度
 * @param {*} lat 腾讯地图对应的坐标 纬度
 * @returns { lng, lat } 对应的百度的坐标经纬度
 */
export const handleTxMapTransBMap = (lng, lat) => {
    let x_pi = (Math.PI * 3000.0) / 180.0; //Math.PI ~ 3.14159265358979324
    let x = lng;
    let y = lat;
    let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
    let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
    let lngs = z * Math.cos(theta) + 0.0065;
    let lats = z * Math.sin(theta) + 0.006;
    return {
        lng: lngs,
        lat: lats,
    };
};

/**
 * 坐标系转换 百度bd09转火星国内Gcj02
 * @param lon
 * @param lat
 * @returns {{latitude: number, longitude: number}}
 */
export const bd09_To_Gcj02 = (lon, lat) => {
    const x_pi = (Math.PI * 3000.0) / 180.0;
    let x = lon - 0.0065,
        y = lat - 0.006;
    let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
    let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
    const longitude = z * Math.cos(theta);
    const latitude = z * Math.sin(theta);
    return { longitude, latitude };
};

/**
 * 坐标转换，百度地图坐标转换成腾讯地图坐标
 * lng 腾讯经度（pointy）
 * lat 腾讯纬度（pointx）
 * 经度>纬度
 */
export const handleBMapTransTxMap = (lng, lat) => {
    if (lng == null || lng == '' || lat == null || lat == '') return [lng, lat];

    var x_pi = 3.14159265358979324;
    var x = parseFloat(lng) - 0.0065;
    var y = parseFloat(lat) - 0.006;
    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
    var lng = (z * Math.cos(theta)).toFixed(7);
    var lat = (z * Math.sin(theta)).toFixed(7);

    return [lng, lat];
};
