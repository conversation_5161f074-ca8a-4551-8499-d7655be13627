<template>
    <div class="container">
        <!-- 自提才显示 -->
        <oil-station-info :order="order" v-if="order.orderSubType == 48" />

        <!-- 自提模式 -->
        <div class="content" v-if="order.orderSubType == 48">
            <p :class="['content-code', orderStatus == 4 ? 'codeIsSuccess' : 'codeIsWatting']" class="">取件码</p>
            <p :class="['content-num', orderStatus == 4 ? 'numIsSuccess' : 'numIsWatting']">{{ order.pickUpCode || '' }}
            </p>
            <p class="content-des" v-if="orderStatus == 4">您的商品已取货，欢迎下次光临</p>
        </div>

        <!-- 外送模式 -->
        <!-- <div v-else-if="order.orderSubType == 36">
            <div class="content">
                <p :class="['content-code', orderStatus == 4 ? 'codeIsSuccess' : 'codeIsWatting']" class="">取件码</p>
                <p :class="['content-num', orderStatus == 4 ? 'numIsSuccess' : 'numIsWatting']">{{ order.pickUpCode ||
            '' }}</p>
                <p class="content-des" v-if="orderStatus == 4">您的商品已送达，欢迎下次光临</p>
            </div>
        </div> -->

    </div>
</template>

<script>
import OilStationInfo from './oil-station-info.vue';

export default {
    components: { OilStationInfo },
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
        orderStatus: {
            type: Number,
            default: 4,
        },
    },
    data() {
        return {};
    },
    created() { },
    mounted() { },
    methods: {},
};
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;

    .content {
        text-align: center;
        height: 110px;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        &-code {
            font-weight: 400;
            font-size: 16px;
        }

        &-num {
            font-size: 30px;
            letter-spacing: 15px;
        }

        &-des {
            color: #333333;
            font-size: 18px;
            font-weight: 500;
        }
    }

    .codeIsSuccess {
        color: #999999;
    }

    .codeIsWatting {
        color: #333333;
    }

    .numIsSuccess {
        color: #999999;
    }

    .numIsWatting {
        color: #ff6b2c;
        font-weight: 700;
    }
}
</style>
