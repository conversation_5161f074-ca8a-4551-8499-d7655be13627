<template>
    <div class="wallet-close">
        <div class="img">
            <img :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" />
            <!-- <img v-else src="../images/card-default2.png" alt="" /> -->
        </div>
        <div class="title">您需要开通昆仑e享卡才能申请加入优待证群组</div>
        <!-- <div class="title" style="margin-bottom: 40px;">才能申请加入优待证群组</div> -->
        <div class="text">温馨提示：</div>
        <div class="text" style="margin-bottom: 40px"
            >申请加入群组后，我们会对您的优待证持证人身份进行审核，审核通过后，我们会发送站内信邀请您加入优待证群组，享受专属优惠。</div
        >
        <div class="btn primary-btn" @click="toOpenAccount">开通昆仑e享卡</div>
    </div>
</template>

<script>
import { currentUsed } from '../../../s-kit/js/v3-http/https3/user';
import { mapGetters } from 'vuex';
export default {
    data() {
        return {
            skinCurrentId: [],
        };
    },
    mounted() {
        this.$store.dispatch('getCurrentImg', () => {});
    },

    computed: {
        ...mapGetters(['walletSkin']),
    },
    methods: {
        //前往开通电子账户
        toOpenAccount() {
            console.log('开通昆仑e享卡');
            let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
            let params = { refer: 'r05' };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet-close {
    padding: 0 15px;
    padding-top: 16px;
    text-align: center;
    .img {
        margin: 0 auto;
        // width: 100%;
        // height: 181px;
        width: 342px;
        height: 216px;
        margin-bottom: 20px;
        border-radius: 16px;
        overflow: hidden;
        // background: rgba(0, 0, 0, 0.5);
        // box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.5);

        img {
            width: 100%;
            height: 100%;
        }
    }

    .title {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 17px;
    }

    .btn {
        height: 44px;
        border-radius: 8px;
        line-height: 44px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 60px;
    }
}
</style>
