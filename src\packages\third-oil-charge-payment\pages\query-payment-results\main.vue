<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="wallet_details p-bf bg-F7F7FB">
            <zj-navbar title="支付订单查询" :border-bottom="false" @click="goBack"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div bg-fff">
                    <!-- <div class="fl-column fl-al-jus-cen" v-if="status == 'success'">
                        <img src="../../image/success_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">您的操作已完成</div>
                        <div class="font-12 color-999 text">如已支付，请查看充值是否到账</div>
                    </div> -->
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'fail'">
                        <img src="../../image/fail_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold"
                            >暂未查询到该笔订单的支付结果，稍后可到订单页查看，若仍未查到，请与站内工作人员沟通</div
                        >
                        <!-- <div class="amount font-16 color-333 weight-bold">建议您联系956100客服</div> -->
                    </div>
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'waiting'">
                        <img src="../../image/wait_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">正在查询支付结果</div>
                        <span class="loading">正在加载中<div class="dot">...</div> </span>
                    </div>
                </div>
                <div class="btn_div" v-if="status == 'fail'">
                    <div class="finish_verification2 color-E64F22 bg-fff" @click="backClick">返回</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { statuslOrderApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { clientCode } from '../../../../../project.config';
import { mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-charge-result',
    data() {
        return {
            status: 'waiting',
            pollTime: 3000,
            requestStatusInterval: null,
            beginTime: 0,
            refer: '',
            unPaidInfo: null,
        };
    },
    onLoad(option) {
        this.unPaidInfo = JSON.parse(decodeURIComponent(option.data));
        this.refer = this.unPaidInfo.refer || '';
        this.$sKit.mpBP.tracker('后支付加油', {
            seed: 'hpayoilBiz',
            pageID: 'orderQuerypage', // 返回sdk标识
            refer: this.refer,
            channelID: clientCode,
            address: this.cityName,
        });
        setTimeout(() => {
            this.beginTime = new Date().getTime();
            this.statuslOrderPost();
        }, this.pollTime);
    },
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
        }),
    },
    mounted() {},
    methods: {
        /**
         * @description  : 查询支付状态
         * @param
         * @return        {*}
         */
        // 查询加油订单是否支付完成
        async statuslOrderPost() {
            try {
                let params = {
                    stationCode: this.unPaidInfo.stationCode,
                    orderNo: this.unPaidInfo.orderNo,
                };
                // uni.showLoading({
                //     title: '加载中...',
                //     mask: true,
                // });
                let res = await statuslOrderApi(params, { isload: false });
                if (res.success) {
                    // 支付完成，跳转支付账单页
                    if (res.data.orderPayStatus == 4) {
                        // uni.hideLoading();
                        console.log('=====订单状态为4，支付成功');
                        let URL = '';
                        if (this.unPaidInfo?.offlineOrder) {
                            URL = `/packages/third-oil-charge-payment/pages/oil-charge-offline-pay-result/main`;
                        } else {
                            URL = `/packages/third-oil-charge-payment/pages/oil-charge-pay-result/main`;
                        }
                        let params = { ...this.unPaidInfo, refer: this.refer };
                        let type = 'redirectTo';
                        this.$sKit.layer.useRouter(URL, params, type);
                        uni.hideLoading();
                    } else {
                        // 轮询支付结果
                        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                        if (new Date().getTime() - this.beginTime > 60 * 1000) {
                            uni.hideLoading();
                            this.status = 'fail';
                            // this.$store.dispatch('zjShowModal', {
                            //     title: '',
                            //     content: '未查询到支付结果，请去我的订单中查看订单状态！',
                            //     confirmText: '确定',
                            //     success(res) {
                            //         if (res.confirm) {
                            //             uni.navigateBack({
                            //                 delta: 1,
                            //             });
                            //         } else if (res.cancel) {
                            //             console.log('用户点击取消');
                            //         }
                            //     },
                            // });
                            return;
                        }
                        this.requestStatusInterval = setTimeout(() => {
                            this.statuslOrderPost();
                        }, this.pollTime);
                    }
                } else {
                    if (this.requestStatusInterval) {
                        clearTimeout(this.requestStatusInterval);
                        this.requestStatusInterval = setTimeout(() => {
                            this.statuslOrderPost();
                        }, this.pollTime);
                    }
                }
            } catch (error) {
                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);

                if (new Date().getTime() - this.beginTime > 60 * 1000) {
                    uni.hideLoading();
                    this.status = 'fail';
                    this.$store.dispatch('zjShowModal', {
                        title: '',
                        content: '未查询到支付结果，请去我的订单中查看订单状态！',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                uni.navigateBack({
                                    delta: 1,
                                });
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                    return;
                }
                this.requestStatusInterval = setTimeout(() => {
                    this.statuslOrderPost();
                }, this.pollTime);
            }
        },
        /**
         * @description  : 点击左上角返回
         * @param
         * @return        {*}
         */
        leftGoBack() {
            const pages = getCurrentPages();
            if (this.status == 'waiting') {
                this.$store.dispatch('zjShowModal', {
                    title: '',
                    content: '支付结果仍在查询中，返回后可到订单页查看，若仍未查到，请与站内工作人员沟通，确定返回吗？',
                    confirmText: '确认',
                    cancelText: '再等等',
                    confirmColor: '#000',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            this.goBack();
                        }
                    },
                });
            } else {
                uni.navigateBack();
            }
        },
        //返回
        backClick() {
            this.goBack();
        },
        /**
         * @description  : 返回首页和上一个页面
         * @return        {*}
         */
        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        //返回首页
        backHome() {
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-bottom: 60px;
            padding-top: 60px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 12px;
            }

            .amount {
                line-height: 23px;
                padding-left: 16px;
                padding-right: 16px;
            }
            .loading {
                display: flex; /*让dot和文字在同一行*/
                align-items: center;
            }
            .dot {
                /*让点垂直居中*/
                height: 1em;
                line-height: 1;
                /*让点垂直排列*/
                display: flex;
                flex-direction: column;
                /*溢出部分的点隐藏*/
                overflow: hidden;
                font-size: 28px;
                margin-bottom: 5px;
            }
            .dot::before {
                /*三行三种点，需要搭配white-space:pre使用才能识别\A字符*/
                content: '...\A..\A.';
                white-space: pre-wrap;
                animation: div 3s infinite step-end; /*step-end确保一次动画结束后直接跳到下一帧而没有过渡*/
            }
            @keyframes div {
                33% {
                    transform: translateY(-2em);
                }
                66% {
                    transform: translateY(-1em);
                }
            }

            .text {
                line-height: 23px;
            }
        }
    }
}

.btn_div {
    margin-top: 20px;
    font-size: 15px;
    display: flex;
    flex-direction: row;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
