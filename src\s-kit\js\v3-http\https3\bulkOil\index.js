import { POST } from '../../index';
// 查询散装油预约登记列表接口 /order/bulkOil/register/list
export const bulkOilRegisterList = (params, config) => {
    return POST('order.bulkOil.register.list', params, config);
};
// 散装油预约登记接口 /order/bulkOil/register
export const bulkOilRegister = (params, config) => {
    return POST('order.bulkOil.register', params, config);
};
// 取消散装油预约登记接口 /order/bulkOil/register/cancel
export const bulkOilRegisterCancel = (params, config) => {
    return POST('order.bulkOil.register.cancel', params, config);
};
// 查询散装油预约登记详情接口 /order/bulkOil/registerInfo
export const bulkOilRegisterInfo = (params, config) => {
    return POST('order.bulkOil.registerInfo', params, config);
};
// 查询散装油购买数量接口 /order/bulkOil/configInfo
export const fuelLiters = (params, config) => {
    return POST('order.bulkOil.configInfo', params, config);
};
