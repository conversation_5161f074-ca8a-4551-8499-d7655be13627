<template>
    <div>
        <div :style="{ color }" v-if="!isDet">
            <span class="int" :style="{ fontSize: intFont }">{{ price | priceIntFilter }}</span>
            <span class="float" :style="{ fontSize: floatFont }">.{{ price | priceFloatFilter }}</span>
        </div>
        <div class="price" :style="{ color }" v-else>
            <span class="int" :style="{ fontSize: intFont }">￥{{ price | priceFilter }}</span>
        </div>
    </div>
</template>

<script>
/**
 * 价格组件   价格整数和小数分离组件
 */
export default {
    name: 'price',
    props: {
        //价格
        price: [Number, String],
        color: {
            type: String,
            default: '#f80f16',
        },
        //整数字体大小
        intFont: {
            type: String,
            default: '36px',
        },
        //小数字体大小
        floatFont: {
            type: String,
            default: '24px',
        },
        // 是否被划掉
        isDet: {
            type: Boolean,
            default: false,
        },
    },
    filters: {
        // 金额整数部分过滤器
        priceIntFilter(price) {
            return Number(price).toFixed(2).split('.')[0];
        },
        // 金额小数不分过滤器
        priceFloatFilter(price) {
            return Number(price).toFixed(2).split('.')[1];
        },
        // 金额全部
        priceFilter(price) {
            return Number(price).toFixed(2).split('.')[0] + '.' + Number(price).toFixed(2).split('.')[1];
        },
    },
};
</script>

<style lang="scss" scoped>
.price {
    margin-left: 5px;
    text-decoration: line-through;
}
</style>
