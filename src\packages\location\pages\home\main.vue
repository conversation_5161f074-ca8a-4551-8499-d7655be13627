<template>
    <div class="view">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="附近油站"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="top-view" v-if="!flag">
            <div class="serch-view">
                <input class="serch-input" type="text" v-model="sreachInput" placeholder="请输入油站编码" />
                <img class="serch-icon" @click="clickSerchInput" src="@/static/homeIcon/find.png" />
            </div>
        </div>
        <div class="service-station-view" v-if="!flag">
            <div class="service-station-item" @click="clickItem(item)" v-for="(item, index) in cityAllServiceStation" :key="index">
                <div class="ss-text-view">
                    <div class="ss-text-title">{{ item.stationName }}</div>
                    <div class="location-view-text">{{ item.distance }}km</div>
                </div>
                <div class="location-view" @click.stop="clickItem(item)">
                    <div class="rechargeBut">{{ flag === 'change' ? '选择' : '加油' }}</div>
                </div>
            </div>
            <div class="loadmore">
                <u-loadmore @loadmore="clickLoadMore" :status="loadMortState" :load-text="loadText" />
            </div>
        </div>
        <div class="service-station-view" v-if="flag == 'change'">
            <div class="service-station-item" @click="clickItem(item)" v-for="(item, index) in markerArr" :key="index">
                <div class="ss-text-view">
                    <div class="ss-text-title">{{ item.stationName + (item.openState == 1 ? '' : '（暂未开通）') }}</div>
                    <div class="location-view-text">{{ item.distance }}km</div>
                </div>
                <div class="location-view" @click.stop="clickItem(item)">
                    <div class="rechargeBut">{{ flag === 'change' ? '选择' : '加油' }}</div>
                </div>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import pageConfig from '@/utils/pageConfig.js';
import { getStationsByCityName } from '@/api/anEcardToOpenCard.js';
export default {
    data() {
        return {
            /**********上方为假数据***********/
            pageConfig: pageConfig, // 页面配置
            sreachInput: '', // 搜索框input
            serchMarker: [],
            loadText: {
                loadmore: '点击或上拉加载更多',
                loading: '努力加载中',
                nomore: '暂无更多数据',
            },
            pageSize: 10,
            pageIndex: 1,
            cityAllServiceStation: [],
            loadMortStateSetup: '',
            loadMortState: 'loading', // 上拉加载状态
            flag: '',
        };
    },
    async onLoad(options) {
        this.flag = options.flag;
        if (!this.flag) {
            this.getAllServiceStationsInTheCity();
        }
    },
    onReachBottom() {
        if (!this.flag) {
            if (this.loadMortState == 'loadmore') {
                //可以加载更多
                this.pageIndex++;
                this.getAllServiceStationsInTheCity();
            }
        }
    },
    computed: {
        ...mapState({
            markerArr: state => state.location.markerArr, // marker数据
            province: state => state.location.province, // 省份
            lat: state => state.location.lat, // 纬度
            lon: state => state.location.lon, // 经度
            // loadMortState: state => state.location.loadMortState, // 加载更多状态
            city: state => state.location.city, // 城市
        }),
    },
    watch: {
        // // 监听输入
        // sreachInput: {
        // 	handler(newName, oldName) {
        // 		let arr = []
        // 		for (let i = 0; i < this.markerArr.length; i++) {
        // 			if (this.markerArr[i].stationName.indexOf(newName) > -1) {
        // 				arr.push(this.markerArr[i])
        // 			}
        // 		}
        // 		this.serchMarker = arr
        // 	},
        // 	immediate: true
        // },
        // 监听输入
        // markerArr: {
        // 	handler(newName, oldName) {
        // 		this.serchMarker = newName
        // 	},
        // 	immediate: true
        // }
    },
    methods: {
        // 获取城市所有油站
        getAllServiceStationsInTheCity(sreachInput) {
            let params = {
                stationName: '',
                stationCode: sreachInput || '',
                city: this.city,
                // city:'朝阳',
                longitude: this.lon, // 经度
                latitude: this.lat, // 维度
                pageIndex: this.pageIndex,
                pageSize: this.pageSize,
            };
            getStationsByCityName(params).then(res => {
                if (res.data.length > 0) {
                    if (res.data.length < this.pageSize) {
                        this.loadMortState = 'nomore';
                        this.cityAllServiceStation.push(...res.data);
                        this.pageIndex = 1;
                    } else {
                        this.loadMortState = 'loadmore';
                        this.pageIndex == 1 ? (this.cityAllServiceStation = res.data) : this.cityAllServiceStation.push(...res.data);
                    }
                } else {
                    this.loadMortState = 'nomore';
                    this.cityAllServiceStation = [];
                    this.pageIndex = 1;
                }
            });
        },
        // 点击对应油站
        clickItem(item) {
            if (!this.flag) {
                uni.$emit('markerdata', {
                    item,
                });
            } else {
                let centerParam = {
                    marker: item,
                };
                this.$store.commit('setSelectMarkerToMapCenter', centerParam);
            }
            uni.navigateBack();
        },
        // 省份点击事件
        // clickProvince() {
        // 	uni.navigateTo({
        // 		url: './select-location'
        // 	})
        // },
        // 搜索按钮点击事件
        clickSerchInput() {
            if (!this.$test.enOrNum(this.sreachInput)) {
                uni.showToast({
                    title: '油站编码格式不正确',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            this.cityAllServiceStation = [];
            this.getAllServiceStationsInTheCity(this.sreachInput);
        },

        clickLoadMore() {
            if (this.loadMortState == 'loadmore') {
                //可以加载更多
                this.pageIndex++;
                this.getAllServiceStationsInTheCity();
            }
        },
        navigationBack() {
            uni.navigateBack();
            // this.$store.dispatch('uploadProvince', {
            // 	suc:() => {
            // 		uni.navigateBack()
            // 	},
            // 	selectIndex: -1
            // })
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    background-color: #f6f6f6;
    min-height: 100%;
    overflow: hidden;
    .top-view {
        display: flex;
        margin-left: 15px;
        width: 345px;
        margin-top: 10px;
        .location-top-view {
            background-color: #ffffff;
            height: 50px;
            width: 90px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;

            .location-top-image {
                width: 9.75px;
                height: 12px;
                display: block;
            }

            .location-top-text {
                font-size: 14px;
                color: #333333;
                margin-left: 10px;
            }
        }

        .serch-view {
            background-color: #ffffff;
            height: 50px;
            border-radius: 5px;
            flex: 1;
            display: flex;
            align-items: center;

            .serch-input {
                margin-left: 10px;
                flex: 1;
                font-size: 14px;
            }

            .serch-icon {
                display: block;
                width: 20px;
                height: 20px;
                margin-right: 5px;
                padding: 10px;
            }
        }
    }

    .gg-swiper {
        height: 45px;
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;

        .gg-swiper-item {
            .swiper-item-img {
                height: 45px;
                width: 345px;
                border-radius: 5px;
            }
        }
    }
    .service-station-view {
        padding-bottom: env(safe-area-inset-bottom);
        margin-bottom: 10px;
    }

    .service-station-item {
        background-color: #ffffff;
        border-radius: 5px;
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;
        display: flex;
        align-items: center;
        height: 100%;
        justify-content: space-between;
        .ss-icon {
            margin-left: 11.5px;
            margin-top: 10.5px;
            width: 31px;
            height: 30px;
            display: block;
        }

        .ss-text-view {
            overflow: hidden;
            flex: 1;
            margin-left: 10px;
            // margin-top: 10.5px;

            .ss-text-title {
                color: #333333;
                font-size: 15px;
                font-weight: 700;
                margin-top: 10px;
                width: 90%;
                line-height: 22px;
            }
            .location-view-text {
                font-size: 12px;
                color: #333333;
                margin-top: 5px;
                margin-bottom: 5px;
            }
        }

        .location-view {
            // display: flex;
            // flex-direction: column;
            // align-items: center;
            // overflow: hidden;
            // width: 55px;
            // margin-top: 15.5px;
            margin-right: 10px;
            .location-view-text {
                font-size: 12px;
                color: #333333;
                margin-top: 10px;
                // margin: 8px;
            }

            .location-view-icon {
                width: 14px;
                height: 14px;
                display: block;
            }
            .rechargeBut {
                color: #f96702;
                border: 1px solid #f96702;
                border-radius: 10rpx;
                padding: 6rpx 20rpx;
                font-size: 26rpx;
                min-width: 50px;
            }
        }
    }

    .loadmore {
        padding-top: 10px;
        padding-bottom: 10px;

        ::v-deep .u-load-more-wrap {
            background-color: transparent !important;
            view {
                background-color: transparent !important;
            }
        }
    }
}
</style>
