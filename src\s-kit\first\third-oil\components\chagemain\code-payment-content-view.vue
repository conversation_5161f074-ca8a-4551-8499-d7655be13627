<template>
    <div class="view p-bf fl-column p-LR-16">
        <div :style="{ marginTop: tabType ? '35px' : '0' }" class="content-wrap bg-fff">
            <div class="img-warp">
                <img v-if="tabType" class="p-bf" src="../../image/logo.png" alt />
            </div>
            <CodeTab @changeCode="changePayCode" :value="codeFlag"></CodeTab>
            <!-- <div class="title te-center color-333 weight-500 font-16">
                {{ `中国石油${codeFlag == 'membershipCodeFlag' ? '会员码' : '付款码'}` }}
            </div> -->
            <!-- 会员码 -->
            <div v-if="codeFlag == 'membershipCodeFlag'">
                <MembershipCode @codeChange="codeChange" ref="membershipCode" :refer="refer"></MembershipCode>
            </div>
            <!-- 付款码 -->
            <div v-if="codeFlag == 'paymentCodeFlag'">
                <PaymentCode @codeChange="codeChange" ref="paymentCode" :refer="refer"></PaymentCode>
            </div>
        </div>
        <div class="img-wrap">
            <img class="img_style" mode="scaleToFill" src="../../image/line.png" alt />
        </div>
        <div class="footer bg-fff p-LR-16">
            <div class="text-wrap">
                <div class="text1 font-15 color-333 weight-bold">温馨提示</div>
                <!-- #ifdef MP-MPAAS -->
                <div v-if="!isHarmony" class="text2 font-15 color-666 weight-400"
                    >请根据实际需求询问加油站工作人员出示紫色会员码还是橙色付款码。</div
                >
                <div v-else class="text2 font-15 color-666 weight-400"
                    >请向加油员出示会员码，会员码被扫描后请勿离开此页面，查询到订单后进行支付。</div
                >
                <!-- #endif -->
                <!-- #ifndef MP-MPAAS -->
                <div class="text2 font-15 color-666 weight-400"
                    >请向加油员出示会员码，会员码被扫描后请勿离开此页面，查询到订单后进行支付。</div
                >
                <!-- #endif -->
            </div>
        </div>
    </div>
</template>
<script>
// tab切换
import CodeTab from './code-tabs.vue';
// 会员码页面
import MembershipCode from './member-ship-code.vue';
import PaymentCode from './pay-ment-code.vue';
import { stationListApi } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { mapGetters, mapState } from 'vuex';
import { clientCode } from '../../../../../../project.config';
export default {
    name: 'paymentCode',
    components: {
        // tab切换组建
        CodeTab,
        // 会员码组建
        MembershipCode,
        // 付款码
        PaymentCode,
    },
    computed: {
        ...mapGetters(['latV3', 'lonV3', 'walletInfo']),
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
            allMarkerArr: state => state.locationV3_app.allMarkerArr, // 全部油站数组
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    props: {
        // 其他页面路由跳转携带过来的参数
        tabType: {
            default: '',
            type: String,
        },
        refer: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            // 会员码or付款码标识
            codeFlag: 'membershipCodeFlag',
            // 会员码和付款码code
            codeNumber: '',
            // 520版本临时处理支付宝小程序rops扫码轮询到结果跳转两次支付页面问题,530版本解决该问题
            temporaryProcessing: false,
        };
    },
    onShow() {},
    async mounted() {
        // 进入会员码页面查询油站信息
        await this.$store.dispatch('initLocationV3_app', {
            callback: async res => {
                // #ifdef MP-MPAAS
                const stationType = this.allMarkerArr[0]?.stationType;
                console.log(stationType, 'stationType=====');
                if (stationType && stationType != 1) {
                    console.log('');
                    // 调用付款码初始化方法
                    this.$nextTick(() => {
                        this.changePayCode('paymentCodeFlag', true);
                    });
                } else {
                    this.$nextTick(() => {
                        // 调用会员码初始化方法
                        this.changePayCode('membershipCodeFlag', true);
                    });
                }
                // #endif
                // #ifndef MP-MPAAS
                this.$nextTick(() => {
                    console.log('22222');
                    // 520版本临时处理支付宝小程序rops扫码轮询到结果跳转两次支付页面问题,530版本解决该问题
                    if (this.temporaryProcessing) return;
                    // 调用会员码初始化方法
                    this.changePayCode('membershipCodeFlag', true);
                });
                // #endif
            },
        });
    },
    methods: {
        /**
         * @description  : 切换会员码or付款码
         * @return        {*}
         */
        async changePayCode(val, init = false) {
            this.codeNumber = '';
            // 值相同禁止点击
            if (val == this.codeFlag && !init) return;
            this.codeFlag = val;
            console.log(this.codeFlag, 'this.codeFlag====');
            // 分发到父级，改变背景颜色
            this.$emit('changeBg', val);
            if (val === 'paymentCodeFlag') {
                this.$sKit.mpBP.tracker('付款码', {
                    seed: 'fkCodeBiz',
                    pageID: 'fkCodePage',
                    refer: this.refer,
                    channelID: clientCode,
                    address: this.cityName,
                });
                this.$nextTick(() => {
                    this.$refs.paymentCode.init();
                    // 获取生成付款码的code值
                    this.codeNumber = this.$refs.paymentCode.paymentCode;
                    // 清除会员码页面的定时器(查询订单的定时器)
                    this.$refs.membershipCode.clearTimerQuqeyOrder();
                    // 清除倒计时一分钟的定时器
                    this.$refs.membershipCode.clearTimerMember();
                    // 将子组件中的turnOffRecursionMember置为false，防止递归方法重新调用
                    this.$refs.membershipCode.turnOffRecursionMember = false;
                });
            } else {
                this.$sKit.mpBP.tracker('会员码', {
                    seed: 'memberCodeBiz',
                    pageID: 'userCodePage',
                    refer: this.refer,
                    channelID: clientCode,
                    address: this.cityName,
                });
                setTimeout(() => {
                    this.$nextTick(() => {
                        this.$refs.membershipCode.init();
                        // 获取生成付款码的code值
                        this.codeNumber = this.$refs.membershipCode.memberCode;
                        // 将子组件中的turnOffRecursionCode置为false，防止递归方法重新调用
                        this.$refs.membershipCode.turnOffRecursionCode = false;
                        // #ifdef MP-MPAAS
                        // 清除付款码页面的定时器(查询订单的定时器)
                        this.$refs.paymentCode.clearTimerQuqeyOrder();
                        // 清除倒计时一分钟的定时器
                        this.$refs.paymentCode.clearTimerCode();
                        // #endif
                    });
                }, 100);
            }
        },
        /**
         * @description  : 切换会员码or付款码通过子组件分发的方法，获取到的值，赋值给 this.codeNumber
         * @return        {*}
         */
        codeChange(val) {
            this.codeNumber = val;
        },
        /**
         * @description  : App中在会员码或者付款码查询到订单后跳转页面，以各种返回方式返回后，再次开启定时轮询订单任务
         * @return        {*}
         */
        restartTimer(val) {
            this.temporaryProcessing = val;
            if (this.codeFlag === 'paymentCodeFlag') {
                // 付款码初始化方法
                this.$refs.paymentCode.init();
            } else if (this.codeFlag === 'membershipCodeFlag') {
                // 会员码初始化方法
                // this.$refs.membershipCode.clearTimerMember();
                // this.$refs.membershipCode.clearTimerQuqeyOrder();
                this.$refs.membershipCode.init();
            }
        },
    },
    beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
.view {
    // margin-top: 25px;
    // padding-bottom: 30px;
    // 顶部导航
    overflow-y: scroll;
    overflow-x: hidden;

    .content-wrap {
        width: 100%;
        // height: 400px;
        position: relative;
        // margin-top: 50px;
        border-radius: 8px 8px 0 0;
        display: block;

        .img-warp {
            width: 50px;
            height: 50px;
            position: absolute;
            left: 50%;
            top: 0%;
            transform: translate(-50%, -50%);
        }

        .title {
            // margin-top: 40px;
        }

        .qr-code {
            width: 244px;
            height: 244px;
        }
    }

    .img-wrap {
        margin-top: -1px;

        .img_style {
            width: 100%;
            height: 30px;
            min-height: 30px;
            display: block;
        }
    }

    .footer {
        margin-top: -1px;
        width: 100%;
        // height: 200px;
        // padding-bottom: 37px;
        border-radius: 0 0 8px 8px;
        margin-bottom: 15px;

        .text-wrap {
            .text1 {
                margin-bottom: 7px;
            }

            .text2 {
            }

            margin-bottom: 20px;
        }

        .payMethods {
            margin-top: 5px;
            margin-bottom: 15px;

            .img-wrap-sj {
                margin-left: 5px;
                width: 7px;
                height: 7px;
                line-height: 7px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .code-line {
            padding-bottom: 15px;
        }
    }

    .model-div {
        background: #f7f7fb;
        border-radius: 10px 10px 0px 0px;

        .marlr16 {
            margin: 0 16px;
        }

        .marb16 {
            margin-bottom: 16px;
        }

        .payment-mode {
            margin-top: 15px;
            // height: 130.5px;
            overflow-y: scroll;

            .payment-item {
                height: 32px;
                margin-top: 12px;

                .payment-item-left {
                    .payment-title {
                        margin-left: 10px;
                    }

                    .payment-img {
                        width: 20px;
                        height: 20px;

                        img {
                            width: 20px;
                            height: 20px;
                        }
                    }
                }

                .select-img {
                    img {
                        width: 17.5px;
                        height: 17.5px;
                    }
                }
            }
        }

        .confirmPayment {
            height: 44px;
            margin-bottom: 20px;
            margin-top: 16px;
        }

        .marb12 {
            margin-bottom: 12px;
        }

        .padtb16 {
            padding-top: 16px;
            padding-bottom: 16px;
        }

        .close {
            img {
                width: 13px;
                height: 13px;
            }
        }
    }
}
</style>
