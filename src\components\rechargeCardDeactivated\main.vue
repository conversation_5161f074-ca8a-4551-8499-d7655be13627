<template>
    <view class="uni-popupWrap bg-fff fl-column border-rad-8">
        <div class="title te-center font-18 color-333 weight-bold">提示</div>
        <div class="explain font-12 color-333 weight-400">
            <!--            1.充值卡充值功能即日起关闭充值e享卡和加油卡功能。-->
            <!--            <br />-->
            <!--            2.充值卡可兑换为等同金额的礼品卡，使用礼品卡可进行线上加油消费或购买非油商品。-->
	        3月20日起能源e站不再支持充值卡充值，您手中持有的充值卡可兑换为等额礼品卡。加油或购买商品时可使用礼品卡进行支付结算，如有疑问可咨询加油站工作人员。
        </div>
    </view>
</template>
<script>
export default {
    name: 'fund-transfer-out',
    components: {},
    data() {
        return {};
    },
    computed: {},
    onLoad() {},
    mounted() {},
    methods: {
        // TODO html测试数据
    },
    filter: {},
    watch: {},
};
</script>
<style lang="scss" scoped>
.uni-popupWrap {
    background-color: #fff;
    // padding: 15px;
    // width: 75%;
    // height: ;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    border-radius: 8px;
    .title {
        margin-bottom: 14px;
        text-align: center;
        height: 23px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }
    .explain {
        margin: 0 auto;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 20px;
    }
    .tips {
        background: #f5f5f5;
        margin: 23rpx auto;
        .tips-content {
            padding: 9px 10px;
            .reminder {
                color: #333333;
                font-weight: bold;
                font-size: 12px;
            }
            .explain2 {
                color: #333333;
                font-size: 12px;
                .span1 {
                    color: #e65023;
                    font-size: 12px;
                }
                .span2 {
                    color: #e64f22;
                    font-size: 12px;
                }
            }
        }
    }
    .btn {
        width: 100%;
        border-top: 1px solid #eee;
        height: 44px;
        border: 0 0 8px 8px;
        display: flex;
        margin-top: 10px;
        flex: 1;
        .allow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #333333;
        }
        .notAllow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            border-right: 1px solid #eee;
        }
    }
}
</style>
