// #ifdef MP-WEIXIN
const Plugin = requirePlugin('pay-plugin');
import config from '../../../../../../utils/config';
import { accountRiskEngineSupport } from '@/s-kit/js/v3-http/https3/wallet';
// #endif
import { mapState, mapGetters } from 'vuex';
export default {
    // #ifdef MP-WEIXIN
    data() {
        return {
            clientName: '微信小程序',
        };
    },
    created() {},
    methods: {
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        enablePositioning() {
            this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                confirmText: '确认',
                confirmColor: '#000',
                cancelText: '取消',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        uni.openSetting({
                            success: opRes => {
                                // 是否同意获取地理位置信息权限
                                if (opRes.authSetting['scope.userLocation']) {
                                    this.init(true);
                                }
                            },
                        });
                    }
                },
            });
        },
        // 小程序端判断是否开启定位权限
        checkIfTheVehicleHasEnabledPositioning(callback) {
            uni.getSetting({
                success: res => {
                    if (res.authSetting['scope.userLocation'] && this.locationState) {
                        callback();
                    } else {
                        this.locationPermission = false;
                    }
                },
            });
        },
        /**
         * @description  : 获取会员码code值，生成会员码
         * @param         {Function} getPayCode -获取会员码方法
         * @param         {Function} codeChange -将获取的生成的会员码的code值分发给父组件
         * @param         {Boolean} isOrder -是否获取订单
         * @return        {*}
         */
        async pollGetQrCode(isOrder) {
            let res = await this.accountDataPlugin.genQrCode();
            if (res.code == 0) {
                this.memberCode = res.data.qrcode;
                this.$emit('codeChange', this.memberCode);
                setTimeout(() => {
                    console.log(res.data.qrcode, 'res.data.qrcode====1111');
                    this.generateQRCode(res.data.qrcode);
                    // let _this = this;
                    // console.log(_this, this.$sKit, '_this存在吗');
                    // // return;
                    // this.$sKit.layer.renderingQrcode('qrcode', 'qrcode', res.data.qrcode, 555, 555, _this);
                }, 0);
                // 会员码风控
                this.riskControl();
                // this.clearTimerMember()
                // this.clearTimerQuqeyOrder()
                // 查询订单
                // 查询订单
                if (isOrder) {
                    console.log('测试会员码=====');
                    this.getRposUnPayList();
                }
            } else {
                this.dispatch('zjShowModal', {
                    title: res.msg,
                    content: `${res.code}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        /**
         * @description  : 轮询订单
         * @param         {Function} getRposUnPayList -查询pos待支付订单
         * @param         {Function} resStatus -查询订单成功或失败
         * @return        {*}
         */
        async getRposUnPayList() {
            // 清除查询订单的定时器
            this.clearTimerQuqeyOrder();
            // 防止查询订单成功后继续执行递归
            if (this.turnOffRecursionMember) {
                let params = {
                    token: this.gsmsToken,
                    openId: this.openId,
                    appId: config.appId,
                    data: {},
                    baseType: config.baseType,
                };
                const res = await Plugin.GetRposUnPayList(params);
                console.log(res, 'res=====微信小程序会员码');
                if (this.turnOffRecursionMember) {
                    if (res.code == 'PAY_SUCCESS') {
                        if (!res.data) {
                            this.memberCodeTimer = setTimeout(() => {
                                this.getRposUnPayList();
                            }, this.pollTime);
                            return;
                        }
                        if (this.isJump) {
                            this.isJump = false;
                            this.clearTimerQuqeyOrder();
                            let url = '/packages/third-scan-code-payment/pages/member-order-price/main';
                            let params = { ...res.data, refer: this.refer };
                            let type = 'navigateTo'; // 默认  uni.navigateTo({})
                            this.$sKit.layer.useRouter(url, params, type);
                            this.clearTimerQuqeyOrder();
                            this.clearTimerMember();
                            this.isJump = false;
                            this.turnOffRecursionMember = false;
                        }
                    } else {
                        // 未查询到订单继续查询
                        this.memberCodeTimer = setTimeout(() => {
                            this.getRposUnPayList();
                        }, this.pollTime);
                    }
                }
            }
        },
    },
    computed: {
        ...mapGetters(['openId']),
        ...mapState({
            gsmsToken: state => state.gsmsToken,
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
            locationState: state => state.locationV3_app.locationState,
        }),
    },
    // #endif
};
