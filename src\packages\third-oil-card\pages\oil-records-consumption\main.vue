<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view p-bf bg-F7F7FB fl-column">
            <zj-navbar :height="44" title="消费记录"></zj-navbar>
            <div class="cardNo p-LR-16 bg-FFF7DC">
                <div class="p-LR-16 font-16 weight-600 color-333">卡号：9000 000 00413 7076</div>
            </div>
            <div class="f-1 p-LR-16 fl-column mh-0 border-rad-8">
                <zj-data-list background="#F7F7FB" ref="oilRecordsConsumption" emptyText="暂无消费记录" :showEmpty="showEmpty"
                    @refreshPullDown="refreshPullDown" @scrolltolower="scrolltolower">
                    <div class="content bg-fff fl-row fl-jus-bet padding-16" v-for="(item, index) in recordsConsumptionList"
                        :key="index" @click="jumpDetails">
                        <div class="fl-column">
                            <div class="station-left-top font-14 weight-500 color-333">{{ item.stationName }}</div>
                            <div class="station-left-bottom fl-column font-12 weight-400 color-999">
                                <div class="station-left-text1">商品内容：{{ item.productContent }}</div>
                                <div class="station-left-text2">支付时间：{{ item.paymentTime }}</div>
                            </div>
                        </div>
                        <div class="fl-column fl-jus-bet">
                            <div class="price-wrap fl-row fl-al-cen fl-sp-end">
                                <div class="positiveNegative font-20 weight-500 color-E64F22">-</div>
                                <div class="price font-16 color-E64F22 font-style">&yen;{{ item.amount }}</div>
                            </div>
                            <div @click.stop="invoic" class="btn-plain font-13 weight-400 color-E64F22 border-rad-4">开具发票
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'oil-records-consumption',
    components: {},
    props: {
        // success: Boolean
    },
    data() {
        return {
            recordsConsumptionList: [
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
                {
                    stationName: '新昌',
                    productContent: '95号',
                    paymentTime: '2022-03-17 15:07:37',
                    amount: '83.99',
                },
            ],
        };
    },
    computed: {},
    created() { },
    mounted() { },
    methods: {
        refreshPullDown() { },
        scrolltolower() { },
        invoic() {
            let url = '/packages/third-invoice/pages/invoice-form/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        jumpDetails(item) {
            let url = '/packages/third-oil-card/pages/oil-card-recharge-detail/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.view {
    .cardNo {
        width: 100%;
        height: 44px;

        div {
            line-height: 44px;
        }
    }

    .content {
        width: 100%;
        height: 105px;
        margin-top: 12px;

        .station-left-top {
            line-height: 20px;
        }

        .station-left-bottom {
            margin-top: 12px;

            .station-left-text1 {
                margin-top: 8px;
                line-height: 16px;
            }

            .station-left-text2 {
                margin-top: 8px;
                line-height: 16px;
            }
        }

        .positiveNegative {
            height: 28px;
            // line-height: 28px;
        }

        .price-wrap {
            margin-right: 5px;

            .price {
                line-height: 22px;
            }
        }

        .btn-plain {
            width: 84px;
            height: 24px;
            line-height: 24px;
        }
    }
}
</style>
