const path = require('path')
const webpack = require('webpack')
const plugins = [
  require('postcss-import')({
    resolve(id, basedir, importOptions) {
      if (id.startsWith('~@/')) {
        return path.resolve(process.env.UNI_INPUT_DIR, id.substr(3));
      } else if (id.startsWith('@/')) {
        return path.resolve(process.env.UNI_INPUT_DIR, id.substr(2));
      } else if (id.startsWith('/') && !id.startsWith('//')) {
        return path.resolve(process.env.UNI_INPUT_DIR, id.substr(1));
      }
      return id;
    }
  }),

  require('autoprefixer')({
    remove: process.env.UNI_PLATFORM !== 'h5'
  }),
  require('@dcloudio/vue-cli-plugin-uni/packages/postcss')
];
if (process.env.UNI_PLATFORM === 'h5') {
  plugins.push(
    require('postcss-px-to-viewport')({
      unitToConvert: "px", // 要转化的单位
      viewportWidth: 375, // UI设计稿的宽度
      unitPrecision: 6, // 转换后的精度，即小数点位数
      propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
      viewportUnit: "vw", // 指定需要转换成的视窗单位，默认vw
      fontViewportUnit: "vw", // 指定字体需要转换成的视窗单位，默认vw
      selectorBlackList: [], // 指定不转换为视窗单位的类名，
      minPixelValue: 0, // 默认值1，小于或等于1px则不进行转换
      mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
      // replace: true, // 是否转换后直接更换属性值
      exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
      landscape: false // 是否处理横屏情况
    })
  );
}

const config = {
  parser: require('postcss-comment'),
  plugins
};
if (webpack.version[0] > 4) {
  delete config.parser
}
module.exports = config
