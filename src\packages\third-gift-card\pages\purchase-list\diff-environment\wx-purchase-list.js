// #ifdef MP-WEIXIN
import { mapState, mapGetters } from 'vuex';
export default {
    computed: {
        ...mapState({
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    onLoad(options) {
        let result = this.selectComponent('#passwordKeyboardId');
        this.$sKit.keyBordPlugin.initRef(result);
        this.$store.commit('setAccountDataPlugin', result);
        this.passwordKeyboardRef = result;
    },
    methods: {
        giftCardQueryCodeFun(giftCardNo) {
            return new Promise((resolve, reject) => {
                this.passwordKeyboardRef.openKeyboard(
                    'password_unique1',
                    6,
                    async pwd => {},
                    () => {
                        uni.showLoading();
                        this.accountDataPlugin
                            .getGiftCardCipher('password_unique1', giftCardNo)
                            .then(res => {
                                uni.hideLoading();
                                if (res.code == 0) {
                                    resolve(res.msg);
                                } else {
                                    reject(res.msg);
                                }
                            })
                            .catch(res => {
                                uni.hideLoading();
                                reject(res.msg);
                            });
                    },
                );
            });
        },
    },
};
// #endif
