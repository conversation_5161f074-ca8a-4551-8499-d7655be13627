<template>
    <div class="main-tabs-wrap">
        <div class="main-tabs">
            <!-- #ifdef MP-TOUTIAO -->
            <div class="tab tab-active"> 加油 </div>
            <!-- #endif -->
            <!-- #ifndef MP-MPAAS -->
            <div
                v-for="(tab, index) in tabs"
                :key="index"
                @click="changeTab(tab.value, index)"
                class="tab"
                :class="{
                    'tab-active': seltab === tab.value,
                    'tab-code': seltab === 'code' && tab.value !== 'code' && tokenInfo.accessToken,
                    'tab-active-code': seltab == 'code' && tab.value == 'code' && tokenInfo.accessToken,
                }"
            >
                <div>{{ tab.label }}</div>
            </div>
            <!-- #endif -->
            <!-- #ifdef MP-MPAAS -->
            <div
                v-for="(tab, index) in tabs"
                :key="index"
                @click="changeTab(tab.value, index)"
                class="tab"
                :class="{
                    'tab-active': seltab === tab.value,
                    'tab-code': seltab === 'code' && tab.value !== 'code',
                    'tab-active-code': seltab == 'code' && tab.value == 'code',
                }"
            >
                {{ tab.label }}
            </div>
            <!-- #endif -->
        </div>

        <div class="guideBox" v-if="guideStep == 1">
            <img class="guideImg" mode="widthFix" src="../../../../image/homeImg/guidePage1.png" alt="" />
            <div class="guideBtton">
                <div class="skip" @click.stop="setGuideStep(0)">跳过</div>
                <div class="next" @click.stop="setGuideStep(2)">下一步</div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
// #ifdef MP-ALIPAY
// import zfbLoginV3 from '@/components/zfb-login-v3/zfb-login-v3.vue';
// #endif
// #ifdef MP-WEIXIN
import getPhoneNumber from '../../../../../components/loginV3/getPhoneNumber.vue';
// #endif
export default {
    name: 'main-tabs',
    components: {
        // #ifdef MP-ALIPAY
        // zfbLoginV3,
        // #endif
        // #ifdef MP-WEIXIN
        getPhoneNumber,
        // #endif
    },
    props: {
        tabType: [String],
        seltab: [String],
    },
    data() {
        return {
            tabs: [
                // TODO @version?
                // { label: 'e享加油', value: 'reserve' },
                { label: '加油', value: 'charge' },
                { label: '会员码', value: 'code' },
            ],
            tokenInfo: {},
            tabsIndex: -1,
        };
    },
    created() {},
    mounted() {
        // #ifndef MP-MPAAS
        this.tokenInfo = uni.getStorageSync('tokenInfo');
        console.log(this.tokenInfo, 'tokenInfo====测试tab');
        // #endif
        // #ifdef H5-CLOUD
        this.tabsList();
        // #endif
    },
    methods: {
        async tabsList() {
            this.tabs = [
                { label: 'e享加油', value: 'reserve' },
                { label: '加油', value: 'charge' },
            ];
        },
        //切换加油（charge）、e享加油（reserve）
        changeTab(tab, index) {
            console.log(tab, '切换tab触发');
            if (tab == 'code') {
                this.tabsIndex = 2;
            } else {
                this.tabsIndex = index;
            }

            // this.$console(tab, '切换tab触发')
            if (tab == this.seltab) return;
            // this.seltab = tab;
            this.$emit('changeTab', tab);
        },
        setGuideStep(step) {
            this.changeTab('reserve');
            this.$store.dispatch('changeGuideStep', step);
        },
        // 支付宝登录成功后回调
        onLoginAfterEvent(type) {
            console.log('登录成功', type);
            this.$sKit.layer.backHomeFun();
        },
    },
    computed: {
        ...mapState({
            guideStep: state => state.thirdIndex.guideStep, // 是否展示首次指引  true-是 false-否
            token: state => state.token,
            token3: state => state.token3,
            homepageTags: state => state.thirdLogin.homepageTags,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    watch: {},
};
</script>

<style lang="scss" scoped>
.main-tabs-wrap {
    position: relative;
    .main-tabs {
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        overflow: hidden;

        .tab {
            color: #666666;
            font-size: 16px;
            line-height: 44px;
            transition: all 0.3s ease;
        }
        .tab-code {
            color: #ffff;
            font-size: 16px;
            line-height: 44px;
        }

        .tab-active {
            color: #e64f22;
            font-weight: bold;
            font-size: 16px;
            position: relative;
            // background-color: #fff;
            &.tab-active::before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 25px;
                height: 2px;
                background: #e64f22;
            }
        }
        .tab-active-code {
            color: #fff;
            font-weight: 500;
            font-size: 16px;
            position: relative;
            font-weight: bold;
            &.tab-active-code::before {
                content: '';
                position: absolute;
                bottom: 2px;
                left: 50%;
                transform: translateX(-50%);
                width: 25px;
                height: 2px;
                background: #fff;
            }
        }
    }

    .guideBox {
        .guideImg {
            width: 100%;
            position: absolute;
            top: 11px;
            right: 6.5px;
            z-index: 999999;
        }
        .guideBtton {
            display: flex;
            position: absolute;
            top: 170px;
            z-index: 9999999;
            right: 50px;
            div {
                height: 51rpx;
                border-radius: 34rpx;
                border: 2rpx solid #ffffff;
                font-size: 24rpx;
                line-height: 51rpx;
                text-align: center;
                width: 69px;
                margin-right: 10px;
                &:nth-of-type(1) {
                    color: #fff;
                }
                &:nth-of-type(2) {
                    color: #333;
                    background-color: #fff;
                }
            }
        }
    }
    .active {
        color: #e64f22;
        font-weight: bold;
        font-size: 16px;
        line-height: 44px;
    }
    .unActive {
        color: #666666;
        font-size: 16px;
        line-height: 44px;
    }
}
</style>
