import { wxPost,mpaasPost,alPost,ttPost,cloudPost} from './post.js';
import flyPost from './flyPost.js';
// import cnpcBridge from '../v3-native-jsapi/cnpcBridge';

import Vue from 'vue';

export const POST = async (url, params = {}, config = {}) => {
    let httpConfig = {
        isCustomErr: false,
        isload: true,
        isEncode: false,
        apiVersion: 'v1',
        isAuth: false,
        handleErrorFn: () => {},
    };
    httpConfig = Object.assign(httpConfig, config);
    if (typeof httpConfig.handleErrorFn !== 'function') {
        throw new Error('handleErrorFn需传入一个方法');
    }
    
    // #ifdef MP-MPAAS
    let mpaasRes = await mpaasPost(url, params, httpConfig);
    return mpaasRes;
    // #endif

    // #ifdef MP-ALIPAY
    let aliRes = await alPost(url, params, httpConfig);
    return aliRes;
    // #endif
    // #ifdef MP-TOUTIAO
    let ttRes = await ttPost(url, params, httpConfig);
    return ttRes;
    // #endif

    // #ifdef MP-WEIXIN
    let wxRes = await wxPost(url, params, httpConfig);
    return wxRes;
    // #endif
    // #ifdef H5-CLOUD
    let cloudRes = await cloudPost(url, params, httpConfig);
    return cloudRes;
    // #endif
};

export const POST2 = async (url, params = {}, config = {}) => {
    let httpConfig = {
        isCustomErr: false,
        isload: true,
        isEncode: false,
        handleErrorFn: () => {},
    };
    httpConfig = Object.assign(httpConfig, config);
    if (typeof httpConfig.handleErrorFn !== 'function') {
        throw new Error('handleErrorFn需传入一个方法');
    }
    return flyPost.post(url, params, httpConfig);
};
export const PUT = async (url, params = {}, config = {}) => {
    let httpConfig = {
        isCustomErr: false,
        isload: true,
        isEncode: false,
        isPut: true,
        handleErrorFn: () => {},
    };
    httpConfig = Object.assign(httpConfig, config);
    if (typeof httpConfig.handleErrorFn !== 'function') {
        throw new Error('handleErrorFn需传入一个方法');
    }
    return flyPost.put(url, params, httpConfig);
};
export const GET2 = async (url, params, config = {}) => {
    let httpConfig = {
        isCustomErr: false,
        isload: true,
        isEncode: false,
        handleErrorFn: () => {},
    };
    httpConfig = Object.assign(httpConfig, config);
    if (typeof httpConfig.handleErrorFn !== 'function') {
        throw new Error('handleErrorFn需传入一个方法');
    }
    return flyPost.get(url);
};
