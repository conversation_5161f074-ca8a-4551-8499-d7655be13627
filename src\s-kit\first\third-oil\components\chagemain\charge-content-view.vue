<template>
    <div class="width100 fl-row">
        <scroll-view
            ref="scrollView"
            :scroll-into-view="scrollTopViewData"
            :scroll-top="scrollTopDistance"
            :scroll-y="true"
            class="page-view"
            @touchend="popupMoving(false)"
            @touchstart="popupMoving(true)"
        >
            <!-- 营销位 -->

            <div v-if="selectMarkerV3 && selectMarkerV3.orgCode" class="zjMarketBox">
                <zjMarket
                    ref="topChargeBanner"
                    :orgCode="selectMarkerV3.orgCode"
                    @isScrollTop="isScrollTop"
                    marketType="lbt"
                    spaceType="oilcharge_top"
                >
                </zjMarket>
            </div>
            <div class="charge-content-view bg-linear fl-column">
                <div ref="toparea" class="top-area">
                    <div v-if="selectMarkerV3 && selectMarkerV3.orgCode" class="stationinfo bg-fff padding12 marlr16">
                        <div class="fl-row fl-jus-bet">
                            <div class="detail f-1">
                                <div class="name-area fl-row fl-al-cen">
                                    <!-- #ifndef MP-ALIPAY -->
                                    <div
                                        :class="{ 'scrolling-text': selectMarkerV3.orgName.length > 11 }"
                                        class="name font-16 weight-bold color-000 f-1"
                                    >
                                        {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                    <!-- #ifdef MP-ALIPAY -->
                                    <div class="name font-16 weight-bold color-000 f-1">
                                        {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                </div>
                                <div class="fl-row fl-al-cen mart5">
                                    <div
                                        v-if="selectMarkerV3.stationStatus == 30 || selectMarkerV3.stationStatus == 50"
                                        class="weight-400 font-12 color-6A6A6A time-icon"
                                        >暂停营业
                                    </div>
                                    <div
                                        v-if="selectMarkerV3.stationStatus == 20 || selectMarkerV3.stationStatus == 10"
                                        class="weight-400 font-12 color-118920 marker-118920"
                                        >正常营业
                                    </div>
                                    <!-- #ifdef MP-MPAAS -->
                                    <div
                                        v-if="isO2O == 1 && !isHarmony"
                                        class="font-10 color-FA6400 e-go fl-row fl-al-jus-cen te-center mar-6-l"
                                        @click="shoppingHandle"
                                    >
                                        <div>进入e享购商城</div>
                                        <div class="arr-right-FA6400 marl5"></div>
                                    </div>
                                    <!-- #endif -->
                                </div>
                            </div>
                            <div class="fl-row">
                                <div class="changestation font-10 color-E64F22" @click.stop="changeStationAction">更多网点</div>
                                <div class="margin-box"></div>
                                <!-- #ifndef H5-CLOUD -->
                                <img alt class="navt-to" src="../../image/stationinfo-icon-location.png" @click.stop="clickNaviStateion" />
                                <!-- #endif -->
                            </div>
                        </div>
                        <div v-if="selectMarkerV3.address != null" class="oil-address font-12 weight-400 color-333 mart5">
                            {{ selectMarkerV3.address || '' }}
                        </div>
                        <div v-if="selectMarkerV3.tagList.length > 0" class="center-area mart5">
                            <div class="fl-row fl-al-cen con-list">
                                <div v-for="(tag, index) in selectMarkerV3.tagList" :key="index" class="item bg-F3F3F6 te-center">
                                    <div class="item-cell">{{ strReplace(tag) }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="marketing-assistant" v-if="marketingAssistant">
                            <div class="marketing-text">{{ marketingAssistant }}</div>
                        </div>
                    </div>
                    <div v-else class="stationinfo bg-fff padding12 marlr16">
                        <div class="fl-row fl-jus-bet">
                            <div class="detail f-1">
                                <div class="name-area fl-row fl-al-cen">
                                    <div class="name font-16 weight-bold color-999">暂未查询到网点</div>
                                    <!-- <div class="changestation font-10 color-E64F22" @click.stop="changeStationAction">更多网点</div> -->
                                </div>
                                <div class="font-12 weight-bold color-999 oil-address">暂无地址</div>
                            </div>
                            <div class="fl-row">
                                <div class="changestation font-10 color-E64F22" @click.stop="changeStationAction">更多网点</div>
                                <div class="margin-box"></div>
                                <!-- #ifndef H5-CLOUD -->
                                <img alt class="navt-to" src="../../image/stationinfo-icon-location.png" @click.stop="clickNaviStateion" />
                                <!-- #endif -->
                            </div>
                        </div>
                    </div>
                    <div class="height12"></div>
                    <div class="card-default marlr16 card-linear">
                        <div class="box-div">
                            <!-- #ifdef MP-TOUTIAO -->
                            <div class="fl-row fl-jus-cen fl-al-cen">
                                <div class="fl-row fl-al-cen">
                                    <img alt="" class="no-reserve" src="../../image/no-reserve.png" />
                                    <div class="marl12">
                                        <div class="font-16 color-333">敬请期待</div>
                                    </div>
                                </div>
                            </div>
                            <!-- #endif -->
                            <!-- #ifndef MP-TOUTIAO -->
                            <div v-if="selectMarkerV3.storeOnlyFlag == 1" class="fl-row fl-jus-cen fl-al-cen">
                                <div class="fl-row fl-al-cen">
                                    <img alt="" class="no-reserve" src="../../image/no-reserve.png" />
                                    <div class="marl12">
                                        <div class="font-16 color-333">此站为纯便利店</div>
                                        <div class="font-12 color-666 mat5">暂不支持加油服务</div>
                                        <div class="font-12 color-666 mat5">您可通过【更多网点】去筛选附近加油站</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="fuelData.length === 0" class="fl-row fl-jus-cen fl-al-cen">
                                <div class="fl-row fl-al-cen">
                                    <img alt="" class="no-reserve" src="../../image/no-reserve.png" />
                                    <div class="marl12">
                                        <div class="font-16 color-333">当前网点维护中</div>
                                        <div class="font-12 color-666 mat5">暂不支持加油服务</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div class="card-default card-linear oil-box">
                                    <div class="">
                                        <div class="padlt127 marb12">
                                            <div class="font-14 weight-bold color-333">选择油号</div>
                                        </div>
                                        <div class="fl-row fl-al-cen con-list">
                                            <div
                                                v-for="(item, index) in fuelData"
                                                :key="index"
                                                :class="selOilType.fuelNo == item.fuelNo ? 'oil-type-sel' : ''"
                                                class="item bg-F3F3F6 te-center"
                                                @click.stop="selOilTypeClick(item, 'click')"
                                            >
                                                <div class="item-cell">{{ $sKit.layer.getOilNum(item.fuelName) }}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="">
                                        <div class="font-14 weight-bold color-333 marb12 fl-row fl-al-base pad12-7">
                                            选择油枪
                                            <div class="font-10 color-999 mar-6-l">请向加油员确认油枪号</div>
                                        </div>
                                        <div class="fl-row fl-al-cen fl-wrap con-list ov-hid">
                                            <div
                                                v-for="(item, index) in selOilType.fuelGunNo"
                                                :key="index"
                                                :class="item == selectCurrent ? 'oil-type-sel' : ''"
                                                class="item bg-F3F3F6 te-center"
                                                @click.stop="selectCurrentGunClick(item, 'click')"
                                            >
                                                <div class="item-cell">{{ item }}号</div>
                                            </div>
                                            <div class="item bg-F3F3F6 te-center" @click="selectInput">
                                                <div class="item-cell">其它</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="width100 fl-column order-box">
                                    <div class="fl-row fl-al-base padb12">
                                        <div class="font-16 weight-bold color-000">选择订单</div>
                                        <div class="font-12 color-999 marl13">请向加油员确认您的订单金额</div>
                                    </div>
                                    <!-- <scroll-view :scroll-y="true" class="ov-y-s" :class="{ hieght100: notPaidList.length > 3 }"> -->
                                    <div class="fl-row fl-al-cen fl-wrap fl-jus-bet con-list ov-hid">
                                        <div
                                            v-for="(item, index) in notPaidList"
                                            :key="index"
                                            class="item"
                                            @click.stop="selOilOrderClick(item, index)"
                                        >
                                            <div v-if="item.createTime" class="font-10 weight-bold color-999 marl12 marb6"
                                                >{{ timeSplit(item.createTime) }}
                                            </div>
                                            <div :class="index == selOilOrder ? 'item-sel' : ''" class="item-borer marb12">
                                                <div class="font-13 weight-bold color-000 line14"
                                                    >{{ item.weight }}{{ item.productUnit }}
                                                </div>
                                                <div class="font-15 weight-bold color-E64F22 line14">¥{{ item.amount }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </scroll-view> -->
                                </div>
                                <div id="payNav" class="pay-btn">
                                    <div v-if="notPaidList.length > 0" class="pay-submit">
                                        <div class="width100 primary-btn color-fff font-18 btn-44" @click.stop="submitOrder">去支付 </div>
                                    </div>
                                </div>
                            </div>
                            <!-- #endif -->
                        </div>
                    </div>

                    <div v-if="selectMarkerV3 && selectMarkerV3.orgCode" class="market-div fl-row fl-jus-bet">
                        <zjMarket :orgCode="selectMarkerV3.orgCode" marketType="rowTwo" spaceType="oilcharge_left"></zjMarket>
                        <zjMarket :orgCode="selectMarkerV3.orgCode" marketType="rowTwo" spaceType="oilcharge_right"></zjMarket>
                    </div>
                </div>
            </div>
        </scroll-view>
        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <!-- 超过距离 -->
                    <div class="fl-column fl-al-jus-cen font-14 color-333 weight-500 content">
                        <div>您当前距离</div>
                        <div>{{ selectMarkerV3.orgName }}</div>
                        <div
                            >超过<span class="color-E64F22"
                                >{{ Number(selectMarkerV3.payDistance) > 0 ? Number(selectMarkerV3.payDistance) : maxDistance }}km</span
                            >距离较远，请稍后使用</div
                        >
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view v-if="cancelText" :style="{ color: cancelColor }" class="btn cancel_btn" @click.stop="clickBtn('cancel')">
                        {{ cancelText }}
                    </view>
                    <view
                        v-if="confirmText"
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickBtn('confirm')"
                        >{{ confirmText }}
                    </view>
                </view>
            </div>
        </custom-popup>
        <custom-popup ref="inputGunDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <div class="font-16 weight-600 te-center color-333 content">请输入油枪号</div>
                    <div class="price-input-area content">
                        <input
                            v-model="selectCurrent"
                            class="price-input"
                            maxlength="2"
                            placeholder="请输入油枪号"
                            type="number"
                            @input="otherOilInput"
                        />
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view :style="{ color: cancelColor }" class="btn cancel_btn" @click.stop="clickInputCancleBtn"> 取消 </view>
                    <view
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickInputBtn"
                        >确定
                    </view>
                </view>
            </div>
        </custom-popup>
        <custom-popup ref="unPaidOrderDialogFlag" :maskClick="false" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <!-- 你存在一笔待支付订单 -->
                    <div class="fl-column fl-al-jus-cen font-14 color-333 weight-500 content">
                        <div>你存在一笔待支付订单</div>
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickUnPaidOrder"
                        >确定
                    </view>
                </view>
            </div>
        </custom-popup>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import {
    creatUnPaidOrderApi,
    preAuthOrderApi,
    unPaidOrdeQueryrApi,
    notPaidListApi,
    transNoList,
    getMarketingRec,
} from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import zjMarket from '../../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import { maxMinute, maxDistance } from '../../../../../../project.config';
import { clientCode } from '../../../../../../project.config';
export default {
    name: 'charge-content-view',
    props: {
        refer: {
            type: String,
            default: '',
        },
    },
    components: { zjMarket },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            cityName: state => state.locationV3_app.cityName,
            isO2O: state => state.locationV3_app.isO2O,
            isHarmony: state => state.thirdIndex.isHarmony,
            firstGetTransNoList: state => state.thirdIndex.firstGetTransNoList,
        }),
    },
    data() {
        return {
            timer: null,
            //油品订单选择
            selOilOrder: '0',
            // 是否有预授权订单
            isExitPreOrder: false,
            //是否有待支付订单
            isUnPaidOrder: false,
            unPaidInfo: '',
            confirmText: '导航到站',
            cancelText: '稍后使用',
            maxDistance: maxDistance || 0.5,
            animationTime: 0,
            //油品选择
            selOilType: {},
            //油枪输入
            selectCurrent: '',
            //油品集合
            notPaidList: [],
            seletctOilOrderData: {},
            scrollTopDistance: 0,
            scrollTopViewData: '',
            // 只展示一次小红点
            redDotFirst: true,
            marketingAssistant: '',
            isH5CLOUD: false,
            scrollTopData: false, //是否有banner
        };
    },
    async created() {
        // watch里监听油站值进行访问
        // if (this.selectMarkerV3 && this.selectMarkerV3.orgCode) {
        //     await this.$store.dispatch('getFuelGunByOrgCodePost');
        // }
    },
    mounted() {
        this.submitOrder = this.$sKit.commonUtil.throttleUtil(this.submitOrder);
        // 为付款逻辑添加节流防抖
        this.clickUnPaidOrder = this.$sKit.commonUtil.throttleUtil(this.clickUnPaidOrder);
        // #ifdef H5-CLOUD
        this.isH5CLOUD = true;
        // #endif
    },
    methods: {
        // test===Promise
        isScrollTop(data) {
            this.scrollTopData = data;
            console.log(data, 'scrollTopData');
        },
        // 拿到不下车加油油品信息
        getPreAuthFuel() {
            this.selOilType = {};
            // #ifdef MP-MPAAS
            this.$cnpcBridge.getValueToNative('Define_Charge_PreAuth_Fuel', value => {
                if (value) {
                    this.selOilType = JSON.parse(decodeURIComponent(value)) ? JSON.parse(decodeURIComponent(value)) : '';
                    this.fuelDataChange();
                } else {
                    this.fuelDataChange();
                }
            });
            // this.$store.commit('setLoadingStatus', false);
            // #endif
            // #ifndef MP-MPAAS
            let value = uni.getStorageSync('Define_Charge_PreAuth_Fuel');
            this.selOilType = value ? JSON.parse(decodeURIComponent(value)) : '';
            this.fuelDataChange();
            console.log('this.selOilType', this.selOilType);
            // #endif
        },
        // 请输入其他油枪号
        otherOilInput(e) {
            const regex = /^\d{0,2}$/;
            let value = e.detail.value;
            if (regex.test(value)) {
                this.selectCurrent = value;
            } else {
                this.$nextTick(() => {
                    this.selectCurrent = '';
                });
            }
        },
        clickInputBtn() {
            if (this.selectCurrent == '') {
                uni.showToast({
                    title: '请输入油枪号',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else {
                this.$refs.inputGunDialogFlag.close();
                const judgeMent =
                    Number(this.selectMarkerV3.payDistance) > 0
                        ? Number(this.selectMarkerV3.distance) > Number(this.selectMarkerV3.payDistance)
                        : Number(this.selectMarkerV3.distance) > (maxDistance || 0.5);
                if (judgeMent) {
                    this.$refs.popDialogFlag.open();
                    this.selectCurrent = '';
                } else {
                    this.notPaidListGet();
                }
            }
        },
        clickInputCancleBtn() {
            this.$refs.inputGunDialogFlag.close();
        },
        // 处理油品数据
        fuelDataChange() {
            // 有油品列表
            if (this.fuelData.length > 0) {
                // 当前没有选中油品
                if (!this.selOilType || !this.selOilType.fuelNo) {
                    this.selOilTypeClick(this.fuelData[0], 'auto');
                    // 有选中的油品进行回显
                } else {
                    let num = this.selOilType.fuelName;
                    let isEixst = false;
                    for (let i = 0; i < this.fuelData.length; i++) {
                        let fuelInfo = this.fuelData[i];
                        if (fuelInfo.fuelName.indexOf(num) != -1) {
                            isEixst = true;
                            this.selOilTypeClick(fuelInfo, 'auto');
                            break;
                        }
                    }
                    if (!isEixst) {
                        if (this.fuelData.length > 0) {
                            this.selOilTypeClick(this.fuelData[0], 'auto');
                        }
                    }
                }
            } else if (this.fuelData.length === 0) {
                // (redDot当前油品列表为空且需要查询带支付订单,但是不需要查询待结交易)
                this.unPaidOrderGet('redDot');
            }
        },
        /**
         * @description : 获取该油枪所有待结交易
         * @param         {string} oilstationCode - 油站编码
         * @param         {string} oilGunNo - 油枪号
         * @param         {string} lastMinute - 最近分钟数,用于查询最近的原始交易 eg: 5(最近五分钟交易)
         * @return        {*}
         */
        async notPaidListGet(type) {
            this.notPaidList = [];
            this.seletctOilOrderData = {};
            this.selOilOrder = '-1';
            console.log('selectCurrent', this.selectCurrent);
            if ((!this.selectCurrent || this.selectCurrent == '') && type != 'getTransNoList') {
                return false;
            }
            let postFun = () => {};
            let params = {};
            let config = {};
            if (type == 'getTransNoList' && this.firstGetTransNoList) {
                if (this.selectMarkerV3.stationType != 1) return;
                // 获取油号下所有订单
                params = {
                    oilstationCode: this.selectMarkerV3.orgCode,
                    oilGunNo: this.selOilType.fuelGunNo,
                    lastMinute: 10,
                };
                postFun = transNoList;
                config = { isload: false, isCustomErr: true };
                this.$store.commit('setFirstGetTransNoList', false);
            } else if (type == 'getTransNoList' && !this.firstGetTransNoList) {
                return;
            } else {
                params = {
                    oilstationCode: this.selectMarkerV3.orgCode,
                    oilGunNo: this.selectCurrent,
                    lastMinute: Number(maxMinute || 10),
                };
                postFun = notPaidListApi;
                config = { isload: false };
            }
            let res = await postFun(params, config);
            if (res.success) {
                this.notPaidList = res.data || [];
                if (this.notPaidList.length == 1) {
                    this.selOilOrderClick(res.data[0], 0);
                }
                if (this.notPaidList.length <= 0 && type != 'getTransNoList') {
                    this.$store.dispatch('zjShowModal', {
                        title: '当前油枪下暂无订单。',
                        confirmText: '确认',
                        cancelText: '',
                        confirmColor: '#333',
                        success: res => {
                            if (res.confirm) {
                                console.log(this, 'this---');
                                this.selectCurrent = '';
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                } else if (this.notPaidList.length > 0) {
                    this.$nextTick(() => {
                        this.queryOffset();
                    });
                }
            } else {
                this.selectCurrent = '';
            }
            setTimeout(() => {
                this.$emit('changeViewHeight');
            }, 30);
        },
        selectInput() {
            // #ifdef MP-ALIPAY
            // 示例：判断当前支付宝客户端版本是否高于 10.3.20，引导低版本客户端升级
            const clientVersion = my.env.clientVersion || my.getSystemInfoSync().clientVersion;
            console.log('支付宝当前版本', clientVersion);
            if (this.compareVersion(clientVersion, '10.2.38') < 0) {
                console.log('支付宝当前版本小于10.2.38', clientVersion);
                my.alert({
                    title: '提示',
                    content: '当前支付宝版本过低，无法使用当前小程序的重要功能，请升级到最新支付宝版本。',
                    success: () => {
                        my.ap.updateAlipayClient();
                    },
                });
            }
            // #endif
            this.$refs.inputGunDialogFlag.open();
        },
        // 支付宝版本号判断，若 v1 > v2 则返回值为 1，v1 = v2 则返回值为 0
        compareVersion(v1, v2) {
            var s1 = v1.split('.');
            var s2 = v2.split('.');
            var len = Math.max(s1.length, s2.length);

            for (let i = 0; i < len; i++) {
                var num1 = parseInt(s1[i] || '0');
                var num2 = parseInt(s2[i] || '0');

                if (num1 > num2) {
                    return 1;
                } else if (num1 < num2) {
                    return -1;
                }
            }
            return 0;
        },
        popupMoving(moveBoolean) {
            this.$emit('reMoveing', moveBoolean);
            console.log(moveBoolean, '子组件 --- popupMoving');
        },
        /**
         * @description : 不显示"站"字
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        /**
         * @description : 截取时间，获取时分秒
         * @return        {*}
         */
        timeSplit(data) {
            try {
                return data.split(' ')[1];
            } catch (error) {}
        },
        /**
         * @description : 打开导航
         * @return        {*}
         */
        clickBtn(data) {
            this.$refs.popDialogFlag.close();
            if (data == 'confirm') {
                this.clickNaviStateion();
            }
        },
        /**
         * @description : 打开选择油号/油枪
         * @return        {*}
         */
        async selectCurrentGunPop(type) {
            // #ifdef MP-MPAAS
            let state = await this.$sKit.commonUtil.judgeLocationAuth();
            if (!state) return;
            // #endif
            if (!this.selectMarkerV3.orgCode) {
                uni.showToast({
                    title: '暂未查询到网点信息',
                    icon: 'none',
                });
                return;
            }
            if (this.selectMarkerV3.stationStatus == 30 || this.selectMarkerV3.stationStatus == 50) {
                uni.showToast({
                    title: '当前网点暂停营业',
                    icon: 'none',
                });
                return;
            }
            // #ifdef MP-MPAAS
            let info = await this.$cnpcBridge.checkPermission();
            if (info && info.appStatus) {
                this.searchOrderBefore(type);
            } else {
                this.selectCurrent = ''; //油枪号取消下选中
                this.$cnpcBridge
                    .openPermissions({
                        code: 'location',
                        explain: '位置权限使用说明',
                        detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                    })
                    .then(value => {
                        if (value) {
                            this.$store.dispatch('initLocationV3_app', () => {});
                        }
                    });
            }
            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD
            uni.getSetting({
                success: res => {
                    // #ifdef MP-WEIXIN
                    const status = res.authSetting['scope.userLocation'];
                    // #endif
                    // #ifdef MP-ALIPAY
                    const status = res.authSetting.location;
                    // #endif
                    console.log('location', status);
                    if (!status) {
                        //没有授权
                        this.selectCurrent = ''; //油枪号取消下选中
                        this.$store.dispatch('zjShowModal', {
                            title: '提示',
                            content: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                            confirmText: '确认',
                            confirmColor: '#000',
                            cancelText: '取消',
                            cancelColor: '#666',
                            success: res => {
                                if (res.confirm) {
                                    uni.openSetting({
                                        success: opRes => {
                                            // 是否同意获取地理位置信息权限
                                            // #ifdef MP-ALIPAY
                                            const openStatus = opRes.authSetting['location'];
                                            // #endif
                                            // #ifdef MP-WEIXIN
                                            const openStatus = opRes.authSetting['scope.userLocation'];
                                            // #endif
                                            if (openStatus) {
                                                //开通了权限
                                                this.$store.dispatch('initLocationV3_app', () => {});
                                            }
                                        },
                                    });
                                }
                            },
                        });
                    } else {
                        this.searchOrderBefore(type);
                    }
                },
            });
            // #endif
            // #ifdef H5-CLOUD
            this.searchOrderBefore(type);
            // #endif
        },
        /**
         * @description : 进入e享购页面
         * @return        {*}
         */
        shoppingHandle() {
            let routerParams = {
                shopId: this.selectMarkerV3.orgCode, // 店铺id
                showCart: false, // 是否弹出购物车, 默认false
            };
            console.log(routerParams, '跳转o2o参数');

            let url = `/packages/o2o-shop/pages/shop-home/main?data=${encodeURIComponent(
                encodeURIComponent(JSON.stringify(routerParams)),
            )}`;
            this.$sKit.layer.cubeMini(url, '4908542685197380');
        },
        selectOilGunBut() {
            this.$sKit.mpBP.tracker('后支付加油', {
                seed: 'hpayoilBiz',
                pageID: 'selectOilGunBut', // 返回sdk标识
                refer: this.refer,
                channelID: clientCode,
                address: this.cityName,
            });
        },
        selectOrderBut() {
            this.$sKit.mpBP.tracker('后支付加油', {
                seed: 'hpayoilBiz',
                pageID: 'selectOrderBut', // 返回sdk标识
                refer: this.refer,
                channelID: clientCode,
                address: this.cityName,
            });
        },
        // 选中油号
        selOilTypeClick(item, triggerType = 'click') {
            this.selectOilGunBut();
            this.selOilType = item;
            console.log('selectOilData-----', this.selOilType);
            this.selectCurrentGunClick('', 'getTransNoList', triggerType);
        },
        //获取待支付订单列表dom
        queryOffset() {
            let query = uni.createSelectorQuery().in(this);
            query.select('#payNav').boundingClientRect();
            query.exec(data => {
                console.log(data[0], 'scrollTop----res');
                this.$nextTick(() => {
                    // setTimeout(() => {
                    this.scrollTopDistance = data[0].top ? Number(data[0].top) : 0;
                    this.scrollTopViewData = data[0].id;
                    console.log('scrollTop', this.scrollTopDistance);
                    this.$emit('chageScrollTop', this.scrollTopDistance);
                    // }, 100);
                });
            });
        },
        /**
         * @description : 选择油枪
         * @return        {*}
         */
        selectCurrentGunClick(item, type, triggerType = 'click') {
            this.selectOilGunBut();
            this.selectCurrent = item;
            const judgeMent =
                Number(this.selectMarkerV3.payDistance) > 0
                    ? Number(this.selectMarkerV3.distance) > Number(this.selectMarkerV3.payDistance)
                    : Number(this.selectMarkerV3.distance) > (maxDistance || 0.5);

            if (judgeMent) {
                if (triggerType === 'click') {
                    this.$refs.popDialogFlag.open();
                    // #ifdef H5-CLOUD
                    this.confirmText = '';
                    // #endif
                }
                this.selectCurrent = '';
                this.notPaidList = [];
                // #ifdef MP-ALIPAY
                // 支付宝小程序开启精准定位
                this.precisePositioning();
                // #endif
            } else {
                this.selectCurrentGunPop(type);
            }
        },
        precisePositioning() {
            my.getLocation({
                type: 1,
                success: async res => {
                    if (res.accuracy > 100) {
                        await this.$store.dispatch('zjShowModal', {
                            title: '提示',
                            content: '无法确认您的精确位置,请授权支付宝获取您的精准定位',
                            confirmText: '确认',
                            confirmColor: '#000',
                            cancelText: '取消',
                            cancelColor: '#666',
                            success: res => {
                                if (res.confirm) {
                                    let info = my.getSystemInfo();
                                    let type = info.platform === 'Harmony' ? 'LOCATION' : 'LBSHIGHACCURACY';
                                    my.showAuthGuide({
                                        authType: type,
                                    });
                                } else if (res.cancel) {
                                }
                            },
                        });
                    }
                },
            });
        },
        /**
         * @description : 选择订单
         * @return        {*}
         */
        selOilOrderClick(item, index) {
            this.selectOrderBut();
            this.selOilOrder = index;
            this.seletctOilOrderData = item;
        },

        // 油站变化还原选取油品油枪
        clearOrderOilStatus() {
            this.selectCurrent = '';
            this.notPaidList = [];
        },
        /**
         * @description : 更多网点
         * @return        {*}
         */
        changeStationAction() {
            let URL = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = { refer: this.refer, bizSeed: 'hpayoilBiz', bizTitle: '后支付加油' };
            let type = 'navigateTo';
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                this.$sKit.layer.useRouter(URL, params, type);
                this.selectCurrent = '';
                this.notPaidList = [];
            } else {
                this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                    if (res) {
                        this.$store.dispatch('initLocationV3_app', {
                            callback: () => {
                                this.$sKit.layer.useRouter(URL, params, type);
                                this.selectCurrent = '';
                                this.notPaidList = [];
                            },
                        });
                    }
                });
            }
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, type);
            this.selectCurrent = '';
            this.notPaidList = [];
            // #endif
        },
        /**
         * @description : 有待支付的订单
         * @return        {*}
         */
        submitOrderPayment(data, navtype) {
            let URL = '/packages/third-oil-charge-payment/pages/oil-charge-payment/main';
            let params = { ...data, refer: this.refer };
            // 支付宝新开页面，其他当前页路由
            let types = this.$sKit.isAlipay || this.isHarmony || this.isH5CLOUD ? 'navigateTo' : 'redirectTo';
            // 支付宝referer过长兼容
            params.zfbRefererMax = true;
            this.notPaidList = [];
            this.selectCurrent = '';
            this.$sKit.layer.useRouter(URL, params, types);
            if (navtype == 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.setValueToNative('Define_Charge_PreAuth_Fuel', encodeURIComponent(JSON.stringify(this.selOilType)));
                // #endif
                // #ifndef MP-MPAAS
                uni.setStorageSync('Define_Charge_PreAuth_Fuel', encodeURIComponent(JSON.stringify(this.selOilType)));
                // #endif
            }
            uni.hideLoading();
        },
        clickClose() {
            this.$refs.unPaidOrderDialogFlag.close();
        },
        /**
         * @description : 有待支付的订单确定弹窗点击事件
         * @return        {*}
         */
        clickUnPaidOrder() {
            this.submitOrderPayment(this.unPaidInfo, 0);
            this.$refs.unPaidOrderDialogFlag.close();
        },
        /**
         * @description : 会员查询预授权订单详情
         * @data         {string} 预授权订单状态1创建；5已取消；3开始加油；4加油结束；
         * @return        {*}
         */
        async preAuthOrderGet(callback) {
            let resData = await preAuthOrderApi({}, { isload: false });
            if (resData.data && (resData.data.preOrderStatus == 1 || resData.data.preOrderStatus == 3)) {
                uni.hideLoading();
                let URL = '/packages/third-oil-charge-payment/pages/authorization-code/main';
                let params = { ...resData.data, refer: this.refer };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(URL, params, type);
            } else {
                callback(resData);
            }
        },
        /**
         * @description : 查询待支付订单
         * @return        {*}
         */
        async unPaidOrderGet(type) {
            let res = await unPaidOrdeQueryrApi({}, { isCustomErr: true, isload: false });
            uni.hideLoading();
            if (res != 'undefined' && res.data && res.data.orderNo && res.data.orderStatus == 1) {
                // 只展示一次小红点的判断
                if (this.redDotFirst) {
                    this.redDotFirst = false;
                    this.$store.commit('setIsOrderDot', true);
                }
                res.data.cancel = true;
                //有待支付订单
                const orderInfo = {
                    unPaidInfo: res.data,
                    stationType: res.data.newStationFlag == 0 ? 1 : 0,
                };
                this.unPaidInfo = orderInfo;
                // this.isUnPaidOrder = true;
                // 你存在一笔待支付订单
                console.log(this.$refs, '测试获取到ref了吗');
                this.$refs.unPaidOrderDialogFlag.open();
                // 油品列表不为空且有选中油品查询待结交易
            } else if (type !== 'redDot') {
                // 没有待支付订单
                this.notPaidListGet(type);
            }
        },

        /**
         * @description : 点击打开导航
         * @return        {*}
         */
        clickNaviStateion() {
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$cnpcBridge.openLocation({
                        latitude: this.selectMarkerV3.latitude,
                        longitude: this.selectMarkerV3.longitude,
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                }
            });
            this.$cnpcBridge.writeMpaasLog('点击打开导航', JSON.stringify(this.selectMarkerV3));
            // #endif
            // #ifndef MP-MPAAS
            uni.openLocation({
                latitude: Number(this.selectMarkerV3.latitude),
                longitude: Number(this.selectMarkerV3.longitude),
                name: this.selectMarkerV3.orgName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
            });
            // #endif
        },

        /**
         * @description : 查询订单前-首页判断是否超过距离-判断是否存在预授权订单信息-再判断是否有位置信息
         * @return        {*}
         */
        searchOrderBefore: function (type) {
            // 会员查询预授权订单详情
            uni.showLoading();
            this.preAuthOrderGet(() => {
                // 查询待支付订单
                this.unPaidOrderGet(type);
            });
        },

        /**
         * @description : submitOrder 该油枪内的10分钟订单
         * @return        {*}
         */
        submitOrder() {
            // 3s内只允许点击一次
            if (!this.timer) {
                if (this.selOilOrder == -'1') {
                    uni.showToast({
                        title: '请先选择订单',
                        icon: 'none',
                        duration: 2000,
                    });
                } else {
                    uni.showLoading({
                        title: '加载中',
                        mask: true,
                        duration: '3000',
                    });
                    this.creatUnPaidOrder();
                    this.timer = setTimeout(() => {
                        this.timer = null;
                    }, 3000);
                }
            }
        },
        /**
         * @description : 生成待支付订单
         * @param         {string} stationCode - 网点编码
         * @param         {string} hosCode - hos编码（网点信息中包含此字段）
         * @param         {string} stationName - 网点名称
         * @param         {string} oilGunNo - 油枪号
         * @param         {string} transNo - 本地交易编号
         * @param         {string} createTime - 创建时间（yyyy-MM-dd HH:mm:ss）
         * @param         {string} hangTime - 挂枪时间
         * @param         {string} priceMode - 价格模式 10全套(零售价)，20:自助（自助价)
         * @param         {string} productNo - 商品编码
         * @param         {string} productName - 商品名称
         * @param         {string} productType - 商品分类
         * @param         {string} unitPrice - 单价
         * @param         {string} productQty - 数量
         * @param         {string} productUnit - 商品单位
         * @param         {string} receivableAmount - 应收金额 (油品必传)
         * @param         {string} gunNo - 枪号 (有枪出必传)
         * @param         {string} tankNo - 油罐号
         * @param         {string} refuelStartTime - 提枪时间 (有枪号必传)
         * @param         {string} refuelEndTime - 挂枪时间 (有枪号必传)
         * @param         {string} startPumpCounter - 起泵码值 (有枪号必传)
         * @param         {string} endPumpCounter - 止泵码值 (有枪号必传)
         * @param         {string} startPressure - 提枪压力 (有枪号必传)
         * @param         {string} endPressure - 挂枪压力 (有枪号必传)
         * @param         {string} tankTemperature - 油罐温度 (油品必传)
         * @param         {string} priceMode - 价格模式 10全套(零售价)，20:自助（自助价)
         * @param         {string} tillNo - 加油机交易号 (有枪号必传)
         * @return        {*}
         */
        async creatUnPaidOrder() {
            let params = {
                stationCode: this.selectMarkerV3.orgCode,
                hosCode: this.selectMarkerV3.hosCode,
                stationName: this.selectMarkerV3.orgName,
                oilGunNo: this.seletctOilOrderData.gunNo,
                transNo: this.seletctOilOrderData.transNo,
                createTime: this.seletctOilOrderData.createTime || '',
                hangTime: this.seletctOilOrderData.hangTime || '',
                priceMode: this.seletctOilOrderData.priceMode || '',
                productNo: this.seletctOilOrderData.productNo || '',
                productName: this.seletctOilOrderData.productName || '',
                productType: this.seletctOilOrderData.productType || '',
                unitPrice: this.seletctOilOrderData.unitPrice || '',
                // productQty: this.seletctOilOrderData.weight || '',
                weight: this.seletctOilOrderData.weight || '',
                productUnit: this.seletctOilOrderData.productUnit || '',
                receivableAmount: this.seletctOilOrderData.receivableAmount || '',
                gunNo: this.seletctOilOrderData.gunNo || '',
                tankNo: this.seletctOilOrderData.tankNo || '',
                refuelStartTime: this.seletctOilOrderData.refuelStartTime || '',
                refuelEndTime: this.seletctOilOrderData.refuelEndTime || '',
                startPumpCounter: this.seletctOilOrderData.startPumpCounter || '',
                endPumpCounter: this.seletctOilOrderData.endPumpCounter || '',
                startPressure: this.seletctOilOrderData.startPressure || '',
                endPressure: this.seletctOilOrderData.endPressure || '',
                tankTemperature: this.seletctOilOrderData.tankTemperature || '',
                tillNo: this.seletctOilOrderData.transNo || '',
            };

            if (this.seletctOilOrderData.deviceNo) {
                params.deviceNo = this.seletctOilOrderData.deviceNo;
            }
            let res = await creatUnPaidOrderApi(params);
            if (res.success) {
                res.data.cancel = false;
                this.unPaidInfo = res.data;
                console.log(this.unPaidInfo, 'this.unPaidInfo');
                this.unPaidInfo.transNo = this.seletctOilOrderData.transNo;
                this.unPaidInfo.gunNo = this.seletctOilOrderData.gunNo;
                this.unPaidInfo.orderStatus = 1;
                const orderInfo = {
                    unPaidInfo: this.unPaidInfo,
                    originalOrder: this.seletctOilOrderData,
                    stationType: this.selectMarkerV3.stationType,
                };
                this.submitOrderPayment(orderInfo, 1);
                uni.hideLoading();
            } else {
            }
        },
        async getMarketingAssistant() {
            let newStationCodeList = await this.$sKit.layer.getMarketingJudgment([this.selectMarkerV3.orgCode]);
            if (newStationCodeList.length > 0) {
                let params = {
                    stationCodeList: newStationCodeList,
                    sceneType: 0,
                };
                let res = await getMarketingRec(params, { isCustomErr: true });
                if (res && res.success) {
                    let newArr = res.data || [];
                    this.marketingAssistant = newArr[0].aiGeneratedCopy;
                    let bizContent = [
                        {
                            stationcode: newArr[0].stationCode,
                            scenetype: newArr[0].sceneType,
                            marketingCode: newArr[0].activityId,
                            marketingContent: newArr[0].aiGeneratedCopy,
                        },
                    ];
                    bizContent = JSON.stringify(bizContent).replace(/,/g, ' ');
                    this.$sKit.mpBP.tracker('智能营销文档曝光', {
                        seed: 'smart_marketing',
                        pageID: 'hpayoilPage', // 返回sdk标识
                        refer: this.refer,
                        channelID: clientCode,
                        dateType: 'exposure',
                        content: bizContent,
                    });
                }
            } else {
                this.marketingAssistant = '';
            }
        },
    },
    watch: {
        // 监听selectMarkerV3.orgCode变化 实现页面变化
        '$store.state.locationV3_app.selectMarkerV3.orgCode': {
            handler: async function (newValue, oldValue) {
                console.log('watch', newValue, oldValue);

                if (newValue) {
                    // #ifdef MP-MPAAS
                    this.getMarketingAssistant();
                    // #endif
                }
                if (newValue != oldValue) {
                    await this.$store.dispatch('getFuelGunByOrgCodePost');
                    console.log('watch:fuelData---', this.fuelData);
                    this.notPaidList = [];
                    this.getPreAuthFuel();
                    setTimeout(() => {
                        this.$emit('changeViewHeight');
                    }, 300);
                }
                //  else if (newValue) {
                //     this.searchOrderBefore();
                // }
            },
            immediate: true,
            deep: true,
        },
        //监听油品信息
        '$store.state.locationV3_app.fuelData': {
            handler: function (newValue, oldValue) {
                console.log('fuelData', newValue);
            },
        },
    },
    // beforeDestroy() {
    //     this.$store.dispatch('zjHideModal');
    // },
};
</script>

<style lang="scss" scoped>
.page-view {
    width: 100%;
    border-radius: 10px 10px 0px 0px;
    flex: 1;
    overflow-y: scroll;
}

.zjMarketBox {
    transform: translateY(5px);
}

.charge-content-view {
    /* #ifdef MP-MPAAS */
    padding-bottom: 40rpx;
    /* #endif */
    /* #ifndef MP-MPAAS */
    /* #endif */
    box-sizing: border-box;
    border-radius: 10px 10px 0 0;
    position: relative;

    .top-area {
        // padding: 12px 0 0 0;
        overflow: hidden;

        .stationinfo {
            border-radius: 0px 0px 8px 8px;

            .detail {
                overflow: hidden;

                // #ifndef MP-ALIPAY
                .name-area {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 180px;

                    .name {
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // white-space: nowrap;
                    }
                }

                // #endif
                // #ifdef MP-ALIPAY
                .name-area {
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 180px;
                    }
                }

                // #endif
                .pad-t-6 {
                    padding-top: 8px;
                }

                .oil-address {
                    padding-top: 12px;
                }
                .time-icon {
                    // width: 52px;
                    padding: 4px 5px;
                    // height: 20px;
                    // line-height: 20px;
                    text-align: center;
                    background: #efeded;
                    border-radius: 4px;
                    // margin: 0 4px;
                }
            }

            .changestation {
                margin-left: 12px;
                width: 56px;
                padding: 2px 0;
                height: 17px;
                text-align: center;
                box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
                border-radius: 4px;
                border: 1px solid #e64f22;
                line-height: 34rpx;
                align-items: center;
                justify-content: center;
                display: flex;
                box-sizing: border-box;
            }

            .margin-box {
                width: 20px;
            }

            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                display: block !important;
            }

            .oil-address {
                // padding-top: 12px;
            }

            .center-area {
                // padding-top: 12px;

                .con-list {
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    margin-right: -10px;

                    .item {
                        padding: 8rpx 16rpx;
                        background: #f3f3f6;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        color: #333333;
                        text-align: center;
                        margin: 0 18rpx 8rpx 0;
                    }

                    // .item:nth-of-type(4n + 0) {
                    //     margin-right: 0px;
                    // }
                }
            }

            // #ifndef MP-ALIPAY
            .center-over {
                width: 320px;
                overflow: hidden;
                // text-overflow: ellipsis;
                white-space: nowrap;
            }

            // #endif
            // #ifdef MP-ALIPAY
            .center-over {
                width: 320px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            // #endif

            .marketing-assistant {
                margin-top: 5px;
                border: 1px solid #c4c4c3;
                background-color: #fef5ee;
                border-radius: 8px 8px;
                padding: 4.5px;

                .marketing-text {
                    font-size: 12px;
                    color: #fb9463;
                    line-height: 17.5px;
                    overflow: hidden; /* 隐藏超出的文本 */
                    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
                    -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
                    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                    word-break: break-all; /* 允许在单词内换行 */
                }
            }
        }
    }

    .box-div {
        padding: 15px 12px 15px 12px;
        box-sizing: border-box;

        // margin-top: 16px;
        // height: 315px;
        .order-box {
            .con-list {
                // margin-top: 12px;
                width: 100%;

                .item {
                    width: 30%;

                    .item-borer {
                        border-radius: 4px;
                        border: 1px solid #d3d3d3;
                        background: #ffffff;
                        padding: 6px 12px;
                        box-sizing: border-box;
                    }

                    .item-sel {
                        background: #fff4f0;
                        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
                        border: 1px solid #e64f22;
                    }
                }
            }

            .con-list::after {
                content: '';
                width: 30%;
            }
        }

        .oil-box {
            .con-list {
                justify-content: flex-start;
                flex-wrap: wrap;
                margin-right: -10px;

                .item {
                    width: 72px;
                    height: 40px;
                    line-height: 40px;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #333333;
                    margin-bottom: 12px;
                    margin-right: 10px;
                    box-sizing: border-box;
                }

                // .item:nth-of-type(4n + 0) {
                //     margin-right: 0px;
                // }

                .oil-type-sel {
                    background: rgba(230, 79, 34, 0.16);
                    border-radius: 4px;
                    border: 1px solid #e64f22;
                    color: #e64f22;
                }
            }
        }
    }
}

.convenienceStoreImg {
    width: 606rpx;
    height: 253rpx;
}

.no-reserve {
    width: 71px !important;
    height: 59px;
    display: block;
}

.marl12 {
    margin-left: 12px;
}

.mart12 {
    margin-top: 12px !important;
}

.mat5 {
    margin-top: 5px;
}

.market-div {
    margin: 12px 16px 0;
}

.height12 {
    width: 100%;
    height: 12px;
    background-color: #fbfbfd;
}

.marb6 {
    margin-bottom: 6px;
}

.marlr15 {
    margin: 0px 15px;
}

._modal {
    flex: none;
    width: 560rpx;
    min-height: 207rpx;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;
}

.iol-pop {
    padding: 38rpx 0 0;

    .content {
        padding: 0 20rpx;
        margin-bottom: 34rpx;
        width: 100%;
        min-height: 68rpx;
    }

    .price-input-area {
        border-radius: 10px;
        height: 44px;

        .price-input {
            width: 100%;
            height: 44px;
            min-height: 44px;
            line-height: 44px;
            background: #f7f7fb;
            border-radius: 8px;
            text-align: center;
            box-sizing: border-box;
        }
    }
}

.slot-btn-box {
    width: 100%;
    border-top: 1px solid #efeff4;

    .btn {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 90rpx;

        &:active {
            background-color: #f7f7f7;
        }
    }

    .cancel_btn {
        border-right: 2rpx solid #efeff4;
    }

    .confirm {
        font-weight: bold;
    }
}

.mar-6-l {
    margin-left: 6px;
}

.mart5 {
    margin-top: 5px;
}

.marl12 {
    margin-left: 12px;
}

.pad-t-10 {
    padding-top: 10px;
}

.padding12 {
    padding: 12px;
    box-sizing: border-box;
}

.marl13 {
    margin-left: 13px;
}

.mart16 {
    margin-top: 16px;
}

.marlr16 {
    margin: 0 16px;
}

.padt16 {
    padding-top: 16px;
}

.padb12 {
    padding-bottom: 12px;
}

.marb12 {
    margin-bottom: 12px;
}

.marb4 {
    margin-bottom: 4px;
}

.pay-btn {
    // padding: 0 16px;
    background-color: #f7f7fb;

    .pay-submit {
        padding: 12px 0 16px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;

        .btn-44 {
            height: 44px;
            border-radius: 8px;
            line-height: 44px;
        }
    }
}

.model-div {
    background: #f7f7fb;
    border-radius: 10px 10px 0px 0px;

    .marlr16 {
        margin: 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .pad12-7 {
        padding: 12px 7px 0;
    }

    .padlt127 {
        padding: 12px 7px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .close {
        padding: 10px;

        img {
            width: 13px;
            height: 13px;
        }
    }

    .con-list {
        justify-content: flex-start;
        flex-wrap: wrap;
        margin-right: -10px;

        .item {
            width: 78px;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            font-size: 14px;
            color: #333333;
            margin-bottom: 12px;
            margin-left: 6px;
            box-sizing: border-box;
        }

        // .item:nth-of-type(4n + 0) {
        //     margin-right: 0px;
        // }

        .oil-type-sel {
            background: rgba(230, 79, 34, 0.16);
            border-radius: 4px;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }
}

.hieght100 {
    height: 120px;
}
.marl5 {
    margin-left: 5px;
}
</style>
