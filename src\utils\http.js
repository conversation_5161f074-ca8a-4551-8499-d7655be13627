import config from './config';
import utils from './index';
import store from '../store/index';
import wxLogin from './login';
import location from '@/store/modules/location.js';
let Fly = require('flyio/dist/npm/wx');

let fly = new Fly();
console.log('fly-------', fly);
let customErr = ''; // 错误信息
let errCode = ''; // 错误号
let needLoadingCount = 0;
let prohibited = false;
let firstErr = true;
// let loginDate = false // 登陆是否过期

const errorApiArr = [
    '/app/json/login/wmnpLogin',
    '/app/json/login2/autoLogin',
    // '/app/json/cnpc_card/cardPay',
    '/app/json/wx_fuel/preOrder',
    '/app/json/card_fuel/cardPay',
    '/app/json/card_fuel/preOrder',
    '/app/json/contract/getContractDisInfo',
    '/app/json/cnpc_card/checkECard',
    '/app/json/cnpc_card/sendVerifyCode',
    '/app/json/cnpc_card/setPayPassword',
    '/app/json/cnpc_card/moveECard',
    '/app/json/cnpc_card/isSetPayPassword',
    '/app/json/cnpc_card/binding',
    '/app/json/ad/getMenuList',
    '/app/json/fuel/lockOrder',
    '/app/json/third/userMigration',
    '/app/json/third/getThirdToken',
    '/app/json/third/isStationNew',
    '/app/json/cnpc_card/getCardConsumeDetails',
    '/app/json/cnpc_card/getCircleDepositList',
    '/app/json/order2/getFuelList',
    '/app/json/invoice/billing',
    '/app/json/coupon/getBestCoupon',
    '/app/json/third/getUserMigrateFlag',
    '/app/json/cnpc_card/applyECard',
    '/app/json/order2/getFuelDetail',
    '/app/json/invoice/billing',
    '/app/json/member/sendLoginVerifyCode',
    '/app/json/member/getVerifyImg',
];
const stringRiskControlArr = ['/app/json/third/getThirdToken'];
const handleErrorFn = () => {
    let _fn;
    if (typeof (_fn = fly.config.handleError) == 'function') {
        _fn();
    }
};
// console.log('store------',store)
fly.config.headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    referrer: 1,
    token: store?.state?.token,
    // 'token': "c:app:test"
};
// 'token':"c:app:test"

fly.config.timeout = 20000;
fly.config.baseURL = config.baseUrl;
fly.config.isLoad = true; // 是否展示加载动画
fly.config.isCustomErr = true; // false 时不回显示错误弹窗
fly.interceptors.request.use(config => {
    config.headers = {
        ...config.headers,
        UUID: 'wx' + utils.getTYUUID('xxxxxxxxxxxxxxxx'),
    };
    // location.state.riskManagementLat = ''
    // location.state.riskManagementLon = ''
    // if (stringRiskControlArr.includes(`${config.url}`)) {
    //   config.body.extendFiled =
    //     JSON.stringify(
    //       {
    //         dfp: '',
    //         gps: (location.state.riskManagementLat && location.state.riskManagementLon) || '',
    //         gpsProvince: '',
    //         gpsCity: '',
    //         gpsArea: '',
    //       }
    //     )
    // }
    if (store.state.token) {
        config.body = Object.assign({}, config.body, { token: store.state.token });
        // config.body = Object.assign({}, config.body, { token: "c:app:test" })
        // config.body = Object.assign({}, config.body, {
        //   token: 'c:app:test'
        // })
    }
    console.log(`---------请求---------${config.url}`, config);
    if (config.isLoad) {
        ShowLoading();
    }
    return config;
});

fly.interceptors.response.use(
    (res, promise) => {
        let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
        let curRoute = routes[routes.length - 1].route; // 获取当前页面路由，也就是最后一个打开的页面路由
        let homeRoute2 = curRoute;
        console.log(`---------响应---------${res.request.url}`, res);
        if (res.request.url === '/app/json/login2/signUpAndBind') {
            if (res.data.errorCode == 40163 && firstErr) {
                firstErr = false;
                return new Promise(async (resolve, reject) => {
                    wxLogin.wxCode = await wxLogin._login();
                    await wxLogin.checkSession();
                    let newRequest = res.request;
                    let newJsonData = JSON.parse(newRequest.body.jsonData);
                    newJsonData.code = wxLogin.wxCode;
                    newRequest.body.jsonData = JSON.stringify(newJsonData);
                    resolve(fly.request(newRequest));
                });
            }
        }
        if (res.request.isLoad) {
            homeRoute2 === 'pages/home/<USER>' ? '' : hideLoading();
        }
        // 登录过期或者未登
        if (fly.config.isCustomErr) {
            console.log('fly.config-----', fly.config);
            if (res.data.errorCode === 1000) {
                customErr = res.data.info + ',no';
                errCode = 1000;
                promise.catch();
            }
            if (res.data.status === -1) {
                if (!errorApiArr.some(item => res.request.url.includes(item))) {
                    customErr = res.data.info + ',no';
                    promise.catch();
                }
            }
        }
        // hideLoading();
        return res.data;
    },
    async error => {
        hideLoading();
        if (
            error.engine._options.url === '/app/json/ad/getPopAdData' ||
            error.engine._options.url === '/app/json/ad/getIndexAdData' ||
            error.engine._options.url === '/app/json/fuel/getPayActivityInfo' ||
            error.engine._options.url === '/app/json/order2/getFuelList'
        ) {
            return;
        }
        if (customErr) {
            let errArr = customErr.split(',');
            utils.showModal(errArr[0], 1).then(res => {
                if (errCode == 1000) {
                    wxLogin.redirectToRegister();
                    errCode = 0;
                }
                handleErrorFn();
                throw new Error(customErr);
            });
        } else {
            //
            utils.showModal('服务繁忙,请稍后重试...', 1).then(res => {
                wx.hideLoading();
                handleErrorFn();
                throw new Error(customErr);
            });
        }
        console.log(error);
    },
);

function ShowLoading() {
    if (needLoadingCount === 0) {
        wx.showLoading({
            title: '加载中',
            mask: true,
        });
    }
    needLoadingCount++;
}

function hideLoading() {
    if (needLoadingCount <= 0) {
        return;
    }
    needLoadingCount--;
    throttle(() => {
        if (needLoadingCount === 0) {
            wx.hideLoading();
        }
    }, 0)();
}

function throttle(func, delay) {
    let timer;
    return function (...args) {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

export default fly;
