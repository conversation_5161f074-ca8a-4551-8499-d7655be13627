<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div style="height: 100%">
            <thirdOrder ref="thirdOrder" v-if="isShow" pageType="page" :navActiveProp="navActive"
                :secondNavActiveProp="secondNavActive" :refuelCardAccountNoProp="refuelCardAccountNo" :refer="refer">
            </thirdOrder>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import thirdOrder from '../../../../s-kit/first/third-order/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'order-list',
    components: { thirdOrder },
    data() {
        return {
            navActive: 1,
            refuelCardAccountNo: '',
            secondNavActive: 0,
            isShow: false,
            refer: '',
        };
    },
    onLoad(options) {
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.navActive) this.navActive = Number(params.navActive);
            if (params.secondNavActive) this.secondNavActive = Number(params.secondNavActive);
            if (params.refuelCardAccountNo) this.refuelCardAccountNo = params.refuelCardAccountNo;
            if (params.refer) this.refer = params.refer;
        }
        this.isShow = true;
    },
    onShow() {
        this.$nextTick(() => {
            this.$refs.thirdOrder.dateReset('onShow');
        });
    },
    methods: {},
    computed: {},
};
</script>
<style scoped lang="scss"></style>
