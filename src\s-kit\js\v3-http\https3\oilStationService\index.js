import { POST, GET2 } from '../../index';
import Store from '@/store';

// export const openWalletPost = (params, config) => {
//   return POST('account.openAccount', params, config)
// }

//查询所有在售油品标号以及油品名称。
export const fuelInfoApi = (params, config) => {
    return POST('oilstation.station.fuelInfo', params, config);
};

//[获取网点列表/oilstation/station/list接口]
export const stationListApi = (params, config) => {
    // 兼容处理orgCode app/支付宝传国标码 微信获取不到目前传空
    // #ifdef MP-MPAAS
    params.orgCode = Store.state.locationV3_app.cityCode;
    // #endif
    // #ifdef MP-ALIPAY
    params.orgCode = Store.state.locationV3_app.cityCode;
    // #endif
    // #ifdef MP-WEIXIN
    params.orgCode = '';
    // #endif
    return POST('oilstation.station.list', params, config);
};

//[查询电子券可用网点列表接口 /account/coupon/availableStation/list]
export const availableStationApi = (params, config) => {
    return POST('account.coupon.availableStation.list', params, config);
};

// 查询油品标号对应的商品信息
export const getProductInfo = (params, config) => {
    return POST('oilstation.station.getProductInfo', params, config);
};

//[获取油品集合/oilstation /station/getFuelGunByOrgCode接口]
export const getFuelGunByOrgCodeApi = (params, config) => {
    return POST('oilstation.station.getFuelGunByOrgCode', params, config);
};
//[查询服务内容列表/oilstation /station/stationServiceList接口]
export const stationServiceListApi = (params, config) => {
    return POST('oilstation.station.stationServiceList', params, config);
};
// [查询油站标签内容列表/oilstation /station/stationServiceLevelList接口]
export const stationServiceLevelListApi = (params, config) => {
    return POST('oilstation.station.stationServiceLevelList', params, config);
};
// 会员查询预授权订单详情
export const preAuthOrderApi = (params, config) => {
    return POST('order.preAuth.order', params, config);
};

// 查询加油状态
export const getPreAuthOrder = (params, config) => {
    return POST('order.preAuth.getPreAuthOrder', params, config);
};
// [生成待支付订单]
export const creatUnPaidOrderApi = (params, config = {}) => {
    config.apiVersion = 'v2';
    return POST('order.unPaidOrder', params, config);
};
// 查询待支付订单
export const unPaidOrdeQueryrApi = (params, config) => {
    // config.apiVersion = 'v2';
    return POST('order.unPaidOrder.query', params, config);
};
//[查询已支付完成的订单详情
export const savedOrderApi = (params, config) => {
    return POST('order.savedOrder.detail', params, config);
};
//手动取消待支付订单 /order/unPaidOrder/cancel]
export const cancelOrderApi = (params, config) => {
    return POST('order.unPaidOrder.cancel', params, config);
};
//[查询订单状态 /order/status接口]
export const statuslOrderApi = (params, config) => {
    return POST('order.status', params, config);
};
//手动选择优惠券之后重新计算优惠
export const calculateOrdeDiscountsApi = (params, config) => {
    return POST('order.calculate.discounts', params, config);
};

// [获取该油枪所有待结交易]
export const notPaidListApi = (params, config) => {
    return POST('order.oilGun.notPaid.list', params, config);
};
// 获取加油码接口
export const getRefuelCodeApi = (params, config) => {
    return POST('order.preAuth.getRefuelCode', params, config);
};
//取消e享加油
export const preAuthCancel = (params, config) => {
    return POST('order.preAuth.cancel', params, config);
};
//e享加油预授权下单
export const generateOrder = (params, config) => {
    return POST('order.preAuth.generateOrder', params, config);
};
//新会员到老站出示付款码后主动轮询订单
export const queryUnpaymentOrderByMemberNo = (params, config) => {
    return POST('account.queryUnpaymentOrderByMemberNo', params, config);
};
//新会员到老站出示付款码后查询订单对应的优惠券列表
export const queryUnusedCouponList = params => {
    return POST('account.queryUnusedCouponList', params);
};
//油站管理_查询油站各油品挂牌价
export const oilPriceApi = (params, config) => {
    return POST('oilstation.station.oilPrice', params, config);
};
// 根据油站编码查询可下单距离接口
export const distanceConfigApi = (params, config) => {
    return POST('oilstation.distanceConfig.query', params, config);
};

//预约加油获取优惠券列表
export const preAuthGetCouponList = (params, config) => {
    return POST('order.preAuth.getCouponList', params, config);
};

// 获取小于10笔订单的油枪待结交易列表
export const transNoList = (params, config) => {
    return POST('order.transNoList', params, config);
};

// export const mapRequestApi = (params,config) => {
//   let url = 'http://api.map.baidu.com/place/v2/search?query=' + params + '&region=131&output=json&ak=' + 'T0VPdIGBUv1w1YgAODQIciaP8CssB588';
//   return GET2(url,config)
// }

// 营销助手
export const getMarketingRec = (params, config) => {
    return POST('oilstation.ai.recByStations', params, config);
};

// 营销助手二期非油商品推荐
export const recNonOilGoods = (params, config) => {
    return POST('oilstation.ai.recNonOilGoods', params, config);
};