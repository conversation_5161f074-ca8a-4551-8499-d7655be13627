import { realPersonIdentify } from '../../../../../s-kit/js/v3-http/https3/oilCard/index'
import { passwordForget } from '../../../../../s-kit/js/v3-http/https3/wallet';
// import { setCloud } from '../../../../../s-kit/js/setCloud'
export default {
  data() {
    return {
      isAuth: false,
      faceValueResponse: null,
      isH5CLOUD: false
    }
  },
  onLoad(options) {
    this.$nextTick(async () => {
      // 获取键盘的实例
      // console.log('this.$sKit.keyBordPlugin',this.$sKit.keyBordPlugin)
      let result = await this.$refs.cloudKeyboardRef.initRef();
      console.log('result', result)
      this.$store.commit('setAccountDataPlugin', result);
      this.passwordKeyboardRef = result;
    });
    console.log('options---', options)
  },
  onShow() {
    // #ifdef H5-CLOUD
    this.isH5CLOUD = true
    // #endif
    if (this.isAuth) {
      this.isAuth = false
      if (this.faceValueResponse) {
        this.realNameAuthentication(this.faceValueResponse)
      }
      return
    }
  },
  mounted() {

  },
  methods: {
    async confirmBtn() {
      if (!this.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none',
        });
        return;
      }
      if (this.newPasswordShow.length !== 6) {
        uni.showToast({
          title: '请输入6位新密码',
          icon: 'none',
        });
        return;
      }
      if (!this.confirmPassword) {
        uni.showToast({
          title: '请再次输入新密码',
          icon: 'none',
        });
        return;
      }
      if (this.confirmNewPasswordShow.length !== 6) {
        uni.showToast({
          title: '请输入6位新密码',
          icon: 'none',
        });
        return;
      }
      // 打开人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', true)
    },
    enterNavEvent() {
      // 关闭人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', false)
      // 支付宝小程序实名--初始化实人认证--人脸验证-实名认证方法
      this.$sKit.h5RealPersonAuthentication.startVerification(this.personInfo).then(response => {
        console.log('链路通过，调用卡通电子账户接口', response);
        if (response) {
          this.faceValueResponse = response
          this.isAuth = true
          upsdk.pluginReady(function () {
            upsdk.createWebView({
              url: response.certifyUrl,
              isFinish: '0' //是否关闭当前的窗口，1':关闭，'0':不关闭
            });
          })
        } else {
        }
      });
    },

    /**
     * @description  :  实人认证
     * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
     * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
     * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
     * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
     * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
     * @param         {string} idNo: 用户身份证号
     * @return        {*}
     */
    realNameAuthentication(faceValue) {
      return new Promise(async (resolve, reject) => {

        let params = {
          // 认证校验码，初始化实人认证接口返回的数据。
          authInfo: faceValue.authInfo,
          // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡；10-资金转出；
          type: faceValue.type || 1,
          // 认证接入方式：1—APP接入；2—PC或H5接入；
          verifyMode: '2',
          // 当type=1时该字段为必填项。
          idNo: faceValue.idNo,
          // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
          verifyUnique: faceValue.verifyUnique,
          // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
          certifyId: faceValue.certifyId,
          gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
        };
        console.log(params, 'H5传入实人认证接口的参数');

        let res = await realPersonIdentify(params);
        if (res.success && res.data.authInfo) {
          console.log(res, '实人认证方法接口返回结果');
          // uni.clearStorageSync()
          this.submitForgotPassword(res.data.authInfo)

          resolve(res);
        } else {

        }
      });
    },
  }
}