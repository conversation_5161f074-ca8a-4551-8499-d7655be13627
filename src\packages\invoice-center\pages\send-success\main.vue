<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="操作成功"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div class="view">
            <img class="suc-image" src="@/static/cz-success-icon.png" mode="" />
            <div class="suc-title">操作成功</div>
            <div class="suc-detail">请稍后登录预留邮箱查看</div>
        </div>
        <div class="add-footer">
            <div class="footer-left" @click="clickLookInvoice">发票预览</div>
            <div class="footer-btn" @click="clickfpHistory">发票历史</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            path: '',
        };
    },
    onLoad(option) {
        this.path = option.src;
    },
    methods: {
        clickfpHistory() {
            uni.navigateBack({
                delta: 2,
            });
        },
        clickLookInvoice() {
            uni.navigateTo({
                url: '/packages/web-view/pages/home/<USER>' + this.path,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .view {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: -200px;
        .suc-image {
            width: 240px;
            height: 190px;
        }
        .suc-title {
            font-size: 24px;
            color: #333333;
            font-weight: 700;
        }
        .suc-detail {
            font-size: 15px;
            color: #909090;
            padding-top: 10px;
        }
    }
}
.add-footer {
    width: 100vw;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    padding: 10px 15px 10px 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
    display: flex;

    .footer-left {
        flex: 1;
        border-radius: 5px;
        border: 1px solid $btn-color;
        background-color: $btn-mantle-color;
        color: $btn-color;
        line-height: 44px;
        text-align: center;
        font-weight: bold;
        box-sizing: border-box;
        font-size: 15px;
    }

    .footer-btn {
        flex: 1;
        margin-left: 10px;
        height: 44px;
        background: $btn-color;
        border-radius: 5px;
        font-size: 15px;
        text-align: center;
        font-weight: bold;
        color: #ffffff;
        line-height: 44px;
    }
}
</style>
