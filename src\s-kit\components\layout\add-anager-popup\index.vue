<template>
    <div class="view">
        <div class="popup-box">
            <img class="popup-bg" src="../../../image/add-anager.png" alt="" />
            <img class="popup-code" show-menu-by-longpress :src="codeImg" alt="" />
            <img class="popup-close" @click="close" src="../../../image/add-anager-close.png" alt="" />
        </div>
    </div>
</template>

<script>
import { addStationWechatWork } from '../../../js/v3-http/https3/user';
export default {
    name: 'add-anager-popup',
    props: {
        orgCode: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            codeImg: '',
        };
    },
    created() {
        this.getCodeImg();
    },
    methods: {
        async getCodeImg() {
            let res = await addStationWechatWork({ orgCode: this.orgCode });
            if (res && res.success) {
                this.codeImg = res.data?.qrCode || '';
            }
        },
        close() {
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    .popup-box {
        width: 560rpx;
        height: 889rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .popup-bg {
            width: 560rpx;
            height: 769rpx;
        }
        .popup-code {
            position: absolute;
            width: 300rpx;
            height: 300rpx;
            top: 261rpx;
            left: 50%;
            transform: translate(-50%, 0);
        }
        .popup-close {
            width: 60rpx;
            height: 60rpx;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%, 0);
        }
    }
}
</style>
