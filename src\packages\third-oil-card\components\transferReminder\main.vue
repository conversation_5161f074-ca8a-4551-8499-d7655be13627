<template>
    <view class="uni-popupWrap bg-fff fl-column border-rad-8" style>
        <div class="title te-center font-18 color-333 weight-bold">温馨提示</div>
        <div class="explain font-12 color-333 weight-400">
            <div>卡号为{{modalData.cardNo}}中，备用金余额￥{{modalData.cardBalance}}，将转入到您的昆仑e享卡余额中，</div>
            <div class="color-FF0A0A te-center">转出后将不能撤回</div>
        </div>
    </view>
</template>
<script>
export default {
    name: 'fund-transfer-out',
    props: {
        modalData: {
            type: Object,
            default: false,
        },
    },
    components: {},
    data() {
        return {};
    },
    computed: {},
    onLoad() {},
    mounted() {
        console.log(2222, this.modalData)
    },
    methods: {
        // TODO html测试数据
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.uni-popupWrap {
    background-color: #fff;
    // padding: 15px;
    // width: 75%;
    // height: ;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    border-radius: 8px;
    .title {
        margin-bottom: 14px;
        text-align: center;
        height: 23px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }
    .explain {
        // text-align: center;
        margin: 0 auto;
        // width: 85%;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        // margin-right: 20px;
        // margin-left: 20px;
        // margin-bottom: 19px;
    }
    .tips {
        background: #f5f5f5;
        // width: 90%;
        margin: 23rpx auto;
        .tips-content {
            padding: 9px 10px;
            .reminder {
                color: #333333;
                font-weight: bold;
                font-size: 12px;
            }
            .explain2 {
                color: #333333;
                font-size: 12px;
                .span1 {
                    color: #e65023;
                    font-size: 12px;
                }
                .span2 {
                    color: #e64f22;
                    font-size: 12px;
                }
            }
        }
    }
    .btn {
        width: 100%;
        border-top: 1px solid #eee;
        height: 44px;
        border: 0 0 8px 8px;
        display: flex;
        margin-top: 10px;
        flex: 1;
        .allow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #333333;
        }
        .notAllow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            border-right: 1px solid #eee;
        }
    }
}
</style>
