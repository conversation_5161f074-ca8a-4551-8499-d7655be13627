<template>
    <div class="view">
        <!-- 管理车辆 -->
        <!-- 管理车辆页面 -->
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="车辆管理"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- 车辆列表 -->
        <view>
            <view class="uni-list" v-if="vehicleList.length !== 0">
                <checkbox-group @change="checkboxChange1">
                    <view class="uni-list-cell uni-list-cell-pd" v-for="item in vehicleList" :key="item.carId">
                        <view>
                            <checkbox :checked="item.checked" :value="item.carId" style="transform: scale(0.6)" color="#FFCC33" />
                        </view>
                        <view class="setFontSize" @click="checkboxChange(item)">
                            <view>{{ item.carNo }}</view>
                            <view>常用油品:{{ item.oilNo | oilType(this) }}</view>
                        </view>
                    </view>
                    <div style="padding-left: 10px" v-if="vehicleList.length === 3 ? true : false">注：每位用户只能添加三个车辆</div>
                </checkbox-group>
            </view>
            <view v-else class="noVehicle-con">
                <div class="noVehicle">
                    <img src="../../image/nocar-placeholder.png" alt />
                </div>
                <div class="addcar" @click="addCar"> <img src="../../image/plus-icon(1).png" alt />立即添加 </div>
            </view>
        </view>
        <!-- 下方选择添加车辆还是编辑车辆 -->
        <view class="selectOrAddCar">
            <div class="car cars" @click="selectCar" v-if="vehicleList.length !== 0">选择车辆</div>
            <div class="car" @click="addCar" v-if="vehicleList.length === 3 ? false : true">添加车辆</div>
        </view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { getVehicleList } from '@/api/anEcardToOpenCard.js';
export default {
    name: 'managementTheVehicle', //管理车辆
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            vehicleList: [],
            testdata: {
                productNoValue: '',
                licensePlateColorValue: '',
                id: '',
                name: '',
            },
            carInfoArr: [],
            multiSelector: [
                { value: '300668', label: '92#汽油' },
                { value: '300667', label: '95#汽油' },
                { value: '300684', label: '98#汽油' },
                { value: '300644', label: '0#柴油' },
                { value: '300568', label: '-10#柴油' },
                { value: '300567', label: '-20#柴油' },
                { value: '300575', label: '-35#柴油' },
            ],
            // carNoColors: [
            //   { 'value': "0", 'label': '蓝色' },
            //   { 'value': "1", 'label': '黄色' },
            //   { 'value': "2", 'label': '黑色' },
            //   { 'value': "3", 'label': '白色' },
            //   { 'value': "4", 'label': '渐变绿色' },
            //   { 'value': "5", 'label': '黄绿双拼色' },
            //   { 'value': "6", 'label': '蓝白渐变色' },
            //   { 'value': "11", 'label': '绿色' },
            //   { 'value': "12", 'label': '红色' },
            //   { 'value': "9", 'label': '未确定' },
            // ],
            productNo: '',
            // licensePlateColor: '',
        };
    },
    onShow() {
        let pages = getCurrentPages();
        let currPage = pages[pages.length - 1]; //当前页面
        this.getCarList();
    },
    filters: {
        oilType(val) {
            return val === '300668'
                ? '92#汽油'
                : val === '300667'
                ? '95#汽油'
                : val === '300684'
                ? '98#汽油'
                : val === '300644'
                ? '0#柴油'
                : val === '300568'
                ? '-10#柴油'
                : val === '300567'
                ? '-20#柴油'
                : val === '300575'
                ? '-35#柴油'
                : '';
        },
    },
    onLoad(options) {
        if (options.carInfo !== '[]') {
            this.carInfoArr = JSON.parse(options.car);
        }
        // let cardInfo = JSON.parse(options.cardInfo)
        // console.log(cardInfo);
    },
    methods: {
        checkboxChange1: function (e) {
            console.log(e);
            var items = this.vehicleList,
                values = e.detail.value;
            for (var i = 0, lenI = items.length; i < lenI; ++i) {
                const item = items[i];
                if (values.includes(item.carId)) {
                    this.$set(item, 'checked', true);
                } else {
                    this.$set(item, 'checked', false);
                }
            }
        },
        // 获取车辆列表
        getCarList() {
            let params = {
                token: this.$store.state.token,
            };
            getVehicleList(params).then(res => {
                if (res.status === 0) {
                    console.log(res, 'resresres');
                    this.vehicleList = res.data;
                    // 遍历回显汽油号
                    res.data.map(item => {
                        // this.multiSelector.map(ite => {
                        //   if (item.oilNo === ite.value) {
                        //     this.productNo = ite.label
                        //   }
                        // })
                        // this.carNoColors.map(ite => {
                        //   if (item.carNoColor === ite.value) {
                        //     this.licensePlateColor = ite.label
                        //   }
                        // })
                    });
                    // 没有车辆就显示弹框
                    if (this.vehicleList.length === 0) {
                        this.sureOrcancel();
                    }
                    // 给车辆json加标识  被转中的车辆 checked 变为true  然后将数据分发出去 在上一个页面进行筛选 checked === true的数据 组合后写入参数传给后台
                    this.vehicleList.map(item => {
                        let key = 'checked';
                        item[key] = false;
                    });
                    if (this.carInfoArr.length !== 0) {
                        this.carInfoArr.map(item => {
                            this.vehicleList.map(ite => {
                                if (item.carLicense === ite.carNo) {
                                    ite.checked = true;
                                    return;
                                }
                            });
                        });
                        uni.$emit('selectCar', this.vehicleList);
                    }

                    // item.cardType == 0 ? commit('setIsHaveEntityCard', true) : commit('setIsHaveECard', true);
                }
            });
        },
        // 选择车辆
        selectCar() {
            if (this.vehicleList.find(item => item.checked)) {
                // this.carInfoArr = this.vehicleList
                console.log(this.vehicleList, 'this.vehicleList');
                uni.$emit('selectCar', this.vehicleList);
                uni.navigateBack({
                    //返回
                    delta: 1,
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: '请选择车辆',
                    confirmColor: '#FF8200',
                    showCancel: true,
                    success: () => {},
                });
            }
        },
        // 添加车辆
        addCar() {
            uni.navigateTo({
                url: '/packages/ecard-apply/pages/addCar/main',
            });
        },
        // 点击当前车辆  拿到相应的item
        checkboxChange(e) {
            uni.navigateTo({
                url: `/packages/ecard-apply/pages/addCar/main?item=${JSON.stringify(e)}`,
            });
        },
        // 当没有车辆时演出弹窗点击确定和取消跳转
        sureOrcancel() {
            wx.showModal({
                title: '提示',
                content: `您尚未添加车辆,是否绑定添加车辆`,
                confirmText: '确定',
                confirmColor: '#FF8200',
                showCancel: true,
                success: res => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/packages/ecard-apply/pages/addCar/main',
                        });
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// .page {
//   height: 100%;
// }
.view {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    .uni-list-cell {
        display: flex;
        align-items: center;
        background: #fff;
        border-bottom: 1px solid #eee;
        height: 70px;
        margin: 10px;
        border-radius: 4px;
        .setFontSize {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            height: 100%;
            // line-height: 100%;
            font-size: 16px;
        }
    }
    .selectOrAddCar {
        position: absolute;
        bottom: 0;
        width: 100%;
        padding: 10px;
        border-radius: 4px;
        .car {
            width: 100%;
            height: 46px;
            margin: 0 auto;
            font-size: 16px;
            border: 1px solid #f96702;
            text-align: center;
            line-height: 46px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .cars {
            background: #f96702;
            color: #fff;
        }
    }
    .noVehicle-con {
        padding-top: 50px;
        .noVehicle {
            width: 150px;
            height: 150px;
            margin: 0 auto;
            img {
                width: 150px;
                height: 150px;
            }
        }
        .addcar {
            display: flex;
            height: 40px;
            background: #f96702;
            width: 120px;
            align-items: center;
            /* text-align: center; */
            justify-content: center;
            border-radius: 4px;
            margin: 0 auto;
            img {
                width: 15px;
                height: 15px;
                margin: 0 5px 0 -5px;
            }
        }
    }
}
</style>
