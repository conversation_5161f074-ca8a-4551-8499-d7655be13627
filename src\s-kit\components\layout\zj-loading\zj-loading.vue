<template>
    <div>
        <div class="mask" v-if="isLoading">
            <div class="showPassword">
                <div class="payment-box">
                    <div class="loading"></div>
                    <div class="box-text">加载中...</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {},
    data() {
        return {};
    },
    onLoad(option) {},
    onShow() {},
    methods: {},
    computed: {
        isLoading() {
            console.log('thirdIndex.isLoading', this.$store.state.thirdIndex.isLoading);
            return this.$store.state.thirdIndex.isLoading;
        },
    },
    watch: {
        '$store.state.thirdIndex.isLoading': {
            handler: function (newValue, oldValue) {
                if (newValue) {
                    setTimeout(() => {
                        this.$store.commit('setLoadingStatus', false);
                    }, 3000);
                }
            },
        },
    },
    components: {},
};
</script>
<style scoped lang="scss">
.mask {
    position: fixed;
    background: rgba(0, 0, 0, 0.1);
    z-index: 9999;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

    .showPassword {
        position: fixed;
        width: 100%;
        top: 50%;
        left: 50%;
        transform: translate(-10%);
        z-index: 999;

        .payment-box {
            position: relative;
            overflow: hidden;
            z-index: 200;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #000;
            border-radius: 4px;
            width: 72px;
            height: 72px;
            color: #fff;

            .loading {
                width: 30px;
                height: 30px;
                border: 2px solid #fff;
                border-top-color: transparent;
                border-radius: 100%;
                animation: circle infinite 0.75s linear;
                margin-bottom: 5px;
            }
        }
    }
}

// 转转转动画
@keyframes circle {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
