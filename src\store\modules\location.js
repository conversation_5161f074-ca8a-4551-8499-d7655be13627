import qqmap from '../../utils/qqmap-wx-jssdk.min.js';
import util from '../../utils/index.js';
import index from '../index';
import Config from '../../utils/config.js';
import getAllCardList from './card.js';
import Vue from 'vue';
import UseRouter from '../../s-kit/js/layer.js';
import { cardListPost } from '@/api/home.js';
import {
    getNearStationList,
    getBannerList,
    getPopadvertisData,
    getMenuList,
    isStationNew,
    userMigration,
    getUserMigrateFlag,
    getThirdToken,
} from '../../api/home.js';
import { staffMemberBind } from '../../s-kit/js/v3-http/https3/wallet.js';
import Store from '../index.js';
import { maxDistance } from '../../../project.config';
import sKit from '@/s-kit/js';

let page = 1; // 页数
let pageSize = 5; // 每页多少个
let timer = null;
const getLocationInfo = async (state, commit, suc, isUpLoad, dispatch) => {
    uni.getLocation({
        type: 'gcj02', //火星坐标系
        success: res => {
            console.log(res.latitude, res.longitude, '获取用户位置信息');
            // console.log('获取位置信息经纬度', res)
            state.authorizedLocationFlag = true;
            getOnlineDistrict(res.latitude, res.longitude, state, commit, suc, true, isUpLoad);
            // getOnlineDistrict(40.166871, 116.23737, state, commit, suc, true, isUpLoad); //规划院KKK测试站（西安劳动南路20号）
            // getOnlineDistrict(39.944575, 116.483976, state, commit, suc, true, isUpLoad)//39.944575,116.483976 北京朝阳
            // getOnlineDistrict(41.549346, 120.343121, state, commit, suc, true, isUpLoad)//41.549346,120.343121 辽宁朝阳
            // getOnlineDistrict(46.055757,121.927412, state, commit, suc, true, isUpLoad)//46.055757,12、1.927412 内蒙兴安盟
            // getOnlineDistrict(36.030459,103.844823, state, commit, suc, true, isUpLoad)//36.030459,103.844823 甘肃兰州
            // getOnlineDistrict(30.860549,120.023555, state, commit, suc, true, isUpLoad)//30.860549,120.023555 浙江湖州
            if (suc.val === 'thirdLogin') {
                // dispatch('getToken3', suc);
            }
        },
        fail: function (err) {
            // 是否登录进入3.0
            let isTetIntoThird = false;
            if (suc.val === 'thirdLogin') {
                // 登录3.0方法返回是否进入3.0
                // isTetIntoThird = dispatch('getToken3', suc);
                return;
            }
            console.log(err, 'errr');
            if (err.errMsg.includes('频繁调用')) {
                return;
            }
            state.authorizedLocationFlag = false;
            return uni.showModal({
                title: '提示',
                content: `暂时无法获取到手机定位信息，请检查手机定位是否开启或切换网络环境尝试。`,
                confirmColor: '#FF8200',
                confirmText: '我知道了',
                // confirmText: "去导航",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        // err.openLocation({
                        //   latitude: Number(this.selectMarker.latitude),
                        //   longitude: Number(this.selectMarker.longitude),
                        //   name: this.selectMarker.stationName,
                        //   address: this.selectMarker.address,
                        // });
                        // 如果登录3.0，提示已选择默认城市
                        if (isTetIntoThird) {
                            util.showModal('暂时无法获取定位，已为您选择默认城市', true, '#333333', '', '确定', '');
                        }
                    }
                },
            });
        },
    });
};
// 获取油站marker信息
const getMarker = async (state, commit, map) => {
    return new Promise(async (resolve, reject) => {
        commit('setLoadMortState', 'loading');
        let locationRes = await getNearStationList({
            province: state.province,
            longitude: state.myLon,
            latitude: state.myLat,
            pageIndex: page,
            pageSize: pageSize,
        });

        console.log(locationRes, 'locationReslocationReslocationRes');
        // locationRes.data = []
        if (JSON.stringify(locationRes.data) !== '[]') {
            wx.hideLoading();
            // 传入banner图所需要的省份下的区或者市
            if (['上海', '重庆', '天津'].includes(locationRes.data[0].provinceName)) {
                commit('setCity', locationRes.data[0].provinceName);
            } else {
                commit('setCity', locationRes.data[0].cityName);
            }
            commit('setProvince', locationRes.data[0].provinceName);
            // console.log(Store.state, 'state.isLogin')
            if (state.advertisementFlag && Store.state.isLogin) {
                Store.dispatch('getAdvertisingPopups', { city: state.city, showLimit: 0 }); // 获取弹窗广告
                Store.dispatch('getBanner', state.city); // 获取轮播图
                Store.dispatch('getMenuData', state.city); // 获取自定义菜单列表
            }

            let locArr = getLocationArr(locationRes.data); // 油站经纬度坐标
            let markerArr = getAvailableMarket(locationRes.data, [], state);
            if (markerArr.length < pageSize) {
                commit('setLoadMortState', 'nomore');
            } else {
                commit('setLoadMortState', 'loadmore');
            }
            resolve(markerArr);
        } else {
            wx.hideLoading();
            commit('setLoadMortState', 'nomore');
            getLocation(state.myLat, state.myLon, commit, true, state);
            // uni.showToast({
            //   icon: 'none',
            //   title: '您的位置附近没有加油站'
            // })
            reject();
        }
    });
};
// 逆解析
const getLocation = (latitude, longitude, commit, islocation, state) => {
    let map = new qqmap({
        key: 'WQEBZ-VTP3F-BBCJR-J5GZP-XZHCQ-LRF4E',
    });
    //通过这个方法来实现经纬度反推省市区
    map.reverseGeocoder({
        location: {
            latitude: latitude,
            longitude: longitude,
        },
        success: async res => {
            wx.hideLoading();
            console.log('位置相关信息', res);
            let mayLoc = {
                lat: latitude,
                lon: longitude,
            };

            console.log(res.result.address_component, 'res.result.address_component');
            commit('setLocationInfo', res.result.address_component);
            commit('setLatAndLon', mayLoc);
            commit('setProvince', res.result.address_component.province);
            if (['北京'].includes(res.result.address_component.city)) {
                commit('setCity', res.result.address_component.district);
            } else {
                commit('setCity', res.result.address_component.city);
            }
            if (islocation) {
                console.log('123123123');
                let locObj = {
                    lat: latitude,
                    lon: longitude,
                };
                commit('setMyLocation', locObj);
                // await getBanner(commit, res.result.address_component.province)
            }
        },
        fail: function (res) {
            wx.hideLoading();
        },
    });

    /****** 去掉腾讯地图api *******/
    let mayLoc = {
        lat: latitude,
        lon: longitude,
    };
    commit('setLatAndLon', mayLoc);
    if (islocation) {
        let locObj = {
            lat: latitude,
            lon: longitude,
        };

        commit('setMyLocation', locObj);
        console.log(mayLoc, locObj);
    }
};

// 这里调用腾讯地图api 获取用户位置所在区
const getOnlineDistrict = async (latitude, longitude, state, commit, suc, islocation = false, isUpLoad = false) => {
    let locObj = {
        lat: latitude,
        lon: longitude,
    };
    // 设置风控经纬度
    commit('setRiskLocation', locObj);
    commit('setMyLocation', locObj);

    page = 1;
    let markerArr = await getMarker(state, commit);
    commit('setMarkerArr', markerArr);
    // 设置地图中心及选中的marker
    let index = markerArr.findIndex(item => item.openState == 1);
    index = index == -1 ? 0 : index;
    let centerParam = {
        marker: markerArr[index],
        isLocation: islocation,
        isUpLoad: isUpLoad,
    };
    commit('setSelectMarkerToMapCenter', centerParam);
    if (suc.val === 'homePages' || suc.val === 'thirdLogin') {
        return;
    } else {
        if (suc.constructor === Object) {
            suc.callback();
        } else {
            suc();
        }
    }
};
// 通过marker数组获取经纬度数组
const getLocationArr = markerArr => {
    let locArr = [];
    for (let i = 0; i < markerArr.length; i++) {
        locArr.push({
            latitude: markerArr[i].posY,
            longitude: markerArr[i].posX,
        });
    }
    return locArr;
};
let petroChinaIcon = require('@/static/img/petroChinaIcon.png');
let normalIcon = require('@/static/img/normal-icon.png');
// marker数据整理
const getAvailableMarket = (markerArr, locArr, state) => {
    for (let i = 0; i < markerArr.length; i++) {
        let distance =
            markerArr[i].distance / 1000 > 1 ? (markerArr[i].distance / 1000).toFixed(1) : (markerArr[i].distance / 1000).toFixed(2);
        // 驾车时间
        let duration = locArr.length > 0 ? parseInt(locArr[i].duration / 60 + 1) : '';
        markerArr[i] = {
            ...markerArr[i],
            distance: distance,
            duration: duration,
            latitude: Number(markerArr[i].posY),
            longitude: Number(markerArr[i].posX),
            customCallout: null,
            iconPath: markerArr[i].openState == 1 ? petroChinaIcon : normalIcon,
            width: 31.95,
            height: 31.95,
        };
        if (!state.isCustomCallout) {
            markerArr[i].callout = {
                content: duration != '' ? `距离您${distance}km 驾车需${duration}分钟` : `距您${distance}km`,
                padding: 10,
                bgColor: '#ffffff',
                borderRadius: 20,
                fontSize: 12,
                color: '#333333',
            };
        }
    }
    console.log(uni.getSystemInfoSync());
    return markerArr;
};
export default {
    state: {
        myLat: '', // 我的纬度
        myLon: '', // 我的经度
        province: '', // 省份（我的定位的省份）
        city: '', // 城市（我的定位的城市）
        lat: '39.908877', //纬度 (地图中心)
        lon: '116.397412', //经度 (地图中心)
        markerArr: '', // 油站信息
        showMarkerArr: [], // 地图展示的map数组
        selectMarker: '', // 选中的油站
        loadMortState: 'loadmore', // 上拉记载更多状态值
        scale: 12, // 地图缩放比例
        isCustomCallout: true, // 对不能自定义气泡的微信版本进行兼容
        /* 省份位置相关 */
        locationArr: [], // 省份位置数组
        bannerListCountry: [], // 省份轮播图数据
        bannerListCity: [], // 城市轮播图数据
        advertisFlag: false, // 控制首页广告图的展示与隐藏
        myPageAdvertisFlag: false, // 控制我的页面广告图的展示与隐藏
        advertisData: null,
        advertisementFlag: 1, // 控制首页广告只展示一次
        myPageAdvertisementFlag: 1, // 控制我的页面广告只展示一次
        moduleBannerListShow: false, // 控制自定义菜单
        moduleBannerList: [], // 自定义菜单数组
        authorizedLocationFlag: false, // 是否同意获取地理位置信息权限
        // 点击立即升级是否弹出弹框标识
        newStationFlag: false,
        riskManagementLat: '', //风控纬度
        riskManagementLon: '', // 风控经度
        // #ifdef MP-WEIXIN
        officialAccountParams: '', // 存储外部跳转进来携带的参数
        // #endif
        maskDialogFlag: false, //首页透明遮罩标识
        // 邀请开卡2.0用户自动升级获取不到携带的参数，导致不能升级成功。(用当前的值做个获取值然后进行升级的判断)
        loginThirdFlag: false,
        // 微信支付携带的跳转链接上是否登录再进行跳转
        wxPayIsLoginFlag: Boolean,
        // 优途小程序手机号
        ytPhone: '',
        // 支付活动在活动页面登录的手机号
        zfPhone: '',
        logInAgain: false,
    },
    mutations: {
        // 设置微信版本兼容参数
        setIsCustomCalloutIs(state, isCustomCallout) {
            state.isCustomCallout = isCustomCallout;
        },
        // 设置地图缩放比例
        setScale(state, scale) {
            state.scale = scale;
        },
        // 设置上拉加载更多状态
        setLoadMortState(state, loadMortState) {
            state.loadMortState = loadMortState;
        },
        // 设置省分位置数组
        setLocationArr(state, locationArr) {
            state.locationArr = locationArr;
        },
        // 设置定位经纬度信息 (地图中心)
        setLatAndLon(state, mapLocation) {
            state.lat = mapLocation.lat;
            state.lon = mapLocation.lon;
        },
        // 设置我的定位信息
        setMyLocation(state, locationObj) {
            state.myLat = locationObj.lat;
            state.myLon = locationObj.lon;
        },
        // 设置风控经纬度
        setRiskLocation(state, locationObj) {
            state.riskManagementLat = locationObj.lat || '';
            state.riskManagementLon = locationObj.lon || '';
        },
        // 设置省份信息
        setProvince(state, province) {
            state.province = province;
            // console.log(province, 'setProvince');
        },
        // 设置省份下的城市信息
        setCity(state, city) {
            state.city = city;
            // console.log(city, 'setProvince');
        },
        // 设置油站信息数组
        setMarkerArr(state, markerArr) {
            //
            state.markerArr = markerArr;
        },
        // 增长油站信息数组
        pushMarkerArr(state, markerArr) {
            state.markerArr = state.markerArr.concat(markerArr);
        },
        // 设置选中的油站信息
        setSelectMarker(state, selectMarker) {
            let index = -1;
            for (let i = 0; i < state.markerArr.length; i++) {
                if (selectMarker.id == state.markerArr[i].id) {
                    if (state.isCustomCallout) {
                        state.markerArr[i].customCallout = {
                            display: 'ALWAYS',
                        };
                    } else {
                        state.markerArr[i].callout.display = 'ALWAYS';
                    }

                    index = i;
                } else {
                    if (state.isCustomCallout) {
                        state.markerArr[i].customCallout = null;
                    } else {
                        state.markerArr[i].callout.display = null;
                    }
                }
            }
            state.showMarkerArr = util.deepCloneSwapArray(state.markerArr, index, state.markerArr.length - 1);
            state.selectMarker = selectMarker;
        },
        // 设置选中的油站并展示在地图中心
        setSelectMarkerToMapCenter(state, centerData) {
            let selectMarker = centerData.marker;
            let islocation = centerData.isLocation;
            let isUpLoad = centerData.isUpLoad;
            let index = -1;
            for (let i = 0; i < state.markerArr.length; i++) {
                if (selectMarker.id == state.markerArr[i].id) {
                    if (state.isCustomCallout) {
                        state.markerArr[i].customCallout = {
                            display: 'ALWAYS',
                        };
                    } else {
                        state.markerArr[i].callout.display = 'ALWAYS';
                    }
                    index = i;
                } else {
                    if (state.isCustomCallout) {
                        state.markerArr[i].customCallout = null;
                    } else {
                        state.markerArr[i].callout.display = null;
                    }
                }
            }

            state.showMarkerArr = util.deepCloneSwapArray(state.markerArr, index, state.markerArr.length - 1);

            state.selectMarker = selectMarker;

            state.lat = islocation && isUpLoad ? state.myLat : selectMarker.latitude;
            state.lon = islocation && isUpLoad ? state.myLon : selectMarker.longitude;
        },
        // 存储轮播省份的数组
        setBannerListCountry(state, bannerListCountry) {
            state.bannerListCountry = bannerListCountry;
        },
        // 存储轮播省份下的城市数组
        setBannerListCity(state, bannerListCity) {
            state.bannerListCity = bannerListCity;
        },
        setAdvertisFlag(state, setAdvertisFlag) {
            // 控制首页弹窗显示
            state.advertisFlag = setAdvertisFlag;
        },
        setMyPageAdvertisFlag(state, setMyPageAdvertisFlag) {
            // 控制我的页面弹窗展示
            state.myPageAdvertisFlag = setMyPageAdvertisFlag;
        },
        setAdvertisData(state, setAdvertisData) {
            // 弹窗数据
            state.advertisData = setAdvertisData;
        },
        setMyPageAdvertisementFlag(state, setMyPageAdvertisementFlag) {
            // 控制我的页面只展示一次
            state.myPageAdvertisementFlag = setMyPageAdvertisementFlag;
        },
        setAdvertisementFlag(state, setAdvertisementFlag) {
            // 控制我的首页只展示一次
            state.advertisementFlag = setAdvertisementFlag;
        },
        setModuleBannerListShow(state, setModuleBannerListShow) {
            // 控制首页轮播图显示
            state.moduleBannerListShow = setModuleBannerListShow;
        },
        setModuleBannerList(state, setModuleBannerList) {
            // 首页自定义菜单数组
            state.moduleBannerList.push(setModuleBannerList);
        },
        setMaskDialogFlag(state, info) {
            // 2.0首页透明遮罩
            state.maskDialogFlag = info;
        },
        setLoginThirdFlag(state, info) {
            state.loginThirdFlag = info;
        },
        // 存储优途小程序的手机号
        setYtPhone: (state, info) => {
            state.ytPhone = info;
        },
        // 存储优途小程序的手机号
        setZfPhone: (state, info) => {
            console.log('vuex中的支付活动的手机号', info);
            state.zfPhone = info;
        },
        setLogInAgain(state, info) {
            state.logInAgain = info;
        },
        // setRecommendedRegistration(state,info){
        //     state.recommendedRegistration = info
        // },

        // 根据公众号或者小程序跳转过来携带的参数以及是否存在3.0token跳转2.0或者3.0相应页面
        async jumpToOfficialAccount(state, token3) {
            console.log(state.logInAgain, '领券登录跳转3.0电子券页面标识');
            const { wxPayIsLoginFlag } = state;
            let officialAccountParams = state.officialAccountParams;
            console.log(officialAccountParams, token3, 'officialAccountParams');
            console.log(index.state.token, '2.0token');
            if (!wxPayIsLoginFlag) return;
            if (token3) {
                await Store.dispatch('getSetWalletStatus', {});
                // e享卡是否开通状态
                let status = Store.state.wallet.walletStatus.status;
                // e享卡开通以后的状态
                let accountStatus = Store.state.wallet.walletStatus.accountStatus;
                // 开通昆仑e享卡页面
                let activateECardUrl = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                // e享卡设置页面
                let eCardSettingsUrl = '/packages/third-my-wallet/pages/wallet-setting/main';
                // 点击我的钱包--->详情页面
                let walletDetailsUrl = '/packages/third-my-wallet/pages/home/<USER>';
                // 掌纹开卡页面跳转判断
                const brushPalmUrl = !status ? activateECardUrl : accountStatus == 3 ? activateECardUrl : eCardSettingsUrl;
                // 员工邀请开卡
                const employeeInvitationCardIssuanceUrl = !status
                    ? activateECardUrl
                    : accountStatus == 3
                    ? activateECardUrl
                    : walletDetailsUrl;
                const EcardUrl = !status ? activateECardUrl : accountStatus == 3 ? activateECardUrl : walletDetailsUrl;
                // 充值页面内需要考虑未开通电子钱包的情况
                // 会员码页面可能存在插件未初始化的情况
                // cz = 充值 dzq = 电子券 fp = 发票 brushPalm = 掌纹开卡 2 = 员工邀请开卡 hym = 会员码 dd = 订单 wd = 我的 yt = 优途小程序跳转 '' = 没有任何跳转(默认值)
                const routerMapThird = {
                    cz: '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                    dzq: '/packages/third-coupon-module/pages/coupon-list/main',
                    fp: '/packages/third-invoice/pages/home/<USER>',
                    brushPalm: `${brushPalmUrl}`,
                    2: `${employeeInvitationCardIssuanceUrl}`,
                    Ecard: EcardUrl,
                    hym: '/packages/third-scan-code-payment/pages/home-code/main',
                    dd: '/pages/thirdHome/main',
                    wd: '/pages/thirdHome/main',
                    yt: '/pages/thirdHome/main',
                    qb: '/pages/thirdHome/main',
                    fissionSharing: '/packages/thirdMarketing/pages/fissionSharing/main',
                    couponCollection: '/packages/thirdMarketing/pages/couponCollection/main',
                    luckyDraw: '/packages/thirdMarketing/pages/luckyDraw/main',
                    undefined: '/pages/thirdHome/main',
                    '': '/pages/thirdHome/main',
                };
                let routerKey = officialAccountParams;
                let routerParams = '';

                if (officialAccountParams?.includes('#;')) {
                    routerKey = officialAccountParams.split('#;')[0];
                    routerParams = officialAccountParams.split('#;')[1];
                }
                console.log(routerMapThird[routerKey], '[officialAccountParams]');
                const urlThird = routerMapThird[routerKey];
                if (urlThird) {
                    let url = routerParams ? urlThird + '?' + routerParams : urlThird;
                    console.log('哪里先执行======111111', url);
                    // uni.reLaunch({ url: url });
                } else {
                    // 微信扫码拿到的活动链接
                    if (officialAccountParams.includes('https')) {
                        await Store.dispatch('memberBaseInfoAction');
                        let memberNo = Store.state.member.memberBaseInfo.memberNo || '';
                        if (officialAccountParams.includes('openId')) {
                            officialAccountParams = officialAccountParams.replace('&openId=', `&openId=${Store.state.openId || ''}`);
                        }
                        // 3.1.3.2版本 先定的是gsmsToken 后来换为 accessToken ，参数名没说要变
                        console.log(`${officialAccountParams}`, '拼接参数后的链接');
                        console.log(`${officialAccountParams}&gsmsToken=${token3}&clientCode=C12&userId=${memberNo}`, '拼接参数后的链接');
                        uni.reLaunch({
                            url: `/packages/web-view/pages/home/<USER>
                                `${officialAccountParams}&gsmsToken=${token3}&clientCode=C12&userId=${memberNo}`,
                            )}`,
                        });
                    } else {
                        console.log('测试跳转首页');
                        uni.reLaunch({
                            url: '/pages/thirdHome/main',
                        });
                    }
                }
                // if (Store.state.staffStationId) {
                //     uni.reLaunch({
                //         url: '/pages/thirdHome/main',
                //     });
                // }
            } else if (index.state.token) {
                console.log('2.0用户跳转====JS');
                // cz = 充值 dzq = 电子券 fp = 发票 brushPalm = 掌纹开卡 ykgl = 油卡管理 dzk = 电子卡 xfjl = 消费记录 yt = 优途
                const routerMap = {
                    cz: '/packages/oil-card/pages/manage-oilcard/main',
                    dzq: '/packages/coupon/pages/home/<USER>',
                    fp: '/packages/invoice-center/pages/home/<USER>',
                    brushPalm: '/pages/home/<USER>',
                    ykgl: '/packages/oil-card/pages/manage-oilcard/main',
                    dzk: '/packages/ecard-apply/pages/apply/main',
                    xfjl: '/packages/my-center/pages/xf-records/main',
                    yt: '/pages/home/<USER>',
                    '': '/pages/home/<USER>',
                };
                const url = routerMap[officialAccountParams];
                if (url) {
                    console.log({ url }, 'url是什么');
                    uni.reLaunch({ url: url });
                } else {
                    // 2.0用户员工邀请开卡
                    if (officialAccountParams == 2) {
                        dispatch('getToken3', 'upgrade');
                    } else if (officialAccountParams.includes('https')) {
                        // 目前没有2.0运营活动，扫3.0运营活动二维码弹窗提示后跳转2.0首页
                        uni.showModal({
                            title: '提示',
                            content: `请先进行系统升级（3.0），再扫码。`,
                            confirmColor: '#FF8200',
                            confirmText: '确认',
                            showCancel: false,
                            success: res => {
                                if (res.confirm) {
                                    uni.reLaunch({
                                        url: `/pages/home/<USER>
                                    });
                                }
                            },
                        });
                        // uni.showToast({
                        //     title: '测试测试测试测试测试测试测试测试测试测试',
                        //     icon: 'none',
                        //     mask: true,
                        // });
                        // setTimeout(() => {
                        //     uni.reLaunch({
                        //         url: `/pages/home/<USER>
                        //     });
                        // }, 2000);
                    } else {
                        uni.reLaunch({
                            url: `/pages/home/<USER>
                        });
                    }
                }
            }
        },
        // 授权位置信息
        setGetlocations(state, params) {
            console.log(params.commit, params.suc, 'commit, suc');
            const grayHomePage = wx.getStorageSync('grayHomePage');
            uni.showModal({
                content: '该功能需要获取您的位置信息，请前往设置授权后使用。',
                showCancel: true,
                confirmColor: grayHomePage ? '#7f7f7f' : '#FF8200',
                success: res => {
                    if (res.confirm) {
                        uni.openSetting({
                            success: opRes => {
                                // 是否同意获取地理位置信息权限
                                if (opRes.authSetting['scope.userLocation']) {
                                    state.authorizedLocationFlag = opRes.authSetting['scope.userLocation'];
                                    console.log(state.authorizedLocationFlag, opRes.authSetting['scope.userLocation'], '999999999');
                                    getLocationInfo(state, params.commit, params.suc, false);
                                }
                            },
                        });
                    }
                },
                fail() {
                    // state.authorizedLocationFlag = false
                },
            });
        },
        // 升级弹窗关闭或打开
        setCloseDialog(state, flag) {
            state.newStationFlag = flag;
        },
        setWxPayIsLoginFlag(state, info) {
            state.wxPayIsLoginFlag = info;
            console.log('setWxPayIsLoginFlag', state.wxPayIsLoginFlag);
        },
    },
    actions: {
        // 初始化位置信息
        initLocation({ state, commit, dispatch }, suc) {
            commit('setIsCustomCalloutIs', true);
            uni.getSetting({
                success: res => {
                    let status = res.authSetting['scope.userLocation']; // 查看位置权限的状态，此处为初次请求，所以值为undefined
                    if (status === undefined) {
                        // 如果是首次授权(undefined)或者之前拒绝授权(false)

                        uni.authorize({
                            scope: 'scope.userLocation',
                            success: res => {
                                wx.hideLoading();
                                getLocationInfo(state, commit, suc, false, dispatch);
                            },
                            fail: err => {
                                // if (state.officialAccountParams) {
                                //   console.log('公众号参数存在时调用')
                                //   dispatch('getToken3')
                                // }
                                if (suc.val === 'thirdLogin') {
                                    // suc.callback()
                                    // dispatch('getToken3');
                                }
                                wx.hideLoading();
                            },
                        });
                    } else if (status === false) {
                        // 首次进入拒绝位置信息授权，再次重新进入小程序 ""
                        if (suc.val === 'thirdLogin') {
                            // dispatch('getToken3', suc);
                        }
                        if (suc.val === 'loactionFlag') {
                            commit('setGetlocations', { commit, suc: suc.callback });
                        }
                        wx.hideLoading();
                    } else {
                        // 没有给位置信息 authorizedLocationFlag = false 反之为true
                        if (!state.authorizedLocationFlag) {
                            // 获取位置信息， suc.val == thirdLogin 执行登录3.0 回调函数
                            getLocationInfo(state, commit, suc, false, dispatch);
                            wx.hideLoading();
                        } else {
                            if (suc.val === 'loactionFlag') {
                                suc.callback();
                            }
                            // 再次登入
                            if (suc.val === 'thirdLogin') {
                                // dispatch('getToken3', suc);
                            }
                        }
                    }
                },
                fail: err => {
                    suc.callback();
                },
            });
        },
        // 更新定位位置
        uploadLocation({ state, commit, dispatch }, suc) {
            console.log('曹鹏威', state.scale);
            commit('setScale', state.scale == 12 ? 11.99 : 12);
            getLocationInfo(state, commit, suc, true);
        },
        // 更换省份 {suc 成功回调，selectIndex 选中的省份索引}
        uploadProvince({ state, commit, dispatch }, params) {
            let res;
            if (params.selectIndex == -1) {
                let index = state.locationArr.findIndex(item => {
                    return item.fullName == state.province;
                });
                res = state.locationArr[index];
            } else {
                res = state.locationArr[params.selectIndex];
            }

            getOnlineDistrict(res.posy, res.posx, state, commit, params.suc);
        },
        // 逆解析
        inverseResolution({ state, commit, dispatch }) {
            getLocation(state.myLat, state.myLon, state, commit);
        },
        // 弹窗广告
        getAdvertisingPopups({ state, commit, dispatch }, obj) {
            let params = {
                cityName: obj.city,
                showLimit: obj.showLimit,
            };
            getPopadvertisData(params)
                .then(res => {
                    if (obj.showLimit) {
                        //   1是我的页面
                        if (res.status === 0 && res.data.image !== '') {
                            commit('setMyPageAdvertisFlag', true);
                            commit('setAdvertisData', res.data);
                            commit('setMyPageAdvertisementFlag', 0);
                        } else {
                            commit('setMyPageAdvertisementFlag', 0);
                        }
                    } else {
                        // 0 是首页
                        if (res.status === 0 && res.data.image !== '') {
                            commit('setAdvertisFlag', true);
                            commit('setAdvertisData', res.data);
                            commit('setAdvertisementFlag', 0);
                        } else {
                            commit('setAdvertisementFlag', 0);
                        }
                    }
                })
                .catch(error => {});
        },
        // 获取轮播图
        getBanner({ state, commit, dispatch }, city) {
            let params = {
                cityName: city,
            };
            getBannerList(params)
                .then(res => {
                    wx.hideLoading();
                    if (res.status === 0) {
                        commit('setBannerListCountry', res.data.countryList);
                        commit('setBannerListCity', res.data.localList);
                    }
                })
                .catch(error => {});
        },
        // 获取后台配置的菜单列表
        getMenuData({ state, commit, dispatch }, city) {
            let params = {
                cityName: city,
            };
            getMenuList(params).then(res => {
                wx.hideLoading();
                if (res.status === 0 && res.data.length > 0) {
                    state.moduleBannerList = [];
                    commit('setModuleBannerListShow', true);
                    let result = [];
                    const chunk = 4;
                    for (var i = 0, j = res.data.length; i < j; i += chunk) {
                        result.push(res.data.slice(i, i + chunk));
                    }
                    result.forEach(item => {
                        commit('setModuleBannerList', { data: item });
                    });
                } else {
                    commit('setModuleBannerListShow', false);
                }
            });
        },
        async getSatationList({ state, commit, dispatch }, suc) {
            let res = await getNearStationList({
                province: state.province,
                longitude: state.myLon,
                latitude: state.myLat,
                pageIndex: page,
                pageSize: pageSize,
            });
            if (JSON.stringify(res.data) !== '[]') {
                let paramsData = {
                    params: res.data,
                    callBack: suc.callBack,
                };
                dispatch('getNewOilStation', paramsData);
            }
        },
        // 获取当前油站是否是新油站
        async getNewOilStation({ state, commit, dispatch }, paramsData) {
            console.log(paramsData, 'paramsData');
            let stationCode = '';
            let distance = '';
            if (paramsData.params.length > 0) {
                stationCode = paramsData.params[0].stationCode;
                distance = paramsData.params[0].distance;
                let params = {
                    hosCode: stationCode,
                    // hosCode: 'AJ17'
                };
                let res = await isStationNew(params);
                if (res.status == 0 && res.data && JSON.stringify(res.data) !== '{}') {
                    // distance 单位米 需要 * 1000
                    if (res.data.onlineType == 1 && Number(distance) < (maxDistance || 0.5) * 1000) {
                        commit('setCloseDialog', true);
                    } else if (res.data.onlineType == '') {
                        commit('setCloseDialog', false);
                        paramsData.callBack();
                    }
                } else if (res.status == -1 || !res.data) {
                    commit('setCloseDialog', false);
                    paramsData.callBack();
                }
            }
        },
        // 判断是否有位置信息
        isPositionInformation({ state, commit, dispatch }, suc) {
            if (!(state.riskManagementLon && state.riskManagementLat)) {
                dispatch('initLocation', suc);
            } else {
                // dispatch('getToken3', suc);
            }
        },
        // 获取3.0token
        async getToken3({ state, commit, dispatch }, val) {
            console.log(Store.state.newUserTag, val, '是新用户注册吗');
            // 未登录2.0的情况下不调用3.0获取token
            if (!index.state.token) return;
            // 是否成功登入3.0
            let isThirdDialog = false;
            let params = {
                extendFiled: JSON.stringify({
                    dfp: '',
                    gps:
                        state.riskManagementLon && state.riskManagementLat ? `(${state.riskManagementLon},${state.riskManagementLat})` : '',
                    gpsProvince: '',
                    gpsCity: '',
                    gpsArea: '',
                }),
                token: index.state.token,
                oldUserGetToken: val == 'upgrade' ? true : false,
            };
            if (Config.baseType == 'qas') {
                params.gsmsUri = 'http://11.54.80.238:30120';
            } else if (Config.baseType == 'sit') {
                // params.gsmsUri = 'http://11.54.86.156:30120'
                params.gsmsUri = 'http://10.30.214.144:20100';
            } else if (Config.baseType == 'gray') {
                params.gsmsUri = '';
            } else if (Config.baseType == 'dev') {
                // params.gsmsUri = 'http://11.54.89.241:30120'
                // params.gsmsUri = 'http://172.30.71.238:20100'
                params.gsmsUri = 'http://10.30.214.136:20100'; // dev
                // params.gsmsUri = 'http://10.30.214.144:20100'; //sit
            }
            let res = await getThirdToken(params);
            commit('setLoginThirdFlag', true);
            if (res.status === 0) {
                // 保存3.0个人信息
                Store.commit('mSetPersonalInformation3', res.data);
                // that.$sKit.layer.dataStorage('tokenInfo',res.data,'setStorage')
                uni.setStorageSync('tokenInfo', res.data);
                let token3 = res.data.accessToken;
                let gsmsToken = res.data.gsmsToken;
                sKit.mpBP.setUserInfo({
                    user_id: res.data.memberNo,
                });

                if (token3) {
                    Store.commit('setToken3', token3);
                    //token失效时的时间戳（未来时间）,客户端可以根据该数值比对当前的时间戳，来判断token是否需要续期。
                    wx.setStorageSync('expiresInV3', res.data.expiresIn);
                    // 用于token过期时 调用刷新token的入参
                    wx.setStorageSync('refreshTokenV3', res.data.refreshToken);
                    //3.0token，只用于sdk访问
                    Store.commit('mSetGsmsToken', gsmsToken);
                    // 登陆成功给标识赋值
                    isThirdDialog = true;
                    // if (!state.officialAccountParams) {
                    //     uni.reLaunch({
                    //         url: `/pages/thirdHome/main`,
                    //     });
                    // }
                }
                // else {
                //     console.log(Store.state.invitingCardOpeningId, 'Store.state.invitingCardOpeningId');
                //     if (Store.state.invitingCardOpeningId == 2 && state.loginThirdFlag) {
                //         Store.commit('setInvitingCardOpeningId', 1);
                //         dispatch('getToken3', 'upgrade');
                //     }
                // }
                // 点击立即升级时触发当前接口
                if (val === 'upgrade') {
                    dispatch('submitUpgrade', res.data.gsmsToken);
                    console.log(res.data, '非白名单获取3.0token获取成功');
                } else {
                    // wxPayIsLoginFlag = false 的时候标识不需要进行登陆
                    console.log(state.officialAccountParams, state.wxPayIsLoginFlag, 'sdvndslkjvndskjvndskvnbdskjvdsk');
                    if (state.officialAccountParams == 'zf' && !state.wxPayIsLoginFlag) {
                        return;
                    }
                    if (state.officialAccountParams == 'dzk') {
                        console.log(Store.state.card.cardList, '油卡列表====');
                        let res = await cardListPost();
                        console.log(res, '油卡信息');
                        if (res.status == 0) {
                            if (res.data.length > 0) {
                                let cardRes = res.data.filter(item => item.cardType == 1);
                                console.log(cardRes, 'cardRes');
                                if (cardRes.length > 0 && cardRes[0].cardType == 1 && !token3) {
                                    Store.dispatch('setOfficialAccountParams', 'ykgl');
                                }
                            }
                        } else {
                            Store.dispatch('setOfficialAccountParams', '');
                        }
                    }
                    // 页面跳转
                    commit('jumpToOfficialAccount', token3);
                }

                console.log(val, 'valvalvalvalvalval');
                if (val != undefined) {
                    if (val.constructor === Object) {
                        val.callback(token3);
                    }
                }
            } else {
                if (val === 'upgrade') {
                    uni.showToast({
                        title: res.info,
                        duration: 2000,
                        icon: 'none',
                    });
                }
            }
            commit('setMaskDialogFlag', false);

            // 返回标识
            return isThirdDialog;
        },
        // 点击确认升级调用接口
        async submitUpgrade({ state, commit, dispatch }, gsmsToken) {
            commit('setCloseDialog', false);
            wx.showLoading({
                mask: true,
                title: '升级中...',
            });
            try {
                let params = {
                    gsmsToken: gsmsToken,
                    // gsmsToken: '',
                };
                await userMigration(params)
                    .then(res => {
                        if (res.status === 0) {
                            dispatch('queryResults');
                        } else {
                            util.showModal(`${res.info}`, true, '#333333', '', '确定', '').then(res => {
                                wx.hideLoading();
                            });
                        }
                    })
                    .catch(err => {
                        wx.hideLoading();
                    });
            } catch (err) {
                wx.hideLoading();
                console.log(err, 'try--catch');
            }
        },
        // 查询迁移结果
        queryResults({ state, commit, dispatch }) {
            getUserMigrateFlag().then(
                res => {
                    if (res.status == 0 && res.data != '' && res.data == 1) {
                        clearInterval(timer);
                        console.log('升级成功=====');
                        // wx.hideLoading();
                        uni.showToast({
                            title: '会员升级成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        const pages = getCurrentPages();
                        const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0]; // 获取栈实例
                        console.log('当前页面的路由post：', pageUrl);
                        setTimeout(() => {
                            uni.reLaunch({
                                url: `/pages/thirdHome/main`,
                            });
                        }, 1000);
                    } else {
                        if (timer != null) {
                            clearInterval(timer);
                        }
                        timer = setInterval(() => {
                            console.log('执行定时器', res.data);
                            dispatch('queryResults');
                        }, 3000);
                    }
                },
                error => {
                    wx.hideLoading();
                    clearInterval(timer);
                    util.showModal('升级状态查询失败', true, '#333333', '', '确定').then(res => {
                        if (res.confirm) {
                            // if (Store.state.newUserTag) {
                            uni.reLaunch({
                                url: `/pages/thirdHome/main`,
                            });
                            // }
                        }
                    });
                },
            );
        },
        async setOfficialAccountParams({ state, commit, dispatch }, info) {
            // 存储外部跳转进来携带的参数
            state.officialAccountParams = info;
            console.log(state.officialAccountParams, 'state.officialAccountParams');
        },
    },
    getters: {
        markerArr: state => state.markerArr,
        // advertisFlag: (state) => state.advertisFlag,
    },
};
