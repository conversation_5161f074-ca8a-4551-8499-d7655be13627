<template>
    <div class="pageMpaas">
        <div :style="{ paddingTop: 10 + Number(statusBarHeight) + 'px' }" class="state-view"></div>
        <div class="primary-btn height40" @click="initKeyBordPlugin">密码插件初始化</div>
        <div class="primary-btn height40" @click="showKeyboard2">打开密码键盘</div>
        <div class="primary-btn height40" @click="initPayPlugin">支付插件初始化</div>
        <div class="primary-btn height40" @click="showpay">打开支付收银台</div>
        <keyboard-plugin></keyboard-plugin>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
export default {
    data() {
        return {
            statusBarHeight: 0,
            passwordKeyboardRef: '',
            payPlugin: '',
        };
    },
    onLoad(option) {
        this.$cnpcBridge.getBarHeight(res => {
            this.statusBarHeight = res;
        });
    },
    onShow() {},
    methods: {
        initKeyBordPlugin() {
            this.$nextTick(async () => {
                // 获取键盘的实例
                let result = await this.$sKit.keyBordPlugin.initRef();
                // console.log('result',result)
                this.$store.commit('setAccountDataPlugin', result);
                this.passwordKeyboardRef = result;
            });
        },
        showKeyboard2() {
            let params = {
                keyboardType: 'number',
                passwordType: 'setup',
                numberPassword: 6,
                setText: '支付',
                mongolianlayer: 1,
            };
            this.passwordKeyboardRef.openKeyboard(
                params,
                () => {
                    this.newPasswordShow = this.passwordKeyboardRef.getFirstLength();
                    this.confirmNewPasswordShow = this.passwordKeyboardRef.getSecondLength();
                    // console.log('pwdLength--',pwdLength)
                    // this.newPasswordShow = pwdLength
                },
                val => {
                    console.log('密码参数：', val);
                    this.newPassword = val.cipherText;
                    this.confirmPassword = val.cipherText;
                },
            );
        },
        async initPayPlugin() {
            this.payPlugin = await this.$sKit.mpaasPayPlugin?.init();
        },
        async showpay() {
            let resparams = {
                stationCode: '1-A4301-C002-S045',
                // stationCode: this.unPaidInfo.stationCode,
            };
            const res = await this.payPlugin.GetBuyTypeList(resparams);
        },
    },
    computed: {},
    components: {},
};
</script>
<style lang="scss" scoped>
.height40 {
    height: 40px;
    margin-top: 20px;
    line-height: 40px;
}
</style>
