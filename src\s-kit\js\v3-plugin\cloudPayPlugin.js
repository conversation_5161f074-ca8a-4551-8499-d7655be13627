import Config from '../third-config.js';
import store from '../../../store/index';
// import {initpay} from "@mpetro/paysdk"
// #ifdef H5-CLOUD
// import payPlugin from "@mpetro/paysdk/dist/cjs"
import LAYER from '../layer';
import { logoutApi } from '@/s-kit/js/v3-http/https3/user';
import * as paysdk from "@petro-gsms/paysdk-js";
console.log(paysdk, 'payPlugin----')
// #endif
let systemInfo = uni.getSystemInfoSync();
console.log('systemInfo---', JSON.stringify(systemInfo))

async function initPayPlugin(result, type = 'number') {
    // #ifdef H5-CLOUD  
    const { gsmsToken } = uni.getStorageSync('tokenInfo') || {};
    if (!gsmsToken) {
        return
    }
    return new Promise(async (resolve, reject) => {
        try {
            // let params = {
            //     token:gsmsToken || '',
            //     data:{},
            //     baseType:Config.baseType,
            //     baseUrl:Config.rpcUrl,
            //     clientCode:Config.clientCode,
            //     // clientCode:"C22",
            //     platform:Config.platform,
            //     // 初始化参数keyboard 密码键盘（银联小程序使用）
            //     // {"appid":"",terminalId:"","baseUrl","地址"，"targetUrl","目标URL"，"width","宽"，"height","高",frameBorder,"边框"}
            //     keyboard:{
            //         appid:Config.mpaasAppId,
            //         terminalId:systemInfo.deviceId || '123331232', //设备id
            //         // baseUrl:`https://partnerkbsdknp.kunlunjyk.com/pages/passwordKeyboardH5/index?url=file://`,
            //         baseUrl:'',
            //         targetUrl:'file://',
            //     }
            // };
            let params = {
                token: gsmsToken || '',
                data: {},
                baseType: Config.baseType,
                clientCode: Config.clientCode,
                platform: Config.platform,
                mpaas: {
                    baseUrl: Config.rpcUrl,
                    appid: Config.mpaasAppId,
                    secretKey: Config.secretKey,
                    signType: 'md5',
                    noRequestBody: false,
                    workspaceid: Config.workspaceid
                }
            };
            console.log('支付插件初始化入参', JSON.stringify(params));

            let res = await paysdk.InitPay(params); //V3.0纯数字键盘
            if (res.code == 'PAY_SUCCESS') {
                console.log('支付插件初始化-succ', res);
                // return res
                resolve(res);
            } else {
                console.log('支付插件初始化-fail', res);
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `错误码：${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        } catch (err) {
            console.log('支付插件初始化-fail', err);
            reject(err);
        }
    });
    // #endif
}
export default {
    initPayPlugin,
};