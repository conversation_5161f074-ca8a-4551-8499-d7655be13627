<template>
    <div>
        <div>
            <!-- <FrostedGlass @change="getCardList"></FrostedGlass> -->
        </div>
        <div class="my-center">
            <div class="pure_top" :style="{ backgroundImage: 'url(' + naviBg + ')' }">
                <u-navbar
                    :background="{ background: 'none' }"
                    :back-icon-size="40"
                    :height="44"
                    :back-icon-color="pageConfig.titleColor.backIconColor"
                    :title-color="pageConfig.titleColor.color"
                    back-text="我的"
                    :back-text-style="pageConfig.titleStyle"
                    :border-bottom="false"
                ></u-navbar>
                <div class="header-view" @click="toSetting" v-if="isLogin">
                    <div class="user-info">
                        <div class="user-header" @click.stop="getUserInfoHandel">
                            <img v-if="isLogin" class="avt-img" :src="registerLoginInformation.headImg" />
                        </div>
                        <div class="userphone" v-if="isLogin" @click.stop="getUserInfoHandel">
                            <div class="userName" v-if="registerLoginInformation.nickName">{{
                                registerLoginInformation.nickName || '微信用户'
                            }}</div>
                            <div>{{ registerLoginInformation.phone | phoneFilter }}</div>
                        </div>
                    </div>
                    <u-icon name="arrow-right" color="#fff"></u-icon>
                </div>
                <get-phone-number @change="getPhoneNumberChange" btnType="login" v-if="!isLogin">
                    <template>
                        <div class="notLoggedIn">
                            <div class="notLoggedInImg">
                                <img src="@/static/img/cnpc-logo.png" alt />
                            </div>
                            <div class="text">登录/注册</div>
                            <div class="arrow">
                                <u-icon name="arrow-right" color="#FFF" size="28"></u-icon>
                            </div>
                        </div>
                    </template>
                </get-phone-number>
                <!-- <div class="card-content" @click="this.priceIsEncryption = !this.priceIsEncryption">
          <div class="card-item line-right">
            <div class="card-price">{{handleComputePrice(xfjlData.lastMonth, false)}}</div>
            <div class="card-title">上月消费(元)</div>
          </div>
          <div class="card-item line-right">
            <div class="card-price">{{handleComputePrice(xfjlData.currentMonth, false)}}</div>
            <div class="card-title">本月消费(元)</div>
          </div>
          <div class="card-item">
            <div class="card-price">{{handleComputePrice(xfjlData.favourable, false)}}</div>
            <div class="card-title">年度优惠(元)</div>
          </div>
        </div>-->
                <div class="oil-content">
                    <div class="oil-wrap">
                        <div class="oil-flex">
                            <div class="oil-title">我的油卡</div>
                            <div class="oil-manage" v-if="cardList.length > 0" @click="manageClick">油卡管理</div>
                        </div>
                        <get-phone-number @change="getPhoneNumberChange" btnType="recharge-add" v-if="cardList.length <= 0">
                            <div class="oil-add">
                                <img src="@/static/add.png" alt class="add-img" />
                                <div class="add-text">添加我的油卡</div>
                                <div class="add-tip">免到油站直接线上充值</div>
                            </div>
                        </get-phone-number>
                        <template v-else>
                            <swiper
                                class="swiper"
                                :indicator-dots="indicatorDots"
                                :autoplay="autoplay"
                                :interval="interval"
                                :duration="300"
                                :current="swiperIndex"
                                :next-margin="nextMargin"
                                :previous-margin="previousMargin"
                                @change="handleChangeSwiperItem"
                            >
                                <swiper-item v-for="(item, index) in cardList" :key="index" class="swiper-box">
                                    <!-- item.cardType == 1 ? `url(${item.cardTypeImg})` : '' -->
                                    <div
                                        class="manage-card"
                                        :style="{
                                            backgroundImage:
                                                item.cardTypeImg && item.cardType == 1
                                                    ? `url(${item.cardTypeImg})`
                                                    : 'url(' + (item.cardType == 1 ? naviBgdianzika : naviBgshitika) + ')',
                                            marginRight: swiperMargin.marginRight,
                                            borderRadius: item.cardTypeImg && item.cardType == 1 ? '8px' : '',
                                        }"
                                    >
                                        <div class="header-div">
                                            <div class="info-div">
                                                <div class="logo-div">
                                                    <img src="@/static/cnpc-logo.png" alt mode="widthFix" />
                                                </div>
                                                <div class="right-div">
                                                    <div class="top-view">
                                                        <span class="top-div">{{
                                                            item.cardType == 1 ? '中国石油电子油卡' : '中国石油加油卡'
                                                        }}</span>
                                                        <span class="top-icon">移动支付</span>
                                                        <span class="top-icon" v-if="item.isDefaultCard">默认卡</span>
                                                    </div>

                                                    <div class="name-div" @click="eyebtn($event, index)">
                                                        <span class="name" v-if="!item.activeed">{{ item.userNameShow }}</span>
                                                        <span v-else class="name">{{ item.userName }}</span>
                                                        <span class="card-number" v-if="!item.activeed">{{ item.cardNoShow }}</span>
                                                        <span v-else class="card-number">{{ item.cardNo }}</span>

                                                        <img
                                                            src="@/static/white-eye-close.png"
                                                            v-if="!item.activeed"
                                                            alt
                                                            class="eye-block-iocn1"
                                                            mode="widthFix"
                                                            :data-item="item"
                                                        />
                                                        <img
                                                            src="@/static/white-eye-open.png"
                                                            v-if="item.activeed"
                                                            alt
                                                            class="eye-block-iocn1"
                                                            mode="widthFix"
                                                            :data-item="item"
                                                        />
                                                    </div>
                                                    <div class="address-text">{{ item.allAddress }}</div>
                                                </div>
                                            </div>
                                            <div class="bottom-div">
                                                <div class="flex-row">
                                                    <div class="flex-item" v-if="item.cardType == 0">
                                                        <div class="row-div">
                                                            <span class="row-left">圈存金</span>
                                                            <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                            <template v-else>
                                                                <span class="row-price" v-if="item.activeed">¥</span>
                                                                <span class="row-text">{{ item.cardBalance }}</span>
                                                            </template>
                                                        </div>
                                                        <div class="row-div">
                                                            <span class="row-left">积分</span>
                                                            <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                            <span class="row-text" style="margin-left: 10px" v-else>{{
                                                                item.cardLoyaltyBalance
                                                            }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="flex-item">
                                                        <div class="row-div">
                                                            <span class="row-left">备用金</span>
                                                            <span class="row-price" v-if="item.activeed">¥</span>
                                                            <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                            <span class="row-text" v-else>{{ item.balance }}</span>
                                                        </div>
                                                        <div class="row-div">
                                                            <span class="row-left">积分</span>
                                                            <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                            <span class="row-text" style="margin-left: 10px" v-else>{{
                                                                item.loyaltyBalance
                                                            }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row-div">
                                                    <span class="row-left" v-if="item.cardExpiredTime"
                                                        >有效期至：{{ item.cardExpiredTime }}</span
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </swiper-item>
                            </swiper>
                        </template>
                        <div class="oil-list">
                            <get-phone-number class="oil-item-wrap" @change="getPhoneNumberChange" btnType="recharge">
                                <div class="oil-item">
                                    <img src="@/static/my-oil.png" alt class="icon-img" mode="aspectFit" />
                                    <div class="item-tetx">油卡查询</div>
                                </div>
                            </get-phone-number>
                            <get-phone-number class="oil-item-wrap" @change="getPhoneNumberChange" btnType="coupon">
                                <div class="oil-item">
                                    <img src="@/static/my-coupon.png" alt class="icon-img" mode="aspectFit" />
                                    <div class="item-tetx">电子券</div>
                                </div>
                            </get-phone-number>
                            <get-phone-number class="oil-item-wrap" @change="getPhoneNumberChange" btnType="invoice">
                                <div class="oil-item">
                                    <img src="@/static/my-invoice.png" alt class="icon-img" mode="aspectFit" />
                                    <div class="item-tetx">我的发票</div>
                                </div>
                            </get-phone-number>
                            <get-phone-number class="oil-item-wrap" @change="getPhoneNumberChange" btnType="consume">
                                <div class="oil-item">
                                    <img src="@/static/my-costom.png" alt class="icon-img" mode="aspectFit" />
                                    <div class="item-tetx">消费记录</div>
                                </div>
                            </get-phone-number>
                            <get-phone-number class="oil-item-wrap" @change="getPhoneNumberChange" btnType="ETCsigningEntry">
                                <div class="oil-item">
                                    <img src="@/static/ETC.png" alt class="icon-img" mode="aspectFit" />
                                    <div class="item-tetx">ETC签约</div>
                                </div>
                            </get-phone-number>
                        </div>
                    </div>
                </div>
            </div>

            <div class="oil-kefu">客服热线 956100</div>
        </div>
        <AdvertisingPopups v-if="myPageAdvertisFlag" @closeEvent="closeEvent"></AdvertisingPopups>
        <Privacy v-if="privacyIsShow"></Privacy>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import GetPhoneNumber from '@/components/login/get-phone-number';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import AdvertisingPopups from '@/components/advertisingPopups/advertisingPopups.vue';
import Privacy from '@/components/privacy/main.vue';
import { mapState, mapGetters } from 'vuex';
import { getOtherUserInfoPost, saveUserInfoApi } from '@/api/my-center';
import projectConfig from '../../../../../project.config';
import {
    getMonthConsume,
    cardListPost,
    // getCardDetails,
    getCardDetailApi,
    // getUnreadMsgCnt
} from '@/api/home.js';
export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            projectConfig,
            navH: 64,
            naviBg: '',
            naviBgshitika: '',
            naviBgdianzika: '',
            xfjlData: '',
            // cardList: [],
            // oilCardRes:[],
            msgNum: 0,
            priceIsEncryption: true, // 价格是否为加密
            swiperIndex: 0,
            indicatorDots: false,
            autoplay: false,
            userAvatarUrl: '', //用户头像信息
            nextMargin: '0',
            previousMargin: '0',
            headImg:
                'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
            codeLoginFlag: false,
            codeLoginObj: {},
        };
    },
    async onLoad() {
        if (this.isLogin && this.myPageAdvertisementFlag) {
            await this.$store.dispatch('getAdvertisingPopups', { city: this.city, showLimit: 1 });
            this.$store.commit('setMyPageAdvertisementFlag', 0);
        }
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/bg.png', 'base64');
        // this.naviBgdianzika = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/dianzikabg.png', 'base64');
        // this.naviBgshitika = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/shitikabg.png', 'base64');
        this.loadImages();
    },
    async onShow() {
        // 判断当前微信用户是否的 微信昵称是否存在
        if (this.isLogin) {
            if (this.registerLoginInformation.nickName === '' || this.registerLoginInformation.nickName === undefined) {
                this.registerLoginInformation.nickName = '微信用户';
            }
        }
        if (this.isLogin) {
            await this.initPage();
        }
        if (this.codeLoginFlag) {
            this.getPhoneNumberChange(this.codeLoginObj);
        }
    },
    async onUnload() {
        // 我的页面返回关闭弹窗
        this.closeEvent();
        this.$store.commit('setMyPageAdvertisementFlag', 0);
    },
    methods: {
        async loadImages() {
            const imageMap = {
                naviBg: '/uniapp/uni-mall/static/img/bg.png',
                naviBgdianzika: '/uniapp/uni-mall/static/img/dianzikabg.png',
                naviBgshitika: '/uniapp/uni-mall/static/img/shitikabg.png',
            };
            for (const [variable, url] of Object.entries(imageMap)) {
                const imageUrl = this.projectConfig.baseImgUrl + url;
                const base64 = await this.fetchAndConvertToBase64(imageUrl);
                this[variable] = base64;
            }
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        // 获取卡列表
        async getCardList() {
            console.log('执行了吗');
            await this.$store.dispatch('card/getAllCardList');
            this.initPage();
        },
        async initPage() {
            // let res = await getUnreadMsgCnt();
            // this.msgNum = res.data;
            // let resa = await getMonthConsume()
            // this.xfjlData = resa.data
            if (this.cardList.length <= 1) {
                this.nextMargin = 0;
            } else {
                this.nextMargin = '20rpx';
            }
            // oilCardResTmp[index].activeed = false
            // console.log('oilCardResTmp',oilCardResTmp);

            // let oilCard;
            // if(index !=-1 ){
            // 		oilCard.data.cardType = oilCardRes.data[index].cardType;
            // }else {
            // 		oilCard = await getCardDetailApi({cardNo: oilCardRes.data[0].cardNo})
            // 		oilCard.data.cardType = oilCardRes.data[0].cardType;
            // }

            // console.log('oilCard',oilCard.data)
            // for (let i = 0; i < oilCardRes.data.length; i++) {
            // 	console.log('oilCard',oilCard)
            // 	// oilCardRes.data[i] = oilCard.data
            // 	oilCardRes.data[i].afterNum = oilCard.data.cardNo.substring(oilCard.data.cardNo.length - 4, oilCard.data.cardNo.length);
            // 	oilCardRes.data[i].cardType = oilCardResTmp.data[i].cardType
            // }

            // this.cardList = oilCard.data
            // }
            // console.log('cardList',this.cardList )
        },
        // current 改变时会触发
        async handleChangeSwiperItem(e) {
            const { current, source } = e.detail;
            if (source == 'touch') {
                //用户触摸引起
                this.swiperIndex = current;
                if (current == this.cardList.length - 1) {
                    this.nextMargin = 0;
                } else {
                    this.nextMargin = '20rpx';
                }
                if (current == 0) {
                    this.previousMargin = 0;
                } else {
                    this.previousMargin = '30rpx';
                }
            }
        },
        // 是否隐藏点击事件
        async eyebtn(e, index) {
            this.$store.dispatch('card/setCardActiveed', index);
        },
        manageClick() {
            uni.navigateTo({
                url: '/packages/oil-card/pages/manage-oilcard/main',
            });
        },
        // get-phone-number组件点击事件
        async getPhoneNumberChange(e) {
            this.codeLoginFlag = false;
            if (e.isLogin) {
                if (e.btnType == 'recharge') {
                    if (this.cardList.length > 0) {
                        uni.navigateTo({
                            url: '/packages/oil-card/pages/manage-oilcard/main',
                        });
                    } else {
                        uni.showModal({
                            title: '温馨提示',
                            content: '您还未绑定加油卡，请绑卡后在操作',
                            cancelText: '暂不需要',
                            confirmText: '马上添加',
                            cancelColor: '#576B95',
                            success(res) {
                                if (res.confirm) {
                                    console.log('用户点击确定');
                                    uni.navigateTo({
                                        url: '/packages/oil-card/pages/add-oilcard/main',
                                    });
                                } else if (res.cancel) {
                                    // console.log('用户点击取消')
                                }
                            },
                        });
                    }
                } else if (e.btnType == 'recharge-add') {
                    if (this.cardList.length > 0) return;
                    uni.navigateTo({
                        url: '/packages/oil-card/pages/add-oilcard/main',
                    });
                } else if (e.btnType == 'order') {
                    uni.navigateTo({
                        url: '/packages/order/pages/order/main?state=' + 0,
                    });
                } else if (e.btnType == 'normal-order') {
                    uni.navigateTo({
                        url: '/packages/order/pages/order/main?state=' + 1,
                    });
                } else if (e.btnType == 'suc-order') {
                    uni.navigateTo({
                        url: '/packages/order/pages/order/main?state=' + 2,
                    });
                } else if (e.btnType == 'setting') {
                    uni.navigateTo({
                        url: '/packages/setting/pages/home/<USER>',
                    });
                } else if (e.btnType == 'invoice') {
                    uni.navigateTo({
                        url: '/packages/invoice-center/pages/home/<USER>',
                    });
                } else if (e.btnType == 'coupon') {
                    uni.navigateTo({
                        url: '/packages/coupon/pages/home/<USER>',
                    });
                } else if (e.btnType == 'webView-etc') {
                    this.webViewLinkFun();
                } else if (e.btnType == 'car-manage') {
                    await this.$store.dispatch('uploadVenicleList');
                    uni.navigateTo({
                        url: '/packages/venicle-set/pages/home/<USER>',
                    });
                } else if (e.btnType == 'msg') {
                    uni.navigateTo({
                        url: '/packages/message/pages/home/<USER>',
                    });
                } else if (e.btnType == 'consume') {
                    uni.navigateTo({
                        url: '/packages/my-center/pages/xf-records/main',
                    });
                } else if (e.btnType == 'ETCsigningEntry') {
                    uni.navigateTo({
                        url: '/packages/my-center/pages/etcSigning/main',
                        // url: '/packages/my-center/pages/newETCSigning/main'
                        // url: '/packages/my-center/pages/terminationByETC/main'
                    });
                    //  uni.navigateTo({
                    //   url: `/packages/web-view/pages/home/<USER>
                    //     'https://miniprotest.95504.net/word/'
                    //     )}`,
                    // });
                }
                // else if (e.btnType == 'login') {
                //   uni.navigateTo({
                //     url: '/packages/avatarNickname/pages/home/<USER>'
                //   })
                // }
                else {
                    // 刷新当前页面
                    if (!e.lastLogin) {
                        await this.initPage();
                    }
                }
            }
        },

        /**
         * 方法
         */
        //webview跳转地址方法
        async webViewLinkFun() {
            const { appId, webViewUrl } = this.pageConfig;
            const { data } = await getOtherUserInfoPost({
                appNo: appId,
            });
            let linkUrl = webViewUrl + '?' + this.queryStrFun(data);
            uni.navigateTo({
                url: `/packages/web-view/pages/home/<USER>
            });
        },
        //拼接查询字符串方法
        queryStrFun(obj) {
            let str = '';
            for (let key in obj) {
                str += `&${key}=${obj[key]}`;
            }
            return str ? str.substring(1) : '';
        },
        //
        handleComputePrice(price, isUseSign, noCardTips = 0) {
            const sign = isUseSign ? '￥' : '';
            const cardList = this.cardList;
            // return cardList.length > 0 ? sign + ( this.priceIsEncryption ? '***' :  price ) : noCardTips
            return sign + (this.priceIsEncryption ? '***' : price);
        },
        getUserInfoHandel() {
            uni.navigateTo({
                url: '/packages/avatarNickname/pages/home/<USER>',
            });
            // this.avatarNicknameFlag = true
            // if(this.registerLoginInformation.headImg == 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' && this.registerLoginInformation.nickName == '微信用户'){
            // uni.getUserProfile({
            // 	desc: '仅用于头像和昵称的展示',
            // 	success: async (e) => {
            // 		this.registerLoginInformation.headImg = e.userInfo.avatarUrl
            // 		this.registerLoginInformation.nickName = e.userInfo.nickName
            //
            // 	}
            // })
            // }
        },
        toSetting() {
            uni.navigateTo({
                url: '/packages/setting/pages/home/<USER>',
            });
        },
        closeEvent() {
            this.$store.commit('setMyPageAdvertisementFlag', 0);
            this.$store.commit('setMyPageAdvertisFlag', false);
        },
    },
    filters: {
        phoneFilter(val) {
            let reg = /^(.{3}).*(.{4})$/;
            if (val) {
                return val.replace(reg, '$1****$2');
            } else {
                return '';
            }
        },
    },
    computed: {
        ...mapGetters(['registerLoginInformation', 'isLogin', 'cardList', 'isHaveEntityCard', 'cardTopinfo']),
        ...mapState({
            lat: state => state.location.lat, // 纬度
            lon: state => state.location.lon, // 经度
            venicleList: state => state.venicle.venicleList, // 车辆列表
            myPageAdvertisFlag: state => state.location.myPageAdvertisFlag,
            city: state => state.location.city, // 城市
            myPageAdvertisementFlag: state => state.location.myPageAdvertisementFlag, // 城市
            privacyIsShow: state => state.privacy.privacyIsShow, // 展示隐私协议弹窗
        }),
        swiperMargin() {
            if (this.cardList.length === 2) {
                return {
                    marginRight: '15rpx',
                };
            } else {
                return {
                    marginRight: 0,
                };
            }
        },
    },
    components: {
        GetPhoneNumber,
        FrostedGlass,
        AdvertisingPopups,
        Privacy,
    },
    watch: {
        '$store.state.isLogin': {
            handler(val, oldVal) {
                if (val && this.myPageAdvertisementFlag) {
                    this.$store.dispatch('getAdvertisingPopups', { city: this.city, showLimit: 1 });
                    this.$store.commit('setMyPageAdvertisementFlag', 0);
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
$text-color: #fff;
$text-size-middle: 15px;

.my-center {
    height: 100vh;
    width: 100%;
    background-color: #f6f6f6;

    .pure_top {
        width: 100%;
        // background-size: 375px 200px;
        // background-position: bottom right;
        background-size: 375px 218px;
        background-repeat: no-repeat;
        padding: 20px 12px;
        // overflow: hidden;
        // .card-view {
        // 	border-top-left-radius: 5px;
        // 	border-top-right-radius: 5px;
        // 	margin-left: 15px;
        // 	width: 345px;
        // 	margin-top: 15px;
        // 	height: 55px;
        // 	background-color: #333333;
        // 	display: flex;
        // 	align-items: center;
        // 	justify-content: space-between;
        // 	.card-title {
        // 		color: #FFFFFF;
        // 		font-size: 16px;
        // 		margin-left: 10px;
        // 	}
        // 	.card-price-wrp {
        // 		display: flex;
        // 		align-items: flex-end;
        // 		.card-price {
        // 			color: #FFFFFF;
        // 			font-size: 14px;
        // 			margin-right: 10px;
        // 		}
        // 		.card-eye-iocn {
        // 			position: relative;
        // 				top: -3px;
        // 			width: 15px;
        // 			height: 7px;
        // 			margin-right: 10px;
        // 		}
        // 	}

        // }
        .header-view {
            display: flex;
            align-items: center;
            padding-bottom: 16px;
            justify-content: space-between;

            // .function-view {
            // 	display: flex;
            // 	.message-view {
            // 		position: relative;
            // 		.message-num {
            // 			position: absolute;
            // 			right: 0px;
            // 			top: 0px;
            // 			line-height: 16px;
            // 			background-color: red;
            // 			color: #FFFFFF;
            // 			min-width: 16px;
            // 			text-align: center;
            // 			font-size: 12px;
            // 			border-radius: 8px;
            // 		}
            // 		.message-icon {
            // 			width: 22px;
            // 			height: 22px;
            // 			padding: 7.5px;
            // 		}
            // 	}

            // 	.kefu-icon {
            // 		width: 22px;
            // 		height: 22px;
            // 		padding: 7.5px;
            // 		margin-right: 7.5px;
            // 	}
            // }
            .user-info {
                flex: 1;
                display: flex;
                align-items: center;
                // align-items: flex-end;

                // .avt-img {
                // 	width: 100rpx;
                // 	height: 100rpx;
                // }

                .user-header {
                    width: 100rpx;
                    height: 100rpx;
                    overflow: hidden;
                    border-radius: 4px;
                    .avt-img {
                        width: 100rpx;
                        height: 100rpx;
                    }
                }

                .userphone {
                    font-size: 14px;
                    color: #ffffff;
                    margin-left: 10px;
                    .userName {
                        font-size: 30rpx;
                        padding-bottom: 16rpx;
                        color: #ffffff;
                        text-align: left;
                    }
                }
            }
        }
        .card-content {
            background: #ffffff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            height: 74px;
            overflow: hidden;
            .card-item {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .card-price {
                    font-size: 19px;
                    color: #333333;
                }
                .card-title {
                    font-size: 11px;
                    color: #888888;
                    letter-spacing: 0.3px;
                    text-align: center;
                }
            }
            .line-right {
                position: relative;
                &.line-right:before {
                    content: ' ';
                    position: absolute;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    width: 1px;
                    border-right: 1px solid #eee;
                    -webkit-transform-origin: 0 0;
                    transform-origin: 0 0;
                    -webkit-transform: scaleX(0.5);
                    transform: scaleX(0.5);
                    z-index: 9;
                }
            }
        }
        .notLoggedIn {
            display: flex;
            align-items: center;
            width: 100%;
            height: 50px;
            color: #fff;
            margin-bottom: 16px;
            .notLoggedInImg {
                width: 40px;
                height: 40px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .text {
                margin-left: 10px;
                font-size: 16px;
            }
            .arrow {
                margin-left: 10px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
    .oil-content {
        background: #ffffff;
        border-radius: 8px;
        // margin: 0 12px;
        box-sizing: border-box;
        .oil-wrap {
            padding: 12px;
            position: relative;
        }
        .oil-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .oil-title {
            font-size: 16px;
            color: #333333;
        }
        .oil-manage {
            font-size: 16px;
            color: #909090;
        }
        .oil-add {
            width: 100%;
            padding: 20px 0;
            border-radius: 8px;
            margin: 10px auto 0;
            background: #fef2ea;
            border: 1px solid rgba(249, 103, 2, 0.13);
            text-align: center;
            .add-img {
                width: 28px;
                height: 28px;
                margin-bottom: 8px;
            }
            .add-text {
                font-size: 14px;
                color: #f96702;
                margin-bottom: 4px;
            }
            .add-tip {
                font-size: 12px;
                color: #999999;
            }
        }
        .oil-list {
            padding-top: 20px;
            display: flex;
            justify-content: space-between;
            text-align: center;
            flex-wrap: wrap;
            align-content: flex-start;
            .oil-item-wrap {
                flex: 0 0 25%;
                .oil-item {
                    margin-bottom: 20px;
                    .icon-img {
                        width: 24px;
                        height: 24px;
                        margin-bottom: 8px;
                    }
                    .item-tetx {
                        font-size: 12px;
                        color: #333333;
                    }
                }
            }
        }
    }
    .swiper {
        margin: 10px 0 0;
        width: 100%;
        height: 315rpx;
        .swiper-box {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 315rpx;
        }
    }
    .manage-card {
        width: 645rpx;
        height: 100%;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin: 0rpx 15rpx 0 0;
        z-index: 1;

        .header-div {
            width: 100%;
            padding: 27rpx 24rpx 24rpx;

            .info-div {
                display: flex;
                align-items: center;

                .logo-div {
                    img {
                        width: 70rpx;
                        height: 70rpx;
                    }
                }

                .right-div {
                    margin-left: 12px;
                    .top-view {
                        display: flex;
                        align-items: center;
                        .top-div {
                            font-size: 14px;
                            color: #fff;
                            vertical-align: center;
                        }

                        .top-icon {
                            background: rgba(0, 0, 0, 0.17);
                            height: 28rpx;
                            line-height: 28rpx;
                            color: #fff;
                            font-size: 9px;
                            text-align: center;
                            vertical-align: center;
                            border-radius: 2px;
                            margin-left: 4px;
                            padding: 0 7rpx;
                        }
                    }

                    .name-div {
                        color: #fff;
                        font-size: 22rpx;
                        line-height: 20px;
                        opacity: 0.8;
                        display: flex;
                        align-items: center;
                        .name {
                            position: relative;
                            margin-right: 16rpx;

                            &.name:before {
                                content: '';
                                position: absolute;
                                top: 0;
                                right: -16rpx;
                                bottom: 0;
                                width: 1px;
                                border-right: 1px solid #ebedf0;
                                -webkit-transform-origin: 0 0;
                                transform-origin: 0 0;
                                -webkit-transform: scaleX(0.5);
                                transform: scaleX(0.5);
                                z-index: 9;
                            }
                        }

                        .card-number {
                            margin-left: 16rpx;
                            display: inline-block;
                            vertical-align: baseline;
                        }
                        .eye-block-iocn1 {
                            width: 17px;
                            height: auto;
                            padding: 0 5px;
                            display: inline-block;
                            vertical-align: baseline;
                        }
                    }
                    .address-text {
                        font-size: 12px;
                        color: #fff;
                    }
                }
            }
        }

        .bottom-div {
            margin-top: 20rpx;
            .flex-row {
                display: flex;
                align-items: center;
                .flex-item {
                    width: 50%;
                }
            }

            .row-div {
                padding: 0 0 8rpx;
                display: flex;
                align-items: center;
                height: 25px;
                overflow: hidden;
                .row-div:last-child {
                    padding: 0;
                }
                .row-left {
                    font-size: 22rpx;
                    opacity: 0.8;
                    color: #fff;
                    margin-right: 8rpx;
                    display: inline-block;
                    vertical-align: baseline;
                }

                .row-price {
                    color: #fff;
                    font-size: 24rpx;
                    margin-right: 8rpx;
                    display: inline-block;
                    vertical-align: baseline;
                }

                .row-text {
                    font-size: 36rpx;
                    color: #fff;
                    display: inline-block;
                    vertical-align: baseline;
                }
                .row-text-center {
                    padding-top: 9px;
                }
            }
        }
    }
    .oil-kefu {
        font-size: 12px;
        color: #999999;
        text-align: center;
        position: relative;
        margin-top: 25px;
        &.oil-kefu:before {
            content: ' ';
            position: absolute;
            left: 25%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }

        &.oil-kefu:after {
            content: ' ';
            position: absolute;
            right: 25%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }
    }
    .xfjl-view {
        background-color: #ffffff;
        display: flex;
        align-items: center;
        width: 375px;
        height: 70px;
        .xfjl-line {
            width: 1px;
            height: 50px;
            background-color: #dcdddd;
        }
        .xfjl-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .xfjl-title {
                font-size: 14px;
                color: #333333;
                line-height: 20px;
            }
            .xfjl-price {
                color: $btn-color;
                line-height: 22px;
                margin-top: 6px;
                font-size: 19px;
                font-weight: 700;
            }
        }
    }
    .header-content {
        width: 100%;

        .top-div {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 15px 10px;
            box-sizing: border-box;

            .header-left {
                .avt-img {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                }

                .userinfo {
                    display: inline-block;
                    vertical-align: middle;
                    color: $text-color;
                    font-size: $text-size-middle;
                    margin-left: 10px;
                    text-align: left;

                    .userphone {
                    }

                    .userauth {
                        background: #ffffff;
                        border-radius: 15px;
                        padding: 2px 8px;
                        font-size: 12px;
                        color: #118820;
                    }
                }
            }

            .header-right {
                width: 90px;
                height: 28px;

                .service-img {
                    width: 90px;
                    height: 28px;
                }
            }
        }
    }

    .section-top {
        width: 92%;
        margin: -40px auto 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        &-item {
            width: 335rpx;
            padding: 15px 5px;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 5px;
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            &.card {
                box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
            }

            .item-icon {
                width: 50px;
                height: 44px;
                margin-right: 5px;
            }

            .item-info {
                flex: 1;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;

                .item-title {
                    font-size: 16px;
                    color: #222;
                    font-weight: 600;
                    overflow: hidden;
                }

                .item-content {
                    font-size: 12px;
                    color: #909090;
                    overflow: hidden;
                    margin-top: 3px;
                }
            }
        }
    }

    .section {
        width: 100%;
        position: relative;
    }

    .section-list {
        margin: 0 auto;
        margin-top: 10px;

        z-index: 1000;
        margin-left: 10px;
        margin-right: 10px;

        .table-view-list {
            // box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
            margin-bottom: 8px;
            background: #fff;
            position: relative;
            overflow: hidden;
            border-radius: 5px;
            .table-view-list-text {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                padding: 16px 0px;
                margin-left: 15px;
                margin-right: 15px;
                // border-bottom: 1px solid #f3f3f3;
                text-align: left;

                .left {
                    .left-icon {
                        width: 20px;
                        height: 20px;
                    }
                }

                .center {
                    padding: 0 8px;
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .center-txt {
                        font-size: 15px;
                        color: #222222;
                        //padding-bottom: 3px;
                    }
                    .center-carNo {
                        font-size: 13px;
                        color: #999999;
                    }
                }

                .table-view-list-arrow {
                }

                .table-view-list-arrow:after {
                    content: ' ';
                    display: inline-block;
                    height: 5px;
                    width: 5px;
                    border-width: 1px 1px 0 0;
                    border-color: #888888;
                    border-style: solid;
                    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
                }
            }
        }
    }
}

.order-state-view {
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    background-color: #ffffff;
    width: 345px;
    border-radius: 5px;
    margin-left: 15px;
    margin-top: 10px;
    margin-bottom: 10px;

    .order-state-title {
        display: flex;
        align-items: center;
        height: 44px;

        .state-title {
            color: #222222;
            font-size: 16px;
            margin-left: 10px;
            font-weight: 700;
            flex: 1;
        }

        .title-detail-view {
            display: flex;
            align-items: center;
            margin-right: 10px;

            .title-detail-text {
                color: #333333;
                font-size: 12px;
            }

            .title-detail-arrow {
                margin-left: 2px;
                padding-bottom: 2px;
            }

            .title-detail-arrow:after {
                content: ' ';
                display: inline-block;
                height: 6px;
                width: 6px;
                border-width: 1px 1px 0 0;
                border-color: #888888;
                border-style: solid;
                transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
            }
        }
    }

    .order-state-bottom {
        display: flex;

        .order-state-item {
            display: flex;
            width: calc(345px / 3);
            height: 76px;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .order-state-icon {
                height: 24px;
                width: 24px;
            }

            .order-state-text {
                margin-top: 5px;
                color: #333333;
                font-size: 12px;
            }
        }
    }
}

.noborder {
    border: none !important;
}

.ad-img {
    display: block;
    width: 92%;
    margin: 8px auto 0;
}
</style>
