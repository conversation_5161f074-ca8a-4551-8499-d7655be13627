<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 订单详情页面 -->
        <div class="select-region-class">
            <zj-navbar title="订单明细"></zj-navbar>
            <div class="content mh-0">
                <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="content-view">
                        <div class="commodity-order-box">
                            <div class="gas-station">
                                <img class="gas-station-img" src="../../image/cheer.png" alt />
                                <div class="gas-station-text">
                                    {{ pageParams.stationName || '' }}
                                </div>
                            </div>
                            <div class="commodity-order" v-for="(item, index) in orderDetail.productList" :key="index">
                                <div class="commodity">
                                    <img class="detail-left-img" v-if="item.productType == 1" src="../../image/order_oils.png" alt />
                                    <img class="commodity-img" v-else :src="item.imgUrl" alt />
                                    <div class="commodity-title">{{ item.productName || '' }}</div>
                                    <div class="commodity-price font-style">
                                        <div class="unit-price">&yen;{{ item.unitPrice || '' }}</div>
                                        <div class="num">
                                            {{ 'x ' + (item.productQty || '') + (item.productUnit || '') }}
                                        </div>
                                    </div>
                                </div>
                                <div class="information">
                                    <div class="information-data">
                                        <div class="information-data-left">商品编码</div>
                                        <div class="information-data-right">
                                            {{ item.productNo || '' }}
                                            <div class="copy" @click="copyProductNo(item.productNo)"> 复制 </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="information-data fl-sp-end">
                                <div class="information-data-right font-style">
                                    实付总额：&yen;{{ pageParams.actualPayTotalAmount || 0 }}
                                </div>
                            </div>
                        </div>
                        <div class="details-price">
                            <div class="details-price-item">
                                <div class="item-left">创建时间</div>
                                <div class="item-right">
                                    {{ pageParams.createTime || '' }}
                                </div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">订单编号</div>
                                <div class="item-right">
                                    <div class="item-right-text">{{ pageParams.orderNo || '' }}</div>
                                    <div class="copy" @click="copyProductNo(pageParams.orderNo)">复制</div>
                                </div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">支付渠道</div>
                                <div class="item-right">{{ orderDetail.payChannel || '' }}</div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">订单类型</div>
                                <div class="item-right">{{ getOrderType(orderDetail.orderSubType) }}</div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">优惠总额</div>
                                <div class="item-right text font-style"> &yen;{{ pageParams.discountTotalAmount || 0 }} </div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">支付折扣</div>
                                <div class="item-right text font-style"> &yen;{{ orderDetail.payDiscountTotal || 0 }} </div>
                            </div>
                            <div class="details-price-item">
                                <div class="item-left">支付明细</div>
                                <div class="item-right"></div>
                            </div>
                            <div class="details-price-item pay-list" v-for="(item, index) in orderDetail.payItems" :key="index">
                                <div class="item-left">{{ item.payMethod || '' }}</div>
                                <div class="item-right text">
                                    {{ item.payMethod == '电子账户积分' ? item.payAmount : '&yen;' + item.payAmount }}
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { orderDetailApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'cancelledOrderDetail',
    data() {
        return {
            // 订单信息是否展示更多按钮绑定值，通过该字段判断
            orderDetailStatus: true,
            //  商品列表
            orderDetail: {},
            pageParams: {},
        };
    },
    onLoad(option) {
        this.pageParams = JSON.parse(decodeURIComponent(option.data));
        this.orderDetailPost();
    },
    mounted() {},
    methods: {
        /**
         * @description  : 匹配回显订单类型
         * @param         {*} flag:
         * @return        {*}
         */
        getOrderType(flag) {
            if (flag == 11) {
                return 'e享加油';
            } else if (flag == 12) {
                return '加油卡预授权加油';
            } else if (flag == 13) {
                return '后支付加油';
            } else if (flag == 23) {
                return 'e享购';
            } else if (flag == 22) {
                return '积分换购';
            } else if (flag == 14) {
                return '室内付款';
            } else if (flag == 15) {
                return '异常订单补录';
            } else if (flag == 20) {
                return '洗车服务';
            } else if (flag == 47) {
                return '洗车服务';
            } else if (flag == 37) {
                return '加油机器人';
            } else if (flag == 48 || flag == 49) {
                return '能源锦鲤商城';
            } else if (flag == 55) {
                return '散装油';
            } else {
                return '';
            }
        },
        // 获取订单详情，并把油品和非油品商品数据分为两个数组便于展示
        async orderDetailPost() {
            let params = {
                orderNo: this.pageParams.orderNo,
                orderDetailStatus: this.orderDetailStatus,
            };
            let res = await orderDetailApi(params);
            if (res.success == true) {
                this.orderDetail = res.data;
                this.orderDetail.productList = res.data.orderItems || [];
                this.orderDetail.payChannel = res.data.payChannel?.join(',') || '';
            }
        },
        // 复制
        copyProductNo(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        // 申请开票
        nextStep() {
            let url = '/packages/third-invoice/pages/invoice-form/main';
            let params = {
                orderNoList: [this.pageParams.orderNo],
                checkAllAmount: this.actualPayTotalAmount,
            };
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style lang="scss" scoped>
.select-region-class {
    background: #f7f7fb;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .system-time {
        width: 375px;
        height: 80px;
        background: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #e64f22;
        font-size: 16px;
        font-weight: bold;
        padding: 0 70px;

        div {
            width: 44px;
            height: 44px;
            color: #ffffff;
            border-radius: 10px;
            text-align: center;
            line-height: 44px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        }
    }

    .content {
        width: 100%;
        flex: 1;

        .content-view {
            padding: 32rpx;
            overflow-y: auto;
            overflow-x: hidden;

            .gas-station {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;

                .gas-station-img {
                    width: 16px;
                    height: 16px;
                    margin-right: 5px;
                }

                .gas-station-text {
                    width: 200px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .oil-order {
                width: 100%;
                margin: 0 auto;
                // height: 257px;
                background: #ffffff;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
            }

            .commodity-order-box {
                width: 100%;
                margin: 0 auto;
                background: #ffffff;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;

                .commodity-order {
                    margin-top: 15px;

                    .item-bottom {
                        height: 35px;
                        line-height: 35px;
                        text-align: center;
                        color: #666;
                    }
                }
            }

            .details-price {
                width: 100%;
                margin: 0 auto;
                background: #ffffff;
                border-radius: 8px;
                padding: 23rpx 28rpx 29rpx;

                .details-price-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .item-left {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 67rpx;
                    }

                    .item-right {
                        display: flex;
                        justify-content: flex-end;
                        align-items: center;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 67rpx;

                        .item-right-text {
                            word-break: break-all;
                            max-width: 420rpx;
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 28rpx;
                        }

                        .copy {
                            width: 29px;
                            height: 16px;
                            background: #ffffff;
                            border-radius: 2px;
                            border: 1px solid #999999;
                            text-align: center;
                            line-height: 16px;
                            color: #666;
                            font-size: 10px;
                            margin-left: 5px;
                        }
                    }

                    .gray {
                        color: #666666;
                    }

                    .item-right-button {
                        padding: 0 20rpx;
                        height: 48rpx;
                        border-radius: 4rpx;
                        border: 1rpx solid #333333;
                        line-height: 48rpx;
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #333333;
                        text-align: center;
                    }

                    .item-right-text {
                        line-height: 50rpx;
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #333333;
                    }

                    .color {
                        color: #e64f22;
                        border: 1rpx solid #e64f22;
                    }
                }
            }

            .commodity {
                display: flex;

                .commodity-img {
                    width: 50px;
                    height: 50px;
                    border-radius: 5px;
                    overflow: hidden;
                    margin-right: 10px;
                }

                .detail-left-number {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 5px;
                    margin-right: 10px;
                    background-color: #f5c41b;
                    line-height: 100rpx;
                    text-align: center;
                    font-weight: bold;
                    font-size: 40rpx;

                    span {
                        vertical-align: text-top;
                        font-size: 24rpx;
                        line-height: 20rpx;
                    }
                }

                .detail-left-img {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 5px;
                    margin-right: 10px;
                }

                .commodity-title {
                    flex: 1;
                    color: #333;
                    font-size: 14px;
                }

                .commodity-price {
                    text-align: right;

                    .unit-price {
                        font-size: 14px;
                        color: #333;
                        line-height: 20px;
                    }

                    .num {
                        font-size: 10px;
                        color: #666;
                        line-height: 20px;
                    }

                    .total-price {
                        font-size: 14px;
                        color: #333;
                        font-weight: bold;
                    }
                }
            }

            .pay-list {
                padding-left: 30rpx;
            }

            .information {
                margin-top: 30rpx;
            }
        }
    }
}

.information-data {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15rpx;
    flex-wrap: wrap;

    &:nth-of-type(1) {
        margin-top: 0;
    }

    .information-data-left {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 48rpx;
    }

    .information-data-right {
        text-align: right;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-end;
        line-height: 48rpx;

        .copy {
            width: 29px;
            height: 18px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #999999;
            text-align: center;
            line-height: 16px;
            color: #666;
            font-size: 10px;
            margin-left: 5px;
        }
    }

    .text {
        color: #e64f22;
    }
}
</style>
