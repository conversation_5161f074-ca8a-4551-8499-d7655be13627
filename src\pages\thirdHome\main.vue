<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <div class="pageMpaas">
        <view v-if="loadCompleted" class="tabar-page-class">
            <view class="tabar-page-item" v-for="(page, index) in footerTabbar" :key="index" v-show="index == curTabIndex">
                <div class="pageBox" v-if="page && page.pageLoad">
                    <oil-charge
                        v-if="page.name == 'oilCharge'"
                        ref="oilCharge"
                        :isLogin="isLogin"
                        :pageChanged="paisLogingeChanged"
                        :refer="oilRefer"
                        :stationCode="stationCode"
                        :systemBarNum="systemBar"
                        :tabType="tabType"
                        @changePageChange="changePageChange"
                    ></oil-charge>
                    <thirdWallet
                        v-if="page.name == 'thirdWallet'"
                        ref="thirdWallet"
                        :isLogin="isLogin"
                        :refer="walletRefer"
                        pageType="home"
                        :systemBarNum="systemBar"
                    ></thirdWallet>
                    <third-order
                        v-if="page.name == 'thirdOrder'"
                        ref="thirdOrder"
                        :isLogin="isLogin"
                        :navActiveProp="orderNavActive"
                        :refer="orderRefer"
                        :secondNavActiveProp="orderSecondNavActive"
                        pageType="home"
                        :systemBarNum="systemBar"
                    ></third-order>
                    <personal-center
                        v-if="page.name == 'personalCenter'"
                        ref="personalCenter"
                        :isLogin="isLogin"
                        @backHome="backHome"
                        :systemBarNum="systemBar"
                    ></personal-center>
                </div>
            </view>
            <zjTabbar
                v-model="curTabIndex"
                :before-switch="beforeSwitch"
                :border-top="true"
                :list="footerTabbar"
                active-color="#FF6B2C"
                inactive-color="#333333"
                @change="tabChange"
            >
            </zjTabbar>
        </view>
        <div class="guideBox" v-if="guideStep == 3">
            <img class="guideImg" mode="widthFix" src="../../s-kit/image/homeImg/guidePage3.png" alt="" />
            <div class="guideBtton">
                <div class="skip" @click.stop="setGuideStep(0)">跳过</div>
                <div class="next" @click.stop="setGuideStep(4)">下一步</div>
            </div>
        </div>
        <GuidePage v-if="guideStep"></GuidePage>
        <div v-if="isHarmony">
            <keyboard-plugin></keyboard-plugin>
            <account-plugin ref="accountPlugin"></account-plugin>
        </div>
        <!-- #ifdef MP-WEIXIN || H5 -->
        <Privacy v-if="privacyIsShow"></Privacy>
        <addAnagerPopup
            v-if="addAnagerPopupShow && addAnagerPopupOrgCode"
            :orgCode="addAnagerPopupOrgCode"
            @close="closeAddAnagerPopup"
        ></addAnagerPopup>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <safe-password id="safeKeyboardIdHome" title="安全键盘"></safe-password>
        <!-- #endif -->
        <!-- #ifdef MP-ALIPAY -->
        <safe-password id="safeKeyboardIdHome" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
        <!-- #endif -->
        <!-- #ifdef H5-CLOUD -->
        <cloudKeyBordPlugin ref="cloudKeyboardRef" passwordType="payment"></cloudKeyBordPlugin>
        <!-- #endif -->
        <zj-show-modal> </zj-show-modal>
        <!-- <div style="width: 200px; height: 200px; background: #bfa" @click="kaika">开卡</div> -->
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import personalCenter from '../../s-kit/first/third-personal/main.vue';
import thirdWallet from '../../s-kit/first/third-wallet/main.vue';
import oilCharge from '../../s-kit/first/third-oil/main.vue';
import thirdOrder from '../../s-kit/first/third-order/main.vue';
import GuidePage from '../../s-kit/components/layout/guidePage/main.vue';
import addAnagerPopup from '@/s-kit/components/layout/add-anager-popup/index.vue';
import { alipayQuerySpaceInfo } from '../../s-kit/js/v3-http/https3/user.js';
// #ifdef MP-WEIXIN
import Privacy from '@/components/privacy/main.vue';
// #endif
// #ifdef H5-CLOUD
import { setCloud } from '../../s-kit/js/setCloud';
import cloudKeyBordPlugin from '../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue';
// #endif

import { clientCode, mpaasAppId, workspaceid } from '../../../project.config';

import zjTabbar from '../../s-kit/components/layout/zj-tabbar';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
const app = getApp();
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            curTabIndex: 0,
            footerTabbar: [
                {
                    iconPath: require('../../s-kit/image/tabbar-icon/no-oil-icon.png'),
                    selectedIconPath: require('../../s-kit/image/tabbar-icon/oil-icon.png'),
                    pageLoad: true,
                    name: 'oilCharge',
                    text: '加油',
                },
                {
                    iconPath: require('../../s-kit/image/tabbar-icon/no-wallet-text-icon.png'),
                    selectedIconPath: require('../../s-kit/image/tabbar-icon/wallet-text-icon.png'),
                    name: 'thirdWallet',
                    firstClick: true,
                    text: '钱包',
                    pageLoad: false,
                },
                {
                    iconPath: require('../../s-kit/image/tabbar-icon/no-order-icon.png'),
                    selectedIconPath: require('../../s-kit/image/tabbar-icon/order-icon.png'),
                    pageLoad: false,
                    name: 'thirdOrder',
                    text: '订单',
                    firstClick: true,
                    isDot: false,
                },
                {
                    iconPath: require('../../s-kit/image/tabbar-icon/no-my-icon.png'),
                    selectedIconPath: require('../../s-kit/image/tabbar-icon/my-icon.png'),
                    name: 'personalCenter',
                    pageLoad: false,
                    firstClick: true,
                    text: '我的',
                },
            ],
            tabType: '',
            stationCode: '',
            orderNavActive: 1,
            orderSecondNavActive: 0,
            tokenInfo: {},
            walletRefer: 'r09',
            oilRefer: 'r24',
            orderRefer: 'r48',
            // #ifndef MP-MPAAS || H5-CLOUD
            loadCompleted: false,
            // #endif
            // #ifdef MP-MPAAS || H5-CLOUD
            loadCompleted: true,
            // #endif
            addAnagerPopupShow: false,
            addAnagerPopupOrgCode: '',
            systemBar: '',
            pageChangeMapRe: false,
            pageChanged: false,
            // #ifndef MP-MPAAS
            isLogin: false,
            // #endif
            // #ifdef MP-MPAAS
            isLogin: true,
            // #endif
            addChargeBtnFlag: false,
        };
    },
    components: {
        personalCenter,
        thirdWallet,
        oilCharge,
        thirdOrder,
        GuidePage,
        addAnagerPopup,
        zjTabbar,
        // #ifdef MP-WEIXIN
        Privacy,
        // #endif
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin,
        // #endif
    },
    computed: {
        ...mapState({
            guideStep: state => state.thirdIndex.guideStep,
            cleanTheRegisteredAddress: state => state.thirdIndex.cleanTheRegisteredAddress,
            isOrderDot: state => state.thirdIndex.isOrderDot,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 存储公众号或者小程序跳转携带的参数
            mapCenterLatV3: state => state.locationV3_app.mapCenterLatV3,
            mapCenterLonV3: state => state.locationV3_app.mapCenterLonV3,
            privacyIsShow: state => state.privacy.privacyIsShow, // 展示隐私协议弹窗
            staffStationId: state => state.staffStationId,
            // #endif
            realNameDialogFlag: state => state.thirdLogin.realNameDialogFlag,
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            homepageTags: state => state.thirdLogin.homepageTags,
            longTimeNotLogin: state => state.thirdLogin.longTimeNotLogin,
            newMember: state => state.thirdLogin.newMember,
            returnToTheSpecifiedPage: state => state.thirdLogin.returnToTheSpecifiedPage,
            facePop: state => state.thirdIndex.facePop,
            token: state => state.token,
            isHarmony: state => state.thirdIndex.isHarmony,
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    watch: {
        isOrderDot: {
            handler(val, oldVal) {
                if (this.footerTabbar.length == 4) {
                    this.footerTabbar[2].isDot = val;
                } else if (this.footerTabbar.length == 5) {
                    this.footerTabbar[3].isDot = val;
                }
            },
            immediate: true,
        },
        tokenInfo: {
            handler(val, oldVal) {
                // #ifndef MP-MPAAS
                this.isLogin = val.accessToken ? true : false;
                // #endif
            },
            immediate: true,
            deep: true,
        },
    },

    // #ifndef H5-CLOUD
    onShareAppMessage() {
        return {
            title: '不下车加油、油卡充值，消费、在线开发票，加油更便捷',
            path: '/pages/thirdHome/main',
        };
    },
    // #endif
    // 生命周期 - 创建完成（访问当前this实例）
    async onLoad(options) {
        // #ifndef MP-MPAAS
        let definePreAuthFuel = uni.getStorageSync('Define_PreAuth_Fuel');
        if (definePreAuthFuel) {
            definePreAuthFuel = JSON.parse(decodeURIComponent(definePreAuthFuel));
            if (!definePreAuthFuel?.categoryCode) {
                uni.removeStorageSync('Define_PreAuth_Fuel');
            }
        }
        // #endif
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        this.tokenInfo = uni.getStorageSync('tokenInfo');
        this.loadCompleted = true;
        setTimeout(() => {
            this.getPlugin();
        }, 500);
        if (this.tokenInfo?.accessToken) {
            this.onLoadVariousInitializations(options);
        }
        // #endif
        // #ifdef H5-CLOUD
        this.tokenInfo = uni.getStorageSync('tokenInfo');
        // setTimeout(() => {
        // this.loadCompleted = true;
        // this.$nextTick(() => {
        //     this.getPlugin();
        // });
        // }, 1500);
        // #endif
        // #ifdef MP-WEIXIN
        // 登录类型 1、手机号验证码登录；4、自动登录； 11、一键登录
        setTimeout(() => {
            this.loadCompleted = true;
        }, 1000);
        let res = await this.$store.dispatch('init', { type: '4' });
        this.loginOver(res, options);
        // #endif
    },
    onShow() {
        if (this.tokenInfo?.accessToken) {
            this.onShowVariousInitializations();
        }
        // #ifdef MP-ALIPAY
        // 支付宝小程序是否授权手机号
        this.toGrantAuthorization();
        // #endif
        // #ifdef MP-MPAAS
        this.onShowVariousInitializations();
        // #endif
    },
    mounted() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$nextTick(async () => {
                this.getPlugin();
            });
            this.$sKit.mpaasPayPlugin?.init();
        }
        // #endif
        // #ifdef H5-CLOUD
        this.$nextTick(() => {
            setCloud('white', '0xFFFFFFFF', false, false)
                .then(dataSystemInfo => {
                    // 处理成功获取的系统信息
                    console.log('System info:', dataSystemInfo);
                    if (dataSystemInfo) {
                        console.log('statusBarHeight:', dataSystemInfo.statusBarHeight);
                        getApp().globalData.systemBar = Number(dataSystemInfo.statusBarHeight);
                        getApp().globalData.systemInfo = dataSystemInfo;
                        this.systemBar = Number(dataSystemInfo.statusBarHeight);
                    }
                })
                .catch(error => {
                    // 处理错误信息
                    console.error('Error:', error.message);
                });
        });
        // #endif
    },

    methods: {
        async addChargeBtn() {
            return new Promise(async (resolve, reject) => {
                this.systemInfo = uni.getSystemInfoSync();
                let productId = `${mpaasAppId}_${this.systemInfo?.osName === 'ios' ? 'IOS' : 'ANDROID'}-${workspaceid}`;
                let params = '';
                let cityCode = this.cityCode ? this.cityCode : '';
                let proCode = this.cityCode ? this.cityCode.toString().substring(0, 2) : '';
                let paramsData = {
                    // utdid, 设备id
                    utdid: this.systemInfo.deviceId,
                    // appId_终端类型-workspcae的组合
                    productId: productId,
                    // 客户端版本号
                    productVersion: '1',
                    // 系统版本号
                    osVersion: this.systemInfo.osVersion,
                    extInfo: [
                        { key: 'channelCode', value: clientCode },
                        { key: 'channel', value: 1 },
                        { key: 'proCode', value: proCode },
                        { key: 'cityCode', value: cityCode },
                    ],
                    // 展位码
                    spaceCodeList: ['charge_entrance'],
                };
                // #ifdef MP-WEIXIN
                let wxTokenInfo = await uni.getStorageSync('tokenInfo');
                paramsData.userId = wxTokenInfo?.memberNo || ''; //会员号
                params = [];
                params.push(paramsData);
                console.log(params, '微信测试获取会员号');
                // #endif
                // #ifdef MP-ALIPAY
                const aliTokenInfo = await uni.getStorageSync('tokenInfo');
                paramsData.userId = aliTokenInfo?.memberNo || ''; //会员号
                params = paramsData;
                console.log(params, '阿里测试获取会员号');
                // #endif
                let res = await alipayQuerySpaceInfo(params, { isload: false, isCustomErr: true });
                const chargeEntrance = res.spaceInfoList.find(item => item.zoneCode === 'charge_entrance');
                if (res && res.spaceInfoList.length > 0 && chargeEntrance.spaceObjectList.length > 0) {
                    this.footerTabbar.splice(1, 0, {
                        iconPath: require('../../s-kit/image/tabbar-icon/unSelectCharging.png'),
                        selectedIconPath: require('../../s-kit/image/tabbar-icon/selectCharging.png'),
                        pageLoad: false,
                        name: 'thirdCharge',
                        text: '充电',
                    });
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        },
        changePageChange(pageChanged) {
            this.pageChanged = pageChanged;
        },
        kaika() {
            // uni.navigateTo({
            //     url: '/packages/third-electronic-wallet/pages/wallet-add-form/main',
            // });
            uni.navigateTo({
                url: '/packages/third-my-center/pages/agreement/main',
            });
        },
        resizeCallback(e, changed) {
            if (this.footerTabbar[0].pageLoad && this.curTabIndex == 0) {
                this.$nextTick(() => {
                    this.$refs.oilCharge[0].isShowMap = false;
                    // this.$refs.oilCharge[0].isShowMapContent = false;
                    this.$refs.oilCharge[0].setMapHeight();
                });
            } else {
                this.pageChangeMapRe = true;
            }
            this.pageChanged = changed;
        },
        loginOver(res, options) {
            if (res.result == 'fail' || !res.success) {
                this.tokenInfo = {};
                this.loadCompleted = true;
                this.$store.commit('setLoginButtonGrayedOut', true);
            } else if (res.success) {
                this.tokenInfo = uni.getStorageSync('tokenInfo');
                this.loadCompleted = true;
                let data = JSON.parse(res.data.postLoginActionJsonStr);
                // this.longTimeNotLogin = data?.longTimeNotLogin;
                this.$store.commit('setLongTimeNotLogin', data?.longTimeNotLogin);
                setTimeout(() => {
                    this.getPlugin();
                }, 500);
                if (this.tokenInfo?.accessToken && this.tokenInfo.newMember) {
                    // 各种初始化
                    this.onLoadVariousInitializations(options);
                }
                // 单独给扫码员工邀请开卡做判断，在app.vue中接收的参数，不走中转页
                if (this.officialAccountParams == 2) {
                    this.$store.dispatch('jumpToOfficialAccount', res.data);
                }
            }
        },
        closeAddAnagerPopup() {
            this.addAnagerPopupShow = false;
        },
        toGrantAuthorization() {
            my.getSetting({
                withSubscriptions: true,
                success: res => {
                    console.log(res.authSetting, 'getSetting====');
                    if (res?.authSetting?.phoneNumber) {
                        // 已授权
                        this.$store.commit('setIsUserRefusesAuthorization', true);
                    } else {
                        // 未授权
                        this.$store.commit('setIsUserRefusesAuthorization', false);
                    }
                },
            });
        },
        // onload初始化
        async onLoadVariousInitializations(options) {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            // TODO @version?
            // this.addChargeBtnFlag = await this.addChargeBtn();
            // #endif
            // #ifdef MP-ALIPAY
            this.$sKit.aliPayPlugin?.init();
            if (options?.data) {
                // 个人中心路由跳转携带过来的参数
                let personalObj = JSON.parse(decodeURIComponent(options.data));
                if (personalObj.qKey == 'oil') {
                    if (personalObj?.refer) {
                        this.oilRefer = personalObj.refer;
                    } else {
                        this.oilRefer = 'r23';
                    }
                    if (personalObj.tabType) {
                        // 传递给ThirdOil组件
                        this.tabType = personalObj.tabType;
                    }
                    if (personalObj.stationCode) {
                        // 传递给ThirdOil组件
                        this.stationCode = personalObj.stationCode;
                    }
                    this.virtualChange(0);
                } else if (personalObj.qKey == 'wallet') {
                    this.walletRefer = personalObj.refer;
                    this.virtualChange(1);
                } else if (personalObj.qKey == 'orderList') {
                    this.orderRefer = 'r51';
                    if (personalObj.navActive) {
                        // 传递给ThirdOrder组件
                        this.orderNavActive = Number(personalObj.navActive);
                    }
                    if (personalObj.secondNavActive) {
                        // 传递给ThirdOrder组件
                        this.orderSecondNavActive = Number(personalObj.secondNavActive);
                    }
                    this.virtualChange(2);
                } else if (personalObj.qKey == 'mine') {
                    this.virtualChange(3);
                }
            }
            // #endif
            let action = '';
            let tabbarActions = {};
            // #ifdef MP-WEIXIN
            this.$sKit.wxPayPlugin?.initPayPlugin();
            tabbarActions = {
                wd: { showIndex: 3, hideIndex: 0 },
                dd: { showIndex: 2, hideIndex: 0 },
                qb: { showIndex: 1, hideIndex: 0 },
            };
            action = tabbarActions[this.officialAccountParams];
            console.log('跳转微信首页', action, this.officialAccountParams);
            if (action) {
                this.virtualChange(action.showIndex);
            }
            if (options?.data) {
                let personalObj = JSON.parse(decodeURIComponent(options.data));
                if (personalObj.type == 'addAnager') {
                    this.addAnagerPopupShow = true;
                    if (personalObj.orgCode) {
                        this.addAnagerPopupOrgCode = personalObj.orgCode;
                    }
                }
                if (personalObj?.curTabIndex) {
                    if (personalObj.curTabIndex == 1) {
                        this.walletRefer = personalObj?.refer;
                    } else if (personalObj.curTabIndex == 2) {
                        this.orderRefer = personalObj?.refer || 'r52';
                        this.orderNavActive = Number(personalObj?.navActive) || 1;
                        this.orderSecondNavActive = Number(personalObj?.secondNavActive) || 0;
                    }
                    this.virtualChange(personalObj?.curTabIndex);
                } else {
                    this.oilRefer = personalObj.refer;
                }
            }

            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD
            // 获取待办事项
            this.getTodoList();
            this.isShowAgreement();

            setTimeout(() => {
                console.log(this.longTimeNotLogin, 'this.longTimeNotLogin');
                if (this.longTimeNotLogin) {
                    this.$store.dispatch('zjShowModal', {
                        title: '提示',
                        content:
                            '由于您长时间未登录，系统检测到您当前账号的身份信息与手机号运营商侧信息不一致，请确认当前账号的身份认证信息是否与您本人一致，避免产生资金风险。如有疑问请联系956100客服',
                        confirmText: '我是本人',
                        confirmColor: '#000',
                        cancelText: '联系客服',
                        cancelColor: '#666',
                        success: res => {
                            this.$store.commit('setLongTimeNotLogin', '');
                            let tokenInfo = uni.getStorageSync('tokenInfo');
                            try {
                                // 尝试解析JSON字符串
                                let obj = JSON.parse(tokenInfo.postLoginActionJsonStr);

                                // 直接更新属性
                                obj.longTimeNotLogin = '';

                                // 序列化回JSON字符串并更新tokenInfo对象
                                tokenInfo.postLoginActionJsonStr = JSON.stringify(obj);

                                // 存储更新后的tokenInfo对象
                                uni.setStorageSync('tokenInfo', tokenInfo);
                            } catch (error) {
                                // 处理解析错误，例如打印错误信息或者设置一个默认值
                                console.error('Failed to parse postLoginActionJsonStr:', error);
                            }
                            if (res.confirm) {
                            } else {
                                uni.makePhoneCall({
                                    phoneNumber: '956100', //仅为示例
                                    success: res => {},
                                    fail: error => {},
                                });
                            }
                        },
                    });
                }
            }, 1500);
            if (this.curTabIndex == 0) {
                this.appletHomeBiz();
            }
            // #endif
            // #ifdef H5-CLOUD
            if (options?.data) {
                let personalObj = JSON.parse(decodeURIComponent(options.data));
                this.virtualChange(personalObj?.curTabIndex);
            }
            // #endif
        },
        // onShow初始化
        onShowVariousInitializations() {
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                this.getPlugin();
                if (this.curTabIndex == 0) {
                    this.notPaidListGet();
                }
            }
            // #endif
            // #ifndef MP-MPAAS
            this.getPlugin();

            if (this.curTabIndex == 0) {
                this.notPaidListGet();
            }
            // #endif
            let actions;
            if (this.footerTabbar.length == 4) {
                actions = {
                    1: () => this.$refs.thirdWallet[0].refreshPullDown(),
                    2: () => this.$refs.thirdOrder[0].dateReset('ok'),
                    3: () => setTimeout(() => this.$refs.personalCenter[0].refreshPullDown(), 600),
                };
            } else if (this.footerTabbar.length == 5) {
                actions = {
                    2: () => this.$refs.thirdWallet[0].refreshPullDown(),
                    3: () => this.$refs.thirdOrder[0].dateReset('ok'),
                    4: () => setTimeout(() => this.$refs.personalCenter[0].refreshPullDown(), 600),
                };
            }
            if (actions[this.curTabIndex]) {
                this.$nextTick(actions[this.curTabIndex]);
            }
        },
        setGuideStep(step) {
            this.$store.dispatch('changeGuideStep', step);
            if (step == 4) {
                this.virtualChange(3);
            }
        },
        backHome() {
            this.virtualChange(0);
        },
        // 查询待结交易订单
        notPaidListGet() {
            this.$nextTick(() => {
                this.$refs.oilCharge[0].chargenotPaidListOrder();
            });
        },

        //底部导航点击事件
        tabChange(index) {
            if (!this.isHarmony && !this.tokenInfo.accessToken) {
                return;
            }
            if (this.footerTabbar.length == 4) {
                // 消息订阅
                if (index === 2 && !this.isHarmony) {
                    this.$sKit.layer.requestSubscribeMessage();
                }
            } else if (this.footerTabbar.length == 5) {
                // 消息订阅
                if (index === 3 && !this.isHarmony) {
                    this.$sKit.layer.requestSubscribeMessage();
                }
            }
            // 跟踪点击埋点事件
            this.trackBottomClick(index);
            // 处理特定 tab 的逻辑
            this.handleTabIndex(index);
        },
        // 跟踪底部导航点击埋点事件
        trackBottomClick(index) {
            let bottomClickMap = {};
            if (this.footerTabbar.length == 4) {
                bottomClickMap = {
                    0: 'oil',
                    1: 'wallet',
                    2: 'order',
                    3: 'mine',
                };
            } else if (this.footerTabbar.length == 5) {
                bottomClickMap = {
                    0: 'oil',
                    1: 'charge',
                    2: 'wallet',
                    3: 'order',
                    4: 'mine',
                };
            }

            const bottomClick = bottomClickMap[index];

            if (bottomClick) {
                this.$sKit.mpBP.tracker('底部导航栏', {
                    seed: 'bottomBiz',
                    pageId: 'homePage',
                    bottom_click: bottomClick,
                    channelID: clientCode,
                });
            }
        },

        // 处理不同 tab 索引的逻辑
        handleTabIndex(index) {
            console.log('handleTabIndex====执行了吗');
            if (index !== 0 && this.$refs.oilCharge[0].topTab === 'code') {
                this.clearTimers();
            }
            if (this.footerTabbar.length == 4) {
                switch (index) {
                    case 0:
                        this.handleOilTab(index);
                        break;
                    case 1:
                        this.handleWalletTab();
                        break;
                    case 2:
                        this.handleOrderTab();
                        break;
                    case 3:
                        this.handleMineTab();
                        break;
                    default:
                        break;
                }
            } else if (this.footerTabbar.length == 5) {
                switch (index) {
                    case 0:
                        this.handleOilTab(index);
                        break;
                    case 1:
                        console.log('充电');
                    case 2:
                        this.handleWalletTab();
                        break;
                    case 3:
                        this.handleOrderTab();
                        break;
                    case 4:
                        this.handleMineTab();
                        break;
                    default:
                        break;
                }
            }
        },

        // 处理油站充值 tab 的逻辑
        handleOilTab(index) {
            if (this.pageChangeMapRe) {
                this.$nextTick(() => {
                    this.$refs.oilCharge[0].isShowMap = false;
                    this.$refs.oilCharge[0].setMapHeight();
                });
                this.pageChangeMapRe = false;
            }
            // 新增：用于标记是否首次调用的变量
            if (this.cleanTheRegisteredAddress) {
                this.$store.dispatch('getTodoInfoFun', {
                    topTab: this.$refs.oilCharge[0].topTab,
                    index,
                });
            }
            this.$nextTick(() => {
                setTimeout(() => {
                    if (this.$refs.oilCharge[0].topTab === 'code') {
                        if (this.$refs.oilCharge[0].$refs.codePaymentConten.codeFlag === 'membershipCodeFlag') {
                            this.clearTimers();
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.membershipCode.init();
                        } else if (this.$refs.oilCharge[0].$refs.codePaymentConten.codeFlag === 'paymentCodeFlag') {
                            this.clearTimers();
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.paymentCode.init();
                        }
                    }
                }, 100);
            });
        },

        // 处理钱包 tab 的逻辑
        handleWalletTab() {},
        // 处理订单 tab 的逻辑
        handleOrderTab() {},

        // 处理我的 tab 的逻辑
        handleMineTab() {},
        virtualChange(index) {
            console.log(index, 'index=====virtualChange');
            let newIndex;
            if (this.addChargeBtnFlag && Number(index) >= 1) {
                newIndex = Number(index) + 1;
            } else {
                newIndex = Number(index);
            }
            if (this.beforeSwitch(newIndex)) {
                this.tabChange(newIndex);
                this.curTabIndex = newIndex;
            }
        },
        beforeSwitch(index) {
            if (this.footerTabbar[1].name == 'thirdCharge') {
                if (index === 1 && this.tokenInfo.accessToken) {
                    // 场景：充电入口-已登录，跳转充电站列表并阻止当前标签切换
                    this.$sKit.layer.useRouter('/packages/third-charge/pages/station-list/main');
                    this.trackBottomClick(index);
                    return false;
                }
                if (index === 1) {
                    // 场景：充电入口-未登录，跳转转账首页并允许切换
                    this.$sKit.layer.useRouter('/pages/union/main');
                    return true;
                }
            }

            if (index === this.curTabIndex) {
                // 同标签重复点击，阻止切换
                return false;
            }

            // 新标签首次加载处理
            if (!this.footerTabbar[index].pageLoad) {
                this.footerTabbar[index].pageLoad = true;
                uni.showLoading();
            }

            // Harmony系统特殊处理（非0标签切换时关闭分屏）
            if (this.isHarmony && index !== 0) {
                this.$cnpcBridge.isCutScreen(false);
            }

            // 允许标签切换
            return true;
        },
        async getPlugin() {
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                this.$nextTick(async () => {
                    let result = await this.$sKit.keyBordPlugin.initRef();
                    this.$store.commit('setAccountDataPlugin', result);
                    const accountPluginRef = this.$refs.accountPlugin;
                    this.$sKit.accountPlugin.initRef(accountPluginRef);
                });
            }
            // #endif
            // #ifdef MP-WEIXIN
            let result = await this.selectComponent('#safeKeyboardIdHome');
            this.$store.commit('setAccountDataPlugin', result);
            // #endif
            // #ifdef MP-ALIPAY
            this.$nextTick(async () => {
                const result = this.$refs['handlePasswordKeyboardRef'];
                this.$sKit.keyBordPlugin.initRef(result);
                console.log('handlePasswordKeyboardRef-result', result);
                this.$store.commit('setAccountDataPlugin', result);
            });
            // #endif
            // #ifdef H5-CLOUD
            this.$sKit.cloudPayPlugin.initPayPlugin();
            this.$nextTick(async () => {
                // 获取键盘的实例
                let result = await this.$refs.cloudKeyboardRef.initRef();
                console.log('result', result);
                this.$store.commit('setAccountDataPlugin', result);
            });

            // #endif
        },
        /**
         * @description  : 获取待办事项
         * @return        {*}
         */
        getTodoList() {
            setTimeout(async () => {
                let result = await this.$store.dispatch('getTodoInfoFun', {
                    topTab: this.$refs.oilCharge[0].topTab,
                    index: this.curTabIndex,
                });
                console.log(result, '待办事项打印');
                let loginGuide = result.data.loginGuide || false;
                if (result.data.todoType != 1 && loginGuide) {
                    this.$store.dispatch('changeGuideStep', 1);
                    if (this.curTabIndex != 0) {
                        this.virtualChange(0);
                    }
                }
            }, 300);
        },
        async isShowAgreement() {
            let timeData = new Date().getTime();
            // 2025-07-07 10:00:00
            if (timeData > 1751853600000) {
                const { memberNo } = uni.getStorageSync('tokenInfo');

                console.log(uni.getStorageSync(`Define_Agreement_Dialog_${memberNo}`), '存在吗');

                if (!uni.getStorageSync(`Define_Agreement_Dialog_${memberNo}`)) {
                    this.$store.dispatch('zjShowModal', {
                        title: '尊敬的客户：您好！由于会员服务升级，本平台多项协议有较大调整，请您充分阅读及理解。',
                        success: res => {
                            if (res.confirm) {
                                this.$sKit.layer.useRouter('/packages/third-my-center/pages/agreement/main', {}, 'redirectTo');
                            }
                        },
                    });
                }
            }
        },
        appletHomeBiz() {
            this.$sKit.mpBP.tracker('支付宝/微信小程序加油页', {
                seed: 'appletHomeBiz',
                pageId: 'homePage',
                refer: this.oilRefer,
                channelID: clientCode,
                dateType: 'exposure',
            });
        },
        clearTimers() {
            this.$nextTick(() => {
                setTimeout(() => {
                    if (this.$refs.oilCharge[0].topTab === 'code') {
                        if (this.$refs.oilCharge[0].$refs.codePaymentConten.codeFlag === 'membershipCodeFlag') {
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.membershipCode.clearTimerMember();
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.membershipCode.clearTimerQuqeyOrder();
                        } else if (this.$refs.oilCharge[0].$refs.codePaymentConten.codeFlag === 'paymentCodeFlag') {
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.paymentCode.clearTimerMember();
                            this.$refs.oilCharge[0].$refs.codePaymentConten.$refs.paymentCode.clearTimerQuqeyOrder();
                        }
                    }
                }, 100);
            });
        },
    },
    // #ifndef H5-CLOUD
    onHide() {
        this.clearTimers();
    },
    // #endif
};
</script>
<style scoped lang="scss">
::v-deep .u-badge-dot {
    right: 0 !important;
}

.tabar-page-class {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;

    .tabar-page-item {
        flex: 1;
        height: 100%;
        min-height: 0;

        .pageBox {
            height: 100%;
            min-height: 0;
            box-sizing: border-box;
        }
    }

    .tabbar {
        position: fixed;
        bottom: 0;
        width: 100%;
        z-index: 20;
    }

    .bg-tab {
        width: 100vw;
        height: 99px;
        position: fixed;
        bottom: calc(-21px + env(safe-area-inset-bottom));
        z-index: 10;
        left: 0;
        right: 0;
    }
}

.pageMpaas {
    position: relative;
}

.guideBox {
    .guideImg {
        position: absolute;
        width: 100%;
        bottom: calc(env(safe-area-inset-bottom) - 2.5px);
        right: 0;
        left: 0;
        z-index: 999999;
    }

    .guideBtton {
        display: flex;
        position: absolute;
        bottom: calc(env(safe-area-inset-bottom) + 85px);
        z-index: 9999999;
        right: 50%;
        transform: translateX(50%);

        div {
            height: 51rpx;
            border-radius: 34rpx;
            border: 2rpx solid #ffffff;
            font-size: 24rpx;
            line-height: 51rpx;
            text-align: center;
            width: 69px;
            margin-right: 10px;

            &:nth-of-type(1) {
                color: #fff;
            }

            &:nth-of-type(2) {
                color: #333;
                margin-right: 0;
                background-color: #fff;
            }
        }
    }
}
</style>
