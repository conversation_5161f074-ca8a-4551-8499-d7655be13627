
 import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
 import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
 
 export default {
     // #ifdef MP-TOUTIAO
     mounted() {
      console.log('ttt抖音')
     },
     methods: {
         /**
          * @description     : 退出登录
          * @return        {*}
          */
          async logout() {
            let tokenInfo = await uni.getStorageSync('tokenInfo');
            if (tokenInfo?.accessToken) {
                let res = await logoutApi();
                if (res.success) {
                    uni.clearStorageSync();
                    this.$store.commit('setLongTimeNotLogin', null);
                    this.$sKit.layer.backHomeFun();
                }
            }
        },
         /**
          * @description     : 查看协议点击事件
          * @return        {*}
          */
         clickXieyi(values) {
             if (values == 17) {
                 uni.navigateTo({
                     url: '/packages/setting/pages/agreement/main?value=17',
                 });
             } else if (values == 63) {
                 uni.navigateTo({
                     url: '/packages/setting/pages/agreement/main?value=63',
                 });
             }
         },
         /**
          * 隐私政策/用户协议
          * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
          * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
          * @returns {Promise<void>}
          */
         async getAgreeOn(type, name) {
             let params = {
                 type: type,
                 cityName: '全国',
                 name: name,
             };
             let userAgreementRes = await userAgreement(params);
             if (userAgreementRes.success) {
                 if (userAgreementRes.data.fileUrl) {
                     // 打开PDF
                     this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                 }
             } else {
                 uni.showToast({ title: '未找到该协议' });
             }
         },
     },
     // #endif
 };
 