<template>
    <div class="img-div">
        <!-- 营销位轮播图 -->
        <div v-if="marketType == 'lbt'">
            <div class="swiper-content" v-if="bannerList.length > 0">
                <swiper
                    class="center-module-swiper"
                    :indicator-dots="indicatorDots"
                    :circular="circular"
                    :autoplay="autoplay"
                    :interval="rotationTime"
                    @change="lbtSwiperChange"
                    indicator-color="rgba(255, 255, 255, 1)"
                    indicator-active-color="rgba(255, 255, 255, 0.4)"
                    v-if="marketType == 'lbt'"
                >
                    <swiper-item class="swiper-item" v-for="(item, index) in bannerList" :key="index">
                        <view class="swiper-img-wrap">
                            <img
                                class="swiper-img"
                                :style="{ borderRadius: radius }"
                                :src="item.hrefUrl"
                                mode="scaleToFill"
                                @click="clickEvent(item, index)"
                            />
                        </view>
                    </swiper-item>
                </swiper>
                <div class="swiper-dot-wrap" v-if="bannerList.length > 1">
                    <div :class="swiperKey == i ? 'swiper-dot-active' : 'swiper-dot'" v-for="(item, i) in bannerList" :key="i"></div>
                </div>
            </div>
        </div>
        <!-- 营销位1行2个 -->
        <div class="rubik-content" v-if="marketType == 'rowTwo'">
            <div
                class="swiper-content"
                v-if="bannerList.length > 0"
                :class="{ 'swiper-content-bottom': bannerList.length > 0 && marketType == 'rowTwo' }"
            >
                <swiper
                    class="center-module-swiper"
                    :indicator-dots="indicatorDots"
                    :circular="circular"
                    :autoplay="autoplay"
                    :interval="rotationTime"
                    @change="lbtSwiperChange"
                    indicator-color="rgba(255, 255, 255, 1)"
                    indicator-active-color="rgba(255, 255, 255, 0.4)"
                >
                    <swiper-item
                        class="swiper-item"
                        v-if="bannerList.length == 1"
                        v-for="(item, index) in bannerList"
                        :key="index"
                        @touchmove.stop
                    >
                        <view class="swiper-img-wrap">
                            <img class="swiper-img" mode="scaleToFill" :src="item.hrefUrl" @click="clickEvent(item, index)" />
                        </view>
                    </swiper-item>
                    <swiper-item class="swiper-item" v-else v-for="(item, index) in bannerList" :key="index">
                        <view class="swiper-img-wrap">
                            <img class="swiper-img" mode="scaleToFill" :src="item.hrefUrl" @click="clickEvent(item, index)" />
                        </view>
                    </swiper-item>
                </swiper>
                <div class="swiper-dot-wrap" v-if="bannerList.length > 1">
                    <div :class="swiperKey == i ? 'swiper-dot-active' : 'swiper-dot'" v-for="(item, i) in bannerList" :key="i"></div>
                </div>
            </div>
        </div>
        <!-- 弹窗广告 -->
        <div class="mask" v-if="marketType == 'screenMask'">
            <custom-popup ref="popDialogFlag" type="center" :animation="true" :maskClick="false">
                <div class="_modal">
                    <div class="iol-pop">
                        <div class="swiper-content" v-if="bannerList.length > 0">
                            <swiper
                                class="center-module-swiper"
                                :indicator-dots="indicatorDots"
                                :circular="circular"
                                :autoplay="autoplay"
                                :interval="rotationTime"
                                @change="lbtSwiperChange"
                                indicator-color="rgba(255, 255, 255, 1)"
                                indicator-active-color="rgba(255, 255, 255, 0.4)"
                                v-if="marketType == 'screenMask'"
                            >
                                <swiper-item class="swiper-item" v-for="(item, index) in bannerList" :key="index">
                                    <view class="swiper-img-wrap">
                                        <img class="swiper-img" :src="item.hrefUrl" mode="scaleToFill" @click="clickEvent(item, index)" />
                                    </view>
                                </swiper-item>
                            </swiper>
                            <div class="swiper-dot-wrap" v-if="bannerList.length > 1">
                                <div
                                    :class="swiperKey == i ? 'swiper-dot-active' : 'swiper-dot'"
                                    v-for="(item, i) in bannerList"
                                    :key="i"
                                ></div>
                            </div>
                        </div>
                    </div>
                    <div class="close-tbn-3" @click.stop="closeScreenMask">
                        <img src="../../../image/popupClose.png" />
                    </div>
                </div>
            </custom-popup>
        </div>
        <div class="bg-fff middle-content-wrap border-rad-4" v-if="marketType === 'redeem' && bannerList.length > 0">
            <div class="fl-row fl-al-cen middle-content fl-jus-bet">
                <div class="fl-row fl-al-cen fl-jus-bet">
                    <img class="img" :src="virtualGift" alt="" />
                    <div class="fl-column text">
                        <div class="weight-600 font-18 color-333 text1">积分兑换好礼</div>
                        <div class="weight-400 font-14 color-999">多种礼品免费兑换</div>
                    </div>
                </div>
                <div class="redeemNow te-center color-fff font-13 weight-400" @click="clickEvent(bannerList[0])">立即兑换</div>
            </div>
        </div>
    </div>
</template>

<script>
import { alipayQuerySpaceInfo } from '../../../js/v3-http/https3/user.js';
import platform from '@/s-kit/js/platform';
import projectConfig from '../../../../../project.config';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            indicatorDots: false,
            circular: true,
            autoplay: true,
            systemInfo: '',
            //轮播位置
            swiperKey: 0,
            bannerList: [],
            rotationTime: '3000',
            // #ifdef MP-WEIXIN
            BMap: null,
            // #endif
            virtualGift: require('../../../image/virtualGift.png'),
        };
    },
    props: {
        marketType: {
            type: String,
            default: '',
        },
        spaceType: {
            type: String,
            default: '',
        },
        radius: {
            type: Number,
            default: 0,
        },
        // orgCode: {
        //     type: String,
        //     default: '',
        // },
        // 支付消费钱数
        payDoneMoney: {
            type: Number,
            default: 0,
        },
        // 支付消费油品名称
        oilProductCode: {
            type: String,
            default: '',
        },
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {},
    //生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
        console.log(this.marketType, 'marketType===');
        console.log('jfldjfslkadfjhlskad');
        // console.log(this.cityCode, 'proCode');
        await this.getData();
    },
    methods: {
        async getData() {
            this.systemInfo = uni.getSystemInfoSync();
            let productId = `${projectConfig.mpaasAppId}_${this.systemInfo?.osName === 'ios' ? 'IOS' : 'ANDROID'}-${
                projectConfig.workspaceid
            }`;
            let params = '';
            let proCode = '';
            let cityCode = '';
            // #ifdef MP-MPAAS
            cityCode = await this.getLocation();
            cityCode = cityCode ? cityCode : '';
            proCode = cityCode ? cityCode.toString().substring(0, 2) : '';
            // #endif
            // #ifndef MP-MPAAS
            cityCode = this.cityCode ? this.cityCode : '';
            proCode = this.cityCode ? this.cityCode.toString().substring(0, 2) : '';
            // #endif
            let paramsData = {
                // utdid, 设备id
                utdid: this.systemInfo.deviceId,
                // appId_终端类型-workspcae的组合
                productId: productId,
                // 客户端版本号
                productVersion: '1',
                // 系统版本号
                osVersion: this.systemInfo.osVersion,
                extInfo: [
                    { key: 'channelCode', value: projectConfig.clientCode },
                    { key: 'spaceType', value: this.spaceType },
                    { key: 'channel', value: 1 },
                    { key: 'proCode', value: proCode },
                    { key: 'cityCode', value: cityCode },
                    { key: 'payMoney', value: this.payDoneMoney },
                    { key: 'rechargeAmount', value: this.payDoneMoney },
                    { key: 'oilLimit', value: this.oilProductCode },
                ],
            };
            // 展位码
            paramsData.spaceCodeList = this.marketType != 'screenMask' ? ['oil_banner'] : ['dialog_banner'];
            // #ifdef MP-WEIXIN
            let wxTokenInfo = await uni.getStorageSync('tokenInfo');
            paramsData.userId = wxTokenInfo?.memberNo || ''; //会员号
            params = [];
            params.push(paramsData);
            console.log(params, '微信测试获取会员号');
            // #endif
            // #ifdef MP-MPAAS
            let mPaasTokenInfo = await this.$cnpcBridge.getUserTokenInfo();
            paramsData.userId = mPaasTokenInfo?.memberNo || ''; //会员号
            //小程序banner位abtest配置，配置值为实验场景内容
            if (this.spaceType == 'oilreserve_top') {
                let switchInfo = await this.getSwitch('BannerMiniAppAbtest');
                if (switchInfo) {
                    paramsData.extInfo.push({ key: 'BannerMiniAppAbtest', value: switchInfo });
                }
            }
            params = paramsData;
            console.log(params, 'APP测试获取会员号');
            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD
            if (platform.isAlipay) {
                const aliTokenInfo = await uni.getStorageSync('tokenInfo');
                paramsData.userId = aliTokenInfo?.memberNo || ''; //会员号
                params = paramsData;
                console.log(params, '阿里测试获取会员号');
            }
            // #endif
            // #ifdef H5-CLOUD
            let CLOUDTokenInfo = await uni.getStorageSync('tokenInfo');
            paramsData.userId = CLOUDTokenInfo?.memberNo || ''; //会员号
            params = [];
            params.push(paramsData);
            console.log(params, 'userId测试获取会员号');
            // #endif
            let res = await alipayQuerySpaceInfo(params, { isload: false, isCustomErr: true });
            console.log('res----', res);
            if (res && res.spaceInfoList.length > 0) {
                console.log(`${this.spaceType}`, res);
                this.bannerList = (res && res.spaceInfoList[0].spaceObjectList) || [];
				if (this.bannerList.length === 0 && this.spaceType === 'redeem_page'){
					// 值为true 代表是没有数据返回，积分列表向上移动
					this.$parent.marTop = true
				}
                this.rotationTime = res && res.spaceInfoList[0].rotationTime + '000';
                if (this.bannerList.length > 0) {
                    // #ifdef H5-CLOUD
                    this.$store.commit('setBoxTop', 90);
                    // #endif
                }
                let bizContent = res.spaceInfoList[0].spaceObjectList.map(item => {
                    let url = item.actionUrl;
                    try {
                        if (typeof url == 'string') {
                            url = JSON.parse(url);
                        }
                    } catch {}
                    return url;
                });
                bizContent = JSON.stringify(bizContent).replace(/,/g, ' ').replace(/&/g, '?');
                const firstUrl = '',
                    count = this.bannerList.length;
                // 营销位数量为0 不提交曝光埋点事件
                if (count === 0) return;
                switch (this.spaceType) {
                    // e享加油
                    case 'oilreserve_top':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilreserve_flowArea_top', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'oilreserve_left':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilreserve_flowArea_left', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'oilreserve_right':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilreserve_flowArea_right', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    // 后支付加油
                    case 'oilcharge_top':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilcharge_flowArea_top', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'oilcharge_left':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilcharge_flowArea_left', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'oilcharge_right':
                        this.$sKit.mpBP.tracker('运营活动', {
                            seed: 'mcdpBiz',
                            pageID: 'oilcharge_flowArea_right', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                }
                if (this.marketType === 'screenMask') {
                    let key = paramsData.userId + '_' + this.spaceType;
                    let res = uni.getStorageSync(key);
                    this.screenMaskShow(res, key);
                }
            }else if(this.spaceType === 'redeem_page'){
				this.$parent.marTop = true
			}
        },
        screenMaskShow(marketFatigue, key) {
            let newMarketFatigue = JSON.parse(JSON.stringify(marketFatigue)) || {};
            let groupId = this.bannerList[0].logExtInfo.find(item2 => item2.key == 'groupId').value;
            if (marketFatigue && newMarketFatigue.groupId == groupId) {
            } else {
                let CLOSE_AFTER_CLICKObj = {};
                let CLOSE_AFTER_TIMESObj = {};
                if (this.bannerList[0].behaviors.length > 0) {
                    CLOSE_AFTER_CLICKObj = this.bannerList[0].behaviors.find(item3 => item3.behavior == 'CLOSE_AFTER_CLICK');
                    CLOSE_AFTER_TIMESObj = this.bannerList[0].behaviors.find(item3 => item3.behavior == 'CLOSE_AFTER_TIMES');
                }
                newMarketFatigue = {
                    groupId: groupId,
                    CLOSE_AFTER_CLICK: { showTimes: CLOSE_AFTER_CLICKObj?.showTimes || 'always', consumed: 0 },
                    CLOSE_AFTER_TIMES: { showTimes: CLOSE_AFTER_TIMESObj?.showTimes || 'always', consumed: 0 },
                };
            }
            console.log('弹窗疲劳度1', newMarketFatigue);
            if (
                (newMarketFatigue.CLOSE_AFTER_TIMES.showTimes == 'always' ||
                    newMarketFatigue.CLOSE_AFTER_TIMES.consumed < newMarketFatigue.CLOSE_AFTER_TIMES.showTimes) &&
                (newMarketFatigue.CLOSE_AFTER_CLICK.showTimes == 'always' ||
                    newMarketFatigue.CLOSE_AFTER_CLICK.consumed < newMarketFatigue.CLOSE_AFTER_CLICK.showTimes)
            ) {
                newMarketFatigue.CLOSE_AFTER_TIMES.consumed += 1;
                console.log('弹窗疲劳度3', newMarketFatigue);
                uni.setStorageSync(key, newMarketFatigue);
                console.log(this.$refs.popDialogFlag, this.spaceType, '弹窗疲劳度4');
                let bizContent = this.bannerList.map(item => {
                    let url = item.actionUrl;
                    try {
                        if (typeof url == 'string') {
                            url = JSON.parse(url);
                        }
                    } catch {}
                    return url;
                });
                bizContent = JSON.stringify(bizContent).replace(/,/g, ' ').replace(/&/g, '?');
                switch (this.spaceType) {
                    case 'home_page':
                        this.$sKit.mpBP.tracker('首页弹窗', {
                            seed: 'ToastBiz',
                            pageID: 'homeActivityToast', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'my_page':
                        this.$sKit.mpBP.tracker('首页弹窗', {
                            seed: 'ToastBiz',
                            pageID: 'mineActivityToast', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'payment_page':
                        this.$sKit.mpBP.tracker('首页弹窗', {
                            seed: 'ToastBiz',
                            pageID: 'payActivityToast', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                    case 'recharge_page':
                        this.$sKit.mpBP.tracker('首页弹窗', {
                            seed: 'ToastBiz',
                            pageID: 'rechargeActivityToast', // 返回sdk标识
                            address: this.cityName,
                            channelID: projectConfig.clientCode,
                            dateType: 'exposure',
                            content: bizContent,
                        });
                        break;
                }
                this.$refs.popDialogFlag.open();
                this.$emit('isPageH', true);
            } else {
                this.$emit('isPageH', false);
            }
        },
        lbtSwiperChange(e) {
            this.swiperKey = e.detail.current;
        },
        getSwitch(key) {
            return new Promise((resolve, reject) => {
                this.$cnpcBridge.getSwitch(key, res => {
                    resolve(res);
                });
            });
        },
        //  轮播图点击
        async clickEvent(item, index) {
			console.log('actionInfo----',this.bannerList,item, typeof item.actionUrl);
			console.log(JSON.parse(item.actionUrl),'转译')
            let actionInfo = JSON.parse(item.actionUrl);
            // console.log('actionInfo----',JSON.parse(actionInfo))
            console.log('actionInfo----', actionInfo);
            let bizContent = [actionInfo];
            bizContent = JSON.stringify(bizContent).replace(/,/g, ' ');
            if (!actionInfo) return;
            // 营销位点击埋点事件
            switch (this.spaceType) {
                // e享加油
                case 'oilreserve_top':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilreserve_flowArea_top', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'oilreserve_left':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilreserve_flowArea_left', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'oilreserve_right':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilreserve_flowArea_right', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                // 后支付加油
                case 'oilcharge_top':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilcharge_flowArea_top', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'oilcharge_left':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilcharge_flowArea_left', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'oilcharge_right':
                    this.$sKit.mpBP.tracker('运营活动', {
                        seed: 'mcdpBiz',
                        pageID: 'oilcharge_flowArea_right', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'home_page':
                    this.$sKit.mpBP.tracker('首页弹窗', {
                        seed: 'ToastBiz',
                        pageID: 'homeActivityToast', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'my_page':
                    this.$sKit.mpBP.tracker('首页弹窗', {
                        seed: 'ToastBiz',
                        pageID: 'mineActivityToast', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'payment_page':
                    this.$sKit.mpBP.tracker('首页弹窗', {
                        seed: 'ToastBiz',
                        pageID: 'payActivityToast', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
                case 'recharge_page':
                    this.$sKit.mpBP.tracker('首页弹窗', {
                        seed: 'ToastBiz',
                        pageID: 'rechargeActivityToast', // 返回sdk标识
                        address: this.cityName,
                        channelID: projectConfig.clientCode,
                        dateType: 'click',
                        content: bizContent,
                    });
                    break;
            }

            let openid = '';
            let gsmsToken = '';
            let memberNo = '';
            // #ifdef MP-WEIXIN
            let tokenInfo = uni.getStorageSync('tokenInfo');
            gsmsToken = this.$store.state.token3 || '';
            openid = this.$store.state.openId || '';
            memberNo = tokenInfo?.memberNo || '';
            // #endif

            // #ifdef MP-ALIPAY
            if (platform.isAlipay) {
                let tokenInfo = uni.getStorageSync('tokenInfo');
                gsmsToken = tokenInfo?.accessToken || '';
                memberNo = tokenInfo?.memberNo || '';
            }
            // #endif
            // #ifdef H5-CLOUD
            let tokenInfo = uni.getStorageSync('tokenInfo');
            gsmsToken = tokenInfo?.accessToken || '';
            memberNo = tokenInfo?.memberNo || '';
            // #endif
            // #ifdef MP-MPAAS
            let tokenInfo = await this.$cnpcBridge.getUserTokenInfo();
            memberNo = tokenInfo?.memberNo || '';
            // #endif
            // #ifdef MP-MPAAS
            if (actionInfo.type && actionInfo.type == 'oldWeb') {
                actionInfo.pageUrl = actionInfo.url;
                this.$cnpcBridge.openOldWeb(actionInfo);
                return;
            }
            if (actionInfo.type && actionInfo.type == 'web') {
                //跳转3.0的新链接
                let userInfo = await this.$cnpcBridge.getUserTokenInfo();
                let splitStr = `gsmsToken=${userInfo.token3}&gsmsFlag=true&clientCode=${projectConfig.clientCode}&userId=${userInfo.memberNo}`;
                actionInfo.url = actionInfo.url.indexOf('?') > -1 ? `${actionInfo.url}&${splitStr}` : `${actionInfo.url}?${splitStr}`;
            }
            if (actionInfo.type == 'mriver') {
                // 打开内部页面
                //如果当前处于加油小程序中的话，执行路由跳转，如果不是，就调用原生协议去获取
                if (actionInfo.isInnerPage == '1' && this.spaceType != 'my_page' && this.spaceType != 'home_page') {
                    let url = actionInfo.path;
                    let params = {};
                    let type = 'navigateTo';
                    this.$sKit.layer.useRouter(url, params, type);
                    return;
                }
            }
			console.log('测试执行到这里了吗')
            this.$cnpcBridge.openModule(actionInfo);
            // #endif
            // #ifdef MP-ALIPAY
            if (platform.isAlipay) {
                const subPath = projectConfig.subPath || '';
                console.log('actionInfo.type---', actionInfo);
                if (actionInfo.type == 'aliPayMiniprogram') {
                    // 打开内部页面
                    if (actionInfo.isInnerPage == 1) {
                        let url = actionInfo.path;
                        let params = {};
                        let type = 'navigateTo';
                        this.$sKit.layer.useRouter(url, params, type);
                    } else {
                        my.navigateToMiniProgram({
                            appId: actionInfo.id ? actionInfo.id : '',
                            path: actionInfo.path ? actionInfo.path : '',
                            extraData: {},
                            /**
                             *  develop	开发版
                                trial	体验版
                                release	正式版
                            * */
                            envVersion: '',
                            success(res) {
                                // 打开成功
                            },
                        });
                    }
                }
                // 打开web-view页面
                if (actionInfo.type == 'web') {
                    // let url = actionInfo.url;
                    // console.log('actionInfo.url', actionInfo.url);
                    // if (url.includes('openId')) {
                    //     url = url.replace('&openId=', `&openId=${openid}`);
                    // }
                    let splitStr = `gsmsToken=${gsmsToken}&clientCode=${projectConfig.clientCode}&userId=${memberNo}`;
                    let url = actionInfo.url.indexOf('?') > -1 ? `${actionInfo.url}&${splitStr}` : `${actionInfo.url}?${splitStr}`;
                    uni.navigateTo({
                        url: `${subPath}/packages/web-view/pages/home/<USER>
                    });
                }
                // 打开web-view页面
                if (actionInfo.type == 'aliPayWeb') {
                    let url = actionInfo.url;
                    my.ap.openURL({
                        url: url,
                    });
                    return;
                }
            }
            // #endif
            // #ifdef MP-WEIXIN
            if (actionInfo.type == 'weChatMiniprogram') {
                // 打开内部页面
                if (actionInfo.isInnerPage == '1') {
                    let url = actionInfo.path;
                    let params = {};
                    let type = 'navigateTo';
                    this.$sKit.layer.useRouter(url, params, type);
                } else {
                    let params = {
                        appId: actionInfo.id ? actionInfo.id : '',
                        path: actionInfo.path ? actionInfo.path : '',
                        extraData: {},
                        /**
                         *  develop	开发版
                         trial	体验版
                         release	正式版
                         * */
                        envVersion: '',
                    };
                    if (actionInfo.hasOwnProperty('openEmbedded') && actionInfo.openEmbedded == '1') {
                        //挑战半屏小程序
                        wx.openEmbeddedMiniProgram({
                            ...params,
                            success(res) {
                                // 打开成功
                            },
                        });
                    } else {
                        wx.navigateToMiniProgram({
                            ...params,
                            success(res) {
                                // 打开成功
                            },
                        });
                    }
                }
            }
            // 打开web-view页面
            if (actionInfo.type == 'web') {
                let url = '';
                console.log('actionInfo.url', actionInfo.url);
                let splitStr = `gsmsToken=${gsmsToken}&clientCode=${projectConfig.clientCode}&userId=${memberNo}`;
                if (actionInfo.url.indexOf('?') > -1) {
                    url = `${actionInfo.url}&${splitStr}`;
                } else {
                    url = `${actionInfo.url}?${splitStr}`;
                }
                if (url.includes('openId')) {
                    url = url.replace('&openId=', `&openId=${openid}`);
                }
                uni.navigateTo({
                    url: `/packages/web-view/pages/home/<USER>
                });
            }
            // #endif
            // #ifdef H5-CLOUD
            if (actionInfo.type == 'web') {
                let splitStr = `gsmsToken=${gsmsToken}&gsmsFlag=true&clientCode=${projectConfig.clientCode}&userId=${memberNo}`;
                let url = `${actionInfo.url}&${splitStr}`;
                console.log('actionInfo.url', url);
                upsdk.pluginReady(function () {
                    upsdk.createWebView({
                        url: url,
                        isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                    });
                });
            } else if (actionInfo.type == 'mriver') {
                // 打开内部页面
                if (actionInfo.isInnerPage == '1') {
                    let url = actionInfo.path;
                    let params = {};
                    let type = 'navigateTo';
                    this.$sKit.layer.useRouter(url, params, type);
                }
            } else {
                return;
            }
            // #endif
            if (this.marketType == 'screenMask') {
                let groupId = this.bannerList[0].logExtInfo.find(item2 => item2.key == 'groupId').value;
                let key = memberNo + '_' + this.spaceType;
                let res = uni.getStorageSync(key);
                let newMarketFatigue = JSON.parse(JSON.stringify(res));
                newMarketFatigue.CLOSE_AFTER_CLICK.consumed += 1;
                uni.setStorageSync(key, newMarketFatigue);
                this.closeScreenMask();
            }
        },
        closeScreenMask() {
            this.$refs.popDialogFlag.close();
            this.$emit('isPageH', false);
        },

        getLocation() {
            // #ifdef MP-MPAAS
            return new Promise(async (resolve, reject) => {
                this.$cnpcBridge.checkPermission().then(res => {
                    if (res.appStatus) {
                        this.$cnpcBridge.getLocation(res => {
                            if (res.cityCode) {
                                resolve(res.cityCode);
                            } else {
                                resolve('');
                            }
                        });
                    } else {
                        resolve('');
                    }
                });
            });
            // #endif
        },
    },
    computed: {
        ...mapState({
            mapCenterLatV3: state => state.locationV3_app.mapCenterLatV3,
            mapCenterLonV3: state => state.locationV3_app.mapCenterLonV3,
            cityCode: state => state.locationV3_app.cityCode,
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
};
</script>
<style scoped lang="scss">
.img-div {
    // height: 100%;

    .swiper-content {
        position: relative;

        .center-module-swiper {
            width: 100%;
            height: 100px;

            .swiper-item {
                width: 100%;
                height: 100px;

                .swiper-img {
                    width: 100%;
                    height: 100px;
                }
            }
        }

        .swiper-dot-wrap {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: center;
            position: absolute;
            bottom: 20rpx;

            .swiper-dot {
                // 指示点元素默认样式
                width: 4px !important;
                height: 4px !important;
                background: #ffffff !important;
                border-radius: 2px !important;
                opacity: 0.4 !important;
                margin-left: 5rpx;
            }

            .swiper-dot-active {
                // 指示点元素激活（当前选中）状态样式
                width: 12px !important;
                height: 4px !important;
                background: #ffffff !important;
                border-radius: 2px !important;
                margin-left: 5rpx;
            }
        }
    }

    .rubik-content {
        width: 100%;

        .swiper-content {
            width: 173.5px;
            position: relative;

            .center-module-swiper {
                width: 100%;
                height: 90px;

                .swiper-item {
                    width: 100%;
                    height: 100%;

                    .swiper-img {
                        width: 173.5px;
                        height: 90px;
                    }
                }
            }

            .swiper-dot-wrap {
                display: flex;
                align-items: center;
                width: 100%;
                justify-content: center;
                position: absolute;
                bottom: 15rpx;

                .swiper-dot {
                    // 指示点元素默认样式
                    width: 4px !important;
                    height: 4px !important;
                    background: #ffffff !important;
                    border-radius: 2px !important;
                    opacity: 0.4 !important;
                    margin-left: 5rpx;
                }

                .swiper-dot-active {
                    // 指示点元素激活（当前选中）状态样式
                    width: 12px !important;
                    height: 4px !important;
                    background: #ffffff !important;
                    border-radius: 2px !important;
                    margin-left: 5rpx;
                }
            }
        }

        .swiper-content-bottom {
            margin-bottom: 12px;
        }

        // img {
        //     width: 167px;
        //     height: 90px;
        //     border-radius: 10px;
        //     position: relative;
        // }
        // img:nth-of-type(2) {
        //     margin-left: 10px;
        // }
    }

    .mask {
        ._modal {
            flex: none;
            width: 345px;
            overflow: hidden;

            .swiper-content {
                width: 300px;
                min-height: 340px;
                position: relative;
                margin: auto auto;

                .center-module-swiper {
                    width: 100%;
                    min-height: 340px;

                    .swiper-item {
                        width: 100%;
                        height: 100%;

                        .swiper-img {
                            width: 300px;
                            min-height: 340px;
                            border-radius: 10px;
                        }
                    }
                }

                .swiper-dot-wrap {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    justify-content: center;
                    position: absolute;
                    bottom: 15rpx;

                    .swiper-dot {
                        // 指示点元素默认样式
                        width: 4px !important;
                        height: 4px !important;
                        background: #ffffff !important;
                        border-radius: 2px !important;
                        opacity: 0.4 !important;
                        margin-left: 5rpx;
                    }

                    .swiper-dot-active {
                        // 指示点元素激活（当前选中）状态样式
                        width: 12px !important;
                        height: 4px !important;
                        background: #ffffff !important;
                        border-radius: 2px !important;
                        margin-left: 5rpx;
                    }
                }
            }

            .close-tbn-3 {
                position: relative;
                z-index: 10;
                margin-top: 30px;
                display: flex;
                justify-content: center;

                img {
                    display: block;
                    width: 25px;
                    height: 25px;
                }
            }
        }
    }
    .middle-content-wrap {
        margin-top: -30px;
        z-index: 10;
        width: 100%;
        .middle-content {
            padding: 10px 20px 10px 12px;
            .img {
                width: 60px;
                height: 60px;
            }
            .text {
                margin-left: 13px;
                .text1 {
                    margin-bottom: 5px;
                }
            }
            .redeemNow {
                width: 78px;
                height: 27px;
                background: linear-gradient(90deg, #ff3e00 0%, #ff7b33 100%);
                box-shadow: 0px 1 10px 0px rgba(0, 0, 0, 0.07);
                border-radius: 4px;
                line-height: 27px;
            }
        }
    }
}
</style>
