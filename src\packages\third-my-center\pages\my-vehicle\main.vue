<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar title="我的车辆" :border-bottom="false"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <zj-pull-down-refresh
                    @refreshPullDown="refreshPullDown"
                    :showEmpty="showEmpty"
                    :emptyImage="require('../../image/kt5cl.png')"
                    emptyText="您目前还没有添加车辆"
                    ref="pullDownRefreshRef"
                >
                    <div class="content-box">
                        <div class="vehicleWrap" v-for="item in vehicleList" :key="item.licensePlate">
                            <div class="vehicle-item-left" @click="vehicleStorage(item)">
                                <div class="licensePlateDefault">
                                    <div class="licensePlate">{{ item.licensePlate }}</div>
                                    <div class="imgWrap" v-if="item.defaultVehicle">
                                        <img src="../../image/default.png" alt />
                                    </div>
                                </div>
                                <div class="amount" v-if="item.refuelAmountMax && item.vehicleType !== 1">
                                    单次最大加油金额：{{ Number(item.refuelAmountMax).toFixed(2) || 0 }}元
                                </div>
                            </div>
                            <div class="vehicle-item-right" @click="editAndAddCar(2, item)">
                                <div v-if="item.vehicleType !== 1" class="oilType">油品型号 {{ fuelDaat(item.fuelType) || '' }}</div>
                                <div class="symbol">
                                    <img src="../../image/right.png" alt />
                                </div>
                            </div>
                        </div>
                        <div class="tip-div" v-if="vehicleList.length >= 3">注：每位用户最多绑定三张车牌</div>
                    </div>
                </zj-pull-down-refresh>
                <div
                    class="addcar btn primary-btn border-rad-8 shad-ef color-fff"
                    @click="editAndAddCar(1, item)"
                    :class="{ gray: gray, 'addcar-fixed': true }"
                >
                    <div class="btn primary-btn border-rad-8 shad-ef color-fff">添加车辆</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { plateList, plateFuelInfo } from '../../../../s-kit/js/v3-http/https3/vehicle/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    computed: {
        //超过3辆添加车辆按钮置灰
        gray() {
            return this.vehicleList.length >= 3;
        },
        // 车辆数据为空时按钮置底
        addcarFixed() {
            return this.vehicleList.length == 0;
        },
    },
    name: '',
    data() {
        return {
            // 油品数组
            oilNumArr: [],
            // 车辆数组
            vehicleList: [],
            // 添加修改或删除后返回页面是否刷新列表标识
            refreshListFlag: false,
            // 列表没数据时变为true显示暂无数据图片
            showEmpty: false,
            // 列表没数据时的图片
            noPicture: require('../../image/kt5cl.png'),
            // uni.navigateBack 需要的参数
            prveDataObject: {
                refreshListFlag: false,
            },
            charge: '',
        };
    },
    onShow(options) {
        // 返回后refreshListFlag未true执行刷新列表标识
        // if (this.prveDataObject.refreshListFlag) {
        // 获取车辆列表
        this.getCarList();
        // }
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            console.log(params, '接受到网电参数了吗');
            this.charge = params?.source;
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'carPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
        // 获取车辆列表
        // this.getCarList()
        // 获取油品信息
        this.enumListPost();
    },
    methods: {
        /**
         * @description  : 获取车辆列表
         * @return        {*}
         */
        getCarList() {
			// 充电小程序取的车牌信息 只取默认车辆信息；
			// 不做油电区分
            let params = {
                newEnergyFlag:  '',
            };
            plateList(params).then(res => {
                if (res && res.success) {
                    // 返回当前页面刷新接口请求成功后置为false
                    this.prveDataObject.refreshListFlag;
                    this.vehicleList = res.data.vehicleList || [];
                    this.$refs.pullDownRefreshRef.stopRefresh();
                }
                if (this.vehicleList.length > 0) {
                    this.showEmpty = false;
                } else {
                    this.showEmpty = true;
                }
            });
        },

        /**
         * @description  : 存储车辆
         * @return        {*}
         */
        vehicleStorage(item) {
            if (!this.charge) return;
            uni.setStorageSync('vehicle', JSON.stringify(item));
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            this.getCarList();
        },
        /**
         * @description  : 获取油品数据
         * @return        {*}
         */
        enumListPost() {
            let p3 = new Promise((resolve, reject) => {
                plateFuelInfo().then(res => {
                    if (res.success == true) {
                        this.oilNumArr = res.data;
                    }
                    resolve('oilNumArr');
                });
            });
        },
        /**
         * @description  : 油品类型对应获取 显示在页面上
         * @param         {*} item:
         * @return        {*}
         */
        fuelDaat(item) {
            let type;
            if (this.oilNumArr.length > 0) {
                for (let index = 0; index < this.oilNumArr.length; index++) {
                    if (item == this.oilNumArr[index].fuelType) return (type = this.oilNumArr[index].fuelName);
                }
            }
        },
        /**
         * @description  : 编辑车辆
         * @param         {*} val:
         * @param         {*} item:
         * @return        {*}
         */
        editCar(val, item) {
            let url = '/packages/third-my-center/pages/my-vehicle-detail/main';
            let params = {
                type: val,
                items: item,
            };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 添加与编辑事件
         * @param         {*} val:1新增 2 编辑
         * @param         {*} item:列表数据
         * @return        {*}
         */
        editAndAddCar(val, item) {
            if (val == 1 && this.vehicleList.length >= 3) {
                return;
            } else {
                let url = '/packages/third-my-center/pages/my-vehicle-detail/main';
                let params = {
                    type: val,
                    items: item,
					source: this.charge,
                };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
    },
};
</script>
<style scoped lang="scss">
.content-box {
    padding: 15px;

    .vehicleWrap {
        width: 100%;
        margin: 10px 0;
        height: 65px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 26rpx 21rpx 26rpx 30rpx;
        border-radius: 8px;
        box-sizing: border-box;
        overflow: hidden;

        .vehicle-item-left {
            display: flex;
            flex-direction: column;

            // margin-left: 15px;
            .licensePlateDefault {
                display: flex;
                justify-content: flex-start;

                .licensePlate {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }

                .imgWrap {
                    width: 30px;
                    height: 15px;
                    margin-left: 15px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .amount {
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 33rpx;
                margin-top: 5rpx;
            }
        }

        .vehicle-item-right {
            display: flex;
            align-items: center;

            // margin-right: 10px;
            .oilType {
                height: 17px;
                font-size: 12px;
                font-weight: 400;
                color: #999999;
                line-height: 17px;
                margin-right: 5px;
            }

            .symbol {
                width: 16px;
                height: 16px;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    .tip-div {
        text-align: center;
        line-height: 1.5;
        color: #999;
        font-size: 12px;
    }
}

.addcar {
    text-align: center;
    // padding: 12px 0px;
    border-radius: 8px;
    height: 44px;
    line-height: 44px;
    margin: 10px auto 0;

    .addcar-text {
        font-weight: 500;
        color: #ffffff;
        line-height: 21px;
        font-size: 15px;
    }
}

.gray {
    // margin: 10px 15px 0px;
    // text-align: center;
    // padding: 12px 0px;
    // border-radius: 8px;
    // background-color: #d6d6d6;
    opacity: 0.3;
}

.addcar-fixed {
    position: fixed;
    bottom: calc(20px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    margin: 10px 15px 0px;
    text-align: center;
    // padding: 12px 0px;
    // background-color: #e64f22;
    border-radius: 8px;
    z-index: 20;
}
</style>
