// #ifdef MP-WEIXIN
const Plugin = requirePlugin('pay-plugin');
import { mapState, mapGetters } from 'vuex';
export default {
    onLoad() {
        this.$sKit.wxPayPlugin.initPayPlugin();
    },
    methods: {
        // 确认支付
        async callPlugIn(paramsData) {
            let params = {
                /**
                 * 发起充值支付
                 * @param areaCode 地区编码
                 * @param bizOrderNo 业务订单编号
                 * @param rcvAmt 应收总金额
                 * @param realAmt 支付金额
                 */
                areaCode: paramsData.areaCode,
                bizOrderNo: paramsData.bizOrderNo,
                rcvAmt: Number(paramsData.rcvAmt),
                realAmt: Number(paramsData.realAmt),
                // 3.0.4风控字段
                extendFiled: JSON.stringify({
                    dfp: '',
                    gps:
                        this.riskManagementLonV3 && this.riskManagementLatV3
                            ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                            : '',
                    gpsProvince: '',
                    gpsCity: '',
                    gpsArea: '',
                }),
                openId: this.openId,
            };
            const res = await Plugin.RechargePay(params);
            if (res.code === 'PAY_SUCCESS') {
                // 只有微信逻辑，后续同名方法或条件编译
                this.callWechatCashier(res.data, params.bizOrderNo);
            } else {
                // 截取字符串后面的数据
                this.isCanPay = true;
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        // 拉起微信收银台
        callWechatCashier(data, bizOrderNo) {
            console.log(data, bizOrderNo, 'data, bizOrderNo');
            // 调用微信支付
            wx.requestPayment({
                appId: data.appId,
                provider: 'wxpay',
                timeStamp: data.timestamp,
                nonceStr: data.nonceStr,
                package: 'prepay_id=' + data.prepayId,
                signType: 'RSA',
                paySign: data.sign,
                success: () => {
                    // 新增触发插件方法
                    Plugin.QueryOrder({
                        bizOrderNo: bizOrderNo,
                        payOrderNo: data.payOrderNo,
                        bizModel: '8',
                    });
                    console.log('充值加快查询结果插件参数', data.payOrderNo, bizOrderNo);
                    this.$sKit.layer.useRouter(
                        '/packages/third-remaining-sum/pages/third-charge-result/main',
                        { orderId: bizOrderNo, payType: 'orderPay', refer: 'r26', addressName: this.walletInfo.addressName },
                        'redirectTo',
                    );
                },
                fail: err => {
                    this.isCanPay = true;
                    console.log(err, 'errerrerr');
                },
            });
        },
    },
    computed: {
        ...mapGetters(['openId']),
        ...mapState({
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
        }),
    },
};
// #endif
