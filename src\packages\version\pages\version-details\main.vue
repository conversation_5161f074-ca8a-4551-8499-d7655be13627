<template>
    <div class="version-details">
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :back-icon-size="40"
            :height="44"
            back-text="版本说明"
            :back-text-style="pageConfig.titleStyle"
        >
        </u-navbar>
        <div class="version-detail-content">
            <u-parse :html="detailsContent" :show-with-animation="true"></u-parse>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
export default {
    data() {
        return {
            detailsContent: null,
        };
    },
    onLoad(options) {
        this.handleResolveOptions(options);
    },
    methods: {
        handleResolveOptions(options) {
            let content;
            if (typeof (content = options.content) !== 'undefined') {
                this.detailsContent = decodeURIComponent(content);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.version-details {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    .version-detail-content {
        flex: 1;
        min-height: 0;
        overflow: scroll;
        padding: 15px;
        /** 下面为富文本全局样式 */
        font-size: 16px;
        color: #3d3d3d;
        line-height: 26px;
    }
}
</style>
