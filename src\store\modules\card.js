import { cardListPost, getCardDetailApi, setDefaultCardApi, getDefaultCardStatistics, unBinding } from '@/api/home.js';
import { getOilCardSubmitPost } from '@/api/my-center';
import { throttleUtil } from '@/utils/index.js';
// 将数组中对应index的item放到数组第一位
const arrTopping = (arr, index) => {
    if (index > 0) {
        let item = arr.splice(index, 1);
        arr.unshift(...item);
    }
};

// 获取卡全地址
const getCardAddress = data => {
    return ['北京市', '上海市', '重庆市', '天津市'].includes(data.cityName) ? data.cityName : `${data.provinceName} ${data.cityName}`;
};

// 获取卡详情方法 index 不传就获取全部，传值则获取
const getCardDetail = async (list, commit, index = 'all') => {
    if (index == 'all') {
        let isHave = false;
        for (let i = 0; i < list.length; i++) {
            let item = list[i];
            let { data } = await getCardDetailApi({ cardNo: item.cardNo });
            Object.assign(item, data);
            item.activeed = false;
            console.log(item.activeed, 'item.activeed');
            item.allAddress = getCardAddress(data);
            item.afterNum = item.cadNo.substring(item.cardNo.length - 4, item.cardNo.length);
            if (item.cardType == 0) {
                isHave = true;
            }
        }
        commit('setIsHaveEntityCard', isHave);
    } else {
        let item = list[index];
        console.log(item.cardNo, 'item.cardNo');
        let { data } = await getCardDetailApi({ cardNo: item.cardNo });
        Object.assign(item, data);
        item.allAddress = getCardAddress(data);
        item.afterNum = item.cardNo.substring(item.cardNo.length - 4, item.cardNo.length);
        if (item.cardType == 0) {
            commit('setIsHaveEntityCard', true);
        }
    }
    commit('setCardList', list);
};

// 获取默认卡信息
const getCardDefaultInfo = async commit => {
    let { data } = await getDefaultCardStatistics();
    commit('setCardTopinfo', data);
};

export default {
    namespaced: true,
    state: {
        cardList: [], // 卡数组
        isHaveEntityCard: false, // 是否含有实体卡
        isHaveECard: false, // 是否含有电子卡
        cardTopinfo: {}, // 获取置顶卡信息
        // 卡充值时小眼睛状态控制
    },
    mutations: {
        setCardList(state, cardList) {
            state.cardList = cardList;
        },
        setIsHaveEntityCard(state, isHaveEntityCard) {
            console.log('实体卡还存在吗');
            state.isHaveEntityCard = isHaveEntityCard;
        },
        setIsHaveECard(state, isHaveECard) {
            state.isHaveECard = isHaveECard;
        },
        setCardTopinfo(state, cardTopinfo) {
            state.cardTopinfo = cardTopinfo;
        },
    },
    actions: {
        // 获取全部卡数据
        async getAllCardList({ state, commit, dispatch }) {
            console.log('1234567890');
            let listRes = await cardListPost();
            console.log(listRes.data.length, '======listRes');
            let cardList = listRes.data || [];
            if (listRes.data.length > 0) {
                //在这里设置下是否包含电子卡或者实体卡
                //初始都设置为false
                commit('setIsHaveEntityCard', false);
                commit('setIsHaveECard', false);
                cardList.map(item => {
                    // 向卡列表中添加 activeed ：false
                    let key = 'activeed';
                    let value = false;
                    item[key] = value;
                    item.cardType == 0 ? commit('setIsHaveEntityCard', true) : commit('setIsHaveECard', true);
                });
                let defaultCardIndex = cardList.findIndex(item => item.isDefaultCard == true);
                // 将设置为默认的卡置顶
                arrTopping(cardList, defaultCardIndex);
                commit('setCardList', cardList);
            } else {
                commit('setCardList', cardList);
                console.log(cardList, 'FFFFFF');
            }

            // await getCardDefaultInfo(commit)
        },
        // 更新对应卡数据
        async uploadCardDetail({ state, commit, dispatch }, cardIndex) {
            await getCardDetail(state.cardList, commit, cardIndex);
        },
        // 设置默认卡
        async setDefaultCard({ state, commit, dispatch }, cardIndex) {
            let item = state.cardList[cardIndex];
            let params = {
                cardNo: item.cardNo,
                isDefaultCard: item.isDefaultCard ? '1' : '0',
            };
            await setDefaultCardApi(params);
            if (item.isDefaultCard) {
                let list = JSON.parse(JSON.stringify(state.cardList));
                list.map((listItem, listIndex) => {
                    if (listIndex == cardIndex) {
                        return listItem;
                    } else {
                        listItem.isDefaultCard = false;
                        return listItem;
                    }
                });
                arrTopping(list, cardIndex);
                commit('setCardList', list);
            }
            await getCardDefaultInfo(commit);
        },
        // 获取默认卡的信息
        async getDefaultCardInfo({ state, commit, dispatch }) {
            await getCardDefaultInfo(commit);
        },
        // 绑定油卡
        async addCard({ state, commit, dispatch }, params) {
            let res = await getOilCardSubmitPost(params);
            await dispatch('getAllCardList');
            return res;
        },
        // 解绑油卡
        async unBindCard({ state, commit, dispatch }, cardIndex) {
            let item = state.cardList[cardIndex];
            let params = {
                // idName: item.name,
                cardNo: item.cardNo,
            };
            await unBinding(params);
            await dispatch('getAllCardList');
        },
        // 设置信息是否隐藏
        async setCardActiveed({ state, commit }, index) {
            console.log('我走了card.js', state.cardList);
            let item = state.cardList[index];
            console.log(item, 'item.activeed');
            if (!item.activeed) {
                let detail = await getCardDetail(state.cardList, commit, index);
                //先判断有没有详情
                if (!item.balance) {
                    //没有详情  获取卡详情
                    // let detail = await getCardDetail(state.cardList, commit, index)
                }
            }
            item.activeed = !item.activeed;
            state.cardList = state.cardList;
        },
        async refresh({ state, commit }, index) {
            await getCardDetail(state.cardList, commit, index);
        },
    },
    getters: {
        cardList: state => state.cardList,
    },
};
