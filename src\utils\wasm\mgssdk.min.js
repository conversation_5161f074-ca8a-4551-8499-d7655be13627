/*! For license information please see mgssdk.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.MP=e():t.MP=e()}(self,(()=>(()=>{var t={8683:function(t,e,r){var n,o;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}n=function(){var e,n;if("object"==("undefined"==typeof WXWebAssembly?"undefined":i(WXWebAssembly))){e=WXWebAssembly;var o=getApp();n=o?o.globalData:{},e.RuntimeError=function(t){return"Runtime Error: "+t}}else e=n.WebAssembly;var a,s,c,l,u=void 0!==u?u:{},f=Object.assign({},u),h=[],d="./this.program",p="object"==i(n),_="function"==typeof importScripts,m="object"==("undefined"==typeof process?"undefined":i(process))&&"object"==i(process.versions)&&"string"==typeof process.versions.node,g="";m?(g=_?r(8625).dirname(g)+"/":"//",c=r(427),l=r(8625),a=function(t,e){return t=l.normalize(t),c.readFileSync(t,e?void 0:"utf8")},s=function(t,e,r){t=l.normalize(t),c.readFile(t,(function(t,n){t?r(t):e(n.buffer)}))},process.argv.length>1&&(d=process.argv[1].replace(/\\/g,"/")),h=process.argv.slice(2),t.exports=u,process.on("uncaughtException",(function(t){if(!(t instanceof tt))throw t})),process.on("unhandledRejection",(function(t){throw t})),u.inspect=function(){return"[Emscripten Module object]"}):(p||_)&&(_?g=self.location.href:"undefined"!=typeof document&&document.currentScript&&(g=document.currentScript.src),g=0!==g.indexOf("blob:")?g.substr(0,g.replace(/[?#].*/,"").lastIndexOf("/")+1):"",a=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},s=function(t,e,r){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?e(n.response):r()},n.onerror=r,n.send(null)});var v=u.print||console.log.bind(console),w=u.printErr||console.warn.bind(console);Object.assign(u,f),f=null,u.arguments&&(h=u.arguments),u.thisProgram&&(d=u.thisProgram),u.quit&&u.quit;var y,b=4;u.wasmBinary&&(y=u.wasmBinary),u.noExitRuntime,"object"!=i(e)&&q("no native wasm support detected");var k=!1;var E,x,A,S,B,z,D,R,T,F="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function C(t,e,r){for(var n=e+r,o=e;t[o]&&!(o>=n);)++o;if(o-e>16&&t.buffer&&F)return F.decode(t.subarray(e,o));for(var i="";e<o;){var a=t[e++];if(128&a){var s=63&t[e++];if(192!=(224&a)){var c=63&t[e++];if((a=224==(240&a)?(15&a)<<12|s<<6|c:(7&a)<<18|s<<12|c<<6|63&t[e++])<65536)i+=String.fromCharCode(a);else{var l=a-65536;i+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}function M(t,e){return t?C(A,t,e):""}function P(t,e,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<t.length;++a){var s=t.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&t.charCodeAt(++a)),s<=127){if(r>=i)break;e[r++]=s}else if(s<=2047){if(r+1>=i)break;e[r++]=192|s>>6,e[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;e[r++]=224|s>>12,e[r++]=128|s>>6&63,e[r++]=128|63&s}else{if(r+3>=i)break;e[r++]=240|s>>18,e[r++]=128|s>>12&63,e[r++]=128|s>>6&63,e[r++]=128|63&s}}return e[r]=0,r-o}function H(t,e,r){return P(t,A,e,r)}function O(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n<=127?e++:n<=2047?e+=2:n>=55296&&n<=57343?(e+=4,++r):e+=3}return e}u.INITIAL_MEMORY;var U,N=[],I=[],j=[];var L=0,W=null,Z=null;function K(t){L++,u.monitorRunDependencies&&u.monitorRunDependencies(L)}function X(t){if(L--,u.monitorRunDependencies&&u.monitorRunDependencies(L),0==L&&(null!==W&&(clearInterval(W),W=null),Z)){var e=Z;Z=null,e()}}function q(t){throw u.onAbort&&u.onAbort(t),w(t="Aborted("+t+")"),k=!0,t+=". Build with -sASSERTIONS for more info.",new e.RuntimeError(t)}var G,V,$,Y,J="data:application/octet-stream;base64,";function Q(t){return t.startsWith(J)}function tt(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function et(t){for(;t.length>0;)t.shift()(u)}function rt(t){this.excPtr=t,this.ptr=t-24,this.set_type=function(t){D[this.ptr+4>>2]=t},this.get_type=function(){return D[this.ptr+4>>2]},this.set_destructor=function(t){D[this.ptr+8>>2]=t},this.get_destructor=function(){return D[this.ptr+8>>2]},this.set_refcount=function(t){z[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,x[this.ptr+12>>0]=t},this.get_caught=function(){return 0!=x[this.ptr+12>>0]},this.set_rethrown=function(t){t=t?1:0,x[this.ptr+13>>0]=t},this.get_rethrown=function(){return 0!=x[this.ptr+13>>0]},this.init=function(t,e){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=z[this.ptr>>2];z[this.ptr>>2]=t+1},this.release_ref=function(){var t=z[this.ptr>>2];return z[this.ptr>>2]=t-1,1===t},this.set_adjusted_ptr=function(t){D[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return D[this.ptr+16>>2]},this.get_exception_ptr=function(){if(he(this.get_type()))return D[this.excPtr>>2];var t=this.get_adjusted_ptr();return 0!==t?t:this.excPtr}}Q(G="utils/wasm/gwcli1-0-2.wasm.br")||(Y=G,G=u.locateFile?u.locateFile(Y,g):g+Y);var nt={isAbs:function(t){return"/"===t.charAt(0)},splitPath:function(t){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(t).slice(1)},normalizeArray:function(t,e){for(var r=0,n=t.length-1;n>=0;n--){var o=t[n];"."===o?t.splice(n,1):".."===o?(t.splice(n,1),r++):r&&(t.splice(n,1),r--)}if(e)for(;r;r--)t.unshift("..");return t},normalize:function(t){var e=nt.isAbs(t),r="/"===t.substr(-1);return(t=nt.normalizeArray(t.split("/").filter((function(t){return!!t})),!e).join("/"))||e||(t="."),t&&r&&(t+="/"),(e?"/":"")+t},dirname:function(t){var e=nt.splitPath(t),r=e[0],n=e[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(t){if("/"===t)return"/";var e=(t=(t=nt.normalize(t)).replace(/\/$/,"")).lastIndexOf("/");return-1===e?t:t.substr(e+1)},join:function(){var t=Array.prototype.slice.call(arguments);return nt.normalize(t.join("/"))},join2:function(t,e){return nt.normalize(t+"/"+e)}};var ot={resolve:function(){for(var t="",e=!1,r=arguments.length-1;r>=-1&&!e;r--){var n=r>=0?arguments[r]:ut.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";t=n+"/"+t,e=nt.isAbs(n)}return(e?"/":"")+(t=nt.normalizeArray(t.split("/").filter((function(t){return!!t})),!e).join("/"))||"."},relative:function(t,e){function r(t){for(var e=0;e<t.length&&""===t[e];e++);for(var r=t.length-1;r>=0&&""===t[r];r--);return e>r?[]:t.slice(e,r-e+1)}t=ot.resolve(t).substr(1),e=ot.resolve(e).substr(1);for(var n=r(t.split("/")),o=r(e.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var c=[];for(s=a;s<n.length;s++)c.push("..");return(c=c.concat(o.slice(a))).join("/")}};function it(t,e,r){var n=r>0?r:O(t)+1,o=new Array(n),i=P(t,o,0,o.length);return e&&(o.length=i),o}var at={ttys:[],init:function(){},shutdown:function(){},register:function(t,e){at.ttys[t]={input:[],output:[],ops:e},ut.registerDevice(t,at.stream_ops)},stream_ops:{open:function(t){var e=at.ttys[t.node.rdev];if(!e)throw new ut.ErrnoError(43);t.tty=e,t.seekable=!1},close:function(t){t.tty.ops.fsync(t.tty)},fsync:function(t){t.tty.ops.fsync(t.tty)},read:function(t,e,r,n,o){if(!t.tty||!t.tty.ops.get_char)throw new ut.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=t.tty.ops.get_char(t.tty)}catch(t){throw new ut.ErrnoError(29)}if(void 0===s&&0===i)throw new ut.ErrnoError(6);if(null==s)break;i++,e[r+a]=s}return i&&(t.node.timestamp=Date.now()),i},write:function(t,e,r,n,o){if(!t.tty||!t.tty.ops.put_char)throw new ut.ErrnoError(60);try{for(var i=0;i<n;i++)t.tty.ops.put_char(t.tty,e[r+i])}catch(t){throw new ut.ErrnoError(29)}return n&&(t.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(t){if(!t.input.length){var e=null;if(m){var r=Buffer.alloc(256),o=0;try{o=c.readSync(process.stdin.fd,r,0,256,-1)}catch(t){if(!t.toString().includes("EOF"))throw t;o=0}e=o>0?r.slice(0,o).toString("utf-8"):null}else void 0!==n&&"function"==typeof n.prompt?null!==(e=n.prompt("Input: "))&&(e+="\n"):"function"==typeof readline&&null!==(e=readline())&&(e+="\n");if(!e)return null;t.input=it(e,!0)}return t.input.shift()},put_char:function(t,e){null===e||10===e?(v(C(t.output,0)),t.output=[]):0!=e&&t.output.push(e)},fsync:function(t){t.output&&t.output.length>0&&(v(C(t.output,0)),t.output=[])}},default_tty1_ops:{put_char:function(t,e){null===e||10===e?(w(C(t.output,0)),t.output=[]):0!=e&&t.output.push(e)},fsync:function(t){t.output&&t.output.length>0&&(w(C(t.output,0)),t.output=[])}}};function st(t){t=function(t,e){return Math.ceil(t/e)*e}(t,65536);var e=fe(65536,t);return e?function(t,e){return A.fill(0,t,t+e),t}(e,t):0}var ct={ops_table:null,mount:function(t){return ct.createNode(null,"/",16895,0)},createNode:function(t,e,r,n){if(ut.isBlkdev(r)||ut.isFIFO(r))throw new ut.ErrnoError(63);ct.ops_table||(ct.ops_table={dir:{node:{getattr:ct.node_ops.getattr,setattr:ct.node_ops.setattr,lookup:ct.node_ops.lookup,mknod:ct.node_ops.mknod,rename:ct.node_ops.rename,unlink:ct.node_ops.unlink,rmdir:ct.node_ops.rmdir,readdir:ct.node_ops.readdir,symlink:ct.node_ops.symlink},stream:{llseek:ct.stream_ops.llseek}},file:{node:{getattr:ct.node_ops.getattr,setattr:ct.node_ops.setattr},stream:{llseek:ct.stream_ops.llseek,read:ct.stream_ops.read,write:ct.stream_ops.write,allocate:ct.stream_ops.allocate,mmap:ct.stream_ops.mmap,msync:ct.stream_ops.msync}},link:{node:{getattr:ct.node_ops.getattr,setattr:ct.node_ops.setattr,readlink:ct.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ct.node_ops.getattr,setattr:ct.node_ops.setattr},stream:ut.chrdev_stream_ops}});var o=ut.createNode(t,e,r,n);return ut.isDir(o.mode)?(o.node_ops=ct.ops_table.dir.node,o.stream_ops=ct.ops_table.dir.stream,o.contents={}):ut.isFile(o.mode)?(o.node_ops=ct.ops_table.file.node,o.stream_ops=ct.ops_table.file.stream,o.usedBytes=0,o.contents=null):ut.isLink(o.mode)?(o.node_ops=ct.ops_table.link.node,o.stream_ops=ct.ops_table.link.stream):ut.isChrdev(o.mode)&&(o.node_ops=ct.ops_table.chrdev.node,o.stream_ops=ct.ops_table.chrdev.stream),o.timestamp=Date.now(),t&&(t.contents[e]=o,t.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(t){return t.contents?t.contents.subarray?t.contents.subarray(0,t.usedBytes):new Uint8Array(t.contents):new Uint8Array(0)},expandFileStorage:function(t,e){var r=t.contents?t.contents.length:0;if(!(r>=e)){e=Math.max(e,r*(r<1048576?2:1.125)>>>0),0!=r&&(e=Math.max(e,256));var n=t.contents;t.contents=new Uint8Array(e),t.usedBytes>0&&t.contents.set(n.subarray(0,t.usedBytes),0)}},resizeFileStorage:function(t,e){if(t.usedBytes!=e)if(0==e)t.contents=null,t.usedBytes=0;else{var r=t.contents;t.contents=new Uint8Array(e),r&&t.contents.set(r.subarray(0,Math.min(e,t.usedBytes))),t.usedBytes=e}},node_ops:{getattr:function(t){var e={};return e.dev=ut.isChrdev(t.mode)?t.id:1,e.ino=t.id,e.mode=t.mode,e.nlink=1,e.uid=0,e.gid=0,e.rdev=t.rdev,ut.isDir(t.mode)?e.size=4096:ut.isFile(t.mode)?e.size=t.usedBytes:ut.isLink(t.mode)?e.size=t.link.length:e.size=0,e.atime=new Date(t.timestamp),e.mtime=new Date(t.timestamp),e.ctime=new Date(t.timestamp),e.blksize=4096,e.blocks=Math.ceil(e.size/e.blksize),e},setattr:function(t,e){void 0!==e.mode&&(t.mode=e.mode),void 0!==e.timestamp&&(t.timestamp=e.timestamp),void 0!==e.size&&ct.resizeFileStorage(t,e.size)},lookup:function(t,e){throw ut.genericErrors[44]},mknod:function(t,e,r,n){return ct.createNode(t,e,r,n)},rename:function(t,e,r){if(ut.isDir(t.mode)){var n;try{n=ut.lookupNode(e,r)}catch(t){}if(n)for(var o in n.contents)throw new ut.ErrnoError(55)}delete t.parent.contents[t.name],t.parent.timestamp=Date.now(),t.name=r,e.contents[r]=t,e.timestamp=t.parent.timestamp,t.parent=e},unlink:function(t,e){delete t.contents[e],t.timestamp=Date.now()},rmdir:function(t,e){var r=ut.lookupNode(t,e);for(var n in r.contents)throw new ut.ErrnoError(55);delete t.contents[e],t.timestamp=Date.now()},readdir:function(t){var e=[".",".."];for(var r in t.contents)t.contents.hasOwnProperty(r)&&e.push(r);return e},symlink:function(t,e,r){var n=ct.createNode(t,e,41471,0);return n.link=r,n},readlink:function(t){if(!ut.isLink(t.mode))throw new ut.ErrnoError(28);return t.link}},stream_ops:{read:function(t,e,r,n,o){var i=t.node.contents;if(o>=t.node.usedBytes)return 0;var a=Math.min(t.node.usedBytes-o,n);if(a>8&&i.subarray)e.set(i.subarray(o,o+a),r);else for(var s=0;s<a;s++)e[r+s]=i[o+s];return a},write:function(t,e,r,n,o,i){if(!n)return 0;var a=t.node;if(a.timestamp=Date.now(),e.subarray&&(!a.contents||a.contents.subarray)){if(i)return a.contents=e.subarray(r,r+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=e.slice(r,r+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(e.subarray(r,r+n),o),n}if(ct.expandFileStorage(a,o+n),a.contents.subarray&&e.subarray)a.contents.set(e.subarray(r,r+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=e[r+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(t,e,r){var n=e;if(1===r?n+=t.position:2===r&&ut.isFile(t.node.mode)&&(n+=t.node.usedBytes),n<0)throw new ut.ErrnoError(28);return n},allocate:function(t,e,r){ct.expandFileStorage(t.node,e+r),t.node.usedBytes=Math.max(t.node.usedBytes,e+r)},mmap:function(t,e,r,n,o){if(!ut.isFile(t.node.mode))throw new ut.ErrnoError(43);var i,a,s=t.node.contents;if(2&o||s.buffer!==E){if((r>0||r+e<s.length)&&(s=s.subarray?s.subarray(r,r+e):Array.prototype.slice.call(s,r,r+e)),a=!0,!(i=st(e)))throw new ut.ErrnoError(48);x.set(s,i)}else a=!1,i=s.byteOffset;return{ptr:i,allocated:a}},msync:function(t,e,r,n,o){return ct.stream_ops.write(t,e,0,n,r,!1),0}}};function lt(t,e,r,n){var o=n?"":"al "+t;s(t,(function(r){var n;n='Loading data file "'+t+'" failed (no arrayBuffer).',r||q(n),e(new Uint8Array(r)),o&&X()}),(function(e){if(!r)throw'Loading data file "'+t+'" failed.';r()})),o&&K()}var ut={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t=ot.resolve(t)))return{path:"",node:null};if((e=Object.assign({follow_mount:!0,recurse_count:0},e)).recurse_count>8)throw new ut.ErrnoError(32);for(var r=t.split("/").filter((function(t){return!!t})),n=ut.root,o="/",i=0;i<r.length;i++){var a=i===r.length-1;if(a&&e.parent)break;if(n=ut.lookupNode(n,r[i]),o=nt.join2(o,r[i]),ut.isMountpoint(n)&&(!a||a&&e.follow_mount)&&(n=n.mounted.root),!a||e.follow)for(var s=0;ut.isLink(n.mode);){var c=ut.readlink(o);if(o=ot.resolve(nt.dirname(o),c),n=ut.lookupPath(o,{recurse_count:e.recurse_count+1}).node,s++>40)throw new ut.ErrnoError(32)}}return{path:o,node:n}},getPath:function(t){for(var e;;){if(ut.isRoot(t)){var r=t.mount.mountpoint;return e?"/"!==r[r.length-1]?r+"/"+e:r+e:r}e=e?t.name+"/"+e:t.name,t=t.parent}},hashName:function(t,e){for(var r=0,n=0;n<e.length;n++)r=(r<<5)-r+e.charCodeAt(n)|0;return(t+r>>>0)%ut.nameTable.length},hashAddNode:function(t){var e=ut.hashName(t.parent.id,t.name);t.name_next=ut.nameTable[e],ut.nameTable[e]=t},hashRemoveNode:function(t){var e=ut.hashName(t.parent.id,t.name);if(ut.nameTable[e]===t)ut.nameTable[e]=t.name_next;else for(var r=ut.nameTable[e];r;){if(r.name_next===t){r.name_next=t.name_next;break}r=r.name_next}},lookupNode:function(t,e){var r=ut.mayLookup(t);if(r)throw new ut.ErrnoError(r,t);for(var n=ut.hashName(t.id,e),o=ut.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===t.id&&i===e)return o}return ut.lookup(t,e)},createNode:function(t,e,r,n){var o=new ut.FSNode(t,e,r,n);return ut.hashAddNode(o),o},destroyNode:function(t){ut.hashRemoveNode(t)},isRoot:function(t){return t===t.parent},isMountpoint:function(t){return!!t.mounted},isFile:function(t){return 32768==(61440&t)},isDir:function(t){return 16384==(61440&t)},isLink:function(t){return 40960==(61440&t)},isChrdev:function(t){return 8192==(61440&t)},isBlkdev:function(t){return 24576==(61440&t)},isFIFO:function(t){return 4096==(61440&t)},isSocket:function(t){return 49152==(49152&t)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(t){var e=ut.flagModes[t];if(void 0===e)throw new Error("Unknown file open mode: "+t);return e},flagsToPermissionString:function(t){var e=["r","w","rw"][3&t];return 512&t&&(e+="w"),e},nodePermissions:function(t,e){return ut.ignorePermissions||(!e.includes("r")||292&t.mode)&&(!e.includes("w")||146&t.mode)&&(!e.includes("x")||73&t.mode)?0:2},mayLookup:function(t){return ut.nodePermissions(t,"x")||(t.node_ops.lookup?0:2)},mayCreate:function(t,e){try{return ut.lookupNode(t,e),20}catch(t){}return ut.nodePermissions(t,"wx")},mayDelete:function(t,e,r){var n;try{n=ut.lookupNode(t,e)}catch(t){return t.errno}var o=ut.nodePermissions(t,"wx");if(o)return o;if(r){if(!ut.isDir(n.mode))return 54;if(ut.isRoot(n)||ut.getPath(n)===ut.cwd())return 10}else if(ut.isDir(n.mode))return 31;return 0},mayOpen:function(t,e){return t?ut.isLink(t.mode)?32:ut.isDir(t.mode)&&("r"!==ut.flagsToPermissionString(e)||512&e)?31:ut.nodePermissions(t,ut.flagsToPermissionString(e)):44},MAX_OPEN_FDS:4096,nextfd:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ut.MAX_OPEN_FDS,r=t;r<=e;r++)if(!ut.streams[r])return r;throw new ut.ErrnoError(33)},getStream:function(t){return ut.streams[t]},createStream:function(t,e,r){ut.FSStream||(ut.FSStream=function(){this.shared={}},ut.FSStream.prototype={},Object.defineProperties(ut.FSStream.prototype,{object:{get:function(){return this.node},set:function(t){this.node=t}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(t){this.shared.flags=t}},position:{get:function(){return this.shared.position},set:function(t){this.shared.position=t}}})),t=Object.assign(new ut.FSStream,t);var n=ut.nextfd(e,r);return t.fd=n,ut.streams[n]=t,t},closeStream:function(t){ut.streams[t]=null},chrdev_stream_ops:{open:function(t){var e=ut.getDevice(t.node.rdev);t.stream_ops=e.stream_ops,t.stream_ops.open&&t.stream_ops.open(t)},llseek:function(){throw new ut.ErrnoError(70)}},major:function(t){return t>>8},minor:function(t){return 255&t},makedev:function(t,e){return t<<8|e},registerDevice:function(t,e){ut.devices[t]={stream_ops:e}},getDevice:function(t){return ut.devices[t]},getMounts:function(t){for(var e=[],r=[t];r.length;){var n=r.pop();e.push(n),r.push.apply(r,n.mounts)}return e},syncfs:function(t,e){"function"==typeof t&&(e=t,t=!1),ut.syncFSRequests++,ut.syncFSRequests>1&&w("warning: "+ut.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=ut.getMounts(ut.root.mount),n=0;function o(t){return ut.syncFSRequests--,e(t)}function i(t){if(t)return i.errored?void 0:(i.errored=!0,o(t));++n>=r.length&&o(null)}r.forEach((function(e){if(!e.type.syncfs)return i(null);e.type.syncfs(e,t,i)}))},mount:function(t,e,r){var n,o="/"===r,i=!r;if(o&&ut.root)throw new ut.ErrnoError(10);if(!o&&!i){var a=ut.lookupPath(r,{follow_mount:!1});if(r=a.path,n=a.node,ut.isMountpoint(n))throw new ut.ErrnoError(10);if(!ut.isDir(n.mode))throw new ut.ErrnoError(54)}var s={type:t,opts:e,mountpoint:r,mounts:[]},c=t.mount(s);return c.mount=s,s.root=c,o?ut.root=c:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),c},unmount:function(t){var e=ut.lookupPath(t,{follow_mount:!1});if(!ut.isMountpoint(e.node))throw new ut.ErrnoError(28);var r=e.node,n=r.mounted,o=ut.getMounts(n);Object.keys(ut.nameTable).forEach((function(t){for(var e=ut.nameTable[t];e;){var r=e.name_next;o.includes(e.mount)&&ut.destroyNode(e),e=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);r.mount.mounts.splice(i,1)},lookup:function(t,e){return t.node_ops.lookup(t,e)},mknod:function(t,e,r){var n=ut.lookupPath(t,{parent:!0}).node,o=nt.basename(t);if(!o||"."===o||".."===o)throw new ut.ErrnoError(28);var i=ut.mayCreate(n,o);if(i)throw new ut.ErrnoError(i);if(!n.node_ops.mknod)throw new ut.ErrnoError(63);return n.node_ops.mknod(n,o,e,r)},create:function(t,e){return e=void 0!==e?e:438,e&=4095,e|=32768,ut.mknod(t,e,0)},mkdir:function(t,e){return e=void 0!==e?e:511,e&=1023,e|=16384,ut.mknod(t,e,0)},mkdirTree:function(t,e){for(var r=t.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{ut.mkdir(n,e)}catch(t){if(20!=t.errno)throw t}}},mkdev:function(t,e,r){return void 0===r&&(r=e,e=438),e|=8192,ut.mknod(t,e,r)},symlink:function(t,e){if(!ot.resolve(t))throw new ut.ErrnoError(44);var r=ut.lookupPath(e,{parent:!0}).node;if(!r)throw new ut.ErrnoError(44);var n=nt.basename(e),o=ut.mayCreate(r,n);if(o)throw new ut.ErrnoError(o);if(!r.node_ops.symlink)throw new ut.ErrnoError(63);return r.node_ops.symlink(r,n,t)},rename:function(t,e){var r,n,o=nt.dirname(t),i=nt.dirname(e),a=nt.basename(t),s=nt.basename(e);if(r=ut.lookupPath(t,{parent:!0}).node,n=ut.lookupPath(e,{parent:!0}).node,!r||!n)throw new ut.ErrnoError(44);if(r.mount!==n.mount)throw new ut.ErrnoError(75);var c,l=ut.lookupNode(r,a),u=ot.relative(t,i);if("."!==u.charAt(0))throw new ut.ErrnoError(28);if("."!==(u=ot.relative(e,o)).charAt(0))throw new ut.ErrnoError(55);try{c=ut.lookupNode(n,s)}catch(t){}if(l!==c){var f=ut.isDir(l.mode),h=ut.mayDelete(r,a,f);if(h)throw new ut.ErrnoError(h);if(h=c?ut.mayDelete(n,s,f):ut.mayCreate(n,s))throw new ut.ErrnoError(h);if(!r.node_ops.rename)throw new ut.ErrnoError(63);if(ut.isMountpoint(l)||c&&ut.isMountpoint(c))throw new ut.ErrnoError(10);if(n!==r&&(h=ut.nodePermissions(r,"w")))throw new ut.ErrnoError(h);ut.hashRemoveNode(l);try{r.node_ops.rename(l,n,s)}catch(t){throw t}finally{ut.hashAddNode(l)}}},rmdir:function(t){var e=ut.lookupPath(t,{parent:!0}).node,r=nt.basename(t),n=ut.lookupNode(e,r),o=ut.mayDelete(e,r,!0);if(o)throw new ut.ErrnoError(o);if(!e.node_ops.rmdir)throw new ut.ErrnoError(63);if(ut.isMountpoint(n))throw new ut.ErrnoError(10);e.node_ops.rmdir(e,r),ut.destroyNode(n)},readdir:function(t){var e=ut.lookupPath(t,{follow:!0}).node;if(!e.node_ops.readdir)throw new ut.ErrnoError(54);return e.node_ops.readdir(e)},unlink:function(t){var e=ut.lookupPath(t,{parent:!0}).node;if(!e)throw new ut.ErrnoError(44);var r=nt.basename(t),n=ut.lookupNode(e,r),o=ut.mayDelete(e,r,!1);if(o)throw new ut.ErrnoError(o);if(!e.node_ops.unlink)throw new ut.ErrnoError(63);if(ut.isMountpoint(n))throw new ut.ErrnoError(10);e.node_ops.unlink(e,r),ut.destroyNode(n)},readlink:function(t){var e=ut.lookupPath(t).node;if(!e)throw new ut.ErrnoError(44);if(!e.node_ops.readlink)throw new ut.ErrnoError(28);return ot.resolve(ut.getPath(e.parent),e.node_ops.readlink(e))},stat:function(t,e){var r=ut.lookupPath(t,{follow:!e}).node;if(!r)throw new ut.ErrnoError(44);if(!r.node_ops.getattr)throw new ut.ErrnoError(63);return r.node_ops.getattr(r)},lstat:function(t){return ut.stat(t,!0)},chmod:function(t,e,r){var n;if(!(n="string"==typeof t?ut.lookupPath(t,{follow:!r}).node:t).node_ops.setattr)throw new ut.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&e|-4096&n.mode,timestamp:Date.now()})},lchmod:function(t,e){ut.chmod(t,e,!0)},fchmod:function(t,e){var r=ut.getStream(t);if(!r)throw new ut.ErrnoError(8);ut.chmod(r.node,e)},chown:function(t,e,r,n){var o;if(!(o="string"==typeof t?ut.lookupPath(t,{follow:!n}).node:t).node_ops.setattr)throw new ut.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(t,e,r){ut.chown(t,e,r,!0)},fchown:function(t,e,r){var n=ut.getStream(t);if(!n)throw new ut.ErrnoError(8);ut.chown(n.node,e,r)},truncate:function(t,e){if(e<0)throw new ut.ErrnoError(28);var r;if(!(r="string"==typeof t?ut.lookupPath(t,{follow:!0}).node:t).node_ops.setattr)throw new ut.ErrnoError(63);if(ut.isDir(r.mode))throw new ut.ErrnoError(31);if(!ut.isFile(r.mode))throw new ut.ErrnoError(28);var n=ut.nodePermissions(r,"w");if(n)throw new ut.ErrnoError(n);r.node_ops.setattr(r,{size:e,timestamp:Date.now()})},ftruncate:function(t,e){var r=ut.getStream(t);if(!r)throw new ut.ErrnoError(8);if(0==(2097155&r.flags))throw new ut.ErrnoError(28);ut.truncate(r.node,e)},utime:function(t,e,r){var n=ut.lookupPath(t,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(e,r)})},open:function(t,e,r){if(""===t)throw new ut.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(e="string"==typeof e?ut.modeStringToFlags(e):e)?4095&r|32768:0,"object"==i(t))n=t;else{t=nt.normalize(t);try{n=ut.lookupPath(t,{follow:!(131072&e)}).node}catch(t){}}var o=!1;if(64&e)if(n){if(128&e)throw new ut.ErrnoError(20)}else n=ut.mknod(t,r,0),o=!0;if(!n)throw new ut.ErrnoError(44);if(ut.isChrdev(n.mode)&&(e&=-513),65536&e&&!ut.isDir(n.mode))throw new ut.ErrnoError(54);if(!o){var a=ut.mayOpen(n,e);if(a)throw new ut.ErrnoError(a)}512&e&&!o&&ut.truncate(n,0),e&=-131713;var s=ut.createStream({node:n,path:ut.getPath(n),flags:e,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),!u.logReadFiles||1&e||(ut.readFiles||(ut.readFiles={}),t in ut.readFiles||(ut.readFiles[t]=1)),s},close:function(t){if(ut.isClosed(t))throw new ut.ErrnoError(8);t.getdents&&(t.getdents=null);try{t.stream_ops.close&&t.stream_ops.close(t)}catch(t){throw t}finally{ut.closeStream(t.fd)}t.fd=null},isClosed:function(t){return null===t.fd},llseek:function(t,e,r){if(ut.isClosed(t))throw new ut.ErrnoError(8);if(!t.seekable||!t.stream_ops.llseek)throw new ut.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new ut.ErrnoError(28);return t.position=t.stream_ops.llseek(t,e,r),t.ungotten=[],t.position},read:function(t,e,r,n,o){if(n<0||o<0)throw new ut.ErrnoError(28);if(ut.isClosed(t))throw new ut.ErrnoError(8);if(1==(2097155&t.flags))throw new ut.ErrnoError(8);if(ut.isDir(t.node.mode))throw new ut.ErrnoError(31);if(!t.stream_ops.read)throw new ut.ErrnoError(28);var i=void 0!==o;if(i){if(!t.seekable)throw new ut.ErrnoError(70)}else o=t.position;var a=t.stream_ops.read(t,e,r,n,o);return i||(t.position+=a),a},write:function(t,e,r,n,o,i){if(n<0||o<0)throw new ut.ErrnoError(28);if(ut.isClosed(t))throw new ut.ErrnoError(8);if(0==(2097155&t.flags))throw new ut.ErrnoError(8);if(ut.isDir(t.node.mode))throw new ut.ErrnoError(31);if(!t.stream_ops.write)throw new ut.ErrnoError(28);t.seekable&&1024&t.flags&&ut.llseek(t,0,2);var a=void 0!==o;if(a){if(!t.seekable)throw new ut.ErrnoError(70)}else o=t.position;var s=t.stream_ops.write(t,e,r,n,o,i);return a||(t.position+=s),s},allocate:function(t,e,r){if(ut.isClosed(t))throw new ut.ErrnoError(8);if(e<0||r<=0)throw new ut.ErrnoError(28);if(0==(2097155&t.flags))throw new ut.ErrnoError(8);if(!ut.isFile(t.node.mode)&&!ut.isDir(t.node.mode))throw new ut.ErrnoError(43);if(!t.stream_ops.allocate)throw new ut.ErrnoError(138);t.stream_ops.allocate(t,e,r)},mmap:function(t,e,r,n,o){if(0!=(2&n)&&0==(2&o)&&2!=(2097155&t.flags))throw new ut.ErrnoError(2);if(1==(2097155&t.flags))throw new ut.ErrnoError(2);if(!t.stream_ops.mmap)throw new ut.ErrnoError(43);return t.stream_ops.mmap(t,e,r,n,o)},msync:function(t,e,r,n,o){return t.stream_ops.msync?t.stream_ops.msync(t,e,r,n,o):0},munmap:function(t){return 0},ioctl:function(t,e,r){if(!t.stream_ops.ioctl)throw new ut.ErrnoError(59);return t.stream_ops.ioctl(t,e,r)},readFile:function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var n=ut.open(t,r.flags),o=ut.stat(t).size,i=new Uint8Array(o);return ut.read(n,i,0,o,0),"utf8"===r.encoding?e=C(i,0):"binary"===r.encoding&&(e=i),ut.close(n),e},writeFile:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var n=ut.open(t,r.flags,r.mode);if("string"==typeof e){var o=new Uint8Array(O(e)+1),i=P(e,o,0,o.length);ut.write(n,o,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(e))throw new Error("Unsupported data type");ut.write(n,e,0,e.byteLength,void 0,r.canOwn)}ut.close(n)},cwd:function(){return ut.currentPath},chdir:function(t){var e=ut.lookupPath(t,{follow:!0});if(null===e.node)throw new ut.ErrnoError(44);if(!ut.isDir(e.node.mode))throw new ut.ErrnoError(54);var r=ut.nodePermissions(e.node,"x");if(r)throw new ut.ErrnoError(r);ut.currentPath=e.path},createDefaultDirectories:function(){ut.mkdir("/tmp"),ut.mkdir("/home"),ut.mkdir("/home/<USER>")},createDefaultDevices:function(){ut.mkdir("/dev"),ut.registerDevice(ut.makedev(1,3),{read:function(){return 0},write:function(t,e,r,n,o){return n}}),ut.mkdev("/dev/null",ut.makedev(1,3)),at.register(ut.makedev(5,0),at.default_tty_ops),at.register(ut.makedev(6,0),at.default_tty1_ops),ut.mkdev("/dev/tty",ut.makedev(5,0)),ut.mkdev("/dev/tty1",ut.makedev(6,0));var t,e,r=(t=function(t){for(var e=0;e<t.length;e++)t[e]=256*Math.random()|0},e=new Uint8Array(1),function(){return t(e),e[0]});ut.createDevice("/dev","random",r),ut.createDevice("/dev","urandom",r),ut.mkdir("/dev/shm"),ut.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){ut.mkdir("/proc");var t=ut.mkdir("/proc/self");ut.mkdir("/proc/self/fd"),ut.mount({mount:function(){var e=ut.createNode(t,"fd",16895,73);return e.node_ops={lookup:function(t,e){var r=+e,n=ut.getStream(r);if(!n)throw new ut.ErrnoError(8);var o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},e}},{},"/proc/self/fd")},createStandardStreams:function(){u.stdin?ut.createDevice("/dev","stdin",u.stdin):ut.symlink("/dev/tty","/dev/stdin"),u.stdout?ut.createDevice("/dev","stdout",null,u.stdout):ut.symlink("/dev/tty","/dev/stdout"),u.stderr?ut.createDevice("/dev","stderr",null,u.stderr):ut.symlink("/dev/tty1","/dev/stderr"),ut.open("/dev/stdin",0),ut.open("/dev/stdout",1),ut.open("/dev/stderr",1)},ensureErrnoError:function(){ut.ErrnoError||(ut.ErrnoError=function(t,e){this.node=e,this.setErrno=function(t){this.errno=t},this.setErrno(t),this.message="FS error"},ut.ErrnoError.prototype=new Error,ut.ErrnoError.prototype.constructor=ut.ErrnoError,[44].forEach((function(t){ut.genericErrors[t]=new ut.ErrnoError(t),ut.genericErrors[t].stack="<generic error, no stack>"})))},staticInit:function(){ut.ensureErrnoError(),ut.nameTable=new Array(4096),ut.mount(ct,{},"/"),ut.createDefaultDirectories(),ut.createDefaultDevices(),ut.createSpecialDirectories(),ut.filesystems={MEMFS:ct}},init:function(t,e,r){ut.init.initialized=!0,ut.ensureErrnoError(),u.stdin=t||u.stdin,u.stdout=e||u.stdout,u.stderr=r||u.stderr,ut.createStandardStreams()},quit:function(){ut.init.initialized=!1;for(var t=0;t<ut.streams.length;t++){var e=ut.streams[t];e&&ut.close(e)}},getMode:function(t,e){var r=0;return t&&(r|=365),e&&(r|=146),r},findObject:function(t,e){var r=ut.analyzePath(t,e);return r.exists?r.object:null},analyzePath:function(t,e){try{t=(n=ut.lookupPath(t,{follow:!e})).path}catch(t){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=ut.lookupPath(t,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=nt.basename(t),n=ut.lookupPath(t,{follow:!e}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(t){r.error=t.errno}return r},createPath:function(t,e,r,n){t="string"==typeof t?t:ut.getPath(t);for(var o=e.split("/").reverse();o.length;){var i=o.pop();if(i){var a=nt.join2(t,i);try{ut.mkdir(a)}catch(t){}t=a}}return a},createFile:function(t,e,r,n,o){var i=nt.join2("string"==typeof t?t:ut.getPath(t),e),a=ut.getMode(n,o);return ut.create(i,a)},createDataFile:function(t,e,r,n,o,i){var a=e;t&&(t="string"==typeof t?t:ut.getPath(t),a=e?nt.join2(t,e):t);var s=ut.getMode(n,o),c=ut.create(a,s);if(r){if("string"==typeof r){for(var l=new Array(r.length),u=0,f=r.length;u<f;++u)l[u]=r.charCodeAt(u);r=l}ut.chmod(c,146|s);var h=ut.open(c,577);ut.write(h,r,0,r.length,0,i),ut.close(h),ut.chmod(c,s)}return c},createDevice:function(t,e,r,n){var o=nt.join2("string"==typeof t?t:ut.getPath(t),e),i=ut.getMode(!!r,!!n);ut.createDevice.major||(ut.createDevice.major=64);var a=ut.makedev(ut.createDevice.major++,0);return ut.registerDevice(a,{open:function(t){t.seekable=!1},close:function(t){n&&n.buffer&&n.buffer.length&&n(10)},read:function(t,e,n,o,i){for(var a=0,s=0;s<o;s++){var c;try{c=r()}catch(t){throw new ut.ErrnoError(29)}if(void 0===c&&0===a)throw new ut.ErrnoError(6);if(null==c)break;a++,e[n+s]=c}return a&&(t.node.timestamp=Date.now()),a},write:function(t,e,r,o,i){for(var a=0;a<o;a++)try{n(e[r+a])}catch(t){throw new ut.ErrnoError(29)}return o&&(t.node.timestamp=Date.now()),a}}),ut.mkdev(o,i,a)},forceLoadFile:function(t){if(t.isDevice||t.isFolder||t.link||t.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!a)throw new Error("Cannot load without read() or XMLHttpRequest.");try{t.contents=it(a(t.url),!0),t.usedBytes=t.contents.length}catch(t){throw new ut.ErrnoError(29)}},createLazyFile:function(t,e,r,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(t){if(!(t>this.length-1||t<0)){var e=t%this.chunkSize,r=t/this.chunkSize|0;return this.getter(r)[e]}},i.prototype.setDataGetter=function(t){this.getter=t},i.prototype.cacheLength=function(){var t=new XMLHttpRequest;if(t.open("HEAD",r,!1),t.send(null),!(t.status>=200&&t.status<300||304===t.status))throw new Error("Couldn't load "+r+". Status: "+t.status);var e,n=Number(t.getResponseHeader("Content-length")),o=(e=t.getResponseHeader("Accept-Ranges"))&&"bytes"===e,i=(e=t.getResponseHeader("Content-Encoding"))&&"gzip"===e,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(t){var e=t*a,o=(t+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[t]&&(s.chunks[t]=function(t,e){if(t>e)throw new Error("invalid range ("+t+", "+e+") or no bytes requested!");if(e>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",r,!1),n!==a&&o.setRequestHeader("Range","bytes="+t+"-"+e),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+r+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):it(o.responseText||"",!0)}(e,o)),void 0===s.chunks[t])throw new Error("doXHR failed!");return s.chunks[t]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,v("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!_)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:a}}else s={isDevice:!1,url:r};var c=ut.createFile(t,e,s,n,o);s.contents?c.contents=s.contents:s.url&&(c.contents=null,c.url=s.url),Object.defineProperties(c,{usedBytes:{get:function(){return this.contents.length}}});var l={};function u(t,e,r,n,o){var i=t.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(i.slice)for(var s=0;s<a;s++)e[r+s]=i[o+s];else for(s=0;s<a;s++)e[r+s]=i.get(o+s);return a}return Object.keys(c.stream_ops).forEach((function(t){var e=c.stream_ops[t];l[t]=function(){return ut.forceLoadFile(c),e.apply(null,arguments)}})),l.read=function(t,e,r,n,o){return ut.forceLoadFile(c),u(t,e,r,n,o)},l.mmap=function(t,e,r,n,o){ut.forceLoadFile(c);var i=st(e);if(!i)throw new ut.ErrnoError(48);return u(t,x,i,e,r),{ptr:i,allocated:!0}},c.stream_ops=l,c},createPreloadedFile:function(t,e,r,n,o,i,a,s,c,l){var u=e?ot.resolve(nt.join2(t,e)):t;function f(r){function f(r){l&&l(),s||ut.createDataFile(t,e,r,n,o,c),i&&i(),X()}Browser.handledByPreloadPlugin(r,u,f,(function(){a&&a(),X()}))||f(r)}K(),"string"==typeof r?lt(r,(function(t){return f(t)}),a):f(r)},indexedDB:function(){return n.indexedDB||n.mozIndexedDB||n.webkitIndexedDB||n.msIndexedDB},DB_NAME:function(){return"EM_FS_"+n.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(t,e,r){e=e||function(){},r=r||function(){};var n=ut.indexedDB();try{var o=n.open(ut.DB_NAME(),ut.DB_VERSION)}catch(t){return r(t)}o.onupgradeneeded=function(){v("creating db"),o.result.createObjectStore(ut.DB_STORE_NAME)},o.onsuccess=function(){var n=o.result.transaction([ut.DB_STORE_NAME],"readwrite"),i=n.objectStore(ut.DB_STORE_NAME),a=0,s=0,c=t.length;function l(){0==s?e():r()}t.forEach((function(t){var e=i.put(ut.analyzePath(t).object.contents,t);e.onsuccess=function(){++a+s==c&&l()},e.onerror=function(){s++,a+s==c&&l()}})),n.onerror=r},o.onerror=r},loadFilesFromDB:function(t,e,r){e=e||function(){},r=r||function(){};var n=ut.indexedDB();try{var o=n.open(ut.DB_NAME(),ut.DB_VERSION)}catch(t){return r(t)}o.onupgradeneeded=r,o.onsuccess=function(){var n=o.result;try{var i=n.transaction([ut.DB_STORE_NAME],"readonly")}catch(t){return void r(t)}var a=i.objectStore(ut.DB_STORE_NAME),s=0,c=0,l=t.length;function u(){0==c?e():r()}t.forEach((function(t){var e=a.get(t);e.onsuccess=function(){ut.analyzePath(t).exists&&ut.unlink(t),ut.createDataFile(nt.dirname(t),nt.basename(t),e.result,!0,!0,!0),++s+c==l&&u()},e.onerror=function(){c++,s+c==l&&u()}})),i.onerror=r},o.onerror=r}},ft={DEFAULT_POLLMASK:5,calculateAt:function(t,e,r){if(nt.isAbs(e))return e;var n;if(n=-100===t?ut.cwd():ft.getStreamFromFD(t).path,0==e.length){if(!r)throw new ut.ErrnoError(44);return n}return nt.join2(n,e)},doStat:function(t,e,r){try{var n=t(e)}catch(t){if(t&&t.node&&nt.normalize(e)!==nt.normalize(ut.getPath(t.node)))return-54;throw t}return z[r>>2]=n.dev,z[r+8>>2]=n.ino,z[r+12>>2]=n.mode,D[r+16>>2]=n.nlink,z[r+20>>2]=n.uid,z[r+24>>2]=n.gid,z[r+28>>2]=n.rdev,$=[n.size>>>0,(V=n.size,+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[r+40>>2]=$[0],z[r+44>>2]=$[1],z[r+48>>2]=4096,z[r+52>>2]=n.blocks,$=[Math.floor(n.atime.getTime()/1e3)>>>0,(V=Math.floor(n.atime.getTime()/1e3),+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[r+56>>2]=$[0],z[r+60>>2]=$[1],D[r+64>>2]=0,$=[Math.floor(n.mtime.getTime()/1e3)>>>0,(V=Math.floor(n.mtime.getTime()/1e3),+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[r+72>>2]=$[0],z[r+76>>2]=$[1],D[r+80>>2]=0,$=[Math.floor(n.ctime.getTime()/1e3)>>>0,(V=Math.floor(n.ctime.getTime()/1e3),+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[r+88>>2]=$[0],z[r+92>>2]=$[1],D[r+96>>2]=0,$=[n.ino>>>0,(V=n.ino,+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[r+104>>2]=$[0],z[r+108>>2]=$[1],0},doMsync:function(t,e,r,n,o){if(!ut.isFile(e.node.mode))throw new ut.ErrnoError(43);if(2&n)return 0;var i=A.slice(t,t+r);ut.msync(e,i,o,r,n)},varargs:void 0,get:function(){return ft.varargs+=4,z[ft.varargs-4>>2]},getStr:function(t){return M(t)},getStreamFromFD:function(t){var e=ut.getStream(t);if(!e)throw new ut.ErrnoError(8);return e}};function ht(t){switch(t){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+t)}}var dt=void 0;function pt(t){for(var e="",r=t;A[r];)e+=dt[A[r++]];return e}var _t={},mt={},gt={},vt=48,wt=57;function yt(t,e){var r=function(t){return function(){"use strict";return t.apply(this,arguments)}}((function(t){this.name=e,this.message=t;var r=new Error(t).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var bt=void 0;function kt(t){throw new bt(t)}var Et=void 0;function xt(t){throw new Et(t)}function At(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in e))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=e.name;if(t||kt('type "'+n+'" must have a positive integer typeid pointer'),mt.hasOwnProperty(t)){if(r.ignoreDuplicateRegistrations)return;kt("Cannot register type '"+n+"' twice")}if(mt[t]=e,delete gt[t],_t.hasOwnProperty(t)){var o=_t[t];delete _t[t],o.forEach((function(t){return t()}))}}var St=[],Bt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function zt(t){t>4&&0==--Bt[t].refcount&&(Bt[t]=void 0,St.push(t))}function Dt(){for(var t=0,e=5;e<Bt.length;++e)void 0!==Bt[e]&&++t;return t}function Rt(){for(var t=5;t<Bt.length;++t)if(void 0!==Bt[t])return Bt[t];return null}var Tt=function(t){return t||kt("Cannot use deleted val. handle = "+t),Bt[t].value},Ft=function(t){switch(t){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var e=St.length?St.pop():Bt.length;return Bt[e]={refcount:1,value:t},e}};function Ct(t){return this.fromWireType(z[t>>2])}function Mt(t,e){switch(e){case 2:return function(t){return this.fromWireType(R[t>>2])};case 3:return function(t){return this.fromWireType(T[t>>3])};default:throw new TypeError("Unknown float type: "+t)}}function Pt(t){for(;t.length;){var e=t.pop();t.pop()(e)}}function Ht(t,e,r,n,o){var i=e.length;i<2&&kt("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==e[1]&&null!==r,s=!1,c=1;c<e.length;++c)if(null!==e[c]&&void 0===e[c].destructorFunction){s=!0;break}var l="void"!==e[0].name,u="",f="";for(c=0;c<i-2;++c)u+=(0!==c?", ":"")+"arg"+c,f+=(0!==c?", ":"")+"arg"+c+"Wired";var h="return function "+function(t){if(void 0===t)return"_unknown";var e=(t=t.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return e>=vt&&e<=wt?"_"+t:t}(t)+"("+u+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+t+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(h+="var destructors = [];\n");var d,p=s?"destructors":"null",_=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[kt,n,o,Pt,e[0],e[1]];for(a&&(h+="var thisWired = classParam.toWireType("+p+", this);\n"),c=0;c<i-2;++c)h+="var arg"+c+"Wired = argType"+c+".toWireType("+p+", arg"+c+"); // "+e[c+2].name+"\n",_.push("argType"+c),m.push(e[c+2]);if(a&&(f="thisWired"+(f.length>0?", ":"")+f),h+=(l?"var rv = ":"")+"invoker(fn"+(f.length>0?", ":"")+f+");\n",s)h+="runDestructors(destructors);\n";else for(c=a?1:2;c<e.length;++c){var g=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==e[c].destructorFunction&&(h+=g+"_dtor("+g+"); // "+e[c].name+"\n",_.push(g+"_dtor"),m.push(e[c].destructorFunction))}return l&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",_.push(h),"release"===t?d=function(t,e,r,n,o,i){return function(){0!==arguments.length&&t("function release called with "+arguments.length+" arguments, expected 0 args!"),e(r)}}:"init"===t?d=function(t,e,r,n,o,i,a,s,c){return function(n,i,l){3!==arguments.length&&t("function init called with "+arguments.length+" arguments, expected 3 args!");var u=a.toWireType(null,n),f=s.toWireType(null,i),h=c.toWireType(null,l),d=e(r,u,f,h);return o.fromWireType(d)}}:"encode"===t?d=function(t,e,r,n,o,i,a,s,c,l,u,f){return function(n,o,i,h){4!==arguments.length&&t("function encode called with "+arguments.length+" arguments, expected 4 args!");var d=a.toWireType(null,n),p=s.toWireType(null,o),_=c.toWireType(null,i),m=l.toWireType(null,h);e(r,d,p,_,m),u(d),f(p)}}:"decode"===t&&(d=function(t,e,r,n,o,i,a,s,c,l,u,f){return function(n,o,i,h){4!==arguments.length&&t("function decode called with "+arguments.length+" arguments, expected 4 args!");var d=a.toWireType(null,n),p=s.toWireType(null,o),_=c.toWireType(null,i),m=l.toWireType(null,h);e(r,d,p,_,m),u(d),f(p)}}),d.apply(null,m)}function Ot(t,e,r){u.hasOwnProperty(t)?((void 0===r||void 0!==u[t].overloadTable&&void 0!==u[t].overloadTable[r])&&kt("Cannot register public name '"+t+"' twice"),function(t,e,r){if(void 0===t[e].overloadTable){var n=t[e];t[e]=function(){return t[e].overloadTable.hasOwnProperty(arguments.length)||kt("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+t[e].overloadTable+")!"),t[e].overloadTable[arguments.length].apply(this,arguments)},t[e].overloadTable=[],t[e].overloadTable[n.argCount]=n}}(u,t,t),u.hasOwnProperty(r)&&kt("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),u[t].overloadTable[r]=e):(u[t]=e,void 0!==r&&(u[t].numArguments=r))}var Ut=[];function Nt(t){var e=Ut[t];return e||(t>=Ut.length&&(Ut.length=t+1),Ut[t]=e=U.get(t)),e}function It(t,e,r){return t.includes("j")?function(t,e,r){var n=u["dynCall_"+t];return r&&r.length?n.apply(null,[e].concat(r)):n.call(null,e)}(t,e,r):Nt(e).apply(null,r)}function jt(t,e){var r,n,o,i=(t=pt(t)).includes("j")?(r=t,n=e,o=[],function(){return o.length=0,Object.assign(o,arguments),It(r,n,o)}):Nt(e);return"function"!=typeof i&&kt("unknown function pointer with signature "+t+": "+e),i}var Lt=void 0;function Wt(t){var e=ue(t),r=pt(e);return se(e),r}function Zt(t,e,r){switch(e){case 0:return r?function(t){return x[t]}:function(t){return A[t]};case 1:return r?function(t){return S[t>>1]}:function(t){return B[t>>1]};case 2:return r?function(t){return z[t>>2]}:function(t){return D[t>>2]};default:throw new TypeError("Unknown integer type: "+t)}}var Kt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Xt(t,e){for(var r=t,n=r>>1,o=n+e/2;!(n>=o)&&B[n];)++n;if((r=n<<1)-t>32&&Kt)return Kt.decode(A.subarray(t,r));for(var i="",a=0;!(a>=e/2);++a){var s=S[t+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function qt(t,e,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=e,o=(r-=2)<2*t.length?r/2:t.length,i=0;i<o;++i){var a=t.charCodeAt(i);S[e>>1]=a,e+=2}return S[e>>1]=0,e-n}function Gt(t){return 2*t.length}function Vt(t,e){for(var r=0,n="";!(r>=e/4);){var o=z[t+4*r>>2];if(0==o)break;if(++r,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function $t(t,e,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=e,o=n+r-4,i=0;i<t.length;++i){var a=t.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&t.charCodeAt(++i)),z[e>>2]=a,(e+=4)+4>o)break}return z[e>>2]=0,e-n}function Yt(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&++r,e+=4}return e}var Jt;function Qt(t,e){var r=mt[t];return void 0===r&&kt(e+" has unknown type "+Wt(t)),r}Jt=m?function(){var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:function(){return performance.now()};var te={};function ee(){if(!ee.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==("undefined"==typeof navigator?"undefined":i(navigator))&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:d||"./this.program"};for(var e in te)void 0===te[e]?delete t[e]:t[e]=te[e];var r=[];for(var e in t)r.push(e+"="+t[e]);ee.strings=r}return ee.strings}var re=function(t,e,r,n){t||(t=this),this.parent=t,this.mount=t.mount,this.mounted=null,this.id=ut.nextInode++,this.name=e,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},ne=365,oe=146;Object.defineProperties(re.prototype,{read:{get:function(){return(this.mode&ne)===ne},set:function(t){t?this.mode|=ne:this.mode&=-366}},write:{get:function(){return(this.mode&oe)===oe},set:function(t){t?this.mode|=oe:this.mode&=-147}},isFolder:{get:function(){return ut.isDir(this.mode)}},isDevice:{get:function(){return ut.isChrdev(this.mode)}}}),ut.FSNode=re,ut.staticInit(),function(){for(var t=new Array(256),e=0;e<256;++e)t[e]=String.fromCharCode(e);dt=t}(),bt=u.BindingError=yt(Error,"BindingError"),Et=u.InternalError=yt(Error,"InternalError"),u.count_emval_handles=Dt,u.get_first_emval=Rt,Lt=u.UnboundTypeError=yt(Error,"UnboundTypeError");var ie,ae={M:function(t){return le(t+24)+24},L:function(t,e,r){throw new rt(t).init(e,r),t},t:function(t,e,r){ft.varargs=r;try{var n=ft.getStreamFromFD(t);switch(e){case 0:return(o=ft.get())<0?-28:ut.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=ft.get();return n.flags|=o,0;case 5:return o=ft.get(),S[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return i=28,z[ce()>>2]=i,-1}}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}var i},K:function(t,e){try{var r=ft.getStreamFromFD(t);return ft.doStat(ut.stat,r.path,e)}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},J:function(t,e,r){try{var n=ft.getStreamFromFD(t);n.getdents||(n.getdents=ut.readdir(n.path));for(var o=280,i=0,a=ut.llseek(n,0,1),s=Math.floor(a/o);s<n.getdents.length&&i+o<=r;){var c,l,u=n.getdents[s];if("."===u)c=n.node.id,l=4;else if(".."===u)c=ut.lookupPath(n.path,{parent:!0}).node.id,l=4;else{var f=ut.lookupNode(n.node,u);c=f.id,l=ut.isChrdev(f.mode)?2:ut.isDir(f.mode)?4:ut.isLink(f.mode)?10:8}$=[c>>>0,(V=c,+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[e+i>>2]=$[0],z[e+i+4>>2]=$[1],$=[(s+1)*o>>>0,(V=(s+1)*o,+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[e+i+8>>2]=$[0],z[e+i+12>>2]=$[1],S[e+i+16>>1]=280,x[e+i+18>>0]=l,H(u,e+i+19,256),i+=o,s+=1}return ut.llseek(n,s*o,0),i}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},s:function(t,e,r){ft.varargs=r;try{var n=ft.getStreamFromFD(t);switch(e){case 21509:case 21505:case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:case 21523:case 21524:return n.tty?0:-59;case 21519:if(!n.tty)return-59;var o=ft.get();return z[o>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:return o=ft.get(),ut.ioctl(n,e,o);default:return-28}}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},I:function(t,e){try{return t=ft.getStr(t),ft.doStat(ut.lstat,t,e)}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},H:function(t,e,r,n){try{e=ft.getStr(e);var o=256&n,i=4096&n;return n&=-4353,e=ft.calculateAt(t,e,i),ft.doStat(o?ut.lstat:ut.stat,e,r)}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},r:function(t,e,r,n){ft.varargs=n;try{e=ft.getStr(e),e=ft.calculateAt(t,e);var o=n?ft.get():0;return ut.open(e,r,o).fd}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},G:function(t,e){try{return t=ft.getStr(t),ft.doStat(ut.stat,t,e)}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},u:function(t,e,r,n,o){},D:function(t,e,r,n,o){var i=ht(r);At(t,{name:e=pt(e),fromWireType:function(t){return!!t},toWireType:function(t,e){return e?n:o},argPackAdvance:8,readValueFromPointer:function(t){var n;if(1===r)n=x;else if(2===r)n=S;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+e);n=z}return this.fromWireType(n[t>>i])},destructorFunction:null})},C:function(t,e){At(t,{name:e=pt(e),fromWireType:function(t){var e=Tt(t);return zt(t),e},toWireType:function(t,e){return Ft(e)},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:null})},o:function(t,e,r){var n=ht(r);At(t,{name:e=pt(e),fromWireType:function(t){return t},toWireType:function(t,e){return e},argPackAdvance:8,readValueFromPointer:Mt(e,n),destructorFunction:null})},d:function(t,e,r,n,o,i){var a=function(t,e){for(var r=[],n=0;n<t;n++)r.push(D[e+4*n>>2]);return r}(e,r);t=pt(t),o=jt(n,o),Ot(t,(function(){!function(t,e){var r=[],n={};throw e.forEach((function t(e){n[e]||mt[e]||(gt[e]?gt[e].forEach(t):(r.push(e),n[e]=!0))})),new Lt(t+": "+r.map(Wt).join([", "]))}("Cannot call "+t+" due to unbound types",a)}),e-1),function(t,e,r){function n(e){var n=r(e);n.length!==t.length&&xt("Mismatched type converter count");for(var o=0;o<t.length;++o)At(t[o],n[o])}t.forEach((function(t){gt[t]=e}));var o=new Array(e.length),i=[],a=0;e.forEach((function(t,e){mt.hasOwnProperty(t)?o[e]=mt[t]:(i.push(t),_t.hasOwnProperty(t)||(_t[t]=[]),_t[t].push((function(){o[e]=mt[t],++a===i.length&&n(o)})))})),0===i.length&&n(o)}([],a,(function(r){var n=[r[0],null].concat(r.slice(1));return function(t,e,r){u.hasOwnProperty(t)||xt("Replacing nonexistant public symbol"),void 0!==u[t].overloadTable&&void 0!==r?u[t].overloadTable[r]=e:(u[t]=e,u[t].argCount=r)}(t,Ht(t,n,null,o,i),e-1),[]}))},c:function(t,e,r,n,o){e=pt(e),-1===o&&(o=4294967295);var i=ht(r),a=function(t){return t};if(0===n){var s=32-8*r;a=function(t){return t<<s>>>s}}var c=e.includes("unsigned");At(t,{name:e,fromWireType:a,toWireType:c?function(t,e){return this.name,e>>>0}:function(t,e){return this.name,e},argPackAdvance:8,readValueFromPointer:Zt(e,i,0!==n),destructorFunction:null})},a:function(t,e,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function o(t){var e=D,r=e[t>>=2],o=e[t+1];return new n(E,o,r)}At(t,{name:r=pt(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},n:function(t,e){var r="std::string"===(e=pt(e));At(t,{name:e,fromWireType:function(t){var e,n=D[t>>2],o=t+4;if(r)for(var i=o,a=0;a<=n;++a){var s=o+a;if(a==n||0==A[s]){var c=M(i,s-i);void 0===e?e=c:(e+=String.fromCharCode(0),e+=c),i=s+1}}else{var l=new Array(n);for(a=0;a<n;++a)l[a]=String.fromCharCode(A[o+a]);e=l.join("")}return se(t),e},toWireType:function(t,e){var n;e instanceof ArrayBuffer&&(e=new Uint8Array(e));var o="string"==typeof e;o||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||kt("Cannot pass non-string to std::string"),n=r&&o?O(e):e.length;var i=le(4+n+1),a=i+4;if(D[i>>2]=n,r&&o)H(e,a,n+1);else if(o)for(var s=0;s<n;++s){var c=e.charCodeAt(s);c>255&&(se(a),kt("String has UTF-16 code units that do not fit in 8 bits")),A[a+s]=c}else for(s=0;s<n;++s)A[a+s]=e[s];return null!==t&&t.push(se,i),i},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:function(t){se(t)}})},l:function(t,e,r){var n,o,i,a,s;r=pt(r),2===e?(n=Xt,o=qt,a=Gt,i=function(){return B},s=1):4===e&&(n=Vt,o=$t,a=Yt,i=function(){return D},s=2),At(t,{name:r,fromWireType:function(t){for(var r,o=D[t>>2],a=i(),c=t+4,l=0;l<=o;++l){var u=t+4+l*e;if(l==o||0==a[u>>s]){var f=n(c,u-c);void 0===r?r=f:(r+=String.fromCharCode(0),r+=f),c=u+e}}return se(t),r},toWireType:function(t,n){"string"!=typeof n&&kt("Cannot pass non-string to C++ string type "+r);var i=a(n),c=le(4+i+e);return D[c>>2]=i>>s,o(n,c+4,i+e),null!==t&&t.push(se,c),c},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:function(t){se(t)}})},B:function(t,e){At(t,{isVoid:!0,name:e=pt(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(t,e){}})},A:function(){return true},k:function(t,e,r){t=Tt(t),e=Qt(e,"emval::as");var n=[],o=Ft(n);return D[r>>2]=o,e.toWireType(n,t)},m:function(t,e,r,n){t=Tt(t);for(var o=function(t,e){for(var r=new Array(t),n=0;n<t;++n)r[n]=Qt(D[e+n*b>>2],"parameter "+n);return r}(e,r),i=new Array(e),a=0;a<e;++a){var s=o[a];i[a]=s.readValueFromPointer(n),n+=s.argPackAdvance}var c=t.apply(void 0,i);return Ft(c)},b:zt,f:function(t){t>4&&(Bt[t].refcount+=1)},j:function(t){Pt(Tt(t)),zt(t)},e:function(t,e){var r=(t=Qt(t,"_emval_take_value")).readValueFromPointer(e);return Ft(r)},z:function(t,e,r,n,o,i){try{var a=ft.getStreamFromFD(o);2&r&&ft.doMsync(t,a,e,n,i),ut.munmap(a)}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return-t.errno}},i:function(){q("")},h:function(){return Date.now()},y:Jt,x:function(t,e,r){A.copyWithin(t,e,e+r)},w:function(t){A.length,0,q("OOM")},F:function(t,e){var r=0;return ee().forEach((function(n,o){var i=e+r;D[t+4*o>>2]=i,function(t,e,r){for(var n=0;n<t.length;++n)x[e++>>0]=t.charCodeAt(n);r||(x[e>>0]=0)}(n,i),r+=n.length+1})),0},E:function(t,e){var r=ee();D[t>>2]=r.length;var n=0;return r.forEach((function(t){n+=t.length+1})),D[e>>2]=n,0},g:function(t){try{var e=ft.getStreamFromFD(t);return ut.close(e),0}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return t.errno}},q:function(t,e,r,n){try{var o=function(t,e,r,n){for(var o=0,i=0;i<r;i++){var a=D[e>>2],s=D[e+4>>2];e+=8;var c=ut.read(t,x,a,s,n);if(c<0)return-1;if(o+=c,c<s)break}return o}(ft.getStreamFromFD(t),e,r);return D[n>>2]=o,0}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return t.errno}},v:function(t,e,r,n,o){try{var i=(c=r)+2097152>>>0<4194305-!!(s=e)?(s>>>0)+4294967296*c:NaN;if(isNaN(i))return 61;var a=ft.getStreamFromFD(t);return ut.llseek(a,i,n),$=[a.position>>>0,(V=a.position,+Math.abs(V)>=1?V>0?(0|Math.min(+Math.floor(V/4294967296),4294967295))>>>0:~~+Math.ceil((V-+(~~V>>>0))/4294967296)>>>0:0)],z[o>>2]=$[0],z[o+4>>2]=$[1],a.getdents&&0===i&&0===n&&(a.getdents=null),0}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return t.errno}var s,c},p:function(t,e,r,n){try{var o=function(t,e,r,n){for(var o=0,i=0;i<r;i++){var a=D[e>>2],s=D[e+4>>2];e+=8;var c=ut.write(t,x,a,s,n);if(c<0)return-1;o+=c}return o}(ft.getStreamFromFD(t),e,r);return D[n>>2]=o,0}catch(t){if(void 0===ut||!(t instanceof ut.ErrnoError))throw t;return t.errno}}},se=(function(){var t={a:ae};function r(t,e){var r,n,o=t.exports;u.asm=o,n=u.asm.N.buffer,E=n,u.HEAP8=x=new Int8Array(n),u.HEAP16=S=new Int16Array(n),u.HEAP32=z=new Int32Array(n),u.HEAPU8=A=new Uint8Array(n),u.HEAPU16=B=new Uint16Array(n),u.HEAPU32=D=new Uint32Array(n),u.HEAPF32=R=new Float32Array(n),u.HEAPF64=T=new Float64Array(n),U=u.asm.V,r=u.asm.O,I.unshift(r),X()}function n(t){r(t.instance)}function o(r){return e.instantiate(G,t).then((function(t){n(t)}))}if(K(),u.instantiateWasm)try{return u.instantiateWasm(t,r)}catch(t){return w("Module.instantiateWasm callback failed with error: "+t),!1}y||"function"!=typeof e.instantiateStreaming||Q(G)||G.startsWith("file://")||m||"function"!=typeof fetch?o():fetch(G,{credentials:"same-origin"}).then((function(r){return e.instantiateStreaming(r,t).then(n,(function(t){return w("wasm streaming compile failed: "+t),w("falling back to ArrayBuffer instantiation"),o()}))}))}(),u.___wasm_call_ctors=function(){return(u.___wasm_call_ctors=u.asm.O).apply(null,arguments)},u._antssm_ecp_keypair_free=function(){return(u._antssm_ecp_keypair_free=u.asm.P).apply(null,arguments)},u._antssm_ecp_cal_key_with_public_key=function(){return(u._antssm_ecp_cal_key_with_public_key=u.asm.Q).apply(null,arguments)},u._antssm_ecp_cal_key_with_private_key=function(){return(u._antssm_ecp_cal_key_with_private_key=u.asm.R).apply(null,arguments)},u._antssm_md_info_from_type=function(){return(u._antssm_md_info_from_type=u.asm.S).apply(null,arguments)},u._random_default=function(){return(u._random_default=u.asm.T).apply(null,arguments)},u._antssm_sm2_kap_compute_key=function(){return(u._antssm_sm2_kap_compute_key=u.asm.U).apply(null,arguments)},u._free=function(){return(se=u._free=u.asm.W).apply(null,arguments)}),ce=u.___errno_location=function(){return(ce=u.___errno_location=u.asm.X).apply(null,arguments)},le=u._malloc=function(){return(le=u._malloc=u.asm.Y).apply(null,arguments)},ue=u.___getTypeName=function(){return(ue=u.___getTypeName=u.asm.Z).apply(null,arguments)},fe=(u.__embind_initialize_bindings=function(){return(u.__embind_initialize_bindings=u.asm._).apply(null,arguments)},u._emscripten_builtin_memalign=function(){return(fe=u._emscripten_builtin_memalign=u.asm.$).apply(null,arguments)}),he=u.___cxa_is_pointer_type=function(){return(he=u.___cxa_is_pointer_type=u.asm.aa).apply(null,arguments)};function de(t){function e(){ie||(ie=!0,u.calledRun=!0,k||(u.noFSInit||ut.init.initialized||ut.init(),ut.ignorePermissions=!1,at.init(),et(I),u.onRuntimeInitialized&&u.onRuntimeInitialized(),function(){if(u.postRun)for("function"==typeof u.postRun&&(u.postRun=[u.postRun]);u.postRun.length;)t=u.postRun.shift(),j.unshift(t);var t;et(j)}()))}t=t||h,L>0||(function(){if(u.preRun)for("function"==typeof u.preRun&&(u.preRun=[u.preRun]);u.preRun.length;)t=u.preRun.shift(),N.unshift(t);var t;et(N)}(),L>0||(u.setStatus?(u.setStatus("Running..."),setTimeout((function(){setTimeout((function(){u.setStatus("")}),1),e()}),1)):e()))}if(u.dynCall_jiji=function(){return(u.dynCall_jiji=u.asm.ba).apply(null,arguments)},Z=function t(){ie||de(),ie||(Z=t)},u.preInit)for("function"==typeof u.preInit&&(u.preInit=[u.preInit]);u.preInit.length>0;)u.preInit.pop()();de();var pe=new Promise((function(t){u.onRuntimeInitialized=function(){t(u)}}));return function(){return pe}},void 0===(o=n.apply(e,[]))||(t.exports=o)},363:function(t,e,r){var n;t.exports=(n=r(3074),r(4165),r(8022),r(2248),r(7827),function(){var t=n,e=t.lib.BlockCipher,r=t.algo,o=[],i=[],a=[],s=[],c=[],l=[],u=[],f=[],h=[],d=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,n=0;for(e=0;e<256;e++){var p=n^n<<1^n<<2^n<<3^n<<4;p=p>>>8^255&p^99,o[r]=p,i[p]=r;var _=t[r],m=t[_],g=t[m],v=257*t[p]^16843008*p;a[r]=v<<24|v>>>8,s[r]=v<<16|v>>>16,c[r]=v<<8|v>>>24,l[r]=v,v=16843009*g^65537*m^257*_^16843008*r,u[p]=v<<24|v>>>8,f[p]=v<<16|v>>>16,h[p]=v<<8|v>>>24,d[p]=v,r?(r=_^t[t[t[g^_]]],n^=t[t[n]]):r=n=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],_=r.AES=e.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,n=4*((this._nRounds=r+6)+1),i=this._keySchedule=[],a=0;a<n;a++)a<r?i[a]=e[a]:(l=i[a-1],a%r?r>6&&a%r==4&&(l=o[l>>>24]<<24|o[l>>>16&255]<<16|o[l>>>8&255]<<8|o[255&l]):(l=o[(l=l<<8|l>>>24)>>>24]<<24|o[l>>>16&255]<<16|o[l>>>8&255]<<8|o[255&l],l^=p[a/r|0]<<24),i[a]=i[a-r]^l);for(var s=this._invKeySchedule=[],c=0;c<n;c++){if(a=n-c,c%4)var l=i[a];else l=i[a-4];s[c]=c<4||a<=4?l:u[o[l>>>24]]^f[o[l>>>16&255]]^h[o[l>>>8&255]]^d[o[255&l]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,a,s,c,l,o)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,u,f,h,d,i),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,o,i,a,s){for(var c=this._nRounds,l=t[e]^r[0],u=t[e+1]^r[1],f=t[e+2]^r[2],h=t[e+3]^r[3],d=4,p=1;p<c;p++){var _=n[l>>>24]^o[u>>>16&255]^i[f>>>8&255]^a[255&h]^r[d++],m=n[u>>>24]^o[f>>>16&255]^i[h>>>8&255]^a[255&l]^r[d++],g=n[f>>>24]^o[h>>>16&255]^i[l>>>8&255]^a[255&u]^r[d++],v=n[h>>>24]^o[l>>>16&255]^i[u>>>8&255]^a[255&f]^r[d++];l=_,u=m,f=g,h=v}_=(s[l>>>24]<<24|s[u>>>16&255]<<16|s[f>>>8&255]<<8|s[255&h])^r[d++],m=(s[u>>>24]<<24|s[f>>>16&255]<<16|s[h>>>8&255]<<8|s[255&l])^r[d++],g=(s[f>>>24]<<24|s[h>>>16&255]<<16|s[l>>>8&255]<<8|s[255&u])^r[d++],v=(s[h>>>24]<<24|s[l>>>16&255]<<16|s[u>>>8&255]<<8|s[255&f])^r[d++],t[e]=_,t[e+1]=m,t[e+2]=g,t[e+3]=v},keySize:8});t.AES=e._createHelper(_)}(),n.AES)},7827:function(t,e,r){var n,o,i,a,s,c,l,u,f,h,d,p,_,m,g,v,w,y,b;t.exports=(n=r(3074),r(2248),void(n.lib.Cipher||(o=n,i=o.lib,a=i.Base,s=i.WordArray,c=i.BufferedBlockAlgorithm,l=o.enc,l.Utf8,u=l.Base64,f=o.algo.EvpKDF,h=i.Cipher=c.extend({cfg:a.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){c.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?b:w}return function(e){return{encrypt:function(r,n,o){return t(n).encrypt(e,r,n,o)},decrypt:function(r,n,o){return t(n).decrypt(e,r,n,o)}}}}()}),i.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),d=o.mode={},p=i.BlockCipherMode=a.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),_=d.CBC=function(){var t=p.extend();function e(t,e,r){var n,o=this._iv;o?(n=o,this._iv=void 0):n=this._prevBlock;for(var i=0;i<r;i++)t[e+i]^=n[i]}return t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,o=n.blockSize;e.call(this,t,r,o),n.encryptBlock(t,r),this._prevBlock=t.slice(r,r+o)}}),t.Decryptor=t.extend({processBlock:function(t,r){var n=this._cipher,o=n.blockSize,i=t.slice(r,r+o);n.decryptBlock(t,r),e.call(this,t,r,o),this._prevBlock=i}}),t}(),m=(o.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,n=r-t.sigBytes%r,o=n<<24|n<<16|n<<8|n,i=[],a=0;a<n;a+=4)i.push(o);var c=s.create(i,n);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},i.BlockCipher=h.extend({cfg:h.cfg.extend({mode:_,padding:m}),reset:function(){var t;h.reset.call(this);var e=this.cfg,r=e.iv,n=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=n.createEncryptor:(t=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(n,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4}),g=i.CipherParams=a.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),v=(o.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;return(r?s.create([1398893684,1701076831]).concat(r).concat(e):e).toString(u)},parse:function(t){var e,r=u.parse(t),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(e=s.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:e})}},w=i.SerializableCipher=a.extend({cfg:a.extend({format:v}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var o=t.createEncryptor(r,n),i=o.finalize(e),a=o.cfg;return g.create({ciphertext:i,key:r,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),t.createDecryptor(r,n).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),y=(o.kdf={}).OpenSSL={execute:function(t,e,r,n){n||(n=s.random(8));var o=f.create({keySize:e+r}).compute(t,n),i=s.create(o.words.slice(e),4*r);return o.sigBytes=4*e,g.create({key:o,iv:i,salt:n})}},b=i.PasswordBasedCipher=w.extend({cfg:w.cfg.extend({kdf:y}),encrypt:function(t,e,r,n){var o=(n=this.cfg.extend(n)).kdf.execute(r,t.keySize,t.ivSize);n.iv=o.iv;var i=w.encrypt.call(this,t,e,o.key,n);return i.mixIn(o),i},decrypt:function(t,e,r,n){n=this.cfg.extend(n),e=this._parse(e,n.format);var o=n.kdf.execute(r,t.keySize,t.ivSize,e.salt);return n.iv=o.iv,w.decrypt.call(this,t,e,o.key,n)}}))))},3074:function(t,e,r){var n;t.exports=(n=n||function(t,e){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==r.g&&r.g.crypto&&(n=r.g.crypto),!n)try{n=r(3828)}catch(t){}var o=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),a={},s=a.lib={},c=s.Base={extend:function(t){var e=i(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},l=s.WordArray=c.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||f).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,o=t.sigBytes;if(this.clamp(),n%4)for(var i=0;i<o;i++){var a=r[i>>>2]>>>24-i%4*8&255;e[n+i>>>2]|=a<<24-(n+i)%4*8}else for(var s=0;s<o;s+=4)e[n+s>>>2]=r[s>>>2];return this.sigBytes+=o,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=c.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(o());return new l.init(e,t)}}),u=a.enc={},f=u.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o++){var i=e[o>>>2]>>>24-o%4*8&255;n.push((i>>>4).toString(16)),n.push((15&i).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new l.init(r,e/2)}},h=u.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o++){var i=e[o>>>2]>>>24-o%4*8&255;n.push(String.fromCharCode(i))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new l.init(r,e)}},d=u.Utf8={stringify:function(t){try{return decodeURIComponent(escape(h.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return h.parse(unescape(encodeURIComponent(t)))}},p=s.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=d.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,n=this._data,o=n.words,i=n.sigBytes,a=this.blockSize,s=i/(4*a),c=(s=e?t.ceil(s):t.max((0|s)-this._minBufferSize,0))*a,u=t.min(4*c,i);if(c){for(var f=0;f<c;f+=a)this._doProcessBlock(o,f);r=o.splice(0,c),n.sigBytes-=u}return new l.init(r,u)},clone:function(){var t=c.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),_=(s.Hasher=p.extend({cfg:c.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new _.HMAC.init(t,r).finalize(e)}}}),a.algo={});return a}(Math),n)},4165:function(t,e,r){var n,o,i;t.exports=(n=r(3074),i=(o=n).lib.WordArray,o.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp();for(var o=[],i=0;i<r;i+=3)for(var a=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<r;s++)o.push(n.charAt(a>>>6*(3-s)&63));var c=n.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(t){var e=t.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var a=r.charAt(64);if(a){var s=t.indexOf(a);-1!==s&&(e=s)}return function(t,e,r){for(var n=[],o=0,a=0;a<e;a++)if(a%4){var s=r[t.charCodeAt(a-1)]<<a%4*2|r[t.charCodeAt(a)]>>>6-a%4*2;n[o>>>2]|=s<<24-o%4*8,o++}return i.create(n,o)}(t,e,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)},3147:function(t,e,r){var n,o,i;t.exports=(n=r(3074),i=(o=n).lib.WordArray,o.enc.Base64url={stringify:function(t,e=!0){var r=t.words,n=t.sigBytes,o=e?this._safe_map:this._map;t.clamp();for(var i=[],a=0;a<n;a+=3)for(var s=(r[a>>>2]>>>24-a%4*8&255)<<16|(r[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|r[a+2>>>2]>>>24-(a+2)%4*8&255,c=0;c<4&&a+.75*c<n;c++)i.push(o.charAt(s>>>6*(3-c)&63));var l=o.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(t,e=!0){var r=t.length,n=e?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var a=0;a<n.length;a++)o[n.charCodeAt(a)]=a}var s=n.charAt(64);if(s){var c=t.indexOf(s);-1!==c&&(r=c)}return function(t,e,r){for(var n=[],o=0,a=0;a<e;a++)if(a%4){var s=r[t.charCodeAt(a-1)]<<a%4*2|r[t.charCodeAt(a)]>>>6-a%4*2;n[o>>>2]|=s<<24-o%4*8,o++}return i.create(n,o)}(t,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},n.enc.Base64url)},4831:function(t,e,r){var n;t.exports=(n=r(3074),function(){var t=n,e=t.lib.WordArray,r=t.enc;function o(t){return t<<8&4278255360|t>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o+=2){var i=e[o>>>2]>>>16-o%4*8&65535;n.push(String.fromCharCode(i))}return n.join("")},parse:function(t){for(var r=t.length,n=[],o=0;o<r;o++)n[o>>>1]|=t.charCodeAt(o)<<16-o%2*16;return e.create(n,2*r)}},r.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i+=2){var a=o(e[i>>>2]>>>16-i%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(t){for(var r=t.length,n=[],i=0;i<r;i++)n[i>>>1]|=o(t.charCodeAt(i)<<16-i%2*16);return e.create(n,2*r)}}}(),n.enc.Utf16)},2248:function(t,e,r){var n,o,i,a,s,c,l,u;t.exports=(u=r(3074),r(6974),r(3175),i=(o=(n=u).lib).Base,a=o.WordArray,c=(s=n.algo).MD5,l=s.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:c,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,n=this.cfg,o=n.hasher.create(),i=a.create(),s=i.words,c=n.keySize,l=n.iterations;s.length<c;){r&&o.update(r),r=o.update(t).finalize(e),o.reset();for(var u=1;u<l;u++)r=o.finalize(r),o.reset();i.concat(r)}return i.sigBytes=4*c,i}}),n.EvpKDF=function(t,e,r){return l.create(r).compute(t,e)},u.EvpKDF)},8228:function(t,e,r){var n,o,i,a;t.exports=(a=r(3074),r(7827),o=(n=a).lib.CipherParams,i=n.enc.Hex,n.format.Hex={stringify:function(t){return t.ciphertext.toString(i)},parse:function(t){var e=i.parse(t);return o.create({ciphertext:e})}},a.format.Hex)},3175:function(t,e,r){var n,o,i;t.exports=(o=(n=r(3074)).lib.Base,i=n.enc.Utf8,void(n.algo.HMAC=o.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=i.parse(e));var r=t.blockSize,n=4*r;e.sigBytes>n&&(e=t.finalize(e)),e.clamp();for(var o=this._oKey=e.clone(),a=this._iKey=e.clone(),s=o.words,c=a.words,l=0;l<r;l++)s[l]^=1549556828,c[l]^=909522486;o.sigBytes=a.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(r))}})))},3561:function(t,e,r){var n;t.exports=(n=r(3074),r(861),r(8835),r(4831),r(4165),r(3147),r(8022),r(6974),r(8653),r(9641),r(8846),r(8664),r(7370),r(2069),r(3175),r(3951),r(2248),r(7827),r(8633),r(9430),r(789),r(5661),r(9511),r(3795),r(6123),r(9643),r(6026),r(5493),r(8228),r(363),r(3412),r(5080),r(3912),r(1187),n)},8835:function(t,e,r){var n;t.exports=(n=r(3074),function(){if("function"==typeof ArrayBuffer){var t=n.lib.WordArray,e=t.init,r=t.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var r=t.byteLength,n=[],o=0;o<r;o++)n[o>>>2]|=t[o]<<24-o%4*8;e.call(this,n,r)}else e.apply(this,arguments)};r.prototype=t}}(),n.lib.WordArray)},8022:function(t,e,r){var n;t.exports=(n=r(3074),function(t){var e=n,r=e.lib,o=r.WordArray,i=r.Hasher,a=e.algo,s=[];!function(){for(var e=0;e<64;e++)s[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=a.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,o=t[n];t[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,a=t[e+0],c=t[e+1],d=t[e+2],p=t[e+3],_=t[e+4],m=t[e+5],g=t[e+6],v=t[e+7],w=t[e+8],y=t[e+9],b=t[e+10],k=t[e+11],E=t[e+12],x=t[e+13],A=t[e+14],S=t[e+15],B=i[0],z=i[1],D=i[2],R=i[3];B=l(B,z,D,R,a,7,s[0]),R=l(R,B,z,D,c,12,s[1]),D=l(D,R,B,z,d,17,s[2]),z=l(z,D,R,B,p,22,s[3]),B=l(B,z,D,R,_,7,s[4]),R=l(R,B,z,D,m,12,s[5]),D=l(D,R,B,z,g,17,s[6]),z=l(z,D,R,B,v,22,s[7]),B=l(B,z,D,R,w,7,s[8]),R=l(R,B,z,D,y,12,s[9]),D=l(D,R,B,z,b,17,s[10]),z=l(z,D,R,B,k,22,s[11]),B=l(B,z,D,R,E,7,s[12]),R=l(R,B,z,D,x,12,s[13]),D=l(D,R,B,z,A,17,s[14]),B=u(B,z=l(z,D,R,B,S,22,s[15]),D,R,c,5,s[16]),R=u(R,B,z,D,g,9,s[17]),D=u(D,R,B,z,k,14,s[18]),z=u(z,D,R,B,a,20,s[19]),B=u(B,z,D,R,m,5,s[20]),R=u(R,B,z,D,b,9,s[21]),D=u(D,R,B,z,S,14,s[22]),z=u(z,D,R,B,_,20,s[23]),B=u(B,z,D,R,y,5,s[24]),R=u(R,B,z,D,A,9,s[25]),D=u(D,R,B,z,p,14,s[26]),z=u(z,D,R,B,w,20,s[27]),B=u(B,z,D,R,x,5,s[28]),R=u(R,B,z,D,d,9,s[29]),D=u(D,R,B,z,v,14,s[30]),B=f(B,z=u(z,D,R,B,E,20,s[31]),D,R,m,4,s[32]),R=f(R,B,z,D,w,11,s[33]),D=f(D,R,B,z,k,16,s[34]),z=f(z,D,R,B,A,23,s[35]),B=f(B,z,D,R,c,4,s[36]),R=f(R,B,z,D,_,11,s[37]),D=f(D,R,B,z,v,16,s[38]),z=f(z,D,R,B,b,23,s[39]),B=f(B,z,D,R,x,4,s[40]),R=f(R,B,z,D,a,11,s[41]),D=f(D,R,B,z,p,16,s[42]),z=f(z,D,R,B,g,23,s[43]),B=f(B,z,D,R,y,4,s[44]),R=f(R,B,z,D,E,11,s[45]),D=f(D,R,B,z,S,16,s[46]),B=h(B,z=f(z,D,R,B,d,23,s[47]),D,R,a,6,s[48]),R=h(R,B,z,D,v,10,s[49]),D=h(D,R,B,z,A,15,s[50]),z=h(z,D,R,B,m,21,s[51]),B=h(B,z,D,R,E,6,s[52]),R=h(R,B,z,D,p,10,s[53]),D=h(D,R,B,z,b,15,s[54]),z=h(z,D,R,B,c,21,s[55]),B=h(B,z,D,R,w,6,s[56]),R=h(R,B,z,D,S,10,s[57]),D=h(D,R,B,z,g,15,s[58]),z=h(z,D,R,B,x,21,s[59]),B=h(B,z,D,R,_,6,s[60]),R=h(R,B,z,D,k,10,s[61]),D=h(D,R,B,z,d,15,s[62]),z=h(z,D,R,B,y,21,s[63]),i[0]=i[0]+B|0,i[1]=i[1]+z|0,i[2]=i[2]+D|0,i[3]=i[3]+R|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;r[o>>>5]|=128<<24-o%32;var i=t.floor(n/4294967296),a=n;r[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(r.length+1),this._process();for(var s=this._hash,c=s.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function l(t,e,r,n,o,i,a){var s=t+(e&r|~e&n)+o+a;return(s<<i|s>>>32-i)+e}function u(t,e,r,n,o,i,a){var s=t+(e&n|r&~n)+o+a;return(s<<i|s>>>32-i)+e}function f(t,e,r,n,o,i,a){var s=t+(e^r^n)+o+a;return(s<<i|s>>>32-i)+e}function h(t,e,r,n,o,i,a){var s=t+(r^(e|~n))+o+a;return(s<<i|s>>>32-i)+e}e.MD5=i._createHelper(c),e.HmacMD5=i._createHmacHelper(c)}(Math),n.MD5)},8633:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.mode.CFB=function(){var t=n.lib.BlockCipherMode.extend();function e(t,e,r,n){var o,i=this._iv;i?(o=i.slice(0),this._iv=void 0):o=this._prevBlock,n.encryptBlock(o,0);for(var a=0;a<r;a++)t[e+a]^=o[a]}return t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,o=n.blockSize;e.call(this,t,r,o,n),this._prevBlock=t.slice(r,r+o)}}),t.Decryptor=t.extend({processBlock:function(t,r){var n=this._cipher,o=n.blockSize,i=t.slice(r,r+o);e.call(this,t,r,o,n),this._prevBlock=i}}),t}(),n.mode.CFB)},789:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.mode.CTRGladman=function(){var t=n.lib.BlockCipherMode.extend();function e(t){if(255==(t>>24&255)){var e=t>>16&255,r=t>>8&255,n=255&t;255===e?(e=0,255===r?(r=0,255===n?n=0:++n):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=n}else t+=1<<24;return t}var r=t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,o=n.blockSize,i=this._iv,a=this._counter;i&&(a=this._counter=i.slice(0),this._iv=void 0),function(t){0===(t[0]=e(t[0]))&&(t[1]=e(t[1]))}(a);var s=a.slice(0);n.encryptBlock(s,0);for(var c=0;c<o;c++)t[r+c]^=s[c]}});return t.Decryptor=r,t}(),n.mode.CTRGladman)},9430:function(t,e,r){var n,o,i;t.exports=(i=r(3074),r(7827),i.mode.CTR=(o=(n=i.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,o=this._iv,i=this._counter;o&&(i=this._counter=o.slice(0),this._iv=void 0);var a=i.slice(0);r.encryptBlock(a,0),i[n-1]=i[n-1]+1|0;for(var s=0;s<n;s++)t[e+s]^=a[s]}}),n.Decryptor=o,n),i.mode.CTR)},9511:function(t,e,r){var n,o;t.exports=(o=r(3074),r(7827),o.mode.ECB=((n=o.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),n.Decryptor=n.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),n),o.mode.ECB)},5661:function(t,e,r){var n,o,i;t.exports=(i=r(3074),r(7827),i.mode.OFB=(o=(n=i.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,o=this._iv,i=this._keystream;o&&(i=this._keystream=o.slice(0),this._iv=void 0),r.encryptBlock(i,0);for(var a=0;a<n;a++)t[e+a]^=i[a]}}),n.Decryptor=o,n),i.mode.OFB)},3795:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,n=4*e,o=n-r%n,i=r+o-1;t.clamp(),t.words[i>>>2]|=o<<24-i%4*8,t.sigBytes+=o},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},n.pad.Ansix923)},6123:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.pad.Iso10126={pad:function(t,e){var r=4*e,o=r-t.sigBytes%r;t.concat(n.lib.WordArray.random(o-1)).concat(n.lib.WordArray.create([o<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},n.pad.Iso10126)},9643:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.pad.Iso97971={pad:function(t,e){t.concat(n.lib.WordArray.create([2147483648],1)),n.pad.ZeroPadding.pad(t,e)},unpad:function(t){n.pad.ZeroPadding.unpad(t),t.sigBytes--}},n.pad.Iso97971)},5493:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.pad.NoPadding={pad:function(){},unpad:function(){}},n.pad.NoPadding)},6026:function(t,e,r){var n;t.exports=(n=r(3074),r(7827),n.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},n.pad.ZeroPadding)},3951:function(t,e,r){var n,o,i,a,s,c,l,u,f;t.exports=(f=r(3074),r(6974),r(3175),i=(o=(n=f).lib).Base,a=o.WordArray,c=(s=n.algo).SHA1,l=s.HMAC,u=s.PBKDF2=i.extend({cfg:i.extend({keySize:4,hasher:c,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,n=l.create(r.hasher,t),o=a.create(),i=a.create([1]),s=o.words,c=i.words,u=r.keySize,f=r.iterations;s.length<u;){var h=n.update(e).finalize(i);n.reset();for(var d=h.words,p=d.length,_=h,m=1;m<f;m++){_=n.finalize(_),n.reset();for(var g=_.words,v=0;v<p;v++)d[v]^=g[v]}o.concat(h),c[0]++}return o.sigBytes=4*u,o}}),n.PBKDF2=function(t,e,r){return u.create(r).compute(t,e)},f.PBKDF2)},1187:function(t,e,r){var n;t.exports=(n=r(3074),r(4165),r(8022),r(2248),r(7827),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,o=[],i=[],a=[],s=r.RabbitLegacy=e.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var o=0;o<4;o++)c.call(this);for(o=0;o<8;o++)n[o]^=r[o+4&7];if(e){var i=e.words,a=i[0],s=i[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=l>>>16|4294901760&u,h=u<<16|65535&l;for(n[0]^=l,n[1]^=f,n[2]^=u,n[3]^=h,n[4]^=l,n[5]^=f,n[6]^=u,n[7]^=h,o=0;o<4;o++)c.call(this)}},_doProcessBlock:function(t,e){var r=this._X;c.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],o=65535&n,s=n>>>16,c=((o*o>>>17)+o*s>>>15)+s*s,l=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=c^l}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}t.RabbitLegacy=e._createHelper(s)}(),n.RabbitLegacy)},3912:function(t,e,r){var n;t.exports=(n=r(3074),r(4165),r(8022),r(2248),r(7827),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,o=[],i=[],a=[],s=r.Rabbit=e.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],o=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)o[r]^=n[r+4&7];if(e){var i=e.words,a=i[0],s=i[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=l>>>16|4294901760&u,h=u<<16|65535&l;for(o[0]^=l,o[1]^=f,o[2]^=u,o[3]^=h,o[4]^=l,o[5]^=f,o[6]^=u,o[7]^=h,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(t,e){var r=this._X;c.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],o=65535&n,s=n>>>16,c=((o*o>>>17)+o*s>>>15)+s*s,l=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=c^l}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}t.Rabbit=e._createHelper(s)}(),n.Rabbit)},5080:function(t,e,r){var n;t.exports=(n=r(3074),r(4165),r(8022),r(2248),r(7827),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,o=r.RC4=e.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,n=this._S=[],o=0;o<256;o++)n[o]=o;o=0;for(var i=0;o<256;o++){var a=o%r,s=e[a>>>2]>>>24-a%4*8&255;i=(i+n[o]+s)%256;var c=n[o];n[o]=n[i],n[i]=c}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var t=this._S,e=this._i,r=this._j,n=0,o=0;o<4;o++){r=(r+t[e=(e+1)%256])%256;var i=t[e];t[e]=t[r],t[r]=i,n|=t[(t[e]+t[r])%256]<<24-8*o}return this._i=e,this._j=r,n}t.RC4=e._createHelper(o);var a=r.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)i.call(this)}});t.RC4Drop=e._createHelper(a)}(),n.RC4)},2069:function(t,e,r){var n;t.exports=(n=r(3074),function(t){var e=n,r=e.lib,o=r.WordArray,i=r.Hasher,a=e.algo,s=o.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=o.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=o.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),u=o.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),f=o.create([0,1518500249,1859775393,2400959708,2840853838]),h=o.create([1352829926,1548603684,1836072691,2053994217,0]),d=a.RIPEMD160=i.extend({_doReset:function(){this._hash=o.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,o=t[n];t[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i,a,d,y,b,k,E,x,A,S,B,z=this._hash.words,D=f.words,R=h.words,T=s.words,F=c.words,C=l.words,M=u.words;for(k=i=z[0],E=a=z[1],x=d=z[2],A=y=z[3],S=b=z[4],r=0;r<80;r+=1)B=i+t[e+T[r]]|0,B+=r<16?p(a,d,y)+D[0]:r<32?_(a,d,y)+D[1]:r<48?m(a,d,y)+D[2]:r<64?g(a,d,y)+D[3]:v(a,d,y)+D[4],B=(B=w(B|=0,C[r]))+b|0,i=b,b=y,y=w(d,10),d=a,a=B,B=k+t[e+F[r]]|0,B+=r<16?v(E,x,A)+R[0]:r<32?g(E,x,A)+R[1]:r<48?m(E,x,A)+R[2]:r<64?_(E,x,A)+R[3]:p(E,x,A)+R[4],B=(B=w(B|=0,M[r]))+S|0,k=S,S=A,A=w(x,10),x=E,E=B;B=z[1]+d+A|0,z[1]=z[2]+y+S|0,z[2]=z[3]+b+k|0,z[3]=z[4]+i+E|0,z[4]=z[0]+a+x|0,z[0]=B},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var o=this._hash,i=o.words,a=0;a<5;a++){var s=i[a];i[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return o},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function p(t,e,r){return t^e^r}function _(t,e,r){return t&e|~t&r}function m(t,e,r){return(t|~e)^r}function g(t,e,r){return t&r|e&~r}function v(t,e,r){return t^(e|~r)}function w(t,e){return t<<e|t>>>32-e}e.RIPEMD160=i._createHelper(d),e.HmacRIPEMD160=i._createHmacHelper(d)}(Math),n.RIPEMD160)},6974:function(t,e,r){var n,o,i,a,s,c,l,u;t.exports=(o=(n=u=r(3074)).lib,i=o.WordArray,a=o.Hasher,s=n.algo,c=[],l=s.SHA1=a.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],l=0;l<80;l++){if(l<16)c[l]=0|t[e+l];else{var u=c[l-3]^c[l-8]^c[l-14]^c[l-16];c[l]=u<<1|u>>>31}var f=(n<<5|n>>>27)+s+c[l];f+=l<20?1518500249+(o&i|~o&a):l<40?1859775393+(o^i^a):l<60?(o&i|o&a|i&a)-1894007588:(o^i^a)-899497514,s=a,a=i,i=o<<30|o>>>2,o=n,n=f}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+i|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}}),n.SHA1=a._createHelper(l),n.HmacSHA1=a._createHmacHelper(l),u.SHA1)},9641:function(t,e,r){var n,o,i,a,s,c;t.exports=(c=r(3074),r(8653),o=(n=c).lib.WordArray,i=n.algo,a=i.SHA256,s=i.SHA224=a.extend({_doReset:function(){this._hash=new o.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=a._doFinalize.call(this);return t.sigBytes-=4,t}}),n.SHA224=a._createHelper(s),n.HmacSHA224=a._createHmacHelper(s),c.SHA224)},8653:function(t,e,r){var n;t.exports=(n=r(3074),function(t){var e=n,r=e.lib,o=r.WordArray,i=r.Hasher,a=e.algo,s=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}for(var n=2,o=0;o<64;)e(n)&&(o<8&&(s[o]=r(t.pow(n,.5))),c[o]=r(t.pow(n,1/3)),o++),n++}();var l=[],u=a.SHA256=i.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],u=r[5],f=r[6],h=r[7],d=0;d<64;d++){if(d<16)l[d]=0|t[e+d];else{var p=l[d-15],_=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=l[d-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[d]=_+l[d-7]+g+l[d-16]}var v=n&o^n&i^o&i,w=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),y=h+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&u^~s&f)+c[d]+l[d];h=f,f=u,u=s,s=a+y|0,a=i,i=o,o=n,n=y+(w+v)|0}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+i|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+u|0,r[6]=r[6]+f|0,r[7]=r[7]+h|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return r[o>>>5]|=128<<24-o%32,r[14+(o+64>>>9<<4)]=t.floor(n/4294967296),r[15+(o+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=i._createHelper(u),e.HmacSHA256=i._createHmacHelper(u)}(Math),n.SHA256)},7370:function(t,e,r){var n;t.exports=(n=r(3074),r(861),function(t){var e=n,r=e.lib,o=r.WordArray,i=r.Hasher,a=e.x64.Word,s=e.algo,c=[],l=[],u=[];!function(){for(var t=1,e=0,r=0;r<24;r++){c[t+5*e]=(r+1)*(r+2)/2%64;var n=(2*t+3*e)%5;t=e%5,e=n}for(t=0;t<5;t++)for(e=0;e<5;e++)l[t+5*e]=e+(2*t+3*e)%5*5;for(var o=1,i=0;i<24;i++){for(var s=0,f=0,h=0;h<7;h++){if(1&o){var d=(1<<h)-1;d<32?f^=1<<d:s^=1<<d-32}128&o?o=o<<1^113:o<<=1}u[i]=a.create(s,f)}}();var f=[];!function(){for(var t=0;t<25;t++)f[t]=a.create()}();var h=s.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,n=this.blockSize/2,o=0;o<n;o++){var i=t[e+2*o],a=t[e+2*o+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(z=r[o]).high^=a,z.low^=i}for(var s=0;s<24;s++){for(var h=0;h<5;h++){for(var d=0,p=0,_=0;_<5;_++)d^=(z=r[h+5*_]).high,p^=z.low;var m=f[h];m.high=d,m.low=p}for(h=0;h<5;h++){var g=f[(h+4)%5],v=f[(h+1)%5],w=v.high,y=v.low;for(d=g.high^(w<<1|y>>>31),p=g.low^(y<<1|w>>>31),_=0;_<5;_++)(z=r[h+5*_]).high^=d,z.low^=p}for(var b=1;b<25;b++){var k=(z=r[b]).high,E=z.low,x=c[b];x<32?(d=k<<x|E>>>32-x,p=E<<x|k>>>32-x):(d=E<<x-32|k>>>64-x,p=k<<x-32|E>>>64-x);var A=f[l[b]];A.high=d,A.low=p}var S=f[0],B=r[0];for(S.high=B.high,S.low=B.low,h=0;h<5;h++)for(_=0;_<5;_++){var z=r[b=h+5*_],D=f[b],R=f[(h+1)%5+5*_],T=f[(h+2)%5+5*_];z.high=D.high^~R.high&T.high,z.low=D.low^~R.low&T.low}z=r[0];var F=u[s];z.high^=F.high,z.low^=F.low}},_doFinalize:function(){var e=this._data,r=e.words,n=(this._nDataBytes,8*e.sigBytes),i=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(t.ceil((n+1)/i)*i>>>5)-1]|=128,e.sigBytes=4*r.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,l=[],u=0;u<c;u++){var f=a[u],h=f.high,d=f.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),l.push(d),l.push(h)}return new o.init(l,s)},clone:function(){for(var t=i.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});e.SHA3=i._createHelper(h),e.HmacSHA3=i._createHmacHelper(h)}(Math),n.SHA3)},8664:function(t,e,r){var n,o,i,a,s,c,l,u;t.exports=(u=r(3074),r(861),r(8846),o=(n=u).x64,i=o.Word,a=o.WordArray,s=n.algo,c=s.SHA512,l=s.SHA384=c.extend({_doReset:function(){this._hash=new a.init([new i.init(3418070365,3238371032),new i.init(1654270250,914150663),new i.init(2438529370,812702999),new i.init(355462360,4144912697),new i.init(1731405415,4290775857),new i.init(2394180231,1750603025),new i.init(3675008525,1694076839),new i.init(1203062813,3204075428)])},_doFinalize:function(){var t=c._doFinalize.call(this);return t.sigBytes-=16,t}}),n.SHA384=c._createHelper(l),n.HmacSHA384=c._createHmacHelper(l),u.SHA384)},8846:function(t,e,r){var n;t.exports=(n=r(3074),r(861),function(){var t=n,e=t.lib.Hasher,r=t.x64,o=r.Word,i=r.WordArray,a=t.algo;function s(){return o.create.apply(o,arguments)}var c=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],l=[];!function(){for(var t=0;t<80;t++)l[t]=s()}();var u=a.SHA512=e.extend({_doReset:function(){this._hash=new i.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],u=r[5],f=r[6],h=r[7],d=n.high,p=n.low,_=o.high,m=o.low,g=i.high,v=i.low,w=a.high,y=a.low,b=s.high,k=s.low,E=u.high,x=u.low,A=f.high,S=f.low,B=h.high,z=h.low,D=d,R=p,T=_,F=m,C=g,M=v,P=w,H=y,O=b,U=k,N=E,I=x,j=A,L=S,W=B,Z=z,K=0;K<80;K++){var X,q,G=l[K];if(K<16)q=G.high=0|t[e+2*K],X=G.low=0|t[e+2*K+1];else{var V=l[K-15],$=V.high,Y=V.low,J=($>>>1|Y<<31)^($>>>8|Y<<24)^$>>>7,Q=(Y>>>1|$<<31)^(Y>>>8|$<<24)^(Y>>>7|$<<25),tt=l[K-2],et=tt.high,rt=tt.low,nt=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,ot=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),it=l[K-7],at=it.high,st=it.low,ct=l[K-16],lt=ct.high,ut=ct.low;q=(q=(q=J+at+((X=Q+st)>>>0<Q>>>0?1:0))+nt+((X+=ot)>>>0<ot>>>0?1:0))+lt+((X+=ut)>>>0<ut>>>0?1:0),G.high=q,G.low=X}var ft,ht=O&N^~O&j,dt=U&I^~U&L,pt=D&T^D&C^T&C,_t=R&F^R&M^F&M,mt=(D>>>28|R<<4)^(D<<30|R>>>2)^(D<<25|R>>>7),gt=(R>>>28|D<<4)^(R<<30|D>>>2)^(R<<25|D>>>7),vt=(O>>>14|U<<18)^(O>>>18|U<<14)^(O<<23|U>>>9),wt=(U>>>14|O<<18)^(U>>>18|O<<14)^(U<<23|O>>>9),yt=c[K],bt=yt.high,kt=yt.low,Et=W+vt+((ft=Z+wt)>>>0<Z>>>0?1:0),xt=gt+_t;W=j,Z=L,j=N,L=I,N=O,I=U,O=P+(Et=(Et=(Et=Et+ht+((ft+=dt)>>>0<dt>>>0?1:0))+bt+((ft+=kt)>>>0<kt>>>0?1:0))+q+((ft+=X)>>>0<X>>>0?1:0))+((U=H+ft|0)>>>0<H>>>0?1:0)|0,P=C,H=M,C=T,M=F,T=D,F=R,D=Et+(mt+pt+(xt>>>0<gt>>>0?1:0))+((R=ft+xt|0)>>>0<ft>>>0?1:0)|0}p=n.low=p+R,n.high=d+D+(p>>>0<R>>>0?1:0),m=o.low=m+F,o.high=_+T+(m>>>0<F>>>0?1:0),v=i.low=v+M,i.high=g+C+(v>>>0<M>>>0?1:0),y=a.low=y+H,a.high=w+P+(y>>>0<H>>>0?1:0),k=s.low=k+U,s.high=b+O+(k>>>0<U>>>0?1:0),x=u.low=x+I,u.high=E+N+(x>>>0<I>>>0?1:0),S=f.low=S+L,f.high=A+j+(S>>>0<L>>>0?1:0),z=h.low=z+Z,h.high=B+W+(z>>>0<Z>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});t.SHA512=e._createHelper(u),t.HmacSHA512=e._createHmacHelper(u)}(),n.SHA512)},3412:function(t,e,r){var n;t.exports=(n=r(3074),r(4165),r(8022),r(2248),r(7827),function(){var t=n,e=t.lib,r=e.WordArray,o=e.BlockCipher,i=t.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=i.DES=o.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var n=a[r]-1;e[r]=t[n>>>5]>>>31-n%32&1}for(var o=this._subKeys=[],i=0;i<16;i++){var l=o[i]=[],u=c[i];for(r=0;r<24;r++)l[r/6|0]|=e[(s[r]-1+u)%28]<<31-r%6,l[4+(r/6|0)]|=e[28+(s[r+24]-1+u)%28]<<31-r%6;for(l[0]=l[0]<<1|l[0]>>>31,r=1;r<7;r++)l[r]=l[r]>>>4*(r-1)+3;l[7]=l[7]<<5|l[7]>>>27}var f=this._invSubKeys=[];for(r=0;r<16;r++)f[r]=o[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],h.call(this,4,252645135),h.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),h.call(this,1,1431655765);for(var n=0;n<16;n++){for(var o=r[n],i=this._lBlock,a=this._rBlock,s=0,c=0;c<8;c++)s|=l[c][((a^o[c])&u[c])>>>0];this._lBlock=a,this._rBlock=i^s}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,h.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function d(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}t.DES=o._createHelper(f);var p=i.TripleDES=o.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),n=t.length<4?t.slice(0,2):t.slice(2,4),o=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=f.createEncryptor(r.create(e)),this._des2=f.createEncryptor(r.create(n)),this._des3=f.createEncryptor(r.create(o))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=o._createHelper(p)}(),n.TripleDES)},861:function(t,e,r){var n,o,i,a,s,c;t.exports=(n=r(3074),i=(o=n).lib,a=i.Base,s=i.WordArray,(c=o.x64={}).Word=a.extend({init:function(t,e){this.high=t,this.low=e}}),c.WordArray=a.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var o=t[n];r.push(o.high),r.push(o.low)}return s.create(r,this.sigBytes)},clone:function(){for(var t=a.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}}),n)},5248:(t,e,r)=>{"use strict";const{Deflate:n,deflate:o,deflateRaw:i,gzip:a}=r(2463),{Inflate:s,inflate:c,inflateRaw:l,ungzip:u}=r(4294),f=r(6735);t.exports.Deflate=n,t.exports.deflate=o,t.exports.deflateRaw=i,t.exports.gzip=a,t.exports.Inflate=s,t.exports.inflate=c,t.exports.inflateRaw=l,t.exports.ungzip=u,t.exports.constants=f},2463:(t,e,r)=>{"use strict";const n=r(9647),o=r(4424),i=r(2641),a=r(699),s=r(3502),c=Object.prototype.toString,{Z_NO_FLUSH:l,Z_SYNC_FLUSH:u,Z_FULL_FLUSH:f,Z_FINISH:h,Z_OK:d,Z_STREAM_END:p,Z_DEFAULT_COMPRESSION:_,Z_DEFAULT_STRATEGY:m,Z_DEFLATED:g}=r(6735);function v(t){this.options=o.assign({level:_,method:g,chunkSize:16384,windowBits:15,memLevel:8,strategy:m},t||{});let e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;let r=n.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(r!==d)throw new Error(a[r]);if(e.header&&n.deflateSetHeader(this.strm,e.header),e.dictionary){let t;if(t="string"==typeof e.dictionary?i.string2buf(e.dictionary):"[object ArrayBuffer]"===c.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,r=n.deflateSetDictionary(this.strm,t),r!==d)throw new Error(a[r]);this._dict_set=!0}}function w(t,e){const r=new v(e);if(r.push(t,!0),r.err)throw r.msg||a[r.err];return r.result}v.prototype.push=function(t,e){const r=this.strm,o=this.options.chunkSize;let a,s;if(this.ended)return!1;for(s=e===~~e?e:!0===e?h:l,"string"==typeof t?r.input=i.string2buf(t):"[object ArrayBuffer]"===c.call(t)?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;;)if(0===r.avail_out&&(r.output=new Uint8Array(o),r.next_out=0,r.avail_out=o),(s===u||s===f)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if(a=n.deflate(r,s),a===p)return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),a=n.deflateEnd(this.strm),this.onEnd(a),this.ended=!0,a===d;if(0!==r.avail_out){if(s>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(0===r.avail_in)break}else this.onData(r.output)}return!0},v.prototype.onData=function(t){this.chunks.push(t)},v.prototype.onEnd=function(t){t===d&&(this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},t.exports.Deflate=v,t.exports.deflate=w,t.exports.deflateRaw=function(t,e){return(e=e||{}).raw=!0,w(t,e)},t.exports.gzip=function(t,e){return(e=e||{}).gzip=!0,w(t,e)},t.exports.constants=r(6735)},4294:(t,e,r)=>{"use strict";const n=r(7709),o=r(4424),i=r(2641),a=r(699),s=r(3502),c=r(8938),l=Object.prototype.toString,{Z_NO_FLUSH:u,Z_FINISH:f,Z_OK:h,Z_STREAM_END:d,Z_NEED_DICT:p,Z_STREAM_ERROR:_,Z_DATA_ERROR:m,Z_MEM_ERROR:g}=r(6735);function v(t){this.options=o.assign({chunkSize:65536,windowBits:15,to:""},t||{});const e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;let r=n.inflateInit2(this.strm,e.windowBits);if(r!==h)throw new Error(a[r]);if(this.header=new c,n.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=i.string2buf(e.dictionary):"[object ArrayBuffer]"===l.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(r=n.inflateSetDictionary(this.strm,e.dictionary),r!==h)))throw new Error(a[r])}function w(t,e){const r=new v(e);if(r.push(t),r.err)throw r.msg||a[r.err];return r.result}v.prototype.push=function(t,e){const r=this.strm,o=this.options.chunkSize,a=this.options.dictionary;let s,c,v;if(this.ended)return!1;for(c=e===~~e?e:!0===e?f:u,"[object ArrayBuffer]"===l.call(t)?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;;){for(0===r.avail_out&&(r.output=new Uint8Array(o),r.next_out=0,r.avail_out=o),s=n.inflate(r,c),s===p&&a&&(s=n.inflateSetDictionary(r,a),s===h?s=n.inflate(r,c):s===m&&(s=p));r.avail_in>0&&s===d&&r.state.wrap>0&&0!==t[r.next_in];)n.inflateReset(r),s=n.inflate(r,c);switch(s){case _:case m:case p:case g:return this.onEnd(s),this.ended=!0,!1}if(v=r.avail_out,r.next_out&&(0===r.avail_out||s===d))if("string"===this.options.to){let t=i.utf8border(r.output,r.next_out),e=r.next_out-t,n=i.buf2string(r.output,t);r.next_out=e,r.avail_out=o-e,e&&r.output.set(r.output.subarray(t,t+e),0),this.onData(n)}else this.onData(r.output.length===r.next_out?r.output:r.output.subarray(0,r.next_out));if(s!==h||0!==v){if(s===d)return s=n.inflateEnd(this.strm),this.onEnd(s),this.ended=!0,!0;if(0===r.avail_in)break}}return!0},v.prototype.onData=function(t){this.chunks.push(t)},v.prototype.onEnd=function(t){t===h&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},t.exports.Inflate=v,t.exports.inflate=w,t.exports.inflateRaw=function(t,e){return(e=e||{}).raw=!0,w(t,e)},t.exports.ungzip=w,t.exports.constants=r(6735)},4424:t=>{"use strict";const e=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);t.exports.assign=function(t){const r=Array.prototype.slice.call(arguments,1);for(;r.length;){const n=r.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(const r in n)e(n,r)&&(t[r]=n[r])}}return t},t.exports.flattenChunks=t=>{let e=0;for(let r=0,n=t.length;r<n;r++)e+=t[r].length;const r=new Uint8Array(e);for(let e=0,n=0,o=t.length;e<o;e++){let o=t[e];r.set(o,n),n+=o.length}return r}},2641:t=>{"use strict";let e=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){e=!1}const r=new Uint8Array(256);for(let t=0;t<256;t++)r[t]=t>=252?6:t>=248?5:t>=240?4:t>=224?3:t>=192?2:1;r[254]=r[254]=1,t.exports.string2buf=t=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);let e,r,n,o,i,a=t.length,s=0;for(o=0;o<a;o++)r=t.charCodeAt(o),55296==(64512&r)&&o+1<a&&(n=t.charCodeAt(o+1),56320==(64512&n)&&(r=65536+(r-55296<<10)+(n-56320),o++)),s+=r<128?1:r<2048?2:r<65536?3:4;for(e=new Uint8Array(s),i=0,o=0;i<s;o++)r=t.charCodeAt(o),55296==(64512&r)&&o+1<a&&(n=t.charCodeAt(o+1),56320==(64512&n)&&(r=65536+(r-55296<<10)+(n-56320),o++)),r<128?e[i++]=r:r<2048?(e[i++]=192|r>>>6,e[i++]=128|63&r):r<65536?(e[i++]=224|r>>>12,e[i++]=128|r>>>6&63,e[i++]=128|63&r):(e[i++]=240|r>>>18,e[i++]=128|r>>>12&63,e[i++]=128|r>>>6&63,e[i++]=128|63&r);return e},t.exports.buf2string=(t,n)=>{const o=n||t.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(t.subarray(0,n));let i,a;const s=new Array(2*o);for(a=0,i=0;i<o;){let e=t[i++];if(e<128){s[a++]=e;continue}let n=r[e];if(n>4)s[a++]=65533,i+=n-1;else{for(e&=2===n?31:3===n?15:7;n>1&&i<o;)e=e<<6|63&t[i++],n--;n>1?s[a++]=65533:e<65536?s[a++]=e:(e-=65536,s[a++]=55296|e>>10&1023,s[a++]=56320|1023&e)}}return((t,r)=>{if(r<65534&&t.subarray&&e)return String.fromCharCode.apply(null,t.length===r?t:t.subarray(0,r));let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n})(s,a)},t.exports.utf8border=(t,e)=>{(e=e||t.length)>t.length&&(e=t.length);let n=e-1;for(;n>=0&&128==(192&t[n]);)n--;return n<0||0===n?e:n+r[t[n]]>e?n:e}},6533:t=>{"use strict";t.exports=(t,e,r,n)=>{let o=65535&t|0,i=t>>>16&65535|0,a=0;for(;0!==r;){a=r>2e3?2e3:r,r-=a;do{o=o+e[n++]|0,i=i+o|0}while(--a);o%=65521,i%=65521}return o|i<<16|0}},6735:t=>{"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},3477:t=>{"use strict";const e=new Uint32Array((()=>{let t,e=[];for(var r=0;r<256;r++){t=r;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e})());t.exports=(t,r,n,o)=>{const i=e,a=o+n;t^=-1;for(let e=o;e<a;e++)t=t>>>8^i[255&(t^r[e])];return-1^t}},9647:(t,e,r)=>{"use strict";const{_tr_init:n,_tr_stored_block:o,_tr_flush_block:i,_tr_tally:a,_tr_align:s}=r(3054),c=r(6533),l=r(3477),u=r(699),{Z_NO_FLUSH:f,Z_PARTIAL_FLUSH:h,Z_FULL_FLUSH:d,Z_FINISH:p,Z_BLOCK:_,Z_OK:m,Z_STREAM_END:g,Z_STREAM_ERROR:v,Z_DATA_ERROR:w,Z_BUF_ERROR:y,Z_DEFAULT_COMPRESSION:b,Z_FILTERED:k,Z_HUFFMAN_ONLY:E,Z_RLE:x,Z_FIXED:A,Z_DEFAULT_STRATEGY:S,Z_UNKNOWN:B,Z_DEFLATED:z}=r(6735),D=258,R=262,T=42,F=113,C=666,M=(t,e)=>(t.msg=u[e],e),P=t=>2*t-(t>4?9:0),H=t=>{let e=t.length;for(;--e>=0;)t[e]=0},O=t=>{let e,r,n,o=t.w_size;e=t.hash_size,n=e;do{r=t.head[--n],t.head[n]=r>=o?r-o:0}while(--e);e=o,n=e;do{r=t.prev[--n],t.prev[n]=r>=o?r-o:0}while(--e)};let U=(t,e,r)=>(e<<t.hash_shift^r)&t.hash_mask;const N=t=>{const e=t.state;let r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+r),t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))},I=(t,e)=>{i(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,N(t.strm)},j=(t,e)=>{t.pending_buf[t.pending++]=e},L=(t,e)=>{t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},W=(t,e,r,n)=>{let o=t.avail_in;return o>n&&(o=n),0===o?0:(t.avail_in-=o,e.set(t.input.subarray(t.next_in,t.next_in+o),r),1===t.state.wrap?t.adler=c(t.adler,e,o,r):2===t.state.wrap&&(t.adler=l(t.adler,e,o,r)),t.next_in+=o,t.total_in+=o,o)},Z=(t,e)=>{let r,n,o=t.max_chain_length,i=t.strstart,a=t.prev_length,s=t.nice_match;const c=t.strstart>t.w_size-R?t.strstart-(t.w_size-R):0,l=t.window,u=t.w_mask,f=t.prev,h=t.strstart+D;let d=l[i+a-1],p=l[i+a];t.prev_length>=t.good_match&&(o>>=2),s>t.lookahead&&(s=t.lookahead);do{if(r=e,l[r+a]===p&&l[r+a-1]===d&&l[r]===l[i]&&l[++r]===l[i+1]){i+=2,r++;do{}while(l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&l[++i]===l[++r]&&i<h);if(n=D-(h-i),i=h-D,n>a){if(t.match_start=e,a=n,n>=s)break;d=l[i+a-1],p=l[i+a]}}}while((e=f[e&u])>c&&0!=--o);return a<=t.lookahead?a:t.lookahead},K=t=>{const e=t.w_size;let r,n,o;do{if(n=t.window_size-t.lookahead-t.strstart,t.strstart>=e+(e-R)&&(t.window.set(t.window.subarray(e,e+e-n),0),t.match_start-=e,t.strstart-=e,t.block_start-=e,t.insert>t.strstart&&(t.insert=t.strstart),O(t),n+=e),0===t.strm.avail_in)break;if(r=W(t.strm,t.window,t.strstart+t.lookahead,n),t.lookahead+=r,t.lookahead+t.insert>=3)for(o=t.strstart-t.insert,t.ins_h=t.window[o],t.ins_h=U(t,t.ins_h,t.window[o+1]);t.insert&&(t.ins_h=U(t,t.ins_h,t.window[o+3-1]),t.prev[o&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=o,o++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<R&&0!==t.strm.avail_in)},X=(t,e)=>{let r,n,i,a=t.pending_buf_size-5>t.w_size?t.w_size:t.pending_buf_size-5,s=0,c=t.strm.avail_in;do{if(r=65535,i=t.bi_valid+42>>3,t.strm.avail_out<i)break;if(i=t.strm.avail_out-i,n=t.strstart-t.block_start,r>n+t.strm.avail_in&&(r=n+t.strm.avail_in),r>i&&(r=i),r<a&&(0===r&&e!==p||e===f||r!==n+t.strm.avail_in))break;s=e===p&&r===n+t.strm.avail_in?1:0,o(t,0,0,s),t.pending_buf[t.pending-4]=r,t.pending_buf[t.pending-3]=r>>8,t.pending_buf[t.pending-2]=~r,t.pending_buf[t.pending-1]=~r>>8,N(t.strm),n&&(n>r&&(n=r),t.strm.output.set(t.window.subarray(t.block_start,t.block_start+n),t.strm.next_out),t.strm.next_out+=n,t.strm.avail_out-=n,t.strm.total_out+=n,t.block_start+=n,r-=n),r&&(W(t.strm,t.strm.output,t.strm.next_out,r),t.strm.next_out+=r,t.strm.avail_out-=r,t.strm.total_out+=r)}while(0===s);return c-=t.strm.avail_in,c&&(c>=t.w_size?(t.matches=2,t.window.set(t.strm.input.subarray(t.strm.next_in-t.w_size,t.strm.next_in),0),t.strstart=t.w_size,t.insert=t.strstart):(t.window_size-t.strstart<=c&&(t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,t.insert>t.strstart&&(t.insert=t.strstart)),t.window.set(t.strm.input.subarray(t.strm.next_in-c,t.strm.next_in),t.strstart),t.strstart+=c,t.insert+=c>t.w_size-t.insert?t.w_size-t.insert:c),t.block_start=t.strstart),t.high_water<t.strstart&&(t.high_water=t.strstart),s?4:e!==f&&e!==p&&0===t.strm.avail_in&&t.strstart===t.block_start?2:(i=t.window_size-t.strstart,t.strm.avail_in>i&&t.block_start>=t.w_size&&(t.block_start-=t.w_size,t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,i+=t.w_size,t.insert>t.strstart&&(t.insert=t.strstart)),i>t.strm.avail_in&&(i=t.strm.avail_in),i&&(W(t.strm,t.window,t.strstart,i),t.strstart+=i,t.insert+=i>t.w_size-t.insert?t.w_size-t.insert:i),t.high_water<t.strstart&&(t.high_water=t.strstart),i=t.bi_valid+42>>3,i=t.pending_buf_size-i>65535?65535:t.pending_buf_size-i,a=i>t.w_size?t.w_size:i,n=t.strstart-t.block_start,(n>=a||(n||e===p)&&e!==f&&0===t.strm.avail_in&&n<=i)&&(r=n>i?i:n,s=e===p&&0===t.strm.avail_in&&r===n?1:0,o(t,t.block_start,r,s),t.block_start+=r,N(t.strm)),s?3:1)},q=(t,e)=>{let r,n;for(;;){if(t.lookahead<R){if(K(t),t.lookahead<R&&e===f)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=U(t,t.ins_h,t.window[t.strstart+3-1]),r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-R&&(t.match_length=Z(t,r)),t.match_length>=3)if(n=a(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=U(t,t.ins_h,t.window[t.strstart+3-1]),r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=U(t,t.ins_h,t.window[t.strstart+1]);else n=a(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(I(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===p?(I(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(I(t,!1),0===t.strm.avail_out)?1:2},G=(t,e)=>{let r,n,o;for(;;){if(t.lookahead<R){if(K(t),t.lookahead<R&&e===f)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=U(t,t.ins_h,t.window[t.strstart+3-1]),r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-R&&(t.match_length=Z(t,r),t.match_length<=5&&(t.strategy===k||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){o=t.strstart+t.lookahead-3,n=a(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=o&&(t.ins_h=U(t,t.ins_h,t.window[t.strstart+3-1]),r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(I(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if(n=a(t,0,t.window[t.strstart-1]),n&&I(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=a(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===p?(I(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(I(t,!1),0===t.strm.avail_out)?1:2};function V(t,e,r,n,o){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=o}const $=[new V(0,0,0,0,X),new V(4,4,8,4,q),new V(4,5,16,8,q),new V(4,6,32,32,q),new V(4,4,16,16,G),new V(8,16,32,32,G),new V(8,16,128,128,G),new V(8,32,128,256,G),new V(32,128,258,1024,G),new V(32,258,258,4096,G)];function Y(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=z,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),H(this.dyn_ltree),H(this.dyn_dtree),H(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),H(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),H(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const J=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||e.status!==T&&57!==e.status&&69!==e.status&&73!==e.status&&91!==e.status&&103!==e.status&&e.status!==F&&e.status!==C?1:0},Q=t=>{if(J(t))return M(t,v);t.total_in=t.total_out=0,t.data_type=B;const e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=2===e.wrap?57:e.wrap?T:F,t.adler=2===e.wrap?0:1,e.last_flush=-2,n(e),m},tt=t=>{const e=Q(t);var r;return e===m&&((r=t.state).window_size=2*r.w_size,H(r.head),r.max_lazy_match=$[r.level].max_lazy,r.good_match=$[r.level].good_length,r.nice_match=$[r.level].nice_length,r.max_chain_length=$[r.level].max_chain,r.strstart=0,r.block_start=0,r.lookahead=0,r.insert=0,r.match_length=r.prev_length=2,r.match_available=0,r.ins_h=0),e},et=(t,e,r,n,o,i)=>{if(!t)return v;let a=1;if(e===b&&(e=6),n<0?(a=0,n=-n):n>15&&(a=2,n-=16),o<1||o>9||r!==z||n<8||n>15||e<0||e>9||i<0||i>A||8===n&&1!==a)return M(t,v);8===n&&(n=9);const s=new Y;return t.state=s,s.strm=t,s.status=T,s.wrap=a,s.gzhead=null,s.w_bits=n,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=o+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<o+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.sym_buf=s.lit_bufsize,s.sym_end=3*(s.lit_bufsize-1),s.level=e,s.strategy=i,s.method=r,tt(t)};t.exports.deflateInit=(t,e)=>et(t,e,z,15,8,S),t.exports.deflateInit2=et,t.exports.deflateReset=tt,t.exports.deflateResetKeep=Q,t.exports.deflateSetHeader=(t,e)=>J(t)||2!==t.state.wrap?v:(t.state.gzhead=e,m),t.exports.deflate=(t,e)=>{if(J(t)||e>_||e<0)return t?M(t,v):v;const r=t.state;if(!t.output||0!==t.avail_in&&!t.input||r.status===C&&e!==p)return M(t,0===t.avail_out?y:v);const n=r.last_flush;if(r.last_flush=e,0!==r.pending){if(N(t),0===t.avail_out)return r.last_flush=-1,m}else if(0===t.avail_in&&P(e)<=P(n)&&e!==p)return M(t,y);if(r.status===C&&0!==t.avail_in)return M(t,y);if(r.status===T&&0===r.wrap&&(r.status=F),r.status===T){let e=z+(r.w_bits-8<<4)<<8,n=-1;if(n=r.strategy>=E||r.level<2?0:r.level<6?1:6===r.level?2:3,e|=n<<6,0!==r.strstart&&(e|=32),e+=31-e%31,L(r,e),0!==r.strstart&&(L(r,t.adler>>>16),L(r,65535&t.adler)),t.adler=1,r.status=F,N(t),0!==r.pending)return r.last_flush=-1,m}if(57===r.status)if(t.adler=0,j(r,31),j(r,139),j(r,8),r.gzhead)j(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),j(r,255&r.gzhead.time),j(r,r.gzhead.time>>8&255),j(r,r.gzhead.time>>16&255),j(r,r.gzhead.time>>24&255),j(r,9===r.level?2:r.strategy>=E||r.level<2?4:0),j(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(j(r,255&r.gzhead.extra.length),j(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(t.adler=l(t.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69;else if(j(r,0),j(r,0),j(r,0),j(r,0),j(r,0),j(r,9===r.level?2:r.strategy>=E||r.level<2?4:0),j(r,3),r.status=F,N(t),0!==r.pending)return r.last_flush=-1,m;if(69===r.status){if(r.gzhead.extra){let e=r.pending,n=(65535&r.gzhead.extra.length)-r.gzindex;for(;r.pending+n>r.pending_buf_size;){let o=r.pending_buf_size-r.pending;if(r.pending_buf.set(r.gzhead.extra.subarray(r.gzindex,r.gzindex+o),r.pending),r.pending=r.pending_buf_size,r.gzhead.hcrc&&r.pending>e&&(t.adler=l(t.adler,r.pending_buf,r.pending-e,e)),r.gzindex+=o,N(t),0!==r.pending)return r.last_flush=-1,m;e=0,n-=o}let o=new Uint8Array(r.gzhead.extra);r.pending_buf.set(o.subarray(r.gzindex,r.gzindex+n),r.pending),r.pending+=n,r.gzhead.hcrc&&r.pending>e&&(t.adler=l(t.adler,r.pending_buf,r.pending-e,e)),r.gzindex=0}r.status=73}if(73===r.status){if(r.gzhead.name){let e,n=r.pending;do{if(r.pending===r.pending_buf_size){if(r.gzhead.hcrc&&r.pending>n&&(t.adler=l(t.adler,r.pending_buf,r.pending-n,n)),N(t),0!==r.pending)return r.last_flush=-1,m;n=0}e=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,j(r,e)}while(0!==e);r.gzhead.hcrc&&r.pending>n&&(t.adler=l(t.adler,r.pending_buf,r.pending-n,n)),r.gzindex=0}r.status=91}if(91===r.status){if(r.gzhead.comment){let e,n=r.pending;do{if(r.pending===r.pending_buf_size){if(r.gzhead.hcrc&&r.pending>n&&(t.adler=l(t.adler,r.pending_buf,r.pending-n,n)),N(t),0!==r.pending)return r.last_flush=-1,m;n=0}e=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,j(r,e)}while(0!==e);r.gzhead.hcrc&&r.pending>n&&(t.adler=l(t.adler,r.pending_buf,r.pending-n,n))}r.status=103}if(103===r.status){if(r.gzhead.hcrc){if(r.pending+2>r.pending_buf_size&&(N(t),0!==r.pending))return r.last_flush=-1,m;j(r,255&t.adler),j(r,t.adler>>8&255),t.adler=0}if(r.status=F,N(t),0!==r.pending)return r.last_flush=-1,m}if(0!==t.avail_in||0!==r.lookahead||e!==f&&r.status!==C){let n=0===r.level?X(r,e):r.strategy===E?((t,e)=>{let r;for(;;){if(0===t.lookahead&&(K(t),0===t.lookahead)){if(e===f)return 1;break}if(t.match_length=0,r=a(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(I(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===p?(I(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(I(t,!1),0===t.strm.avail_out)?1:2})(r,e):r.strategy===x?((t,e)=>{let r,n,o,i;const s=t.window;for(;;){if(t.lookahead<=D){if(K(t),t.lookahead<=D&&e===f)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(o=t.strstart-1,n=s[o],n===s[++o]&&n===s[++o]&&n===s[++o])){i=t.strstart+D;do{}while(n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&o<i);t.match_length=D-(i-o),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(r=a(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=a(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(I(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===p?(I(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(I(t,!1),0===t.strm.avail_out)?1:2})(r,e):$[r.level].func(r,e);if(3!==n&&4!==n||(r.status=C),1===n||3===n)return 0===t.avail_out&&(r.last_flush=-1),m;if(2===n&&(e===h?s(r):e!==_&&(o(r,0,0,!1),e===d&&(H(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),N(t),0===t.avail_out))return r.last_flush=-1,m}return e!==p?m:r.wrap<=0?g:(2===r.wrap?(j(r,255&t.adler),j(r,t.adler>>8&255),j(r,t.adler>>16&255),j(r,t.adler>>24&255),j(r,255&t.total_in),j(r,t.total_in>>8&255),j(r,t.total_in>>16&255),j(r,t.total_in>>24&255)):(L(r,t.adler>>>16),L(r,65535&t.adler)),N(t),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?m:g)},t.exports.deflateEnd=t=>{if(J(t))return v;const e=t.state.status;return t.state=null,e===F?M(t,w):m},t.exports.deflateSetDictionary=(t,e)=>{let r=e.length;if(J(t))return v;const n=t.state,o=n.wrap;if(2===o||1===o&&n.status!==T||n.lookahead)return v;if(1===o&&(t.adler=c(t.adler,e,r,0)),n.wrap=0,r>=n.w_size){0===o&&(H(n.head),n.strstart=0,n.block_start=0,n.insert=0);let t=new Uint8Array(n.w_size);t.set(e.subarray(r-n.w_size,r),0),e=t,r=n.w_size}const i=t.avail_in,a=t.next_in,s=t.input;for(t.avail_in=r,t.next_in=0,t.input=e,K(n);n.lookahead>=3;){let t=n.strstart,e=n.lookahead-2;do{n.ins_h=U(n,n.ins_h,n.window[t+3-1]),n.prev[t&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=t,t++}while(--e);n.strstart=t,n.lookahead=2,K(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,t.next_in=a,t.input=s,t.avail_in=i,n.wrap=o,m},t.exports.deflateInfo="pako deflate (from Nodeca project)"},8938:t=>{"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},9116:t=>{"use strict";const e=16209;t.exports=function(t,r){let n,o,i,a,s,c,l,u,f,h,d,p,_,m,g,v,w,y,b,k,E,x,A,S;const B=t.state;n=t.next_in,A=t.input,o=n+(t.avail_in-5),i=t.next_out,S=t.output,a=i-(r-t.avail_out),s=i+(t.avail_out-257),c=B.dmax,l=B.wsize,u=B.whave,f=B.wnext,h=B.window,d=B.hold,p=B.bits,_=B.lencode,m=B.distcode,g=(1<<B.lenbits)-1,v=(1<<B.distbits)-1;t:do{p<15&&(d+=A[n++]<<p,p+=8,d+=A[n++]<<p,p+=8),w=_[d&g];e:for(;;){if(y=w>>>24,d>>>=y,p-=y,y=w>>>16&255,0===y)S[i++]=65535&w;else{if(!(16&y)){if(0==(64&y)){w=_[(65535&w)+(d&(1<<y)-1)];continue e}if(32&y){B.mode=16191;break t}t.msg="invalid literal/length code",B.mode=e;break t}b=65535&w,y&=15,y&&(p<y&&(d+=A[n++]<<p,p+=8),b+=d&(1<<y)-1,d>>>=y,p-=y),p<15&&(d+=A[n++]<<p,p+=8,d+=A[n++]<<p,p+=8),w=m[d&v];r:for(;;){if(y=w>>>24,d>>>=y,p-=y,y=w>>>16&255,!(16&y)){if(0==(64&y)){w=m[(65535&w)+(d&(1<<y)-1)];continue r}t.msg="invalid distance code",B.mode=e;break t}if(k=65535&w,y&=15,p<y&&(d+=A[n++]<<p,p+=8,p<y&&(d+=A[n++]<<p,p+=8)),k+=d&(1<<y)-1,k>c){t.msg="invalid distance too far back",B.mode=e;break t}if(d>>>=y,p-=y,y=i-a,k>y){if(y=k-y,y>u&&B.sane){t.msg="invalid distance too far back",B.mode=e;break t}if(E=0,x=h,0===f){if(E+=l-y,y<b){b-=y;do{S[i++]=h[E++]}while(--y);E=i-k,x=S}}else if(f<y){if(E+=l+f-y,y-=f,y<b){b-=y;do{S[i++]=h[E++]}while(--y);if(E=0,f<b){y=f,b-=y;do{S[i++]=h[E++]}while(--y);E=i-k,x=S}}}else if(E+=f-y,y<b){b-=y;do{S[i++]=h[E++]}while(--y);E=i-k,x=S}for(;b>2;)S[i++]=x[E++],S[i++]=x[E++],S[i++]=x[E++],b-=3;b&&(S[i++]=x[E++],b>1&&(S[i++]=x[E++]))}else{E=i-k;do{S[i++]=S[E++],S[i++]=S[E++],S[i++]=S[E++],b-=3}while(b>2);b&&(S[i++]=S[E++],b>1&&(S[i++]=S[E++]))}break}}break}}while(n<o&&i<s);b=p>>3,n-=b,p-=b<<3,d&=(1<<p)-1,t.next_in=n,t.next_out=i,t.avail_in=n<o?o-n+5:5-(n-o),t.avail_out=i<s?s-i+257:257-(i-s),B.hold=d,B.bits=p}},7709:(t,e,r)=>{"use strict";const n=r(6533),o=r(3477),i=r(9116),a=r(5766),{Z_FINISH:s,Z_BLOCK:c,Z_TREES:l,Z_OK:u,Z_STREAM_END:f,Z_NEED_DICT:h,Z_STREAM_ERROR:d,Z_DATA_ERROR:p,Z_MEM_ERROR:_,Z_BUF_ERROR:m,Z_DEFLATED:g}=r(6735),v=16180,w=16190,y=16191,b=16192,k=16194,E=16199,x=16200,A=16206,S=16209,B=16210,z=t=>(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24);function D(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const R=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||e.mode<v||e.mode>16211?1:0},T=t=>{if(R(t))return d;const e=t.state;return t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=v,e.last=0,e.havedict=0,e.flags=-1,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new Int32Array(852),e.distcode=e.distdyn=new Int32Array(592),e.sane=1,e.back=-1,u},F=t=>{if(R(t))return d;const e=t.state;return e.wsize=0,e.whave=0,e.wnext=0,T(t)},C=(t,e)=>{let r;if(R(t))return d;const n=t.state;return e<0?(r=0,e=-e):(r=5+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?d:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=r,n.wbits=e,F(t))},M=(t,e)=>{if(!t)return d;const r=new D;t.state=r,r.strm=t,r.window=null,r.mode=v;const n=C(t,e);return n!==u&&(t.state=null),n};let P,H,O=!0;const U=t=>{if(O){P=new Int32Array(512),H=new Int32Array(32);let e=0;for(;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(a(1,t.lens,0,288,P,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;a(2,t.lens,0,32,H,0,t.work,{bits:5}),O=!1}t.lencode=P,t.lenbits=9,t.distcode=H,t.distbits=5},N=(t,e,r,n)=>{let o;const i=t.state;return null===i.window&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new Uint8Array(i.wsize)),n>=i.wsize?(i.window.set(e.subarray(r-i.wsize,r),0),i.wnext=0,i.whave=i.wsize):(o=i.wsize-i.wnext,o>n&&(o=n),i.window.set(e.subarray(r-n,r-n+o),i.wnext),(n-=o)?(i.window.set(e.subarray(r-n,r),0),i.wnext=n,i.whave=i.wsize):(i.wnext+=o,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=o))),0};t.exports.inflateReset=F,t.exports.inflateReset2=C,t.exports.inflateResetKeep=T,t.exports.inflateInit=t=>M(t,15),t.exports.inflateInit2=M,t.exports.inflate=(t,e)=>{let r,D,T,F,C,M,P,H,O,I,j,L,W,Z,K,X,q,G,V,$,Y,J,Q=0;const tt=new Uint8Array(4);let et,rt;const nt=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(R(t)||!t.output||!t.input&&0!==t.avail_in)return d;r=t.state,r.mode===y&&(r.mode=b),C=t.next_out,T=t.output,P=t.avail_out,F=t.next_in,D=t.input,M=t.avail_in,H=r.hold,O=r.bits,I=M,j=P,J=u;t:for(;;)switch(r.mode){case v:if(0===r.wrap){r.mode=b;break}for(;O<16;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(2&r.wrap&&35615===H){0===r.wbits&&(r.wbits=15),r.check=0,tt[0]=255&H,tt[1]=H>>>8&255,r.check=o(r.check,tt,2,0),H=0,O=0,r.mode=16181;break}if(r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&H)<<8)+(H>>8))%31){t.msg="incorrect header check",r.mode=S;break}if((15&H)!==g){t.msg="unknown compression method",r.mode=S;break}if(H>>>=4,O-=4,Y=8+(15&H),0===r.wbits&&(r.wbits=Y),Y>15||Y>r.wbits){t.msg="invalid window size",r.mode=S;break}r.dmax=1<<r.wbits,r.flags=0,t.adler=r.check=1,r.mode=512&H?16189:y,H=0,O=0;break;case 16181:for(;O<16;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(r.flags=H,(255&r.flags)!==g){t.msg="unknown compression method",r.mode=S;break}if(57344&r.flags){t.msg="unknown header flags set",r.mode=S;break}r.head&&(r.head.text=H>>8&1),512&r.flags&&4&r.wrap&&(tt[0]=255&H,tt[1]=H>>>8&255,r.check=o(r.check,tt,2,0)),H=0,O=0,r.mode=16182;case 16182:for(;O<32;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.head&&(r.head.time=H),512&r.flags&&4&r.wrap&&(tt[0]=255&H,tt[1]=H>>>8&255,tt[2]=H>>>16&255,tt[3]=H>>>24&255,r.check=o(r.check,tt,4,0)),H=0,O=0,r.mode=16183;case 16183:for(;O<16;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.head&&(r.head.xflags=255&H,r.head.os=H>>8),512&r.flags&&4&r.wrap&&(tt[0]=255&H,tt[1]=H>>>8&255,r.check=o(r.check,tt,2,0)),H=0,O=0,r.mode=16184;case 16184:if(1024&r.flags){for(;O<16;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.length=H,r.head&&(r.head.extra_len=H),512&r.flags&&4&r.wrap&&(tt[0]=255&H,tt[1]=H>>>8&255,r.check=o(r.check,tt,2,0)),H=0,O=0}else r.head&&(r.head.extra=null);r.mode=16185;case 16185:if(1024&r.flags&&(L=r.length,L>M&&(L=M),L&&(r.head&&(Y=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Uint8Array(r.head.extra_len)),r.head.extra.set(D.subarray(F,F+L),Y)),512&r.flags&&4&r.wrap&&(r.check=o(r.check,D,L,F)),M-=L,F+=L,r.length-=L),r.length))break t;r.length=0,r.mode=16186;case 16186:if(2048&r.flags){if(0===M)break t;L=0;do{Y=D[F+L++],r.head&&Y&&r.length<65536&&(r.head.name+=String.fromCharCode(Y))}while(Y&&L<M);if(512&r.flags&&4&r.wrap&&(r.check=o(r.check,D,L,F)),M-=L,F+=L,Y)break t}else r.head&&(r.head.name=null);r.length=0,r.mode=16187;case 16187:if(4096&r.flags){if(0===M)break t;L=0;do{Y=D[F+L++],r.head&&Y&&r.length<65536&&(r.head.comment+=String.fromCharCode(Y))}while(Y&&L<M);if(512&r.flags&&4&r.wrap&&(r.check=o(r.check,D,L,F)),M-=L,F+=L,Y)break t}else r.head&&(r.head.comment=null);r.mode=16188;case 16188:if(512&r.flags){for(;O<16;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(4&r.wrap&&H!==(65535&r.check)){t.msg="header crc mismatch",r.mode=S;break}H=0,O=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=y;break;case 16189:for(;O<32;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}t.adler=r.check=z(H),H=0,O=0,r.mode=w;case w:if(0===r.havedict)return t.next_out=C,t.avail_out=P,t.next_in=F,t.avail_in=M,r.hold=H,r.bits=O,h;t.adler=r.check=1,r.mode=y;case y:if(e===c||e===l)break t;case b:if(r.last){H>>>=7&O,O-=7&O,r.mode=A;break}for(;O<3;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}switch(r.last=1&H,H>>>=1,O-=1,3&H){case 0:r.mode=16193;break;case 1:if(U(r),r.mode=E,e===l){H>>>=2,O-=2;break t}break;case 2:r.mode=16196;break;case 3:t.msg="invalid block type",r.mode=S}H>>>=2,O-=2;break;case 16193:for(H>>>=7&O,O-=7&O;O<32;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if((65535&H)!=(H>>>16^65535)){t.msg="invalid stored block lengths",r.mode=S;break}if(r.length=65535&H,H=0,O=0,r.mode=k,e===l)break t;case k:r.mode=16195;case 16195:if(L=r.length,L){if(L>M&&(L=M),L>P&&(L=P),0===L)break t;T.set(D.subarray(F,F+L),C),M-=L,F+=L,P-=L,C+=L,r.length-=L;break}r.mode=y;break;case 16196:for(;O<14;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(r.nlen=257+(31&H),H>>>=5,O-=5,r.ndist=1+(31&H),H>>>=5,O-=5,r.ncode=4+(15&H),H>>>=4,O-=4,r.nlen>286||r.ndist>30){t.msg="too many length or distance symbols",r.mode=S;break}r.have=0,r.mode=16197;case 16197:for(;r.have<r.ncode;){for(;O<3;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.lens[nt[r.have++]]=7&H,H>>>=3,O-=3}for(;r.have<19;)r.lens[nt[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,et={bits:r.lenbits},J=a(0,r.lens,0,19,r.lencode,0,r.work,et),r.lenbits=et.bits,J){t.msg="invalid code lengths set",r.mode=S;break}r.have=0,r.mode=16198;case 16198:for(;r.have<r.nlen+r.ndist;){for(;Q=r.lencode[H&(1<<r.lenbits)-1],K=Q>>>24,X=Q>>>16&255,q=65535&Q,!(K<=O);){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(q<16)H>>>=K,O-=K,r.lens[r.have++]=q;else{if(16===q){for(rt=K+2;O<rt;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(H>>>=K,O-=K,0===r.have){t.msg="invalid bit length repeat",r.mode=S;break}Y=r.lens[r.have-1],L=3+(3&H),H>>>=2,O-=2}else if(17===q){for(rt=K+3;O<rt;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}H>>>=K,O-=K,Y=0,L=3+(7&H),H>>>=3,O-=3}else{for(rt=K+7;O<rt;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}H>>>=K,O-=K,Y=0,L=11+(127&H),H>>>=7,O-=7}if(r.have+L>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=S;break}for(;L--;)r.lens[r.have++]=Y}}if(r.mode===S)break;if(0===r.lens[256]){t.msg="invalid code -- missing end-of-block",r.mode=S;break}if(r.lenbits=9,et={bits:r.lenbits},J=a(1,r.lens,0,r.nlen,r.lencode,0,r.work,et),r.lenbits=et.bits,J){t.msg="invalid literal/lengths set",r.mode=S;break}if(r.distbits=6,r.distcode=r.distdyn,et={bits:r.distbits},J=a(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,et),r.distbits=et.bits,J){t.msg="invalid distances set",r.mode=S;break}if(r.mode=E,e===l)break t;case E:r.mode=x;case x:if(M>=6&&P>=258){t.next_out=C,t.avail_out=P,t.next_in=F,t.avail_in=M,r.hold=H,r.bits=O,i(t,j),C=t.next_out,T=t.output,P=t.avail_out,F=t.next_in,D=t.input,M=t.avail_in,H=r.hold,O=r.bits,r.mode===y&&(r.back=-1);break}for(r.back=0;Q=r.lencode[H&(1<<r.lenbits)-1],K=Q>>>24,X=Q>>>16&255,q=65535&Q,!(K<=O);){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(X&&0==(240&X)){for(G=K,V=X,$=q;Q=r.lencode[$+((H&(1<<G+V)-1)>>G)],K=Q>>>24,X=Q>>>16&255,q=65535&Q,!(G+K<=O);){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}H>>>=G,O-=G,r.back+=G}if(H>>>=K,O-=K,r.back+=K,r.length=q,0===X){r.mode=16205;break}if(32&X){r.back=-1,r.mode=y;break}if(64&X){t.msg="invalid literal/length code",r.mode=S;break}r.extra=15&X,r.mode=16201;case 16201:if(r.extra){for(rt=r.extra;O<rt;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.length+=H&(1<<r.extra)-1,H>>>=r.extra,O-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=16202;case 16202:for(;Q=r.distcode[H&(1<<r.distbits)-1],K=Q>>>24,X=Q>>>16&255,q=65535&Q,!(K<=O);){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(0==(240&X)){for(G=K,V=X,$=q;Q=r.distcode[$+((H&(1<<G+V)-1)>>G)],K=Q>>>24,X=Q>>>16&255,q=65535&Q,!(G+K<=O);){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}H>>>=G,O-=G,r.back+=G}if(H>>>=K,O-=K,r.back+=K,64&X){t.msg="invalid distance code",r.mode=S;break}r.offset=q,r.extra=15&X,r.mode=16203;case 16203:if(r.extra){for(rt=r.extra;O<rt;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}r.offset+=H&(1<<r.extra)-1,H>>>=r.extra,O-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=S;break}r.mode=16204;case 16204:if(0===P)break t;if(L=j-P,r.offset>L){if(L=r.offset-L,L>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=S;break}L>r.wnext?(L-=r.wnext,W=r.wsize-L):W=r.wnext-L,L>r.length&&(L=r.length),Z=r.window}else Z=T,W=C-r.offset,L=r.length;L>P&&(L=P),P-=L,r.length-=L;do{T[C++]=Z[W++]}while(--L);0===r.length&&(r.mode=x);break;case 16205:if(0===P)break t;T[C++]=r.length,P--,r.mode=x;break;case A:if(r.wrap){for(;O<32;){if(0===M)break t;M--,H|=D[F++]<<O,O+=8}if(j-=P,t.total_out+=j,r.total+=j,4&r.wrap&&j&&(t.adler=r.check=r.flags?o(r.check,T,j,C-j):n(r.check,T,j,C-j)),j=P,4&r.wrap&&(r.flags?H:z(H))!==r.check){t.msg="incorrect data check",r.mode=S;break}H=0,O=0}r.mode=16207;case 16207:if(r.wrap&&r.flags){for(;O<32;){if(0===M)break t;M--,H+=D[F++]<<O,O+=8}if(4&r.wrap&&H!==(4294967295&r.total)){t.msg="incorrect length check",r.mode=S;break}H=0,O=0}r.mode=16208;case 16208:J=f;break t;case S:J=p;break t;case B:return _;default:return d}return t.next_out=C,t.avail_out=P,t.next_in=F,t.avail_in=M,r.hold=H,r.bits=O,(r.wsize||j!==t.avail_out&&r.mode<S&&(r.mode<A||e!==s))&&N(t,t.output,t.next_out,j-t.avail_out)?(r.mode=B,_):(I-=t.avail_in,j-=t.avail_out,t.total_in+=I,t.total_out+=j,r.total+=j,4&r.wrap&&j&&(t.adler=r.check=r.flags?o(r.check,T,j,t.next_out-j):n(r.check,T,j,t.next_out-j)),t.data_type=r.bits+(r.last?64:0)+(r.mode===y?128:0)+(r.mode===E||r.mode===k?256:0),(0===I&&0===j||e===s)&&J===u&&(J=m),J)},t.exports.inflateEnd=t=>{if(R(t))return d;let e=t.state;return e.window&&(e.window=null),t.state=null,u},t.exports.inflateGetHeader=(t,e)=>{if(R(t))return d;const r=t.state;return 0==(2&r.wrap)?d:(r.head=e,e.done=!1,u)},t.exports.inflateSetDictionary=(t,e)=>{const r=e.length;let o,i,a;return R(t)?d:(o=t.state,0!==o.wrap&&o.mode!==w?d:o.mode===w&&(i=1,i=n(i,e,r,0),i!==o.check)?p:(a=N(t,e,r,r),a?(o.mode=B,_):(o.havedict=1,u)))},t.exports.inflateInfo="pako inflate (from Nodeca project)"},5766:t=>{"use strict";const e=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),r=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),n=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),o=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);t.exports=(t,i,a,s,c,l,u,f)=>{const h=f.bits;let d,p,_,m,g,v,w=0,y=0,b=0,k=0,E=0,x=0,A=0,S=0,B=0,z=0,D=null;const R=new Uint16Array(16),T=new Uint16Array(16);let F,C,M,P=null;for(w=0;w<=15;w++)R[w]=0;for(y=0;y<s;y++)R[i[a+y]]++;for(E=h,k=15;k>=1&&0===R[k];k--);if(E>k&&(E=k),0===k)return c[l++]=20971520,c[l++]=20971520,f.bits=1,0;for(b=1;b<k&&0===R[b];b++);for(E<b&&(E=b),S=1,w=1;w<=15;w++)if(S<<=1,S-=R[w],S<0)return-1;if(S>0&&(0===t||1!==k))return-1;for(T[1]=0,w=1;w<15;w++)T[w+1]=T[w]+R[w];for(y=0;y<s;y++)0!==i[a+y]&&(u[T[i[a+y]]++]=y);if(0===t?(D=P=u,v=20):1===t?(D=e,P=r,v=257):(D=n,P=o,v=0),z=0,y=0,w=b,g=l,x=E,A=0,_=-1,B=1<<E,m=B-1,1===t&&B>852||2===t&&B>592)return 1;for(;;){F=w-A,u[y]+1<v?(C=0,M=u[y]):u[y]>=v?(C=P[u[y]-v],M=D[u[y]-v]):(C=96,M=0),d=1<<w-A,p=1<<x,b=p;do{p-=d,c[g+(z>>A)+p]=F<<24|C<<16|M|0}while(0!==p);for(d=1<<w-1;z&d;)d>>=1;if(0!==d?(z&=d-1,z+=d):z=0,y++,0==--R[w]){if(w===k)break;w=i[a+u[y]]}if(w>E&&(z&m)!==_){for(0===A&&(A=E),g+=b,x=w-A,S=1<<x;x+A<k&&(S-=R[x+A],!(S<=0));)x++,S<<=1;if(B+=1<<x,1===t&&B>852||2===t&&B>592)return 1;_=z&m,c[_]=E<<24|x<<16|g-l|0}}return 0!==z&&(c[g+z]=w-A<<24|64<<16|0),f.bits=E,0}},699:t=>{"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},3054:t=>{"use strict";function e(t){let e=t.length;for(;--e>=0;)t[e]=0}const r=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),n=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),o=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),i=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),a=new Array(576);e(a);const s=new Array(60);e(s);const c=new Array(512);e(c);const l=new Array(256);e(l);const u=new Array(29);e(u);const f=new Array(30);function h(t,e,r,n,o){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=o,this.has_stree=t&&t.length}let d,p,_;function m(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}e(f);const g=t=>t<256?c[t]:c[256+(t>>>7)],v=(t,e)=>{t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},w=(t,e,r)=>{t.bi_valid>16-r?(t.bi_buf|=e<<t.bi_valid&65535,v(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=r-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)},y=(t,e,r)=>{w(t,r[2*e],r[2*e+1])},b=(t,e)=>{let r=0;do{r|=1&t,t>>>=1,r<<=1}while(--e>0);return r>>>1},k=(t,e,r)=>{const n=new Array(16);let o,i,a=0;for(o=1;o<=15;o++)a=a+r[o-1]<<1,n[o]=a;for(i=0;i<=e;i++){let e=t[2*i+1];0!==e&&(t[2*i]=b(n[e]++,e))}},E=t=>{let e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.sym_next=t.matches=0},x=t=>{t.bi_valid>8?v(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},A=(t,e,r,n)=>{const o=2*e,i=2*r;return t[o]<t[i]||t[o]===t[i]&&n[e]<=n[r]},S=(t,e,r)=>{const n=t.heap[r];let o=r<<1;for(;o<=t.heap_len&&(o<t.heap_len&&A(e,t.heap[o+1],t.heap[o],t.depth)&&o++,!A(e,n,t.heap[o],t.depth));)t.heap[r]=t.heap[o],r=o,o<<=1;t.heap[r]=n},B=(t,e,o)=>{let i,a,s,c,h=0;if(0!==t.sym_next)do{i=255&t.pending_buf[t.sym_buf+h++],i+=(255&t.pending_buf[t.sym_buf+h++])<<8,a=t.pending_buf[t.sym_buf+h++],0===i?y(t,a,e):(s=l[a],y(t,s+256+1,e),c=r[s],0!==c&&(a-=u[s],w(t,a,c)),i--,s=g(i),y(t,s,o),c=n[s],0!==c&&(i-=f[s],w(t,i,c)))}while(h<t.sym_next);y(t,256,e)},z=(t,e)=>{const r=e.dyn_tree,n=e.stat_desc.static_tree,o=e.stat_desc.has_stree,i=e.stat_desc.elems;let a,s,c,l=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<i;a++)0!==r[2*a]?(t.heap[++t.heap_len]=l=a,t.depth[a]=0):r[2*a+1]=0;for(;t.heap_len<2;)c=t.heap[++t.heap_len]=l<2?++l:0,r[2*c]=1,t.depth[c]=0,t.opt_len--,o&&(t.static_len-=n[2*c+1]);for(e.max_code=l,a=t.heap_len>>1;a>=1;a--)S(t,r,a);c=i;do{a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],S(t,r,1),s=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=s,r[2*c]=r[2*a]+r[2*s],t.depth[c]=(t.depth[a]>=t.depth[s]?t.depth[a]:t.depth[s])+1,r[2*a+1]=r[2*s+1]=c,t.heap[1]=c++,S(t,r,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],((t,e)=>{const r=e.dyn_tree,n=e.max_code,o=e.stat_desc.static_tree,i=e.stat_desc.has_stree,a=e.stat_desc.extra_bits,s=e.stat_desc.extra_base,c=e.stat_desc.max_length;let l,u,f,h,d,p,_=0;for(h=0;h<=15;h++)t.bl_count[h]=0;for(r[2*t.heap[t.heap_max]+1]=0,l=t.heap_max+1;l<573;l++)u=t.heap[l],h=r[2*r[2*u+1]+1]+1,h>c&&(h=c,_++),r[2*u+1]=h,u>n||(t.bl_count[h]++,d=0,u>=s&&(d=a[u-s]),p=r[2*u],t.opt_len+=p*(h+d),i&&(t.static_len+=p*(o[2*u+1]+d)));if(0!==_){do{for(h=c-1;0===t.bl_count[h];)h--;t.bl_count[h]--,t.bl_count[h+1]+=2,t.bl_count[c]--,_-=2}while(_>0);for(h=c;0!==h;h--)for(u=t.bl_count[h];0!==u;)f=t.heap[--l],f>n||(r[2*f+1]!==h&&(t.opt_len+=(h-r[2*f+1])*r[2*f],r[2*f+1]=h),u--)}})(t,e),k(r,l,t.bl_count)},D=(t,e,r)=>{let n,o,i=-1,a=e[1],s=0,c=7,l=4;for(0===a&&(c=138,l=3),e[2*(r+1)+1]=65535,n=0;n<=r;n++)o=a,a=e[2*(n+1)+1],++s<c&&o===a||(s<l?t.bl_tree[2*o]+=s:0!==o?(o!==i&&t.bl_tree[2*o]++,t.bl_tree[32]++):s<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=0,i=o,0===a?(c=138,l=3):o===a?(c=6,l=3):(c=7,l=4))},R=(t,e,r)=>{let n,o,i=-1,a=e[1],s=0,c=7,l=4;for(0===a&&(c=138,l=3),n=0;n<=r;n++)if(o=a,a=e[2*(n+1)+1],!(++s<c&&o===a)){if(s<l)do{y(t,o,t.bl_tree)}while(0!=--s);else 0!==o?(o!==i&&(y(t,o,t.bl_tree),s--),y(t,16,t.bl_tree),w(t,s-3,2)):s<=10?(y(t,17,t.bl_tree),w(t,s-3,3)):(y(t,18,t.bl_tree),w(t,s-11,7));s=0,i=o,0===a?(c=138,l=3):o===a?(c=6,l=3):(c=7,l=4)}};let T=!1;const F=(t,e,r,n)=>{w(t,0+(n?1:0),3),x(t),v(t,r),v(t,~r),r&&t.pending_buf.set(t.window.subarray(e,e+r),t.pending),t.pending+=r};t.exports._tr_init=t=>{T||((()=>{let t,e,i,m,g;const v=new Array(16);for(i=0,m=0;m<28;m++)for(u[m]=i,t=0;t<1<<r[m];t++)l[i++]=m;for(l[i-1]=m,g=0,m=0;m<16;m++)for(f[m]=g,t=0;t<1<<n[m];t++)c[g++]=m;for(g>>=7;m<30;m++)for(f[m]=g<<7,t=0;t<1<<n[m]-7;t++)c[256+g++]=m;for(e=0;e<=15;e++)v[e]=0;for(t=0;t<=143;)a[2*t+1]=8,t++,v[8]++;for(;t<=255;)a[2*t+1]=9,t++,v[9]++;for(;t<=279;)a[2*t+1]=7,t++,v[7]++;for(;t<=287;)a[2*t+1]=8,t++,v[8]++;for(k(a,287,v),t=0;t<30;t++)s[2*t+1]=5,s[2*t]=b(t,5);d=new h(a,r,257,286,15),p=new h(s,n,0,30,15),_=new h(new Array(0),o,0,19,7)})(),T=!0),t.l_desc=new m(t.dyn_ltree,d),t.d_desc=new m(t.dyn_dtree,p),t.bl_desc=new m(t.bl_tree,_),t.bi_buf=0,t.bi_valid=0,E(t)},t.exports._tr_stored_block=F,t.exports._tr_flush_block=(t,e,r,n)=>{let o,c,l=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=(t=>{let e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0})(t)),z(t,t.l_desc),z(t,t.d_desc),l=(t=>{let e;for(D(t,t.dyn_ltree,t.l_desc.max_code),D(t,t.dyn_dtree,t.d_desc.max_code),z(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*i[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e})(t),o=t.opt_len+3+7>>>3,c=t.static_len+3+7>>>3,c<=o&&(o=c)):o=c=r+5,r+4<=o&&-1!==e?F(t,e,r,n):4===t.strategy||c===o?(w(t,2+(n?1:0),3),B(t,a,s)):(w(t,4+(n?1:0),3),((t,e,r,n)=>{let o;for(w(t,e-257,5),w(t,r-1,5),w(t,n-4,4),o=0;o<n;o++)w(t,t.bl_tree[2*i[o]+1],3);R(t,t.dyn_ltree,e-1),R(t,t.dyn_dtree,r-1)})(t,t.l_desc.max_code+1,t.d_desc.max_code+1,l+1),B(t,t.dyn_ltree,t.dyn_dtree)),E(t),n&&x(t)},t.exports._tr_tally=(t,e,r)=>(t.pending_buf[t.sym_buf+t.sym_next++]=e,t.pending_buf[t.sym_buf+t.sym_next++]=e>>8,t.pending_buf[t.sym_buf+t.sym_next++]=r,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(l[r]+256+1)]++,t.dyn_dtree[2*g(e)]++),t.sym_next===t.sym_end),t.exports._tr_align=t=>{w(t,2,3),y(t,256,a),(t=>{16===t.bi_valid?(v(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)})(t)}},3502:t=>{"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},3828:()=>{},427:()=>{},8625:()=>{}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{"use strict";r.r(n),r.d(n,{MGS:()=>k,default:()=>E});var t=r(8683),e=r.n(t);function o(t,r,n,o,i){return new Promise((a=>{e()().then((e=>{i&&e.init(0===t?i:null,1===t?i:null,2===t||3===t?i:null),e.decode(n,r,t,((t,e)=>{if(0===t){const t=new Uint8Array(e);o?o(t):a(t)}else o?o(!1):a(!1)}))}))}))}function i(t,r,n,o){return new Promise(((i,a)=>{e()().then((e=>{e.init(0===t?r:null,1===t?r:null,2===t||3===t?r:null),e.encode(function(t){let e="";for(let t=16;t>0;--t)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(62*Math.random())];return e}(),n,t,((t,e,r,n)=>{let s={symmetricKey:new Uint8Array(e),secData:new Uint8Array(r),symmetricKeySend:new Uint8Array(n),success:!1};0===t?(s={...s,success:!0},o?o(s):i(s)):(s={...s,errorCode:t},o?o(s):a(s))}))}))}))}var a=r(3561);function s(t,e,r,n){let o,i="";if("string"!=typeof t)throw new Error("sign error: type must be string");switch(i=t.toLowerCase(),i){case"sha256":o=(0,a.SHA256)(`${r}&${e}`).toString();break;case"hmacsha256":r&&(o=(0,a.HmacSHA256)(e,r).toString());break;case"md5":o=(0,a.MD5)(`${r}&${e}`).toString()}n(o||!1)}const c="function"==typeof btoa,l="function"==typeof Buffer,u=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder?new TextEncoder:void 0),f=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),h=((t=>{let e={};t.forEach(((t,r)=>e[t]=r))})(f),String.fromCharCode.bind(String)),d=("function"==typeof Uint8Array.from&&Uint8Array.from.bind(Uint8Array),t=>t.replace(/=/g,"").replace(/[+\/]/g,(t=>"+"==t?"-":"_"))),p=c?t=>btoa(t):l?t=>Buffer.from(t,"binary").toString("base64"):t=>{let e,r,n,o,i="";const a=t.length%3;for(let a=0;a<t.length;){if((r=t.charCodeAt(a++))>255||(n=t.charCodeAt(a++))>255||(o=t.charCodeAt(a++))>255)throw new TypeError("invalid character found");e=r<<16|n<<8|o,i+=f[e>>18&63]+f[e>>12&63]+f[e>>6&63]+f[63&e]}return a?i.slice(0,a-3)+"===".substring(a):i},_=l?t=>Buffer.from(t).toString("base64"):t=>{let e=[];for(let r=0,n=t.length;r<n;r+=4096)e.push(h.apply(null,t.subarray(r,r+4096)));return p(e.join(""))},m=t=>{if(t.length<2)return(e=t.charCodeAt(0))<128?t:e<2048?h(192|e>>>6)+h(128|63&e):h(224|e>>>12&15)+h(128|e>>>6&63)+h(128|63&e);var e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return h(240|e>>>18&7)+h(128|e>>>12&63)+h(128|e>>>6&63)+h(128|63&e)},g=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,v=l?t=>Buffer.from(t,"utf8").toString("base64"):u?t=>_(u.encode(t)):t=>p(t.replace(g,m)),w=(t,e=!1)=>e?d(v(t)):v(t),y=r(5248);function b(t){const e={get:"GET",post:"POST"},{baseURL:r,method:n,operationType:o,appid:i,workspaceid:a,extraHttpConfig:s={},signHeaders:c={},extraHeaderInfos:l={},data:u,encryptType:f}=t,h={...s};return"number"==typeof f&&(h.responseType="arraybuffer"),new Promise(((t,s)=>{wx.request({url:r,method:e[n],data:u,dataType:"ArrayBuffer",...h,header:{version:"2","operation-type":o,[`X-CORS-${i}-${a}`]:"1",appid:i,workspaceid:a,...c,...l},success:e=>{200===e.statusCode?t(e):s(e)},fail:t=>{s(t.response)}})}))}const k=new class{call(t,e,r){return new Promise(((n,a)=>{const{data:c}=e;switch(t){case"sign":{const{signType:t,operationType:o,timeStamp:i,noRequestBody:a,secretKey:l}=e;let u="";u=a?"object"==typeof c&&null!==c?JSON.stringify(c):c:JSON.stringify([{_requestBody:c}]),u=`Operation-Type=${o}&Request-Data${w(u)}&Ts=${i}`,s(t,u,l,(t=>{t&&(r?r(t):n(t))}));break}case"encrypt":{const{encryptType:t,publicKey:o}=e;i(t,o,c,(t=>{const{success:e}=t;r?r(t):n(t)}));break}case"decrypt":{const{decryptType:t,symmetricKey:i,publicKey:a}=e;o(t,c,i,(t=>{r?r(t):n(t)}),a);break}case"rpc":(function(t){return new Promise(((e,r)=>{const{data:n,noRequestBody:a,signType:c,operationType:l,encryptType:u,publicKey:f}=t,h={};let d="";if(d=a?n:[{_requestBody:n}],c){const{secretKey:e}=t,r=JSON.stringify(d),n=(new Date).getTime(),o=`Operation-Type=${l}&Request-Data=${w(r)}&Ts=${n}`;s(c,o,e,(t=>{t&&(h.sign=t,h.SignType=c,h.Ts=n)}))}"number"==typeof u&&f?i(u,f,y.gzip(JSON.stringify(d)),(n=>{const{success:i,secData:a,symmetricKey:s,symmetricKeySend:c,errorCode:l}=n;if(i){const n=function(t,e,r){const n=[];return n.push(t+1),n.push((16711680&e.length)>>16),n.push((65280&e.length)>>8),n.push(255&e.length),e.forEach((t=>{n.push(t)})),n.push(t+1<3?1:2),n.push((16711680&r.length)>>16),n.push((65280&r.length)>>8),n.push(255&r.length),r.forEach((t=>{n.push(t)})),new Uint8Array(n)}(u,c,a);b({...t,signHeaders:h,data:n.buffer}).then((async t=>{const{header:{"Result-Status":n,Tips:i}}=t;if("1000"!==n)return r({data:void 0,reason:decodeURIComponent(i),status:n});let a=t.data;const c=new Uint8Array(a);if(a=await o(u,c,s),!a)return r({data:void 0,reason:"decrypt fail"});a=y.ungzip(a),a&&(a=function(t){var e,r,n,o,i,a;for(e="",n=t.length,r=0;r<n;)switch((o=t[r++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:e+=String.fromCharCode(o);break;case 12:case 13:i=t[r++],e+=String.fromCharCode((31&o)<<6|63&i);break;case 14:i=t[r++],a=t[r++],e+=String.fromCharCode((15&o)<<12|(63&i)<<6|(63&a)<<0)}return e}(a)),e({...t,data:a,status:200})})).catch((t=>{r("string"==typeof t?{data:void 0,reason:t}:t)}))}else r({data:void 0,reason:`encrypt fail ${l}`})})):b({...t,signHeaders:h,data:d}).then((async t=>{const r=t.data;e({...t,data:r,status:200})})).catch((t=>{r(t)}))}))})(e).then((t=>{r?r(t):n(t)})).catch((t=>{r?r(t):a(t)}));break;default:throw new Error("Not a supported method")}}))}},E={MGS:k}})(),n})()));