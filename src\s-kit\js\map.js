/**
 * 将腾讯地图的经纬度转为百度的经纬度
 * @param {*} lng 腾讯地图对应的坐标 经度
 * @param {*} lat 腾讯地图对应的坐标 纬度
 * @returns { lng, lat } 对应的百度的坐标经纬度
 */
export const handleTxMapTransBMap = (lng, lat) => {
    let x_pi = (Math.PI * 3000.0) / 180.0; //Math.PI ~ 3.14159265358979324
    let x = lng;
    let y = lat;
    let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
    let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
    let lngs = z * Math.cos(theta) + 0.0065;
    let lats = z * Math.sin(theta) + 0.006;
    return {
        longitude: lngs,
        latitude: lats,
    };
};

/**
 * 坐标系转换 百度bd09转火星国内Gcj02
 * @param lon
 * @param lat
 * @returns {{latitude: number, longitude: number}}
 */
export const bd09_To_Gcj02 = (lon, lat) => {
    const x_pi = (Math.PI * 3000.0) / 180.0;
    let x = lon - 0.0065,
        y = lat - 0.006;
    let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
    let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
    const longitude = z * Math.cos(theta);
    const latitude = z * Math.sin(theta);
    return { longitude, latitude };
};

/**
 * 坐标系转换 火星国内Gcj02转wgs84
 * @param lon
 * @param lat
 * @returns {{latitude: number, longitude: number}}
 */
export const Gcj02_To_wgs84 = (lon, lat) => {
    var dlat = CoordinateUtil.transformlat(lon - 105.0, lat - 35.0);
    var dlng = CoordinateUtil.transformlng(lon - 105.0, lat - 35.0);
    var radlat = (lat / 180.0) * CoordinateUtil.pi;
    var magic = Math.sin(radlat);
    magic = 1 - CoordinateUtil.ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((CoordinateUtil.a * (1 - CoordinateUtil.ee)) / (magic * sqrtmagic)) * CoordinateUtil.pi);
    dlng = (dlng * 180.0) / ((CoordinateUtil.a / sqrtmagic) * Math.cos(radlat) * CoordinateUtil.pi);
    var mglat = lat + dlat;
    var mglng = lon + dlng;
    var longitude = lon * 2 - mglng;
    var latitude = lat * 2 - mglat;
    console.log('转换中3', longitude, latitude);
    return { longitude, latitude };
};

/**
 * 坐标系转换 百度bd09转wgs84
 * @param lon
 * @param lat
 * @returns {{latitude: number, longitude: number}}
 */
export const bd09_To_wgs84 = (lon, lat) => {
    console.log('转换中1');
    let obj = bd09_To_Gcj02(lon, lat);
    console.log('转换中2', obj.longitude, obj.latitude);
    let { longitude, latitude } = Gcj02_To_wgs84(obj.longitude, obj.latitude);
    console.log('转换中4', longitude, latitude);
    return { longitude, latitude };
};

function outOfChina(lng, lat) {
    return (lng < 72.004 || lng > 137.8347) || (lat < 0.8293 || lat > 55.8271);
}

export const gcj02ToBd09 = (lng, lat) => {
    const x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    if (outOfChina(lng, lat)) {
        return {
            lng: lng,
            lat: lat
        };
    }
    const x = lng;
    const y = lat;
    const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
    const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
    const bd_lng = z * Math.cos(theta) + 0.0065;
    const bd_lat = z * Math.sin(theta) + 0.006;
    return {
        lng: bd_lng,
        lat: bd_lat
    };
}


const CoordinateUtil = {
    x_pi: (3.14159265358979324 * 3000.0) / 180.0,
    //pai
    pi: 3.1415926535897932384626,
    //离心率
    ee: 0.00669342162296594323,
    //长半轴
    a: 6378245.0,
    //经度转换
    transformlat: function (lng, lat) {
        var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * CoordinateUtil.pi) + 20.0 * Math.sin(2.0 * lng * CoordinateUtil.pi)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lat * CoordinateUtil.pi) + 40.0 * Math.sin((lat / 3.0) * CoordinateUtil.pi)) * 2.0) / 3.0;
        ret += ((160.0 * Math.sin((lat / 12.0) * CoordinateUtil.pi) + 320 * Math.sin((lat * CoordinateUtil.pi) / 30.0)) * 2.0) / 3.0;
        return ret;
    },
    //纬度转换
    transformlng: function (lng, lat) {
        var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * CoordinateUtil.pi) + 20.0 * Math.sin(2.0 * lng * CoordinateUtil.pi)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lng * CoordinateUtil.pi) + 40.0 * Math.sin((lng / 3.0) * CoordinateUtil.pi)) * 2.0) / 3.0;
        ret += ((150.0 * Math.sin((lng / 12.0) * CoordinateUtil.pi) + 300.0 * Math.sin((lng / 30.0) * CoordinateUtil.pi)) * 2.0) / 3.0;
        return ret;
    },
};
