<template>
    <div class="add-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="选择发票抬头"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>

        <!-- <scroll-view
          class="scroll-view_H"
          :scroll-left="scrollLeft"
          scroll-x="true"
        >
          <div class="slide_type_list">
            <div
              class="slide_type_list_view"
              v-for="(item, index) in typeList"
              :key="index"
              :class="{ is_selected: active == index }"
              @click="changeType(item, index)"
            >
              <div>{{ item.name }}</div>
            </div>
          </div>
        </scroll-view>

        <div class="mask-view flex-list" v-if="active == 0">
          <template v-if="companyTitleList.length < 0">
            <u-empty text="暂无发票抬头信息" mode="list" :margin-top="80"></u-empty>
          </template>
          <template v-else>
            <div
              class="mask-list mask-flex"
              v-for="(item, index) in companyTitleList"
              :key="item.id"
              @click="selectCompanyTitle(index,item)"
            >
              <div class="mask-cell">
                <div class="label-txt font15 title-bold">
                  {{ item.invoicetitle }}
                  <span class="mask-default" v-if="item.id == defaultEnterpriseInvoice">默认</span>
                </div>
                <div class="mt8">税号：{{ item.taxcode }}</div>
                <div class="label-txt">常用邮箱：<EMAIL></div>
              </div>
              <div class="right-div" v-if="companyIconId == index">
                <img src="@/static/select-icon.png" alt="">
              </div>
            </div>
          </template>
        </div>
        <div class="mask-view flex-list" v-if="active == 1">
          <template v-if="personalTitleList.length < 0">
            <u-empty text="暂无发票抬头信息" mode="list" :margin-top="80"></u-empty>
          </template>
          <template v-else>
            <div
              class="mask-list mask-flex"
              v-for="(item, index) in personalTitleList"
              :key="item.id"
              @click="selectPersonaTitle(index,item)"
            >
              <div class="mask-cell">
                <div class="label-txt font15 title-bold" style="padding-bottom: 0">
                  {{ item.invoicetitle }}
                  <span class="mask-default" v-if="item.id == defaultPersonalInvoice">默认</span>
                </div>
                <div class="label-txt">常用邮箱：<EMAIL></div>
              </div>
              <div class="right-div" v-if="personalIconId == index">
                <img src="@/static/select-icon.png" alt="">
              </div>
            </div>
          </template>
    </div>-->
        <scroll-view class="title-list">
            <div class="item" v-for="(item, index) in list" :key="index" @click="choiceNowTitle(index, item)">
                <div class="title-name">
                    <img v-if="item.islogo == 2" src="../../images/personal.png" alt />
                    <img v-else src="../../images/company.png" alt />
                    <span>{{ item.invoicetitle }}</span>
                </div>
                <div class="is-choice">
                    <img v-if="index == choiceActive" src="@/static/images/type-checked.png" alt />
                    <img v-else src="@/static/images/type-unchecked.png" alt />
                </div>
            </div>
        </scroll-view>
        <div class="bottom-view" v-if="list.length < 3">
            <div class="bottom-btn" @click="clickAddIncoive">新增发票抬头</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
// import { getCompanyTitleListPost, getPersonalTitleListPost, delInvoiceTitlePost } from "@/api/my-center";
import { getInvoiceTitleList } from '@/api/my-center';

export default {
    name: 'choose-invoice-title',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            /*
      //nav选中index
      active: 0,
      companyIconId:-1,
      personalIconId:-1,
      //navList
      typeList: [
        {
          name: "企业抬头",
        },
        {
          name: "个人/非企业抬头",
        },
      ],
      companyTitleList: [], //企业抬头列表
      personalTitleList: [], //个人抬头列表
      defaultEnterpriseInvoice: "", // 默认抬头(企业)id
      defaultPersonalInvoice: "",  // 默认抬头(个人)id
      */

            list: [],
            choiceActive: -1,
            hasChoiceID: '',
        };
    },
    onLoad(options) {
        // this.hasChoiceID = options.id || ''
        this.invoiceDetail = JSON.parse(options.invoiceDetail) || '';
    },
    onShow() {
        this.getTitleList();
        // this.getPersonalTitleList();
        // this.getCompanyTitleList();
        // this.getPageDataFun();
    },
    methods: {
        /**
         * 方法
         */
        //获取页面数据方法
        /* async getPageDataFun(){
      const [{ data: companyTitleList }, { data: personalTitleList }] = await Promise.all([getCompanyTitleListPost(), getPersonalTitleListPost()]);
      Object.assign(this, {
        companyTitleList, personalTitleList,
        defaultEnterpriseInvoice: this.$Storage.defaultEnterpriseInvoice.value,
        defaultPersonalInvoice: this.$Storage.defaultPersonalInvoice.value
      })
    },

    //nav点击事件
    changeType(item, index) {
      this.active = index;
    },
    //选择个人抬头
    selectPersonaTitle(index,item) {
      this.personalIconId = index
      uni.$emit('chooseTitle',item)
      uni.navigateBack({
        delta:-1
      })
    },
    //选择公司抬头
    selectCompanyTitle(index,item) {
      this.companyIconId = index
      uni.$emit('chooseTitle',item)
      uni.navigateBack({
        delta:-1
      })
    }, */

        // 添加发票抬头
        clickAddIncoive() {
            uni.navigateTo({
                url: '/packages/invoice-center/pages/add-invoice-title/main?type=add',
            });
        },
        // 获取抬头列表
        async getTitleList() {
            let { data } = await getInvoiceTitleList();
            this.list = data;
            // if (this.hasChoiceID) {
            //     this.choiceActive = data.findIndex(item => {
            //         return item.id == this.hasChoiceID
            //     })
            // }
            data.map((item, index) => {
                if (item.id == this.invoiceDetail.id) {
                    return (this.choiceActive = index);
                }
            });
        },
        choiceNowTitle(idx, item) {
            if (idx == this.choiceActive) return;
            this.choiceActive = idx;
            uni.$emit('chooseTitle', item);
            setTimeout(() => {
                uni.$emit('chooseTitle', item);
                uni.navigateBack({
                    delta: -1,
                });
            }, 500);
            // uni.navigateBack({
            //     delta: -1
            // })
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 16px;
$colorgray: #909090;
/* .add-center {
      width: 100%;
      background-color: #f6f6f6;
      min-height: 100%;
      overflow: hidden;

      // 横向滑动tab
      .scroll-view_H {
        background-color: #f6f6f6;

        .slide_type_list {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          padding: 15px 0;

          .slide_type_list_view {
            padding-bottom: 5px;
            font-size: 15px;
            color: #333333;
          }

          .is_selected {
            color: $btn-color;
            font-weight: bold;
            position: relative;
          }

          .is_selected:before {
            content: "";
            position: absolute;
            width: 24px;
            height: 3px;
            background: linear-gradient(270deg, rgba(255, 130, 0, 0) 0%, $btn-color 100%);
            left: 50%;
            bottom: -4rpx;
            transform: translateX(-50%);
          }
        }
      }

      .mask-view {
        padding: 10px 15px;
        padding-top: 0px;
        padding-bottom: calc(env(safe-area-inset-bottom) + 64px);
        .mask-flex {
          display: flex;
          margin: 10px 0;
          margin-top: 0;
          align-items: center;
          justify-content: space-between;
        }

        .mask-list {
          padding: 20rpx;
          background: #ffffff;
          border-radius: 5px;
          .mask-default {
            width: 30px;
            height: 16px;
            background: $btn-mantle-color;
            border-radius: 3px;
            border: 1px solid $btn-color;
            line-height: 18px;
            text-align: center;
            font-size: 10px;
            color: $btn-color;
            display: inline-block;
            margin-left: 8px;
          }
          .right-div {
            img {
              width: 18px;
              margin-left: 22px;
              height: 17.5px;
            }
          }
        }
      }

      .label-txt {
        font-size: 28rpx;
      }
      .mt8{
        margin-top:8rpx;
        font-size: 24rpx;
      }

      .title-bold {
        font-weight: 500;
      }

      .color-font {
        color: $btn-color;
      }

      .no-data {
        text-align: center;
        margin-top: 80px;
      }

      .bottom-view {
        padding-bottom: env(safe-area-inset-bottom);
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-btn {
          margin-top: 10px;
          margin-bottom: 10px;
          line-height: 44px;
          width: 345px;
          background-color: $btn-color;
          color: #ffffff;
          font-size: 15px;
          border-radius: 5px;
          text-align: center;
        }
      }
    } */

.add-center {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    .title-list {
        flex: 1;
        padding: 24rpx 0;
        .item {
            background: #ffffff;
            margin: 0 24rpx;
            padding: 50rpx 30rpx;
            border-radius: 12rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;
            .title-name {
                display: flex;
                width: 90%;
                flex-direction: row;
                align-items: center;
                font-size: 36rpx;
                img {
                    width: 60rpx;
                    height: 60rpx;
                    margin-right: 20rpx;
                }
                span {
                    width: 80%;
                }
            }
            .is-choice {
                img {
                    width: 50rpx;
                    height: 50rpx;
                }
            }
        }
    }

    .bottom-view {
        padding-bottom: env(safe-area-inset-bottom);
        width: 100vw;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-btn {
            margin-top: 10px;
            margin-bottom: 10px;
            line-height: 44px;
            width: 345px;
            background-color: $btn-color;
            color: #ffffff;
            font-size: 15px;
            border-radius: 5px;
            text-align: center;
        }
    }
}
</style>
