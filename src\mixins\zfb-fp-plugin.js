// 支付宝小程序设备指纹

// #ifdef MP-ALIPAY || MP-MPAAS
import plugin from '@/s-kit/js/v3-plugin/fp/zfb/frms';
import { fpUrl,name } from '../../project.config';
import { mapGetters } from 'vuex';
import js from '../s-kit/js';

// import Store from '@/store';
// #endif
// #ifdef MP-MPAAS
import cnpcBridge from '@/s-kit/js/v3-native-jsapi/cnpcBridge.js';
// #endif
export default {
    // #ifdef MP-ALIPAY || MP-MPAAS
    computed: {
        ...mapGetters(['fpInfo']),
    },
    mounted() {
        console.log('zfb fp mounted');
    },
    methods: {
        getFP(custId = 'zfb') {
            // fp已内置缓存，不用再做缓存处理
            // if (this.fpInfo) return this.fpInfo;
            let openId 
            if (JSON.stringify(name).includes('-zfb')) {
                openId = uni.getStorageSync('tokenInfo').openId || {};
            }else{
                // let userTokenInfo = {}
                // userTokenInfo = uni.getStorageSync('Define_Res_User_Info') || {};
                // // cnpcBridge.getValueToNative('Define_Res_User_Info', value => {
                // //     if (value) {
                // //         userTokenInfo = JSON.parse(decodeURIComponent(value)) ? JSON.parse(decodeURIComponent(value)) : '';
                // openId = JSON.parse(userTokenInfo).thirdId
                // //     }
                // // });
            }
            // console.log('openId',openId)
            return new Promise((resolve, reject) => {
                plugin.setCustID(custId);
                plugin.setUrl(fpUrl);
                plugin.getFingerPrint(openId || '', {
                    success: res => {
                        console.log('zfb dfp: ' + res);
                        // Store.commit('setFPInfo', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.log('zfb fp error: ' + err);
                        reject(err);
                    },
                });
            });
        },
    },
    // #endif
};
