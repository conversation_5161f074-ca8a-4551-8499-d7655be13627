<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="实体卡绑定"></zj-navbar>
            <div class="f-1 bg-F7F7FB p-LR-16">
                <div class="form border-rad-4">
                    <input
                        class="border-box border-rad-4"
                        v-model="current.name"
                        :placeholder-style="fromInput"
                        disabled
                        placeholder="请输入真实姓名"
                    />
                </div>
                <div class="form border-rad-4">
                    <input
                        class="border-box border-rad-4"
                        disabled="disabled"
                        :placeholder-style="fromInput2"
                        type="text"
                        v-model="current.identitytypeName"
                    />
                </div>
                <div class="form border-rad-4">
                    <input
                        class="border-box border-rad-4"
                        :placeholder-style="fromInput"
                        maxlength="18"
                        v-model="current.identity"
                        disabled
                        placeholder="请输入证件号码"
                    />
                </div>
                <div class="form border-rad-4">
                    <input
                        class="border-box border-rad-4"
                        :placeholder-style="fromInput"
                        maxlength="16"
                        type="number"
                        v-model="current.cardNo"
                        placeholder="请输入16位实体卡卡号"
                    />
                </div>
                <!-- #ifndef MP-MPAAS -->
                <div class="form formCode fl-row fl-al-cen border-rad-4">
                    <input
                        class="border-box border-rad-4"
                        :placeholder-style="fromInput"
                        maxlength="6"
                        v-model="current.captcha"
                        placeholder="请输入验证码"
                    />
                    <div v-if="codeFlag" @click="getVerificationCode()" class="color-E64F22 bg-fff fl-sp-end te-right"> 获取验证码 </div>
                    <div v-else class="color-E64F22 bg-fff fl-sp-end te-right"> {{ count }}秒后再次获取 </div>
                </div>
                <div class="prompt font-12 weight-400 color-999">验证码短信会发送到您的办卡预留手机上</div>
                <!--  #endif -->
                <!-- #ifdef MP-MPAAS -->
                <div class="form formCode fl-row fl-al-cen border-rad-4" v-if="isHarmony">
                    <input
                        class="border-box border-rad-4"
                        :placeholder-style="fromInput"
                        maxlength="6"
                        v-model="current.captcha"
                        placeholder="请输入验证码"
                    />
                    <div v-if="codeFlag" @click="getVerificationCode()" class="color-E64F22 bg-fff fl-sp-end te-right"> 获取验证码 </div>
                    <div v-else class="color-E64F22 bg-fff fl-sp-end te-right"> {{ count }}秒后再次获取 </div>
                </div>
                <div class="prompt font-12 weight-400 color-999">验证码短信会发送到您的办卡预留手机上</div>
                <!--  #endif -->
                <div class="btn-warp fl-jus-bet fl-row">
                    <div @click="toUsedCardBind" class="btn btn-plain border-rad-8 color-E64F22">曾用卡绑定</div>
                    <div @click="boundOilCard" class="btn primary-btn border-rad-8 shad-ef color-fff">绑定</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';

// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-oil-add-cards';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD
import wxOilAddCards from './diff-environment/wx-oil-add-cards.js';
import zfbOilAddCards from './diff-environment/zfb-oil-add-cards.js';
// #endif
// #ifdef H5-CLOUD
import cloudOilAddCards from './diff-environment/cloud-oil-add-cards.js';
// #endif
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxOilAddCards,
        zfbOilAddCards,
        // #endif
        // #ifdef H5-CLOUD
        cloudOilAddCards
        // #endif

    ],
    data() {
        return {
            data: ['身份证', '护照'],
            second: '发送验证码', //验证码倒计时
            //表单校验规则
            current: {
                // 姓名
                name: '',
                // 证件号码
                identity: '',
                // 卡号
                cardNo: '',
                // 验证码
                captcha: '',
                // 证件类型
                identitytype: '1',
                identitytypeName: '身份证',
            },
            // 倒计时
            count: 60,
            // 是否显示倒计时
            codeFlag: true,
            // 倒计时TImer
            countdownTimer: null,
            // 入参身份证号
            identityNo: '',
        };
    },
    methods: {},
    computed: {
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            isHarmony: state => state.thirdIndex.isHarmony,
            // riskManagementLatV3: (state) => state.locationV3_app.riskManagementLatV3, //风控纬度
            // riskManagementLonV3: (state) => state.locationV3_app.riskManagementLonV3, //风控经度
        }),
    },
};
</script>
<style scoped lang="scss">
.view {
    .form {
        width: 100%;
        height: 44px;
        margin-top: 16px;

        input {
            padding-left: 15px;
        }
    }

    .formCode {
        div {
            width: 100%;
            height: 44px;
            line-height: 44px;
            padding-right: 15px;
        }
    }

    .prompt {
        margin-top: 12px;
    }

    .btn-warp {
        margin-top: 16px;

        .btn {
            height: 44px;
            line-height: 44px;
            width: 50%;
        }

        :nth-child(2) {
            margin-left: 12px;
        }
    }
}
</style>
<style scoped>
input {
    width: 100%;
    height: 100%;
    background-color: #fff;
}
</style>
