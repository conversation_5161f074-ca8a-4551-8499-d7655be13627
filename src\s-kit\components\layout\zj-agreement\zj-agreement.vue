<template>
    <div class="pop_div" v-if="facePop">
        <div class="content">
            <div class="content_div">
                <div class="pop_title">人脸验证</div>
                <div class="tips">为了您的账户安全，请进行人脸识别验证</div>
                <img class="facial_img" :src="imgUrl" alt="" />
                <!-- <div class="facial_img"></div> -->
                <div class="agreement_div fl-row">
                    <img v-if="selectShow" src="../../../image/empty.png" alt="" @click="changeSelectShow(false)" />
                    <img v-else src="../../../image/successSel.png" alt="" @click="changeSelectShow(true)" />

                    <!-- #ifdef MP-MPAAS -->
                    <div class="box font-12 fl-row fl-al-sta weight-400;">
                        <div class="box_first font-12 color-999" @click='selectShow = !selectShow'>我已阅读并同意</div>
                        <div class="box2 font-12 color-E64F22" @click="checkAgr()">《认证协议》</div>
                        <span class="box2 font-12 color-999">和</span>
                        <div class="box2 font-12 color-E64F22" @click="checkAgrUrl">《个人信息处理规则》</div>
                    </div>
                    <!-- #endif -->
                    <!-- #ifndef MP-MPAAS -->
                    <div class="box font-12 fl-row fl-al-cen weight-400;">
                        <div class="box_first font-12 color-999" @click='selectShow = !selectShow'>我已阅读并同意</div>
                        <div class="box2 font-12 color-E64F22" @click="checkAgr()">《认证协议》</div>
                    </div>
                    <!-- #endif -->
                </div>
                <div class="btn_div">
                    <div class="finish_verification" @click="agreeClick()">同意并开始验证 </div>
                </div>
            </div>
            <div class="close_div" @click="closeBtn()">
                <img src="../../../image/closeAgreement.png" alt />
            </div>
        </div>
    </div>
</template>

<script>
import { userAgreement } from '../../../js/v3-http/https3/oilCard/index';
const { baseImgUrl } = require('../../../../../project.config');
import { mapGetters, mapState } from 'vuex';
export default {
    name: 'facePop',
    components: {},

    props: {
        // enterNavEvent: {
        //   type: Function,
        //   default: () => {
        //     return Function;
        //   },
        // },
    },
    data() {
        return {
            // 协议勾选
            selectShow: true,
            // 协议链接
            webUrl: 'https://render.alipay.com/p/yuyan/180020010001196791/preview.html?agreementId=AG01000130',
            // 背景图
            imgUrl: baseImgUrl + '/uniapp/uni-mall/static/img/facialImages.png',
        };
    },
    computed: {
        // ...mapGetters(['facePop']),
        ...mapState({
            facePop: state => state.thirdIndex.facePop,
        }),
    },
    methods: {
        /**
         * @description  : 关闭按钮
         * @return        {*}
         */
        closeBtn() {
            // 取消协议勾选
            this.selectShow = true;
            // 关闭验证弹窗
            this.$store.dispatch('changeFacePop', false);
            this.$emit('cancelClick');
        },
        /**
         * @description  : 协议勾选/取消
         * @return        {*}
         */
        changeSelectShow(val) {
            this.selectShow = val;
        },
        /**
         * @description  : 同意并开始验证
         * @return        {*}
         */
        agreeClick() {
            if (this.selectShow) {
                uni.showToast({
                    title: '请勾选协议',
                    icon: 'none',
                });
                return;
            }
            this.$emit('enterNavEvent');
            this.selectShow = true;
        },
        // #ifdef MP-MPAAS
        checkAgrUrl() {
            this.$cnpcBridge.openModule({
                type: 'web',
                url: this.webUrl,
            });
        },
        checkAgr() {
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: '1',
                    cityName: res.cityCode,
                    name: '人脸识别认证协议',
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    } else {
                        uni.showToast({ title: '未找到该协议' });
                    }
                } else {
                    uni.showToast({ title: userAgreementRes.message });
                }
            });
        }, // #endif
        // #ifndef MP-MPAAS || H5-CLOUD 
        async checkAgr() {
            let params = {
                type: '1',
                cityName: '全国',
                name: '人脸识别认证协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // uni.navigateTo({
                    //     url: '/packages/web-view/pages/home/<USER>' + encodeURIComponent(userAgreementRes.data.fileUrl),
                    // });
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            } else {
                uni.showToast({ title: userAgreementRes.message });
            }
        },
        // #endif
        // #ifdef H5-CLOUD
        async checkAgr() {
            let params = {
                type: '1',
                cityName: '全国',
                name: '人脸识别认证协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);

                    this.closeBtn()

                } else {

                    this.closeBtn()

                    uni.showToast({ title: '未找到该协议' });
                }
            } else {
                uni.showToast({ title: userAgreementRes.message });
            }
        },
        // #endif
    },
    created() { },
    mounted() { },
    watch: {},
    // beforeDestroy() {
    //     // 关闭人脸认证协议弹窗
    //     this.$store.dispatch('changeFacePop', false);
    // },
    destroyed() {
        this.selectShow = true;
        // 关闭人脸认证协议弹窗
        this.$store.dispatch('changeFacePop', false);
    },
};
</script>

<style lang="scss" scoped>
.pop_div {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    /* #ifndef APP-NVUE */
    z-index: 1000;
    /* #endif */
    // #ifdef H5-CLOUD
    z-index: 100;
    // #endif
    background-color: rgba(0, 0, 0, 0.6);
}

.content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .content_div {
        background: #fff;
        border-radius: 8px;
        width: 285px;
        // min-width 285px
        // min-height 386px
        // height 426px
        position: relative;
        // z-index 0
        padding: 20px 16px 20px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .pop_title {
            font-size: 18px;
            font-weight: 500;
            color: #333333;
            line-height: 23px;
        }

        .tips {
            font-size: 13px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
        }

        .facial_img {
            width: 139px;
            height: 150px;
            margin: 31px 0 20px 0;
        }

        .agreement_div {
            // display: flex;
            // flex-direction: row;
            // align-items: flex-start;
            line-height: 17px;
            width: 100%;

            img {
                width: 17px;
                height: 16px;
                line-height: 17px;
            }

            .agreement {
                width: 200px;
                font-size: 12px;
                font-weight: 400;
                color: #999999;
                line-height: 17px;
                display: flex;

                a {
                    color: #e64f22;
                }
            }

            // .box {
            //     display: inline-block;
            //     width: 100%;
            //     white-space: pre-wrap;
            //     margin-top: 4px;
            //     word-break: break-all;

            //     div {
            //         // display: inline-block;
            //     }

            //     .box_first {
            //         float: left;
            //     }
            // }
            .box {
                display: inline-block;
                width: 100%;
                white-space: pre-wrap;
                // margin-top: 1rpx;
                margin-left: 3px;
                word-break: break-all;
                overflow-wrap: break-word;
                line-height: 17px;
            }

            // .box div {
            //     display: inline-block;
            // }

            .box_first {
                float: left;
                line-height: 17px;
            }

            .box2 {
                display: inline;
                line-height: 17px;
            }
        }
    }

    .finish_verification {
        width: 252px;
        height: 44px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1 10px 0px rgba(0, 0, 0, 0.07);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        color: #ffffff;
        font-size: 15px;
        margin-top: 13px;
    }
}

.close_div {
    text-align: center;
    margin-top: 30px;

    img {
        width: 29px;
        height: 29px;
    }
}</style>
