
import projectConfig from '../../../../../../../project.config';
import { mapState, mapGetters } from 'vuex';
import qrcode from 'uniapp-qrcode';
// import { accountRiskEngineSupport } from '@/s-kit/js/v3-http/https3/wallet';

export default {
    // #ifdef MP-ALIPAY
    data() {
        return {
            clientName: '支付宝小程序',
        };
    },
    computed: {
        ...mapGetters(['openId']),
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
            locationState: state => state.locationV3_app.locationState,
        }),
        gsmsToken() {
            let { gsmsToken } = uni.getStorageSync('tokenInfo') || {};
            return gsmsToken || '';
        },
        payPlugin() {
            return this.$sKit.aliPayPlugin;
        },
    },
    methods: {
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        enablePositioning() {
            this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                confirmText: '确认',
                confirmColor: '#000',
                cancelText: '取消',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        uni.openSetting({
                            success: opRes => {
                                // 是否同意获取地理位置信息权限
                                if (opRes.authSetting['location']) {
                                    this.init(true);
                                }
                            },
                        });
                    }
                },
            });
        },
        // 小程序端判断是否开启定位权限
        checkIfTheVehicleHasEnabledPositioning(callback) {
            uni.getSetting({
                success: res => {
                    if (res.authSetting['location'] && this.locationState) {
                        callback();
                    } else {
                        this.locationPermission = false;
                    }
                },
            });
        },
        // 插件生成会员码
        async pollGetQrCode(isOrder) {
            // TODO 313小程序不做此功能，后续版本异步风控信息提交，后续确认订单如阻断则人脸逻辑
            // try {
            //     const riskRes = accountRiskEngineSupport({});
            // } catch (e) {}
            let res = await this.keyBoardRef.genQrCode();
            if (res.code == 0) {
                this.memberCode = res.data.qrcode;
                this.$emit('codeChange', this.memberCode);
                setTimeout(() => {
                    // qrcode.qrcode('qrcode', res.data.qrcode, 555, 555, this);
                    this.generateQRCode(res.data.qrcode);
                }, 0);
                // this.clearTimerMember()
                // this.clearTimerQuqeyOrder()
                this.riskControl();
                // 查询订单
                if (isOrder) {
                    console.log('测试会员码=====');
                    this.getRposUnPayList();
                }
            } else {
                this.dispatch('zjShowModal', {
                    title: res.msg,
                    content: `${res.code}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        // 轮询订单
        async getRposUnPayList() {
            // 清除查询订单的定时器
            this.clearTimerQuqeyOrder();
            // 防止查询订单成功后继续执行递归
            if (this.turnOffRecursionMember) {
                let params = {
                    token: this.gsmsToken,
                    openId: this.openId,
                    appId: projectConfig.appId,
                    data: {},
                    baseType: projectConfig.baseType,
                };
                const res = await this.payPlugin.GetRposUnPayList(params);
                if (this.turnOffRecursionMember) {
                    if (res.code === 'PAY_SUCCESS') {
                        if (!res.data) {
                            this.memberCodeTimer = setTimeout(() => {
                                this.getRposUnPayList();
                            }, this.pollTime);
                            return;
                        }
                        if (this.isJump) {
                            this.isJump = false;
                            this.clearTimerQuqeyOrder();
                            let url = '/packages/third-scan-code-payment/pages/member-order-price/main';
                            let params = { ...res.data, refer: this.refer };
                            // 支付宝referer过长兼容
                            params.zfbRefererMax = true;
                            let type = 'navigateTo';
                            this.$sKit.layer.useRouter(url, params, type);
                            this.clearTimerQuqeyOrder();
                            this.clearTimerMember();
                            this.isJump = false;
                            this.turnOffRecursionMember = false;
                        }
                    } else {
                        // 未查询到订单继续查询
                        this.memberCodeTimer = setTimeout(() => {
                            this.getRposUnPayList();
                        }, this.pollTime);
                    }
                }
            }
        },
    },
    // #endif
};
