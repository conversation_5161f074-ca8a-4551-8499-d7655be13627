<template>
    <div class="content">
        <img class="login-image" src="@/static/cnpc-logo.png" />
        <div class="login-text">能源e站</div>
        <div class="login" @click="clickLogin">重新登录</div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import GetPhoneNumber from '@/components/login/get-phone-number';
import wxLogin from '@/utils/login';
import { mapState, mapMutations, mapGetters } from 'vuex';
import Config from '@/utils/config';

export default {
    components: {
        GetPhoneNumber,
    },
    data() {
        return {
            redirectUrl: '',
        };
    },
    onLoad() {
        this.clickLogin = this.$util.throttleUtil(this.clickLogin);
        const _this = this;
        const redirectUrl = this.$root.$mp.query.redirectUrl || '';
        this.redirectUrl = redirectUrl ? decodeURIComponent(redirectUrl) : '';
        console.log('注册页面路径', this.redirectUrl);
    },
    methods: {
        async clickLogin() {
            this.$store.commit('setPaymentFlag', false);
            // this.$store.commit("mSetInfoV3", false);
            try {
                await wxLogin.init('FFFW');
            } catch (error) {
                console.log(error);
            }
            if (!this.token3) {
                uni.redirectTo({
                    url: '/packages/transferAccount/pages/home/<USER>',
                });
            }
        },
        getPhoneNumberChange() {
            uni.redirectTo({
                url: this.redirectUrl,
            });
        },
    },
    computed: {
        ...mapState({
            token3: state => state.token3, // 打开升级弹窗
        }),
    },
};
</script>

<style scoped lang="stylus">
.content
  width 100vw
  height 100vh
  display flex
  flex-direction column
  align-items center
  justify-content center
.login-image
  height 100px
  width 100px
.login-text
  margin-top 20px
  font-weight 700
  color #333333
  font-size 16px
.login
  margin-top 40px
  color #FFFFFF
  padding-left 100px
  padding-right 100px
  background-color #FF8200
  border-radius 5px
  line-height 50px
  font-weight 700
</style>
