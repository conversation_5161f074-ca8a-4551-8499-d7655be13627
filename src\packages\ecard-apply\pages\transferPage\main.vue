<template>
    <div class="view">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :back-text="cardType == 1 ? '电子卡开通成功 ' : cardType == 0 ? '实体卡绑定成功' : ''"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <view class="setIcon">
            <view class="setingCon">
                <view class="setimg">
                    <img src="../../image/open_success.png" alt />
                </view>
                <view class="setfont">{{ cardType == 1 ? '电子卡办理成功 ' : cardType == 0 ? '实体卡绑定成功' : '' }}</view>
            </view>
        </view>
        <view class="setBtnPadding">
            <view>
                <!-- <u-button>主要按钮</u-button> -->

                <p
                    >温馨提醒：您的{{ cardType == 1 ? '电子卡加油卡' : cardType == 0 ? '实体卡加油卡' : '' }}已{{
                        cardType == 1 ? '办理' : cardType == 0 ? '绑定' : ''
                    }}成功，设置支付密码后才可正常消费使用，请前往设置</p
                >
                <view class="btn" @click="toRestePassword">请前往设置密码</view>
            </view>
        </view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import pageConfig from '@/utils/pageConfig.js';
export default {
    name: '',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            params: '',
            idType: Number,
            cardType: '',
            detailCard: {},
        };
    },
    onShow() {
        // this.$store.dispatch("card/getAllCardList");
    },
    onLoad(options) {
        // this.params = options.params
        console.log(JSON.parse(options.params), '-----options.params-----');
        this.detailCard = JSON.parse(options.params);
        console.log(this.cardList, 'TTTTTTTT');
        this.cardList.map(item => {
            console.log(item.cardNo == this.detailCard.cardNo, item.cardNo, this.detailCard.cardNo, '-----UUUUUU------');
            console.log('进入map了吗');
            if (item.cardNo == this.detailCard.cardNo) {
                console.log('进入判断了吗');
                console.log(item.cardNo, 'item.cardNo');
                this.cardType = item.cardType;
                this.detailCard.cardType = item.cardType;
            }
        });
        console.log(this.cardType, this.detailCard.cardType, this.detailCard, '当前绑定的油卡的相关参数');
        this.params = JSON.stringify(this.detailCard);
    },
    methods: {
        getCardType(val) {
            return val == 1 ? '电子卡开通成功 ' : val == 0 ? '实体卡绑定成功' : '';
        },
        toRestePassword() {
            uni.redirectTo({
                url: `/packages/password/pages/reset-password/main?params=${this.params}&id=0`,
            });
        },
    },
    computed: {
        ...mapGetters(['openId', 'cardList']),
    },
};
</script>

<style lang="scss" scoped>
.page {
    height: 100%;
}
.view {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    .setIcon {
        height: 30%;
        margin: 10px;
        background: #fff;
        border-radius: 4px;
        .setingCon {
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: center;
            align-items: center;
            .setimg {
                img {
                    width: 100px;
                    height: 100px;
                }
            }
            .setfont {
                font-size: 18px;
            }
        }
    }
    .setBtnPadding {
        padding: 10px;
        .btn {
            width: 100%;
            height: 40px;
            background: #ff8200;
            line-height: 40px;
            text-align: center;
            font-size: 17px;
            border-radius: 4px;
            color: #fff;
        }
        p {
            font-size: 10px;
            margin-bottom: 5px;
            // color: #ff8200;
        }
    }
}
</style>
