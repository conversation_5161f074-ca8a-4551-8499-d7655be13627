import { refreshTokenApi } from '../../v3-http/https3/user';
import { getTodoInfo } from '../../v3-http/https3/wallet';
import cnpcBridge from '../../v3-native-jsapi/cnpcBridge';
import accountCenter from '../../v3-native-jsapi/accountCenter';
import paymentCenter from '../../v3-native-jsapi/paymentCenter';
import Store from '../../../../store/index';
import sKit from '../../index';
import store from '../../../../store/index';

export default {
    state: {
        //是否弹出加油弹窗
        isPoppedOil: false,
        // 首页加油是否轮询
        circularOrder: false,
        //加油选择订单可用券
        selectTickets: undefined,
        // 加油选择礼品卡
        selectCard: [],
        //E享加油的订单
        infoAuthOrder: {},
        tabbarSelectIndex: 0,
        // 处理web小程序会员码组件查询到订单后，路由跳转到下一个页面，返回该组件页面时继续启动定时任务
        memberBack: false,
        // 处理web小程序付款码组件查询到订单后，路由跳转到下一个页面，返回该组件页面时继续启动定时任务
        codeBack: false,
        // 付款码订单页面是否执行onshow标识
        isToSelectTicketPage: false,
        //v3账户插件实例
        accountDataPlugin: null,
        // 首次指引步骤标识
        guideStep: 0,
        // 订单红点标识
        isOrderDot: false,
        // 城市code
        cityCode: '',
        //人脸验证之前的协议弹窗
        facePop: false,
        // 是否应该广告弹窗
        isUpAdPopupOil: true,
        isUpAdPopupMy: true,
        // e享加油选择券
        selectCoupon: {},
        // E享加油订单
        eInfoAuthOrder: {},
        isHarmony: false,
        isLoading: false,
        selectedCoupon: {}, //选中的券信息
        firstGetTransNoList: true,
        stationTagsArr: [],
        stationTagsObj: {},
        showComponent: false,
        mpaasAccountDataPlugin: {},
        boxTop: '210', //有无banner页面高度
        zjNavBarShow: true,
        rootFontSize: '',
        cloudAuth: false, //处理云闪付触支付发生物风控
        cleanTheRegisteredAddress: true,
    },
    mutations: {
        setCleanTheRegisteredAddress(state, info) {
            state.cleanTheRegisteredAddress = info;
        },
        setBoxTop(state, info) {
            state.boxTop = info;
        },
        /**
         * @description  : // 是否应该广告弹窗
         * @return        {*}
         */
        setIsUpAdPopupOil: (state, isUpAdPopupOil) => {
            state.isUpAdPopupOil = isUpAdPopupOil;
        },
        setShowComponent: (state, info) => {
            state.showComponent = info;
        },
        /**
         * @description  : // 是否应该广告弹窗
         * @return        {*}
         */
        setIsUpAdPopupMy: (state, isUpAdPopupMy) => {
            state.isUpAdPopupMy = isUpAdPopupMy;
        },
        /**
         * @description  : // 是否弹出加油弹窗
         * @return        {*}
         */
        setIsPoppedOil: (state, isPoppedOil) => {
            state.isPoppedOil = isPoppedOil;
        },
        /**
         * @description  : // 首页加油是否轮询
         * @return        {*}
         */
        mSetCircularOrder(state, info) {
            state.circularOrder = false;
        },
        /**
         * @description  : // E享加油的订单
         * @return        {*}
         */
        mSetInfoAuthOrder(state, info) {
            state.infoAuthOrder = info;
        },
        /**
         * @description  : // 暂时没用
         * @return        {*}
         */
        mSetTabbarSelectIndex(state, info) {
            state.tabbarSelectIndex = info;
        },
        /**
         * @description  : // v3账户插件实例
         * @return        {*}
         */
        setAccountDataPlugin(state, info) {
            state.accountDataPlugin = info || {};
        },
        setMpaasAccountDataPlugin(state, info) {
            state.mpaasAccountDataPlugin = info || {};
        },
        /**
         * @description  : //  加油选择订单可用券
         * @return        {*}
         */

        setSelectTickets(state, info) {
            if (info) {
                let stationStr = encodeURIComponent(JSON.stringify(info));
                //加油选择订单可用券
                state.selectTickets = stationStr;
            } else {
                state.selectTickets = info;
            }
        },
        /**
         * 加油选择礼品卡
         * */
        setSelectCard(state, info) {
            state.selectCard = info;
        },
        /**
         * @description  : //  首次指引步骤标识
         * @return        {*}
         */
        setGuideStep(state, info) {
            state.guideStep = info;
        },
        /**
         * @description  : //  订单红点标识
         * @return        {*}
         */
        setIsOrderDot(state, info) {
            state.isOrderDot = info;
        },
        /**
         * @description  : //  订单红点标识
         * @return        {*}
         */
        setFacePop(state, info) {
            state.facePop = info;
        },
        /**
         * @description  : //  城市code
         * @return        {*}
         */
        setCityCode(state, info) {
            state.cityCode = info;
        },
        /**
         * @description  : //  e享加油选择券
         * @return        {*}
         */
        setSelectCoupon(state, info) {
            state.selectCoupon = info;
        },
        /**
         * 存取鸿蒙状态
         * */
        setIsHarmony(state, info) {
            state.isHarmony = info;
        },
        /**
         * @description  : //  loading效果
         * @return        {*}
         */
        setLoadingStatus(state, info) {
            state.isLoading = info;
        },
        /**
         * @description  : //  选中券信息
         * @return        {*}
         */
        setSelectedCoupon(state, info) {
            console.log('获取选择的信息', info);
            state.selectedCoupon = info;
        },
        setFirstGetTransNoList(state, info) {
            state.firstGetTransNoList = info;
        },
        setStationTags(state, info) {
            state.stationTagsArr = info.stationTagsArr;
            state.stationTagsObj = info.stationTagsObj;
        },
        setZjNavBarShow(state, info) {
            state.zjNavBarShow = info;
        },
        setRootFontSize(state, info) {
            console.log(info, '触发onResize');
            state.rootFontSize = info;
        },
        setCloudAuth(state, info) {
            console.log(info, 'setCloudAuth');
            state.cloudAuth = info;
        },
    },

    actions: {
        /**
         * @description  :  token过期无感刷新
         * @param         {String} refreshToken -刷新token
         * @return        {*}
         */
        async tokenExpired({ state, commit }, suc) {
            // #ifdef MP-MPAAS
            let userTokenInfo = await cnpcBridge.getUserTokenInfo();
            // #endif
            let params = {
                // #ifdef MP-MPAAS
                refreshToken: userTokenInfo.refreshToken,
                // #endif
                // #ifdef MP-ALIPAY
                refreshToken: uni.getStorageSync('tokenInfo')?.refreshToken,
                // #endif
                // #ifdef MP-WEIXIN
                refreshToken: uni.getStorageSync('tokenInfo')?.refreshToken,
                // #endif
                // #ifdef H5-CLOUD
                refreshToken: uni.getStorageSync('tokenInfo')?.refreshToken,
                // #endif
            };
            if (!params.refreshToken) return;
            let res = await refreshTokenApi(params, { isCustomErr: true });
            console.log('调用刷新token接口的结果', res);
            if (res.success && res.data.refreshToken) {
                // #ifdef MP-MPAAS
                // 3.0登录token
                userTokenInfo.token3 = res.data.accessToken;
                userTokenInfo.expiresIn = res.data.expiresIn;
                // 用来刷新token
                userTokenInfo.refreshToken = res.data.refreshToken;
                // 插件和密码键盘使用恶token
                userTokenInfo.gsmsToken = res.data.gsmsToken;
                // APP存储token
                cnpcBridge.setValueToNative('UserTokenInfo', encodeURIComponent(JSON.stringify(userTokenInfo)));
                paymentCenter.initPay(userTokenInfo.gsmsToken);
                accountCenter.initAccountSDK(userTokenInfo.gsmsToken);
                // #endif
                let tokenInfo = uni.getStorageSync('tokenInfo');
                // #ifdef MP-ALIPAY
                if (tokenInfo.openId) {
                    res.data.openId = tokenInfo.openId;
                    uni.setStorageSync('v3token', res.data.accessToken);
                    store.commit('setNewMember', res.data.newMember);
                    res.data.cppeiLoginInfo = JSON.parse(res.data.cppeiLoginInfo || null);
                    res.data.postLoginActionJsonStr = JSON.parse(res.data.postLoginActionJsonStr || null);
                    uni.setStorageSync('tokenInfo', res.data);
                    sKit.aliPayPlugin?.init();
                } else {
                    suc.fail(res);
                    return;
                }
                // #endif
                // #ifdef MP-WEIXIN
                if (tokenInfo.openId) {
                    res.data.openId = tokenInfo.openId;
                    store.commit('mSetPersonalInformation3', res.data);
                    uni.setStorageSync('tokenInfo', res.data);
                    Store.commit('setToken3', res.data.accessToken);
                    wx.setStorageSync('expiresInV3', res.data.expiresIn);
                    wx.setStorageSync('refreshTokenV3', res.data.refreshToken);
                    Store.commit('mSetGsmsToken', res.data.gsmsToken);
                    sKit.wxPayPlugin?.initPayPlugin();
                    sKit.keyBordPlugin.initRef(state.accountDataPlugin);
                } else {
                    suc.fail(res);
                    return;
                }
                // #endif
                // #ifdef H5-CLOUD
                res.data.openId = tokenInfo.openId;
                store.commit('mSetPersonalInformation3', res.data);
                uni.setStorageSync('tokenInfo', res.data);
                Store.commit('setToken3', res.data.accessToken);
                uni.setStorageSync('expiresInV3', res.data.expiresIn);
                uni.setStorageSync('refreshTokenV3', res.data.refreshToken);
                Store.commit('mSetGsmsToken', res.data.gsmsToken);
                sKit.cloudPayPlugin.initPayPlugin();
                // #endif
                suc.success(res);
            } else {
                suc.fail(res);
            }
        },
        /**
         * @description  : 订单生成后选择使用的优惠券
         * @return        {*}
         */
        selectTicketsAction(context, info) {
            context.commit('setSelectTickets', info);
        },
        /**
         * @description  : 订单生成后选择使用的礼品卡
         * @return        {*}
         */
        selectCardAction(context, info) {
            context.commit('setSelectCard', info);
        },

        /**
         * @description  : 付款码页面是否执行onshow标识
         * @return        {*}
         */
        setIsToSelectTicketPageAction({ state, commit }, info) {
            state.isToSelectTicketPage = info;
        },
        /**
         * @description  : // 首次指引步骤标识
         * @return        {*}
         */
        changeGuideStep({ state, commit }, step) {
            commit('setGuideStep', step);
        },
        /**
         * @description  : // 人脸验证之前的协议弹窗
         * @return        {*}
         */
        changeFacePop({ state, commit }, val) {
            commit('setFacePop', val);
        },
        /**
         * @description  : // 获取待办事项
         * @return        {*}
         */
        getTodoInfoFun({ state, commit }, val) {
            return new Promise(async resolve => {
                let res = await getTodoInfo({},{ isload: false, isCustomErr: true });
                // res.data.todoType = 3;
                console.log(val, '下标');
                if (res.success) {
                    console.log(val.topTab, val.index, res.data.todoType, '获取待办事项==res.success');
                    if (val.topTab !== 'code' && val.index === 0 && res.data.todoType === 3) {
                        await store.dispatch('zjShowModal', {
                            content: '您的注册地信息尚未完善,为避免影响后续服务体验,请及时完善。',
                            title: '注册地待完善',
                            confirmText: '立即完善',
                            cancelText: '暂不处理',
                            confirmColor: '#333',
                            cancelColor: '#666',
                            success: res => {
                                commit('setCleanTheRegisteredAddress', false);
                                if (res.confirm) {
                                    console.log('跳转完善开户地', sKit);
                                    sKit.layer.useRouter('/packages/third-my-center/pages/clear-registr-sation/main');
                                } else if (res.cancel) {
                                }
                            },
                        });
                    } else {
                        resolve(res);
                    }
                }
            });
        },
    },
    getters: {
        //是否弹出加油弹窗
        isPoppedOil: state => state.isPoppedOil,
        // 首页加油是否轮询
        circularOrder: state => state.circularOrder,
        //E享加油的订单
        infoAuthOrder: state => state.infoAuthOrder,
        //加油选择订单可用券
        selectTickets: state => state.selectTickets,
        // 付款码页面是否执行onshow标识
        isToSelectTicketPage: state => state.isToSelectTicketPage,
        //人脸验证之前的协议弹窗
        facePop: state => state.facePop,
        // 选择礼品卡数组
        selectCard: state => state.selectCard,
        // 城市code
        cityCode: state => state.cityCode,
        // E享加油可用券
        selectCoupon: state => state.selectCoupon,
        // E享加油可用券
        selectedCoupon: state => state.selectedCoupon,
        boxTop: state => state.boxTop,
        setCloudAuth: state => state.cloudAuth,
    },
};
