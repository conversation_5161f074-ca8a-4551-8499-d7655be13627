import Http from './http';
import Util from './index';
import Store from '../store';
import location from '../store/modules/location';
import Config from './config';
import Router from 'uni-simple-router';
import { getCityFlag } from '../api/anEcardToOpenCard.js';

class WxLogin {
    /**
     * 检查是否授权
     * @returns {Promise}
     */
    constructor() {
        this.useRfrCode = true;
        this.wxCode = '';
    }
    checkScopeUserInfo() {
        return new Promise((resolve, reject) => {
            uni.getSetting({
                success: res => {
                    if (res.authSetting['scope.userInfo']) {
                        resolve();
                    } else {
                        reject('scope.userInfo 未授权');
                    }
                },
                fail: err => {
                    reject('scope.userInfo 未授权');
                    console.log(err);
                },
            });
        });
    }
    getUserInfo() {
        return new Promise((resolve, reject) => {
            uni.getUserInfo({
                // 获取授权信息
                withCredentials: true,
                success: res => {
                    resolve(res);
                },
                fail: error => {
                    reject(error);
                },
            });
        });
    }
    async init(val) {
        uni.reLaunch({
            url: '/packages/transferAccount/pages/home/<USER>',
        });
        return;
        // 01
        return new Promise(async (resolve, reject) => {
            try {
                wx.getPrivacySetting({
                    success: async res => {
                        let _this = this;
                        if (res.needAuthorization) {
                            console.log(res.needAuthorization, 'res.needAuthorization');
                            Store.dispatch('receiveButtonParameters', {
                                async privacySuccess(res) {
                                    if (res.confirm) {
                                        await _this.getInfo();
                                        resolve();
                                    } else {
                                        // 关闭毛玻璃显示登录按钮
                                        Store.commit('setFrostedglassIsLoading', false);
                                    }
                                },
                            });
                        } else {
                            await _this.getInfo();
                            resolve();
                        }
                    },
                });
            } catch (error) {
                await this.getInfo();
                reject();
            }
        });
    }
    async getInfo() {
        return new Promise(async (resolve, reject) => {
            Store.commit('setMaskDialogFlag', false);
            this.wxCode = await this._login();
            try {
                const res = await this.checkScopeUserInfo()
                    .then(() => {
                        return this.getUserInfo().then(res => {
                            //  头像已授权
                            console.log(res, 'getUserInfo');
                            Store.commit('setUserInfo', res);
                            Store.commit('setMpUserInfo', res);
                            return res;
                        });
                    })
                    .catch(e => {});
                await this.autoLogin().then(res => {
                    if (res.data.token) {
                    }
                });
                resolve();
            } catch (e) {
                console.log(e);
            } finally {
                Store.commit('setMaskDialogFlag', false);
                reject();
            }
        });
    }
    autoLogin() {
        uni.reLaunch({
            url: '/packages/transferAccount/pages/home/<USER>',
        });
        return;
        // 02
        // 公众号跳转小程序中转页按钮隐藏标识
        Store.state.isOfficialAccount = false;
        return new Promise(async (resolve, reject) => {
            if (!this.wxCode) {
                this.wxCode = await this._login();
            }
            await this.checkSession();
            Store.state.token = '';
            let params = { code: this.wxCode };
            if (Store.state.mpUserInfo && Store.state.mpUserInfo.userInfo) {
                params.city = Store.state.mpUserInfo.userInfo.city;
                params.province = Store.state.mpUserInfo.userInfo.province;
                params.nickName = Store.state.mpUserInfo.userInfo.nickName;
                params.avtUrl = Store.state.mpUserInfo.userInfo.avatarUrl;
                params.encryptedData = Store.state.mpUserInfo.encryptedData;
                params.ivStr = Store.state.mpUserInfo.iv;
            }

            let autoLoginParams = { jsonData: JSON.stringify(params) };
            Http.post('/app/json/login2/autoLogin', autoLoginParams)
                .then(res => {
                    Store.state.isOfficialAccount = true;
                    if (res.status === 0) {
                        Store.state.token = res.data.token;
                        wx.hideLoading();
                        if (res.data.token) {
                            this.saveUserInfo(res, resolve);
                            // 判断是否存在位置信息
                            Store.dispatch('isPositionInformation', {
                                callback: callbackToken3 => {
                                    console.log(callbackToken3, 'callbackToken3');
                                },
                                val: 'thirdLogin',
                            });
                        }
                        // 外部小程序跳转进入能源e站，如果是未登录的状态，弹出毛玻璃遮罩层
                        Store.commit('setFrostedglassIsLoading', false);
                        if (res.data.errorCode === 1034) {
                            Store.dispatch('isPositionInformation', { callback: () => {}, val: '' });
                            Store.commit('setOpenId', res.info);
                            // isLogin 是否登录成功标识
                            Store.commit('setLoginStatus', false);
                        }
                    } else {
                        uni.hideLoading();
                        // 2.0首页透明遮罩
                        Store.commit('setMaskDialogFlag', false);
                        Store.state.isOfficialAccount = false;
                        Store.dispatch('isPositionInformation', { callback: () => {}, val: '' });
                        Util.showModal(res.info || '登录失败', true);
                    }
                    wx.setStorageSync('grayHomePage', res.data.grayHomePage);
                    resolve(res);
                })
                .catch(error => {
                    uni.hideLoading();
                    Store.commit('setMaskDialogFlag', false);
                    Store.commit('setFrostedglassIsLoading', false);
                    Store.state.isOfficialAccount = false;
                    Store.dispatch('isPositionInformation', { callback: () => {}, val: '' });
                    reject(error);
                });
        });
    }
    /**
     * 注册登录
     * @param res 解码
     * @returns {Promise<unknown>}
     */
    async registerByAuthPhoneNumber(res, type) {
        uni.reLaunch({
            url: '/packages/transferAccount/pages/home/<USER>',
        });
        return;
        console.log('-----', type);
        // 公众号跳转小程序中转页按钮隐藏标识
        Store.state.isOfficialAccount = false;
        // return
        return new Promise(async (resolve, reject) => {
            // this.wxCode = res.code || await this._login()
            this.wxCode = await this._login();
            // // 检查登录过期状态，如果过期重新获取
            await this.checkSession();
            let url;
            let params = {};
            params.code = this.wxCode;
            if (type != 'smsLogin') {
                url = '/app/json/login2/signUpAndBind';
                params.encryptedData = res.encryptedData;
                if (res.iv) {
                    params.ivStr = res.iv;
                }
            } else {
                url = '/app/json/login2/phoneCodeLogin';
                params = { ...res, ...params };
            }
            console.log('params----', params);
            if (location.state.city && location.state.province) {
                params.province = location.state.province;
                params.city = location.state.city;
            }
            if (Store.state.mpUserInfo && Store.state.mpUserInfo.userInfo) {
                params = Object.assign({}, params, {
                    nickName: Store.state.mpUserInfo.userInfo.nickName || '',
                    // sex: Store.state.mpUserInfo.userInfo.gender || '',
                    avtUrl: Store.state.mpUserInfo.userInfo.avatarUrl || '',
                });
            }
            //推荐注册
            if (Store.state.staffStationId) {
                params.staffStationId = Store.state.staffStationId;
            }

            let signParams = { jsonData: JSON.stringify(params) };
            Http.post(url, signParams)
                .then(async res => {
                    Store.state.isOfficialAccount = true;
                    if (res.status === 0) {
                        Store.commit('setFrostedglassIsLoading', false);
                        Store.state.token = res.data.token;

                        if (res.data.token) {
                            this.saveUserInfo(res, resolve);
                            // 使用当前第一个油站请求接口判断是否是试点地区
                            // 如果是试点地区将用户自动升级为3.0用户
                            // 不是试点地区继续向下执行之前的逻辑
                            console.log(Store.state.location.markerArr, 'Store.state.location.markerArr');
                            if (Store.state.location.markerArr.length > 0 && res.data.newUserTag == 1) {
                                let resPilotAreas = await getCityFlag({ cityName: Store.state.location.markerArr[0].provinceName });

                                if (resPilotAreas.status == 0 && resPilotAreas.data.flag == 1) {
                                    console.log('是新用户吗');
                                    Store.dispatch('getToken3', 'upgrade');
                                } else {
                                    Store.dispatch('isPositionInformation', {
                                        callback: callbackToken3 => {
                                            Util.successToast('登录成功');
                                            console.log(callbackToken3, '3.0登录登录回调');
                                            if (!callbackToken3) {
                                                resolve(res.data);
                                            }
                                        },
                                        val: 'thirdLogin',
                                        loginFlag: type,
                                    });
                                }
                                return;
                            } else {
                                Store.dispatch('isPositionInformation', {
                                    callback: callbackToken3 => {
                                        Util.successToast('登录成功');
                                        console.log(callbackToken3, '3.0登录登录回调');
                                        if (!callbackToken3) {
                                            resolve(res.data);
                                        }
                                    },
                                    val: 'thirdLogin',
                                    loginFlag: type,
                                });
                            }
                        }
                    } else {
                        // 2.0首页透明遮罩
                        Store.commit('setMaskDialogFlag', false);
                        // 关闭毛玻璃
                        Store.commit('setFrostedglassIsLoading', false);
                        Store.state.isOfficialAccount = false;
                        Store.dispatch('isPositionInformation', { callback: () => {}, val: '' });
                        // 出现异常将中转页按钮换回官方的type类型为getPhoneNumber的按钮
                        this.$store.commit('transferAccountGrayBtnFlag', true);
                        reject(res);
                    }
                })
                .catch(err => {
                    Store.commit('setMaskDialogFlag', false);
                    Store.commit('setFrostedglassIsLoading', false);
                    Store.state.isOfficialAccount = false;
                    Store.dispatch('isPositionInformation', { callback: () => {}, val: '' });

                    // Util.showModal('登录失败', true)
                    reject(err);
                });
        });
    }
    _login() {
        return new Promise(resolve => {
            uni.login({
                success: res => {
                    resolve(res.code);
                },
            });
        });
    }
    checkSession() {
        const _this = this;
        return new Promise((resolve, reject) => {
            // 通过 wx.login 接口获得的用户登录态拥有一定的时效性 用户越久未使用小程序，用户登录态越有可能失效。
            uni.checkSession({
                success(res) {
                    resolve();
                },
                fail() {
                    // session_key 已经失效，需要重新执行登录流程
                    console.log('fail');
                    _this._login().then(code => {
                        _this.wxCode = code;
                        reject();
                    });
                },
            });
        });
    }
    checkLogin() {
        if (!Store.state.isLogin) {
            this.redirectToRegister();
            return false;
        } else {
            return true;
        }
    }

    saveUserInfo(res, callback) {
        // 03
        Store.state.token = res.data.token;
        Store.state.phone = res.data.phone;
        Store.state.accessToken = res.data.accessToken;
        // Store.state.userInfo = res.data
        // Store.state.mpUserInfo = res.data
        Store.state.registerLoginInformation = res.data;
        Store.state.openId = res.data.openid;
        Store.state.unionId = res.data.unionId;
        Store.state.newUserTag = res.data.newUserTag;
        Store.commit('setLoginStatus', true);
        // Store.commit('setUserInfo', res.data)
        // Store.commit('setMpUserInfo', res.data)
        if (Store.state.token3) {
            callback && callback(res);
        }
    }

    redirectTo(url) {
        // 手机号密码登录
        if (url.includes('?')) {
            url += '&';
        } else {
            url += '?';
        }
        Router.$root.push(`${url}url=${encodeURIComponent(Util.getCurrentPageUrlWithArgs())}`);
    }

    redirectToRegister() {
        uni.redirectTo({
            url: `/packages/transferAccount/pages/home/<USER>
        });
    }

    redirectToRegisterScanBuy() {
        uni.redirectTo({
            url: `/packages/scanbuy/pages/register?url=${encodeURIComponent(Util.getCurrentPageUrlWithArgs())}`,
        });
    }
}

export default new WxLogin();
