<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="我的电子券"></zj-navbar>
            <div class="padding-16 bg-F7F7FB f-1">
                <div class="content-wrap bg-fff border-rad-8">
                    <div class="content-item-wrap fl-column mh-0 p-LR-16">
                        <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
                            <div class="text-left font-14 weight-400 color-666">电子券号</div>
                            <div class="text-right font-14 weight-400 color-333">{{ useDetailObject.id }}</div>
                        </div>
                        <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
                            <div class="text-left font-14 weight-400 color-666">核销金额</div>
                            <div class="text-right font-14 weight-400 color-333">{{ useCouponData.checkAmount }}</div>
                        </div>
                        <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
                            <div class="text-left font-14 weight-400 color-666">订单编号</div>
                            <div class="text-right font-14 weight-400 color-333">{{ useCouponData.transactionNo || '' }} </div>
                        </div>
                        <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
                            <div class="text-left font-14 weight-400 color-666">站点名称</div>
                            <div class="text-right font-14 weight-400 color-333">{{ useCouponData.transactionSite || '' }} </div>
                        </div>
                        <!-- <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
            <div class="text-left font-14 weight-400 color-666">EPS流水号</div>
            <div class="text-right font-14 weight-400 color-333">{{useCouponData.epsno || ''}}</div>
          </div>-->
                        <div class="content-item fl-row mh-0 fl-jus-bet fl-al-cen">
                            <div class="text-left font-14 weight-400 color-666">交易时间</div>
                            <div class="text-right font-14 weight-400 color-333">{{ useCouponData.transactionDate }}</div>
                        </div>
                        <div class="border-dashed"></div>
                        <div class="bar-code-num fl-row mh-0 fl-al-jus-cen bg-F3F3F6 border-rad-8">
                            <div class="font-14 color-333 weight-400">条形码：&nbsp;&nbsp;&nbsp;</div>
                            <div class="font-14 color-333 weight-400">{{ checkCode }}</div>
                        </div>
                        <div class="bar-wrap">
                            <canvas canvas-id="barcode" id="barcode" class="img"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import wxcode from 'uniapp-qrcode';
import { couponDetail } from '../../../../s-kit/js/v3-http/https3/conpon/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 获取已使用电子券页面带过来的参数
            useDetailObject: {},
            // 生成条形码的code
            checkCode: '',
            bizTypetext: '',
            // 已使用详情数据
            useCouponData: {},
            refer: '',
        };
    },
    onLoad(options) {
        // 获取已使用电子券页面带过来的参数
        this.useDetailObject = JSON.parse(decodeURIComponent(options.data));
        if (this.useDetailObject.refer) {
            this.refer = this.useDetailObject.refer;
        }
        this.$sKit.mpBP.tracker('优惠券', {
            seed: 'couponBiz',
            pageID: 'userCoupon_detailPage',
            refer: this.refer,
            channelID: clientCode,
        });
    },
    mounted() {
        // 获取电子券已使用详情
        this.getUseDetail();
    },
    methods: {
        /**
         * @description  :  获取电子券已使用详情
         * @param         {*} id:电子券id
         * @param         {*} businessId:手机号
         * @param         {*} useCouponData:已使用详情数据
         * @param         {*} checkCode: 生成条形码的code
         * @return        {*}
         */
        async getUseDetail() {
            let params = {
                id: this.useDetailObject.id,
                businessId: this.useDetailObject.businessId,
            };
            let res = await couponDetail(params);
            if (res.success) {
                this.useCouponData = res.data;
                console.log(this.useCouponData, 'useCouponData');
                if (res.data.checkOrderNo) {
                    this.checkCode = res.data.checkOrderNo;
                    wxcode.barcode('barcode', this.checkCode, 476, 150);
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    .content-wrap {
        width: 100%;
        display: inline-block;

        .content-item-wrap {
            margin-top: 25px;

            .content-item {
                margin-bottom: 9px;

                .text-left {
                    line-height: 33.5px;
                }

                .text-right {
                    line-height: 24px;
                }
            }

            .border-dashed {
                margin-top: 12px;
            }

            .bar-code-num {
                width: 100%;
                height: 44px;
                margin-top: 16px;
                line-height: 20px;
            }

            .bar-wrap {
                width: 210px;
                height: 50px;
                margin: 0 auto;
                margin-top: 20px;
                margin-bottom: 20px;

                .img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
</style>
