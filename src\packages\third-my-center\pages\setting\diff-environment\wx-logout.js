import { unBindThirdUser } from '../../../../../s-kit/js/v3-http/https3/classInterest/index';
import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';

export default {
    // #ifdef MP-WEIXIN
    mounted() {},
    methods: {
        /**
         * @description     : 退出登录
         * @return        {*}
         */
        async logout() {
            let res = await logoutApi();
            if (res.success) {
                this.$store.commit('setMpUserInfo', null);
                this.$store.commit('setCarListV3', []);
                this.$store.state.token3 = '';
                this.$store.state.token = '';
                this.$store.state.location.officialAccountParams = '';
                this.$store.commit('setVenicleList', []);
                this.$store.commit('card/setCardList', []);
                this.$store.commit('setBannerListCity', []);
                this.$store.commit('setBannerListCountry', []);
                this.$store.state.location.moduleBannerListShow = false;
                this.$store.state.location.moduleBannerList = [];
                this.$store.commit('card/setIsHaveECard', false);
                this.$store.commit('card/setIsHaveEntityCard', false);
                this.$store.state.location.advertisementFlag = 0;
                this.$store.state.location.myPageAdvertisementFlag = 0;
                this.$store.commit('setSource', '');
                this.$store.commit('setLoginStatus', false);
                this.$store.commit('mSetPersonalInformation3', {});
                this.$store.commit('setUserInfo', {});
                this.$store.commit('setMpUserInfo', {});
                this.$store.commit('setLongTimeNotLogin', null);
                this.$store.commit('setCleanTheRegisteredAddress', true);
                uni.setStorageSync('tokenInfo', '');
                wx.clearStorageSync();
                uni.reLaunch({
                    url: '/pages/thirdHome/main',
                });
            }
        },
        /**
         * @description     : 查看协议点击事件
         * @return        {*}
         */
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('1', '微信小程序隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // 打开PDF
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
    },
    // #endif
};
