<template>
    <view class="uni-popup-dialog">
        <view class="uni-dialog-title">
            <text class="uni-dialog-title-text">{{ titleText }}</text>
        </view>
        <view v-if="mode === 'base'" class="uni-dialog-content">
            <slot>
                <text class="uni-dialog-content-text">{{ content }}</text>
            </slot>
        </view>
        <view v-else class="uni-dialog-content" style="flex-direction: column">
            <slot>
                <input class="uni-dialog-input" v-model="val" type="text" :placeholder="placeholderText" />
                <div class="uni-dialog-content-input-text" v-if="content">{{ content }}</div>
            </slot>
        </view>
        <view class="uni-dialog-button-group">
            <view class="uni-dialog-button" @click="closeDialog">
                <text class="uni-dialog-button-text uni-button-close-color">{{ closeText }}</text>
            </view>
            <view class="uni-dialog-button uni-border-left" @click="onOk">
                <text class="uni-dialog-button-text uni-button-color">{{ okText }}</text>
            </view>
        </view>
    </view>
</template>

<script>
import popup from '../uni-popup/popup.js';
import { initVueI18n } from '@dcloudio/uni-i18n';
import messages from '../uni-popup/i18n/index.js';
const { t } = initVueI18n(messages);
// import Vue from 'vue'
// Vue.directive('focus', {
//     inserted: function (el) {
//         Vue.nextTick(() => {
//             el.focus();
//         });
//     }
// });

/**
 * PopUp 弹出层-对话框样式
 * @description 弹出层-对话框样式
 * @tutorial https://ext.dcloud.net.cn/plugin?id=329
 * @property {String} value input 模式下的默认值
 * @property {String} placeholder input 模式下输入提示
 * @property {String} type = [success|warning|info|error] 主题样式
 *  @value success 成功
 * 	@value warning 提示
 * 	@value info 消息
 * 	@value error 错误
 * @property {String} mode = [base|input] 模式、
 * 	@value base 基础对话框
 * 	@value input 可输入对话框
 * @property {String} content 对话框内容
 * @property {Boolean} beforeClose 是否拦截取消事件
 * @event {Function} confirm 点击确认按钮触发
 * @event {Function} close 点击取消按钮触发
 */

export default {
    name: 'uniPopupDialog',
    mixins: [popup],
    emits: ['confirm', 'close'],
    props: {
        value: {
            type: [String, Number],
            default: '',
        },
        placeholder: {
            type: [String, Number],
            default: '',
        },
        type: {
            type: String,
            default: 'error',
        },
        mode: {
            type: String,
            default: 'base',
        },
        title: {
            type: String,
            default: '',
        },
        content: {
            type: String,
            default: '',
        },
        beforeClose: {
            type: Boolean,
            default: false,
        },
        cancelText: {
            type: String,
            default: '',
        },
        confirmText: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            dialogType: 'error',
            focus: false,
            val: '',
        };
    },
    computed: {
        okText() {
            return this.confirmText || t('uni-popup.ok');
        },
        closeText() {
            return this.cancelText || t('uni-popup.cancel');
        },
        placeholderText() {
            return this.placeholder || t('uni-popup.placeholder');
        },
        titleText() {
            return this.title || t('uni-popup.title');
        },
    },
    watch: {
        type(val) {
            this.dialogType = val;
        },
        mode(val) {
            if (val === 'input') {
                this.dialogType = 'info';
            }
        },
        value(val) {
            this.val = val;
        },
    },
    created() {
        // 对话框遮罩不可点击
        this.popup.disableMask();
        // this.popup.closeMask()
        if (this.mode === 'input') {
            this.dialogType = 'info';
            this.val = this.value;
        } else {
            this.dialogType = this.type;
        }
    },
    mounted() {
        this.focus = true;
    },
    methods: {
        /**
         * 点击确认按钮
         */
        onOk() {
            if (this.mode === 'input') {
                this.$emit('confirm', this.val);
                this.val = '';
            } else {
                this.$emit('confirm');
                this.val = '';
            }
            if (this.beforeClose) return;
            this.popup.close();
        },
        /**
         * 点击取消按钮
         */
        closeDialog() {
            this.val = '';
            if (this.beforeClose) return;
            this.$parent.cardPassword = '';
            this.popup.close();
        },
        close() {
            this.popup.close();
        },
    },
};
</script>

<style lang="scss">
$uni-primary: #007aff !default;
$uni-success: #4cd964 !default;
$uni-warning: #f0ad4e !default;
$uni-error: #dd524d !default;

.uni-popup-dialog {
    width: 300px;
    border-radius: 11px;
    background-color: #fff;
}

.uni-dialog-title {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    justify-content: center;
    padding-top: 25px;
}

.uni-dialog-title-text {
    font-size: 16px;
    color: #000;
}

.uni-dialog-content {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.uni-dialog-content-text {
    font-size: 14px;
    color: #6c6c6c;
}

.uni-dialog-content-input-text {
    font-size: 12px;
    color: #6c6c6c;
    margin-top: 10px;
    width: 100%;
}

.uni-dialog-button-group {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    border-top-color: #f5f5f5;
    border-top-style: solid;
    border-top-width: 1px;
}

.uni-dialog-button {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */

    flex: 1;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 45px;
}

.uni-border-left {
    border-left-color: #f0f0f0;
    border-left-style: solid;
    border-left-width: 1px;
}

.uni-dialog-button-text {
    font-size: 16px;
}
.uni-button-close-color {
    color: #666;
}

.uni-button-color {
    color: #333;
}

.uni-dialog-input {
    font-size: 24rpx;
    width: 100%;
    height: 60rpx;
    border-radius: 8rpx;
    padding-left: 30rpx;
    border: 2rpx solid #dedddd;
}

.uni-popup__success {
    color: $uni-success;
}

.uni-popup__warn {
    color: $uni-warning;
}

.uni-popup__error {
    color: $uni-error;
}

.uni-popup__info {
    color: #909399;
}
</style>
