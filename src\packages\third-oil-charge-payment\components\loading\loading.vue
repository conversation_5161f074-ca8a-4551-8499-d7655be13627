<template>
    <div class="view">
        <div class="google-loader2">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <div class="refueling weight-bold">加油服务中</div>
        <div class="google-loader">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
</template>
<script>
export default {
    name: 'HelloWorld',
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {},
    components: {},
};
</script>
<style scoped>
.view {
    display: flex;
    width: 100%;
    height: 30px;
    margin-top: 25px;
    margin-bottom: 16px;
    justify-content: center;
    align-items: center;
}
.google-loader {
    display: block;
}
.google-loader span {
    display: inline-block;
    margin-top: 10px;
    height: 4px;
    width: 4px;
    border-radius: 50%;
}
.google-loader span:not(:first-child) {
    margin-left: 5px;
}
.google-loader span:nth-child(1) {
    background: #e64a1d;
    animation: move 1s ease-in-out -0.75s infinite alternate;
}
.google-loader span:nth-child(2) {
    background: #e64a1d;
    animation: move 1s ease-in-out -0.5s infinite alternate;
}
.google-loader span:nth-child(3) {
    background: #e64a1d;
    animation: move 1s ease-in-out -0.25s infinite alternate;
}

@keyframes move {
    from {
        transform: translateY(-10px);
    }
    to {
        transform: translateY(5px);
    }
}

.google-loader2 {
    display: block;
}
.google-loader2 span {
    display: inline-block;
    margin-top: 10px;
    height: 4px;
    width: 4px;
    border-radius: 50%;
}
.google-loader2 span:not(:first-child) {
    margin-left: 5px;
}
.google-loader2 span:nth-child(1) {
    background: #e64a1d;
    animation: move 1s ease-in-out -1.25s infinite alternate;
}
.google-loader2 span:nth-child(2) {
    background: #e64a1d;
    animation: move 1s ease-in-out -1s infinite alternate;
}
.google-loader2 span:nth-child(3) {
    background: #e64a1d;
    animation: move 1s ease-in-out -0.75s infinite alternate;
}
.refueling {
    width: 115px;
    height: 29px;
    font-size: 21px;
    color: #e64a1d;
    margin-left: 30px;
    margin-right: 30px;
}
@keyframes move {
    from {
        transform: translateY(-10px);
    }
    to {
        transform: translateY(5px);
    }
}

html,
body {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
