<template>
    <div class="goods-item" @click="goodsClick(goodsItem)">
        <div class="goods-image-wrap">
            <img class="goods-image" :src="goodsItem.pluPic" alt="" />
        </div>
        <div class="goods-info">
            <div class="goods-name">{{ goodsItem.pluNickname || '' }}</div>
            <!-- <div class="goods-name-sub">{{ goodsItem.discountTitle }}</div> -->
            <!-- <div class="goods-tag" v-if="goodsItem.tag">
                {{ goodsItem.tag }}
            </div> -->
            <!--<div class="goods-price">-->
                <!--到手价&yen;<my-price :price="goodsItem.discPrice || 0"></my-price>-->
                <!--<my-price :price="goodsItem.salPrice" isDet intFont="20rpx" color="#999999" v-if="goodsItem.salPrice"></my-price>-->
            <!--</div>-->
            <div class="goods-price">
                到手价&yen;<my-price :price="goodsItem.showPrice && goodsItem.showPrice != goodsItem.salPrice ? goodsItem.showPrice : goodsItem.salPrice"></my-price>
                <my-price :price="goodsItem.salPrice" isDet intFont="20rpx" color="#999999" v-if="goodsItem.salPrice"></my-price>
            </div>
            <div class="goods-comment-icon" @click.stop="noGoodsClick">
                <!-- <div class="goods-comment">好评率 98%</div> -->
                <number-box
                    :value="Number(goodsItem.count)"
                    :index="goodsItem.orgArrIndex"
                    :min="0"
                    :step="Number(goodsItem.sumCount)"
                    disabled-input
                    input-width="50"
                    input-height="50"
                    bg-color="#fff"
                    color="#333"
                    size="22"
                    :saleCount="Number(goodsItem.onSaleCount)"
                    :availableNum="Number(goodsItem.availableNum)"
                    :curbSalesCount="Number(goodsItem.curbSalesCount)"
                    @change="goodsCountChange"
                ></number-box>
            </div>
        </div>
    </div>
</template>

<script>
/**
 * 瀑布流商品卡片
 */
import numberBox from '../number-box/number-box.vue';
import myPrice from '../price/index.vue';

export default {
    props: {
        // 商品信息对象
        goodsItem: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {};
    },
    computed: {},
    components: {
        myPrice,
        numberBox,
    },
    watch: {
        goodsItem: {
            handler: function (v1, v2) {
                console.log('goodsItem值改变了', v1, v2);
            },
            deep: true,
        },
    },
    mounted() {
        console.log(this.goodsItem, 'goodsItem');
    },
    methods: {
        //热门商品点击事件
        async goodsClick(goodsItem) {
            this.$emit('goodsClick', goodsItem);
        },
        goodsCountChange(goodObj) {
            console.log(this.goodsItem, '商品');
            this.$emit('goodsCountChange', goodObj);
        },
        noGoodsClick() {},
    },
};
</script>

<style lang="scss" scoped>
.goods-item {
    // margin: 0 8rpx 10rpx;
    margin-bottom: 16rpx;
    position: relative;
    background: #fff;
    border-radius: 16rpx;
    padding-bottom: 13px;
    overflow: hidden;
    .goods-image-wrap {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        position: relative;
        .goods-image {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }
    }
    .goods-info {
        padding: 0 20rpx;
        .goods-name {
            margin: 12px 0;
            font-size: 13px;
            color: #333333;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .goods-name-sub {
            font-size: 12px;
            color: #999;
        }
        .goods-tag {
            height: 16px;
            background: #fff7f5;
            border-radius: 4px;
            border: 1px solid #ff4000;
            line-height: 1.5;
            padding: 0 4px;
            display: inline-block;
            min-width: 40px;
            font-size: 10px;
            font-weight: 400;
            color: #ff4000;
            margin: 8px 0 9px 0;
        }
        .goods-price {
            display: flex;
            align-items: baseline;
            font-size: 12px;
            font-weight: 500;
            color: #ff4000;
            padding: 10rpx 0;
        }
        .goods-comment-icon {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .goods-comment {
                flex: 1;
                font-size: 12px;
                color: #999;
            }
        }
    }
}
</style>
