<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="p-bf fl-column" style="background: #f7f7fb">
            <zj-navbar :border-bottom="false" title="开具发票"></zj-navbar>
            <div class="content" v-if="!isSuccess">
                <div class="formPage">
                    <div class="formBox">
                        <div class="formBoxTop">
                            <div class="title">发票详情</div>
                            <div class="formItem">
                                <div class="itemLeft"> <div class="dot">*</div>抬头名称 </div>
                                <div class="itemRight fl-jus-bet" @click="addMore">
                                    <div class="nameItemText">{{ formData.buyerName }}</div>
                                    <div class="nameItemBtn" @click="addMore">更多/添加</div>
                                </div>
                            </div>
                            <div class="formItem">
                                <div class="itemLeft">抬头类型</div>
                                <div class="itemRight">
                                    <div
                                        class="nameItemText"
                                        v-if="formData.buyerNature == 1 || formData.buyerNature == 2 || formData.buyerNature == 3"
                                        >非自然人</div
                                    >
                                    <div class="nameItemText" v-if="formData.buyerNature == 4">自然人</div>
                                </div>
                            </div>
                            <div class="formItem">
                                <div class="itemLeft">税号</div>
                                <div class="itemRight" @click="addMore">
                                    <div class="nameItemText" style="color: #999999">{{ formData.buyerTaxId || '' }}</div>
                                </div>
                            </div>
                            <div class="formItem" style="border-bottom: 0" v-if="type != 'invoice2'">
                                <div class="itemLeft" :class="{ btnDisabled: remarkDis }">
                                    <img v-if="selectedAll" src="../../image/type-checked.png" alt="" @click="addRemarks('unAll')" />
                                    <img
                                        v-else
                                        src="../../image/type-unchecked.png"
                                        alt=""
                                        @click="addRemarks('all')"
                                    />选择发票备注内容打印信息<div class="leftTip"> (可多填) </div>
                                </div>
                                <div class="itemRight viewExamples" :class="{ btnDisabled: remarkDis }" @click="viewExamples()">
                                    查看示例
                                </div>
                                <div :class="{ btnCol: orderType != 'charge', chargeBtnCol: orderType == 'charge' }">
                                    <div
                                        class="remarkBtn"
                                        :class="{ btnSelect: item.select, btnDisabled: remarkDis, chargeBtn: orderType == 'charge' }"
                                        v-for="(item, index) in remarkBtnList"
                                        :key="index"
                                        @click="addRemarks(item.name)"
                                        >{{ item.value }}
                                        <img v-if="item.select" class="deleteIcon" src="../../image/deleteIcon.png" alt="" />
                                    </div>
                                </div>
                                <div class="remarkBox" @click="focusOpen">
                                    <div class="top">
                                        <!-- <div class="tip" v-if="!quickRemarksText && !formData.remark"></div> -->
                                        <div class="quickRemarks" v-if="quickRemarksText"> {{ quickRemarksText }} </div>
                                        <textarea
                                            ref="textarea"
                                            :disabled="type == 'invoice2'"
                                            class="inputItem"
                                            disable-default-padding
                                            :placeholder="quickRemarksText ? '还可以输入更多备注' : '备注：该内容将会打印到发票上'"
                                            placeholder-class="invoice-form-placeholder"
                                            v-model="formData.remark"
                                            @input="textareaInput"
                                            @focus="textareaFocus"
                                            auto-height
                                            fixed
                                            :enableNative="false"
                                            :show-count="false"
                                            :auto-focus="autofocus"
                                            :focus="focus"
                                            :maxlength="remarkMaxlength"
                                        ></textarea>
                                        <div class="clearRemark">
                                            <div class="maxlength">{{ GBKLength <= 110 ? GBKLength : '110' }}/110</div>
                                            <div class="imgBtn" @click="deleteAll">
                                                <img src="../../image/delete.png" alt="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="formItem">
                                <div class="itemLeft">总金额</div>
                                <div class="itemRight itemAmount"
                                    >{{ formData.checkAllAmount || formData.taxInclusiveTotalAmount || 0 }}元
                                </div>
                            </div>
                            <div class="tips">
                                <div class="tipsTitle">温馨提示：</div>
                                <div class="tipsText">
                                    电子发票的销售方名称与您的昆仑加油卡归属地或交易消费加油站主体一致;
                                    <br />
                                    <div>可能开具多张发票</div>，多张开票金额总和为所选订单总金额；提交后预计在5分钟内可完成发票开具；
                                    <br />所选订单第一次开票后 <div>仅能重开一次</div>， <br />请确认信息无误后提交。
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- #ifdef MP-WEIXIN || H5-->
                    <div class="formBox2" @click="goAuthorize">
                        <div class="formBox2_item formBox2_left">
                            <img src="../../image/wxIcon.png" alt="" />
                            <div class="formBox2_left_text">微信卡包</div>
                        </div>
                        <div class="formBox2_item formBox2_right">
                            <div class="formBox2_right_text" :class="{ yellowColor: authorizeStatus }">{{
                                authorizeStatus ? '已授权' : '去授权'
                            }}</div>
                            <img src="../../image/right.png" alt="" />
                        </div>
                    </div>
                    <div class="cardPackTips" @click="cardPackTips">
                        <div class="tipsText tipsLeft">仅部分地区自营加油站发票可同步至微信卡包</div>
                        <div class="tipsText tipsRight">点击查看<div class="right"></div></div>
                    </div>
                    <!-- #endif -->
                </div>
                <div class="btnBox">
                    <div class="btn" @click="submit">提交</div>
                </div>
            </div>
            <invoiceSuccess :type="type" :refer="refer" :orderType="orderType" v-else></invoiceSuccess>
        </div>
        <ZjPopup ref="titlePopup" :maskClick="true" @maskClick="maskTitleClose" type="bottom">
            <div class="isScreen">
                <div class="closeImgWrap">
                    <div class="closeBox" @click="closeTitlePopup">
                        <img src="../../image/black-close.png" alt />
                    </div>
                </div>
                <div v-for="(item, index) in titleList" :key="index" class="dete-item" @click="handelTitleActive(index, item)">
                    <div class="left">
                        <div class="name">{{ item.buyerName }}</div>
                        <div class="id" v-if="item.buyerTaxId">{{ item.buyerTaxId }}</div>
                    </div>
                    <radio color="#e64f22" :checked="formData.headerId == item.headerId" :name="item.id" style="transform: scale(0.65)" />
                </div>
                <div class="btn" @click="addInvoiceTitle">添加发票抬头</div>
            </div>
        </ZjPopup>
        <ZjPopup ref="submitPopup" :maskClick="true" @maskClick="maskSubmitClose" type="bottom">
            <div class="isScreen">
                <div class="closeImgWrap">
                    <div class="closeBox" @click="closeSubmitPopup">
                        <img src="../../image/black-close.png" alt />
                    </div>
                </div>
                <div class="submit-dete-item">
                    <div class="left">发票类型</div>
                    <input class="right" v-model="invoiceType" disabled />
                </div>
                <div class="submit-dete-item">
                    <div class="left">抬头名称</div>
                    <input class="right" v-model="formData.buyerName" disabled />
                </div>
                <div class="submitTips">请确认发票抬头无误，电子发票开具成功后，您可以在“开票历史”中查看和下载。</div>
                <div class="btn" @click="submitAgain">确认提交</div>
            </div>
        </ZjPopup>
        <ZjPopup ref="subMessagePopup" :maskClick="false" type="bottom">
            <div class="isScreen">
                <img class="subMessageImg" mode="widthFix" src="../../image/subMessagePopupImg.png" alt="" />
                <div class="btn" @click="subMessagePopupSubmit">我知道了</div>
            </div>
        </ZjPopup>
        <zj-show-modal></zj-show-modal>
        <zj-unrealized-authentication
            v-if="realNameDialogFlag"
            @realNameDialogClose="realNameDialogClose"
            @realNameInfo="realNameInfo"
        ></zj-unrealized-authentication>
        <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
    </div>
</template>

<script>
import {
    chargeInvoiceMakeApi,
    invoiceMakeApi,
    invoiceTitleListApi,
    invoiceRedFlushApi,
    reverseInvoiceAsync,
    invoiceTitleDetailApi,
} from '../../../../s-kit/js/v3-http/https3/invoice/index';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import invoiceSuccess from '../../components/invoice-success/main.vue';
import { plateList } from '../../../../s-kit/js/v3-http/https3/vehicle/index';
import zfbInvoiceForm from './diff-environment/zfb-invoice-form';
import wxInvoiceForm from './diff-environment/wx-invoice-form';
import appInvoiceForm from './diff-environment/app-invoice-form';
import { detailList } from '../../../../s-kit/js/v3-http/https3/order/index';
import post from '../../../../s-kit/js/v3-http/post';
import { clientCode, appId } from '../../../../../project.config';
import { mapGetters, mapState } from 'vuex';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    name: 'invoiceForm',
    components: { ZjPopup, invoiceSuccess },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appInvoiceForm,
        // #endif
        // #ifndef MP-MPAAS
        wxInvoiceForm,
        zfbInvoiceForm,
        // #endif
    ],
    data() {
        return {
            formData: {
                // 抬头类型
                buyerNature: null,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 抬头id
                headerId: '',
                remark: '',
                checkAllAmount: '',
                orderNoList: [],
            },
            titleList: [],
            isSuccess: false,
            type: '',
            orderType: '',
            invoiceType: '电子发票',
            titleDetail: {},
            remarkBtnList: [
                { value: '车牌号', name: 'car', select: false, content: '' },
                { value: '交易时间', name: 'time', select: false, content: '' },
                { value: '油站名称', name: 'station', select: false, content: '' },
                { value: '商品名称', name: 'goods', select: false, content: '' },
            ],
            remarkDis: false,
            autofocus: false,
            focus: false,
            quickRemarksText: '',
            GBKLength: 0,
            oldRemark: '',
            remarkMaxlength: -1,
            realNameDialogFlag: false,
            refer: '',
            authorizeStatus: 0,
            authorizeOpenId: '',
        };
    },
    async onLoad(option) {
        this.submitAgain = this.$sKit.commonUtil.throttleUtil(this.submitAgain, 3000);
        // 接收上个页面的传参
        let params = JSON.parse(decodeURIComponent(option.data));
        //
        this.type = params.type;
        this.orderType = params.orderType || '';
        if (this.orderType == 'charge') {
            this.remarkBtnList = [
                { value: '车牌号', name: 'car', select: false, content: '' },
                { value: '交易时间', name: 'time', select: false, content: '' },
                { value: '充电站名称', name: 'station', select: false, content: '' },
            ];
        }
        if (this.type == 'invoice') {
            // 开票
            this.formData.checkAllAmount = params.checkAllAmount;
            this.formData.orderNoList = params.orderNoList;
            this.remarkBtnList.forEach(async item => {
                if (item.name == 'car') {
                    item.content = await this.getPlateList();
                }
                if (item.name == 'time') {
                    item.content = params.createTime;
                }
                if (item.name == 'station') {
                    item.content = params.orgName;
                }
                if (item.name == 'goods') {
                    item.content = params.goods;
                }
            });
        } else if (this.type == 'change') {
            // 换开
            this.formData.invoiceCode = params.invoiceInfo.invoiceCode;
            this.formData.invoiceTypeCode = params.invoiceInfo.invoiceTypeCode;
            this.formData.invoiceNo = params.invoiceInfo.invoiceNo;
            this.formData.orgCode = params.invoiceInfo.orgCode;
            this.formData.issueDate = params.invoiceInfo.issueDate;
            this.formData.taxInclusiveTotalAmount = params.invoiceInfo.taxInclusiveTotalAmount;
            this.formData.orderNoList = params.invoiceInfo.orderNumList;
        } else if (this.type == 'invoice2') {
            this.formData.checkAllAmount = params.checkAllAmount;
            this.formData.orderNoList = params.orderNoList;
            this.formData.cardNo = params.cardNo;
        }

        if (this.type == 'invoice2' || this.formData.orderNoList.length > 1) {
            this.remarkDis = true;
        } else if (this.type == 'change' && this.formData.orderNoList.length == 1) {
            this.orderDetailPost();
        }
        if (params.refer) {
            this.refer = params.refer;
        }
        if (this.type == 'change') {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'hk-kjinvoicePage',
                refer: this.refer,
                channelID: clientCode,
            });
        } else {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'kjinvoicePage',
                refer: this.refer,
                channelID: clientCode,
            });
        }
    },
    onShow() {
        // 获取发票抬头列表
        this.getInvoiceTitleListApi();
    },
    watch: {
        // 监视备注输入框，输入内容后不能选择
        remarkBtnList: {
            handler(newValue) {
                let text = '';
                this.remarkBtnList.forEach(item => {
                    if (item.select) {
                        if (item.content) {
                            text = text.concat(item.content + ' ');
                        }
                    }
                });
                let gbkNum = this.getStrLeng(text + this.formData.remark, false);
                if (gbkNum < 220) {
                    this.quickRemarksText = text;
                    this.remarkMaxlength = -1;
                } else if (gbkNum == 220) {
                    this.quickRemarksText = text;
                    this.remarkMaxlength = this.formData.remark.length;
                } else {
                    let quickGbkNum = this.getStrLeng(text, false);
                    this.quickRemarksText = this.stringTrim(text, quickGbkNum - (gbkNum - 220));
                    this.remarkMaxlength = this.formData.remark.length;
                }
                this.getStrLeng(this.quickRemarksText + this.formData.remark);
            },
            deep: true,
        },
        isSuccess: {
            handler(newValue) {
                if (newValue) {
                    if (this.type == 'change') {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'hk-invoiceSuccessPage',
                            refer: this.refer,
                            channelID: clientCode,
                        });
                    } else {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'invoiceSuccessPage',
                            refer: this.refer,
                            channelID: clientCode,
                        });
                    }
                }
            },
        },
    },
    computed: {
        selectedAll: {
            get() {
                let num = 0;
                this.remarkBtnList.forEach(item => {
                    if (item.select) num += 1;
                });
                return num == this.remarkBtnList.length;
            },
        },
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        /**
         * @description  : 关闭实人认证弹窗
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameDialogClose() {
            this.realNameDialogFlag = false;
        },
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.faceVerificaton();
        },
        focusOpen() {
            this.autofocus = true;
            this.focus = true;
            this.$refs.textarea.focus();
        },
        textareaFocus() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'notesInput',
                refer: this.refer,
                channelID: clientCode,
            });
        },
        textareaInput(e) {
            let GBKNum = this.getStrLeng(this.quickRemarksText + e.detail.value);
            if (GBKNum < 220) {
                console.log('可以输入', GBKNum);
                this.remarkMaxlength = -1;
                // this.formData.remark = e.detail.value;
                this.oldRemark = e.detail.value;
                return;
            }
            if (GBKNum == 220) {
                console.log('正好到上限', GBKNum);
                // this.formData.remark = e.detail.value;
                this.oldRemark = e.detail.value;
                this.remarkMaxlength = this.oldRemark.length;
                return;
            }
            if (GBKNum > 220) {
                console.log('不可以输入', GBKNum, this.oldRemark);
                this.remarkMaxlength = this.oldRemark.length;
                setTimeout(() => {
                    this.formData.remark = this.oldRemark;
                }, 300);
                return;
            }
        },
        stringTrim(str, extraLength) {
            var realLength = 0;
            var len = str.length;
            var charCode = -1;
            let resultString = '';
            for (var i = 0; i < len; i++) {
                charCode = str.charCodeAt(i);
                if (charCode >= 0 && charCode <= 128) {
                    realLength += 1;
                } else {
                    // 如果是中文则长度加2
                    realLength += 2;
                }
                if (realLength >= extraLength) {
                    resultString = str.substring(0, i);
                    break;
                }
            }
            return resultString;
        },
        getStrLeng(str, isCalculate = false) {
            var realLength = 0;
            var len = str.length;
            var charCode = -1;
            for (var i = 0; i < len; i++) {
                charCode = str.charCodeAt(i);
                if (charCode >= 0 && charCode <= 128) {
                    realLength += 1;
                } else {
                    // 如果是中文则长度加2
                    realLength += 2;
                }
            }
            this.GBKLength = Math.ceil(realLength / 2);
            console.log(this.GBKLength, '长度', realLength, '字节数');
            return realLength;
        },
        /**
         * @description  : 获取订单列表
         * @return        {*}
         */
        async orderDetailPost() {
            let params = {
                orderNum: this.formData.orderNoList,
            };
            let res = await detailList(params, { isCustomErr: true });
            if (res && res.success) {
                if (res.data.rows.length > 0) {
                    this.remarkBtnList.forEach(async item => {
                        if (item.name == 'car') {
                            item.content = await this.getPlateList();
                        }
                        if (item.name == 'time') {
                            item.content = res.data.rows[0].createTime;
                        }
                        if (item.name == 'station') {
                            item.content = res.data.rows[0].stationName;
                        }
                        if (item.name == 'goods') {
                            item.content = res.data.rows[0].orderItems
                                .map(goodsItem => {
                                    return goodsItem.productName;
                                })
                                .join(',');
                        }
                    });
                    this.remarkDis = false;
                } else {
                    this.remarkDis = true;
                }
            } else {
                this.remarkDis = true;
            }
        },
        /**
         * @description  : 清空备注内容
         * @return        {*}
         */
        deleteAll() {
            this.formData.remark = '';
            this.addRemarks('unAll');
        },
        /**
         * @description  : 快捷备注
         * @param         {*} name: 备注类型
         * @return        {*}
         */
        addRemarks(name) {
            if (this.remarkDis) return;
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'quickRemarksBu',
                refer: this.refer,
                channelID: clientCode,
            });
            this.remarkBtnList = this.remarkBtnList.map(item => {
                let newItem = JSON.parse(JSON.stringify(item));
                if (newItem.name == name) {
                    newItem.select = !newItem.select;
                }
                if (name == 'all') {
                    newItem.select = true;
                }
                if (name == 'unAll') {
                    newItem.select = false;
                }
                return newItem;
            });
        },
        /**
         * @description  : 获取用户默认车辆
         * @return        {*}
         */
        async getPlateList() {
            return new Promise(async (resolve, reject) => {
                let res = await plateList();
                if (res && res.success) {
                    let vehicleList = res.data.vehicleList || [];
                    let vehicle = vehicleList.find(item => {
                        return item.defaultVehicle;
                    });
                    let str = '';
                    if (vehicle) {
                        str = vehicle.licensePlate + '';
                    } else if (vehicleList.length > 0) {
                        str = vehicleList[0].licensePlate + '';
                    }
                    resolve(str);
                }
            });
        },
        /**
         * @description  : 获取发票抬头列表
         * @return        {*}
         */
        async getInvoiceTitleListApi() {
            let res = await invoiceTitleListApi();
            if (res && res.success) {
                if (res.data.length > 0) {
                    this.titleList = res.data;
                    // 开票时将默认抬头信息填入表单
                    let defaultTitle = this.titleList.find(item => item.defaultCount)
                        ? this.titleList.find(item => item.defaultCount)
                        : this.titleList[0];
                    this.formData.buyerName = defaultTitle.buyerName;
                    this.formData.buyerTaxId = defaultTitle.buyerTaxId;
                    this.formData.headerId = defaultTitle.headerId;
                    this.loadDetail();
                }
            }
        },
        /**
         * @description  : 通过获取发票详情，获取发票类型
         * @return        {*}
         */
        async loadDetail() {
            let res = await invoiceTitleDetailApi({ headerId: this.formData.headerId });
            if (res && res.success) {
                this.formData.buyerNature = res.data.buyerNature;
                if (this.type == 'invoice2') {
                    this.titleDetail = res.data;
                }
            }
        },
        /**
         * @description  : 点击遮罩关闭弹窗事件
         * @return        {*}
         */
        maskTitleClose() {},
        /**
         * @description  : 点击遮罩关闭弹窗事件
         * @return        {*}
         */
        maskSubmitClose() {
            if (this.type == 'change') {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'hk-closeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'closeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
        },
        /**
         * @description  : 开启弹窗
         * @return        {*}
         */
        addMore() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'replaceHeadBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$refs.titlePopup.open();
        },
        /**
         * @description  : 关闭弹窗
         * @return        {*}
         */
        closeTitlePopup() {
            this.$refs.titlePopup.close();
        },
        /**
         * @description  : 关闭弹窗
         * @return        {*}
         */
        closeSubmitPopup() {
            if (this.type == 'change') {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'hk-closeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'closeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            this.$refs.submitPopup.close();
        },
        /**
         * @description  : 抬头列表，抬头信息点击事件
         * @param         {*} index:序号
         * @param         {*} item:发票抬头数据
         * @return        {*}
         */
        handelTitleActive(index, item) {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'chooseHeadBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.formData.buyerNature = item.buyerNature;
            this.formData.buyerName = item.buyerName;
            this.formData.buyerTaxId = item.buyerTaxId;
            this.formData.headerId = item.headerId;
            this.loadDetail();
            this.closeTitlePopup();
        },
        /**
         * @description  : 提交按钮点击事件
         * @return        {*}
         */
        submit() {
            if (this.formData.headerId) {
                if (this.type == 'change') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'hk-submitBut',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                } else {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'submitBut',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                this.$refs.submitPopup.open();
            } else {
                this.$sKit.layer.showToast({ title: '请选择抬头' });
            }
        },
        /**
         * @description  : 确认提交点击事件
         * @return        {*}
         */
        submitAgain() {
            if (this.type == 'change') {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'hk-confirmSubmitBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'confirmSubmitBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            if (this.type == 'invoice') {
                this.invoiceMake();
            } else if (this.type == 'change') {
                this.invoiceRedFlushPost();
            } else if (this.type == 'invoice2') {
                this.invoiceMake2();
            }
        },
        faceVerificaton() {
            this.$sKit.commonUtil.nextOilTriggerRisk().then(
                res => {
                    this.invoiceMake(true);
                },
                () => {},
            );
        },
        /**
         * @description  : 3.0开票接口
         * @return        {*}
         */
        async invoiceMake(isAuth = false) {
            let params = {
                orderNoList: this.formData.orderNoList,
                headerId: this.formData.headerId,
                remark: this.quickRemarksText + this.formData.remark,
            };
            // #ifdef MP-WEIXIN
            if (this.authorizeStatus) {
                params.uId = this.authorizeOpenId;
                let openId = uni.getStorageSync('tokenInfo').openId || '';
                params.outUserId = `${appId}@${openId}`;
            }
            // #endif
            if (isAuth) {
                params.authStatus = 1;
                params.authTime = new Date().getTime();
            }
            let postFun = () => {};
            let url = '';
            if (this.orderType == 'charge') {
                postFun = chargeInvoiceMakeApi;
                url = 'charge.invoice.make';
            } else {
                postFun = invoiceMakeApi;
                url = 'invoice.make';
            }
            let res = await postFun(params, { isCustomErr: true, isAuth: isAuth });
            this.$refs.submitPopup.close();
            if (res && res.success) {
                this.isSuccess = true;
            } else {
                this.invoiceRiskControl(res, isAuth, url);
            }
        },
        invoiceRiskControl(res, isAuth, url) {
            if (res.errorCode == 'B_B20_300002') {
                this.$sKit.commonUtil.oilTriggerRisk().then(res => {
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (res == 13) {
                        // 打开实人认证的表单弹窗
                        this.realNameDialogFlag = true;
                    }
                });
            } else {
                post.showError({
                    result: res,
                    config: {
                        isCustomErr: false,
                        isload: true,
                        isEncode: false,
                        apiVersion: 'v1',
                        isAuth: isAuth,
                        handleErrorFn: () => {},
                    },
                    url: url,
                });
            }
        },
        /**
         * @description  : 发票换开
         * @return        {*}
         */
        async invoiceRedFlushPost() {
            let params = {
                invoiceCode: this.formData.invoiceCode || '',
                invoiceTypeCode: this.formData.invoiceTypeCode,
                invoiceNo: this.formData.invoiceNo || '',
                issueDate: this.formData.issueDate || '',
                headerId: this.formData.headerId || '',
                // 3.0.4新增字段
                orgCode: this.formData.orgCode || '',
                // 3.1.3新增字段
                remark: this.quickRemarksText + this.formData.remark,
            };
            let res = await invoiceRedFlushApi(params);
            if (res && res.success) {
                this.$refs.submitPopup.close();
                this.isSuccess = true;
            }
        },
        /**
         * @description  : 添加发票抬头逻辑跳转
         * @return        {*}
         */
        addInvoiceTitle() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'chooseHeadBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let url = '/packages/third-invoice/pages/invoice-title-list/main';
            let params = {};
            this.$sKit.layer.useRouter(url, params);
        },
        // checkItemChange(value) {
        //   this.formData.buyerNature = value
        // }
        /**
         * @description  : 查看发票示例
         * @return        {*}
         */
        viewExamples() {
            let url = '/packages/third-invoice/pages/invoice-example/main';
            let params = {};
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    width: 100%;
    flex: 1;
    background: #f7f7fb;

    .formPage {
        padding: 32rpx 32rpx calc(185rpx + env(safe-area-inset-bottom));

        .formBox {
            border-radius: 16rpx;
            margin: 0 auto;
            overflow: hidden;

            .formBoxTop {
                background: #ffffff;
                padding: 30rpx 24rpx 32rpx 32rpx;

                .title {
                    font-size: 32rpx;
                    font-weight: 400;
                    color: #000000;
                    line-height: 45rpx;
                    margin-bottom: 20rpx;
                }

                .formItem {
                    display: flex;
                    border-bottom: 1rpx solid #efeff4;
                    padding: 24rpx 0;
                    min-height: 88rpx;
                    flex-wrap: wrap;

                    .itemLeft {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 40rpx;
                        min-width: 167rpx;
                        display: flex;
                        align-items: center;

                        img {
                            width: 38rpx;
                            height: 39rpx;
                            margin-right: 16rpx;
                        }

                        .dot {
                            color: #e64f22;
                        }

                        .leftTip {
                            color: #999999;
                            font-size: 24rpx;
                        }
                    }

                    .itemRight {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        .checkBox {
                            display: flex;
                            align-items: center;
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 40rpx;

                            &:nth-of-type(1) {
                                margin-right: 64rpx;
                            }

                            img {
                                width: 40rpx;
                                height: 40rpx;
                                margin-right: 16rpx;
                            }
                        }

                        .nameItemText {
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 40rpx;
                            width: 70%;
                            word-wrap: break-word;
                            word-break: break-all;
                        }

                        .nameItemBtn {
                            min-width: 114rpx;
                            height: 42rpx;
                            background: #ffffff;
                            border-radius: 8rpx;
                            border: 1rpx solid #e64f22;
                            font-size: 20rpx;
                            font-weight: 400;
                            color: #e64f22;
                            line-height: 42rpx;
                            text-align: center;
                        }
                    }
                    .viewExamples {
                        font-size: 24rpx;
                        color: #e64f22;
                        justify-content: flex-end;
                        height: 100%;
                    }

                    .itemAmount {
                        font-size: 28rpx;
                        font-weight: bold;
                        color: #e64f22;
                        line-height: 40rpx;
                    }

                    .btnCol,
                    .chargeBtnCol {
                        margin-top: 24rpx;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        &.btnCol {
                            justify-content: space-between;
                        }
                        &.chargeBtnCol {
                            margin-right: -20rpx;
                        }

                        .chargeBtn {
                            margin-right: 20rpx;
                        }

                        .remarkBtn {
                            padding: 0 20rpx;
                            height: 60rpx;
                            background: #f7f7fb;
                            font-size: 26rpx;
                            color: #333333;
                            line-height: 60rpx;
                            border-radius: 16rpx;
                            position: relative;

                            .deleteIcon {
                                position: absolute;
                                width: 26rpx;
                                height: 26rpx;
                                top: -14rpx;
                                right: -14rpx;
                            }
                        }
                        .btnSelect {
                            height: 56rpx;
                            padding: 0 16rpx;
                            line-height: 56rpx;
                            background: #ffffff;
                            border: 2rpx solid #e64f22;
                            color: #e64f22;
                        }
                    }

                    .remarkBox {
                        width: 100%;
                        min-height: 195rpx;
                        background: #f7f7fb;
                        padding: 20rpx;
                        border-radius: 16rpx;
                        margin-top: 24rpx;
                        position: relative;
                        padding-bottom: 70rpx;
                        .top {
                            display: flex;
                            justify-content: space-between;
                            flex-direction: column;

                            .tip {
                                font-size: 26rpx;
                                color: #999999;
                                line-height: 37rpx;
                            }

                            .quickRemarks {
                                font-size: 26rpx;
                                font-weight: 400;
                                line-height: 40rpx;
                                min-height: 34rpx;
                                margin-bottom: 14rpx;
                                padding: 10rpx 0;
                            }
                        }

                        .inputItem {
                            min-height: 0;
                            overflow: visible;
                            width: 100%;
                            font-size: 26rpx;
                            font-weight: 400;
                            line-height: 40rpx;
                            padding: 0;
                            color: rgb(51, 51, 51);
                            background: #f7f7fb;
                        }
                        .clearRemark {
                            position: absolute;
                            bottom: 10rpx;
                            right: 14rpx;
                            display: flex;
                            align-items: flex-end;
                            justify-content: flex-end;

                            .maxlength {
                                font-size: 20rpx;
                                color: #333333;
                                line-height: 28rpx;
                            }

                            .imgBtn {
                                width: 50rpx;
                                height: 60rpx;
                                display: flex;
                                align-items: flex-end;
                                justify-content: flex-end;
                                img {
                                    width: 32rpx;
                                    height: 32rpx;
                                }
                            }
                        }
                    }
                }

                .tips {
                    margin-top: 24rpx;

                    .tipsTitle {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #e64f22;
                        line-height: 40rpx;
                    }

                    .tipsText {
                        margin-top: 17rpx;
                        font-size: 24rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 38rpx;

                        div {
                            display: inline;
                            color: #e64f22;
                        }
                    }
                }
            }
        }

        .formBox2 {
            background: #ffffff;
            border-radius: 8px;
            padding: 30rpx 24rpx 32rpx 32rpx;
            align-items: center;
            margin: 24rpx auto 0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .formBox2_item {
                display: flex;
                align-items: center;
            }

            .formBox2_left {
                img {
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 22rpx;
                }

                .formBox2_left_text {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    line-height: 40rpx;
                }
            }

            .formBox2_right {
                .formBox2_right_text {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #999999;
                    line-height: 40rpx;
                    margin-right: 4rpx;
                }

                .yellowColor {
                    color: #e64f22;
                }

                img {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }

        .cardPackTips {
            width: 100%;
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff7dc;
            margin: 24rpx auto 0;
            padding: 23rpx 30rpx 23rpx 30rpx;
            border-radius: 16rpx;

            .tipsText {
                font-size: 24rpx;
                color: #5d2d14;
                line-height: 42rpx;
            }

            .tipsRight {
                display: flex;
                align-items: center;
                .right {
                    width: 14rpx;
                    height: 14rpx;
                    border-top: 2rpx solid #5d2d14;
                    border-right: 2rpx solid #5d2d14;
                    transform: rotate(45deg);
                }
            }
        }
    }
}

.isScreen {
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding: 32rpx;

    .subMessageImg {
        width: 100%;
    }

    .closeImgWrap {
        width: 100%;
        display: flex;
        justify-content: flex-end;

        .closeBox {
            width: 35rpx;
            height: 35rpx;
            text-align: center;
            line-height: 35rpx;

            img {
                width: 25rpx;
                height: 25rpx;
            }
        }
    }

    .dete-item {
        background: #f7f7fb;
        border-radius: 16rpx;
        padding: 24rpx 28rpx 29rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;

        &:nth-of-type(1) {
            margin-top: 0;
        }

        .left {
            .name {
                font-size: 28rpx;
                font-weight: bold;
                color: #333333;
                line-height: 40rpx;
            }

            .id {
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 33rpx;
                margin-top: 8rpx;
            }
        }
    }

    .submit-dete-item {
        background: #f7f7fb;
        border-radius: 8rpx;
        margin-top: 24rpx;
        padding: 24rpx 31rpx 19rpx;
        display: flex;

        &:nth-of-type(1) {
            margin-top: 0;
        }

        .left {
            font-size: 26rpx;
            font-weight: 400;
            color: #333333;
            line-height: 37rpx;
            min-width: 172rpx;
        }

        .right {
            background: #f7f7fb;
            box-sizing: border-box;
            width: 100%;
            height: 37rpx;
            font-size: 26rpx;
            font-weight: 400;
            line-height: 37rpx;
            padding: 0;
        }
    }

    .submitTips {
        font-size: 24rpx;
        font-weight: 400;
        color: #666666;
        line-height: 38rpx;
        margin-top: 24rpx;
    }
}
.btnBox {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 32rpx 32rpx calc(16px + env(safe-area-inset-bottom));
    z-index: 99;
    background: #fff;
}

.btn {
    background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
    border-radius: 16rpx;
    color: #ffffff;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
}
.btnDisabled {
    color: #999999 !important;
}
</style>
<style lang="scss">
.invoice-form-placeholder {
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
    line-height: 40rpx;
}
</style>
