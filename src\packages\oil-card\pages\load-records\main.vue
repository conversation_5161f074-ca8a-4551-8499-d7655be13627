<template>
    <div class="xf-records">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            back-text="圈存记录"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <scroll-view class="content-list" scroll-y @scrolltolower="loadMore">
            <div class="content-box" v-if="dataList.length > 0">
                <div class="list-item" v-for="(item, index) in dataList" :key="index">
                    <div class="title">
                        <img src="@/static/cnpc-logo.png" alt />
                        {{ item.orgName }}
                    </div>
                    <div class="detail">创建时间：{{ item.tradeTime }}</div>
                    <div class="prize">
                        <span>￥</span>
                        <my-price color="#f96702" intFont="40rpx" floatFont="22rpx" :price="item.amount"></my-price>
                    </div>
                    <div class="info-btn">{{ item.tradeType }}</div>
                </div>
            </div>
            <div v-if="dataList.length == 0 || !hasMore" class="no-more">
                <img src="@/static/qc-no.png" alt class="no-img" mode="widthFix" />
                <div class="no-data">暂无圈存记录</div>
                <!-- <u-loadmore status="nomore" /> -->
            </div>
        </scroll-view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import myPrice from '@/components/price/price.vue';
import { getCircleDepositListApi } from '@/api/home.js';
export default {
    components: {
        myPrice,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            dataList: [],
            page: 1,
            cardNo: '',
            hasMore: true,
        };
    },
    onLoad(options) {
        if (options.cardNo) {
            this.cardNo = options.cardNo;
            this.loadData();
        }
    },
    methods: {
        async loadData() {
            let params = {
                cardNo: this.cardNo,
                pageNo: this.page,
                pageSize: 10,
            };
            let res = await getCircleDepositListApi(params, { isCustomErr: false });
            console.log('------', res);
            if (res.status === 0) {
                this.dataList = this.dataList.concat(res.data);
                if (res.data.length == 0 || res.data.length < 10) {
                    this.hasMore = false;
                }
                if (res.data.length == 0) {
                    uni.showModal({
                        title: '提示',
                        content: '本查询结果只显示12个月内记录，如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: () => {},
                    });
                }
            } else if (res.status == -1) {
                let str = '本查询结果只显示12个月内记录，如需更多查询记录请登录www.95504.net查询。(6100)';
                uni.showModal({
                    title: '提示',
                    content:
                        str != res.info
                            ? res.info
                            : '本查询结果只显示12个月内记录如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                    confirmColor: '#FF8200',
                    showCancel: false,
                    success: () => {},
                });
            }
        },
        loadMore() {
            this.page++;
            this.loadData();
        },
    },
};
</script>

<style lang="scss" scoped>
.xf-records {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);

    .content-list {
        min-height: 0;
        flex: 1;

        .content-box {
            padding: 24rpx;

            .list-item {
                padding: 24rpx;
                border-radius: 16rpx;
                background: #fff;
                margin-bottom: 24rpx;
                position: relative;

                .title {
                    font-size: 26rpx;

                    img {
                        display: inline-block;
                        height: 36rpx;
                        width: 36rpx;
                        margin-right: 16rpx;
                    }
                }

                .detail {
                    font-size: 22rpx;
                    color: #999999;
                    margin-top: 15rpx;
                }

                .prize {
                    margin-top: 25rpx;
                    display: flex;
                    align-items: baseline;
                    font-size: 40rpx;
                    color: #f96702;

                    span {
                        font-size: 22rpx;
                    }
                }

                .info-btn {
                    padding: 6px 12px;
                    box-sizing: border-box;
                    font-size: 12px;
                    text-align: center;
                    color: #909090;
                    // border: 1px solid #EDEDED;
                    margin-left: 4px;
                    width: 72px;
                    position: absolute;
                    right: 12px;
                    bottom: 12px;
                }
            }
        }

        .no-more {
            width: 100%;
            padding: 24rpx 0;
            text-align: center;
            transform: translateY(100px);
            .no-img {
                width: 320rpx;
                height: 320rpx;
            }
            .no-data {
                color: #666;
                font-size: 12px;
                margin-top: -50px;
            }
        }
    }
}
</style>
