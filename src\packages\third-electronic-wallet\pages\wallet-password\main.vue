<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view">
            <zj-navbar :height="44" title="开通昆仑e享卡"></zj-navbar>
            <div class="content-wrap">
                <div class="content-wrap-header">
                    <div class="current">
                        <input
                            class="border-box border-rad-4"
                            placeholder="请输入手机号"
                            :placeholder-style="fromInput"
                            type="text"
                            :disabled="form.authCode"
                            v-model="memberBaseInfo.phone"
                            disabled
                        />
                    </div>
                    <div @click="showKeyboard1" class="current">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            payPassword || '请输入6位数支付密码'
                        }}</div>
                    </div>
                    <div @click="showKeyboard2" class="current">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            confirmPassword || '请重复输入6位数支付密码'
                        }}</div>
                    </div>
                </div>
                <div class="footer">
                    <div class="footer-text">支付密码为6位数字；</div>
                    <div class="footer-text">昆仑e享卡与实体卡移动支付共用同一个支付密码，修改后实体卡线下支付密码不受影响；</div>
                    <div class="footer-text">能源e站App和小程序采用统一的支付密码，修改后同时生效。</div>
                </div>
                <div class="floatBtn">
                    <div class="btn" @click="jumpAuthorizationDescription">开通昆仑e享卡</div>
                </div>
            </div>
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-MPAAS -->
            <keyboard-plugin ref="handlePasswordKeyboardRef" v-if="isHarmony"></keyboard-plugin>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <cloudKeyBordPlugin ref="cloudKeyboardRef"></cloudKeyBordPlugin>
            <!-- #endif -->
            <zj-old-account v-if="isTransfer" :activate="activate"></zj-old-account>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { baseType } from '../../../../../project.config';
import { openAccount } from '../../../../s-kit/js/v3-http/https3/wallet';
import projectConfig from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
// #ifdef H5-CLOUD
import cloudKeyBordPlugin from '../../../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue'
// #endif
export default {
    mixins: [publicMixinsApi,],
    name: 'wallet-password',
    components: {
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin
        // #endif
    },
    props: {},
    data() {
        return {
            // input框的样式
            fromInput: 'color:#999999;font-size:14px;',
            // 中转之前页面的数据
            beforeParamsForm: {},
            // 是否显示密码的键盘
            isShowPassword: true,
            // 判断密码键盘是否初始化成功
            initSafePasswordFlag: false,
            // 是否显示确认密码的键盘
            isShowDefinePassword: true,
            // 密码长度
            passwordlength: '',
            // 确认密码的长度
            definePasswordlength: '',
            samepassword: '', //校验密码是否相同
            // 密码表单数据
            form: {
                // 支付密码
                payPassword: '',
                confirmPassword: '',
                // 是否开启免密支付
                passwordFree: 0,
                // // 免密金额
                // freeAmount: 0,
            },
            // 支付密码
            payPassword: '',
            // 确认支付密码
            confirmPassword: '',
            // 密码键盘实例
            passwordKeyboardRef: {},
            // 区分功能限制电子卡迁移成功后返回的到哪个页面
            activate: '',
            passWordOpenVal: {},
            isH5Cloud:false
        };
    },
    onShow() {},
    onLoad(options) {
        // 客户名称，手机号，会员等级等基本信息和能源币，油卡，电子券，积分和余额。
        this.$store.dispatch('memberBaseInfoAction');
        // #ifdef MP-WEIXIN
        let result = this.selectComponent('#passwordKeyboardId');
        this.$sKit.keyBordPlugin.initRef(result);
        this.passwordKeyboardRef = result;
        // #endif
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        this.$nextTick(() => {
            let result = this.$refs['handlePasswordKeyboardRef'];
            console.log('keybord初始化', result);
            this.$sKit.keyBordPlugin.initRef(result);
            this.passwordKeyboardRef = this.$refs['handlePasswordKeyboardRef'];
        });
        // #endif
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$nextTick(async () => {
                await this.$cnpcBridge.isCutScreen(true);
                let result = await this.$sKit.keyBordPlugin.initRef();
                this.$store.commit('setAccountDataPlugin', result);
                this.passwordKeyboardRef = result;
            });
        }
        // #endif
        // #ifdef H5-CLOUD
        this.isH5Cloud = true
        this.$nextTick(async () => {
            // 获取键盘的实例
            let result = await this.$refs.cloudKeyboardRef.initRef();
            // console.log('result',result)
            this.$store.commit('setAccountDataPlugin', result);
            this.passwordKeyboardRef = result;
        });
        // #endif
        // 如果存在某个页面跳转的参数并且解出来的数据不为空对象
        if (options && decodeURIComponent(options.data) !== '{}') {
            console.log(JSON.parse(decodeURIComponent(options.data)), '开通电子卡接收的个人信息参数');
            let personInfo = JSON.parse(decodeURIComponent(options.data));
            this.from = Object.assign(this.form, personInfo);
            console.log(this.from, '合并后的个人信息与手机号密码');
            this.$sKit.mpBP.tracker('e享卡开通', {
                seed: 'eCardActiveBiz',
                pageID: 'setPsdPage', // 页面名
                refer: personInfo.refer || '', // 来源
                channelID: projectConfig.clientCode, // C10/C12/C13
                address: personInfo.address,
            });
        }
        this.jumpAuthorizationDescription = this.$sKit.commonUtil.throttleUtil(this.jumpAuthorizationDescription);
    },
    onReady() {},
    computed: {
        ...mapGetters(['openId', 'memberBaseInfo']),
        ...mapState({
            gsmsToken: state => state.gsmsToken,
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            backWalletRechange: state => state.wallet.backWalletRechange, // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        /**
         * @description  : 关闭密码键盘
         * @return        {*}
         */
        // #ifndef MP-MPAAS || H5-CLOUD
        closeKeyboard1() {
            // this.selectComponent("#passwordKeyboardId").closeKeyboard();
            this.passwordKeyboardRef.closeKeyboard();
        },
        /**
         * @description  : 关闭确认支付密码键盘
         * @return        {*}
         */
        closeKeyboard2() {
            this.passwordKeyboardRef.closeKeyboard();
        },
        // #endif
        /**
         * @description  : 支付密码
         * @return        {*}
         */
        showKeyboard1() {
            // #ifdef MP-MPAAS || H5-CLOUD
            if (this.isHarmony || this.isH5Cloud) {
                let params = {
                    keyboardType: 'number',
                    passwordType: 'setup',
                    numberPassword: 6,
                    setText: '支付',
                    mongolianlayer: 1,
                };
                console.log('this.passwordKeyboardRef', this.passwordKeyboardRef);
                this.$nextTick(async () => {
                    await this.passwordKeyboardRef.openKeyboard(
                        params,
                        () => {
                            this.payPassword = this.passwordKeyboardRef.getFirstLength();
                            this.confirmPassword = this.passwordKeyboardRef.getSecondLength();
                            // console.log('pwdLength--',pwdLength)
                            // this.newPasswordShow = pwdLength
                        },
                        val => {
                            console.log('密码参数：', val);
                            this.form.payPassword = val.cipherText;
                            this.form.confirmPassword = val.cipherText;
                            this.passWordOpenVal = val;
                            this.form.deviceId = val.deviceId;
                            this.form.keyboardDataCacheId = val.keyboardDataCacheId;
                            this.form.uniqueId = val.uniqueId;
                        },
                    );
                });
            }
            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                async pwd => {
                    this.passwordlength = this.passwordKeyboardRef.getLength('password_unique1');
                    this.form.payPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    this.payPassword = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                },
            );
            // #endif
        },
        /**
         * @description  : 确认密码
         * @return        {*}
         */
        // #ifndef MP-MPAAS || H5-CLOUD
        showKeyboard2() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique2',
                6,
                async pwd => {
                    this.definePasswordlength = this.passwordKeyboardRef.getLength('password_unique2');
                    this.form.confirmPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique2');
                    this.confirmPassword = pwd;
                },
                () => {
                    console.log('密码键盘2的关闭函数');
                },
            );
        },
        // #endif
        /**
         * @description  : 跳转授权说明页面,校验表单并跳转人脸识别入口页面 密码6位数字校验
         * @return        {*}
         */
        async jumpAuthorizationDescription() {
            // #ifndef MP-MPAAS || H5-CLOUD
            this.samepassword = this.passwordKeyboardRef.equal('password_unique1', 'password_unique2');
            if (this.definePasswordlength < 6 && this.passwordlength < 6) {
                console.log('两个一起清空');
                this.form.confirmPassword = '';
                this.form.payPassword = '';
                return this.$util.noneToast('请输入6位数字支付密码');
            }
            if (this.passwordlength < 6) {
                this.form.payPassword = '';
                return this.$util.noneToast('请输入6位数字支付密码');
            }
            if (this.definePasswordlength < 6) {
                this.form.confirmPassword = '';
                return this.$util.noneToast('请再次输入支付密码');
            }
            if (this.samepassword == 1) {
                this.form.payPassword = '';
                this.form.confirmPassword = '';
                this.payPassword = '';
                this.confirmPassword = '';
                return this.$util.noneToast('两次密码不一致，请重新输入');
            }
            // #endif
            // #ifdef MP-MPAAS || H5-CLOUD
            if (this.isHarmony || this.isH5Cloud) {
                if (!this.form.payPassword) {
                    uni.showToast({
                        title: '请输入6位新密码',
                        icon: 'none',
                    });
                    return;
                } else if (!this.form.confirmPassword) {
                    uni.showToast({
                        title: '请输入6位新密码',
                        icon: 'none',
                    });
                    return;
                }
                if (this.payPassword.length !== 6) {
                    console.log('两个一起清空');
                    this.form.confirmPassword = '';
                    this.form.payPassword = '';
                    this.payPassword = '';
                    this.confirmPassword = '';
                    return this.$util.noneToast('请输入6位数字支付密码');
                } else if (this.confirmPassword.length !== 6) {
                    console.log('两个一起清空');
                    this.payPassword = '';
                    this.confirmPassword = '';
                    this.form.confirmPassword = '';
                    this.form.payPassword = '';
                    return this.$util.noneToast('请输入6位数字支付密码');
                }
            }
            // #endif
            console.log(this.form, 'this.form==');
            // #ifdef MP-WEIXIN
            if (this.from.authCode) {
                let res = await openAccount(this.from);
                if (res.success) {
                    await this.$sKit.commonUtil.eWalletNormal({
                        nextFun: () => {
                            this.$sKit.mpBP.tracker('e享卡开通', {
                                seed: 'eCardActiveBiz',
                                pageID: 'sucessPage', // 页面名
                                refer: this.from.refer || '', // 来源
                                channelID: projectConfig.clientCode, // C10/C12/C13
                                address: this.from.address,
                            });
                            if (this.from.isPalmPay || this.backWalletRechange) {
                                this.$store.dispatch('zjShowModal', {
                                    title: '恭喜您，昆仑e享卡开通成功！',
                                    confirmText: '确认',
                                    confirmColor: '#E64F22',
                                    success: res => {
                                        if (res.confirm) {
                                            let url = '';
                                            let params = {};
                                            if (this.from.isPalmPay) {
                                                // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
                                                url = '/packages/third-my-wallet/pages/wallet-setting/main';
                                            } else if (this.backWalletRechange) {
                                                url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
                                                params.refer = 'r17';
                                            }
                                            let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                            this.$sKit.layer.useRouter(url, params, type);
                                        }
                                    },
                                });
                            } else {
                                let url = '/packages/third-electronic-wallet/pages/wallet-success/main';
                                let params = {};
                                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                this.$sKit.layer.useRouter(url, params, type);
                            }
                        },
                        freezeReasonArr: [9, 10],
                        cancelCallback: () => {
                            // 获取用户非脱敏身份信息和获取电子卡迁移数据
                            this.activate = 'ktqb';
                            // 获取用户非脱敏身份信息和获取电子卡迁移数据
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                        walletAddParams: {
                            refer: 'r15',
                        },
                    });
                }
                return;
            }
            // #endif
            let url = '/packages/third-electronic-wallet/pages/wallet-authorization-description/main';
            let params = this.from;
            let type = 'redirectTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    filter: {},
    watch: {},
    async beforeDestroy() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
};
</script>
<style scoped lang="scss">
.view {
    display: flex;
    height: 100%;
    width: 100%;
    flex-direction: column;

    .content-wrap {
        flex: 1;
        background: #f7f7f7;
        padding: 16px 16px 66px;

        .content-wrap-header {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .current {
                width: 100%;
                height: 44px;
                margin-bottom: 16px;
                border-radius: 4px;
                .inputBox {
                    height: 44px;
                    line-height: 44px;
                    padding-left: 15px;
                }

                input {
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    padding-left: 15px;
                }
            }
        }

        .footer {
            .footer-text {
                margin-bottom: 7px;
                font-size: 13px;
                font-weight: 500;
                color: #333;
            }
        }
    }
    .floatBtn {
        position: fixed;
        left: 0;
        right: 0;
        bottom: env(safe-area-inset-bottom);
        padding: 12px 32rpx;
        background: #ffffff;
        .btn {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            text-align: center;
            height: 44px;
            line-height: 44px;
            width: 100%;
            border-radius: 8px;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            color: #fff;
        }
    }
}
</style>
<style scoped>
input {
    background: #fff;
}
</style>
