// 微信小程序设备指纹

// #ifdef MP-WEIXIN || H5-CLOUD
import plugin from '@/s-kit/js/v3-plugin/fp/wx';
const pluginHm = require('../s-kit/js/v3-plugin/fp/wx/utils/bsdfp.min');
import { mapGetters } from 'vuex';
import { fpUrl, harmonyOSRiskControlPublicKey } from '../../project.config';
// import Store from '@/store';
// #endif

export default {
    // #ifdef MP-WEIXIN
    computed: {
        ...mapGetters(['openId', 'fpInfo']),
    },
    mounted() {
        console.log('wx fp mounted');
    },
    methods: {
        getFP(custId = 'wx') {
            // fp已内置缓存，不用再做缓存处理
            // if (this.fpInfo) return this.fpInfo;
            return new Promise((resolve, reject) => {
                plugin.setCustID(custId);
                plugin.setUrl(fpUrl);
                plugin.getFingerPrint(
                    this.openId,
                    res => {
                        console.log('wx dfp: ' + res);
                        // Store.commit('setFPInfo', res.info);
                        resolve(res);
                    },
                    err => {
                        console.log('wx fp error: ' + err);
                        reject(err);
                    },
                );
            });
        },
        getHmFP(custId = 'wx') {
            console.log('鸿蒙系统微信小程序调用风控设备指纹方法');
            // 方法二：同步获取指纹
            let openid = uni.getStorageSync('tokenInfo').openId || null;
            pluginHm.setup(fpUrl, custId);
            pluginHm.setPublicKey(harmonyOSRiskControlPublicKey);
            return new Promise(async (resolve, reject) => {
                pluginHm.getFingerPrint(
                    openid,
                    res => {
                        console.log(res, '鸿蒙系统====dfp===成功');
                        // Store.commit('setFPInfo', res.info);
                        resolve(res);
                    },
                    err => {
                        console.log(err, '鸿蒙系统====dfp===失败');
                        reject(err);
                    },
                );
            });
        },
    },
    // #endif
};
