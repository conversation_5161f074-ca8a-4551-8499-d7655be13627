<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details p-bf bg-fff">
            <zj-navbar title="更换绑定手机号" :border-bottom="false"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div">
                    <div class="fl-column fl-al-jus-cen">
                        <img src="../../image/success_img.png" alt />
                        <!-- <div class="amount font-16 color-333 weight-bold">您已完成注销</div> -->
                        <div class="font-12 color-999 text">手机号变更成功</div>
                    </div>
                </div>
                <div class="btn_div fl-jus-bet">
                    <div class="finish_verification btn-plain color-E64F22 font-15" @click="backClick()">返回首页</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: '',
    data() {
        return {};
    },
    mounted() {},
    methods: {
        // 返回首页
        backClick() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;

            padding-top: 40px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 12px;
            }

            .amount {
                margin-top: 20px;
                line-height: 23px;
            }

            .text {
                line-height: 23px;
                margin-bottom: 40px;
            }
        }
    }
}

.btn_div {
    display: flex;
    flex-direction: row;

    .finish_verification {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
