//SDK Version: 3.4.12 created at: 2024-12-23 15:19:51
var onProcess = !1,
    cbList = [];
function setup(e, t) {
    setCustID(t),
        setUrl(e, {
            success: e => {
                e.merID && e.ccAppID && prefetchSinature();
            },
            fail: e => {},
        }),
        getMerID() && getAppID() && (debugLog('local configuration triggers prefetching'), prefetchSinature());
}
function setPublicKey(e) {
    setSM2PublicKey(e);
}
function getFingerPrint(t, e, n) {
    var r = getdfp();
    r
        ? (e(r),
          generate(!1)
              .then(function (e) {
                  debugLog('CCID success in getFingerPrint: ' + e);
              })
              .catch(function (e) {
                  debugLog('CCID fail in getFingerPrint: ' + e);
              }))
        : (cbList.push({ success: e, fail: n }),
          onProcess
              ? debugLog('getDfp onProcess')
              : (debugLog('getDfp'),
                (onProcess = !0),
                checkConfig(e => {
                    debugLog('check config:', e),
                        getCollection(
                            t,
                            e => {
                                handleCallback(!0, e);
                            },
                            e => {
                                handleCallback(!1, e);
                            },
                        );
                })));
}
function handleCallback(e, t) {
    for (const n of cbList) e ? n.success(t) : n.fail(t);
    (onProcess = !1), (cbList = []);
}
async function getFingerprintSync(e) {
    const t = getdfp();
    var n = gettime();
    if (null != t && null != t && validateTime(n) && 0 < t.length)
        return (
            generate(!1)
                .then(function (e) {
                    debugLog('CCID success in getFingerPrintSync: ' + e);
                })
                .catch(function (e) {
                    debugLog('CCID fail in getFingerPrintSync: ' + e);
                }),
            t
        );
    {
        const t = await sendRequest(await getCollection(e, null, null), null, null);
        return t;
    }
}
function setMgrUrl(e) {
    setMgrServerUrl(e);
}
function getDfpUUID() {
    var e = getLocalDfpUUID();
    return (e && '' != e) || setLocalDfpUUID((e = randomUUID())), e;
}
module.exports = {
    getFingerPrint: getFingerPrint,
    getFingerprintSync: getFingerprintSync,
    setup: setup,
    setPublicKey: setPublicKey,
    setMgrUrl: setMgrUrl,
};
const dcyUrl = 'https://msg.cmpassport.com/h5/getMobile',
    xcdUrl = 'https://id6.me/gw/preuniq.do',
    tulUrl = 'https://nisportal.10010.com:9001/api';
class ZXSDKError extends Error {
    constructor(e, t) {
        super(e), (this.name = 'ZXSDKError'), (this.stack = null), (this.cause = t);
    }
}
class ZXSDK {
    constructor() {
        this.isInit = !1;
    }
    emptyCarrierRequest = t => {
        this.isInit
            ? this.sendEmptyRequest(t)
            : this.init({
                  success: () => {
                      this.sendEmptyRequest(t);
                  },
                  fail: e => {
                      this.failCallback(e, t);
                  },
              });
    };
    sendEmptyRequest = t => {
        this.emtpyList = [];
        const n = this;
        wx.request({
            url: dcyUrl,
            data: {},
            method: 'POST',
            success(e) {
                n.emptyRequestComplete('dcy', t);
            },
            fail(e) {
                n.emptyRequestComplete('dcy', t);
            },
        }),
            wx.request({
                url: tulUrl,
                data: {},
                method: 'GET',
                success(e) {
                    n.emptyRequestComplete('tul', t);
                },
                fail(e) {
                    n.emptyRequestComplete('tul', t);
                },
            }),
            wx.request({
                url: xcdUrl,
                data: {},
                method: 'GET',
                success(e) {
                    n.emptyRequestComplete('xcd', t);
                },
                fail(e) {
                    n.emptyRequestComplete('xcd', t);
                },
            }),
            setTimeout(() => {
                t.success('empty request force end');
            }, 300);
    };
    emptyRequestComplete = (e, t) => {
        this.emtpyList.push(e), 3 <= this.emtpyList.length && t.success('all emtpty request send');
    };
    handleSignParams = e => {
        var t = JSON.parse(JSON.stringify(e));
        for (const n in e)
            ('dcy' !== n && 'tul' !== n && 'xcd' !== n) ||
                (delete t[n].mode, delete t[n].bindId, delete t[n].reqUrl, 'xcd' === n && delete t[n].trd);
        return t;
    };
    requireCelluarToken = (e, t) => {
        this.bsSigns = e;
        const n = this.handleSignParams(e);
        this.isInit
            ? this.getToken(n, t)
            : this.init({
                  success: () => {
                      this.getToken(n, t);
                  },
                  fail: e => {
                      this.failCallback(e, t);
                  },
              });
    };
    getToken = (n, r) => {
        wx.getNetworkType({
            success: e => {
                var t;
                'wifi' == e.networkType
                    ? ((t = new ZXSDKError('wifi not supported!')), this.failCallback(t, r))
                    : 'none' == e.networkType || 'unknown' == e.networkType
                    ? ((t = new ZXSDKError('network not avaliable!')), this.failCallback(t, r))
                    : this.cellarToken(n, r);
            },
            fail: e => {
                var t = new ZXSDKError('network not avaliable!');
                this.failCallback(t, r);
            },
        });
    };
    init = e => {
        var t = wx.getDeviceInfo();
        this.platform = t.platform.toLowerCase();
        'ohos' != this.platform && 'ios' != this.platform && 'android' != this.platform
            ? ((t = new ZXSDKError(this.platform + ' not supported')), this.failCallback(t, e))
            : e.success();
    };
    cellarToken = (e, n) => {
        var t = {};
        this.dcyGetToken(e.dcy, {
            success: e => {
                this.carrierMode = this.bsSigns.dcy.mode;
                let t = e;
                1 === this.carrierMode && (t = this.handResult(e, 0, '', this.bsSigns.dcy.bindId)), n.success(t);
            },
            fail: e => {
                (t.cc = e || ''), this.jundgeCallback(t, n);
            },
        });
        const r = e.trd;
        this.xcdGetToken(e.xcd, {
            success: e => {
                this.carrierMode = this.bsSigns.xcd.mode;
                let t = e;
                3 === this.carrierMode && (t = this.handResult(e, 1, r, this.bsSigns.xcd.bindId)), n.success(t);
            },
            fail: e => {
                (t.ct = e || ''), this.jundgeCallback(t, n);
            },
        }),
            this.tulGetToken(e.tul, {
                success: e => {
                    this.carrierMode = this.bsSigns.tul.mode;
                    var t;
                    2 === this.carrierMode && ((t = this.handResult(e.code, 2, '', this.bsSigns.tul.bindId)), (e.code = t)),
                        n.success(JSON.stringify(e));
                },
                fail: e => {
                    (t.uc = e || ''), this.jundgeCallback(t, n);
                },
            });
    };
    jundgeCallback = (e, t) => {
        var n = e.cc,
            r = e.ct,
            e = e.uc;
        null != e && null != n && null != r && ((n = new ZXSDKError(n + ' | ' + r + ' | ' + e + ': 网关出错')), this.failCallback(n, t));
    };
    handResult = (e, t, n, r) => {
        (e = this.insertStr(e, 4, t)), (e = this.insertStr(e, 9, r));
        return 1 == t && (e += n), (e += r.length);
    };
    insertStr = (e, t, n) => e.slice(0, t) + n + e.slice(t);
    failCallback = (e, t) => {
        t && t.fail && t.fail(e);
    };
    dcyGetToken = (t, n) => {
        var e = this.bsSigns.dcy.reqUrl;
        wx.request({
            url: e,
            data: t,
            method: 'POST',
            success(e) {
                try {
                    '103000' == e.data.body.resultCode
                        ? n.success(e.data.body.token)
                        : n.fail && n.fail((e.data.body.resultCode || '') + ',' + t.traceid + ',' + t.msgId);
                } catch (e) {
                    n.fail(t.traceid + ',' + t.msgId);
                }
            },
            fail(e) {
                n.fail && n.fail(e.code);
            },
        });
    };
    tulGetToken = (t, n) => {
        var e = this.bsSigns.tul;
        wx.request({
            url: e.reqUrl,
            data: t,
            method: 'GET',
            success(e) {
                200 == e.statusCode && null != e.data.authurl
                    ? wx.request({
                          url: e.data.authurl + '/api',
                          data: t,
                          method: 'GET',
                          success(e) {
                              200 == e.statusCode && null != e.data.code && 6 < e.data.code.length
                                  ? n.success(e.data)
                                  : n.fail && n.fail(e.data.code);
                          },
                          fail(e) {
                              n && n.fail && n.fail(e.code);
                          },
                      })
                    : n.fail && n.fail(e.data.code);
            },
            fail(e) {
                n && n.fail && n.fail(e.code);
            },
        });
    };
    xcdGetToken = (e, t) => {
        var n = this.bsSigns.xcd.reqUrl;
        wx.request({
            url: n,
            data: e,
            header: { ContentType: 'application/x-www-form-urlencoded;charset=UTF-8' },
            method: 'GET',
            success(e) {
                '0' == e.data.result ? t.success(e.data.data) : t.fail && t.fail(e.data.result);
            },
            fail(e) {
                t && t.fail && t.fail(e.code);
            },
        });
    };
}
const MaxRetryTimes = 5;
var key,
    iv,
    signatureType,
    carrierMode,
    preSignature,
    statisticsData,
    sendingData,
    tokenErrData,
    signatureBeginTime,
    generateBeginTime,
    tokenBeginTime,
    exchangeBeginTime,
    CCIDServerUrl = 'https://dfppro.bsfit.com.cn/proBsfitMgr',
    retryCount = 0,
    onProccess = !1,
    errUpload = !1,
    emptyReqSend = !1,
    prefetching = !1;
function generate(r) {
    return new Promise(function (t, n) {
        if (!onProccess)
            if (((onProccess = !0), getAppID() && getMerID())) {
                var e = '' == checkCCIDExpired();
                if (r && e) signatureType = 2;
                else if (r && !e) signatureType = 0;
                else {
                    if (r || !e) return n(''), void (onProccess = !1);
                    signatureType = 1;
                }
                startStatistics(),
                    sendEmptyRequest(),
                    debugLog('getSignature start, type', signatureType),
                    getSignaure()
                        .then(function (e) {
                            debugLog('getSignature end'),
                                setTime('signature'),
                                (tokenBeginTime = timestamp()),
                                waitEmptyRequest().then(() => {
                                    getToken(e)
                                        .then(function (e) {
                                            setTime('token'),
                                                (exchangeBeginTime = timestamp()),
                                                (carrierMode = e.mode),
                                                getCCIDWithToken(e.token, e.mode)
                                                    .then(function (e) {
                                                        t(e), setTime('exchange'), endStatistics(0, '');
                                                    })
                                                    .catch(function (e) {
                                                        n(e), setTime('exchange'), endStatistics(3003, e);
                                                    });
                                        })
                                        .catch(function (e) {
                                            n(e), setTime('token'), endStatistics(3002, e), uploadTokenErr();
                                        });
                                });
                        })
                        .catch(function (e) {
                            n(e), setTime('signature'), endStatistics(3001, 'get signature failed');
                        });
            } else n('CCID is not enable'), (onProccess = !1);
    });
}
function sendEmptyRequest() {
    0 != signatureType
        ? (debugLog('send empty requeset'),
          (emptyReqSend = !1),
          new ZXSDK().emptyCarrierRequest({
              success: e => {
                  debugLog('empty request success: ' + e), (emptyReqSend = !0);
              },
              fail: e => {
                  debugLog('empty request fail: ' + e.message), (emptyReqSend = !0);
              },
          }))
        : (emptyReqSend = !0);
}
function waitEmptyRequest() {
    return new Promise(e => {
        if (emptyReqSend) e();
        else {
            const t = setInterval(() => {
                debugLog('check empty req'), emptyReqSend && (clearInterval(t), e());
            }, 10);
        }
    });
}
function getSignaure() {
    return new Promise(function (t, n) {
        if (preSignature) t(preSignature), (preSignature = null);
        else if (prefetching) {
            debugLog('wait prefetching end');
            const e = setInterval(() => {
                prefetching ||
                    (clearInterval(e),
                    preSignature
                        ? (t(preSignature), debugLog('checkPrefetchStatus prefetch success end'))
                        : (debugLog('prefetch failed, requestSignature'),
                          requestSignature()
                              .then(e => {
                                  t(e);
                              })
                              .catch(e => {
                                  n(e);
                              })));
            }, 10);
        } else
            debugLog('not prefetching, requestSignature'),
                requestSignature()
                    .then(e => {
                        t(e);
                    })
                    .catch(e => {
                        n(e);
                    });
    });
}
function prefetchSinature() {
    var e;
    prefetching ||
        ((e = getCCID()) && 0 < e.length) ||
        (debugLog('prefetchSinature'),
        (prefetching = !0),
        debugLog('prefetch type:', (signatureType = getdfp() ? 1 : 2)),
        sendEmptyRequest(),
        requestSignature()
            .then(function (e) {
                debugLog('prefetch signature success'),
                    (preSignature = e),
                    (prefetching = !1),
                    setTimeout(() => {
                        debugLog('clean preSignature'), (preSignature = null);
                    }, 18e4);
            })
            .catch(function (e) {
                prefetching = !1;
            }));
}
function requestSignature() {
    return new Promise(function (n, r) {
        var e = wx.getSystemInfoSync(),
            t = wx.getAccountInfoSync().miniProgram.appId,
            i = getAppID(),
            a = timestampStr(),
            s = ((key = randomKey(16, 16)), (iv = randomKey(16, 16)), key + iv + getDfpUUID().slice(-2)),
            t = { pkgName: t, appID: i, timestamp: a, uuid: s, carrier: 0, type: signatureType, systemPlatform: e.platform },
            a = getKeyAndIVFromStr(i),
            s = sm4Encrypt(JSON.stringify(t), a.key, a.iv);
        wx.request({
            url: CCIDServerUrl + '/data/getSign?custID=' + getCustID() + '&platform=WMP&merID=' + getMerID(),
            method: 'POST',
            data: { data: s },
            success(e) {
                var t;
                200 != e.data.code
                    ? r(e.data.message)
                    : (setCcidEnable((t = (e = JSON.parse(sm4Decrypt(e.data.data, key, iv))).ccidEnable)),
                      1 != t
                          ? r('ccid is not enable')
                          : ((errUpload = e.errUpload),
                            0 < e.signData.length ? ((t = buildSignParams(e.signData)), n(t)) : r('ccid configuration is not set')));
            },
            fail(e) {
                r(e);
            },
        });
    });
}
function getToken(e) {
    return new Promise(function (n, t) {
        const r = new ZXSDK();
        r.requireCelluarToken(e, {
            success: e => {
                var t = r.carrierMode;
                n({ token: e, mode: t });
            },
            fail: e => {
                t(e.message);
            },
        });
    });
}
function getCCIDWithToken(s, o) {
    return new Promise(function (n, r) {
        var e = wx.getAccountInfoSync().miniProgram.appId,
            t = getAppID(),
            i = timestampStr(),
            a = ((key = randomKey(16, 16)), (iv = randomKey(16, 16)), key + iv + getDfpUUID().slice(-2)),
            e = { pkgName: e, appID: t, timestamp: i, uuid: a, mode: o, token: s, type: signatureType },
            i = getKeyAndIVFromStr(t),
            a = sm4Encrypt(JSON.stringify(e), i.key, i.iv);
        wx.request({
            url: CCIDServerUrl + '/data/getCcid?custID=' + getCustID() + '&platform=WMP&merID=' + getMerID(),
            method: 'POST',
            data: { data: a },
            success(e) {
                var t;
                200 != e.data.code
                    ? r(e.data.message)
                    : ((e = JSON.parse(sm4Decrypt(e.data.data, key, iv))),
                      0 == signatureType
                          ? n('')
                          : (setExpiration(e.expiration), setCCID((t = e.ccid + '#' + timestampStr())), n(t), updateCCIDMatch(e.ccid)));
            },
            fail(e) {
                r(e);
            },
        });
    });
}
function buildSignParams(e) {
    var t,
        n = {};
    for (t in e) {
        var r = e[t];
        1 === r.mode || 4 === r.mode
            ? (n.dcy = r)
            : 2 === r.mode || 7 === r.mode
            ? (n.tul = r)
            : (3 !== r.mode && 6 !== r.mode) || ((n.trd = r.trd), (n.xcd = r));
    }
    return n;
}
function checkCCIDExpired() {
    var e,
        t,
        n = getCCID();
    return null != n &&
        '' != n &&
        2 == (n = n.split('#')).length &&
        ((e = n[0]), (n = parseInt(n[1]) / 1e3), (t = getExpiration()), timestamp() / 1e3 - n < t)
        ? e
        : '';
}
function getCCIDConfig() {
    let e = getConfig();
    return (e = null != e && null != e && '' != e ? e : { ccidUpload: '0', ccidUploadMode: '0', ccidWait: '0', waitTime: 0 });
}
function collectCCID() {
    return new Promise(function (t, e) {
        var n;
        debugLog('CCID collect start'),
            -1 != getCcidEnable() && getAppID() && getMerID()
                ? null != (n = getCCID()) && '' != n
                    ? (t(n),
                      generate(!0)
                          .then(function (e) {})
                          .catch(function (e) {}))
                    : '1' == (currentConfig = getCCIDConfig()).ccidWait && '' == getCookieCode() && 0 < currentConfig.waitTime
                    ? promiseTimeout(currentConfig.waitTime, generate(!0))
                          .then(function (e) {
                              t(e);
                          })
                          .catch(function (e) {
                              t('');
                          })
                    : (t(''),
                      generate(!0)
                          .then(function (e) {
                              debugLog('CCID: ' + e);
                          })
                          .catch(function (e) {
                              debugLog('CCID fail in collectCCID : ' + e);
                          }))
                : (t(''), debugLog('CCID collect finished: CCID is unavailable'));
    });
}
function startStatistics() {
    (statisticsData = {
        createTime: timestamp(),
        platform: 'WMP',
        custID: getCustID(),
        ccAppID: getAppID(),
        sdkVersion: '3.4.12',
        type: signatureType,
    }),
        wx.getNetworkType({
            success(e) {
                e = e.networkType;
                statisticsData.networkType = e;
            },
        }),
        (statisticsData.dfpUUID = getDfpUUID()),
        (generateBeginTime = timestamp()),
        (signatureBeginTime = timestamp());
}
function endStatistics(e, t) {
    (onProccess = !1),
        setTime('generate'),
        (statisticsData.errorCode = e),
        null != carrierMode && (statisticsData.mode = carrierMode),
        null != t && '' != t && (statisticsData.errorMsg = t),
        (sendingData = Object.assign({}, statisticsData)),
        (tokenErrData = Object.assign({}, statisticsData)),
        uploadStatistics();
}
function uploadStatistics() {
    var t = getCCIDConfig();
    if ('1' == t.ccidUpload) {
        let e = {};
        (e =
            '1' != t.ccidUploadMode
                ? {
                      createTime: sendingData.createTime,
                      ccAppID: sendingData.ccAppID,
                      custID: sendingData.custID,
                      platform: sendingData.platform,
                      errorCode: sendingData.errorCode,
                      ccAppID: sendingData.ccAppID,
                      type: sendingData.type,
                      mode: sendingData.mode || 0,
                  }
                : sendingData),
            debugLog('CCID statistics:'),
            debugLog(e),
            wx.request({
                url: getUrl() + '/public/collections?type=2&channel=WMP&custID=' + getCustID(),
                method: 'POST',
                data: sendingData,
                success(e) {
                    1 == e.data ? (retryCount = 0) : retryUpload();
                },
                fail(e) {
                    retryUpload();
                },
            });
    }
}
function retryUpload() {
    ++retryCount <= MaxRetryTimes
        ? (debugLog('retry upload CCID statistics'),
          setTimeout(() => {
              uploadStatistics();
          }, 5e3))
        : (debugLog('retry-uploads reached max count'), (retryCount = 0));
}
function setTime(e) {
    'signature' == e
        ? (statisticsData.signatureTime = timestamp() - signatureBeginTime)
        : 'token' == e
        ? (statisticsData.tokenTime = timestamp() - tokenBeginTime)
        : 'exchange' == e
        ? (statisticsData.exchangeTime = timestamp() - exchangeBeginTime)
        : 'generate' == e && (statisticsData.generateTime = timestamp() - generateBeginTime);
}
function updateCCIDMatch(e) {
    var t = getdfp(),
        n = gettime();
    null != t &&
        validateTime(n) &&
        0 < t.length &&
        ((n = getAppID()),
        (t = { outCode: t, CCID: e, timestamp: timestampStr() }),
        (e = getKeyAndIVFromStr(n)),
        (n = sm4Encrypt(JSON.stringify(t), e.key, e.iv)),
        wx.request({
            url: getUrl() + '/public/generate/update?platform=WMP&custID=' + getCustID(),
            method: 'POST',
            data: { data: n },
            success(e) {
                debugLog('/generate/update success: ' + e.data);
            },
            fail(e) {
                debugLog('/generate/update fail: ' + e.errMsg);
            },
        }));
}
function uploadTokenErr() {
    var e, t, n;
    !errUpload ||
        (tokenErrData.errorMsg && -1 != tokenErrData.errorMsg.indexOf('not supported')) ||
        ((t = wx.getAccountInfoSync().miniProgram.appId),
        (n = getAppID()),
        (e = timestampStr()),
        (key = randomKey(16, 16)),
        (iv = randomKey(16, 16)),
        (t = { pkgName: t, appID: n, timestamp: e, uuid: key + iv + getDfpUUID().slice(-2), mode: 0, type: signatureType }),
        delete tokenErrData.createTime,
        delete tokenErrData.dfpUUID,
        delete tokenErrData.custID,
        delete tokenErrData.platform,
        delete tokenErrData.generateTime,
        delete tokenErrData.ccAppID,
        (e = Object.assign({}, t, tokenErrData)),
        debugLog('uploadTokenErr data:'),
        debugLog(e),
        (t = getKeyAndIVFromStr(n)),
        (n = sm4Encrypt(JSON.stringify(e), t.key, t.iv)),
        wx.request({
            url: CCIDServerUrl + '/data/getCcid?custID=' + getCustID() + '&platform=WMP&merID=' + getMerID(),
            method: 'POST',
            data: { data: n },
            success(e) {
                200 == e.data.code ? debugLog('uploadTokenErr success') : debugLog('uploadTokenErr fail');
            },
            fail(e) {
                debugLog('uploadTokenErr fail');
            },
        }));
}
function promiseTimeout(r, e) {
    var t = new Promise((e, t) => {
        const n = new Error('请求超时');
        setTimeout(() => {
            t(n);
        }, r);
    });
    return Promise.race([e, t]);
}
function setMgrServerUrl(e) {
    CCIDServerUrl = e;
}
function getCollection(a, s, o) {
    return new Promise(function (t, e) {
        debugLog('getCollection start');
        let n = { platform: 'WMP', sdkVersion: '3.4.12' };
        const r = getCustID();
        (n.custID = r), (n.dfpUUID = getDfpUUID());
        var i = new Date().getTime(),
            i = ((n.timestamp = i.toString()), wx.getDeviceInfo()),
            i = ((n.brand = i.brand), (n.model = i.model), (n.osVersion = i.system), (n.systemPlatform = i.platform), wx.getWindowInfo()),
            i =
                ((n.devicePixelRatio = i.pixelRatio.toString()),
                (n.screenWidth = i.screenWidth.toString()),
                (n.screenHeight = i.screenHeight.toString()),
                (n.windowWidth = i.windowWidth.toString()),
                (n.windowHeight = i.windowHeight.toString()),
                (n.statusBarHeight = i.statusBarHeight.toString()),
                wx.getAppBaseInfo()),
            i =
                ((n.language = i.language),
                (n.version = i.version),
                (n.wxSDKVersion = i.SDKVersion),
                (n.enableDebug = i.enableDebug ? '1' : '0'),
                wx.getSystemSetting()),
            i =
                ((n.wifiEnable = i.wifiEnabled ? '1' : '0'),
                (n.locationEnabled = i.locationEnabled ? '1' : '0'),
                (n.bluetoothEnabled = i.bluetoothEnabled ? '1' : '0'),
                generateSmartID(n.model, n.pixelRatio, n.screenWidth, n.screenHeight, n.brand));
        (n.wxSmartID = i),
            getLocation()
                .then(function (e) {
                    return (
                        e && null != e.latitude && null != e.latitude && (n.coordinates = '[' + e.longitude + ',' + e.latitude + ']'),
                        getNetworkType()
                    );
                })
                .then(function (e) {
                    return e && null != e.networkType && (n.networkType = e.networkType), getScreenBrightness();
                })
                .then(function (e) {
                    return e && (n.brightness = e.value.toString()), getConnectedWifiInfo();
                })
                .then(function (e) {
                    null != e &&
                        null != e.wifi &&
                        ((t = e.wifi.BSSID), (e = MD5(e.wifi.SSID)), null != t) &&
                        '' != t &&
                        null != e &&
                        '' != e &&
                        (n.currentWiFi = '[' + e + '-' + t + ']');
                    var t,
                        e = wx.getBatteryInfoSync();
                    return (
                        null != e && ((n.batteryStatus = e.isCharging ? '1' : '0'), (n.batteryLevel = e.level.toString())),
                        null != a && (n.userInfo = MD5(a)),
                        (n.custID = r),
                        '' != getCookieCode() && (n.cookieCode = getCookieCode()),
                        collectCCID()
                    );
                })
                .then(function (e) {
                    debugLog('CCID collect return'), '' != e && ((e = e.split('#')), (n.CCID = e[0]), (n.ccidUpdateTime = e[1]));
                    e = getAppID();
                    null != e && null != getMerID() && (n.ccAppID = getAppID()),
                        debugLog('getCollection end'),
                        s && o ? sendRequest(n, s, o) : t(n);
                });
    });
}
function generateSmartID(e, t, n, r, i) {
    return MD5(e + '' + t + n + r + i);
}
function getLocation() {
    return new Promise(function (e, t) {
        e(null);
    });
}
function getNetworkType() {
    return new Promise(function (t, e) {
        wx.getNetworkType({
            success: function (e) {
                t(e);
            },
            fail: function (e) {
                t(null);
            },
        });
    });
}
function getScreenBrightness() {
    return new Promise(function (t, e) {
        wx.getScreenBrightness({
            success: function (e) {
                t(e);
            },
            fail: function (e) {
                t(null);
            },
        });
    });
}
function getConnectedWifiInfo() {
    return new Promise(function (t, e) {
        wx.getConnectedWifi({
            success: function (e) {
                t(e);
            },
            fail: function (e) {
                t(null);
            },
        });
    });
}
var offsetDfpData = null;
function sendRequest(c, u, g) {
    return new Promise(function (t, e) {
        var n,
            r,
            i,
            a,
            s = getUrl();
        null == s && g('url不存在');
        let o;
        (o =
            offsetDfpData ||
            ((r = JSON.stringify(c)),
            (n = require('miniprogram-sm-crypto').sm4),
            (i = randomKey(16, 16)),
            (a = randomKey(16, 16)),
            (n = base64ArrayBuffer((n = n.encrypt(r, stringToByte(i), { mode: 'cbc', iv: stringToByte(a) })))),
            (r = require('miniprogram-sm-crypto').sm2),
            (i = i + a),
            (a = getSM2PublickKey()),
            { inputItem: n, bse: 2, bsk: base64ArrayBuffer('04' + r.doEncrypt(i, a, 1)) })),
            debugLog('send dfp generate request'),
            wx.request({
                url: s + '/public/mp/generate/post',
                data: o,
                method: 'POST',
                success(e) {
                    200 == e.statusCode
                        ? (setdfp(e.data.dfp),
                          settime(e.data.exp),
                          e.data.cookieCode && setCookieCode(e.data.cookieCode),
                          (u ? (debugLog('dfp generate request success'), u) : t)(e.data.dfp))
                        : (debugLog('dfp generate failed'),
                          (e = 'status code:' + e.statusCode),
                          g ? g(e) : ((e = buildOffestDfp((offsetDfpData = o))), t(e)));
                },
                fail(e) {
                    debugLog('dfp generate failed'), g ? g(JSON.stringify(e)) : ((e = buildOffestDfp((offsetDfpData = o))), t(e));
                },
            });
    });
}
function buildOffestDfp(e) {
    return base64Encode(JSON.stringify(e));
}
const Swap1 = function (t) {
        var n,
            r = t.length,
            i = t.split('');
        for (let e = 0; e < parseInt(r / 2); e++) e % 2 == 0 && ((n = t.charAt(e)), (i[e] = i[r - 1 - e]), (i[r - 1 - e] = n));
        return i.join('');
    },
    Plus = function (t) {
        let n = '';
        var r = t.length;
        for (let e = 0; e < r; e++) {
            var i = t.charAt(e).charCodeAt(0);
            127 === i ? (n += String.fromCharCode(0)) : (n += String.fromCharCode(i + 1));
        }
        return n;
    },
    Bls3 = function (e) {
        var t,
            n = e.length,
            r = n % 3 == 0 ? parseInt(n / 3) : parseInt(n / 3) + 1;
        return n < 3 ? e : ((t = e.substring(0, +r)), e.substring(+r, 2 * r) + e.substring(2 * r, n) + t);
    },
    Brs3 = function (e) {
        var t,
            n,
            r = e.length,
            i = r % 3 == 0 ? parseInt(r / 3) : parseInt(r / 3) + 1;
        return r < 3 ? e : ((t = e.substring(0, +i)), (n = e.substring(+i, 2 * i)), e.substring(2 * i, r) + t + n);
    },
    Reverse = function (t) {
        let n = '';
        for (let e = t.length - 1; 0 <= e; e--) n += t.charAt(e);
        return n;
    };
let SHA256 = function (e) {
        return CryptoJS.SHA256(e).toString(CryptoJS.enc.Base64);
    },
    Swap = function (e) {
        var t = e.length;
        let n = '';
        return (n =
            e.length % 2 == 0
                ? e.substring(t / 2, t) + e.substring(0, t / 2)
                : e.substring(t / 2 + 1, t) + e.charAt(t / 2) + e.substring(0, t / 2));
    };
var rotateLeft = function (e, t) {
        return (e << t) | (e >>> (32 - t));
    },
    addUnsigned = function (e, t) {
        var n = 2147483648 & e,
            r = 2147483648 & t,
            i = 1073741824 & e,
            a = 1073741824 & t,
            e = (1073741823 & e) + (1073741823 & t);
        return i & a ? 2147483648 ^ e ^ n ^ r : i | a ? (1073741824 & e ? 3221225472 ^ e ^ n ^ r : 1073741824 ^ e ^ n ^ r) : e ^ n ^ r;
    },
    F = function (e, t, n) {
        return (e & t) | (~e & n);
    },
    G = function (e, t, n) {
        return (e & n) | (t & ~n);
    },
    H = function (e, t, n) {
        return e ^ t ^ n;
    },
    I = function (e, t, n) {
        return t ^ (e | ~n);
    },
    FF = function (e, t, n, r, i, a, s) {
        return (e = addUnsigned(e, addUnsigned(addUnsigned(F(t, n, r), i), s))), addUnsigned(rotateLeft(e, a), t);
    },
    GG = function (e, t, n, r, i, a, s) {
        return (e = addUnsigned(e, addUnsigned(addUnsigned(G(t, n, r), i), s))), addUnsigned(rotateLeft(e, a), t);
    },
    HH = function (e, t, n, r, i, a, s) {
        return (e = addUnsigned(e, addUnsigned(addUnsigned(H(t, n, r), i), s))), addUnsigned(rotateLeft(e, a), t);
    },
    II = function (e, t, n, r, i, a, s) {
        return (e = addUnsigned(e, addUnsigned(addUnsigned(I(t, n, r), i), s))), addUnsigned(rotateLeft(e, a), t);
    },
    convertToWordArray = function (e) {
        for (var t, n = e.length, r = n + 8, r = 16 * (1 + (r - (r % 64)) / 64), i = Array(r - 1), a = 0, s = 0; s < n; )
            (a = (s % 4) * 8), (i[(t = (s - (s % 4)) / 4)] = i[t] | (e.charCodeAt(s) << a)), s++;
        return (i[(t = (s - (s % 4)) / 4)] = i[t] | (128 << (a = (s % 4) * 8))), (i[r - 2] = n << 3), (i[r - 1] = n >>> 29), i;
    },
    wordToHex = function (e) {
        for (var t = '', n = '', r = 0; r <= 3; r++) t += (n = '0' + ((e >>> (8 * r)) & 255).toString(16)).substr(n.length - 2, 2);
        return t;
    },
    uTF8Encode = function (e) {
        e = e.replace(/\x0d\x0a/g, '\n');
        for (var t = '', n = 0; n < e.length; n++) {
            var r = e.charCodeAt(n);
            r < 128
                ? (t += String.fromCharCode(r))
                : (t =
                      127 < r && r < 2048
                          ? (t += String.fromCharCode((r >> 6) | 192)) + String.fromCharCode((63 & r) | 128)
                          : (t = (t += String.fromCharCode((r >> 12) | 224)) + String.fromCharCode(((r >> 6) & 63) | 128)) +
                            String.fromCharCode((63 & r) | 128));
        }
        return t;
    };
function MD5(e) {
    var t, n, r, i, a, s, o, c, u, g;
    Array();
    for (
        e = uTF8Encode(e), t = convertToWordArray(e), o = 1732584193, c = 4023233417, u = 2562383102, g = 271733878, n = 0;
        n < t.length;
        n += 16
    )
        (o = FF((r = o), (i = c), (a = u), (s = g), t[n + 0], 7, 3614090360)),
            (g = FF(g, o, c, u, t[n + 1], 12, 3905402710)),
            (u = FF(u, g, o, c, t[n + 2], 17, 606105819)),
            (c = FF(c, u, g, o, t[n + 3], 22, 3250441966)),
            (o = FF(o, c, u, g, t[n + 4], 7, 4118548399)),
            (g = FF(g, o, c, u, t[n + 5], 12, 1200080426)),
            (u = FF(u, g, o, c, t[n + 6], 17, 2821735955)),
            (c = FF(c, u, g, o, t[n + 7], 22, 4249261313)),
            (o = FF(o, c, u, g, t[n + 8], 7, 1770035416)),
            (g = FF(g, o, c, u, t[n + 9], 12, 2336552879)),
            (u = FF(u, g, o, c, t[n + 10], 17, 4294925233)),
            (c = FF(c, u, g, o, t[n + 11], 22, 2304563134)),
            (o = FF(o, c, u, g, t[n + 12], 7, 1804603682)),
            (g = FF(g, o, c, u, t[n + 13], 12, 4254626195)),
            (u = FF(u, g, o, c, t[n + 14], 17, 2792965006)),
            (c = FF(c, u, g, o, t[n + 15], 22, 1236535329)),
            (o = GG(o, c, u, g, t[n + 1], 5, 4129170786)),
            (g = GG(g, o, c, u, t[n + 6], 9, 3225465664)),
            (u = GG(u, g, o, c, t[n + 11], 14, 643717713)),
            (c = GG(c, u, g, o, t[n + 0], 20, 3921069994)),
            (o = GG(o, c, u, g, t[n + 5], 5, 3593408605)),
            (g = GG(g, o, c, u, t[n + 10], 9, 38016083)),
            (u = GG(u, g, o, c, t[n + 15], 14, 3634488961)),
            (c = GG(c, u, g, o, t[n + 4], 20, 3889429448)),
            (o = GG(o, c, u, g, t[n + 9], 5, 568446438)),
            (g = GG(g, o, c, u, t[n + 14], 9, 3275163606)),
            (u = GG(u, g, o, c, t[n + 3], 14, 4107603335)),
            (c = GG(c, u, g, o, t[n + 8], 20, 1163531501)),
            (o = GG(o, c, u, g, t[n + 13], 5, 2850285829)),
            (g = GG(g, o, c, u, t[n + 2], 9, 4243563512)),
            (u = GG(u, g, o, c, t[n + 7], 14, 1735328473)),
            (c = GG(c, u, g, o, t[n + 12], 20, 2368359562)),
            (o = HH(o, c, u, g, t[n + 5], 4, 4294588738)),
            (g = HH(g, o, c, u, t[n + 8], 11, 2272392833)),
            (u = HH(u, g, o, c, t[n + 11], 16, 1839030562)),
            (c = HH(c, u, g, o, t[n + 14], 23, 4259657740)),
            (o = HH(o, c, u, g, t[n + 1], 4, 2763975236)),
            (g = HH(g, o, c, u, t[n + 4], 11, 1272893353)),
            (u = HH(u, g, o, c, t[n + 7], 16, 4139469664)),
            (c = HH(c, u, g, o, t[n + 10], 23, 3200236656)),
            (o = HH(o, c, u, g, t[n + 13], 4, 681279174)),
            (g = HH(g, o, c, u, t[n + 0], 11, 3936430074)),
            (u = HH(u, g, o, c, t[n + 3], 16, 3572445317)),
            (c = HH(c, u, g, o, t[n + 6], 23, 76029189)),
            (o = HH(o, c, u, g, t[n + 9], 4, 3654602809)),
            (g = HH(g, o, c, u, t[n + 12], 11, 3873151461)),
            (u = HH(u, g, o, c, t[n + 15], 16, 530742520)),
            (c = HH(c, u, g, o, t[n + 2], 23, 3299628645)),
            (o = II(o, c, u, g, t[n + 0], 6, 4096336452)),
            (g = II(g, o, c, u, t[n + 7], 10, 1126891415)),
            (u = II(u, g, o, c, t[n + 14], 15, 2878612391)),
            (c = II(c, u, g, o, t[n + 5], 21, 4237533241)),
            (o = II(o, c, u, g, t[n + 12], 6, 1700485571)),
            (g = II(g, o, c, u, t[n + 3], 10, 2399980690)),
            (u = II(u, g, o, c, t[n + 10], 15, 4293915773)),
            (c = II(c, u, g, o, t[n + 1], 21, 2240044497)),
            (o = II(o, c, u, g, t[n + 8], 6, 1873313359)),
            (g = II(g, o, c, u, t[n + 15], 10, 4264355552)),
            (u = II(u, g, o, c, t[n + 6], 15, 2734768916)),
            (c = II(c, u, g, o, t[n + 13], 21, 1309151649)),
            (o = II(o, c, u, g, t[n + 4], 6, 4149444226)),
            (g = II(g, o, c, u, t[n + 11], 10, 3174756917)),
            (u = II(u, g, o, c, t[n + 2], 15, 718787259)),
            (c = II(c, u, g, o, t[n + 9], 21, 3951481745)),
            (o = addUnsigned(o, r)),
            (c = addUnsigned(c, i)),
            (u = addUnsigned(u, a)),
            (g = addUnsigned(g, s));
    return (wordToHex(o) + wordToHex(c) + wordToHex(u) + wordToHex(g)).toLowerCase();
}
var b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
function hexToArray(n) {
    var r = [];
    for (let e = 0, t = n.length; e < t; e += 2) r.push(parseInt(n.substr(e, 2), 16));
    return r;
}
function base64ArrayBuffer(e) {
    for (
        var t,
            e = hexToArray(e),
            n = '',
            r = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',
            i = new Uint8Array(e),
            e = i.byteLength,
            a = e % 3,
            s = e - a,
            o = 0;
        o < s;
        o += 3
    )
        n += r[(16515072 & (t = (i[o] << 16) | (i[o + 1] << 8) | i[o + 2])) >> 18] + r[(258048 & t) >> 12] + r[(4032 & t) >> 6] + r[63 & t];
    return (
        1 == a
            ? (n += r[(252 & (t = i[s])) >> 2] + r[(3 & t) << 4] + '==')
            : 2 == a && (n += r[(64512 & (t = (i[s] << 8) | i[1 + s])) >> 10] + r[(1008 & t) >> 4] + r[(15 & t) << 2] + '='),
        n
    );
}
function weAtob(e) {
    if (((e = String(e).replace(/[\t\n\f\r ]+/g, '')), !b64re.test(e)))
        throw new TypeError("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");
    e += '=='.slice(2 - (3 & e.length));
    for (var t, n, r, i = '', a = 0; a < e.length; )
        (t =
            (b64.indexOf(e.charAt(a++)) << 18) |
            (b64.indexOf(e.charAt(a++)) << 12) |
            ((n = b64.indexOf(e.charAt(a++))) << 6) |
            (r = b64.indexOf(e.charAt(a++)))),
            (i +=
                64 === n
                    ? String.fromCharCode((t >> 16) & 255)
                    : 64 === r
                    ? String.fromCharCode((t >> 16) & 255, (t >> 8) & 255)
                    : String.fromCharCode((t >> 16) & 255, (t >> 8) & 255, 255 & t));
    return i;
}
function base64DecodeToHex(e) {
    var t = weAtob(e);
    let n = '';
    for (let e = 0; e < t.length; e++) {
        var r = t.charCodeAt(e).toString(16);
        n += 2 === r.length ? r : '0' + r;
    }
    return n.toUpperCase();
}
function base64Encode(e) {
    return base64ArrayBuffer(stringToHex(e));
}
function stringToHex(e) {
    for (var t = '', n = 0; n < e.length; n++) t += e.charCodeAt(n).toString(16).padStart(2, '0');
    return t;
}
const sm4 = require('miniprogram-sm-crypto').sm4;
function sm4Encrypt(e, t, n) {
    return base64ArrayBuffer(sm4.encrypt(e, stringToByte(t), { mode: 'cbc', iv: stringToByte(n) }));
}
function sm4Decrypt(e, t, n) {
    e = base64DecodeToHex(e);
    return sm4.decrypt(e, stringToByte(t), { mode: 'cbc', iv: stringToByte(n) });
}
function getKeyAndIVFromStr(e) {
    return e.length < 32 ? null : { key: e.substring(0, 16), iv: e.substring(16, 32) };
}
function stringToByte(e) {
    for (var t, n = new Array(), r = e.length, i = 0; i < r; i++)
        65536 <= (t = e.charCodeAt(i)) && t <= 1114111
            ? (n.push(((t >> 18) & 7) | 240), n.push(((t >> 12) & 63) | 128), n.push(((t >> 6) & 63) | 128), n.push((63 & t) | 128))
            : 2048 <= t && t <= 65535
            ? (n.push(((t >> 12) & 15) | 224), n.push(((t >> 6) & 63) | 128), n.push((63 & t) | 128))
            : 128 <= t && t <= 2047
            ? (n.push(((t >> 6) & 31) | 192), n.push((63 & t) | 128))
            : n.push(255 & t);
    return n;
}
function randomKey(e, t) {
    var n,
        r = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split(''),
        i = [];
    for (t = t || r.length, n = 0; n < e; n++) i[n] = r[0 | (Math.random() * t)];
    return i.join('');
}
function timestampStr() {
    return new Date().getTime().toString();
}
function timestamp() {
    return Date.now();
}
function randomUUID() {
    function e() {
        return ((65536 * (1 + Math.random())) | 0).toString(16).substring(1);
    }
    return e() + e() + '-' + e() + '-' + e() + '-' + e() + '-' + e() + e() + e();
}
const DEBUG = !1;
function debugLog(...e) {
    var t;
    DEBUG && ((t = Date.now()), console.log(`[BSDFP][${t}]`, ...e));
}
function validateTime(e) {
    return !!e && new Date().getTime() < e;
}
function getdfp() {
    var e = wx.getStorageSync('dfp'),
        t = gettime();
    return null != e && validateTime(t) && 0 < e.length ? e : null;
}
function setdfp(e) {
    wx.setStorageSync('dfp', e);
}
function gettime() {
    return wx.getStorageSync('time');
}
function settime(e) {
    wx.setStorageSync('time', e);
}
function setCookieCode(e) {
    wx.setStorageSync('cookieCode', e);
}
function getCookieCode() {
    return wx.getStorageSync('cookieCode');
}
function setCCID(e) {
    wx.setStorageSync('bs_ccid', e);
}
function getCCID() {
    return wx.getStorageSync('bs_ccid');
}
function setLocalDfpUUID(e) {
    wx.setStorageSync('bs_dfp_uuid', e);
}
function getLocalDfpUUID(e) {
    return wx.getStorageSync('bs_dfp_uuid');
}
const bs_config_key = 'bs_config',
    bs_ccid_enable_key = 'bs_ccid_enable',
    bs_ccid_expiration_key = 'bs_ccid_expiration';
var currentConfig,
    ccidEnable,
    expiration,
    surl = '',
    custID = '',
    bs_public_key = '';
function setCustID(e) {
    custID = e;
}
function getCustID() {
    return custID;
}
function setUrl(e, t) {
    (surl = e), getServerConfig(t);
}
function getUrl() {
    return surl;
}
function getAppID() {
    var e = getConfig();
    return e ? e.ccAppID : null;
}
function getMerID() {
    var e = getConfig();
    return e ? e.merID : null;
}
function setSM2PublicKey(e) {
    bs_public_key = e;
}
function getSM2PublickKey() {
    return bs_public_key;
}
function getConfig() {
    var e;
    return null != currentConfig ? currentConfig : (e = wx.getStorageSync(bs_config_key)) && '' != e ? e : null;
}
function getServerConfig(t) {
    wx.request({
        url: surl + '/public/sdkConfig/get?channel=WMP&custID=' + custID + '&uuid=' + getDfpUUID(),
        method: 'GET',
        success(e) {
            e = e.data;
            e ? ((currentConfig = e), wx.setStorageSync(bs_config_key, e), t.success(e)) : t.fail('get config fail');
        },
        fail(e) {
            t.fail('get config fail');
        },
    });
}
function getCcidEnable() {
    return null != ccidEnable ? ccidEnable : wx.getStorageSync(bs_ccid_enable_key);
}
function setCcidEnable(e) {
    (ccidEnable = e), wx.setStorageSync(bs_ccid_enable_key, e);
}
function getExpiration() {
    var e;
    return null != expiration ? expiration : '' != (e = wx.getStorageSync(bs_ccid_expiration_key)) && 0 < e ? e : 604800;
}
function setExpiration(e) {
    (expiration = e), wx.setStorageSync(bs_ccid_expiration_key, e);
}
function checkConfig(t) {
    getConfig()
        ? t(!0)
        : getServerConfig({
              success: e => {
                  t(!0);
              },
              fail: e => {
                  t(!1);
              },
          });
}
