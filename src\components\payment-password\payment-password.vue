<template>
    <div v-if="show || showErrorBox" class="payment-password">
        <!-- 自定义密码输入弹窗 -->
        <u-popup v-model="isShow" mode="center" negative-top="200" border-radius="20" :mask="true">
            <div class="payment-box">
                <img @click="close" class="payment-box-close" src="~@/static/black-close.png" />
                <div class="payment-box-title">
                    <span class="payment-title-text">请输入支付密码</span>
                </div>
                <div class="payment-box-content">
                    <!-- <span class="payment-content-merchant">{{ merchant }}</span> -->
                    <div class="payment-content-amount">
                        <span class="payment-amount-sign">¥</span>
                        <span class="payment-amount-text">{{ amount }}</span>
                    </div>
                </div>
                <div class="payment-box-footer">
                    <u-message-input
                        ref="uMessageInputRef"
                        :maxlength="maxlength"
                        :width="100"
                        :dot-fill="true"
                        :breathe="false"
                        :disabled-keyboard="true"
                        :value="password"
                    ></u-message-input>
                </div>
            </div>
        </u-popup>
        <u-modal
            @confirm="reEnter"
            @cancel="forgetPwd"
            v-model="showErrorBox"
            mode="center"
            border-radius="20"
            confirm-text="重试"
            cancel-text="忘记密码"
            :width="660"
            :show-title="false"
            :cancel-style="{
                color: '#333',
                'font-size': '18px',
                'font-weight': 'bold',
            }"
            :confirm-style="{
                color: '#FF8200',
                'font-size': '18px',
                'font-weight': 'bold',
            }"
            :show-cancel-button="true"
        >
            <view class="payment-error">
                <span>支付密码错误请重试</span>
            </view>
        </u-modal>
        <div class="password-keyboard">
            <u-number-keyboard
                ref="uKeyboard"
                mode="number"
                @change="keyboardChange"
                @backspace="keyboardBackspace"
                :dot-enabled="false"
                :random="isShowKeyboard"
                v-if="isShowKeyboard"
                safe-area-inset-bottom
            ></u-number-keyboard>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        showError: {
            // 是否显示支付密码输入错误弹窗
            type: Boolean,
            default: false,
        },
        merchant: {
            // 商户名称
            type: String,
            default: '昆仑加油',
        },
        amount: {
            // 支付金额
            type: Number,
        },
        maxlength: {
            // 密码最大长度
            type: Number,
            default: 6,
        },
    },
    options: { styleIsolation: 'shared' },
    data() {
        return {
            isShowKeyboard: false, // 是否显示自定义键盘
            password: '', // 支付需要输入的密码
            showErrorBox: this.showError,
        };
    },
    watch: {
        password(val) {
            val = String(val);
            if (val.length > this.maxlength) return;
            if (val.length == this.maxlength) {
                this.$emit('completed', {
                    value: val,
                });
            }
        },
        show(val) {
            if (!val) {
                this.password = '';
            }
            this.isShowKeyboard = val;
        },
        showError(val, oldVal) {
            if (oldVal !== val) {
                this.showErrorBox = val;
            }
        },
        showErrorBox(val) {
            this.$emit('update:showError', val);
        },
    },
    methods: {
        // 重试
        reEnter(e) {
            this.$emit('reEnter');
        },
        // 忘记密码
        forgetPwd(e) {
            this.$emit('forgetPwd');
        },
        // 关闭密码框
        close(e) {
            this.$emit('close');
        },
        // 密码输入事件
        keyboardChange(text) {
            this.password += text;
            this.$emit('change', {
                value: this.password,
            });
        },
        // 键盘退格事件
        keyboardBackspace(e) {
            const pwd = this.password;
            this.password = pwd.substring(0, pwd.length - 1);
            this.$emit('change', {
                value: this.password,
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.payment-password {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    // 自定义支付密码弹窗
    .payment-box {
        position: relative;
        width: 330px;
        height: 225px;
        overflow: hidden;
        .payment-box-close {
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 20px;
            padding: 10px;
        }
        .payment-box-title {
            margin-top: 20px;
            text-align: center;
            .payment-title-text {
                color: #333333;
                font-size: 18px;
                font-weight: bold;
            }
        }
        .payment-box-content {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 15px;
            .payment-content-merchant {
                color: #909090;
                font-size: 15px;
                font-weight: 400;
            }
            .payment-content-amount {
                line-height: 42px;
                color: #333;
                font-weight: bold;
                .payment-amount-sign {
                    font-size: 15px;
                }
                .payment-amount-text {
                    font-size: 30px;
                }
            }
        }
        .payment-box-footer {
            height: 50px;
            margin: 20px 15px 0;
            & ::v-deep .u-char-item {
                margin: 0 -1px -1px 0; /** 避免左右边框重叠 */
                border-color: #dcdcdc !important;
                border-width: 2px;
            }
        }
    }
    .password-keyboard {
        background: red;
        ::v-deep .u-keyboard {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: #fff;
            padding-bottom: env(safe-area-inset-bottom);
            z-index: 100003;
        }
    }
    .payment-error {
        width: 100%;
        padding: 49px 93px;
        text-align: center;
        font-size: 16px;
        color: #333;
        font-weight: bold;
    }
}
</style>
