<template>
    <div class="main-tabs fl-row">
        <div
            v-for="(tab, index) in tabs"
            :key="index"
            @click="changeCode(tab.value)"
            class="font-18 weight-500 f-1"
            style="text-align: center"
            :class="{ 'color-FF6B2C': value === tab.value }"
            >{{ tab.label }}</div
        >
        <zj-old-account v-if="isTransfer"></zj-old-account>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    name: 'main-tabs',
    props: {
        value: {
            type: String,
            default: 'membershipCodeFlag',
        },
    },
    components: {},
    data() {
        return {
            tabs: [],
        };
    },
    created() {
        if (this.isHarmony) {
            this.tabs = [{ label: '会员码', value: 'membershipCodeFlag' }];
        } else {
            this.tabs = [
                { label: '会员码', value: 'membershipCodeFlag' },
                // #ifdef MP-MPAAS
                { label: '付款码', value: 'paymentCodeFlag' },
                // #endif
            ];
        }
    },
    mounted() {
        this.changeCode = this.$sKit.commonUtil.throttleUtil(this.changeCode);
    },
    methods: {
        /**
         * @description  : 切换会员码or付款码
         * @param         {String} value -默认展示会员码
         * @param         {Function} eWalletNormal -昆仑e享卡功能限制
         * @param         {Function} changeCode -执行分发
         * @return        {*}
         */
        changeCode(tab) {
            console.log(tab, '切换tab触发');
            if (tab == this.value) return;
            if (tab == 'paymentCodeFlag') {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        // this.value = tab;
                        this.$emit('changeCode', tab);
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    walletAddParams: {
                        refer: 'r09',
                    },
                });
            } else {
                // this.value = tab;
                this.$emit('changeCode', tab);
            }
        },
    },
    computed: {
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
};
</script>

<style lang="scss" scoped>
.main-tabs {
    width: 100%;
    height: 44px;
    margin-top: 26px;
    // padding-top: 26px;
    padding-left: 50px;
    padding-right: 50px;
}
</style>
