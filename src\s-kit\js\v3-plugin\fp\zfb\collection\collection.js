const comp = require("../utils/compareVersion.js");
const encrypt = require('../utils/encrypt.js');
const network = require('../network/network.js');
const storage = require('../storage/storage.js');
const formatLocation = require('../utils/format-location.js');

var sdkVersion = "3.1.1";
var baseVersion = "1.0.0";
var platform = "AMP";
var collectElements = new Map();

function getCollection(openId, custid, url, callback) {
	if (url == "") {
		callback.fail("fail:url 不能为空");
		return;
	}
	if (custid == "") {
		callback.fail("fail:custid 不能为空");
		return;
	}
	collectElements["custID"] = custid;
	collectElements["sdkVersion"] = sdkVersion;
	//基础版本库号
	collectElements["baseSDKVersion"] = my.SDKVersion;
	baseVersion = my.SDKVersion;
  collectElements["platform"] = platform;
  //用户信息-openId
  collectElements["userInfo"] = encrypt.MD5(openId);
	if (storage.getCookieCode()) {
		collectElements["cookieCode"] = storage.getCookieCode();
	}
	//采集系统信息
	getSystemInfo().then(function(res) {
		console.log(res);
		return getLocation();
	}).then(function(res) {
		console.log(res);
		//网络状态
		return getNetworkType();
	}).then(function(res) {
		console.log(res);
		return getScreenBrightness();
	}).then(function(res) {
		console.log(res);
		return getBatteryInfo();
	}).then(function(res) {
		console.log(res);
		return getRunScene();
	}).then(function(res) {
		console.log(res);
		let array = sortKey(collectElements);
		array.sort();
    var dataMap = new Map();
		for (let p in array) { //遍历json对象的每个key/value对,p为key
			if (collectElements[array[p]] != undefined && collectElements[array[p]] != "" && collectElements[array[p]] != "null") {
        dataMap[array[p]]=collectElements[array[p]].toString().replace(" ", "");
			}
    }
    console.log(dataMap);
		network.sendRequest(dataMap, url, callback);
	});
}

function getSystemInfo() {
	return new Promise(function(r, j) {
		if (my.getSystemInfo) {
			my.getSystemInfo({
				success: (res) => {
					//设备型号
					var model = res.model;
          collectElements["model"] = model;
          collectElements["timestamp"] = Date.parse(new Date());
					//dpi
					var pixelRatio = res.pixelRatio;
					collectElements["devicePixelRatio"] = pixelRatio;
					//屏幕宽度
					var screenWidth = res.screenWidth;
					collectElements["screenWidth"] = screenWidth;
					//屏幕高度
					var screenHeight = res.screenHeight;
					collectElements["screenHeight"] = screenHeight;
					//手机品牌
					var brand = res.brand;
					collectElements["brand"] = brand;
					//MD5 计算出smartCode
					let smartID = encrypt.MD5((model + pixelRatio + screenWidth + screenHeight + brand).replace(" ",""));
					collectElements["alipaySmartID"] = smartID;
					//系统名 Android 
					var systemPlatform = res.platform;
					collectElements["systemPlatform"] = systemPlatform;
					//窗口宽度
					var windowWidth = res.windowWidth
					collectElements["windowWidth"] = windowWidth;
					//窗口高度
					var windowHeight = res.windowHeight
					collectElements["windowHeight"] = windowHeight;
					//支付宝设置的语言
					var language = res.language
					collectElements["language"] = language;
					//支付宝版本号
					var version = res.version
					collectElements["osVersion"] = version;
					//设备磁盘容量
					var storage = res.storage
					collectElements["storage"] = storage;
					//操作系统版本
					var system = res.system
					collectElements["system"] = system;
					//标题栏高度
					var titleBarHeight = res.titleBarHeight
					collectElements["titleBarHeight"] = titleBarHeight;
					//	状态栏高度
					var statusBarHeight = res.statusBarHeight
					collectElements["statusBarHeight"] = statusBarHeight;
					//用户设置字体大小
					var fontSizeSetting = res.fontSizeSetting
					collectElements["fontSizeSetting"] = fontSizeSetting;
					//当前运行的客户端，当前是支付宝则有效值是 "alipay"
					var app = res.app
					collectElements["app"] = app;
					//设备是否开启无障碍
					var screenReaderEnabled = res.screenReaderEnabled
					if(screenReaderEnabled){
						collectElements["screenReaderEnabled"] = "1";
					}else{
						collectElements["screenReaderEnabled"] = "0";
					}
					r(res);
				},
				fail: function fail(e) {
					r(e);
				}
			});
		} else {
			r("SystemInfo error");
		}
	});

}

//获取经纬度
function getLocation() {
	return new Promise(function(r, j) {
		if (my.getLocation) {
			my.getLocation({
				success(res) {
					collectElements["coordinates"] = "{"+res.latitude+","+res.longitude+"}";
					r(res);
				},
				fail(e) {
					r(e);
				},
			});
		} else {
			r("Location error");
		}
	});
}

//获取网络状态
function getNetworkType() {
	return new Promise(function(r, j) {
		if (my.getNetworkType) {
			my.getNetworkType({
				success(res) {
					collectElements["networkType"] = res.networkType;
					r(res);
				},
				fail(e) {
					r(e);
				},
			});
		} else {
			r("NetworkType error");
		}
	});
}

//获取屏幕亮度
function getScreenBrightness() {
	return new Promise(function(r, j) {
		if (my.getScreenBrightness) {
			my.getScreenBrightness({
				success(res) {
					collectElements["screenbrightness"] = res.brightness;
					r(res);
				},
				fail(e) {
					r(e);
				},
			});
		} else {
			r("ScreenBrightness error");
		}
	});
}

//获取电量信息
function getBatteryInfo() {
	return new Promise(function(r, j) {
		if (my.getBatteryInfo) {
			my.getBatteryInfo({
				success: (res) => {
					collectElements["batteryLevel"] = res.level;
					if(res.isCharging){
						collectElements["batteryStatus"] = "1";
					}else{
						collectElements["batteryStatus"] = "0";
					}
					r(res)
				},
				fail: (error) => {
					r(error)
				},
			})
		} else {
			r("batteryInfo error")
		}
	});
}


//小程序当前运行的版本
function getRunScene() {
	return new Promise(function(r, j) {
		if (my.getRunScene) {
			my.getRunScene({
				success: (res) => {
					collectElements["runScene"] = res.envVersion;
					r(res);
				},
				fail: (e) => {
					r(e);
				},
			});
		} else {
			r("runScene error");
		}
	});
}

//对采集要素的key排序，排除
function sortKey(collect) {
	var array = [];
	for (var p in collect) {//遍历json对象的每个key/value对,p为key
		array.push(p);
	}
	return array;
}

module.exports = {
	getCollection: getCollection
}


