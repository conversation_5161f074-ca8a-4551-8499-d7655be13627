import { realNameAuth, initRealPersonIdentify, realPersonIdentify } from './v3-http/https3/oilCard/index';
import { originReturnUrl, v3sign, clientCode } from '../../../project.config';


// #ifdef H5-CLOUD 
// H5实名--初始化实人认证--人脸验证-实名认证方法
async function startVerification(personInfo) {
    return new Promise(async (resolve, reject) => {
        // 实名认证
        await realNameAuthFun(personInfo)
            .then(async realNameAuthRes => {
                // 合并参数
                let params = Object.assign(personInfo, realNameAuthRes);
                // 初始化实人认证
                return await initFaceAuthentication(params);
            })
            .then(async initFaceAuthenticationRes => {
                // H5人脸验证
                resolve(initFaceAuthenticationRes)
                // return await H5Face(initFaceAuthenticationRes);
            })
            // .then(async initFaceAuthenticationRes => {
            //     let params = {
            //         ...initFaceAuthenticationRes,
            //         ...personInfo,
            //     };
            //     // 实名认证
            //     return await realNameAuthentication(params);
            // })
            // .then(async result => {
            //     return await resolve(result);
            // })

            .catch(error => {
                return reject(error); // 返回失败的结果
                uni.showToast({ title: 'startVerification报错' + error });
            });
    });
}

/**
 * @description  : 实名认证  (同意开通开通昆仑e享卡)
 * @param         {String} realName -用户在上个页面输入的姓名
 * @param         {String} idNo -用户在上个页面输入的身份证号
 * @param         {String} idType -身份证件类型 1:身份证 2:军人身份证件 3:台胞证 4:港澳通行证 5:外国人永久居留身份证 6:护照
 * @param         {String} type - 7-绑卡 1-电子账户注册 2-忘记密码 11-风控校验
 * @param         {String} authInfo - Type=7必传，传入验证码接口传过来的字串
 * @return        {*}
 */
function realNameAuthFun(personInfo) {
    return new Promise(async (resolve, reject) => {
        let params = {
            realName: personInfo.name,
            idType: '1',
            idNo: personInfo.idNo,
            authInfo: personInfo.authInfo || '',
            type: personInfo.type || '1',
            // areaCode: this.locationInfo.cityCode || this.personInfo.areaCode
            // areaCode: this.personInfo.areaCode
        };
        let res = await realNameAuth(params);
        // uni.showModal({
        //     title: '提示',
        //     content: '初始化实人认证接口返回',
        //     success: rv => {
        //         if (rv.confirm) {
        if (res.data.authInfo && res.success) {
            // return
            console.log('初始化实人认证接口返回', res.data);
            resolve(res.data);
        }
        //         }
        //     },
        // });

    });
}
/**
 * @description  :  人脸验证初始化
 * @param         {String} returnUrl -业务回跳地址；PC或H5接入时 必传，APP接入时为空
 * @param         {String} metaInfo -MetaInfo环境参数，需要通过JS获取或SDK获取
 * @param         {String} verifyMode -认证接入方式：1—APP接入；2—PC或H5接入；
 * @param         {Function} aliMetaInfo -阿里人脸采集MetaInfo
 * @param         {string} verifyUnique -身份认证唯一标识
 * @param         {string} certifyId -实人认证三方系统的标识
 * @param         {string} certifyUrl -第三方认证地址
 * @param         {Function} zjShowModal -全局自定义弹窗
 * @return        {*}
 */
function initFaceAuthentication(param) {
    return new Promise(async (resolve, reject) => {
        // 在调用实人认证服务端发起认证请求时需要传入该MetaInfo值
        var MetaInfo = window.getMetaInfo();
        let CLOUDTokenInfo = await uni.getStorageSync('tokenInfo');
        let cloudParams = {
            token: CLOUDTokenInfo?.accessToken,
            userId: CLOUDTokenInfo?.memberNo,
            v3sign: v3sign,
            clientCode: clientCode
        }
        let params = {
            returnUrl: `${originReturnUrl}?data=${encodeURIComponent(JSON.stringify(cloudParams))}`,
            metaInfo: JSON.stringify(MetaInfo),
            verifyMode: '2',
        };
        // 获取跳转URL和CertifyId
        let res = await initRealPersonIdentify(params);
        if (res && res.success) {
            if (JSON.stringify(res.data) !== '{}') {
                console.log(res.data, '=====初始化实人认证接口返回结果');
                let params = {
                    ...param,
                    ...res.data,
                };
                resolve(params);
            }
        }
    });
}


/**
 * @description  :  实人认证
 * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
 * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
 * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
 * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
 * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
 * @param         {string} idNo: 用户身份证号
 * @return        {*}
 */
function realNameAuthentication(wxOrZfbFaceValue) {
    return new Promise(async (resolve, reject) => {
        // #ifdef H5-CLOUD
        let params = {
            // 认证校验码，初始化实人认证接口返回的数据。
            authInfo: wxOrZfbFaceValue.authInfo,
            // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡； 10-资金转出；
            type: wxOrZfbFaceValue.type || 1,
            // 认证接入方式：1—APP接入；2—PC或H5接入；
            verifyMode: '2',
            // 当type=1时该字段为必填项。
            idNo: wxOrZfbFaceValue.idNo,
            // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
            verifyUnique: wxOrZfbFaceValue.verifyUnique,
            // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
            certifyId: wxOrZfbFaceValue.certifyId,
            gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
        };
        console.log(params, 'H5传入实人认证接口的参数');
        // #endif
        let res = await realPersonIdentify(params);
        if (res.success && res.data.authInfo) {
            console.log(params, '实人认证方法接口返回结果');
            resolve(res);
        }
    });
}
export default {
    startVerification,
    realNameAuthentication
};
// #endif
