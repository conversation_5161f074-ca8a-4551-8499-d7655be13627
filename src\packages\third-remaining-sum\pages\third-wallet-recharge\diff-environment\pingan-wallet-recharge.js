import { mapGetters, mapState } from 'vuex';
export default {
    data() {
        return {
            payPlugin: {},
        };
    },
    computed: {},
    async mounted() {},
    onLoad() {},
    methods: {
        async getRechargeMethod() {
            let params = {
                areaCode: this.walletInfo.addressNo,
            };
            const res = await this.payPlugin.GetRechargeTypeList(params);
            console.log(res, '获取支付方式');
            if (!res) {
                return;
            }

            if (res.code === 'PAY_SUCCESS' && res.data.length > 0) {
                console.log('RechargeTypeList------', res.data);
                let newArr
                newArr = isHarmony ? res.data.filter(item => item.payType == 2) : res.data;
                this.list = newArr.map(item => {
                    let img = '';
                    switch (Number(item.payType)) {
                        case 1:
                            img = require('../../../images/wx.png');
                            break;
                        case 2:
                            img = require('../../../images/zfb.png');
                            break;
                        case 8:
                            img = require('../../../images/yun_unionpay.png');
                            break;
                        case 4:
                            img = require('../../../images/kunpeng.png');
                            break;
                        default:
                            break;
                    }
                    item.img = img || '';
                    return item
                });
                // let newArr = res.data.filter(item => item.payType !== 2);
                // console.log(newArr);

                if (this.list.length > 0) {
                    this.payType = this.list[0].payType;
                }
                // res.data.map(item => {
                //   // 目前微信只有微信支付方式
                //   if (item.payType === '1') {
                //     this.paymentMethod = item.payType
                //   }
                // })
            } else {
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
            this.payingFlag = false;
        },
        /**
         * 发起充值支付
         * @param areaCode 地区编码
         * @param bizOrderNo 业务订单编号
         * @param rcvAmt 应收总金额
         * @param realAmt 支付金额
         */
        async callUpPayment(resMP) {
            uni.showLoading({
                title: '加载中',
            });
            console.log('🚀 ~ file: zfb-wallet-recharge.js:80 ~ callUpPayment ~ resMP:', resMP);
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: resMP.data.prePayId,
                rcvAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                realAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                // 3.0.4风控字段
                extendFiled: JSON.stringify({
                    dfp: '',
                    gps:
                        this.riskManagementLonV3 && this.riskManagementLatV3
                            ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                            : '',
                    gpsProvince: '',
                    gpsCity: '',
                    gpsArea: '',
                }),
                openId: this.openId,
                payType: resMP.data.paymentType + '',
            };
            const res = await this.payPlugin.RechargePay(params);
            uni.hideLoading();
            this.payingFlag = false;
            if (res.code === 'PAY_SUCCESS') {
                this.otherAmount = '';
                this.$sKit.layer.useRouter(
                    '/packages/third-remaining-sum/pages/third-charge-result/main',
                    { orderId: resMP.data.prePayId },
                    'navigateTo',
                );
            } else {
                this.closePopup();
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
    },
};
