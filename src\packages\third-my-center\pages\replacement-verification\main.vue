<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas bg-fff">
        <!-- 修改登录密码 -->

        <div>
            <zj-navbar :height="44" title="修改登录密码"></zj-navbar>
            <div style="padding: 16px">
                <div class="form">
                    <div class="input">
                        <input
                            :password="!showImg1"
                            placeholder="请输入原密码"
                            placeholder-style="font-size: 14px;color: #999999;"
                            v-model.trim="originPwd"
                            :maxlength="20"
                            :minlength="6"
                        />
                        <div class="eyeImg_div" @click="showImg1 = !showImg1">
                            <img v-if="!showImg1" src="../../image/close_img.png" alt />
                            <img v-else src="../../image/eyes_open.png" alt />
                        </div>
                    </div>
                    <div class="input">
                        <input
                            :password="!showImg2"
                            placeholder="请输入新密码（6~20位字母数字组合）"
                            placeholder-style="font-size: 14px;color: #999999;"
                            v-model.trim="userPwd"
                            :maxlength="20"
                            :minlength="6"
                        />
                        <div class="eyeImg_div" @click="showImg2 = !showImg2">
                            <img v-if="!showImg2" src="../../image/close_img.png" alt />
                            <img v-else src="../../image/eyes_open.png" alt />
                        </div>
                    </div>

                    <div class="input">
                        <input
                            :password="!showImg3"
                            placeholder="请确认新密码"
                            placeholder-style="font-size: 14px;color: #999999;"
                            v-model.trim="sureuserPwd"
                            :maxlength="20"
                            :minlength="6"
                        />
                        <div class="eyeImg_div" @click="showImg3 = !showImg3">
                            <img v-if="!showImg3" src="../../image/close_img.png" alt />
                            <img v-else src="../../image/eyes_open.png" alt />
                        </div>
                    </div>
                </div>
                <div class="btn primary-btn" @click="complete()">完成</div>
                <div class="forgot_div" @click="toForgotPassword()">忘记密码</div>
            </div>

            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { appjsonusermodifyPwd, passwordUpdate } from '../../../../s-kit/js/v3-http/https3/user';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    computed: {},
    data() {
        return {
            msg: 'msg',
            sureuserPwd: '',
            originPwd: '',
            userPwd: '',
            isShowOriginPwd: '',
            token: '',
            showImg1: false,
            showImg2: false,
            showImg3: false,
            newPassword: false, //判断是否为3.0接口的人脸/指纹开通
        };
    },
    onLoad(options) {},
    async created() {
        // #ifdef MP-MPAAS
        this.newPassword = await this.$cnpcBridge.judgeProtocolCall('3.6.6');
        // #endif
        await this.$cnpcBridge.isCutScreen(true)
    },
    mounted() {},
    methods: {
        toForgotPassword() {
            let url = '/packages/third-my-center/pages/forgot-login-password/main';
            let params = { page: 3 };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 修改登录密码确认
        async complete() {
            if (!this.originPwd) {
                uni.showToast({
                    title: '请输入原密码!',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.userPwd || !this.$sKit.test.checkPassWord(this.userPwd)) {
                uni.showToast({
                    title: '请输入正确格式的新密码!',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.sureuserPwd || !this.$sKit.test.checkPassWord(this.sureuserPwd)) {
                uni.showToast({
                    title: '请输入正确格式的确认新密码!',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.sureuserPwd != this.userPwd) {
                uni.showToast({
                    title: '两次输入的密码不一致!',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.originPwd == this.userPwd) {
                uni.showToast({
                    title: '新密码不能与原密码重复!',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let oldpwd = this.originPwd;
            let newpwd = this.userPwd;
            if (this.newPassword) {
                let params = {
                    oldPassword: await this.$cnpcUtils.encryption(oldpwd),
                    newPassword: await this.$cnpcUtils.encryption(newpwd),
                };
                let res = await passwordUpdate(params);
                if (res.success) {
                    this.showToastAndLogout('密码修改成功');
                }
                // else {
                //     this.showToast('修改密码失败', res.message);
                // }
            } else {
                let params = {
                    originPwd: await this.$cnpcUtils.encryption(oldpwd),
                    userPwd: await this.$cnpcUtils.encryption(newpwd),
                };
                let res = await appjsonusermodifyPwd(params);
                if (res && res.result === 'success') {
                    this.showToastAndLogout('密码修改成功');
                }
                // else {
                //     this.showToast('修改密码失败', res.info);
                // }
            }
            console.log(oldpwd + '-' + newpwd + '-' + this.sureuserPwd + 'passwordUpdate----params=====');
        },
        showToast(message, info = '') {
            uni.showToast({
                title: info ? info : message,
                icon: 'none',
                duration: 2000,
            });
        },
        showToastAndLogout(message) {
            this.showToast(message);
            setTimeout(() => {
                this.$sKit.commonUtil.logoutFun();
            }, 3000);
        },
    },
    async beforeDestroy() {
         await this.$cnpcBridge.isCutScreen(false)
    },
    async destroyed() {
       await this.$cnpcBridge.isCutScreen(false)
    },
};
</script>

<style scoped lang="scss">
.form {
    .input {
        height: 44px;
        background: #f7f7fb;
        margin-bottom: 12px;
        padding: 0 12;
        display: flex;
        align-items: center;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        input {
            width: 100%;
            flex: 1;
            background: #f7f7fb;
            height: 44px;
            position: absolute;
            top: 0;
            left: 0;
            padding-left: 15px;
        }

        // z-index 1
        .eyeImg_div {
            height: 44x;
            width: 22px;
            // z-index 2
            position: absolute;
            right: 14px;

            img {
                width: 20px;
                height: 15px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        // div {
        //   width: 94px;
        //   height: 30px;
        //   font-size: 14px;
        //   line-height: 30px;
        //   text-align: center;
        //   font-weight: 400;
        //   color: #e64f22;

        //   border-left: 1px solid #d3d3d3;
        // }
    }
}

.btn {
    height: 44px;
    line-height: 44px;
    color: #fff;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    border-radius: 8px;
}
.forgot_div {
    margin-top: 15px;
    color: #999;
    float: right;
}
</style>
