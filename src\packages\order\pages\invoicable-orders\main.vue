<template>
    <div class="detail-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="可开票订单"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div class="tip">开具发票仅支持3个月内订单</div>
        <scroll-view
            :style="{
                height: 'calc(100vh - 134px - env(safe-area-inset-bottom) - ' + navh + 'px)',
                marginTop: '30rpx',
            }"
            scroll-y="true"
            @scrolltolower="lower"
        >
            <div class="content">
                <div class="order" v-for="(item, index) in fuelList" :key="item.id">
                    <div class="order-list">
                        <checkbox color="#FF8200" @click="checkboxChange(item)" :checked="item.checked" />
                        <div class="order-list" @click="detailtap(item)">
                            <img
                                class="order-type"
                                :src="'@/static/' + (item.payMode == 'wx' ? 'pay-wx.png' : 'pay-oilcard.png')"
                                mode=""
                            />
                            <div class="order-left">
                                <div class="time">{{ item.orderTime }}</div>
                                <div class="title">{{ item.stationName }}</div>
                                <div class="info">{{ item.oils }}#汽油 ({{ item.unitPrice }}元/升) x{{ item.quantity }}L </div>
                            </div>
                            <div class="order-right">
                                <div class="right-money">
                                    <span>¥{{ item.realAmt + (isInteger(item.realAmt) ? '.00' : '') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </scroll-view>
        <div class="footer">
            <div class="footer-text">
                <span class="color">{{ orderNum }}</span
                >笔订单，共<span class="color">{{ orderPrice }}</span
                >元
            </div>
            <div class="footer-select">
                <u-checkbox active-color="#FF8200" shape="circle" v-model="checkedPage" @change="selectAll(0)">本页全选</u-checkbox>
                <u-checkbox active-color="#FF8200" shape="circle" v-model="checkedAll" @change="selectAll(1)">全选</u-checkbox>
                <div class="footer-btn" @click="nextStep">下一步</div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { mayBillingOrder } from '@/api/home.js';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            fuelList: [], //全部订单
            page: 1, // 分页页数
            pageSize: 10, //分页数
            navh: 64, // 导航高度
            checkedPage: false, //本页全选
            checkedAll: false, //全选
            orderNum: 0, //订单数量
            orderPrice: 0, //订单价格
            loadMortState: true, //分页状态
        };
    },
    onLoad(option) {
        this.navh = this.$GlobalData.systemInfo.statusBarHeight + 40;
        this.fuelList = [];
        this.getFuelList();
    },
    onReady() {},
    onUnload() {},
    computed: {},
    methods: {
        //多选事件
        checkboxChange(e) {
            this.orderNum = 0;
            this.orderPrice = 0;
            e.checked = !e.checked;
            if (!e.checked) {
                this.checkedPage = false;
                this.checkedAll = false;
            }
            this.fuelList.forEach(item => {
                if (item.checked) {
                    ++this.orderNum;
                    this.orderPrice += item.realAmt;
                }
            });
            this.orderPrice = this.orderPrice.toFixed(2);
        },
        //选择 本页 0 &&  全部 1
        selectAll(val) {
            if (val == 0 && this.checkedAll) {
                this.checkedAll = false;
            } else if (val == 1 && this.checkedPage) {
                this.checkedPage = false;
            }
            let flag = this.fuelList.every(item => item.checked == true);
            let checkedFlag = [this.checkedPage, this.checkedAll].some(item => item == true);
            if (flag && checkedFlag) {
                this.fuelList.forEach(item => {
                    item.checked = false;
                });
                this.orderNum = 0;
                this.orderPrice = 0;
            } else {
                this.orderPrice = 0;
                this.fuelList.forEach(item => {
                    item.checked = true;
                    this.orderPrice += item.realAmt;
                });
                this.orderNum = this.fuelList.length;
                this.orderPrice = Number(this.orderPrice.toFixed(2));
            }
            if (val == 1) {
                if (this.loadMortState) {
                    this.getAllList();
                }
            }
        },
        //请求数据
        async getAllList() {
            if (!this.loadMortState) {
                this.orderPrice = 0;
                this.fuelList.forEach(item => {
                    item.checked = true;
                    this.orderPrice += item.realAmt;
                });
                this.orderNum = this.fuelList.length;
                this.orderPrice = Number(this.orderPrice.toFixed(2));
                return;
            }
            ++this.page;
            await this.getFuelList();
            await this.getAllList();
        },
        //触底加载
        async lower(e) {
            ++this.page;
            await this.getFuelList();
        },
        //下一步 跳转开票
        nextStep() {
            let selectOrderArr = [];
            this.fuelList.forEach(item => {
                if (item.checked == true) {
                    selectOrderArr.push(item);
                }
            });
            if (selectOrderArr.length > 0) {
                uni.navigateTo({
                    url:
                        '/packages/invoice-center/pages/add-invoice/main?list=' +
                        encodeURIComponent(JSON.stringify(selectOrderArr)) +
                        '&ordertype=""',
                });
            } else {
                uni.showToast({
                    title: '请选择开票',
                    icon: 'none',
                });
            }
        },
        // 点击进入详情页
        clickOrderDetail(item) {
            uni.navigateTo({
                url: '/packages/order/pages/order-detail-before/main?id=' + item.id,
            });
        },
        // 判断是否是整数
        isInteger(obj) {
            return Number.isInteger(obj);
        },
        // 单个开发票
        typetap(index) {
            let selectOrderArr = [this.fuelList[index]];
            uni.navigateTo({
                url:
                    '/packages/invoice-center/pages/add-invoice/main?list=' +
                    encodeURIComponent(JSON.stringify(selectOrderArr)) +
                    '&ordertype=' +
                    (this.active == 1 ? 'recharge' : ''),
            });
        },
        // 详情按钮点击事件
        detailtap(item) {
            uni.navigateTo({
                url: '/packages/order/pages/order-detail-before/main?id=' + item.id,
            });
        },
        //获取加油订单列表
        async getFuelList() {
            if (!this.loadMortState) return;
            let res = await mayBillingOrder({
                pageNo: this.page,
                pageSize: this.pageSize,
            });
            if (res.status == 0 && res.data.length > 0) {
                if (res.data.length < this.pageSize) {
                    this.loadMortState = false;
                }
                res.data.map((val, index) => {
                    val.checked = false;
                });
                this.fuelList = this.fuelList.concat(res.data);
            }
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 12px;
.color {
    color: $btn-color;
}
.detail-center {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    box-sizing: border-box;

    .tip {
        width: 100%;
        height: 30px;
        color: $bg-color;
        line-height: 30px;
        text-align: center;
        background: $btn-color;
    }

    .ticket-btn {
        margin-left: 15px;
        width: 345px;
        height: 44px;
        box-sizing: border-box;
        background: $btn-mantle-color;
        border-radius: 5px;
        border: 1px solid $btn-color;
        font-size: 18px;
        font-weight: bold;
        color: $btn-color;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            font-size: $font14;
        }
    }
    .disable-ticket-btn {
        background: #f1f1f1;
        border: 1px solid #dcdcdc;
        color: #909090;
    }
    .loadmore {
        padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
    }
    .content {
        padding: 0px 15px;
        position: relative;
        overflow: hidden;

        .action-list {
            margin-bottom: 10px;
            overflow: hidden;
            border-radius: 5px;
            .flex-list {
                background: #ffffff;
                display: flex;
                align-items: center;
                padding: 6px 10px 6px 10px;
                overflow: hidden;
                border-radius: 5px;
                height: 90px;
                .flex-left {
                    flex: 1;
                    .cell {
                        display: flex;
                        align-items: center;
                        .cell-title {
                            line-height: 24px;
                            font-size: 12px;
                            font-weight: 700;
                            width: 60px;
                        }
                        .cell-detail {
                            font-size: 12px;
                            color: #909090;
                        }
                        .cell-theme {
                            color: $btn-color;
                            font-size: 15px;
                            font-weight: 700;
                            line-height: 24px;
                        }
                    }
                }
                .cell-btn {
                    background-color: $btn-mantle-color;
                    line-height: 24px;
                    font-size: 12px;
                    padding-left: 7px;
                    padding-right: 7px;
                    border: 1px solid $btn-color;
                    color: $btn-color;
                    border-radius: 3px;
                }

                .disable-cell-btn {
                    border: 1px solid #dcdcdc;
                    color: #909090;
                    background-color: #f6f6f6;
                }

                .flex-right {
                    text-align: center;
                }
            }
        }
        .order {
            width: 100%;
            overflow: hidden;
            margin-bottom: 10px;
            border-radius: 5px;

            .order-list {
                flex: 1;
                background: #ffffff;
                border-radius: 5px;
                display: flex;
                align-items: center;
                padding: 9px 10px 9px 10px;
                height: 90px;
                .order-type {
                    margin-right: 15px;
                    width: 18px;
                    height: 16px;
                }
                .order-left {
                    flex: 1;

                    .time {
                        font-size: 12px;
                        font-weight: 400;
                        color: #909090;
                        line-height: 24px;
                    }

                    .title {
                        font-size: 15px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 24px;
                    }

                    .info {
                        font-size: $font14;
                        font-weight: 400;
                        color: #333333;
                        line-height: 24px;
                    }
                }

                .order-img {
                    flex: 0.5;

                    img {
                        width: 44px;
                    }
                }

                .order-right {
                    height: 72px;
                    line-height: 72px;
                    .right-money {
                        color: $btn-color;
                        font-size: $font14;
                        text-align: right;

                        span {
                            font-size: 18px;
                            font-weight: 700;
                            line-height: 21px;
                            margin-top: 3px;
                        }

                        .arrow-right {
                            margin-left: 9px;
                        }
                    }
                }
            }
        }
    }
    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom);
        width: 100%;
        background: $bg-color;
        .footer-text {
            padding: 0 15px;
            height: 25px;
            line-height: 25px;
            border-bottom: 1px solid #e1e1e1;
            text-align: center;
        }
        .footer-select {
            display: flex;
            align-items: center;
            margin-top: 10px;
            padding: 0 15px 10px;
            .footer-btn {
                margin: auto;
                width: 120px;
                height: 44px;
                color: #ffffff;
                line-height: 44px;
                font-size: 15px;
                border-radius: 8px;
                text-align: center;
                background: $btn-color;
            }
        }
    }
}
</style>
