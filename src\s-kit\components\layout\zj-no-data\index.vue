<template>
    <div class="box">
        <div class="boxInner">
            <img :src="emptyImage" alt />
            <div class="label">{{ emptyText }}</div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'zj-no-data',
    props: {
        emptyText: {
            type: String,
            return: '暂无数据',
        },
        emptyImage: {
            type: String,
            return: '',
        },
    },
    data() {
        return {};
    },
    mounted() {
        // console.log('state-----', this.state);
    },
};
</script>

<style lang="scss" scoped>
pages {
    background: transparent;
    height: 100%;
}
.box {
    width: 100%;
    height: 100%;
    position: relative;
    .boxInner {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        img {
            width: 500rpx;
            height: 450rpx;
        }
        .label {
            width: 100%;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
            margin-top: 25rpx;
        }
    }
}
</style>
