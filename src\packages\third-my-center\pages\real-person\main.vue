<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <!-- 更换手机号页面 -->
        <div class="bg-FFFFFF" style="height: 100%">
            <zj-navbar :height="44" title="账号与安全"></zj-navbar>
            <div class="content">
                <img src="../../image/real_person.png" alt="" />
                <div class="text">1.每年可更换2次；</div>
                <div class="text">2.账号内若存在未使用电子券更换手机号后将视为自愿放弃；</div>
                <div class="text">3.为保证您的账号安全修改手机号需要先完成实人认证。</div>
                <div class="btn" @click="openClick">确认并开始实人认证</div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        </div>
    </div>
</template>

<script>
import { initRealPersonIdentify, realPersonIdentify } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import Vue from 'vue';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            verificationCode: '',
            phone: '',
            type: 0,
            show: true,
            count: 60,
            timer: null,
            imageCheckShow: false,
        };
    },
    onLoad(option) {},
    mounted() {},
    watch: {},
    methods: {
        openClick() {
            // 打开人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', true);
        },
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);

            this.initFaceAuthentication();
        },
        /**人脸验证初始化
     *  returnUrl:  业务回跳地址；PC或H5接入时 必传，APP接入时为空
        metaInfo:  MetaInfo环境参数，需要通过JS获取或SDK获取
        verifyMode:  认证接入方式：1—APP接入；2—PC或H5接入；
    */
        async initFaceAuthentication() {
            let params = {
                returnUrl: '',
                metaInfo: await this.$cnpcBridge.aliMetaInfo(),
                verifyMode: '1',
            };

            let res = await initRealPersonIdentify(params);

            if (res && res.success) {
                this.verifyUnique = res.data.verifyUnique;
                this.$cnpcBridge.aliFaceCollec(res.data.certifyId, async result => {
                    if (result.status) {
                        this.realNameAuthentication(res.data);
                    } else {
                        uni.showToast({ title: result.msg });
                    }
                });
            }
        },
        /** 人脸实名验证
     *
     * @param {*} data
     *  type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
        authInfo: 认证校验码，实名认证接口返回的数据。
        verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
        certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
        verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
    */
        realNameAuthentication(data) {
            let params = {
                type: '6',
                // authInfo: this.authInfo,
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                verifyMode: '1',
                // idNo: this.personInfo.idNo
            };
            realPersonIdentify(params).then(res => {
                if (res.success) {
                    let url = '/packages/third-account/pages/account-change-phone/main';
                    let params = {
                        type: 1,
                        authInfo: res.data.authInfo,
                    };
                    this.$sKit.layer.useRouter(url, params, 'navigateTo');
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 16px;

    img {
        display: block;
        width: 241px;
        height: 204px;
        margin: 0 auto;
        margin-top: 48px;
    }

    .text {
        margin-top: 12px;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
    }

    .btn {
        height: 44px;
        margin-top: 16px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        line-height: 44px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
    }
}
</style>
