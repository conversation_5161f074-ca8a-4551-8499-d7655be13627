<template>
    <div class="detail">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            back-text="消息详情"
            :back-text-style="pageConfig.titleStyle"
            :background="pageConfig.bgColor"
            :border-bottom="false"
        ></u-navbar>
        <div class="container">
            <div class="title">{{ messageObj.sketch }}</div>
            <div class="time">{{ messageObj.appSendTime }}</div>
            <div class="content">{{ messageObj.content }}</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';

export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            messageObj: {},
        };
    },
    async onLoad(option) {
        this.messageObj = JSON.parse(decodeURIComponent(option.obj));
    },
    methods: {},
};
</script>

<style scoped lang="scss">
.detail {
    width: 100vw;
    min-height: 100vh;
    background-color: #f6f6f6;
    .container {
        padding: 22px 0;
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        .time {
            margin-top: 10px;
            font-size: 12px;
            color: #909090;
            text-align: center;
        }
        .content {
            width: 325px;
            margin: 17px auto 0;
            font-size: 12px;
            color: #909090;
        }
    }
}
</style>
