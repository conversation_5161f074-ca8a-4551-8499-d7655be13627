<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view">
            <zj-navbar title="订单详情"></zj-navbar>
            <div class="content">
                <!-- 昆仑e享卡充值订单详情 -->
                <div class="content-overall" v-if="pageInfo.walletOrderType == 1">
                    <div class="content-box">
                        <div class="state" v-if="pageInfo.orderStatus == 4">
                            <img src="../../image/success.png" alt="" />
                            <div>已支付</div>
                        </div>
                        <div class="state" v-if="pageInfo.orderStatus == 6">
                            <img src="../../image/fail.png" alt="" />
                            <div>已取消</div>
                        </div>
                        <div class="order-no-box">
                            <div class="order-no">订单编号{{ pageInfo.orderNo }}</div>
                            <div class="copy-box" @click="copy(pageInfo.orderNo)">
                                <img mode="heightFix" src="../../image/copyBackground.png" alt="" />
                                <div class="copy-btn">复制订单编号</div>
                            </div>
                        </div>
                        <div class="detail">
                            <div class="detail-item" v-if="pageInfo.orderStatus != 6">
                                <div class="left">支付时间</div>
                                <div class="rigth">{{ pageInfo.endTime }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付渠道</div>
                                <div class="rigth">{{ pageInfo.sourceChannelNoName }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付金额</div>
                                <div class="rigth">&yen;{{ pageInfo.rechargeAmount }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付方式</div>
                                <div class="rigth">{{ pageInfo.paymentTypeName }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">创建时间</div>
                                <div class="rigth">{{ pageInfo.createTime }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 实体卡充值订单详情 -->
                <div class="content-overall" v-if="pageInfo.walletOrderType == 2">
                    <div class="content-box">
                        <div class="state">
                            <img src="../../image/success.png" alt="" />
                            <div>已支付</div>
                        </div>
                        <div class="order-no-box">
                            <div class="order-no">订单编号{{ pageInfo.orderId }}</div>
                            <div class="copy-box" @click="copy(pageInfo.orderNo)">
                                <img mode="heightFix" src="../../image/copyBackground.png" alt="" />
                                <div class="copy-btn">复制订单编号</div>
                            </div>
                        </div>
                        <div class="detail">
                            <div class="detail-item">
                                <div class="left">支付时间</div>
                                <div class="rigth">{{ pageInfo.occurTime }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付金额</div>
                                <div class="rigth">&yen;{{ pageInfo.amount }}</div>
                            </div>

                            <div class="detail-item">
                                <div class="left">发票状态</div>
                                <div class="item-right-text" v-if="pageInfo.isInvoice == '4'">红冲中</div>
                                <div class="item-right-text" v-if="pageInfo.isInvoice == '3'">开票中</div>
                                <div class="item-right-text" v-if="pageInfo.isInvoice == '0'">不可开票</div>
                                <div class="item-right-button color" v-if="pageInfo.isInvoice == '1'" @click="nextStep">去开票</div>
                                <div class="item-right-button" @click="seeInvoice" v-if="pageInfo.isInvoice == '2'">已开票，去查看</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 昆仑e享卡详情页跳转到充值订单详情 -->
                <div class="content-overall" v-if="pageInfo.walletDetail">
                    <div class="content-box">
                        <div class="state">
                            <img src="../../image/success.png" alt="" />
                            <div>已支付</div>
                        </div>
                        <div class="order-no-box">
                            <div class="order-no">订单编号{{ pageInfo.orderNo }}</div>
                            <div class="copy-box" @click="copy(pageInfo.orderNo)">
                                <img mode="heightFix" src="../../image/copyBackground.png" alt="" />
                                <div class="copy-btn">复制订单编号</div>
                            </div>
                        </div>
                        <div class="detail">
                            <div class="detail-item" v-if="pageInfo.orderStatus != 6">
                                <div class="left">支付时间</div>
                                <div class="rigth">{{ pageInfo.time }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付渠道</div>
                                <div class="rigth">{{ pageInfo.sourceChannel }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付金额</div>
                                <div class="rigth">&yen;{{ pageInfo.amountChange }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="left">支付方式</div>
                                <div class="rigth">{{ pageInfo.typeName }}</div>
                            </div>
                            <!-- <div class="detail-item">
                                <div class="left">创建时间</div>
                                <div class="rigth">{{ pageInfo.time }}</div>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { getInvoiceByOrderNoApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'wallet-order-detail',
    data() {
        return {
            // 订单数据
            pageInfo: {},
        };
    },
    onLoad(options) {
        // 接收订单详情数据
        this.pageInfo = JSON.parse(decodeURIComponent(options.data));
        console.log(this.pageInfo, 'this.pageInfo=====');
        this.$sKit.mpBP.tracker('我的充值订单', {
            seed: 'rechargeOrderBiz',
            pageID: 'recharge_des_page',
            refer: this.pageInfo.refer || '',
            channelID: clientCode,
        });
        this.nextStep = this.$sKit.commonUtil.throttleUtil(this.nextStep);
    },
    methods: {
        /**
         * @description  : 判断数据发票状态，返回展示文本
         * @param         {Object} item: isInvoice    0 不开发票    1 等待开票 2 开票成功  3 开票中  4 红冲中
         * @return        {String} 返回展示文本
         */
        checkFp(item) {
            let tag = item.isInvoice;
            if (tag == '0') {
                return '不可开票';
            } else if (tag == '1') {
                return '等待开票';
            } else if (tag == '2') {
                return '开票成功';
            } else if (tag == '3') {
                return '开票中';
            } else if (tag == '4') {
                // 红冲中
                return '换开中';
            }
        },
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:被复制的内容
         * @return        {*}
         */
        copy(value) {
            uni.setClipboardData({
                data: String(value), //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        /**
         * @description  : 整理数据跳转开票页面开票
         * @return        {*}
         */
        nextStep() {
            this.$sKit.mpBP.tracker('我的充值订单', {
                seed: 'rechargeOrderBiz',
                pageID: 'invoicingBut',
                refer: this.pageInfo.refer || '',
                channelID: clientCode,
            });
            let url = '/packages/third-invoice/pages/invoice-form/main';
            let params = {
                type: 'invoice2',
                orderNoList: [this.pageInfo.orderId],
                checkAllAmount: this.pageInfo.amount,
                cardNo: this.pageInfo.cardAsn,
                refer: 'r32',
            };
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 根据订单号查询发票数据
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        async seeInvoice() {
            let params = {
                orderNum: this.pageInfo.orderId,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    min-height: 0;
    background: #f7f7fb;

    .content-overall {
        padding: 32rpx 32rpx 0;

        .content-box {
            background: #ffffff;
            border-radius: 16rpx;
            padding: 62rpx 28rpx 32rpx;

            .state {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                img {
                    width: 128rpx;
                    height: 128rpx;
                }

                div {
                    margin-top: 30rpx;
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 46rpx;
                }
            }

            .order-no-box {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;

                .order-no {
                    text-align: center;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 60rpx;
                }

                .copy-box {
                    position: relative;
                    width: 100%;
                    height: 58rpx;
                    text-align: center;

                    img {
                        overflow: unset;
                        width: 180rpx;
                        height: 58rpx;
                    }

                    .copy-btn {
                        position: absolute;
                        bottom: 0;
                        width: 100%;
                        font-size: 24rpx;
                        color: #666666;
                        line-height: 45rpx;
                        text-align: center;
                    }
                }
            }

            .detail {
                margin-top: 45rpx;

                .detail-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .left {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #666666;
                        line-height: 67rpx;
                    }

                    .item-right-button {
                        padding: 0 20rpx;
                        height: 48rpx;
                        border-radius: 4rpx;
                        border: 1px solid #333333;
                        line-height: 50rpx;
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #333333;
                        text-align: center;
                    }

                    .item-right-text {
                        line-height: 50rpx;
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #333333;
                    }

                    .color {
                        color: #e64f22;
                        border: 1px solid #e64f22;
                    }

                    .rigth {
                        max-width: 450rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 67rpx;
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;

                        .right-value {
                            max-width: 390rpx;
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 28rpx;
                            word-break: break-all;
                        }

                        .copy {
                            width: 29px;
                            height: 16px;
                            background: #ffffff;
                            border-radius: 2px;
                            border: 1px solid #999999;
                            text-align: center;
                            line-height: 16px;
                            color: #666;
                            font-size: 10px;
                            margin-left: 5px;
                        }
                    }
                }
            }
        }
    }
}
</style>
