<template>
    <view class>
        <view class="u-navbar" :style="[navbarStyle]" :class="{ 'u-navbar-fixed': isFixed, 'u-border-bottom': borderBottom }">
            <view
                class="u-bg-color"
                :style="{
                    backgroundColor: bgColor ? bgColor : 'rgba(255, 255, 255, ' + bgOpacity + ')',
                }"
            ></view>
            <view class="u-status-bar-wrap">
                <view class="u-status-bar" :style="{ height: statusBarHeight + 'px' }" v-if="isShowStatusBar"></view>
                <view class="u-navbar-inner transition" :style="[navbarInnerStyle]">
                    <!-- 支付宝无法取消返回按钮 -->
                    <!-- #ifdef MP-WEIXIN || MP-MPAAS || MP-TOUTIAO || H5-CLOUD -->
                    <view class="u-back-wrap" v-if="isBack" @tap="goBack">
                        <view class="u-icon-wrap">
                            <view class="icon-back iconfont" :style="{ borderColor: backIconColor }" v-if="backIconSize > 0"></view>
                        </view>
                        <view class="u-icon-wrap u-back-text u-line-1" v-if="backText" :style="[backTextStyle]">{{ backText }}</view>
                    </view>
                    <!-- #endif -->
                    <view class="u-navbar-content-title" v-if="title" :style="[titleStylrObj]">
                        <view
                            class="u-title u-line-1"
                            :style="{
                                color: titleColor,
                                fontWeight: titleBold ? 'bold' : titleFontWeight,
                            }"
                            >{{ title }}
                            <slot name="title_img"></slot>
                        </view>
                    </view>
                    <!-- <view class="u-slot-content">
            <slot></slot>
          </view>-->
                    <view class="u-slot-right">
                        <slot name="right"></slot>
                    </view>
                    <!-- 站内信 -->
                    <view class="u-slot-right_text">
                        <slot name="title_text"></slot>
                    </view>
                </view>
                <view class="u-slot-tow" :style="[towStyle]" v-if="isTow">
                    <slot name="tow"></slot>
                </view>
            </view>
        </view>
        <!-- 解决fixed定位后导航栏塌陷的问题 -->
        <view
            class="u-navbar-placeholder transition"
            v-if="isFixed && !immersive"
            :style="{ width: '100%', height: Number(navbarHeight) + statusBarHeight + 'px' }"
        ></view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
const app = getApp();
let systemInfo;
systemInfo = uni.getSystemInfoSync();
console.log('systemInfo---', JSON.stringify(systemInfo));
let menuButtonInfo = {};
// 如果是小程序，获取右上角胶囊的尺寸信息，避免导航栏右侧内容与胶囊重叠(支付宝小程序非本API，尚未兼容)
// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ
menuButtonInfo = uni.getMenuButtonBoundingClientRect();
// #endif

/**
 * navbar 自定义导航栏
 * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uniapp自带的导航栏。
 * @tutorial https://www.uviewui.com/components/navbar.html
 * @property {String Number} height 导航栏高度(不包括状态栏高度在内，内部自动加上)，注意这里的单位是px（默认44）
 * @property {String} back-icon-color 左边返回图标的颜色（默认#606266）
 * @property {String} back-icon-name 左边返回图标的名称，只能为阿里矢量图标库中uni-mall项目中的iconfont
 * @property {String Number} back-icon-size 左边返回图标的大小，单位rpx（默认30）
 * @property {String} back-text 返回图标右边的辅助提示文字
 * @property {Object} back-text-style 返回图标右边的辅助提示文字的样式，对象形式（默认{ color: '#606266' }）
 * @property {String} title 导航栏标题，如设置为空字符，将会隐藏标题占位区域
 * @property {String Number} title-width 导航栏标题的最大宽度，内容超出会以省略号隐藏，单位rpx（默认250）
 * @property {String} title-color 标题的颜色（默认#606266）
 * @property {String Number} title-size 导航栏标题字体大小，单位rpx（默认32）
 * @property {Function} custom-back 自定义返回逻辑方法
 * @property {String Number} z-index 固定在顶部时的z-index值（默认980）
 * @property {Boolean} is-back 是否显示导航栏左边返回图标和辅助文字（默认true）
 * @property {Object} background 导航栏背景设置，见官网说明（默认{ background: '#ffffff' }）
 * @property {Boolean} is-fixed 导航栏是否固定在顶部（默认true）
 * @property {Boolean} immersive 沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效（默认false）
 * @property {Boolean} border-bottom 导航栏底部是否显示下边框，如定义了较深的背景颜色，可取消此值（默认false）
 * @example <zj-navbar back-text="返回" title="剑未配妥，出门已是江湖"></zj-navbar>
 */
export default {
    name: 'zj-navbar',
    props: {
        // 透明度
        bgOpacity: {
            type: [String, Number],
            default: 0,
        },
        // 导航栏背景颜色
        bgColor: {
            type: [String, Number],
        },
        // 导航栏高度，单位px，非rpx
        height: {
            type: [String, Number],
            default: '',
        },
        // 返回箭头的颜色
        backIconColor: {
            type: String,
            // default: '#606266'
            default: '#000',
        },
        // 左边返回图标的大小，rpx
        backIconSize: {
            type: [String, Number],
            default: '44',
        },
        // 返回的文字提示
        backText: {
            type: String,
            default: '',
        },
        // 返回的文字的 样式
        backTextStyle: {
            type: Object,
            default() {
                return {
                    // color: '#606266'
                    color: '#000',
                };
            },
        },
        // 导航栏标题
        title: {
            type: String,
            default: '',
        },
        // 标题的宽度，如果需要自定义右侧内容，且右侧内容很多时，可能需要减少这个宽度，单位rpx
        titleWidth: {
            type: [String, Number],
            default: '250',
        },
        // 标题的颜色
        titleColor: {
            type: String,
            // default: '#606266'
            default: '#000',
        },
        // 标题字体是否加粗
        titleBold: {
            type: Boolean,
            default: false,
        },
        titleFontWeight: {
            type: [String, Number],
            default: '400',
        },
        // 标题的字体大小
        titleSize: {
            type: [String, Number],
            default: 32,
        },
        isBack: {
            type: [Boolean, String],
            default: true,
        },
        // 对象形式，因为用户可能定义一个纯色，或者线性渐变的颜色
        background: {
            type: Object,
            default() {
                return {
                    background: '#ffffff',
                };
            },
        },
        homeIcon: {
            type: [Boolean],
            default: false,
        },
        // 导航栏是否固定在顶部
        isFixed: {
            type: Boolean,
            default: true,
        },
        // 是否沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效
        immersive: {
            type: Boolean,
            default: false,
        },
        // 是否显示导航栏的下边框
        borderBottom: {
            type: Boolean,
            default: false,
        },
        zIndex: {
            type: [String, Number],
            default: '',
        },
        // 自定义返回逻辑
        customBack: {
            type: Function,
            default: null,
        },
        // 是否显示状态栏
        isShowStatusBar: {
            type: Boolean,
            default: true,
        },
        // 是否去除右侧胶囊宽
        isRightCapsuleW: {
            type: Boolean,
            default: false,
        },
        // 第一行高度，单位px，非rpx  目的是为了：设置整体高度过高，导致返回按键与右侧胶囊不对齐
        oneHeight: {
            type: [String, Number],
            default: '',
        },
        // 是否显示第二行  第二行高度为整体高减去第一行的高度  即height - oneHeight
        isTow: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            menuButtonInfo: menuButtonInfo,
            // #ifndef H5-CLOUD
            statusBarHeight: systemInfo.statusBarHeight,
            // #endif
            // #ifdef H5-CLOUD
            statusBarHeight: Number(app.globalData.systemBar),
            // #endif
            titleStylrObj: {},
        };
    },
    computed: {
        ...mapState({
            zjNavBarShow: state => state.thirdIndex.zjNavBarShow,
        }),
        // 导航栏内部盒子的样式
        navbarInnerStyle() {
            let style = {};
            // 导航栏宽度，如果在小程序下，导航栏宽度为胶囊的左边到屏幕左边的距离
            let height = this.navbarHeight;
            if (this.oneHeight) {
                height = this.oneHeight;
            }
            style.height = height + 'px';
            // // 如果是各家小程序，导航栏内部的宽度需要减少右边胶囊的宽度
            // #ifdef MP-WEIXIN
            let rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;
            style.marginRight = rightButtonWidth + 'px';
            // #endif
            // #ifdef MP-ALIPAY
            style.marginRight = '125px';
            // #endif
            if (this.isRightCapsuleW) {
                style.marginRight = 0;
            }
            return style;
        },
        towStyle() {
            let style = {};
            let height = this.navbarHeight - this.oneHeight;
            style.height = height + 'px';
            return style;
        },
        // 整个导航栏的样式
        navbarStyle() {
            let style = {};
            style.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.navbar;
            if (this.isShowStatusBar) {
                style.height = this.statusBarHeight + this.navbarHeight + 'px';
            } else {
                style.height = this.navbarHeight + 'px';
            }
            // 合并用户传递的背景色对象
            Object.assign(style, this.background);
            return style;
        },
        // 转换字符数值为真正的数值
        navbarHeight() {
            // #ifdef APP-PLUS || H5 || H5-CLOUD
            return this.height ? Number(this.height) : 44;
            // #endif
            // #ifdef MP
            // 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)
            // 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式
            // return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度
            let height = systemInfo.platform == 'ios' ? 44 : 48;
            return this.height ? Number(this.height) : height;
            // #endif
        },
    },
    watch: {
        '$store.state.thirdIndex.zjNavBarShow': {
            handler: function (newValue, oldValue) {
                if (newValue) {
                    console.log(newValue);
                    systemInfo = uni.getSystemInfoSync();
                    this.titleStyle();
                }
            },
            immediate: true,
            deep: true,
        },
    },
    created() {},
    methods: {
        // 导航中间的标题的样式
        titleStyle() {
            let style = {};
            // #ifndef MP
            style.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';
            style.right = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';
            // #endif
            // #ifdef MP
            // 此处是为了让标题显示区域即使在小程序有右侧胶囊的情况下也能处于屏幕的中间，是通过绝对定位实现的
            let rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;
            style.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';
            style.right = rightButtonWidth - (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + rightButtonWidth + 'px';
            // #endif
            // #ifdef H5-CLOUD
            style.left = (Number(systemInfo.windowWidth) - uni.upx2px(this.titleWidth)) / 2 + 'px';
            style.right = (Number(systemInfo.windowWidth) - uni.upx2px(this.titleWidth)) / 2 + 'px';
            // #endif
            style.width = uni.upx2px(this.titleWidth) + 'px';
            this.titleStylrObj = style;
        },
        //返回首页
        backHome() {
            this.$sKit.layer.backHomeFun();
        },
        // 关闭小程序
        closeEvent() {
            const pages = getCurrentPages();
            // console.log('走到',pages.length)
            // uni.showToast({
            //   title: JSON.stringify(pages.length),
            //   icon: 'none',
            //   duration: 4000
            // })
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        goBack(type) {
            // 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑
            if (typeof this.customBack === 'function') {
                // 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this
                // 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文
                this.customBack.bind(this.$u.$parent.call(this))();
            } else {
                this.closeEvent();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.back-home-wrap {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    .back-home-icon {
        width: 35rpx;
        height: 35rpx;
    }
}
.back-or-home {
    display: flex;
    align-items: center;
    border-radius: 16px;
    height: 32px;
    width: 88px;
    box-sizing: border-box;
    border: 1px solid #979797;
    background-color: #fff;
    .or-line {
        height: 18px;
        width: 1px;
        background-color: #979797;
    }
    .icon-view {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 100%;
        .nav-icon {
            height: 16px;
            width: 16px;
        }
        .home-icon {
            height: 18px;
            width: 18px;
        }
    }
}
.transition {
    transition: height 50ms;
}
.u-navbar {
    width: 100%;
    position: relative;
}

.u-navbar-fixed {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 991;
}

.u-bg-color {
    width: 100%;
    height: 100%;
}

.u-status-bar-wrap {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.u-status-bar {
    width: 100%;
}

.u-navbar-inner {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
    align-items: center;
}

.u-back-wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    flex-grow: 0;
    padding: 14rpx 14rpx 14rpx 48rpx;
}
.icon-back {
    border: 1px solid #333;
    border-width: 0px 1px 1px 0px;
    display: inline-block;
    padding: 5px;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}
.navi-my-icon {
    width: 24px;
    height: 24px;
}

.u-back-text {
    padding-left: 4rpx;
    font-size: 30rpx;
}

.u-navbar-content-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: absolute;
    left: 0;
    right: 0;
    // height: 60rpx;
    text-align: center;
    flex-shrink: 0;
}

.u-navbar-centent-slot {
    flex: 1;
}

.u-title {
    // line-height: 60rpx;
    line-height: 30px;
    font-size: 32rpx;
    flex: 1;
    align-items: center;
}

.u-navbar-right {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}

.u-slot-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.u-slot-right {
    // display: flex;
    // justify-content: flex-end;
    // align-items: center;
    // width: 40px;
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: absolute;
    right: -42%;
}
.u-slot-right_text {
    // display: flex;
    // justify-content: flex-end;
    // align-items: center;
    // width: 40px;
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: absolute;
    right: -29%;
}
</style>
