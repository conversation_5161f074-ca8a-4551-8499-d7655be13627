<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" title="预约列表"></zj-navbar>
            <div class="oil-tab">
                <map
                    v-if="isShowMap"
                    id="mapBulkOil"
                    :enable-overlooking="false"
                    :enable-rotate="false"
                    :enable-scroll="(!guideStep && !popupMoving) || !isFuel"
                    :latitude="newMapCenterLatV3"
                    :longitude="newMapCenterLonV3"
                    :markers="selectBulkOilStationList"
                    :scale="Number(scale_app)"
                    :setting="mapSetting"
                    :style="{ height: mapHeight + 'px', width: mapWidth + 'px' }"
                    show-location
                    @callouttap="clickCallOut"
                    @markertap="clickMarker"
                >
                    <!-- #ifdef MP-WEIXIN -->
                    <cover-view v-if="isCustomCallout" slot="callout">
                        <cover-view
                            v-for="item in selectBulkOilStationList"
                            :key="item.id"
                            :marker-id="item.id"
                            class="marker-view fl-row fl-al-cen"
                        >
                            <cover-view class="marker-lable-view fl-row fl-al-cen">
                                <cover-image v-if="item.isFirst" :src="mapImgClosest" alt="" class="navigation"></cover-image>
                                <cover-image v-else :src="mapImg" alt="" class="navigation"></cover-image>
                                <cover-view class="fl-column mar-left-4">
                                    <cover-view class="marker-name weight-600 color-000">
                                        {{ item.orgName + '　' }}
                                    </cover-view>
                                    <cover-view class="fl-row pa-t-5 fl-al-cen">
                                        <cover-view class="marker-text font-10 color-333">距离您</cover-view>
                                        <cover-view class="marker-text font-10 color-E64F22">{{ item.distance }}km </cover-view>

                                        <!-- <cover-view class="l-h28 font-10 color-333">， 驾车约</cover-view>
											<cover-view class="l-h28 font-10 color-E64F22">4min</cover-view>-->
                                    </cover-view>
                                </cover-view>
                            </cover-view>
                        </cover-view>
                    </cover-view>
                    <!-- #endif -->
                </map>
                <div
                    v-if="isShowMapContent"
                    ref="drag-content2"
                    :style="{ bottom: boxBottom2 + 'px' }"
                    class="drag-content drag-content2"
                    @click="drawerappClick(2)"
                    @touchend="fuelTouchend"
                    @touchstart="fuelTouchstart"
                    @touchmove.stop.prevent
                >
                    <reserveBulkVview
                        ref="reserveContentView"
                        :refer="refer"
                        class="popup-view"
                        @isShowDialog="isShowDialog"
                        @reMoveing="reMoveing"
                        :applicationFormInformation="applicationFormInformation"
                    >
                    </reserveBulkVview>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
        <!-- #ifdef MP-WEIXIN -->
        <safe-password id="safeKeyboardIdHome" title="安全键盘"></safe-password>
        <!-- #endif -->
        <!-- #ifdef MP-ALIPAY -->
        <safe-password id="safeKeyboardIdHome" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
        <!-- #endif -->
        <div v-if="isHarmony">
            <keyboard-plugin></keyboard-plugin>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import projectConfig, { maxDistance } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import reserveBulkVview from './reserveBulkVview.vue';
export default {
    mixins: [publicMixinsApi],
    name: 'purchaseOfBulkOil',
    data() {
        return {
            topTab: 'reserve',
            // #ifdef MP-WEIXIN
            isWX: true,
            // #endif
            mapImgClosest: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/stationClosest.png',
            mapImg: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/station.png',
            isCustomCallout: true,
            iconBottom: 0,
            // 地图高度
            mapHeight: 0,
            // 地图宽度
            mapWidth: 0,
            boxBottom2: 0,
            // 地图设置开启手指缩放
            mapSetting: {
                // #ifdef MP-MPAAS
                // 手势
                gestureEnable: 1,
                // 比例尺
                showScale: 1,
                // 指南针
                showCompass: 0,
                // 双手下滑
                tiltGesturesEnabled: 1,
                // 交通路况展示
                trafficEnabled: 0,
                // 地图POI信息
                showMapText: 1,
                // #endif
            },
            mapCtx: {},
            popupMoving: false,
            isFuel: false, //表示是否在加油操作中,加油操作中无法点击地图
            // 申请单信息
            applicationFormInformation: {},
            isShowMap: false,
            isShowMapContent: false,
        };
    },
    components: { reserveBulkVview },
    computed: {
        ...mapState({
            // 地图缩放比例
            scale_app: state => state.locationV3_app.scale_app,
            selectBulkOilStationList: state => state.bulkOil.selectBulkOilStationList,
            selectBulkOilStationObject: state => state.bulkOil.selectBulkOilStationObject,
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            walletStatus: state => state.wallet.walletStatus,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        newMapCenterLatV3() {
            // #ifdef  MP-MPAAS
            return Number(this.selectBulkOilStationObject.latitude) - 0.07;
            // #endif
            // #ifdef MP-WEIXIN
            return Number(this.selectBulkOilStationObject.latitude) - 0.035;
            // #endif
            // #ifdef MP-ALIPAY || MP-TOUTIAO
            return Number(this.selectBulkOilStationObject.latitude) - 0.035;
            // #endif
        },
        newMapCenterLonV3() {
            return Number(this.selectBulkOilStationObject.longitude);
        },
    },
    mounted() {
        console.log(this.selectBulkOilStationList, 'selectBulkOilStationList');
        this.setMapHeight();
    },
    async onLoad(options) {
        if (!this.isHarmony) {
            this.isShowMap = true;
            this.isShowMapContent = true;
        }
        if (Object.keys(options).length) {
            let params = JSON.parse(decodeURIComponent(options.data));
            this.applicationFormInformation = params;
            console.log(this.applicationFormInformation, '申请单信息');
            if (params.refer) {
                this.refer = params.refer;
            }
        }
        console.log(this.selectBulkOilStationList, 'selectBulkOilStationList');
        // // 地图拖拽到中心点，现在还在请求油站列表，需要修改
        // await this.initLocation();
        // 初始化密码键盘
        await this.initPasswordKeyboard();
    },
    methods: {
        async initPasswordKeyboard() {
            // #ifdef MP-WEIXIN
            let result = await this.selectComponent('#safeKeyboardIdHome');
            this.$store.commit('setAccountDataPlugin', result);
            // #endif
            // #ifdef MP-ALIPAY
            this.$nextTick(async () => {
                const result = this.$refs['handlePasswordKeyboardRef'];
                this.$sKit.keyBordPlugin.initRef(result);
                console.log('handlePasswordKeyboardRef-result', result);
                this.$store.commit('setAccountDataPlugin', result);
            });
            // #endif
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                this.$nextTick(async () => {
                    let result = await this.$sKit.keyBordPlugin.initRef();
                    this.$store.commit('setAccountDataPlugin', result);
                });
                this.$sKit.mpaasPayPlugin?.init();
            }
            // #endif
        },
        /**
         * @description  : 获取窗口高度
         * @return        {*}
         */
        setMapHeight() {
            let that = this;
            that.$nextTick(() => {
                setTimeout(() => {
                    uni.createSelectorQuery()
                        .select('.oil-tab')
                        .boundingClientRect()
                        .exec(data => {
                            if (data && data[0]) {
                                console.log('data-----', data[0]);
                                that.mapHeight = data[0].height;
                                that.mapWidth = data[0].width;
                                that.isShowMap = true;
                                that.isShowMapContent = true;
                            } else {
                                console.error('未找到 .oil-tab 元素');
                            }
                        });
                }, 100);
            });
        },
        /**
         * @description  : 处理地图组件的底部加油功能的上拉加载
         * @param         {string} type - 滑动类型，为3时，不滑动
         * @return        {*}
         */
        drawerappClick(type) {
            if (this.topTab == 'reserve' || this.topTab == 'charge') {
                if (type == 3) {
                    if (!this.isHarmony) {
                        this.$nextTick(() => {
                            setTimeout(() => {
                                uni.createSelectorQuery()
                                    .in(this)
                                    .select('.drag-content')
                                    .boundingClientRect(data => {
                                        if (data.height > 500) {
                                            this.iconBottom = 60;
                                        } else {
                                            this.iconBottom = data.height + 30;
                                        }
                                    })
                                    .exec();
                            }, 900);
                        });
                    }
                    this.chageScrollTop(0);
                    // this.$store.commit('setLoadingStatus', false);
                }
            }
        },
        async initLocation() {
            return;
            if (this.noInitLocation) return;
            const nextFun = async () => {
                let initParams = {};
                if (this.isHarmony) {
                } else {
                    initParams = {
                        callback: res => {
                            // this.$nextTick(() => {
                            //     // #ifdef MP-WEIXIN
                            //     // this.mapCtx.moveToLocation({ latitude: res.mapCenterLatV3, longitude: res.mapCenterLonV3 });
                            //     // #endif
                            //     // #ifdef MP-MPAAS
                            //     this.mapCtx.moveToLocation({
                            //         latitude: Number(res.mapCenterLatV3) - 0.07,
                            //         longitude: res.mapCenterLonV3,
                            //     });
                            //     // #endif
                            //     // #ifdef MP-ALIPAY
                            //     if (this.stationCode !== '') {
                            //         uni.showLoading();
                            //         let selectItem = this.selectBulkOilStationList.find(item => item.orgCode == this.stationCode);
                            //         if (selectItem) {
                            //             this.$store.commit('setSelectMarkerToMapCenterV3', {
                            //                 marker: selectItem,
                            //             });
                            //         }
                            //         uni.hideLoading();
                            //     }
                            //     this.mapCtx.moveToLocation({
                            //         latitude: Number(res.mapCenterLatV3) - 0.035,
                            //         longitude: res.mapCenterLonV3,
                            //     });
                            //     // #endif
                            // });
                        },
                        type: 'initMap',
                    };
                }
                this.$nextTick(() => {
                    this.mapCtx = uni.createMapContext('mapBulkOil');
                    this.$store.dispatch('initLocationV3_app', initParams);
                });
            };
            // #ifdef MP-MPAAS
            this.$nextTick(() => {
                this.$cnpcBridge.checkPermission().then(res => {
                    console.log(res);
                    if (!res.appStatus) {
                        this.$cnpcBridge
                            .confirmDialog({
                                title: '位置权限使用说明',
                                message: '根据您的位置信息获取您附近的加油站网点信息服务,是否开启？',
                                confirmBtnText: '去开启',
                            })
                            .then(() => {
                                this.$cnpcBridge
                                    .openPermissions({
                                        code: 'location',
                                        explain: '位置权限使用说明',
                                        detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                                    })
                                    .then(res => {
                                        if (res) {
                                            nextFun();
                                        }
                                    });
                            });
                    } else {
                        nextFun();
                    }
                });
            });
            if (this.isHarmony) {
                this.$cnpcBridge.isCutScreen(true);
            } else {
                this.$refs.codePaymentConten.$refs.membershipCode.init();
            }
            // #endif
            // #ifndef MP-MPAAS
            nextFun();
            // #endif
        },
        clickCallOut() {},
        clickMarker() {},
        fuelTouchstart() {
            this.isFuel = true;
        },
        fuelTouchend() {
            setTimeout(() => {
                this.isFuel = false;
            }, 300);
        },
        reMoveing(moveBoolean) {
            this.popupMoving = moveBoolean;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100%;
    height: 100%;
    .oil-tab {
        width: 100%;
        //height: 100%;
        position: relative;
        flex: 1;
        min-height: 0;
        overflow-y: hidden;
    }
}
.drag-content {
    position: absolute;
    width: 100%;
    max-height: 100%;
}
// 地图
map {
    .marker-view {
        position: relative;
        height: 51px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        width: fit-content;

        .marker-lable-view {
            padding: 0 8px 0 5px;
            height: 51px;
            background: #fff;
            box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            width: fit-content;

            .navigation {
                width: 39px;
                height: 39px;
            }

            .marker-name {
                line-height: 18px;
                font-size: 13px;
                overflow: visible;
                width: auto;
            }

            .marker-text {
                line-height: 14px;
            }

            .marker-118920 {
                width: 50px;
                height: 21px;
                background: rgba(17, 137, 32, 0.05);
                border-radius: 4px;
                line-height: 21px;
                text-align: center;
            }

            .pa-t-5 {
                padding-top: 5px;
            }
        }
    }
}
</style>
