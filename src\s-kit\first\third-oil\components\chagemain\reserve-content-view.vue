<template>
    <div class="page-view">
        <div class="bg-none patb10" v-if="circularOrder">
            <div class="fl-row fl-jus-bet card-default color-FF0A0A font-14 bg-FFEAE0 card-reser" @click.stop="authCode">
                <div>您有一笔待加油订单</div>
                <div class="fl-row fl-al-cen">
                    <div class="marr3">去查看</div>
                    <div class="arrow-right-FF0A0A"></div>
                </div>
            </div>
        </div>
        <!-- 营销位 -->
        <div
            :style="{ overflowY: guideStep ? 'visible' : 'scroll' }"
            class="height420"
            @touchend="popupMoving(false)"
            @touchstart="popupMoving(true)"
        >
            <div class="zjMarketBox" v-if="selectMarkerV3 && selectMarkerV3.orgCode">
                <zjMarket ref="topReserveBanner" :orgCode="selectMarkerV3.orgCode" marketType="lbt" spaceType="oilreserve_top"></zjMarket>
            </div>
            <div class="charge-content-view fl-column bg-linear">
                <div class="guideBox" v-if="guideStep == 2">
                    <img class="guideImg" mode="widthFix" src="../../../../image/homeImg/guidePage2.png" alt="" />
                    <div class="guideBtton">
                        <div class="skip" @click.stop="setGuideStep(0)">跳过</div>
                        <div class="next" @click.stop="setGuideStep(3)">下一步</div>
                    </div>
                </div>
                <div class="top-area bg-fff" ref="toparea">
                    <div class="stationinfo padding12">
                        <div class="fl-row fl-jus-bet">
                            <div class="detail f-1" v-if="selectMarkerV3 && selectMarkerV3.orgCode">
                                <div class="name-area fl-row fl-al-cen">
                                    <!-- #ifndef MP-ALIPAY -->
                                    <div
                                        :class="{ 'scrolling-text': selectMarkerV3.orgName.length > 11 }"
                                        class="name font-16 weight-bold color-000 f-1"
                                    >
                                        {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                    <!-- #ifdef MP-ALIPAY -->
                                    <div class="name font-16 weight-bold color-000 f-1">
                                        {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                </div>
                                <div class="fl-row fl-al-cen mart12">
                                    <div
                                        v-if="selectMarkerV3.stationStatus == 30 || selectMarkerV3.stationStatus == 50"
                                        class="weight-400 font-12 color-6A6A6A time-icon"
                                        >暂停营业
                                    </div>
                                    <div
                                        v-if="selectMarkerV3.stationStatus == 20 || selectMarkerV3.stationStatus == 10"
                                        class="weight-400 font-12 color-118920 marker-118920"
                                        >正常营业
                                    </div>
                                    <!-- #ifdef MP-MPAAS -->
                                    <div
                                        v-if="isO2O == 1 && !isHarmony"
                                        class="font-10 color-FA6400 e-go fl-row fl-al-jus-cen te-center mar-6-l"
                                        @click="shoppingHandle"
                                    >
                                        <div>进入e享购商城</div>
                                        <div class="arr-right-FA6400 marl5"></div>
                                    </div>
                                    <!-- #endif -->
                                </div>
                                <div v-if="selectMarkerV3.address != 'null'" class="font-12 weight-400 color-333 oil-address"
                                    >{{ selectMarkerV3.address || '' }}
                                </div>
                            </div>
                            <div class="detail f-1" v-else>
                                <div class="name-area fl-row fl-al-cen">
                                    <div class="name font-16 weight-bold color-999">暂未查询到网点</div>
                                    <!-- <div class="changestation font-10 color-E64F22" @click.stop="changeStationAction">更多网点</div> -->
                                </div>
                                <div class="font-12 weight-bold color-999 oil-address">暂无地址</div>
                            </div>
                            <div class="fl-row">
                                <div class="changestation font-10 color-E64F22" @click.stop="changeStationAction">更多网点</div>
                                <div class="margin-box"></div>
                                <!-- #ifndef H5-CLOUD -->
                                <img alt class="navt-to" src="../../image/stationinfo-icon-location.png" @click.stop="clickNaviStateion" />
                                <!-- #endif -->
                            </div>
                        </div>
                        <div class="center-area" v-if="selectMarkerV3.tagList && selectMarkerV3.tagList.length > 0">
                            <div class="fl-row fl-al-cen con-list">
                                <div v-for="(tag, index) in selectMarkerV3.tagList" :key="index" class="item bg-F3F3F6 te-center">
                                    <div class="item-cell">{{ strReplace(tag) }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="marketing-assistant" v-if="marketingAssistant">
                            <div class="marketing-text">{{ marketingAssistant }}</div>
                        </div>
                    </div>
                    <div class="line_bottom_dashed margin-8-l-r"></div>
                    <div class="center-area padding12">
                        <div class="fl-row fl-jus-cen fl-al-cen" v-if="JSON.stringify(selectMarkerV3) == '{}'">
                            <div class="fl-row fl-al-cen">
                                <img src="../../image/no-reserve.png" alt="" class="no-reserve" />
                                <div class="marl12">
                                    <div class="font-16 color-333">附近暂未查询到网点</div>
                                    <div class="font-12 color-666 mat5">请您稍后查询</div>
                                </div>
                            </div>
                        </div>
                        <div class="fl-row fl-jus-cen fl-al-cen" v-else-if="selectMarkerV3.storeOnlyFlag == 1">
                            <div class="fl-row fl-al-cen">
                                <img src="../../image/no-reserve.png" alt="" class="no-reserve" />
                                <div class="marl12">
                                    <div class="font-16 color-333">此站为纯便利店</div>
                                    <div class="font-12 color-666 mat5">暂不支持加油服务</div>
                                    <div class="font-12 color-666 mat5">您可通过【更多网点】去筛选附近加油站</div>
                                </div>
                            </div>
                        </div>
                        <div class="fl-row fl-jus-cen fl-al-cen" v-else-if="selectMarkerV3.bookingRefueling != 1">
                            <div class="fl-row fl-al-cen">
                                <img src="../../image/no-reserve.png" alt="" class="no-reserve" />
                                <div class="marl12">
                                    <div class="font-16 color-333">此站不支持e享加油</div>
                                    <div class="font-12 color-666 mat5">请切换其他油站</div>
                                </div>
                            </div>
                        </div>

                        <div class="fl-row fl-jus-bet fl-al-cen" v-else>
                            <div class="fl-row fl-al-cen" @click.stop="chooseOilNumber('oilDialogFlag')">
                                <div class="font-14 color-666 weight-500 mar-r-8">油号</div>
                                <div
                                    v-if="seletFuel && seletFuel.fuelName"
                                    class="font-20 fl-row fl-al-base fl-jus-cen color-333 bg-ADA1A1 width75 te-center height35 border-rad-4"
                                >
                                    <div class="weight-bold font-20">{{ $sKit.layer.getOilNum(seletFuel.fuelName, 1) || '' }}</div>
                                    <div class="font-14 font">#</div>
                                </div>
                                <div v-else class="font-14 color-666 bg-ADA1A1 height35 width75 te-center border-rad-4"> 请选择 </div>
                            </div>
                            <div class="fl-row fl-al-cen" @click.stop="chooseOilNumber('moneyDialogFlag')">
                                <div class="font-14 color-666 weight-500 mar-r-8">加油金额</div>
                                <div
                                    v-if="preaAuthNumber"
                                    class="fl-row fl-al-base fl-jus-cen color-333 bg-EFEDED height35 width107 te-center border-rad-4"
                                >
                                    <div class="font-14 color-333 marr3">&yen;</div>
                                    <div class="weight-bold font-20"> {{ preaAuthNumber }}</div>
                                </div>
                                <div v-else class="font-14 color-666 bg-EFEDED height35 width107 te-center border-rad-4"> 请输入 </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="line_bottom"></div> -->
                    <!-- fl-row fl-jus-bet -->
                    <div class="card-nav-no" v-if="isWalletStatusCard">
                        <div class="fl-row fl-al-cen fl-jus-bet">
                            <div class="font-13 weight-500 color-FF6B2C">请开通昆仑e享卡 使用e享加油</div>
                            <div class="fl-row fl-al-cen" @click.stop="rechargeClick">
                                <div class="font-15 weight-500 color-FF6B2C">去开通</div>
                                <div class="arroe-right-E64F22 marl5"></div>
                            </div>
                        </div>
                    </div>
                    <div class="card-nav padding12" v-else>
                        <div class="fl-row fl-al-cen">
                            <div class="font-14 color-666 weight-500">余额</div>
                            <div class="font-24 color-333 marl10 fl-row fl-al-base">
                                <div class="font-14 color-333 marr3">&yen;</div>
                                {{ walletInfo.walletBalance || '0' }}
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen" @click.stop="rechargeClick">
                            <div class="font-15 weight-500 color-FF6B2C">充值</div>
                            <div class="arroe-right-E64F22 marl5"></div>
                        </div>
                    </div>
                    <div v-if="!circularOrder" class="coupon_div">
                        <div class="coupon_div_left">
                            <img class="coupon_div_left_img" src="../../../../image/marketing-couponIcon.png" />
                            <div class="coupon_div_left_title">优惠券</div>
                        </div>
                        <div class="coupon_div_right" @click="toCouponDetail()">
                            <div class="coupon_div_right_text" v-if="!availableList.length">暂无可用</div>
                            <div
                                v-else
                                :class="{
                                    'color-E64F22': textColor,
                                }"
                                class="coupon_div_right_text"
                            >
                                {{ couponText }}
                            </div>
                            <img class="coupon_div_right_icon" src="../../../../image/backIcon.png" />
                        </div>
                    </div>
                </div>
                <!-- bookingRefueling 预约加油（1代表可以进行预约加油的油站；0代表不可以） -->
                <!-- <div class="equity-div bg-FFF7DC font-12 color-333 weight-500 te-center">结算后根据您的实付金额自动匹配优惠权益</div> -->
                <!-- <div class="color-fff font-18 btn-44" :class="!isWalletStatusCard ? 'primary-btn' : 'bg-opacity-288'" -->
                <div
                    v-if="selectMarkerV3.bookingRefueling != 1"
                    class="color-fff font-18 btn-44 bg-opacity-288"
                    @click.stop="submitOrderBefore()"
                    >提交订单
                </div>
                <div
                    class="color-fff font-18 btn-44 primary-btn"
                    v-else-if="selectMarkerV3.stationStatus == 20 || selectMarkerV3.stationStatus == 10"
                    @click.stop="submitOrderBefore()"
                >
                    提交订单
                </div>
                <div class="bg-opacity-288 color-fff font-18 btn-44" v-else>当前网点未营业</div>

                <div class="market-div fl-row fl-jus-bet" v-if="selectMarkerV3 && selectMarkerV3.orgCode">
                    <zjMarket
                        ref="leftReserveBanner"
                        :orgCode="selectMarkerV3.orgCode"
                        marketType="rowTwo"
                        spaceType="oilreserve_left"
                    ></zjMarket>
                    <zjMarket
                        ref="rightReserveBanner"
                        :orgCode="selectMarkerV3.orgCode"
                        marketType="rowTwo"
                        spaceType="oilreserve_right"
                    ></zjMarket>
                </div>
            </div>
        </div>
        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <!-- 没有油品信息时 当前网点非加油站或正在维护中  -->
                    <div
                        v-if="oilDialogFlagType == 'noFuelDataDialogFlag'"
                        class="fl-column fl-al-jus-cen font-14 color-333 weight-500 content"
                    >
                        <div>当前网点非加油站或正在维护中</div>
                    </div>
                    <!-- 油号选择 -->
                    <div v-if="fuelData.length > 0">
                        <div class="fl-column fl-al-jus-cen te-center content" v-if="oilDialogFlagType == 'oilDialogFlag'">
                            <div class="font-16 weight-600 color-333">请选择油品号</div>
                            <div class="mart14 p-bf">
                                <div
                                    v-for="(item, index) in fuelData"
                                    :key="index"
                                    :class="cacheSeletFuel.fuelNo == item.fuelNo ? 'active-item' : 'item'"
                                    class="fl-row fl-al-cen marb10 fl-jus-bet"
                                    @click.stop="chooseOilItem(item)"
                                >
                                    <div class="f-1">{{ item.fuelName }}</div>
                                    <div class="check" v-if="cacheSeletFuel.fuelNo == item.fuelNo"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 商品选择 -->
                    <div v-if="productCodeAndNameList.length > 0">
                        <div class="fl-column fl-al-jus-cen te-center content" v-if="oilDialogFlagType == 'oilProductFlag'">
                            <div class="font-16 weight-600 color-333">请选择</div>
                            <div class="mart14 p-bf">
                                <div
                                    v-for="(item, index) in productCodeAndNameList"
                                    :class="cacheSeletProduct.productNo == item.productNo ? 'active-item' : 'item'"
                                    :key="index"
                                    class="fl-row fl-al-cen marb10 fl-jus-bet"
                                    @click.stop="chooseProductItem(item)"
                                >
                                    <div class="f-1">{{ item.fuelDetailName }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 超过距离 -->
                    <div
                        v-if="oilDialogFlagType == 'distanceDialogFlag'"
                        class="fl-column fl-al-jus-cen font-14 color-333 weight-500 content"
                    >
                        <div>您当前距离</div>
                        <div>{{ selectMarkerV3.orgName }}</div>
                        <div
                            >超过<span class="color-E64F22">{{ preAuthOrderDistance.distance || 10 }}km</span>距离较远，请稍后使用</div
                        >
                    </div>
                    <!-- 加油金额 -->
                    <div v-if="oilDialogFlagType == 'moneyDialogFlag'">
                        <div class="fl-column te-center font-14 color-333 weight-500 content">
                            <div class="font-16 color-333 weight-500">本次加油金额</div>
                            <div
                                v-if="Number(priceText) > Number(walletInfo.walletBalance)"
                                class="font-13 color-333 weight-400 mart20 marb17"
                            >
                                当前输入金额已超过余额请充值~
                                <div class="font-13" style="opacity: 0">$</div>
                            </div>
                            <div class="mart20 marb17" v-else>
                                <div class="font-13 color-333 weight-400">最大加油金额为5000元</div>
                                <div class="font-13 color-333 weight-400">最小加油金额为10元</div>
                            </div>
                            <div class="fl-row fl-jus-aro">
                                <div
                                    class="fl-row-item border-box border-rad-8"
                                    v-for="(item, index) in preSetAmountList"
                                    :key="item.price"
                                    @click="selectAmount(item, index)"
                                >
                                    <div
                                        class="fl-row fl-jus-cen fl-al-cen"
                                        :class="
                                            selectedIndex === index && Number(priceText) <= Number(walletInfo.walletBalance)
                                                ? 'active color-E64F22 bg-FF6B2C'
                                                : 'color-333 bg-F3F3F6'
                                        "
                                    >
                                        <div :class="index === 0 ? 'font-13 weight-500' : 'font-16 weight-600'" class="price">{{
                                            item.title
                                        }}</div>
                                        <div class="text weight-500" v-if="index !== 0">元</div>
                                    </div>
                                </div>
                            </div>

                            <div
                                :class="{
                                    'card-input-border': Number(priceText) > Number(walletInfo.walletBalance),
                                    'price-input-area': !(Number(priceText) > Number(walletInfo.walletBalance)),
                                }"
                            >
                                <input
                                    v-model="priceText"
                                    class="price-input"
                                    placeholder="请输入加油金额"
                                    type="number"
                                    @input="otherAmountsInput"
                                />
                            </div>
                            <div v-if="isAllIn" class="font-12 color-FF3E00 weight-400 te-center mart10"
                                >您当前的余额为{{ walletInfo.walletBalance }}元</div
                            >
                        </div>
                    </div>
                    <!-- 密码次数或者密码错误 -->
                    <div class="fl-column fl-al-jus-cen content" v-if="oilDialogFlagType == 'cipherDialogFlag'">
                        <div class="font-16 color-333 weight-500 marb10"> {{ oilDialogtitle }}</div>
                        <div class="font-14 color-666">{{ oilDialogCode }}</div>
                    </div>
                    <!-- 余额不足 -->
                    <div v-if="oilDialogFlagType == 'balanceFlag'" class="fl-column fl-al-jus-cen font-16 color-333 weight-bold content">
                        <div>您的“昆仑e享卡”余额不足</div>
                        <div>请充值~</div>
                    </div>
                    <!-- 网点暂未营业 -->
                    <div v-if="oilDialogFlagType == 'stationStatus'" class="fl-column fl-al-jus-cen font-16 color-333 weight-bold content">
                        <div>网点暂未营业，是否需要导航到此网点</div>
                    </div>
                </div>

                <!-- <template v-slot:button> -->
                <view class="fl-row slot-btn-box">
                    <view v-if="cancelText" :style="{ color: cancelColor }" class="btn cancel_btn" @click.stop="clickBtn('cancel')">
                        {{ cancelText }}
                    </view>
                    <view
                        v-if="confirmText"
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickBtn('confirm')"
                        >{{ confirmText }}
                    </view>
                </view>
                <!-- </template> -->
            </div>
        </custom-popup>
        <OilPopWindow
            ref="oilPopWindow"
            :oilPopWindowFlag="oilPopWindowFlag"
            :productCodeAndNameList="productCodeAndNameList"
            class="dialog"
            @cancelSelect="cancelSelect"
            @selectComplete="selectComplete"
        ></OilPopWindow>
        <zj-old-account v-if="isTransfer"></zj-old-account>
        <zj-unrealized-authentication v-if="realNameDialogFlag" @realNameDialogClose="realNameDialogClose" @realNameInfo="realNameInfo">
        </zj-unrealized-authentication>
        <zj-agreement @enterNavEvent="enterNavEvent" @cancelClick="cancelClick"></zj-agreement>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import OilPopWindow from '../oilPopWindow/main.vue';

import {
    generateOrder,
    getProductInfo,
    preAuthCancel,
    preAuthOrderApi,
    distanceConfigApi,
    oilPriceApi,
    preAuthGetCouponList,
    getMarketingRec,
} from '../../../../js/v3-http/https3/oilStationService/index.js';
// import Vue from 'vue';

// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-resver.js';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD
import wxMixin from './diff-environment/wx-resever.js';
import zfbMixin from './diff-environment/zfb-resver.js';
// #endif
import zjMarket from '../../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import { clientCode } from '../../../../../../project.config';
// #ifdef H5-CLOUD
import cloudMixin from './diff-environment/cloud-resver.js';
// #endif
export default {
    name: 'charge-content-view',
    components: {
        OilPopWindow,
        zjMarket,
    },

    // #ifdef MP-MPAAS
    mixins: [appMixin],
    // #endif
    // #ifndef MP-MPAAS || H5-CLOUD
    mixins: [wxMixin, zfbMixin],
    // #endif
    // #ifdef H5-CLOUD
    mixins: [cloudMixin],
    // #endif
    computed: {
        ...mapGetters(['walletInfo', 'walletStatus', 'latV3', 'lonV3']),
        ...mapState({
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin, //是否显示弹窗
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
            guideStep: state => state.thirdIndex.guideStep, // 是否展示首次指引  true-是 false-否
            cityName: state => state.locationV3_app.cityName,
            isO2O: state => state.locationV3_app.isO2O,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        isWalletStatusCard() {
            // this.$console(this.walletStatus, 'this.walletStatus')
            if (!this.walletStatus.status) {
                return true;
            } else if (this.walletStatus.status && this.walletStatus.accountStatus == 3) {
                return true;
            } else {
                return false;
            }
        },
    },
    props: {
        refer: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            // isCard: false,
            productCodeAndNameList: [],
            // 油品选择
            seletFuel: {},
            //油品集合
            oilNumArr: [],
            //显示的预授权金额
            preaAuthNumber: '',
            // 油品编码
            fuelType: '',
            // 油品类型
            fuelName: '',
            // 打开油品弹窗标识
            oilDialogFlagType: '',
            // 弹窗是否显示标识
            modelDialogFlag: false,
            cancelText: '取消',
            cancelColor: '#666',
            confirmText: '确认',
            confirmColor: '#000',
            oilPopWindowFlag: false,
            //输入框的预授权金额
            priceText: '',
            infoAuthOrder: '', //预授权订单
            timer: null,
            circularOrder: false, //是否存在预授权订单
            isPreaAuthing: false, //是否预约加油过程中
            oilDialogtitle: '',
            oilDialogCode: '',
            //未实人认证加油时需要弹出输入身份证号和姓名的弹窗
            realNameDialogFlag: false,
            // 下单成功的数据
            preOrderData: {},
            dataInfo: '',
            preAuthOrderDistance: {}, //可下单距离
            cacheSeletFuel: {},
            availableList: [], //可用券列表
            unCouponList: [], //不可用券列表
            // selectCoupon: '',
            textColor: false,
            isToSelectTicketPage: false,
            // 删除vuex中的值
            selectedCoupon: null,
            couponText: '',
            marketingAssistant: '',
            cacheSeletProduct: {},
            preSetAmountList: [
                {
                    title: '加满',
                    price: '',
                },
                {
                    title: '500',
                    price: '500',
                },
                {
                    title: '300',
                    price: '300',
                },
                {
                    title: '200',
                    price: '200',
                },
            ],
            selectedIndex: -1,
            isAllIn: false,
        };
    },
    created() {
        // 获取电子钱包金额
        this.storageDataFun();
        this.submitOrderBefore = this.$sKit.commonUtil.throttleUtil(this.submitOrderBefore);
        this.changeStationAction = this.$sKit.commonUtil.throttleUtil(this.changeStationAction);
        this.rechargeClick = this.$sKit.commonUtil.throttleUtil(this.rechargeClick);
    },
    watch: {
        // 监听selectMarkerV3.orgCode变化 实现页面变化
        '$store.state.locationV3_app.selectMarkerV3.orgCode': {
            handler: async function (newValue, oldValue) {
                console.log(newValue, oldValue, '========selectMarkerV3.orgCode');
                // #ifdef MP-MPAAS
                this.getMarketingAssistant();
                // #endif
                if (newValue !== oldValue && this.seletFuel?.fuelName && this.preaAuthNumber) {
                    this.clearSetCouponInfo();
                    await this.getOilPrice();
                }
                if (newValue) {
                    await this.$store.dispatch('getFuelGunByOrgCodePost');
                }
            },
            immediate: true,
            deep: true,
        },
    },
    mounted() {},
    methods: {
        isScrollTop(data) {
            this.scrollTopData = data;
            console.log(data, 'scrollTopData');
        },
        chooseProductItem(item) {
            this.cacheSeletProduct = item;
        },
        discountCouponText() {
            this.textColor = false;
            console.log(this.selectedCoupon, 'this.selectedCoupon======');
            if (this.selectedCoupon) {
                const coupon = this.selectedCoupon.couponInfo;
                if (Object.keys(coupon).length > 0) {
                    this.textColor = true;
                    if (coupon.couponType == '10') {
                        this.couponText = '-￥' + coupon.couponAmount;
                    } else {
                        this.couponText = coupon.couponAmount + '折';
                    }
                } else {
                    this.couponText = this.availableList.length + '张可用';
                }
            } else {
                this.couponText = this.availableList.length + '张可用';
            }

            // if (this.selectedCoupon && Object.keys(this.selectedCoupon).length == 0) return this.availableList.length + '张可用';
            // const coupon = this.selectedCoupon.couponInfo;
            // if (coupon && Object.keys(coupon).length == 0) {
            //     return this.availableList.length + '张可用';
            // } else {
            //     this.textColor = true;
            //     if (coupon.couponType == '10') {
            //         return '-￥' + coupon.couponAmount;
            //     } else {
            //         return coupon.couponAmount + '折';
            //     }
            // }
        },
        // 信息改变清除缓存
        clearSetCouponInfo() {
            // this.$store.commit('setSelectedCoupon', {});
            this.selectedCoupon = null;
            // #ifdef MP-MPAAS
            this.$cnpcBridge.removeValueToNative('Define_Selected_Coupon');
            // #endif
            // #ifndef MP-MPAAS
            uni.setStorageSync('setSelectCoupon', '');
            // #endif
        },
        // 跳转优惠券详情
        toCouponDetail() {
            if (!this.availableList.length) return;
            this.isToSelectTicketPage = true;
            let params = {
                availableList: this.availableList,
                unCouponList: this.unCouponList,
            };
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                params.zfbRefererMax = true;
            }
            // #endif
            // #ifdef H5-CLOUD
            params.zfbRefererMax = true;
            // #endif
            let URL = '/packages/third-coupon-module/pages/coupon-list-entrance/main';
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        // 获取油品单价
        async getOilPrice() {
            // 在订单存在的时候，不允许调用查询e享加油获取优惠券接口
            if (this.circularOrder) return;
            console.log(this.seletFuel?.fuelName, this.preaAuthNumber, this.selectMarkerV3, this.selectMarkerV3.orgCode, '测试66666');
            if (
                this.seletFuel?.fuelName &&
                this.seletFuel?.categoryCode &&
                this.seletFuel?.fuelNo &&
                this.preaAuthNumber &&
                this.selectMarkerV3 &&
                this.selectMarkerV3.orgCode
            ) {
                let params = {
                    orgCode: this.selectMarkerV3.orgCode, //站点编码
                    productNoList: [this.seletFuel.fuelNo], //油品编号
                };

                let res = await oilPriceApi(params, { isCustomErr: true });
                console.log(res, 'oilPriceApi======');
                if (res.success && res.data.length > 0) {
                    await this.getCouponList(res.data[0]);
                } else {
                    this.availableList = []; //可用券列表
                    this.unCouponList = []; //不可用券列表
                    this.discountCouponText();
                }
            }
        },
        // 获取优惠券信息
        async getCouponList(val) {
            let params = {
                stationCode: this.selectMarkerV3.orgCode, //站点编码
                orderMoney: this.preaAuthNumber, //预约加油订单金额
                productList: [
                    {
                        productCode: this.seletFuel.fuelNo, //商品编码
                        categoryNo: this.seletFuel.categoryCode, //品类编码
                        money: this.preaAuthNumber, //应收金额
                        unitPrice: val.listedPrice, //零售价
                    },
                ],
            };
            let res = await preAuthGetCouponList(params);
            console.log(res, 'preAuthGetCouponList===');
            if (res.success) {
                // 传递券列表
                if (res.data.usableList && res.data.usableList.length) {
                    this.availableList = res.data.usableList;
                } else {
                    this.availableList = [];
                }
                this.unCouponList = res.data.unusableList;
                this.discountCouponText();
            }
        },
        /**
         * @description : 不显示"站"字
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        popupMoving(moveBoolean) {
            this.$emit('reMoveing', moveBoolean);
        },
        setGuideStep(step) {
            this.$store.dispatch('changeGuideStep', step);
        },
        async storageDataFun() {
            // #ifdef MP-MPAAS
            let res = await this.$cnpcBridge.getValueToNativePromise('Define_Selected_Coupon');
            this.selectedCoupon = res ? JSON.parse(decodeURIComponent(res)) : null;
            // #endif
            // #ifndef MP-MPAAS
            this.selectedCoupon = uni.getStorageSync('setSelectCoupon') ? uni.getStorageSync('setSelectCoupon') : null;
            // #endif
            if (this.isToSelectTicketPage) {
                this.isToSelectTicketPage = false;
                this.discountCouponText();
                //选券回来不执行
            } else if (!this.isPreaAuthing) {
                await this.getPreAuthFuel();
                await this.getPreAuthPrice();
                this.getPreAuthorizationDetails();
                this.getOilPrice();
            }
            this.$store.dispatch('getAccountBalanceAction');
            this.$store.dispatch('getSetWalletStatus');
        },
        // 拿到e享加油油品信息
        async getPreAuthFuel() {
            return new Promise(async resolve => {
                if (this.seletFuel.fuelName) {
                    resolve();
                    return;
                }
                let value;
                // #ifdef MP-MPAAS
                value = await this.$cnpcBridge.getValueToNativePromise('Define_PreAuth_Fuel');
                this.seletFuel = value ? JSON.parse(decodeURIComponent(value)) : '';
                this.$store.commit('setLoadingStatus', false);
                // #endif
                // #ifndef MP-MPAAS
                value = uni.getStorageSync('Define_PreAuth_Fuel');
                this.seletFuel = value ? JSON.parse(decodeURIComponent(value)) : '';
                // #endif
                resolve();
            });
        },
        // 拿到e享加油油品金额
        async getPreAuthPrice() {
            return new Promise(async resolve => {
                if (this.preaAuthNumber) {
                    resolve();
                    return;
                }
                let value;
                // #ifdef MP-MPAAS
                value = await this.$cnpcBridge.getValueToNativePromise('Define_PreAuth_Price');
                if (value) {
                    this.preaAuthNumber = Number(value) ? Number(value) : '';
                    this.priceText = Number(value) ? Number(value) : '';
                }
                // #endif
                // #ifndef MP-MPAAS
                value = uni.getStorageSync('Define_PreAuth_Price');
                console.log('value-----', value);
                if (value) {
                    this.preaAuthNumber = Number(value) ? Number(value) : '';
                    this.priceText = Number(value) ? Number(value) : '';
                } else {
                    this.preaAuthNumber = '';
                    this.priceText = '';
                }
                // #endif
                resolve();
            });
        },
        // 跳转预授权待支付订单
        authCode() {
            let URL = '/packages/third-oil-charge-payment/pages/authorization-code/main';
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, { ...this.infoAuthOrder, refer: this.refer }, type);
        },

        // 充值和去开通跳转
        rechargeClick() {
            // 去充值
            if (!this.isWalletStatusCard) {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        this.$sKit.layer.useRouter(
                            '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                            { refer: 'r16' },
                            'navigateTo',
                        );
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    walletAddParams: {
                        refer: 'r02',
                    },
                });
            } else {
                // 去开通
                this.$sKit.layer.useRouter('/packages/third-electronic-wallet/pages/wallet-add-form/main', { refer: 'r02' }, 'navigateTo');
            }
        },
        /**
         * @description : 查询预授权订单详情
         * data : 预授权订单状态1创建；5已取消；3开始加油；4加油结束；
         * @return        {*}
         */
        async getPreAuthorizationDetails() {
            let res = await preAuthOrderApi({}, { isload: false });
            if (res.success) {
                // #ifdef H5-CLOUD
                if (this.isAuthCloud) {
                    this.isAuthCloud = false;
                    this.realNameAuthentication(this.faceValueResponse);
                }
                // #endif
                if (res.data) {
                    this.infoAuthOrder = res.data;
                    this.circularOrder = true;
                    return true;
                } else {
                    this.circularOrder = false;
                    return false;
                }
            } else {
                this.circularOrder = false;
                return false;
            }
        },
        // 根据油站编码查询可下单距离接口
        // async distanceConfigPost() {
        //     let params = {
        //         stationCode: this.selectMarkerV3.orgCode,
        //     };
        //     let res = await distanceConfigApi(params, { isCustomErr: true });
        //     if (res.success) {
        //         this.preAuthOrderDistance = res.data;
        //     }
        // },
        // 查询油品标号对应的商品信息
        async getCorrespondingProducts() {
            console.log('this.seletFuel.fuelType', this.seletFuel.fuelType);
            // if(this.seletFuel && this.seletFuel.fuelType){
            let params = {
                orgCode: this.selectMarkerV3.orgCode,
                fuelType: this.seletFuel.fuelType,
            };
            // Vue.prototype.$console('已经有订单信息了', params)
            let res = await getProductInfo(params, { isload: false });
            if (res.success) {
                this.productCodeAndNameList = res.data || [];

                // 如果返回的数组长度大于一 弹出单选框 用户自己选择e享加油的油品
                // if (this.productCodeAndNameList.length > 1) {
                //     // this.oilPopWindowFlag = true
                //     // 如果返回的数组长只有一条数据 那么就取这一条
                // }
                // else
                if (this.productCodeAndNameList.length !== 0) {
                    if (this.productCodeAndNameList.length == 1) {
                        this.fuelType = this.productCodeAndNameList[0].productNo;
                        this.fuelName = this.productCodeAndNameList[0].productName;
                        this.preAuthorizedRefueling();
                    } else {
                        uni.hideLoading();
                        this.chooseOilNumber('oilProductFlag');
                    }
                    // 如果当前数组 === 0 提示没当前
                } else if (this.productCodeAndNameList.length === 0) {
                    uni.hideLoading();
                    uni.showToast({
                        title: '当前油站无所选油品',
                        icon: 'none',
                        duration: 2000,
                    });
                    return;
                }
            } else {
                uni.hideLoading();
            }
            // }
        },
        // 取消订单
        async cancelAction(dataInfo) {
            let params = {
                preAuthOrderNo: dataInfo.preAuthzOrderNo,
                stationCode: dataInfo.stationCode,
            };
            let res = await preAuthCancel(params);
            if (res.success) {
                this.$store.dispatch('getAccountBalanceAction');
                this.$store.dispatch('getSetWalletStatus');
                this.circularOrder = false;
                // this.getPreAuthorizationDetails()
                // this.$store.commit('mSetInfoAuthOrder', {})
                // uni.showToast({
                //     title: '订单取消成功',
                //     icon: 'none',
                //     duration: 2000,
                // });
            }
        },
        // 调用e享加油接口
        async preAuthorizedRefueling() {
            let params = {
                stationCode: this.selectMarkerV3.orgCode, //网点编码
                stationName: this.selectMarkerV3.orgName, //网点名称
                preAuthzAccount: this.walletInfo.ewalletNo, //昆仑e享卡账号（e享加油必填）
                productNo: this.fuelType, //商品编码（油品编码)
                productName: this.fuelName, //商品名称（油品名称）
                preAuthzAmount: this.preaAuthNumber, //预授权金额（e享加油必填，单位：元）
                usedInterestsAccount: '1', // 是否使用权益账户（e享加油，默认使用）1—是；0—否,// 是否使用权益账户（e享加油，默认使用）1—是；0—否
                // licensePlate: this.carListV3[0].licensePlate, //车牌号(e享加油需传)
                // licensePlateColor: this.carListV3[0].licensePlateColor, // 车牌颜色
                couponNo: this.selectedCoupon?.couponInfo?.couponNo,
                couponTemplateNo: this.selectedCoupon?.couponInfo?.couponTemplateNo,
                calDiscountFlag: this.selectedCoupon ? (this.selectedCoupon.switchTab ? 0 : 1) : 0, //是否匹配最优标志 0-需要,1-不需要。默认1
            };
            console.log(params, '------generateOrder----params------');
            let res = await generateOrder(params, { isload: false });
            console.log(res, 'generateOrder----res');
            if (res.success) {
                this.preOrderData = res.data;
                console.log('接口预下单成功', res.data);
                this.isPreaAuthing = true;
                this.dataInfo = res.data;
                // 调用插件进行预下单
                this.preLicensingPlaceAnOrder(res.data);
            } else {
                uni.hideLoading();
            }
        },
        // 实人认证弹窗的确认事件
        realNameInfo(val) {
            this.$sKit.commonUtil
                .triggerRiskAuth(val.name, val.idNumber)
                .then(res => {
                    uni.showLoading({
                        mask: true,
                    });
                    this.realNameDialogFlag = false;
                    // #ifdef H5-CLOUD
                    if (res) {
                        this.faceValueResponse = res;
                        this.isAuthCloud = true;
                        upsdk.pluginReady(function () {
                            upsdk.createWebView({
                                url: res.certifyUrl,
                                isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                            });
                        });
                    }
                    // #endif
                    // #ifndef H5-CLOUD
                    this.preLicensingPlaceAnOrder(this.preOrderData, true);
                    // #endif
                })
                .catch(err => {
                    uni.showToast({ title: err });
                });
        },
        // 关闭实人认证弹窗
        realNameDialogClose() {
            this.realNameDialogFlag = false;
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);

            this.$sKit.commonUtil
                .nextOilTriggerRisk()
                .then(res => {
                    // #ifdef H5-CLOUD
                    if (res) {
                        this.faceValueResponse = res;
                        this.isAuthCloud = true;
                        upsdk.pluginReady(function () {
                            upsdk.createWebView({
                                url: res.certifyUrl,
                                isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                            });
                        });
                    }
                    // #endif
                    // #ifndef H5-CLOUD
                    this.preLicensingPlaceAnOrder(this.preOrderData, true);
                    // #endif
                })
                .catch(res => {
                    this.cancelAction(this.dataInfo);
                });
        },
        // 选择油号,金额弹窗
        async chooseOilNumber(data) {
            // 如果存在订单，那么禁止点击选择油品和禁止更改金额
            if (this.circularOrder) return;
            this.oilDialogFlagType = data;
            // this.$store.commit('setFuelData', [])
            this.$refs.popDialogFlag.open();
            if (this.oilDialogFlagType == 'oilDialogFlag') {
                this.$sKit.mpBP.tracker('e享加油', {
                    seed: 'exoilBiz',
                    pageID: 'selectOilGunBut',
                    refer: this.refer,
                    channelID: clientCode,
                    address: this.cityName,
                });
                this.confirmText = '确认';
                this.confirmColor = '#000';
                if (this.fuelData.length <= 0) {
                    await this.$store.dispatch('getFuelGunByOrgCodePost');
                }
                if (this.fuelData.length <= 0) {
                    this.oilDialogFlagType = 'noFuelDataDialogFlag';
                    this.confirmText = '确认';
                    this.cancelText = '';
                }
                return false;
            }
            if (this.oilDialogFlagType == 'oilProductFlag') {
                this.confirmText = '确认';
                this.confirmColor = '#000';
                return false;
            }
            if (this.oilDialogFlagType == 'moneyDialogFlag') {
                this.$sKit.mpBP.tracker('e享加油', {
                    seed: 'exoilBiz',
                    pageID: 'inputAmountBut',
                    refer: this.refer,
                    channelID: clientCode,
                    address: this.cityName,
                });
                if (
                    Object.keys(this.walletInfo).length == 0 ||
                    this.walletInfo.walletBalance * 1 == 0 ||
                    Number(this.priceText) > Number(this.walletInfo.walletBalance)
                ) {
                    this.confirmText = '去充值';
                    this.confirmColor = '#FF6B2C';
                } else {
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                }
            } else {
                this.confirmText = '确认';
                this.confirmColor = '#000';
            }
            this.cancelText = '取消';
        },
        clickBtn(data) {
            this.$refs.popDialogFlag.close();
            if (data == 'confirm') {
                if (this.confirmText == '去充值') {
                    this.$sKit.commonUtil.eWalletNormal({
                        nextFun: () => {
                            this.$sKit.layer.useRouter(
                                '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                                { refer: 'r16' },
                                'navigateTo',
                            );
                        },
                        freezeReasonArr: [9, 10],
                        cancelCallback: res => {
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                        isEnter: 1,
                        walletAddParams: {
                            refer: 'r02',
                        },
                    });
                } else if (this.confirmText == '导航到站') {
                    // #ifdef MP-MPAAS
                    this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                        if (res) {
                            this.$cnpcBridge.openLocation({
                                latitude: this.selectMarkerV3.latitude,
                                longitude: this.selectMarkerV3.longitude,
                                name: this.selectMarkerV3.orgName,
                                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                            });
                        }
                    });
                    // #endif
                    // #ifndef MP-MPAAS
                    uni.openLocation({
                        latitude: Number(this.selectMarkerV3.latitude),
                        longitude: Number(this.selectMarkerV3.longitude),
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                    // #endif
                    // this.clickNaviStateion()
                } else if (this.confirmText == '确认') {
                    if (this.oilDialogFlagType == 'moneyDialogFlag') {
                        if (Number(this.priceText) < 10 || Number(this.priceText) > 5000) {
                            uni.showToast({
                                title: '输入金额不符合加油金额限制',
                                icon: 'none',
                            });
                            this.priceText = '';
                            return;
                        } else if (
                            Object.keys(this.walletInfo).length == 0 ||
                            this.walletInfo.walletBalance * 1 == 0 ||
                            Number(this.priceText) > Number(this.walletInfo.walletBalance)
                        ) {
                            // 判断输入金额是否大于钱包金额 && 钱包金额必须有值
                            this.confirmText = '去充值';
                            this.confirmColor = '#FF6B2C';
                            return;
                        } else {
                            this.$refs.popDialogFlag.close();
                            if (this.preaAuthNumber != this.priceText) {
                                this.preaAuthNumber = this.priceText;
                                this.clearSetCouponInfo();
                                this.getOilPrice();
                            }
                        }
                    } else if (this.oilDialogFlagType == 'oilDialogFlag') {
                        if (this.seletFuel.fuelNo != this.cacheSeletFuel.fuelNo) {
                            this.seletFuel = this.cacheSeletFuel;
                            this.clearSetCouponInfo();
                            this.getOilPrice();
                        }
                    } else if (this.oilDialogFlagType == 'oilProductFlag') {
                        if (this.fuelType != this.cacheSeletProduct.productNo) {
                            this.fuelType = this.cacheSeletProduct.productNo;
                            this.fuelName = this.cacheSeletProduct.productName;
                            this.preAuthorizedRefueling();
                        }
                    }
                }
            } else {
                if (this.oilDialogFlagType == 'moneyDialogFlag') {
                    this.$refs.popDialogFlag.close();
                    this.priceText = '';
                    this.selectedIndex = -1;
                    this.isAllIn = false;
                } else if (this.oilDialogFlagType == 'oilDialogFlag') {
                    this.cacheSeletFuel = this.seletFuel;
                    this.$refs.popDialogFlag.close();
                } else if (this.oilDialogFlagType == 'oilProductFlag') {
                    this.cacheSeletProduct = {};
                    this.$refs.popDialogFlag.close();
                }
                if (this.oilDialogFlagType == 'stationStatus') {
                    this.$refs.popDialogFlag.close();
                }
            }
        },
        // 选择油号
        chooseOilItem(item) {
            console.log(item, 'item----');
            this.cacheSeletFuel = item;
            // this.seletFuel = item; oilNumberData
            // //缓存客户上一次的下单油品号
        },
        // 更换油站点击事件
        changeStationAction() {
            // uni.showLoading({
            //     title: '跳转中',
            // })
            //bookingRefueling e享加油（1代表可以进行e享加油的油站；0代表不可以，不传代表搜索全部油站）
            // this.$console(this.infoAuthOrder)
            if (this.circularOrder) {
                return;
            }
            let URL = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {
                refer: this.refer,
                bizSeed: 'exoilBiz',
                bizTitle: 'e享加油',
            };
            let type = 'navigateTo';
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                this.$sKit.layer.useRouter(URL, params, type);
            } else {
                this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                    if (res) {
                        this.$store.dispatch('initLocationV3_app', {
                            callback: () => {
                                this.$sKit.layer.useRouter(URL, params, type);
                            },
                        });
                    }
                });
            }
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, type);
            // #endif
            // let url = '/packages/third-evaluate/pages/home/<USER>';
            // let type = 'navigateTo';
            // let params = {
            //     evaluateType:'code',
            //     "orgCode":"1-A5001-C001-S017"  ,"placeName":"场所评价测试1"
            // };
            // this.$sKit.layer.useRouter(url, params, type);
        },
        //
        async submitEvent() {
            if (!this.selectMarkerV3.orgCode) {
                uni.showToast({
                    title: '暂未查询到网点',
                    icon: 'none',
                });
                return;
            }
            // await this.distanceConfigPost();
            if (this.selectMarkerV3.stationStatus != 20 && this.selectMarkerV3.stationStatus != 10) {
                uni.showToast({
                    title: '网点暂未营业',
                    icon: 'none',
                });
                return;
            }

            if (this.selectMarkerV3.bookingRefueling != 1) {
                uni.showToast({
                    title: '此站不支持e享加油',
                    icon: 'none',
                });
                return;
            }

            console.log('this.preAuthOrderDistance.distance', this.preAuthOrderDistance.distance);
            console.log('distance', this.selectMarkerV3.distance);
            this.$store.dispatch('initLocationV3_app', {
                callback: async () => {
                    let limitDistance;
                    if (this.preAuthOrderDistance.distance == '' || this.preAuthOrderDistance.distance == null) {
                        limitDistance = 10;
                    } else {
                        limitDistance = this.preAuthOrderDistance.distance;
                    }
                    if (Number(this.selectMarkerV3.distance) > Number(limitDistance)) {
                        this.oilDialogFlagType = 'distanceDialogFlag';
                        this.$refs.popDialogFlag.open();
                        // #ifndef H5-CLOUD
                        this.confirmText = '导航到站';
                        // #endif
                        // #ifdef H5-CLOUD
                        this.confirmText = '';
                        // #endif
                        this.cancelText = '稍后使用';
                        this.confirmColor = '#000';
                        return;
                    } else {
                        // this.submitEvent()
                        if (this.circularOrder) {
                            uni.showToast({
                                title: '存在待加油订单，您可完成或取消订单后再次操作',
                                icon: 'none',
                            });
                            return;
                        }
                        if (this.fuelData.length <= 0) {
                            await this.$store.dispatch('getFuelGunByOrgCodePost');
                        }
                        if (this.fuelData.length <= 0) {
                            this.oilDialogFlagType = 'noFuelDataDialogFlag';
                            this.confirmText = '确认';
                            this.cancelText = '';
                            this.$refs.popDialogFlag.open();
                            return false;
                        }
                        if (Object.keys(this.seletFuel).length == 0 || this.seletFuel.fuelName == '') {
                            uni.showToast({
                                title: '请选择油号',
                                icon: 'none',
                            });
                            return;
                        }
                        if (this.fuelData.filter(item => this.seletFuel.fuelName == item.fuelName).length == 0) {
                            uni.showToast({
                                title: '当前油站无所选油品',
                                icon: 'none',
                            });
                            return;
                        }
                        //

                        if (!this.preaAuthNumber) {
                            uni.showToast({
                                title: '请输入预授权金额',
                                icon: 'none',
                            });
                            return;
                        }
                        if (Number(this.preaAuthNumber) < 10 || Number(this.preaAuthNumber) > 5000) {
                            uni.showToast({
                                title: '输入金额不符合加油金额限制',
                                icon: 'none',
                            });
                            return;
                        }
                        // 判断输入金额是否大于钱包金额 && 钱包金额必须有值
                        if (
                            Object.keys(this.walletInfo).length == 0 ||
                            this.walletInfo.walletBalance * 1 == 0 ||
                            (Number(this.preaAuthNumber) > Number(this.walletInfo.walletBalance) && Number(this.preaAuthNumber) < 5000)
                        ) {
                            this.oilDialogFlagType = 'balanceFlag';
                            this.$refs.popDialogFlag.open();
                            this.confirmText = '去充值';
                            this.confirmColor = '#FF6B2C';
                            return;
                        }
                        // 查询预授权订单详情 如果有存在的订单就不向下执行 跳转相应页面
                        uni.showLoading();
                        if (!(await this.getPreAuthorizationDetails())) {
                            this.getCorrespondingProducts();
                        } else {
                            uni.hideLoading();
                        }
                    }
                },
                type: 'onlyLocation',
            });
        },
        // 提交e享加油订单
        async submitOrderBefore() {
            this.$sKit.mpBP.tracker('e享加油', {
                seed: 'exoilBiz',
                pageID: 'submitOrderBut',
                refer: this.refer,
                channelID: clientCode,
                djAmount: this.preSetAmountList[this.selectedIndex]?.title == '加满' ? '加满' : this.priceText,
                address: this.cityName,
            });
            // if(this.isHarmony){
            //     uni.showToast({
            //         title: '暂未开放，敬请期待',
            //         icon: 'none',
            //     });
            //     return;
            // }else{
            if (this.selectMarkerV3.stationStatus != 20 && this.selectMarkerV3.stationStatus != 10) {
                uni.showToast({
                    title: '网点暂未营业',
                    icon: 'none',
                });
                return;
            }
            // 3s内只允许点击一次
            if (!this.timer) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.setClickEvent('submit_order');
                // #endif
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        // this.$store.dispatch("initLocationV3_app", {
                        //     callback: () => {
                        // if (Number(this.selectMarkerV3.distance) > 10) {
                        //     this.oilDialogFlagType = 'distanceDialogFlag'
                        //     this.$refs["popDialogFlag"].open()
                        //     this.confirmText = "导航到站"
                        //     this.cancelText = "稍后使用"
                        //     this.confirmColor = '#000'
                        //     return
                        //     }
                        this.submitEvent();
                        // }
                        // })
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    walletAddParams: {
                        refer: 'r02',
                    },
                });
                this.timer = setTimeout(() => {
                    this.timer = null;
                }, 3000);
            }
            // }
        },
        //点击打开导航
        // 传入起始点startlong startlat 和终点的经纬度 endlong  endlat
        clickNaviStateion() {
            if (this.selectMarkerV3) {
                if (this.selectMarkerV3.stationStatus != 20 && this.selectMarkerV3.stationStatus != 10) {
                    this.oilDialogFlagType = 'stationStatus';
                    this.$refs.popDialogFlag.open();
                    this.confirmText = '导航到站';
                    this.cancelText = '稍后使用';
                    this.confirmColor = '#000';
                } else {
                    // #ifdef MP-MPAAS

                    this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                        if (res) {
                            this.$cnpcBridge.openLocation({
                                latitude: this.selectMarkerV3.latitude,
                                longitude: this.selectMarkerV3.longitude,
                                name: this.selectMarkerV3.orgName,
                                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                            });
                        }
                    });
                    // #endif
                    // #ifndef MP-MPAAS
                    uni.openLocation({
                        latitude: Number(this.selectMarkerV3.latitude),
                        longitude: Number(this.selectMarkerV3.longitude),
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                    // #endif
                }
            }
        },
        // 输入自定义金额
        otherAmountsInput(e) {
            // console.log('this.balanceData',Number(this.walletInfo.walletBalance))
            let value = e.detail.value;
            // this.$console('value', value)
            var price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            if (this.priceText != price || price[0]) {
                this.selectedIndex = -1;
            }
            this.$nextTick(() => {
                this.priceText = typeof price === 'string' ? price : price[0];
                // this.$console('value', this.walletInfo)
                if (Number(this.priceText) > Number(this.walletInfo.walletBalance)) {
                    this.isAllIn = true;
                } else {
                    this.isAllIn = false;
                }
                if (Number(this.priceText) < 10 || Number(this.priceText) > 5000) {
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                } else if (
                    Object.keys(this.walletInfo).length == 0 ||
                    this.walletInfo.walletBalance * 1 == 0 ||
                    Number(this.priceText) > Number(this.walletInfo.walletBalance)
                ) {
                    this.confirmText = '去充值';
                    this.confirmColor = '#FF6B2C';
                } else {
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                }
            });
        },
        /**
         * @description : 进入e享购页面
         * @return        {*}
         */
        shoppingHandle() {
            let routerParams = {
                shopId: this.selectMarkerV3.orgCode, // 店铺id
                showCart: false, // 是否弹出购物车, 默认false
            };
            console.log(routerParams, '跳转o2o参数');
            let url = `/packages/o2o-shop/pages/shop-home/main?data=${encodeURIComponent(
                encodeURIComponent(JSON.stringify(routerParams)),
            )}`;
            this.$sKit.layer.cubeMini(url, '4908542685197380');
        },
        cancelClick() {
            this.cancelAction(this.dataInfo);
        },
        async getMarketingAssistant() {
            let newStationCodeList = await this.$sKit.layer.getMarketingJudgment([this.selectMarkerV3.orgCode]);
            if (newStationCodeList.length > 0) {
                let params = {
                    stationCodeList: newStationCodeList,
                    sceneType: 0,
                };
                let res = await getMarketingRec(params, { isCustomErr: true });
                if (res && res.success) {
                    let newArr = res.data || [];
                    this.marketingAssistant = newArr[0].aiGeneratedCopy;
                    let bizContent = [
                        {
                            stationcode: newArr[0].stationCode,
                            scenetype: newArr[0].sceneType,
                            marketingCode: newArr[0].activityId,
                            marketingContent: newArr[0].aiGeneratedCopy,
                        },
                    ];
                    bizContent = JSON.stringify(bizContent).replace(/,/g, ' ');
                    this.$sKit.mpBP.tracker('智能营销文档曝光', {
                        seed: 'smart_marketing',
                        pageID: 'exoilPage', // 返回sdk标识
                        refer: this.refer,
                        channelID: clientCode,
                        dateType: 'exposure',
                        content: bizContent,
                    });
                }
            } else {
                this.marketingAssistant = '';
            }
        },
        selectAmount(item, index) {
            this.selectedIndex = index;
            setTimeout(() => {
				this.isAllIn = true;
                if (item.title === '加满') {
                    this.priceText = this.walletInfo.walletBalance;
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                    return;
                }
                this.priceText = item.price;
                if (Number(this.priceText) < 10 || Number(this.priceText) > 5000) {
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                } else if (
                    Object.keys(this.walletInfo).length == 0 ||
                    this.walletInfo.walletBalance * 1 == 0 ||
                    Number(this.priceText) > Number(this.walletInfo.walletBalance)
                ) {
                    this.confirmText = '去充值';
                    this.confirmColor = '#FF6B2C';
                } else {
                    this.confirmText = '确认';
                    this.confirmColor = '#000';
                }
            }, 100);
        },
    },
    // beforeDestroy() {
    //     this.$store.dispatch('zjHideModal');
    // },
};
</script>

<style lang="scss" scoped>
.font-14 {
    font-size: 14px;
}

.page-view {
    // height: 100%;
    // max-height: 420px;
    width: 100%;
    // overflow-y: scroll;
    flex: 1;
    position: relative;
}

.guideBox {
    .guideImg {
        position: absolute;
        width: 100%;
        top: -154px;
        right: 0;
        left: 0;
        z-index: 999999;
    }

    .guideBtton {
        display: flex;
        position: absolute;
        top: -53px;
        z-index: 9999999;
        right: 70px;

        div {
            height: 51rpx;
            border-radius: 34rpx;
            border: 2rpx solid #ffffff;
            font-size: 24rpx;
            line-height: 51rpx;
            text-align: center;
            width: 69px;
            margin-right: 10px;

            &:nth-of-type(1) {
                color: #fff;
            }

            &:nth-of-type(2) {
                color: #333;
                background-color: #fff;
            }
        }
    }
}

.height420 {
    // max-height: 420px;
    height: 100%;
}

.zjMarketBox {
    transform: translateY(5px);
}

.charge-content-view {
    border-radius: 10px 10px 0px 0px;
    /* #ifdef MP-MPAAS */
    padding-bottom: 40rpx;
    /* #endif */
    width: 100%;
    // margin-top: -5px;
    position: relative;

    .top-area {
        margin: 0px 15px;
        box-sizing: border-box;

        .stationinfo {
            .detail {
                overflow: hidden;

                // #ifndef MP-ALIPAY
                .name-area {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 180px;

                    .name {
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // white-space: nowrap;
                    }
                }

                // #endif
                // #ifdef MP-ALIPAY
                .name-area {
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 180px;
                    }
                }

                // #endif
                .pad-t-6 {
                    padding-top: 8px;
                }

                .oil-address {
                    padding-top: 12px;
                }

                .time-icon {
                    // width: 52px;
                    // height: 20px;
                    // line-height: 20px;
                    padding: 4px 5px;
                    text-align: center;
                    background: #efeded;
                    border-radius: 4px;
                    margin: 0 4px;
                }
            }

            .margin-box {
                width: 20px;
            }

            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                // margin-left: 20px;
                display: block;
            }

            .center-area {
                padding-top: 12px;

                .con-list {
                    justify-content: flex-start;
                    width: 100%;
                    flex-wrap: wrap;

                    .item {
                        padding: 8rpx 16rpx;
                        background: #f3f3f6;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        color: #333333;
                        text-align: center;
                        margin: 0 18rpx 8rpx 0;
                    }

                    // .item:nth-of-type(4n + 0) {
                    //     margin-right: 0px;
                    // }
                }
            }

            .marketing-assistant {
                margin-top: 5px;
                border: 1px solid #c4c4c3;
                background-color: #fef5ee;
                border-radius: 8px 8px;
                padding: 4.5px;

                .marketing-text {
                    font-size: 12px;
                    color: #fb9463;
                    line-height: 17.5px;
                    overflow: hidden;
                    /* 隐藏超出的文本 */
                    display: -webkit-box;
                    /* 将对象作为弹性伸缩盒子模型显示 */
                    -webkit-line-clamp: 2;
                    /* 限制在一个块元素显示的文本的行数 */
                    -webkit-box-orient: vertical;
                    /* 设置或检索伸缩盒对象的子元素的排列方式 */
                    word-break: break-all;
                    /* 允许在单词内换行 */
                }
            }
        }

        .changestation {
            margin-left: 12px;
            width: 56px;
            height: 17px;
            line-height: 34rpx;
            align-items: center;
            justify-content: center;
            display: flex;
            padding: 2px 0;
            text-align: center;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            border-radius: 4px;
            border: 1px solid #e64f22;
            box-sizing: border-box;
        }

        .margin-8-l-r {
            margin: 0 8px;
        }

        .center-area {
            position: relative;
            padding-top: 16px;
            // padding-bottom: 16px;
            box-sizing: border-box;

            .width107 {
                width: 107px;
            }

            .width75 {
                width: 75px;
            }

            .height35 {
                height: 35px;
                line-height: 35px;
            }

            .mar-r-8 {
                margin-right: 8px;
            }

            .pdtl4 {
                padding-top: 4px;
                padding-left: 4px;
                box-sizing: border-box;
            }
        }

        .card-nav {
            padding: 0 12px 19px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .marl5 {
                margin-left: 5px;
            }
        }

        .card-nav-no {
            padding: 0 12px 19px;
            box-sizing: border-box;

            .marl5 {
                margin-left: 5px;
            }
        }
    }

    .mart12 {
        margin-top: 12px !important;
    }

    .marl10 {
        margin-left: 10px;
    }

    .equity-div {
        margin: 0px 15px;
        height: 33px;
        border-radius: 0px 0px 8px 8px;
        line-height: 33px;
        box-sizing: border-box;
    }

    .btn-44 {
        margin: 0 16px;
        height: 44px;
        margin-top: 16px;
        border-radius: 8px;
        line-height: 44px;
    }

    .market-div {
        margin: 12px 16px 0;
    }

    .advertisingPopups {
        position: fixed;
        bottom: 0;
        top: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        z-index: 998;
        background-color: rgba(0, 0, 0, 0.6);
    }
}

// #ifndef MP-ALIPAY
.activename {
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    // text-overflow: ellipsis;
}

// #endif
// #ifdef MP-ALIPAY
.activename {
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

// #endif

.mart20 {
    margin-top: 20px;
}
/* 每个项目的样式 */
.fl-row-item {
    // text-align: center;
    // flex: 1;
    box-sizing: border-box;
    margin-bottom: 10px;
    .text {
        margin-top: 1px;
    }
}

/* 项目内部内容样式 */
.fl-row-item > div {
    width: 55px;
    height: 35px;
    line-height: 35px;
    border-radius: 4px;
    // text-align: center;
    transition: all 0.2s; /* 添加过渡效果 */
}

.active {
    border: 1px solid #ff6b2c;
}

.no-reserve {
    width: 71px !important;
    height: 59px;
    display: block;
}

.marl12 {
    margin-left: 12px;
}

.mat5 {
    margin-top: 5px;
}

.marb17 {
    margin-bottom: 17px;
}

.mart14 {
    margin-top: 14px;
}

.marb10 {
    margin-bottom: 10px;
}

.marl5 {
    margin-left: 5px;
}

._modal {
    flex: none;
    width: 280px;
    min-height: 104px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 19px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 34px;
        }

        .item {
            height: 31px;
            font-size: 15px;
            font-weight: 400;
            color: #666666;
            line-height: 31px;
            text-align: center;
            padding: 0 12px;
        }

        .active-item {
            height: 31px;
            font-size: 15px;
            font-weight: 500;
            color: #ff6b2c;
            line-height: 31px;
            background: #f7f7fb;
            border-radius: 8px;
            text-align: center;
            padding: 0 12px;
            margin-left: 16px;
            margin-right: 16px;
        }

        .price-input-area {
            border-radius: 10px;
            height: 44px;
        }

        .price-input {
            width: 100%;
            height: 44px;
            min-height: 44px;
            padding-top: 5px;
            padding-bottom: 5px;
            line-height: 44px;
            background: #f7f7fb;
            border-radius: 8px;
            text-align: center;
            box-sizing: border-box;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}

.patb10 {
    padding: 10px 0 0;
}

.marr3 {
    margin-right: 3px;
}

.card-reser {
    margin: 0 16px 6px;
    padding: 9px 12px;
    border: 1px solid rgba(255, 107, 44, 0.14);
}

.padding12 {
    padding: 12px;
    box-sizing: border-box;
}

.coupon_div {
    width: 100%;
    height: 48rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-bottom: 10px;
    padding: 0 20rpx;
}

.coupon_div_left {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.coupon_div_left_img {
    width: 48rpx;
    height: 48rpx;
}

.coupon_div_left_title {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    margin-left: 9px;
}

.coupon_div_right_text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    margin-right: 2px;
}

.color-E64F22 {
    color: #e64f22 !important;
}

.coupon_div_right_icon {
    width: 32rpx;
    height: 32rpx;
}

.coupon_div_right {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}
.convenienceStoreImg {
    width: 606rpx;
    height: 253rpx;
}
</style>
