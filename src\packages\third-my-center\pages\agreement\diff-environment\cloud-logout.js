 import { unBindThirdUser } from '../../../../../s-kit/js/v3-http/https3/classInterest/index';
 import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
 import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
 
 export default {
    // #ifdef H5-CLOUD
     mounted() {},
     methods: {
         /**
          * @description     : 退出登录
          * @return        {*}
          */
         async logout() {
             // let res = await unBindThirdUser({ regChannel: 6 });
             if (this.$store.state.token3 || this.$store.state.token3 !== '') {
                 await logoutApi();
                 this.$store.commit('setMpUserInfo', null);
                 this.$store.commit('setCarListV3', []);
                 // this.$store.commit("mSetInfoV3", false);
             }
 
             this.$store.state.token3 = '';
             this.$store.state.token = '';
             this.$store.commit('setVenicleList', []);
             this.$store.commit('card/setCardList', []);
             this.$store.commit('setBannerListCity', []);
             this.$store.commit('setBannerListCountry', []);
             this.$store.commit('card/setIsHaveECard', false);
             this.$store.commit('card/setIsHaveEntityCard', false);
             this.$store.commit('setSource', '');
             this.$store.commit('setLoginStatus', false);
             this.$store.commit('mSetPersonalInformation3', {});
             this.$store.commit('setUserInfo', {});
             this.$store.commit('setMpUserInfo', {});
             this.$store.commit('setLongTimeNotLogin', null);
             this.$store.commit('setCleanTheRegisteredAddress', true);
             uni.setStorageSync('tokenInfo', '');
            try {
                uni.clearStorageSync();
            } catch (e) {
                // error
            }
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
         },
     },
     // #endif
 };
 
