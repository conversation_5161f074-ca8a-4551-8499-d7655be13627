import { unBindThirdUser } from '../../../../../s-kit/js/v3-http/https3/classInterest/index';
import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';

export default {
    // #ifdef MP-WEIXIN
    mounted() {},
    methods: {
        /**
         * @description     : 退出登录
         * @return        {*}
         */
        async logOut() {
            let res = await logoutApi();
            if (res.success) {
                this.$store.commit('setMpUserInfo', null);
                this.$store.commit('setCarListV3', []);
                this.$store.state.token3 = '';
                this.$store.state.token = '';
                this.$store.state.location.officialAccountParams = '';
                this.$store.commit('setVenicleList', []);
                this.$store.commit('card/setCardList', []);
                this.$store.commit('setBannerListCity', []);
                this.$store.commit('setBannerListCountry', []);
                this.$store.state.location.moduleBannerListShow = false;
                this.$store.state.location.moduleBannerList = [];
                this.$store.commit('card/setIsHaveECard', false);
                this.$store.commit('card/setIsHaveEntityCard', false);
                this.$store.state.location.advertisementFlag = 0;
                this.$store.state.location.myPageAdvertisementFlag = 0;
                this.$store.commit('setSource', '');
                this.$store.commit('setLoginStatus', false);
                this.$store.commit('mSetPersonalInformation3', {});
                this.$store.commit('setUserInfo', {});
                this.$store.commit('setMpUserInfo', {});
                this.$store.commit('setLongTimeNotLogin', null);
                this.$store.commit('setCleanTheRegisteredAddress', true);
                uni.setStorageSync('tokenInfo', '');
                wx.clearStorageSync();
                uni.reLaunch({
                    url: '/pages/thirdHome/main',
                });
            }
        },
    },
    // #endif
};
