<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            :background="pageConfig.bgColor"
            back-text="所在省市选择"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div
            class="location-top-view"
            :style="{
                top: navH + 'px',
            }"
        >
            <div class="location-title-view">
                <div class="location-title">您当前所在省份</div>
                <div class="location-address">
                    <img class="location-address-icon" src="@/static/homeIcon/location-icon-withe.png" />
                    <div class="location-address-text">{{ province }}</div>
                </div>
            </div>
        </div>
        <scroll-view
            class="location-list-scroll"
            :style="{
                top: navH + 100 + 'px',
                height: screenHeight - navH - 100 + 'px',
            }"
        >
            <div class="location-list-view" style="background-color: ">
                <div
                    class="location-list-item"
                    :style="{
                        border: selectLocatinIndex == index ? '0.5px solid #118820' : '0.5px solid #ffffff',
                        backgroundColor: selectLocatinIndex == index ? '#EDFFEF' : '#ffffff',
                        color: selectLocatinIndex == index ? '#118820' : '#333333',
                    }"
                    v-for="(item, index) in locationArr"
                    :key="index"
                    @click="clickProviceItem(index)"
                    >{{ item.shortName }}</div
                >
            </div>
        </scroll-view>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            /**********上方为假数据***********/
            pageConfig: pageConfig, // 页面配置
            navH: 64, // 导航高度
            screenHeight: 667, // 屏幕高度
            selectLocatinIndex: -1,
        };
    },
    onLoad() {
        let systemInfo = uni.getSystemInfoSync();
        this.navH = 44 + systemInfo.statusBarHeight;
        this.screenHeight = systemInfo.screenHeight;
        this.selectLocatinIndex = this.locationArr.findIndex(item => {
            return item.fullName == this.province;
        });
    },
    computed: {
        ...mapState({
            province: state => state.location.province, // 省份
            locationArr: state => state.location.locationArr, // 省份位置信息
        }),
    },
    methods: {
        clickProviceItem(index) {
            this.selectLocatinIndex = index;
            this.$store.dispatch('uploadProvince', {
                suc: () => {
                    uni.navigateBack();
                },
                selectIndex: index,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
}
.location-top-view {
    position: fixed;
    left: 0;
    width: 100vw;
    display: flex;
    align-items: center;
    height: 100px;
    background-color: $bg-color;
    .location-title-view {
        margin-left: 17.5px;
        .location-title {
            font-size: 13px;
            color: #ffffff;
        }
        .location-address {
            display: flex;
            align-items: center;
            margin-top: 11px;
            .location-address-icon {
                width: 13px;
                height: 16px;
                display: block;
            }
            .location-address-text {
                margin-left: 4px;
                font-size: 18px;
                font-weight: 700;
                color: #ffffff;
            }
        }
    }
}
.location-list-scroll {
    position: fixed;
    left: 0;
    width: 100vw;
    .location-list-view {
        margin-top: 20px;
        margin-left: 7.5px;
        margin-right: 17.5px;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;
        .location-list-item {
            margin-left: 10px;
            width: 76.5px;
            height: 43px;
            margin-bottom: 10px;
            line-height: 43px;
            text-align: center;
            border-radius: 5px;
            font-weight: 700;
            font-size: 14px;
        }
    }
}
</style>
