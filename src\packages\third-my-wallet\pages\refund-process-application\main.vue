<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas" style="position: relative; background: #f7f7fb">
        <zj-navbar title="退款申请" :border-bottom="false"></zj-navbar>
        <div class="card-default marlr15 mart12">
            <div class="fl-row pad15">
                <div class="fl-column fl-al-cen">
                    <img src="../../images/process-2.png" alt="" class="process-img" />
                    <div class="process-div"></div>
                    <img src="../../images/process-1.png" alt="" class="process-img" />
                </div>
                <div class="fl-column marl13">
                    <div class="font-18 color-FF6B2C weight-600 pad3-8">{{ applyRefundData.refundMsg }}</div>
                    <div class="font-14 color-333 weight-400 padb12">退款金额:{{ applyRefundData.refundAmount }}元</div>
                    <div class="font-13 fl-row hei24">
                        <div class="color-666">退款原因</div>
                        <div class="color-333 marl8">{{ getReasonCode(applyRefundData.reasonCode) }}</div>
                    </div>
                    <div class="font-13 fl-row hei24">
                        <div class="color-666">退款说明</div>
                        <div class="color-333 marl8">{{ applyRefundData.describe }}</div>
                    </div>
                    <div class="font-13 fl-row hei24">
                        <div class="color-666">退款时间</div>
                        <div class="color-333 marl8">{{ applyRefundData.applyTime }}</div>
                    </div>
                    <!-- <div class="font-13 fl-row hei24">
                        <div class="color-666">{{ applyRefundData.refundMsg }}</div>
                    </div> -->
                    <div class="font-18 color-FF6B2C weight-600 padb3 padt28">{{ getRefundStatus(applyRefundData.refundStatus) }}</div>
                </div>
            </div>
        </div>
        <div class="primary-btn refund-btn marlr15 mart16" v-if="applyRefundData.refundStatus == 1" @click="cancelHandle">取消申请</div>
        <zj-show-modal>
            <div class="tc_div" v-if="showModalType == 1">
                <div class="title-cancel">您是否取消退款？</div>
            </div>
            <div class="tc_div" v-if="showModalType == 2">
                <div class="title-cancel">取消退款成功。</div>
            </div>
        </zj-show-modal>
    </div>
</template>

<script>
import { applyRefundQuery, applyRefundCancel } from '../../../../s-kit/js/v3-http/https3/oilCard/index.js';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            applyRefundData: '',
            showModalType:0
        };
    },
    onLoad(option) {},
    onShow() {
        this.applyRefundQueryPost();
    },
    methods: {
        getReasonCode(data) {
            let name;
            if (data == '1') {
                name = '充值误操作';
            } else if (data == '2') {
                name = '暂时不使用';
            } else if (data == '3') {
                name = '消费不方便';
            } else if (data == '4') {
                name = '其他原因';
            } else {
                name = '';
            }
            return name;
        },
        getRefundStatus(data) {
            let name;
            if (data == '1') {
                name = '申请退款待审批';
            } else if (data == '2') {
                name = '申请退款审批中';
            } else if (data == '3') {
                name = '申请退款审批通过';
            }  else if (data == '4') {
                name = '申请退款已取消';
            } else if (data == '5') {
                name = '申请退款已拒绝';
            } else if (data == '6') {
                name = '退款中';
            } else if (data == '7') {
                name = '已退款完成';
            } else {
                name = '';
            }
            return name;
        },
        // 查看退款信息
        async applyRefundQueryPost() {
            let res = await applyRefundQuery();
            if (res.success) {
                this.applyRefundData = res.data;
            }
        },
        // 根据会员编号取消退款申请
        async applyRefundCancelPost() {
            let res = await applyRefundCancel();
            if (res.success) {
                await this.$store.dispatch('getSetWalletStatus');
                this.showModalType = 2
                this.$store.dispatch('zjShowModal', {
                    confirmText: '确定',
                    cancelText: '',
                    success: res => {
                        if (res.confirm) {
                            // 4. 返回上一页面
                            uni.navigateBack({
                                delta: 1, // 返回的页面数
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                // uni.showToast({
                //     title: '取消成功',
                //     icon: 'none',
                //     duration: 2000,
                // });
                // 4. 返回上一页面
                // uni.navigateBack({
                //     delta: 1, // 返回的页面数
                // });
                return;
            }
        },
        // 取消退款
        cancelHandle() {
            this.showModalType = 1
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        this.applyRefundCancelPost();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
            return;
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.marlr15 {
    margin-left: 15px;
    margin-right: 15px;
}

.mart12 {
    margin-top: 12px;
}

.mart16 {
    margin-top: 16px;
}
.marl8 {
    margin-left: 8px;
}
.pad15 {
    padding: 17px 15px 16px;
    box-sizing: border-box;
}
.process-img {
    width: 28px;
    height: 28px;
    display: block;
    flex-shrink: 0;
}
.process-div {
    width: 3px;
    height: 100%;
    background-image: linear-gradient(180deg, #ff6b2c 0%, #fff6d8 100%);
}
.pad3-8 {
    padding-top: 3px;
    padding-bottom: 8px;
}
.padb3 {
    padding-bottom: 3px 0;
}
.padt28 {
    padding-top: 18px;
}
.padb12 {
    padding-bottom: 12px;
}
.hei24 {
    height: 24px;
    line-height: 24px;
    margin-bottom: 10px;
}
.marl13 {
    margin-left: 13px;
}
.refund-btn {
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
    width: 343px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 16rpx;
}
.tc_div {
    .title-cancel {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        margin-top: 6px;
        text-align: center;
    }
}
</style>
