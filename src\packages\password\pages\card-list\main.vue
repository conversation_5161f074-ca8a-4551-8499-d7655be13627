<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            back-text="加油卡移动支付列表页面"
            :back-text-style="pageConfig.titleStyle"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :border-bottom="false"
        ></u-navbar>
        <div class="noOilcard" v-if="!cardList.length">暂无油卡</div>
        <div class="cardlist-div" v-else>
            <div v-for="(item, index) in cardList" :key="index" class="card-box">
                <!-- backgroundImage: 'url(' + (item.cardType == 0 ? naviBgshitika : naviBg) + ')',-->
                <div
                    class="manage-card"
                    :style="{
                        backgroundImage:
                            item.cardTypeImg && item.cardType == 1
                                ? `url(${item.cardTypeImg})`
                                : 'url(' + (item.cardType == 0 ? naviBgshitika : naviBg) + ')',
                        borderRadius: item.cardTypeImg && item.cardType == 1 ? '8px' : '',
                    }"
                    @click="toResetpassword($event, index)"
                >
                    <div class="header-div">
                        <div class="info-div">
                            <div class="logo-div">
                                <img src="@/static/cnpc-logo.png" alt mode="widthFix" />
                            </div>
                            <div class="right-div">
                                <div class="top-view">
                                    <span class="top-div">
                                        {{ item.cardType == 1 ? '中国石油电子油卡' : '中国石油加油卡' }}
                                    </span>
                                    <span class="top-icon">移动支付</span>
                                    <span class="top-icon" v-if="item.isDefaultCard">默认卡</span>
                                </div>
                                <div class="name-div">
                                    <span class="name" v-if="!item.activeed">
                                        {{ item.userNameShow }}
                                    </span>
                                    <span v-else class="name">{{ item.userName }}</span>
                                    <span class="card-number" v-if="!item.activeed">
                                        {{ item.cardNoShow }}
                                    </span>
                                    <span v-else class="card-number">{{ item.cardNo }}</span>
                                    <img
                                        :src="!item.activeed ? '/static/white-eye-close.png' : '/static/white-eye-open.png'"
                                        alt
                                        class="eye-block-iocn1"
                                        mode="widthFix"
                                        :data-item="item"
                                        @click.stop="eyebtn($event, index)"
                                    />
                                </div>
                                <div class="address-text">{{ item.allAddress }}</div>
                            </div>
                        </div>
                        <div class="bottom-div">
                            <div class="flex-row">
                                <div class="flex-item" v-if="item.cardType == 0">
                                    <div class="row-div">
                                        <span class="row-left">圈存金</span>
                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                        <template v-else>
                                            <span class="row-price">¥</span>
                                            <span class="row-text">{{ item.cardBalance }}</span>
                                        </template>
                                    </div>
                                    <div class="row-div">
                                        <span class="row-left">积分</span>
                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                        <span class="row-text" v-else>
                                            {{ item.cardLoyaltyBalance }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-item">
                                    <div class="row-div">
                                        <span class="row-left">备用金</span>
                                        <span class="row-price" v-if="item.activeed">¥</span>
                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                        <span class="row-text" v-else>{{ item.balance }}</span>
                                    </div>
                                    <div class="row-div">
                                        <span class="row-left">积分</span>
                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                        <span class="row-text" v-else>
                                            {{ item.loyaltyBalance }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="row-div">
                                <span class="row-left">有效期至：</span>
                                <span class="row-text row-text-center" v-if="!item.cardExpiredTime">****</span>
                                <span class="row-left" v-else>{{ item.cardExpiredTime }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import projectConfig from '../../../../../project.config';
import { mapGetters } from 'vuex';
export default {
    data() {
        return {
            pageConfig: pageConfig,
            projectConfig,
            naviBgshitika: '',
            naviBg: '',
        };
    },
    onLoad() {
        //  this.getCardList()
        console.log(this.cardList);
        this.loadImages();
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/dianzikabg.png', 'base64');
        // this.naviBgshitika = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/shitikabg.png', 'base64');
    },
    methods: {
        async loadImages() {
            this.naviBg = await this.fetchAndConvertToBase64(this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/dianzikabg.png');
            this.naviBgshitika = await this.fetchAndConvertToBase64(
                this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/shitikabg.png',
            );
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        // 是否隐藏点击事件
        async eyebtn(e, index) {
            this.$store.dispatch('card/setCardActiveed', index);
        },
        // 跳转到重置密码的页面
        async toResetpassword(e, index) {
            console.log(e, 'csddslnsc');
            console.log(index);
            let { cardList } = this;
            console.log(cardList, 'cardListcardListcardListcardList');
            let params = {
                cardNo: cardList[index].cardNo,
                idName: cardList[index].userName,
                idType: cardList[index].idType, // 0
            };
            console.log(params, 'paramsparamsparamsparamsparams');
            uni.navigateTo({
                url: `/packages/password/pages/reset-password/main?params=${JSON.stringify(params)}&id=${1}`,
            });
        },
    },
    computed: {
        ...mapGetters(['cardList']),
    },
};
</script>

<style scoped lang="scss">
.content {
    background: #f6f6f6;
    height: 100vh;
    .cardlist-div {
        // width: ;
        margin: 10px;
        overflow: hidden;
        background: #fff;
        padding: 40rpx;
    }
}
.card-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 315rpx;
    margin-top: 30rpx;
}
.manage-card {
    width: 702rpx;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // margin: 0rpx 15rpx 0 0;
    z-index: 1;

    .header-div {
        width: 100%;
        padding: 27rpx 24rpx 24rpx;

        .info-div {
            display: flex;
            align-items: center;

            .logo-div {
                img {
                    width: 70rpx;
                    height: 70rpx;
                }
            }

            .right-div {
                margin-left: 12px;
                .top-view {
                    display: flex;
                    align-items: center;
                    .top-div {
                        font-size: 14px;
                        color: #fff;
                        vertical-align: center;
                    }

                    .top-icon {
                        background: rgba(0, 0, 0, 0.17);
                        height: 28rpx;
                        line-height: 28rpx;
                        color: #fff;
                        font-size: 9px;
                        text-align: center;
                        vertical-align: center;
                        border-radius: 2px;
                        margin-left: 4px;
                        padding: 0 7rpx;
                    }
                }

                .name-div {
                    color: #fff;
                    font-size: 22rpx;
                    line-height: 20px;
                    opacity: 0.8;
                    display: flex;
                    align-items: center;
                    .name {
                        position: relative;
                        margin-right: 16rpx;

                        &.name:before {
                            content: '';
                            position: absolute;
                            top: 0;
                            right: -16rpx;
                            bottom: 0;
                            width: 1px;
                            border-right: 1px solid #ebedf0;
                            -webkit-transform-origin: 0 0;
                            transform-origin: 0 0;
                            -webkit-transform: scaleX(0.5);
                            transform: scaleX(0.5);
                            // z-index: 9;
                        }
                    }

                    .card-number {
                        margin-left: 16rpx;
                        display: inline-block;
                        vertical-align: baseline;
                    }
                    .eye-block-iocn1 {
                        width: 17px;
                        height: auto;
                        padding: 0 5px;
                        display: inline-block;
                        vertical-align: baseline;
                        z-index: 99;
                    }
                }
                .address-text {
                    font-size: 12px;
                    color: #fff;
                }
            }
        }
    }

    .bottom-div {
        margin-top: 20rpx;
        .flex-row {
            display: flex;
            align-items: center;
            .flex-item {
                width: 50%;
            }
        }

        .row-div {
            padding: 0 0 8rpx;
            display: flex;
            align-items: center;
            height: 25px;
            overflow: hidden;
            .row-div:last-child {
                padding: 0;
            }
            .row-left {
                font-size: 22rpx;
                opacity: 0.8;
                color: #fff;
                margin-right: 8rpx;
                display: inline-block;
                vertical-align: baseline;
            }

            .row-price {
                color: #fff;
                font-size: 24rpx;
                margin-right: 8rpx;
                display: inline-block;
                vertical-align: baseline;
            }

            .row-text {
                font-size: 36rpx;
                color: #fff;
                display: inline-block;
                vertical-align: baseline;
            }
            .row-text-center {
                padding-top: 9px;
            }
        }
    }
}
.noOilcard {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 52rpx;
    color: #c4c4c4;
    background: #fff;
    margin: 10px;
    padding: 40rpx;
}
</style>
