<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="个人资料" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="card-default mar16">
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('header')" v-if="false">
                            <div class="f-1 font-14 weight-400 color-333">头像</div>
                            <div class="fl-row fl-al-cen">
                                <img :src="memberData.photo" alt="" class="head_style marr6" />
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>

                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12 space-between" @click="headersClick('nikeName')">
                            <div class="font-14 weight-400 color-333 nameWidth">昵称</div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-999 marr6">{{ memberData.alias || '会员名称' }}</div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>

                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12">
                            <div class="f-1 font-14 weight-400 color-333">证件号</div>
                            <div class="fl-row fl-al-cen">
                                <img src="../../image/idcard.png" alt="" class="icon-20" />
                                <div class="font-14 weight-400 color-999">{{ memberData.idNo || '' }}</div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('mail')">
                            <div class="f-1 font-14 weight-400 color-333">邮箱</div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-999 marr6">{{ memberData.email || '' }}</div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('sex')">
                            <div class="f-1 font-14 weight-400 color-333">性别</div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-999 marr6">{{ getGenderStr(memberData.sex) }}</div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                        <!-- <div class="fl-row fl-al-cen padd12 marlr12" @click="headersClick('car')">
            <div class="f-1 font-14 weight-400 color-333">我的车辆</div>
            <div class="fl-row fl-al-cen">
              <div class="font-14 weight-400 color-999 marr6">{{carListV3[0].licensePlate}}</div>
              <div class="arroe-right-small-1"></div>
            </div>
          </div> -->

                        <div class="setItem fl-row fl-al-cen padd12 marlr12" @click="headersClick('address')">
                            <div class="itemLeft f-1 fl-row fl-al-cen">
                                <div class="title font-14 weight-400 color-333">常用地</div>
                                <div class="tip font-12 color-999 weight-400">常用地和昆仑e享卡常用地是同步的</div>
                            </div>
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 weight-400 color-999 marr6">{{ memberData.address || '' }}</div>
                                <div class="arroe-right-small-1"></div>
                            </div>

                            <!-- <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('addressInfo')">
            <div class="f-1 font-14 weight-400 color-333">详细地址</div>
            <div class="fl-row fl-al-cen">
              <div class="font-14 weight-400 color-999 marr6">{{memberData.addressInfo}}</div>
              <div class="arroe-right-small-1"></div>
            </div>
          </div> -->
                        </div>
                    </div>
                </zj-pull-down-refresh>
                <uniPop ref="sexPop" :maskClick="true" type="bottom">
                    <div class="model-div">
                        <div class="marlr16 padtb16">
                            <div class="font-16 weight-600 color-000">选择性别</div>
                            <div class="fl-row fl-jus-bet paddt11">
                                <div
                                    :class="sexIndex != item.id ? 'label' : 'active-label'"
                                    @click.stop="sexClick(index, item.id)"
                                    v-for="(item, index) in sexList"
                                    :key="index"
                                >
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                    </div>
                </uniPop>
                <SelectCity
                    v-if="showPopup"
                    :provinceCityArray="provincesAndCitiesList"
                    :show="showPopup"
                    @sureSelectArea="sureSelectArea"
                    @hideShow="hideShow"
                ></SelectCity>
            </div>
            <zj-show-modal>
                <div class="tc_div">
                    <div class="title">常用地由“{{ memberData.address }}”变更为 “{{ choseCity }}”</div>
                    <div class="text">更换后: </div>
                    <div class="text">您个人常用地更换后,昆仑e享卡常用地将随你变更归属地而变更。</div>
                </div>
            </zj-show-modal>
        </div>
    </div>
</template>

<script>
import Config from '../../../../../project.config.js';
import { modifyUserInfoApi, imageUpload } from '../../../../s-kit/js/v3-http/https3/user.js';
import { memberInfo } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { basicInfoQuery } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import uniPop from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import { mapGetters, mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 选择开户地区弹窗
            showPopup: false,
            // 省市区数组
            provincesAndCitiesList: [],
            memberData: '',
            headImg: '',
            // personType:'',
            sexIndex: '0',
            cityCode: '',
            sexList: [
                {
                    id: '1',
                    label: '男士',
                },
                {
                    id: '2',
                    label: '女士',
                },
            ],
            choseCity: '',
        };
    },
    onLoad(option) {
        // 获取省市地区
        this.getProvincesCities();
    },
    async onShow() {
        // 查询用户详细信息接口(使用脱敏身份证号字段)
        this.getBasicInfoQuery();
        // this.getmemberInfo()
    },
    methods: {
        /**
         * @description     : 查询用户详细信息
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery();
            if (res.success) {
                console.log(res.data, '查询用户详细信息');
                this.memberData = res.data;
            }
        },
        // async getmemberInfo () {
        //   let res = await memberInfo();
        //   if (res.success) {
        //     for (let key in res.data) {
        //       this.$set(this.memberData, key, res.data[key])
        //     }
        //   }
        // },
        // 切换性别点击事件
        sexClick(index, id) {
            this.sexIndex = id;
            this.modifyUserInfoPost('6', id);
        },
        // 头像、昵称、邮箱、性别、常用地
        headersClick(data) {
            // this.personType = data
            let URL;
            let params = {};
            let type = 'navigateTo';
            if (data == 'sex') {
                this.$refs.sexPop.open();
            } else if (data === 'addressInfo' || data === 'nikeName' || data === 'mail') {
                URL = '/packages/third-my-center/pages/edit-my-center/main';
            } else if (data === 'car') {
                URL = '/packages/third-my-center/pages/my-vehicle/main';
            } else if (data === 'header') {
                // this.$refs.sexPop.open()
                this.uploadAvatar();
            } else if (data === 'address') {
                this.showPopup = true;
            }
            params.pageType = data;
            this.$sKit.layer.useRouter(URL, params, type);
        },
        //头像修改
        uploadAvatar() {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                success: res => {
                    console.log('chooseImage', JSON.stringify(res.tempFilePaths));
                    this.imageUpload(res);
                },
            });
        },
        //上传头像
        async imageUpload(resData) {
            let headerData = await imageUpload({
                fileName: resData.tempFilePaths[0],
                mediaType: 'image/png',
            });
            // uni.uploadFile({
            //   fileType: "image",
            //   filePath: resData.tempFilePaths[0],
            //   name: 'file',
            //   url: await imageUpload(),
            //   formData:{
            //     fileName:resData.tempFilePaths[0],
            //     mediaType:"image/png"
            //   },
            //   success: (res) => {
            //     console.log('uploadFile',JSON.stringify(res));
            //     if(res.success){
            //       uni.showToast({
            //         title: "上传成功",
            //         icon: "none",
            //         duration: 2000,
            //       });
            //       this.modifyUserInfoPost('1','')
            //     }else{
            //       uni.showToast({
            //         title: "上传失败",
            //         icon: "none",
            //         duration: 2000,
            //       });
            //     }
            //   }
            // })

            if (headerData.success) {
                uni.showToast({
                    title: '上传成功',
                    icon: 'none',
                    duration: 2000,
                });
                this.modifyUserInfoPost('1', headerData.data.objectKey);
            } else {
                uni.showToast({
                    title: '上传失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        //操作类型：
        // 1—修改头像；
        // 2—修改邮箱；
        // 3—修改手机号；
        // 4—修改昵称；
        // 5—修改常用地；
        // 6—修改性别；
        // 修改用户基本信息
        async modifyUserInfoPost(type, data) {
            let params = {
                type: type,
            };
            if (type == '6') {
                params.sex = data;
            } else if (type == '5') {
                params.address = this.cityCode;
                // params.usedPlace = this.cityCode
            }

            let res = await modifyUserInfoApi(params);
            console.log('res---', res);
            if (res.success) {
                this.getBasicInfoQuery();
                // this.memberData.sex = this.sexIndex
                // this.memberData.photo = data
                if (type == '6') {
                    uni.showToast({
                        title: '修改性别成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.$refs.sexPop.close();
                } else {
                    uni.showToast({
                        title: '更换常用地成功',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            } else {
                if (type == '6') {
                    uni.showToast({
                        title: '修改性别失败',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.$refs.sexPop.close();
                } else {
                    uni.showToast({
                        title: '更换常用地失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            }
        },
        // 下拉刷新
        refreshPullDown() {
            this.$refs.pullDownRefreshRef.stopRefresh();
            this.getBasicInfoQuery();
        },
        // 性别展示
        getGenderStr(gender) {
            if (gender == 0) {
                return '未知';
            } else if (gender == 1) {
                return '男';
            } else if (gender == 2) {
                return '女';
            }
        },
        /**
         * @description  : 确定选择当前开户地
         * @return        {*}
         */
        sureSelectArea({ detail }) {
            this.showPopup = false;
            this.choseCity = detail.province + ' ' + detail.city;
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        let address = detail.province + detail.city;
                        this.cityCode = detail.cityCode;
                        console.log(this.address, '开户地');
                        this.modifyUserInfoPost('5', address);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        /**
         * @description  : 获取省市区
         * @return        {*}
         */
        async getProvincesCities() {
            let res = await provinceAndCityList();
            //
            if (res.success) {
                let arr2 = res.data.map(item => {
                    let newItem = {
                        name: item.parentAreaName,
                        code: item.parentAreaCode,
                        city: item.areaList,
                    };
                    return newItem;
                });
                this.provincesAndCitiesList = arr2;
            }
        },
        /**
         * @description  : 关闭选择城市弹窗
         * @return        {*}
         */
        hideShow() {
            console.log('关闭选择城市弹窗');
            this.showPopup = false;
        },
    },
    computed: {
        ...mapState({}),
        ...mapGetters([]),
    },
    components: {
        SelectCity,
        uniPop,
    },
};
</script>
<style scoped lang="scss">
.setItem {
    .itemLeft {
        line-height: 28rpx;

        .title {
            white-space: nowrap;
        }
        .tip {
            line-height: 28rpx;
            margin-left: 15rpx;
            margin-right: 15rpx;
        }
    }
}
.mar16 {
    margin: 16px;
}

.marlr12 {
    margin: 0 12px;
}

.padd12 {
    padding: 12px 0;
}

.head_style {
    width: 36px;
    height: 36px;
    border-radius: 50% 50%;
    border: 1px solid #ffffff;
}

.icon-20 {
    width: 24px;
    height: 20px;
    margin-right: 14px;
}

.marr6 {
    margin-right: 6px;
}

.model-div {
    background: #ffffff;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: env(safe-area-inset-bottom);

    .marlr16 {
        margin: 0 16px;
    }

    .padtb16 {
        padding: 23px 0 36px 0;
    }

    .paddt11 {
        padding-top: 11px;
    }

    .phitem {
        height: 45px;
        line-height: 45px;
    }

    .bottom-line {
        border-bottom: 1px solid #efeff4;
    }

    .bottom-line-bold {
        border-bottom: 6px solid #efeff4;
    }

    .calitem {
        height: 51px;
        line-height: 51px;
    }
}

.label {
    width: 156px;
    height: 44px;
    background: #f7f7fb;
    border-radius: 8px;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #333333;
}

.active-label {
    width: 156px;
    height: 44px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #fd6337;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    color: #fd6337;
    font-weight: 500;
}
.nameWidth {
    width: 80px;
    display: inline-block;
}
.space-between {
    justify-content: space-between;
}
.tc_div {
    .title {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        text-align: center;
        margin-top: 6px;
    }
    .title-cancel {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        margin-top: 6px;
        text-align: center;
    }

    .number_of_times {
        font-size: 10px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        flex-wrap: wrap;
        margin-bottom: 16px;
        justify-content: center;

        div {
            color: #e64f22;
        }
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
    }
}
</style>
