import store from '../../../../store/index';
import { stationList<PERSON>pi } from '../../v3-http/https3/oilStationService/index';
import { getFuelGunByOrgCodeApi } from '../../v3-http/https3/oilStationService/index';
import { queryStoreInfoListByCodeList } from '../../v3-http/https3/o2o/index';
import Vue from 'vue';
import { handleTxMapTransBMap, bd09_To_Gcj02, gcj02ToBd09 } from '../../map.js';
import commonUtil from '../../commonUtil.js';
import cnpcBridge from '../../v3-native-jsapi/cnpcBridge';
import sKit from '../../index';
const { baseImgUrl } = require('../../../../../project.config');
const { osName, system } = uni.getSystemInfoSync();
import { clientCode, name } from '../../../../../project.config';
console.log(osName, '当前设备类型');
// #ifdef MP-WEIXIN
import bmap from '../../bmap-wx.js';
// #endif

let page = 1; // 页数
let pageSize = 10; // 每页多少个
// 获取位置信息
const getLocationInfoV3 = async (state, commit, suc, dispatch) => {
    // #ifdef MP-MPAAS
    cnpcBridge.getLocation(async locationinfo => {
        console.log(locationinfo, 'app获取位置');

        state.locationState = locationinfo.state == 1 ? true : false;
        let mapdw = { longitude: locationinfo.longitude, latitude: locationinfo.latitude };
        let cityName = locationinfo.province + locationinfo.city;

        if (suc?.type === 'onlyLocation') {
            await commit('setLocationV3', {
                latitude: mapdw.latitude,
                longitude: mapdw.longitude,
                cityCode: locationinfo.cityCode,
                cityName: cityName,
                provinceV3: locationinfo.province,
                cityV3: locationinfo.city,
                district: locationinfo.district || '',
            });
            if (suc && suc.callback && typeof suc.callback === 'function') {
                suc.callback({
                    latitude: mapdw.latitude,
                    longitude: mapdw.longitude,
                    cityCode: locationinfo.cityCode,
                    cityName: cityName,
                    provinceV3: locationinfo.province,
                    cityV3: locationinfo.city,
                    district: locationinfo.district || '',
                });
            }
            return;
        }
        getOnlineDistrictV3(
            mapdw.latitude,
            mapdw.longitude,
            locationinfo.cityCode,
            cityName,
            locationinfo.province,
            locationinfo.city,
            locationinfo.district,
            state,
            commit,
            suc,
            dispatch,
            true,
        );
    });

    // #endif

    // 获取gps成功回调
    const successCallBack = async res => {
        state.locationState = true;
        if (res.latitude && res.longitude) {
            // 腾讯坐标转百度坐标
            const { longitude, latitude } = handleTxMapTransBMap(res.longitude, res.latitude);
            console.log(res, 'res======获取gps成功回调');
            // 微信小程序初始化百度地图
            // 风控经纬度传给接口，使用百度坐标
            await commit('setRiskLocationV3', { latitude, longitude });
            if (suc?.type === 'onlyLocation') {
                await commit('setLocationV3', {
                    latitude,
                    longitude,
                    cityCode: res.cityCode,
                    cityName: res.cityName,
                    provinceV3: res.provinceV3,
                    cityV3: res.cityV3,
                    district: res.district || '',
                });
                if (suc && suc.callback && typeof suc.callback === 'function') {
                    suc.callback({
                        latitude: latitude,
                        longitude: longitude,
                        cityCode: res.cityCode,
                        cityName: res.cityName,
                        provinceV3: res.provinceV3,
                        cityV3: res.cityV3,
                        district: res.district,
                    });
                }
                return;
            }
            getOnlineDistrictV3(
                latitude,
                longitude,
                res.cityCode,
                res.cityName,
                res.provinceV3,
                res.cityV3,
                res.district,
                state,
                commit,
                suc,
                dispatch,
                true,
            );
        }
    };

    // 获取gps失败回调
    const failCallBack = async err => {
        state.locationState = false;
        if (err?.errMsg?.includes('频繁调用')) {
            return;
        }

        store.dispatch('zjShowModal', {
            title: '提示',
            content: '暂时无法获取定位，已为您选择默认城市',
            success: res => {
                if (res.confirm) {
                } else if (res.cancel) {
                }
            },
        });
        if (suc?.type === 'onlyLocation') {
            // 写死gcj02坐标
            await commit('setLocationV3', {
                latitude: '39.914579',
                longitude: '116.404421',
                cityCode: '110101',
                cityName: '北京市北京市',
                provinceV3: '北京市',
                cityV3: '北京市',
                district: '东城区',
            });
            if (suc && suc.callback && typeof suc.callback === 'function') {
                suc.callback({ latitude: '39.914579', longitude: '116.404421' });
            }
            return;
        }
        // 写死gcj02坐标
        getOnlineDistrictV3(
            '39.914579',
            '116.404421',
            '110101',
            '北京市北京市',
            '北京市',
            '北京市',
            '东城区',
            state,
            commit,
            suc,
            dispatch,
            false,
        );
    };
    // #ifdef MP-WEIXIN
    /**
     * @description  : 获取当前的地理位置
     * @return        {*}
     */
    uni.getLocation({
        type: 'gcj02', //火星坐标系
        isHighAccuracy: true,
        success: async res => {
            state.locationState = true;
            let wxLocationRes = {
                ...res,
                cityCode: state.cityCode,
                cityName: state.cityName,
                provinceV3: state.provinceV3,
                cityV3: state.cityV3,
                district: state.district,
            };
            console.log(wxLocationRes, 'wxLocationRes====');
            if (state.cityCode === '' || state.cityName === '' || state.oldLocationState !== state.locationState) {
                var BMap = new bmap.BMapWX({
                    ak: 'xOPSojXDUr6UHY6PZeRbTQK3CygUI2cl',
                });
                BMap.regeocoding({
                    fail: function (data) {
                        successCallBack(wxLocationRes);
                    },
                    success: async res => {
                        console.log(res, 'res');
                        let resData = res.originalData;
                        console.log(resData, 'resData=======');
                        if (resData.status === 0) {
                            let isDirectlyCity = ['北京市', '天津市', '上海市', '重庆市'].includes(resData.result.addressComponent.city)
                                ? true
                                : false;
                            let cityCode = isDirectlyCity
                                ? resData.result.addressComponent.adcode
                                : resData.result.addressComponent.adcode.substring(0, 4) + '00';
                            let cityName = resData.result.addressComponent.province + resData.result.addressComponent.city;
                            wxLocationRes.cityCode = cityCode;
                            wxLocationRes.cityName = cityName;
                            wxLocationRes.provinceV3 = resData.result.addressComponent.province;
                            wxLocationRes.cityV3 = resData.result.addressComponent.city;
                            wxLocationRes.district = resData.result.addressComponent.district;
                            successCallBack(wxLocationRes);
                        }
                    },
                });
            } else {
                successCallBack(wxLocationRes);
            }
            state.oldLocationState = true;
        },
        // 获取失败的回调
        fail: async err => {
            failCallBack(err);
            state.oldLocationState = false;
        },
    });
    // #endif
    // #ifdef MP-ALIPAY
    my.getLocation({
        type: 1,
        success: async res => {
            // 直辖市城市编码取的是区编码
            res.cityCode = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city) ? res.districtAdcode : res.cityAdcode;
            res.cityName = res.province + res.city;
            res.provinceV3 = res.province;
            res.cityV3 = res.city;
            successCallBack(res);
        },
        fail(err) {
            failCallBack(err);
        },
    });
    // #endif
    // #ifdef MP-TOUTIAO
    tt.getLocation({
        type: 'gcj02', //火星坐标系
        success: async res => {
            // 直辖市城市编码取的是区编码
            // res.cityCode = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city) ? res.districtAdcode : res.cityAdcode;
            successCallBack(res);
        },
        fail(err) {
            failCallBack(err);
        },
    });
    // #endif
    // #ifdef H5-CLOUD
    upsdk.pluginReady(() => {
        let locationData = {};
        upsdk.getLocationGps({
            success: function (data) {
                state.locationState = true;
                if (data) {
                    if (typeof data === 'string') {
                        console.log('LocationGps----', data);
                        let locationGps = JSON.parse(data);
                        locationData.longitude = locationGps.longitude;
                        locationData.latitude = locationGps.latitude;
                    } else {
                        console.log('LocationGps----ios', data);
                        locationData.longitude = data.longitude;
                        locationData.latitude = data.latitude;
                    }
                }
                // 发起获取当前位置城市信息的请求
                upsdk.getCurrentLocationCity({
                    success: function (cityData) {
                        console.log('CurrentLocationCity----', JSON.stringify(cityData));
                        locationData.cityName = cityData.city;
                        locationData.cityCode = cityData.districtAdcode; // 编码
                        console.log('mapdw----', JSON.stringify(locationData));
                        locationData.provinceV3 = '';
                        locationData.cityV3 = cityData.city; //城市名称
                        locationData.district = cityData.district; //区域名称
                        successCallBack(locationData);
                    },
                    fail: function (msg) {
                        // 失败回调
                        console.log('LocationCity----msg', JSON.stringify(msg));
                        failCallBack(msg);
                    },
                });
            },
            fail: function (msg) {
                state.locationState = false;
                console.log('LocationGps----msg', JSON.stringify(msg));
                // 失败回调
                failCallBack(msg);
            },
        });
    });
    // #endif
};
// 这里调用腾讯地图api 获取用户位置所在区
const getOnlineDistrictV3 = async (
    latitude,
    longitude,
    cityCode,
    cityName,
    provinceV3,
    cityV3,
    district,
    state,
    commit,
    suc,
    dispatch,
    getLocationState,
) => {
    await commit('setLocationV3', { latitude, longitude, cityCode, cityName, provinceV3, cityV3, district });
    uni.setStorageSync('locationInfo', { cityCode: state.cityCode, cityName: state.cityName, cityV3, district });
    // 获取位置后，还要继续获取展示油站列表
    if (suc?.type === 'getLocationed') {
        if (suc && suc.callback && typeof suc.callback === 'function') {
            suc.callback({ latitude: latitude, longitude: longitude });
        }
    }
    sKit.mpBP.setUserInfo();
    let markerArr = await getMarkerV3(state, commit, suc);
    if (store.state.bulkOil.isBulkOilStationFlag === 1) {
        store.commit('setPullDownToSelectAnArray', setShowMarkerArrInit(markerArr));
        suc.callback();
        return;
    }
    await commit('setMarkerArrV3', markerArr);

    // 设置地图中心及选中的marker
    let index = markerArr.findIndex(item => item.stationStatus == 20 || item.stationStatus == 10);
    index = index == -1 ? 0 : index;
    let centerParam = {
        marker: markerArr[index],
        longitude: longitude,
        latitude: latitude,
        init: true,
        upLocation: true,
        suc: suc,
    };
    // 3.0地图选中油站的方法
    await dispatch('setSelectMarkerToMapCenterV3', centerParam);
    if (suc && suc.callback && typeof suc.callback === 'function') {
        if (!suc.type) {
            suc.callback(getLocationState);
        }
    }
};
/**
 * @description  : 获取油站marker信息
 * @param         {String} longitude -经度
 * @param         {String} latitude -纬度
 * @param         {String} pageNum -条数
 * @param         {String} pageSize -页码
 * @param         {String} distance -范围
 * @param         {String} bookingRefueling -是否是新站
 * @param         {String} mapType -地图种类
 * @param         {String} orgCode -会员开户地
 * @return        {*}
 */
const getMarkerV3 = async (state, commit, suc) => {
    return new Promise(async (resolve, reject) => {
        let params = {
            longitude: state.lonV3 || '', // 经度
            latitude: state.latV3 || '', // 纬度
            pageNum: page || '', // 条数
            pageSize: pageSize || '', // 页码
            distance: '10', // 范围
            bookingRefueling: state.bookingRefueling || '', // 是否是新站
            mapType: '1', //地图坐标系标识（0高德，1百度，2腾讯）
            orgCode: store.state.wallet.walletInfo.addressNo || '', // 会员开户地
        };
        // 散装油标识 isBulkOilStationFlag
        if (store.state.bulkOil.isBulkOilStationFlag === 1) {
            params.bulkOilFlag = store.state.bulkOil.isBulkOilStationFlag;
        } else if (params.bulkOilFlag) {
            delete params.bulkOilFlag;
        }

        let locationRes = await stationListApi(params);
        console.log(locationRes, 'locationRes====');
        // 当前油站列表大于0
        if (locationRes.data.rows.length > 0) {
            let allMarkerArr = getAvailableMarketV3(locationRes.data.rows); //未过滤全部油站列表
            commit('setAllMarkerArr', allMarkerArr);
            const filteredData = sKit.layer.filterLocations(locationRes.data.rows);
            console.log('filteredData====', filteredData);
            let markerArr = getAvailableMarketV3(filteredData);
            resolve(markerArr);
        } else {
            resolve([]);
        }
    });
};
// marker数据整理
const getAvailableMarketV3 = markerArr => {
    let newArr = JSON.parse(JSON.stringify(markerArr));

    for (let i = 0; i < newArr.length; i++) {
        newArr[i].id = Number(newArr[i].stationId);
        newArr[i].markerId = Number(newArr[i].stationId);
        newArr[i].businessHoursStr = timeSplit(newArr[i]);
        // 将当前油站列表第一条数据在油站列表上显示距离最近
        if (i == 0) {
            newArr[i].isFirst = true;
        } else {
            newArr[i].isFirst = false;
        }
        let mapArr = {};
        // #ifndef MP-MPAAS
        // 转换经纬度为高德地图经纬度
        mapArr = bd09_To_Gcj02(newArr[i].longitude, newArr[i].latitude);
        newArr[i].longitude = mapArr.longitude;
        newArr[i].latitude = mapArr.latitude;
        // #endif
        // #ifdef MP-MPAAS
        let isHarmony = store.state.thirdIndex.isHarmony;
        if (isHarmony) {
            mapArr = bd09_To_Gcj02(newArr[i].longitude, newArr[i].latitude);
            newArr[i].longitude = mapArr.longitude;
            newArr[i].latitude = mapArr.latitude;
        }
        // #endif
        let iconPath = '';
        if (newArr[i].storeOnlyFlag) {
            if (newArr[i].stationStatus == 20 || newArr[i].stationStatus == 10) {
                iconPath = '/uniapp/uni-mall/static/img/mapShop.png';
            } else {
                iconPath = '/uniapp/uni-mall/static/img/mapShopGray.png';
            }
        } else {
            if (newArr[i].stationStatus == 20 || newArr[i].stationStatus == 10) {
                iconPath = '/uniapp/uni-mall/static/img/mapStation.png';
            } else {
                iconPath = '/uniapp/uni-mall/static/img/mapStationGray.png';
            }
        }
        newArr[i] = {
            ...newArr[i],
            customCallout: null,
            iconPath: baseImgUrl + iconPath,
            width: 28,
            height: 36,
        };
    }
    return newArr;
};
/**
 * @description  : 截取时间字符串
 * @param         {*} data:时间字符串
 * @return        {*}
 */
const timeSplit = item => {
    let newarr = item.businessHoursList.map(time => {
        return time.startTime.substring(0, 5) + '-' + time.endTime.substring(0, 5);
    });
    return newarr.join(' ');
};
/**
 * @description  : 初始化支付宝小程序和mpaas小程序地图气泡
 * @param         {String} OilStationArr -油站列表
 * @param         {Object} centerData -回调及类型
 * @return        {*}
 */
const setShowMarkerArrInit = (OilStationArr, selectMarker = { stationId: '' }) => {
    let newOilStationArr = [];
    newOilStationArr = OilStationArr.map((item, index) => {
        // #ifdef MP-ALIPAY || MP-MPAAS
        return setShowMarkerItemAli(item, index, selectMarker);
        // #endif
        // #ifdef MP-WEIXIN || H5-CLOUD
        return setShowMarkerItemWx(item, index, selectMarker);
        // #endif
        // #ifdef MP-TOUTIAO
        return setShowMarkerItemTT(item, index, selectMarker);
        // #endif
    });
    return newOilStationArr;
};
const setShowMarkerItemAli = (olditem, index, selectMarker) => {
    const { isBulkOilStationFlag } = store.state.bulkOil;
    let item = JSON.parse(JSON.stringify(olditem));
    let imageUrl = '';
    // 如果是true显示距离最近图片
    if (item.isFirst) {
        imageUrl = baseImgUrl + '/uniapp/uni-mall/static/img/stationClosest.png';
    } else {
        imageUrl = baseImgUrl + '/uniapp/uni-mall/static/img/station.png';
    }
    const result = isBulkOilStationFlag === 1 || item.stationId === selectMarker.stationId ? 1 : 0;
    item.customCallout = {
        isShow: result,
        layout: {
            params: {
                distance: `${item.distance}km`,
                name: item.orgName,
                image: imageUrl,
            },
        },
        layoutBubble: {
            style: 'bubble',
            bgColor: '#F4F5F4',
            borderRadius: 4,
        },
    };
    let src = '';
    // #ifdef MP-MPAAS
    // 判断是IOS还是Android 使用不同的XML文件
    src = osName == 'ios' ? '/xml/oil-map-ios-mpaas.xml' : '/xml/oil-map-android.xml';
    // #endif
    // #ifndef MP-MPAAS
    src = osName == 'ios' ? '/xml/oil-map-ios.xml' : '/xml/oil-map-android.xml';
    // #endif
    item.customCallout.layout.src = src;
    // 判断XML的高级定制渲染在IDE中不生效，（my.isIDE是否在支付宝开发者工具）
    if (my.isIDE) {
        item.customCallout = {
            type: 2,
            descList: [
                {
                    desc: item.orgName,
                    descColor: '#333333',
                },
                {
                    desc: `${item.distance}km`,
                    descColor: '#108EE9',
                },
            ],
            isShow: index == 0 ? 1 : 0,
        };
    }
    return item;
};
const setShowMarkerItemWx = (olditem, index, selectMarker) => {
    const { isBulkOilStationFlag } = store.state.bulkOil;
    let item = JSON.parse(JSON.stringify(olditem));
    // 改变气泡显示状态
    if (item.stationId == selectMarker.stationId || isBulkOilStationFlag === 1) {
        item.customCallout = {
            display: 'ALWAYS',
        };
    } else {
        item.customCallout = null;
    }
    return item;
};
const setShowMarkerItemTT = (olditem, index, selectMarker) => {
    const { isBulkOilStationFlag } = store.state.bulkOil;
    let item = JSON.parse(JSON.stringify(olditem));
    let imageUrl = '';
    // 如果是true显示距离最近图片
    if (item.isFirst) {
        imageUrl = baseImgUrl + '/uniapp/uni-mall/static/img/stationClosest.png';
    } else {
        imageUrl = baseImgUrl + '/uniapp/uni-mall/static/img/station.png';
    }
    item.callout = {
        content: `<div
            style='height: 51px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 8px 0 5px;box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            width: fit-content;
            '>
            <image style='width:39px;height: 39px;' src='${imageUrl}' alt="" />
            <div style='display: flex;flex-direction: column; justify-content: center;margin-left: 10px;'>
            <div style='font-weight: bold; line-height: 18px;
            font-size: 13px;
            overflow: visible;
            width: auto;'>${item.orgName}</div>
            <div style='margin-top: 5px;font-size:10px; display: flex;'>距离您<div style='color: #E64F22;'>${item.distance}km</div>
            </div>
            </div>
            </div>`,
        fontSize: 14,
        textAlign: 'left',
        bgColor: '#F4F5F4',
        display: item.stationId == selectMarker.stationId ? 'ALWAYS' : 'null',
    };
    return item;
};
const changeMapCallout = (OilStationArr, selectMarkerV3) => {
    let showMarkerArrV3_app = [];
    // #ifndef MP-WEIXIN || MP-TOUTIAO
    //遍历油站，当前油站列表的id和选中的油站的id相同显示自定义气泡
    showMarkerArrV3_app = OilStationArr.map(item => {
        if (item.id == selectMarkerV3.id) {
            item.customCallout.isShow = 1;
        } else {
            item.customCallout.isShow = 0;
        }
        return item;
    });
    // #endif
    // #ifdef MP-TOUTIAO
    showMarkerArrV3_app = OilStationArr.map(item => {
        if (item.id == selectMarkerV3.id) {
            item.callout.display = 'ALWAYS';
        } else {
            item.callout.display = 'BYCLICK';
        }
        return item;
    });
    // #endif
    return showMarkerArrV3_app;
};
export default {
    state: {
        // 省份（我的定位的省份）
        provinceV3: '',
        // 城市（我的定位的城市）
        cityV3: '',
        //纬度 (用户位置)
        latV3: '', //纬度 (地图中心) 百度坐标类型
        //经度 (用户位置)
        lonV3: '', //经度 (地图中心) 百度坐标类型
        cityCode: '', // 国标码 110114
        cityName: '', // 省市
        // xxx区
        district: '',
        // 百度纬度 // 接口参数
        riskManagementLatV3: '', // 百度坐标 bd09坐标
        // 百度经度 // 接口参数
        riskManagementLonV3: '', // 百度坐标 bd09坐标
        // 地图中心纬度(选中油站)(地图中心点)
        mapCenterLatV3: '39.914579', //经度 (地图中心) 当前环境坐标类型
        // 地图中心经度(选中油站)(地图中心点)
        mapCenterLonV3: '116.404421', //经度 (地图中心) 当前环境坐标类型
        // 油站信息
        markerArrV3: [],
        // 未过滤全部油站列表
        allMarkerArr: [],
        // 地图展示的map数组
        showMarkerArrV3_app: [],
        // 选中的油站
        selectMarkerV3: {},
        // 地图缩放比例
        scale_app: 12,
        // 获取该网点的油品编码集合。
        fuelData: [],
        //预约加油（1代表可以进行预约加油的油站；0代表不可以，不传代表搜索全部油站）
        bookingRefueling: '',
        // 表示是否成功获取位置的状态 true成功 false失败
        locationState: false,
        // o2o业务 0:无 1:有
        isO2O: 1,
        // 上一位获取位置信息成功还是失败的对比
        oldLocationState: false,
    },
    mutations: {
        // 设置地图markers
        setShowMarkerArrV3_app(state, ShowMarkerArrV3_app) {
            // 加油 e享加油地图展示使用的油站数组
            state.showMarkerArrV3_app = ShowMarkerArrV3_app;
        },
        // 设置地图缩放比例
        setScale(state, scale_app) {
            state.scale_app = scale_app;
        },
        // 设置我的定位信息
        setLocationV3(state, locationObj) {
            state.latV3 = locationObj.latitude;
            state.lonV3 = locationObj.longitude;
            state.cityCode = locationObj.cityCode || '';
            state.cityName = locationObj.cityName || '';
            state.provinceV3 = locationObj.provinceV3 || '';
            state.cityV3 = locationObj.cityV3 || '';
            state.district = locationObj.district || '';
        },
        // 设置风控经纬度
        setRiskLocationV3(state, locationObj) {
            state.riskManagementLatV3 = locationObj.latitude || '';
            state.riskManagementLonV3 = locationObj.longitude || '';
        },
        // 设置油站信息数组
        setMarkerArrV3(state, markerArr) {
            state.markerArrV3 = markerArr;
        },
        //设置油品信息集合包括油品油枪
        setFuelData(state, arr) {
            return new Promise((resolve, reject) => {
                state.fuelData = arr;
                resolve();
            });
        },
        //设置O2O信息
        setIsO2O(state, o2o) {
            return new Promise((resolve, reject) => {
                state.isO2O = o2o;
                resolve();
            });
        },
        setAllMarkerArr(state, arr) {
            state.allMarkerArr = arr;
        },
    },
    actions: {
        // 设置选中的油站并展示在地图中心
        async setSelectMarkerToMapCenterV3({ state, commit, dispatch }, centerData) {
            let OilStationArr = centerData.init ? state.markerArrV3 : state.showMarkerArrV3_app;
            let selectMarker = OilStationArr.find(item => {
                return item.stationId == centerData.marker.stationId;
            });
            // 在非初始化的条件中
            if (!centerData.init) {
                // 判断选中油站是否在展示油站列表中
                // 如果不在，将选中油站数据处理后加入到展示油站列表中
                if (!selectMarker) {
                    selectMarker = getAvailableMarketV3([centerData.marker])[0];
                    // #ifndef MP-WEIXIN
                    selectMarker = setShowMarkerArrInit([selectMarker], selectMarker)[0];
                    // #endif
                    OilStationArr.push(selectMarker);
                }
            }
            // 展示到地图上的油站列表
            commit('setShowMarkerArrV3_app', []);
            if (OilStationArr.length > 0) {
                let setStation = [];
                if (centerData.init) {
                    // 初始化支付宝小程序和mpaas小程序地图气泡
                    setStation = setShowMarkerArrInit(OilStationArr, selectMarker);
                } else {
                    // #ifdef MP-WEIXIN || H5-CLOUD
                    // 微信切换与初始化方法相同
                    setStation = setShowMarkerArrInit(OilStationArr, selectMarker);
                    // #endif
                    // #ifndef MP-WEIXIN || H5-CLOUD
                    setStation = changeMapCallout(OilStationArr, selectMarker);
                    // #endif
                }
                commit('setShowMarkerArrV3_app', setStation);
            }
            state.selectMarkerV3 = selectMarker || {};
            // #ifdef MP-MPAAS
            await dispatch('judgeo2o');
            // #endif
            console.log('油站列表', state.showMarkerArrV3_app, '选中油站', state.selectMarkerV3);
            // 判断油站列表为空，地图中心点取用户当前位置
            state.mapCenterLatV3 = OilStationArr.length > 0 ? selectMarker.latitude : state.latV3;
            state.mapCenterLonV3 = OilStationArr.length > 0 ? selectMarker.longitude : state.lonV3;
            if (centerData.suc && centerData.suc?.type && centerData.suc?.type == 'initMap' && state.locationState) {
                if (centerData.suc.callback && typeof centerData.suc.callback === 'function') {
                    centerData.suc.callback({ mapCenterLatV3: state.mapCenterLatV3, mapCenterLonV3: state.mapCenterLonV3 });
                }
            }
            // 标记，限制埋点次数，更多网点才埋

            if (centerData?.oilChangeStation) {
                sKit.mpBP.tracker(centerData.bizTitle, {
                    seed: centerData.bizSeed,
                    pageID: 'selectStationCode',
                    refer: centerData.refer,
                    channelID: clientCode,
                    stationCode: state.selectMarkerV3.orgCode,
                    address: state.cityName,
                });
            }
            // 重新定位时进行缩放，回到地图中心点
            setNewScaleApp();
            function setNewScaleApp() {
                let num = Math.random() / 10;
                let new_scale_app = 12 + num;
                // 缩放值相同再缩放一次
                if (new_scale_app == state.scale_app) {
                    setNewScaleApp();
                } else {
                    state.scale_app = new_scale_app;
                }
            }
        },
        // 更新油站列表
        upShowMarkerArrV3_app({ state, commit, dispatch }, arr) {
            // 设置地图markers
            commit.setShowMarkerArrV3_app(arr);
        },
        // 初始化位置信息和更新位置信息
        async initLocationV3_app({ state, commit, dispatch }, suc) {
            getLocationInfoV3(state, commit, suc, dispatch);
        },
        // 小程序开启定位并获取位置信息逻辑
        openLocationMiniApp({ state, commit, dispatch }, suc) {
            // #ifndef MP-MPAAS || H5-CLOUD
            uni.getSetting({
                success: res => {
                    // #ifdef MP-WEIXIN
                    const status = res.authSetting['scope.userLocation'];
                    // #endif
                    // #ifdef MP-ALIPAY || MP-TOUTIAO
                    const status = res.authSetting.location;
                    // #endif
                    if (status === undefined) {
                        dispatch('initLocationV3_app', {
                            type: 'initMap',
                        });
                    } else if (status === false) {
                        store.dispatch('zjShowModal', {
                            title: '位置权限使用说明',
                            content: '根据您的位置信息获取您附近的加油站网点信息服务，是否开启？',
                            confirmText: '去开启',
                            cancelText: '取消',
                            confirmColor: '#333',
                            cancelColor: '#999',
                            success: res => {
                                if (res.confirm) {
                                    uni.openSetting({
                                        success: opRes => {
                                            // #ifdef MP-WEIXIN
                                            const status = opRes.authSetting['scope.userLocation'];
                                            // #endif
                                            // #ifdef MP-ALIPAY || MP-TOUTIAO
                                            const status = opRes.authSetting.location;
                                            // #endif
                                            if (status) {
                                                dispatch('initLocationV3_app', {
                                                    type: 'initMap',
                                                });
                                            }
                                        },
                                    });
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                    } else if (status === true) {
                    }
                },
            });
            // #endif
            // #ifdef H5-CLOUD
            // upsdk.pluginReady(() => {
            //     let locationData = {};
            //     upsdk.getLocationGps({
            //         success: function (data) {
            //             if (data) {
            //                 if (typeof data === 'string') {
            //                     console.log('LocationGps----', data);
            //                     let locationGps = JSON.parse(data);
            //                     locationData.longitude = locationGps.longitude;
            //                     locationData.latitude = locationGps.latitude;
            //                 } else {
            //                     console.log('LocationGps----ios', data);
            //                     locationData.longitude = data.longitude;
            //                     locationData.latitude = data.latitude;
            //                 }
            //             }
            //             // 发起获取当前位置城市信息的请求
            //             upsdk.getCurrentLocationCity({
            //                 success: function (cityData) {
            //                     console.log('CurrentLocationCity----', JSON.stringify(cityData));
            //                     locationData.cityName = cityData.city;
            //                     locationData.cityCode = cityData.districtAdcode; // 编码
            //                     console.log('mapdw----', JSON.stringify(locationData));
            //                     locationData.provinceV3 = '';
            //                     locationData.cityV3 = cityData.city; //城市名称
            //                     locationData.district = cityData.district; //区域名称
            //                     successCallBack(locationData);
            //                 },
            //                 fail: function (msg) {
            //                     // 失败回调
            //                     console.log('LocationCity----msg', JSON.stringify(msg));
            //                     failCallBack(msg);
            //                 },
            //             });
            //         },
            //         fail: function (msg) {
            //             console.log('LocationGps----msg', JSON.stringify(msg));
            //             // 失败回调
            //             failCallBack(msg);
            //         },
            //     });
            // });
            // #endif
        },
        // 获取该网点的油品编码集合接口
        async getFuelGunByOrgCodePost({ state, commit, dispatch }, suc) {
            // if (state.EOilselectMarkerV3.orgCode == '') {
            //   return
            // }
            // console.log('state---', state);
            return new Promise(async (resolve, reject) => {
                if (state.selectMarkerV3.hosCode) {
                    // 获取油品集合/oilstation /station/getFuelGunByOrgCode接口]
                    let resData = await getFuelGunByOrgCodeApi({
                        orgCode: state.selectMarkerV3.orgCode,
                        onlineType: state.selectMarkerV3.stationType == 1 ? '1' : '0',
                        hosCode: state.selectMarkerV3.hosCode || '',
                    });
                    if (resData.success) {
                        console.log(resData);
                        let dataArr = resData.data;
                        await commit('setFuelData', dataArr);
                        resolve();
                    }
                } else {
                    // 如果没网点列表没配置hosCode，就把油品数据置空
                    await commit('setFuelData', []);
                    resolve();
                }
            });
        },
        // o2o业务 0:无 1:有
        async judgeo2o({ state, commit, dispatch }, suc) {
            return new Promise(async (resolve, reject) => {
                if (state.selectMarkerV3?.orgCode) {
                    try {
                        let params = {
                            orgCodes: [state.selectMarkerV3.orgCode],
                        };
                        let res = await queryStoreInfoListByCodeList(params, { isload: false });
                        if (res.success) {
                            let o2oBusiness = res.data[0].o2oBusiness || 0;
                            await commit('setIsO2O', o2oBusiness);
                        }
                        resolve();
                    } catch (error) {
                        resolve();
                    }
                } else {
                    resolve();
                }
            });
        },
    },
    getters: {
        markerArrV3: state => state.markerArrV3, // 油站列表
        lonV3: state => state.lonV3, // 经度
        latV3: state => state.latV3, // 纬度
        showMarkerArrV3_app: state => state.showMarkerArrV3_app, // 显示在地图上的油站数据
    },
};
