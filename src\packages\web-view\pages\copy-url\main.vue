<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="page-union">
        <div class="content"></div>
        <image class="transferAccountBg_style" :src="transferAccountBg" alt=""></image>
        <div class="content_div">
            <view class="container">
                <image class="logo_style" :src="logo"></image>
                <view class="">
                    <view class="title">能源e站</view>
                    <view class="text">e享加油生活</view>
                </view>
            </view>
            <div class="primary-btn2 btnStyle" @click="copyUrl()">复制链接</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import logo from '@/static/img/cnpc-logo.png';
import transferAccountBg from '@/static/transferAccount-bg.png';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            src: '',
            logo,
            transferAccountBg,
        };
    },
    onLoad(options) {
        let data = JSON.parse(decodeURIComponent(options.data));
        this.src = data.src;
    },
    onShow() {
        setTimeout(() => {
            wx.hideHomeButton(); // 隐藏返回首页图标
        }, 100);
    },
    computed: {},
    methods: {
        copyUrl() {
            uni.setClipboardData({
                data: String(this.src), //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.page-union {
    width: 100%;
    background: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}
.content {
    height: 100vh;
}
// .container {
//     padding: 0 24rpx;
// }

.logo {
    width: 140rpx;
    height: 140rpx;
    margin: 200rpx auto 30rpx;
    display: block;
}

.btn-auth-login {
    color: #fff;
    background-color: rgb(51, 107, 225);
    border-radius: 60rpx;

    &:active {
        opacity: 0.7;
    }
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
}

.subtitle {
    font-size: 28rpx;
    color: #888;
    text-align: center;
    margin: 20rpx 0 80rpx;
}

.agreement-box {
    margin-top: 30rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    white-space: nowrap;
    margin-left: -10px;
    margin-bottom: -1px;

    .link {
        color: #1d77cd;
    }
}

.loading-box {
    padding-top: 80rpx;
    display: flex;
    justify-content: center;
    gap: 10rpx;

    .loading-dot {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: #888;
        animation: bolling 1s alternate infinite;
    }
}

.footer {
    position: fixed;
    bottom: 50rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    width: 100%;
}
.agreement_name {
    display: inline;
}
@keyframes bolling {
    0% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(5px);
    }
}

.transferAccountBg_style {
    width: 100%;
    height: 365px;
    position: absolute;
    z-index: 0;
    top: 0;
}
.content_div {
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 152px;
    padding: 0 63px;
}
.logo_style {
    width: 47px;
    height: 45px;
    margin-right: 14px;
}
.container {
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    margin-bottom: 43px;
}

.logo_div {
    display: flex;
}

.title {
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
}
.text {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    letter-spacing: 1px;
}
.agreement_div {
    margin-bottom: 16px;
    display: flex;
    .select_div {
        display: inline-block;
        margin-right: 5px;
        image {
            width: 17px;
            height: 17px;
        }
    }
}
.agreement_text {
    width: 94%;
}
.agreement_name {
    display: inline;
}
</style>
