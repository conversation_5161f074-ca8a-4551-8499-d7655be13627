import { queryRechargeRecords } from '@/s-kit/js/v3-http/https3/oilCard';
import { clientCode } from '../../../../../../project.config';
export default {
    methods: {
        async rechargeRecordListPost({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            if (isInit) {
                this.orderList.forEach(item => {
                    if (item?.timer) clearInterval(item.timer);
                });
                this.cardOrderList = [];
                this.orderList = [];
                this.pageNum = 1;
            }
            this.$emit('loadStatusChange', 'loading');
            let params = {
                startDate: listParams.timeObj.startTime + ' 00:00:00',
                endDate: listParams.timeObj.endTime + ' 23:59:59',
                cardNo: listParams.refuelCardAccountNo,
                cardType: 0,
                pageNo: this.pageNum,
                pageSize: this.pageSize,
            };
            let res = await queryRechargeRecords(params);
            this.$emit('stopRefresh');
            if (res.status == 0) {
                // 获取未开票订单数量，给每条订单添加隐藏更多商品属性
                if (this.pageNum == 1) {
                    this.cardOrderList = res.data || [];
                } else {
                    this.cardOrderList = this.cardOrderList.concat(res.data || []);
                }
                if (res.data.length < 10) {
                    this.$emit('loadStatusChange', 'nomore');
                } else {
                    this.$emit('loadStatusChange', 'contentdown');
                }
                this.pageNum++;
            }
            if (this.cardOrderList.length == 0) {
                this.$emit('showEmptyChange', true);
            } else {
                this.$emit('showEmptyChange', false);
            }
        },
        /**
         * @description  : 2.0充值订单开票逻辑
         * @param         {*} item:订单数据
         * @param         {*} type:'invoice2' 2.0充值订单开票逻辑
         * @return        {*}
         */
        async invoice2(item) {
            this.$sKit.mpBP.tracker('我的充值订单', {
                seed: 'rechargeOrderBiz',
                pageID: 'invoicingBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let params = {
                type: 'invoice2',
                checkAllAmount: item.amount,
                orderNoList: [item.orderId],
                cardNo: item.cardAsn,
                refer: 'r32',
            };
            this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params);
        },
    },
};
