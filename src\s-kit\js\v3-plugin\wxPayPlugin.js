import Config from '../third-config.js';
import store from '../../../store/index';
import LAYER from '../layer';
import { logoutApi } from '@/s-kit/js/v3-http/https3/user';
// #ifdef MP-WEIXIN
const payPlugin = requirePlugin('pay-plugin');
// #endif

async function initPayPlugin(result, type = 'number') {
    // #ifdef MP-WEIXIN
    // const gsmsToken = store.state.gsmsToken || '';
    // const openid = store.state.openId || '';
    const { gsmsToken, openId, openid, accessToken } = uni.getStorageSync('tokenInfo') || {};
    if (!openId || !gsmsToken){
        if (accessToken) {
            let res = await logoutApi();
            if (res.success) {
                store.commit('setLongTimeNotLogin', null);
            }
        }
        uni.clearStorageSync();
        LAYER.backHomeFun();
        return
    }
    return new Promise(async (resolve, reject) => {
        try {
            let params = {
                token: gsmsToken,
                openId: openId || openid,
                appId: Config.appId,
                data: {},
                baseType: Config.baseType,
            };
            console.log('支付插件初始化入参', params);
            let res = await payPlugin.InitPay(params); //V3.0纯数字键盘
            if (res.code == 'PAY_SUCCESS') {
                resolve(res);
                console.log('支付插件初始化-succ', res);
            } else {
                console.log('支付插件初始化-fail', res);
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `错误码：${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        } catch (err) {
            console.log('支付插件初始化-fail', err);
            reject(err);
        }
    });
    // #endif
}

export default {
    initPayPlugin,
};
