<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb; height: unset; min-height: 100vh">
        <div class="view fl-column">
            <zj-navbar :height="44" title="我的电子券"></zj-navbar>

            <div class="padding-16 bg-F7F7FB f-1" v-if="JSON.stringify(unUseDetailObject) !== '{}'">
                <div class="border-rad-8 bg-coupon">
                    <div class="upperLeft font-10 weight-500 color-fff te-center bg-ff6133">{{
                        differentiationType(unUseDetailObject.kind)
                    }}</div>
                    <div class="content-wrap fl-row">
                        <div class="left-wrap fl-row">
                            <div class="content-left fl-column fl-al-jus-cen">
                                <div v-if="!detailInfo.templatePicOss">
                                    <div class="price fl-row fl-al-base fl-al-jus-cen color-E64F22">
                                        <div
                                            v-if="unUseDetailObject.couponType && unUseDetailObject.couponType != '40'"
                                            class="symbol font-14 weight-400"
                                            >&yen;
                                        </div>
                                        <div class="font-28 weight-600">{{
                                            unUseDetailObject.couponType && unUseDetailObject.couponType != '40'
                                                ? unUseDetailObject.amount
                                                : unUseDetailObject.discountValue
                                        }}</div>
                                        <div
                                            v-if="unUseDetailObject.couponType && unUseDetailObject.couponType == '40'"
                                            class="symbol font-14 weight-400"
                                            >折</div
                                        >
                                    </div>
                                    <div
                                        :class="{
                                            'color-EB5130': unUseDetailObject.bgColor,
                                            'color-666': !unUseDetailObject.bgColor,
                                        }"
                                        class="font-13 weight-400"
                                        >{{ thresholdAmount(unUseDetailObject) }}</div
                                    >
                                </div>
                                <img v-else-if="detailInfo.templatePicOss" :src="detailInfo.templatePicOss" />
                            </div>
                            <div class="content-sx"></div>
                        </div>
                        <div class="right-wrap fl-column">
                            <div class="title font-12 color-1E1E1E weight-500">{{ unUseDetailObject.title }}</div>
                            <div class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400 color-FA6400 border-fa6400">
                                {{ unUseDetailObject.type == 1 ? '油品券' : '非油券' }}</div
                            >
                            <div class="title2 fl-row fl-jus-bet fl-al-cen">
                                <div class="fl-column">
                                    <div class="time marTop font-10 color-999 weight-400"
                                        >开始时间: {{ unUseDetailObject.startTime || '' }}</div
                                    >
                                    <div class="time font-10 color-999 weight-400">结束时间: {{ unUseDetailObject.endTime || '' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- #ifdef MP-MPAAS -->
                <div v-if="locationPermission">
                    <!-- #endif -->
                    <div class="bg-fff border-rad-8 padding-16">
                        <!-- 大于500米 -->
                        <div v-if="!distanceOut && !checkOrderNo" class="bottomLine">
                            <div class="reminder-top font-16 color-000 weight-600">温馨提示</div>
                            <div class="navigationTips font-14 color-333 weight-400"
                                >您距离最近的网点{{
                                    showMarkerArrV3_app[0].distance || '大于0.5'
                                }}KM，距离较远，请您到站后再使用核销码。</div
                            >
                            <!-- <div
                                    @click="navigationToStation"
                                    class="primary-btn btn te-center border-rad-8 color-fff weight-500 font-16"
                                    >查看附近网点</div
                                > -->
                        </div>
                        <div v-if="checkOrderNo" class="check-wrap fl-column fl-al-jus-cen">
                            <div>
                                <div class="reminder-top-check font-16 color-000 weight-600">温馨提示</div>
                                <div class="navigationTips-check font-14 color-333 weight-400"
                                    >此电子优惠券已被使用，可以到电子券使用记录页面查看使用详情。
                                </div>
                            </div>
                        </div>
                        <div v-if="distanceOut && !checkOrderNo">
                            <div class="detail-header fl-column fl-al-cen border-rad-8">
                                <div class="system-time fl-row fl-al-jus-cen font-16 weight-bold color-fff te-center font-18 weight-bold">
                                    <div class="countdown-bg1 border-rad-10">00</div>
                                    <div class="dian color-E64F22">:</div>
                                    <div class="countdown-bg2 border-rad-10">{{ countdownSeconds }}</div>
                                </div>
                                <div class="bar-wrap">
                                    <canvas ref="myBarCanvas" canvas-id="barcode" id="barcode"></canvas>
                                </div>
                                <div class="qr-code-wrap fl-row fl-jus-cen">
                                    <canvas canvas-id="qrcode" id="qrcode" @click="init()"></canvas>
                                </div>

                                <div class="card-text fl-row fl-al-cen font-12 weight-500 te-center fl-jus-cen color-666">
                                    （点击二维码更新）
                                    <div @click="init()" class="fl-row fl-al-cen">
                                        <div>刷新</div>
                                        <img src="../../images/shuaxin.png" alt />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="coupon-rule font-15 color-333 weight-500">使用说明:</div>
                            <!-- <div class="rule-explain font-14 weight-400 color-666" v-if="!showUseDirection">{{ useDirection }}</div> -->
                            <div class="rule-explain font-14 weight-400 color-666">
                                <rich-text :nodes="useDirection"></rich-text>
                            </div>
                            <div class="prompt2 font-14 weight-400 color-666" v-if="couponLeftNum !== null && couponLimit !== null"
                                >注：{{ unUseDetailObject.type == 1 ? '油品券' : '非油券' }}，每人每天限用{{ couponLimit }}张{{
                                    unUseDetailObject.kind != 1 ? `,今日剩余可用` + couponLeftNum + `张。` : '。'
                                }}
                            </div>
                            <div class="prompt2 font-14 weight-400 color-666" v-else
                                >注：油品券每人每日限用1张，非油券每人每日限用5张。</div
                            >
                        </div>
                        <div v-if="distanceOut && !checkOrderNo">
                            <div class="prompt font-15 weight-500 color-333">温馨提示</div>
                            <div class="font-14 weight-400 color-666"
                                >为保证本券的正常使用，当前页面每1分钟会自动刷新，如遇到使用失败，可手动点击二维码“刷新”</div
                            >
                        </div>
                    </div>
                    <!-- #ifdef MP-MPAAS -->
                </div>
                <!-- #endif -->
                <!-- #ifdef MP-MPAAS || H5 -->
                <div v-if="!locationPermission" class="bg-fff border-rad-8 padding-16">
                    <div class="no-code-box">
                        <img class="no-code-img" src="../../images/positioningNotEnabled.png" alt />
                        <div class="code-text"
                            >付款码在距离中石油加油站1公里内才可以使 用，现在还不知道您在哪里，请在设置中开启 能源e站定位权限。</div
                        >
                        <div class="code-enablePositioning" @click="enablePositioning()">开启定位</div>
                    </div>
                </div>
                <!-- #endif -->
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import wxcode from 'uniapp-qrcode';
import { mapState } from 'vuex';
import { couponDetail } from '../../../../s-kit/js/v3-http/https3/conpon/index';
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html';
import Vue from 'vue';
import { maxDistance, clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    computed: {
        ...mapState({
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
        }),
    },
    components: {
        mpHtml,
    },
    data() {
        return {
            // 电子券未使用数据
            couponUnUseDetail: {},
            // 实时时间的timer
            timer: null,
            //实时时间展示绑定值
            hour: '00',
            minutes: '00',
            seconds: '00',
            count: '00',
            // 每隔一分钟刷新会员码Timer
            qrcodeTimer: null,
            // 上个列表页面传递过来的item
            unUseDetailObject: {},
            // 生成二维码条形码的code
            checkCode: '',
            // 倒计时绑定值
            countdownMinutes: '00',
            // 倒计时绑定值
            countdownSeconds: 60,
            // 倒计时绑定值
            countdownCount: 1000,
            // 倒计时TImer
            countdownTimer: null,
            // 是否显示
            distanceOut: true,
            // 详情信息
            detailInfo: {},
            // 生成条形码的code
            checkOrderNo: '',
            // // 判断当前距离是否> 500米
            // stationDistance: -1,
            // 电子券使用说明
            useDirection: '',
            // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
            unusedCouponDetailFlag: true,
            // 当前电子券说明是否是富文本
            showUseDirection: false,
            maxDistance: maxDistance || 0.5,
            // 用户是否开启定位标识
            locationPermission: true,
            // 是否调用检查用户开启定位权限标识
            checkLocationPermission: true,
            couponLeftNum: null, //剩余张数
            couponLimit: null, //限用张数
            testImgBar: '',
            testImgQr: '',
            refer: '',
        };
    },
    async onShow() {},
    created() {
        // 清除实时时间的定时器
        clearInterval(this.timer);
        this.timer = null;
        // 清除倒计时的定时器
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
    },
    onLoad(options) {
        // 获取电子券列表页面未使用且已到开始时间的券数据
        this.unUseDetailObject = JSON.parse(decodeURIComponent(options.data));
        console.log(this.unUseDetailObject, 'this.unUseDetailObject');
        if (this.unUseDetailObject.refer) {
            this.refer = this.unUseDetailObject.refer;
        }
        let bizContent = '';
        if (this.unUseDetailObject.couponType == 10) {
            bizContent = '满减券';
        } else if (this.unUseDetailObject.couponType == 20) {
            bizContent = '记次券';
        } else if (this.unUseDetailObject.couponType == 30) {
            bizContent = '兑换券';
        } else if (this.unUseDetailObject.couponType == 40) {
            bizContent = '折扣券';
        }
        this.$sKit.mpBP.tracker('优惠券', {
            seed: 'couponBiz',
            pageID: 'couponDetailPage',
            couponMoeny: this.unUseDetailObject.couponType != '40' ? this.unUseDetailObject.amount : this.unUseDetailObject.discountValue,
            couponType: this.unUseDetailObject.type == 1 ? '油品券' : '非油券',
            conponTime: this.unUseDetailObject.endTime,
            refer: this.refer,
            channelID: clientCode,
            content: bizContent,
            dataType: 'click',
        });
        // 初始化
        this.init();
        // 实时时间
        // this.realTime();
    },
    methods: {
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        enablePositioning() {
            this.$cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(res => {
                    if (res) {
                        console.log(res, '开启定位权限res');
                        this.locationPermission = true;
                        this.checkLocationPermission = false;
                        this.init();
                    } else {
                        console.log(res, '开启定位权限res==else');
                    }
                });
        },
        async init() {
            // 获取位置信息，判断当前位置距离油站是否>500米
            await this.$store.dispatch('initLocationV3_app', {
                callback: async () => {
                    // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
                    if (this.unusedCouponDetailFlag) {
                        if (this.showMarkerArrV3_app.length > 0) {
                            // 获取最近的油站
                            // this.stationDistance = Number(this.showMarkerArrV3_app[0].distance);
                            // 获取电子券详情
                            this.getCouponUseDetails();
                        } else {
                            this.distanceOut = false;
                            // this.stationDistance = 0;
                        }
                    }
                },
            });
        },

        /**
         * @description  : 获取电子券详情
         * @param         {*} businessId:会员码
         * @param         {*} id:电子券id
         * @param         {*} checkCode:生成二维码条形码的code
         * @return        {*}
         */
        async getCouponUseDetails() {
            // #ifdef MP-MPAAS
            // 是否调用检查用户开启定位权限标识
            if (this.checkLocationPermission) {
                // this.$cnpcBridge.checkPermission() 检查用户定位权限是否开启
                let locationRes = await this.$cnpcBridge.checkPermission();
                this.locationPermission = locationRes.appStatus;
                console.log('用户开启定位了吗===电子券', this.locationPermission);
                if (!this.locationPermission) return;
            }
            // #endif
            // 清除倒计时的定时器
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
            let params = {
                id: this.unUseDetailObject.id, // 券id
                businessId: this.unUseDetailObject.businessId, //会员码
            };
            let res = await couponDetail(params);
            // console.log(val, 'res===================couponDetail');
            if (res.success) {
                this.detailInfo = res.data;
                // console.log(res.data, '======电子券详情');
                // TODO
                // // 电子券使用说明
                if (res.data.useDirection) {
                    this.useDirection = res.data.useDirection.replace(/\n/g, '<br/>');
                    console.log(this.useDirection, '');
                }
                // 券品类：1-油品券2-非油券（不传参默认查询全部券）
                // noilCouponLeftNum: null
                // noilCouponLimit: null
                // oilCouponLeftNum: null
                // oilCouponLimit: null
                if (res.data.categoryType == 1) {
                    this.couponLeftNum = res.data.oilCouponLeftNum;
                    this.couponLimit = res.data.oilCouponLimit;
                } else if (res.data.categoryType == 2) {
                    this.couponLeftNum = res.data.noilCouponLeftNum;
                    this.couponLimit = res.data.noilCouponLimit;
                }
                // couponStatus有值代表的是3.0的券
                if (res.data.couponStatus) {
                    // 状态20 代表是未核销
                    if (res.data.couponStatus == 20) {
                        // 判断当前距离是否大于500米
                        if (Number(this.showMarkerArrV3_app[0].distance) < (maxDistance || 0.5)) {
                            this.distanceOut = true;
                            // 电子券未使用数据
                            this.checkCode = res.data.checkCode;
                            setTimeout(() => {
                                wxcode.barcode('barcode', res.data.checkCode, 476, 150);
                                wxcode.qrcode('qrcode', res.data.checkCode, 500, 500);
                            }, 0);
                            this.countdownSeconds = 60;
                            this.countdownCount = 1000;
                            // 执行倒计时
                            this.countDown();
                        } else {
                            this.distanceOut = false;
                        }

                        // 状态20 代表是以核销
                    } else if (res.data.couponStatus == 40) {
                        this.checkOrderNo = '1';
                        clearInterval(this.countdownTimer);
                    }
                    // couponStatus 不存在代表的是2.0的券 通过判断checkOrderNo是否存在值来判断是不是被核销
                } else if (!res.data.couponStatus) {
                    //  存在值代表已经被核销
                    if (res.data.checkOrderNo) {
                        // console.log(res.data, '========2.0电子券已经被核销了吗');
                        this.checkOrderNo = res.data.checkOrderNo;
                        clearInterval(this.countdownTimer);
                    } else {
                        // 判断当前距离是否大于500米
                        if (Number(this.showMarkerArrV3_app[0].distance) < (maxDistance || 0.5)) {
                            this.distanceOut = true;
                            // 电子券未使用数据
                            this.checkCode = res.data.checkCode;
                            setTimeout(() => {
                                wxcode.barcode('barcode', res.data.checkCode, 476, 150);
                                wxcode.qrcode('qrcode', res.data.checkCode, 500, 500);
                            }, 0);
                            this.countdownSeconds = 60;
                            this.countdownCount = 1000;
                            // 执行倒计时
                            this.countDown();
                        } else {
                            this.distanceOut = false;
                        }
                    }
                }
                if (res.data.useDirection.includes('<p>')) {
                    console.log('当前包含P标签');
                    this.showUseDirection = true;
                }
            } else {
                uni.showToast({
                    title: res.message,
                    icon: 'none',
                });
            }
        },

        /**
         * @description  : 电子券类型
         * @return        {*}
         */
        getCouponType(val) {
            // console.log(val, '电子券类型');
            return val == 10 ? '满减券' : val == 20 ? '计次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
        },
        /**
         * @description  : 区分券类型
         * @return        {*}
         */
        differentiationType(val) {
            // console.log(val, '油品非油品------');
            // 券可用品类 1 油品 2 非油品
            return val == 1 ? '现金券' : '优惠券';
        },
        /**
         * @description  : 实时时间
         * @return        {*}
         */
        realTime() {
            this.timer = setInterval(() => {
                this.count = Number(this.count);
                if (this.count < 99) {
                    this.count = this.count + 1;
                } else {
                    this.count = 0;
                }
                if (this.count < 10) {
                    this.count = '0' + this.count;
                }
                var date = new Date();
                this.hour = date.getHours(); // 时
                if (this.hour >= 0 && this.hour <= 9) {
                    this.hour = '0' + this.hour;
                }
                this.minutes = date.getMinutes(); // 分
                if (this.minutes >= 0 && this.minutes <= 9) {
                    this.minutes = '0' + this.minutes;
                }
                this.seconds = date.getSeconds(); //秒
                if (this.seconds >= 0 && this.seconds <= 9) {
                    this.seconds = '0' + this.seconds;
                }
            }, 10);
        },
        /**
         * @description  : 倒计时
         * @return        {*}
         */
        countDown() {
            const TIME_COUNT = 60;
            if (!this.countdownTimer) {
                this.countdownTimer = setInterval(() => {
                    if (this.countdownSeconds > 0 && this.countdownSeconds <= TIME_COUNT) {
                        this.countdownSeconds--;
                        // console.log(this.countdownSeconds, ' this.countdownSeconds--');
                    } else {
                        console.log(this.countdownSeconds, '倒计时结束');
                        clearInterval(this.countdownTimer);
                        this.countdownTimer = null;
                        this.countdownSeconds = 60;
                        this.getCouponUseDetails();
                    }
                }, 1000);
            }
        },
        /**
         * @description  : 处理折和元
         * @return        {*}
         */
        faceValueFilter(item) {
            if (item.couponType && item.couponType == '40') {
                return item.discountValue + '<span style="font-size: 12px;">折</span>';
            } else {
                return (
                    `<span class="font-style" style="font-size: 12px">&yen;</span>` + `<span style="font-size: 28px;">${item.amount}</span>`
                );
            }
        },
        /**
         * @description  : 满减券和折扣券说明
         * @return        {*}
         */
        thresholdAmount(item) {
            if (item.thresholdAmount) {
                if (item.couponType && item.couponType == '40') {
                    return '满' + item.thresholdAmount + '元可用';
                } else {
                    return '满' + item.thresholdAmount + '减' + item.amount;
                }
            } else {
                return '无金额门槛';
            }
        },
        /**
         * @description  : 当大于500米时，点击跳转到网点导航页面
         * @return        {*}
         */
        navigationToStation() {
            // this.$cnpcBridge.openLocation({
            //   latitude: Number(this.showMarkerArrV3_app[0].latitude),
            //   longitude: Number(this.showMarkerArrV3_app[0].longitude),
            //   name: this.showMarkerArrV3_app[0].orgName,
            //   address: this.showMarkerArrV3_app[0].address,
            // });
            let url = `/packages/third-oil-charge-payment/pages/oil-station-module/main`;
            // 隐藏导航页面的去加油的按钮
            let params = {
                hideRefuel: true,
            };
            let type = 'redirectTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    // 销毁计时器
    beforeDestroy() {
        // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
        this.unusedCouponDetailFlag = false;
        // 清除实时时间的timer
        clearInterval(this.timer);
        this.timer = null;
        // 清除倒计时countdownTimer
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
    },
};
</script>

<style scoped lang="scss">
.view {
    .bg-coupon {
        width: 100%;
        height: 100px;
        position: relative;
        background-repeat: no-repeat;
        position: relative;
        margin-bottom: 12px;
        background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);

        &::before {
            content: '';
            width: 100%;
            height: 100%;
            position: absolute;
            background-image: radial-gradient(circle at 95px top, #f7f7fb, #f7f7fb 5px, transparent 6px),
                radial-gradient(circle at 95px bottom, #f7f7fb, #f7f7fb 5px, transparent 6px);
        }

        .upperLeft {
            position: absolute;
            top: 0;
            left: 0;
            width: 45.5px;
            height: 16px;
            line-height: 16px;
            border-radius: 8px 0 0 0;
        }

        .content-wrap {
            height: 100%;

            .left-wrap {
                width: 30%;

                .content-left {
                    width: 100%;
                    img {
                        width: 65px;
                        height: 65px;
                        line-height: 65px;
                        margin-top: 23rpx;
                    }
                }

                .content-sx {
                    height: 136rpx;
                    opacity: 0.5;
                    border: 1rpx solid;
                    border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                    margin-right: 8px;
                    margin-top: 15px;
                }
            }

            .right-wrap {
                width: 70%;
                height: 100%;
                // padding: 12px 0;

                .title {
                    margin-right: 22px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    margin-top: 12px;
                }
                .title2 {
                    position: absolute;
                    bottom: 10px;
                }
                .useBtn {
                    width: 77.5px;
                    height: 27px;
                    line-height: 27px;
                    margin-right: 12px;
                }

                .time {
                    // margin-top: 5px;
                }
                .marTop {
                    margin-top: 3px;
                }
                .type-cou {
                    width: 36px;
                    padding: 1px 0;
                    margin-top: 3px;
                }
            }
        }
    }

    .reminder-top {
        width: 100%;
        text-align: center;
    }

    .check-wrap {
        height: 200px;

        .reminder-top-check {
            text-align: center;
            line-height: 50px;
        }

        .navigationTips-check {
            width: 90%;
            margin: 0 auto;
        }
    }

    .navigationTips {
        margin-top: 12px;
        // text-align: center;
    }

    .btn {
        width: 100%;
        height: 44px;
        line-height: 44px;
        margin-top: 32px;
    }

    .border-dashed {
        margin-top: 32px;
    }

    .coupon-rule {
        line-height: 15px;
        margin-top: 12px;
    }

    .rule-explain {
        // margin-top: 12px;
    }

    .prompt {
        margin-top: 10px;
    }

    .prompt2 {
        margin-top: 5px;
    }

    .reminder-bottom {
        line-height: 15px;
        margin-top: 16px;
    }

    .refresh-text {
        line-height: 20px;
        margin-top: 12px;
    }

    .detail-header {
        .system-time {
            height: 80px;
            width: 100%;

            div {
                width: 44px;
                height: 44px;
                line-height: 44px;
            }

            .dian {
                width: 4.5px;
                height: 22px;
                line-height: 22px;
                margin: 0 7px;
            }
        }
    }

    .bar-wrap {
        // width: 210px;
        width: calc(100% - 70px);
        height: 50px;
        margin: 0 auto;
        margin-top: 20px;
        #barcode {
            // width: 210px;
            height: 50px;
        }
        .img {
            width: 100%;
            height: 100%;
        }
    }

    .qr-code-wrap {
        // width: 243px;
        // width: calc(100% - 60px);
        width: 100%;
        height: 245px;
        #qrcode {
            width: calc(100% - 60px);
            // width: 243px;
            height: 245px;
        }
        .img {
            width: 100%;
            height: 100%;
        }
    }

    .card-text {
        margin-bottom: 10px;

        img {
            width: 8px;
            height: 8px;
            margin-left: 3px;
        }
    }
    .no-code-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 90rpx;
        .no-code-img {
            width: 114rpx;
            height: 114rpx;
        }
        .code-text {
            margin-top: 28rpx;
            font-size: 30rpx;
            font-weight: 400;
            color: #333333;
            line-height: 42rpx;
        }
        .code-enablePositioning {
            margin-top: 60rpx;
            width: 218rpx;
            height: 80rpx;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(255, 87, 36, 0.25), 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 41rpx;
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 80rpx;
            text-align: center;
            text-shadow: 0px 9px 9px rgba(255, 87, 36, 0.25);
        }
    }
}
.bottomLine {
    padding-bottom: 10px;
    border-bottom: 1px solid #efeff4;
}
</style>
