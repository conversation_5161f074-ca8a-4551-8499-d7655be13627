<template>
    <div>
        <div>
            <FrostedGlass></FrostedGlass>
        </div>
        <div class="view">
            <u-navbar
                :background="pageConfig.bgColor"
                :back-icon-size="40"
                :back-icon-color="pageConfig.titleColor.backIconColor"
                :height="44"
                :title-color="pageConfig.titleColor.color"
                back-text="ETC签约"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>
            <view>
                <view class="uni-list" v-if="carList.length !== 0">
                    <div v-for="(item, index) in carList" :key="index">
                        <div class="uni-list-content" @click="terminationOfETC(item)">
                            <div class="uni-list-content-carNo">{{ item.carNo }}</div>
                            <u-icon class="right" name="arrow-right" color="#cfcfcf" size="25"></u-icon>
                        </div>
                    </div>
                </view>
                <view v-else class="noVehicle-con">
                    <div class="unsignedPrompt">暂无签约信息</div>
                    <div class="unsignedPrompt">您还没有绑定ETC,请您点击下方按钮进行签约</div>
                    <view class="addBtn-wrap">
                        <div class="addBtn" @click="addETC">新增ETC签约</div>
                    </view>
                </view>
                <view v-if="carList.length !== 0" class="addBtn-wrap2">
                    <div class="addBtn" @click="addETC">新增ETC签约</div>
                </view>
            </view>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import { etcAgencyData } from '@/api/ETC.js';
export default {
    name: 'etcSigning', //管理车辆
    components: {
        FrostedGlass,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            carList: [
                // {
                //   "agreementAmount": 200,
                //   "agreementOil": "92#汽油",
                //   "userName": "*占强",
                //   "idNo": "15042***********13",
                //   "carNo": "京A34567",
                //   "carNoColor": "0",
                //   "carBrand": "",
                //   "carColor": ""
                // }
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
                // { carNo: '2142356' },
            ],
            flag: false,
        };
    },
    onShow() {
        this.carList.length > 0 ? (this.flag = true) : (this.flag = false);
        this.getEtcAgencyData();
    },
    onLoad(options) {},
    methods: {
        addETC() {
            uni.navigateTo({
                url: '/packages/my-center/pages/newETCSigning/main',
            });
        },
        terminationOfETC(item) {
            uni.navigateTo({
                url: `/packages/my-center/pages/terminationByETC/main?carData=${JSON.stringify(item)}`,
            });
        },
        getEtcAgencyData() {
            etcAgencyData().then(res => {
                if (res.status === 0) {
                    this.carList = res.data;
                    res.data.length > 0 ? (this.flag = true) : (this.flag = false);
                    // this.$forceUpdate()
                    // if (res.data.length > 0) {
                    //   this.flag = true
                    // }else{

                    // }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100%;
    height: 100vh;
    background-color: #efefef;
    overflow-y: auto;
    .uni-list {
        margin: 15px;
        .uni-list-content {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            height: 50px;
            font-size: 23px;
            align-items: center;
            color: #000;
            background: #fff;
            .uni-list-content-carNo {
                margin-left: 15px;
            }
            .right {
                margin-right: 15px;
            }
        }
    }

    .noVehicle-con {
        width: 100%;
        display: flex;
        flex-direction: column;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        .addBtn-wrap {
            bottom: 0;
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            .addBtn {
                width: 100%;
                height: 46px;
                margin: 0 auto;
                font-size: 16px;
                background: #f96702;
                color: #fff;
                text-align: center;
                line-height: 46px;
                margin-bottom: 10px;
                border-radius: 20px;
            }
        }
        // margin-top: 200px;
        .unsignedPrompt {
            display: flex;
            height: 30px;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            width: 100%;
        }
    }
    .addBtn-wrap2 {
        position: absolute;
        bottom: 0;
        width: 100%;
        padding: 10px;
        border-radius: 4px;
        .addBtn {
            width: 100%;
            height: 46px;
            margin: 0 auto;
            font-size: 16px;
            background: #f96702;
            color: #fff;
            text-align: center;
            line-height: 46px;
            margin-bottom: 10px;
            border-radius: 20px;
        }
    }
}
</style>
