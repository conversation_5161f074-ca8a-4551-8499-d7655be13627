<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details p-bf bg-F7F7FB">
            <zj-navbar title="账单详情" :border-bottom="false"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div bg-fff">
                    <img v-if="isRefund" src="../../images/refund.png" alt />
                    <img v-else-if="tradeMode == 10" src="../../images/recharge.png" alt />
                    <img v-else-if="tradeMode == 1" src="../../images/consumption.png" alt />
                    <div class="amount fl-row fl-al-end font-style">
                        <div class="right_type color-E64F22 font-22">&yen;</div>
                        <div class="amount_value color-E64F22">{{ amount }}</div>
                    </div>
                    <div class="text">交易成功</div>
                </div>
                <div class="info_div">
                    <div class="info_div_item bg-fff">
                        <div class="info_item">
                            <div class="title">付款方式</div>
                            <div class="text">{{ payType }}</div>
                        </div>
                        <div class="info_item">
                            <div class="title">商品说明</div>
                            <div class="text">{{ describe }}</div>
                        </div>
                        <div class="info_item">
                            <div class="title">创建时间</div>
                            <div class="text">{{ createTime }}</div>
                        </div>
                        <div class="info_item">
                            <div class="title">订单号</div>
                            <div class="text">{{ orderNo }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { detailInfo } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-billing-details',
    data() {
        return {
            orderNo: '',
            payType: '',
            describe: '',
            createTime: '',
            tradeMode: '',
            tradeModeText: '',
            amount: '',
            isRefund: false,
        };
    },
    onLoad(option) {
        //上个页面传过来的参数  金额和订单号
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        this.amount = queryInfo.amount;
        this.orderNo = queryInfo.orderNo;
    },
    mounted() {
        // 初始化数据
        this.initData();
    },
    methods: {
        //orderNo 订单号 // 初始化数据获取订单详情信息
        async initData() {
            let res = await detailInfo({ orderNo: this.orderNo });
            if (res && res.success) {
                this.tradeMode = res.data.tradeMode;
                this.payType = res.data.typeName || '';
                this.createTime = res.data.createTime;
                this.describe = res.data.describe;
                this.amount = res.data.amountChange;
                this.isRefund = this.describe.includes('退款') || this.describe.includes('退货')
            }
        },
    },
    /**
   *
   * @param {* 消费还是充值  类型 } tradeMode
   * @param {* 类型详情} type
   * (充值类：1-微信、2-支付宝、3-银联卡、4-数字人民币、5-电子账户、6-加油卡、7-信用账户、8-云闪付、9-和包支付、10-翼支付、11-优惠券、12-礼品卡、13-油币、14-能源币、15-积分
、29-现金支付、99-聚合扫码

   */
    typeItem(tradeMode, type) {
        console.log(type);
        if (tradeMode == 10) {
            this.tradeModeText = '充值';
            switch (type) {
                case 1:
                    this.payType = '微信';
                    break;
                case 2:
                    this.payType = '支付宝';
                    break;
                case 3:
                    this.payType = '银联卡';
                    break;
                case 4:
                    this.payType = '数字人民币';
                    break;
                case 5:
                    this.payType = '昆仑e享卡';
                    break;
                case 6:
                    this.payType = '加油卡';
                    break;
                case 7:
                    this.payType = '信用账户';
                    break;
                case 8:
                    this.payType = '云闪付';
                    break;
                case 9:
                    this.payType = '和包支付';
                    break;
                case 10:
                    this.payType = '翼支付';
                    break;
                case 11:
                    this.payType = '优惠券';
                    break;
                case 12:
                    this.payType = '礼品卡';
                    break;
                case 13:
                    this.payType = '油币';
                    break;
                case 14:
                    this.payType = '能源币';
                    break;
                case 15:
                    this.payType = '积分';
                    break;
                case 29:
                    this.payType = '现金支付';
                    break;
                case 99:
                    this.payType = '聚合扫码';
                    break;
                default:
                    break;
            }
        } else if (tradeMode == 1) {
            this.tradeModeText = '消费';
            /*消费类：7-室外支付、8-室内扫码、9-预授权) */
            switch (type) {
                case 7:
                    this.payType = '室外支付';
                    break;
                case 8:
                    this.payType = '室内扫码';
                    break;
                case 9:
                    this.payType = '预授权';
                    break;

                default:
                    break;
            }
        }
        console.log(this.payType, '  this.payType   this.payType   this.payType ');
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-bottom: 16px;
            padding-top: 33px;

            img {
                width: 64px;
                height: 64px;
            }

            .amount {
                // color: $color-theme;
                font-size: 20px;
                margin-top: 15px;

                .right_type {
                    line-height: 33px;
                }

                .amount_value {
                    font-size: 36px;
                    margin-left: 5px;
                }
            }
        }

        .text {
            color: #333333;
            font-size: 12px;
            line-height: 23px;
        }
    }

    .info_div {
        margin-top: 30px;
        padding: 0 15px;

        .info_div_item {
            border-radius: 8px;
            padding: 0 15px;

            .info_item {
                padding: 15px 0;
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                .title {
                    font-size: 14px;
                    color: #666;
                    line-height: 20px;
                }

                .text {
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                }
            }
        }
    }
}
</style>
