<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <div class="view f-1 fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="待领取礼品卡"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <zj-data-list
                    ref="dataList"
                    emptyText="暂无数据"
                    :showEmpty="showEmpty"
                    :emptyImage="noPicture"
                    @scrolltolower="scrolltolower"
                    @refreshPullDown="refreshPullDown"
                    style="height: 100%"
                >
                    <div style="background: #f7f7fb" class="mart10">
                        <div class="list-wrap" v-for="(item, index) in cardBuyArray" :key="index">
                            <div class="item-module">
                                <div class="fl-row fl-jus-bet paddb13 fl-al-cen">
                                    <div class="f-1 fl-row">
                                        <img src="../../images/cnpc-logo.png" alt="" class="logo-img" />
                                        <div class="font-14 color-333 marl5 weight-500">{{ item.giftCardName }}</div>
                                    </div>
                                    <div class="status-div fl-row">
                                        <div class="fl-row fl-jus-bet">
                                            <div
                                                class="btn primary-btn border-rad-4 color-fff font-13 weight-400 marl10"
                                                @click="bindCard(item)"
                                                >领取
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="fl-row fl-jus-bet fl-al-cen color-333 font-12 weight-500 paddb13" v-if="item.giftCardNo">
                                    NO.{{ item.giftCardNo }}
                                    <div class="color-FF6B2C fl-row weight-500 fl-al-base">
                                        <div class="font-14">¥</div>
                                        <div class="font-18">{{ item.faceAmount || '0.00' }}</div>
                                    </div>
                                </div>
                                <div class="line"></div>
                                <div class="fl-row fl-jus-bet fl-al-cen">
                                    <div class="color-666 font-12 f-1"
                                        >有效期：{{ formatTimeFun(item.startDate) }}-{{ formatTimeFun(item.endDate) }}</div
                                    >
                                    <!-- <div class="color-E64F22 fl-row weight-500 fl-al-base">
                                        <div class="font-14">¥</div>
                                        <div class="font-18">{{ item.faceAmount || '0.00' }}</div>
                                    </div> -->
                                </div>
                            </div>
                        </div></div
                    >
                </zj-data-list>
            </div>
            <custom-popup ref="popDialogFlag" type="center">
                <div class="_modal">
                    <div class="iol-pop">
                        <div class="fl-column te-center font-14 color-333 weight-500 content">
                            <div class="font-16 color-333 weight-500 padb18">查看卡密</div>
                            <div class="font-13 color-FF6B2C">请勿将卡密复制截图给他人，谨防诈骗！</div>
                            <div class="price-input-area" v-if="isCardPassword == false">
                                <div class="font-18 color-333 padt35">请点击确认查看卡密</div>
                            </div>
                            <div class="price-input-area padt15" v-else>
                                <div class="fl-row fl-al-jus-cen font-14 color-333 padb11">
                                    <div class="te-right">卡号：</div>
                                    <!-- 卡号：{{giftCardNo}} -->
                                    <div class="te-left">{{ giftCardNo }} </div>
                                </div>
                                <div class="fl-row fl-al-jus-cen font-14 color-333">
                                    <!-- <div>卡密：{{desString}}</div> -->
                                    <div class="te-right">卡密：</div>
                                    <div class="te-left">{{ desString }}</div>
                                    <div class="copy-div" @click.stop="handleCopy(desString)">复制</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <view class="fl-row slot-btn-box">
                        <view class="btn cancel_btn" :style="{ color: cancelColor }" @click.stop="clickBtn('cancel')" v-if="cancelText">
                            {{ cancelText }}
                        </view>
                        <view
                            class="btn confirm"
                            :style="{
                                color: confirmColor,
                                background: confirmBackgroundColor,
                            }"
                            @click.stop="clickBtn('confirm')"
                            v-if="confirmText"
                            >{{ confirmText }}
                        </view>
                    </view>
                </div>
            </custom-popup>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import { buyGiftCardList, giftCardBind, unclaimedList } from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 是否展示空态标识
            showEmpty: false,
            // 暂无电子券图片
            noPicture: require('../../images/lpk-no-data.png'),
            cancelText: '取消',
            cancelColor: '#666',
            confirmText: '确认',
            confirmColor: '#000',
            cardPassword: '',
            // 查看卡密
            isCardPassword: false,
            // 页码
            page: 1,
            // 购卡列表
            cardBuyArray: [],
            // 总页码
            totalPage: 0,
            //礼品卡号
            giftCardNo: '',
            //卡密
            desString: '',
            closeForm: false,
            cardType: 'unbind',
        };
    },
    onLoad(option) {
        console.log('option---', option);
        if (JSON.stringify(option) !== '{}') {
            let optionsData = JSON.parse(decodeURIComponent(option.data));
            console.log('optionsData---', optionsData);
            this.cardType = optionsData.type;
            if (this.cardType == 'unclaimed') {
                this.getUnclaimedList();
            } else {
                this.getBuyGiftCardList();
            }
        }
    },
    async mounted() {
        // #ifdef MP-MPAAS
        // fixed,上线前要改正
        this.closeForm = await this.$cnpcBridge.judgeProtocolCall('3.6.4');
        console.log(this.closeForm, 'this.closeForm====开通e享卡');
        // #endif
    },
    onShow() {
        // this.getBuyGiftCardList();
        // this.getUnclaimedList();
    },
    methods: {
        // 处理时间只展示年月日
        formatTimeFun(time) {
            if (!time) {
                return;
            }
            let date = time.split(' ')[0];
            date = `${date.split('-')[0]}年${date.split('-')[1]}月${date.split('-')[2]}日`;
            return date;
        },
        /**
         * @description  : 上拉加载查看用户名下购卡列表
         * @return        {*}
         */
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                // this.getBuyGiftCardList();
                if (this.cardType == 'unclaimed') {
                    this.getUnclaimedList();
                } else {
                    this.getBuyGiftCardList();
                }
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            // this.getBuyGiftCardList({ isInit: true });
            if (this.cardType == 'unclaimed') {
                this.getUnclaimedList({ isInit: true });
            } else {
                this.getBuyGiftCardList({ isInit: true });
            }
        },
        // 查询用户名下礼品卡列表接口
        async getBuyGiftCardList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    cardBuyArray: [],
                    page: 1,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, cardBuyArray, totalPage } = this;
            /***
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
            };
            let res = await buyGiftCardList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];
                // list.map((item, index) => {
                //   item.faceAmount = item.faceAmount ? this.$sKit.layer.returnFloat(item.faceAmount) : ''
                // })

                // 将处理好的数组合并到定义的数组，放到页面渲染
                cardBuyArray = cardBuyArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    cardBuyArray,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && page >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = cardBuyArray.length <= 0 ? true : false;
            }
        },
        // 查询用户待领取礼品卡列表接口
        async getUnclaimedList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    cardBuyArray: [],
                    page: 1,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, cardBuyArray, totalPage } = this;
            /***
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
            };
            let res = await unclaimedList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];
                // list.map((item, index) => {
                //   item.faceAmount = item.faceAmount ? this.$sKit.layer.returnFloat(item.faceAmount) : ''
                // })

                // 将处理好的数组合并到定义的数组，放到页面渲染
                list = list.map(e => {
                    // 待领取礼品
                    return {
                        giftCardNo: e.giftCardAsn,
                        giftCardName: e.templetName,
                        faceAmount: e.faceValue,
                        startDate: e.effectTime,
                        endDate: e.expireTime,
                    };
                });
                cardBuyArray = cardBuyArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    cardBuyArray,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && page >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = cardBuyArray.length <= 0 ? true : false;
            } else {
                this.showEmpty = true;
            }
        },
        // 查看卡密
        lookCard(item) {
            this.confirmColor = '#FF6B2C';
            this.giftCardNo = item.giftCardNo;
            this.$refs.popDialogFlag.open();
            this.isCardPassword = false;
            // this.isCardPassword = true
        },
        //通过卡号绑定自己的礼品卡。
        async bindCard(item) {
            /**
             * type	是	礼品卡绑定类型：
                1—卡密绑卡；
                2—卡号绑卡；
                3-待领取礼品卡绑卡；
                password	否	卡密，type=1时该字段为必填项
                giftCardNo	否	礼品卡号，type=2时该字段为必填项
             * */
            let params = {
                type: this.cardType == 'unclaimed' ? 3 : 2,
                giftCardNo: item.giftCardNo,
            };
            let res = await giftCardBind(params);
            if (res.success) {
                this.$store.dispatch('zjShowModal', {
                    title: '绑定成功',
                    confirmText: '确认',
                    success: res => {
                        if (res.confirm) {
                            uni.navigateBack({
                                delta: 1, //返回的页面数
                            });
                        } else if (res.cancel) {
                        }
                    },
                });
            } else {
                this.$util.tipsToastNoicon('绑定失败');
            }
        },
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:被复制的内容
         * @return        {*}
         */
        handleCopy(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        //关闭查看弹窗并查看卡密
        clickBtn(type) {
            if (type == 'confirm') {
                if (this.isCardPassword == true) {
                    this.$refs.popDialogFlag.close();
                } else {
                    console.log('this.giftCardNo', this.giftCardNo);
                    uni.showLoading();
                    this.$accountCenter.giftCardQueryCode({ cardNo: this.giftCardNo }, res => {
                        uni.hideLoading();
                        if (res.isSuccessed) {
                            uni.showToast({
                                title: '成功',
                                icon: 'none',
                                duration: 2000,
                            });
                            (this.confirmText = '确认'), (this.cancelText = '');
                            this.confirmColor = '#000';
                            this.isCardPassword = true;
                            this.desString = res.desString;
                        } else {
                            uni.hideLoading();
                            this.$util.tipsToastNoicon(res.desString || '查看失败');
                        }
                    });
                }
            }
            if (type == 'cancel') {
                this.$refs.popDialogFlag.close();
            }
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.list-wrap {
    position: relative;
    width: 345px;
    margin: 0 auto 15px;

    .item-module {
        position: relative;
        min-height: 110px;
        overflow: hidden;
        background: #ffffff;
        border-radius: 8px;
        padding: 11px 11px 10px 13px;
        box-sizing: border-box;
        margin-bottom: 10px;

        .logo-img {
            width: 24px;
            height: 23px;
            flex-shrink: 0; /*防止被压缩*/
        }

        .status-div {
            .btn {
                width: 60px;
                height: 24px;
                line-height: 24px;
            }
        }
    }

    .marl5 {
        margin-left: 5px;
    }

    .marl10 {
        margin-left: 10px;
    }

    .paddb13 {
        padding-bottom: 13px;
    }
    .line {
        // border: 1px solid #EDEDED;
        margin-bottom: 14px;
        height: 1px;
        background: #ededed;
    }
}

._modal {
    flex: none;
    width: 280px;
    min-height: 227px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 18px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 147px;
        }

        .price-input-area {
            border-radius: 10px;
            height: 44px;

            .price-input {
                width: 100%;
                height: 100% !important;
                background: #f7f7fb;
                border-radius: 8px;
                text-align: center;
                box-sizing: border-box;
            }
        }
    }

    .copy-div {
        width: 29px;
        padding: 3px 0;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #999999;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        text-align: center;
        margin-left: 5px;
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }

    .padb18 {
        padding-bottom: 18px;
    }

    .padb11 {
        padding-bottom: 11px;
    }
}

.padt15 {
    padding-top: 15px;
}
.padt35 {
    padding-top: 35px;
}

.mart10 {
    margin-top: 10px;
}
</style>
