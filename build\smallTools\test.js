const fs = require('fs');
const { readFile, resolve, isExistingFile } = require('./util');
const generateRoutes = require('./generate-routes');
let pagesJson = JSON.parse(readFile(resolve('../src/pages.json')).toString());
const indexPages = pagesJson.pages || [];
indexPages.forEach(page => {
    // console.log(resolve('../src/' + page.path))
    console.log(isExistingFile(resolve('../src/' + page.path + '.vue')));
});
// generateRoutes(appConfig)
