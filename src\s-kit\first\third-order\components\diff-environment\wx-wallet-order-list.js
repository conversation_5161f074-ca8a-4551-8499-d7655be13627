// #ifdef MP-WEIXIN
import { queryRechargeRecords, getRechargeDetail } from '../../../../../s-kit/js/v3-http/https3/oilCard';
import { clientCode } from '../../../../../../project.config';
export default {
    methods: {
        async rechargeRecordListPost({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            if (isInit) {
                this.orderList.forEach(item => {
                    if (item?.timer) clearInterval(item.timer);
                });
                this.cardOrderList = [];
                this.orderList = [];
                this.pageNum = 1;
            }
            this.$emit('loadStatusChange', 'loading');
            let params = {
                startTime: listParams.timeObj.startTime + ' 00:00:00',
                endTime: listParams.timeObj.endTime + ' 23:59:59',
                cardNo: listParams.refuelCardAccountNo,
                pageNo: this.pageNum,
                pageSize: this.pageSize,
            };
            let res = await queryRechargeRecords(params, { isload: false });
            this.$emit('stopRefresh');
            if (res.status == 0) {
                let arr = res.data.map(item => {
                    item.occurTime = item.tradeTime;
                    item.isInvoice = item.invoiceStatus;
                    return item;
                });
                if (this.pageNum == 1) {
                    this.cardOrderList = arr || [];
                } else {
                    this.cardOrderList = this.cardOrderList.concat(arr || []);
                }
                if (res.data.length < 10) {
                    this.$emit('loadStatusChange', 'nomore');
                } else {
                    this.$emit('loadStatusChange', 'contentdown');
                }
                this.pageNum++;
            }
            if (this.cardOrderList.length == 0) {
                this.$emit('showEmptyChange', true);
            } else {
                this.$emit('showEmptyChange', false);
            }
        },
        /**
         * @description  : 2.0充值订单开票逻辑
         * @param         {*} item:订单数据
         * @param         {*} type:'invoice2' 2.0充值订单开票逻辑
         * @return        {*}
         */
        async invoice2(item) {
            this.$sKit.mpBP.tracker('我的充值订单', {
                seed: 'rechargeOrderBiz',
                pageID: 'invoicingBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let res = await getRechargeDetail({ orderId: item.orderId });
            let params = {
                type: 'invoice2',
                checkAllAmount: item.amount,
                orderNoList: [item.orderId],
                cardNo: res.data.cardNo,
                refer: 'r32',
            };
            this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params);
        },
    },
};
// #endif
