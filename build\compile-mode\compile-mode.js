const fs = require('fs');

const notesSHtml = /<!--\s#s-([a-zA-Z]*)\s(.*)\s-->/; // 匹配html中条件编译的开始标签
const notesEHteml = /<!--\s#s-endif\s-->/; // 匹配html中条件编译的结束标签
const notesSJs = /\/{2}\s#s-([a-zA-Z]*)\s(.*)/; // 匹配js中条件编译的开始标签
const notesEJs = /\/{2}\s#s-endif/; // 匹配js中条件编译的结束标签
const notesSCss = /\/\*\s#s-([a-zA-Z]*)\s(.*)\s\*\//; // 匹配css中条件编译的开始标签
const notesECss = /\/\*\s#s-endif\s\*\//; // 匹配css中条件编译的开始标签

const cpArr = [
    /\/{2}\s#s-ifdef\s([a-zA-Z0-9-]*)/,
    /\/{2}\s#s-ifndef\s([a-zA-Z0-9-]*)/,
    /<!--\s#s-ifdef\s([a-zA-Z0-9-]*)/,
    /<!--\s#s-ifndef\s([a-zA-Z0-9-]*)/,
    /\/\*\s#s-ifdef\s([a-zA-Z0-9-]*)/,
    /\/\*\s#s-ifndef\s([a-zA-Z0-9-]*)/,
];

/**
 * 删除指定索引的字符串
 * @param {*} str 要截取的字符串
 * @param {*} startIndex 删除的开始index
 * @param {*} endIndex 删除的结束index
 * @returns 删除后的字符串
 */
const deleteSlice = (str, startIndex, endIndex) => {
    let deStr = str.slice(startIndex, endIndex);
    let reStrArr = str.split(deStr);
    return reStrArr[0] + reStrArr[1];
};
/**
 * 条件编译方法
 * @param {*} content 需要条件编译的内容
 * @param {*} projectName 需要条件编译的项目名
 * @returns 条件编译后的字符串
 */
const filter = (content, projectName) => {
    let resultContent = '';
    while (content) {
        if (!compileModeHtml() && !compileModeJs() && !compileModeCss()) {
            resultContent = resultContent + content;
            break;
        }
    }
    return resultContent;
    // 处理html部分的条件编译
    function compileModeHtml() {
        let htmlSTag = content.match(notesSHtml);
        if (htmlSTag) {
            let htmlETag = content.match(notesEHteml);
            if (htmlETag) {
                let show = isShow(htmlSTag);
                let sIndex = htmlSTag.index;
                let eIndex = htmlETag.index + 17;
                let advStr = advance(eIndex);
                if (show) {
                    resultContent = resultContent + advStr;
                } else {
                    resultContent = resultContent + deleteSlice(advStr, sIndex, eIndex);
                }
                return true;
            } else {
                throw '请检查html中的条件编译代码';
            }
        } else {
            return false;
        }
    }

    // 处理js部分的条件编译
    function compileModeJs() {
        let jsSTag = content.match(notesSJs);
        if (jsSTag) {
            let jsETag = content.match(notesEJs);
            if (jsETag) {
                let show = isShow(jsSTag);
                let sIndex = jsSTag.index;
                let eIndex = jsETag.index + 11;
                let advStr = advance(eIndex);
                if (show) {
                    resultContent = resultContent + advStr;
                } else {
                    resultContent = resultContent + deleteSlice(advStr, sIndex, eIndex);
                }
                return true;
            } else {
                throw '请检查js中的条件编译代码';
            }
        } else {
            return false;
        }
    }

    // 处理css部分的条件编译
    function compileModeCss() {
        let cssSTag = content.match(notesSCss);
        if (cssSTag) {
            let cssETag = content.match(notesECss);
            if (cssETag) {
                let show = isShow(cssSTag);
                let sIndex = cssSTag.index;
                let eIndex = cssETag.index + 14;
                let advStr = advance(eIndex);
                if (show) {
                    resultContent = resultContent + advStr;
                } else {
                    resultContent = resultContent + deleteSlice(advStr, sIndex, eIndex);
                }
                return true;
            } else {
                throw '请检查css中的条件编译代码';
            }
        } else {
            return false;
        }
    }

    // 用于判断是否展示在本项目中
    function isShow(tag) {
        let ifStr = tag[1];
        let pName = tag[2];
        let pNameArr = pName.split('||');
        if (ifStr == 'ifdef' && pNameArr.some(item => compH5(projectName, item.trim()))) return true;
        if (ifStr == 'ifndef' && !pNameArr.some(item => compH5(projectName, item.trim()))) return true;
        return false;
    }

    // 截取原代码并返回被截取的部分
    function advance(n) {
        let adv = content.substring(0, n);
        content = content.substring(n);
        return adv;
    }

    // h5环境打包平台项目是deploy名称
    function compH5(projectName, itemName) {
        if (process.env.UNI_PLATFORM === 'h5') {
            return itemName.includes(projectName);
        }
        return projectName.includes(itemName);
    }
};

/**
 * 将项目名称写入代码提示
 * @param {*} projectName 项目名称
 */
const wcodePrompt = projectName => {
    try {
        let content = fs.readFileSync('./.vscode/compile-mode.code-snippets', 'utf-8');
        for (let i = 0; i < cpArr.length; i++) {
            let value = cpArr[i];
            let matchArr = content.match(value);
            let sTagStr = matchArr[0];
            let pName = matchArr[1]; // 匹配到的项目名
            content =
                content.slice(0, matchArr.index + sTagStr.length - pName.length) +
                projectName +
                content.slice(matchArr.index + sTagStr.length);
        }
        fs.writeFileSync('./.vscode/compile-mode.code-snippets', content);
    } catch {
        console.log('请在文档中拷贝compile-mode.code-snippets');
    }
};
module.exports = {
    filter,
    deleteSlice,
    wcodePrompt,
};
