
<template>
    <div class="view fl-column mh-0 p-bf">
        <zj-navbar :height="44" title="我的电子券"></zj-navbar>
        <div class="f-1 fl-column mh-0">
            <div class="tabs_style p-LR-16 mh-0 fl-row fl-al-cen">
                <div
                    v-for="(tab, index) in tabs"
                    :key="index"
                    :class="{ 'color-E64F22': selectId == tab.id }"
                    class="tab_style"
                    @click="selectClick(tab, index)"
                >
                    <div class="font-15 weight-500">{{ tab.title }}({{ tab.quantity || 0 }})</div>
                </div>
            </div>
            <div class="line_bottom"></div>
            <div class="type-sort p-LR-16 fl-row mh-0 fl-al-cen font-13 weight-400 color-333">
                <div
                    v-for="(item, index) in typeSortArray"
                    :key="index"
                    class="sort-item bg-F3F3F6 border-rad-4"
                    :class="{ selectSorted: sortId === item.id }"
                    @click="selectSortClick(item, index)"
                    >{{ item.content }}</div
                >
            </div>
            <div class="reminder p-LR-16 bg-FFF7DC font-12 weight-400 color-333" :style="{ marginBottom: showEmpty ? '200rpx' : '0rpx' }"
                >温馨提示：现金券仅可在室内扫码支付</div
            >
            <div class="coupon-wrap f-1 mh-0">
                <zj-data-list
                    background="#F7F7FB"
                    ref="dataList"
                    emptyText="暂无电子券"
                    :showEmpty="showEmpty"
                    :emptyImage="noPicture"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrolltolower"
                >
                    <div class="padding-16">
                        <div v-for="(item, index) in couponArray" :key="index" @click="unusedDetails(item, index)">
                            <div
                                class="border-rad-8"
                                :class="{
                                    'bg-coupon': item.bgColor,
                                    toTakeEffect: !item.bgColor,
                                }"
                            >
                                <div
                                    class="upperLeft font-10 weight-500 color-fff te-center"
                                    :class="{
                                        'bg-ff6133': item.bgColor,
                                        'bg-999': !item.bgColor,
                                    }"
                                    >{{ differentiationType(item.kind) }}</div
                                >
                                <div class="content-wrap fl-row">
                                    <div class="left-wrap fl-row">
                                        <div class="content-left fl-column fl-al-jus-cen">
                                            <div
                                                class="price fl-row fl-al-base"
                                                :class="{
                                                    'color-E64F22': item.bgColor,
                                                    'color-666': !item.bgColor,
                                                }"
                                            >
                                                <div class="symbol font-14 weight-400" v-if="item.couponType && item.couponType != '40'"
                                                    >&yen;</div
                                                >
                                                <div class="font-28 weight-600">{{
                                                    item.couponType && item.couponType != '40' ? item.amount : item.discountValue
                                                }}</div>
                                                <div class="symbol font-14 weight-400" v-if="item.couponType && item.couponType == '40'"
                                                    >折</div
                                                >
                                            </div>
                                            <div
                                                class="font-13 weight-400"
                                                :class="{
                                                    'color-EB5130': item.bgColor,
                                                    'color-666': !item.bgColor,
                                                }"
                                                >{{ thresholdAmount(item) }}</div
                                            >
                                        </div>
                                        <div class="content-sx"></div>
                                    </div>
                                    <div class="right-wrap fl-column fl-jus-bet">
                                        <div class="title font-14 color-1E1E1E weight-600" style="margin-top: 3px">{{ item.title }}</div>
                                        <div class="fl-row fl-jus-bet fl-al-end">
                                            <div class="fl-column fl-jus-cen">
                                                <div
                                                    class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400"
                                                    :class="{
                                                        'color-FA6400 border-fa6400': item.bgColor,
                                                        'color-666 border-999': !item.bgColor,
                                                    }"
                                                    >{{ item.type == 1 ? '油品券' : '非油券' }}</div
                                                >
                                                <div class="time font-10 color-999 weight-400">{{ screen(item) }}</div>
                                            </div>
                                            <div
                                                class="useBtn border-rad-4 te-center color-fff"
                                                :class="{
                                                    'bg-90': item.bgColor,
                                                    'bg-D0D0D0': !item.bgColor,
                                                }"
                                                >{{ buttonText(selectId, item) }}</div
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <div class="btnWrap p-LR-16">
                <div class="weight-500 btn-plain color-E64F22 font-15 border-rad-8" @click="couponAusageRecord">电子券使用记录</div>
            </div>
        </div>
    </div>
</template>

<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import { couponList, couponAmount } from '../../../s-kit/js/v3-http/https3/conpon/index';
export default {
  data () {
    return {
      // 一级筛选条件
      tabs: [
        { id: 20, title: '全部', quantity: '' },
        { id: 40, title: '油品券', quantity: '' },
        { id: 70, title: '非油券', quantity: '' },
      ],
      // 油品、非油、全部、默认选中的值
      selectId: '20',
      // 二级筛选条件
      typeSortArray: [
        { content: '现金券', id: '1' },
        { content: '优惠券', id: '2' },
        { content: '快过期', id: '3' },
        { content: '金额从大到小', id: '4' },
      ],
      // 全排序类型默认值
      sortId: '0',
      // 电子券数组
      couponArray: [],
      // 页码
      page: 1,
      // 油品非油品
      categoryType: '',
      // 现金券优惠券
      kind: '',
      // 排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
      sortType: 'distTime',
      // 排序方式：1-升序2-降序3-默认排序
      orderWay: '3',
      // 暂无电子券图片
      noPicture: require('./images/kt1yhq.png'),
      // 是否展示空态标识
      showEmpty: false,
    };
  },
  mounted () {
    // 获取电子券数量
    this.getCouponQuantity();
    // 获取电子券列表
    this.getCouponList();
  },
  methods: {
    /**
     * @description  : 获取电子券数量
     * @return        {*}
     */
    getCouponQuantity () {
      couponAmount().then(res => {
        if (res.success) {
          // 定义一个对象，将需要赋值的数据放入其中
          let quantityData = {
            20: res.data.unUsedAmount,
            40: res.data.unUsedOilAmount,
            70: res.data.unUsedOtherAmount,
            // 其他需要赋值的数据
          };
          // 使用循环遍历对象，将值赋给对应的属性
          for (let key in quantityData) {
            // 这里使用parseInt是为了解决for..in..循环中数字类型的key变为字符串的问题，这里也可以不使用严格判断来解决此处的问题
            let tab = this.tabs.find(tab => tab.id === parseInt(key));
            if (tab !== undefined) {
              tab.quantity = quantityData[key];
            }
          }
        }
      });
    },
    /**
     * @description  : 选择一级油品非油的类型
     * @return        {*}
     */
    selectClick (tab, index) {
      // 如果点击的是相同的筛选类型阻止向下调用接口
      if (this.selectId === tab.id) return;
      // 将选中的id赋值在页面上高亮展示
      this.selectId = tab.id;
      // 券品类：1-油品券2-非油券（不传参默认查询全部券）
      const categoryTypes = {
        0: '',
        1: '1',
        2: '2',
      };
      // 使用index下标取Objct中某一项
      this.categoryType = categoryTypes[index] || '';
      console.log(this.categoryType, 'this.categoryType');
      // 将券类型置空：0—优惠券；1—现金券；（不传参默认查询全部券）
      this.kind = '';
      // 将排序字段按照快过期优先展示
      this.sortType = 'distTime';
      // 将排序方式设置为默认：
      this.orderWay = 3;
      // 将二级高亮选中取消
      this.sortId = '';
      // 获取电子券列表
      this.getCouponList({ isInit: true });
    },
    /**
     * @description  : 选择二级筛选条件
     * @return        {*}
     */
    selectSortClick (item, index) {
      // 如果点击的是相同的筛选类型阻止向下调用接口
      if (this.sortId == item.id) {
        // 值相同取消高亮
        this.sortId = 0;
        // 如果1级筛选存在值就赋值不存在就置为空
        this.categoryType = this.categoryType == '' ? '' : this.categoryType.toString();
        // 将券类型置空：0—优惠券；1—现金券；（不传参默认查询全部券）
        this.kind = '';
        // 将排序字段按照快过期优先展示
        this.sortType = 'distTime';
        // 将排序方式设置为默认：
        this.orderWay = 3;
      } else {
        console.log('11111');
        // 如果点击的是非相同的筛选类型赋值页面变为高亮
        this.sortId = item.id;
        // 定义的二级筛选类型数组
        const sortTypes = [
          { kind: 1, sortType: 'distTime', orderWay: 3 },
          { kind: 0, sortType: 'distTime', orderWay: 3 },
          { kind: '', sortType: 'endDate', orderWay: 1 },
          { kind: '', sortType: 'faceValue', orderWay: 2 },
        ];
        // 通过index在数组中选择相应的筛选条件
        const sortType = sortTypes[index];
        // 如果存在值就赋值，调用接口进行条件筛选
        if (sortType) {
          this.kind = sortType.kind;
          this.sortType = sortType.sortType;
          this.orderWay = sortType.orderWay;
        }
      }

      this.getCouponList({ isInit: true });
    },
    /**
     * @description  : 电子券类型
     * @return        {*}
     */
    getCouponType (val) {
      console.log(val, '电子券类型');
      return val == 10 ? '满减券' : val == 20 ? '计次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
    },
    /**
     * @description  : 区分券类型
     * @return        {*}
     */
    differentiationType (val) {
      console.log(val, '油品非油品------');
      // 券可用品类 1 油品 2 非油品
      return val == 1 ? '现金券' : '优惠券';
    },
    /**
     * @description  : 电子券未使用详情
     * @param        {Object} item -选中的券信息
     * @return        {*}
     */
    unusedDetails (item, index) {
      if (item.bgColor) {
        let url = `/packages/third-coupon-module/pages/conopn-unuse-detail/main`;
        let params = {
          ...item,
        };
        let type = 'navigateTo';
        this.$sKit.layer.useRouter(url, params, type);
      }
    },
    /**
     * @description  : 上拉加载
     * @return        {*}
     */
    scrolltolower () {
      console.log('上拉触底事件');
      if (this.$refs.dataList.loadStatus == 'contentdown') {
        this.$refs.dataList.loadStatus = 'loading';
        this.getCouponList();
      }
    },
    /**
     * @description  : 下拉刷新
     * @return        {*}
     */
    refreshPullDown (e) {
      // 重置数据获取电子券列表
      this.getCouponList({ isInit: true });
    },
    /**
     * @description  : 获取电子券列表
     * @param         {*} isInit 等于true的时候重置数据
     * @param         {*} couponArray:电子券数组
     * @param         {*} page:页码
     * @param         {*} totalPage:返回数据的条数
     * @param         {*} pageSize:每页10条
     * @param         {*} couponStatus:查询状态：20-未使用，40-已使用，70-已过期
     * @param         {*} categoryType:券品类：1-油品券2-非油券（不传参默认查询全部券）
     * @param         {*} kind:券类型：0—优惠券；1—现金券；（不传参默认查询全部券）
     * @param         {*} sort:排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
     * @param         {*} order:排序方式：1-升序2-降序3-默认排序
     * @return        {*}
     */
    async getCouponList ({ isInit = false } = {}) {
      if (isInit) {
        Object.assign(this, {
          couponArray: [],
          page: 1,
        });
        // 重置入参页码
        this.$refs.dataList.loadStatus = 'loading';
      }
      let { page, couponArray, totalPage } = this;
      let params = {
        pageNum: this.page,
        pageSize: PAGE_SIZE,
        couponStatus: '20', // 券类型：优惠券类型必须为10满减券  20记次券 30兑换券 40折扣券"
        categoryType: this.categoryType, //券品类 券品类：1-油品券2-非油券（不传参默认查询全部券）
        kind: this.kind, //券类型：0—优惠券；1—现金券；（不传参默认查询全部券）
        sort: this.sortType, //排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
        order: this.orderWay, //排序方式：1-升序2-降序3-默认排序
      };
      let res = await couponList(params);
      if (res.success) {
        this.$refs.dataList.stopRefresh();
        this.$refs.dataList.pullDownHeight = 0;
        this.$refs.dataList.pullingDown = false;
        let list = res.data.rows;
        const currentTimestamp = new Date().getTime();
        // 遍历电子券数组 将返回的时间转成时间戳，与当前时间对比，给券标记未到期标识
        list = list.map(item => {
          var handelTime = new Date(item.startTime.replace(/-/g, '/'));
          if (currentTimestamp > Date.parse(handelTime)) {
            // 到使用日期
            item.bgColor = true;
          } else {
            // 未到使用日期
            item.bgColor = false;
          }
          return item;
        });
        // 将处理好的数组合并到定义的数组，放到页面渲染
        couponArray = couponArray.concat(list);
        // 将处理好的数据放置this中
        Object.assign(this, {
          couponArray,
          page: Number(page) + 1,
        });
        console.log('page', page);
        // 返回总条数
        totalPage = res.data.pageSum;
        if (res.data && page >= totalPage) {
          // 没有更多了
          this.$refs.dataList.loadStatus = 'nomore';
        } else {
          // 上拉加载更多
          this.$refs.dataList.loadStatus = 'contentdown';
        }
        this.showEmpty = couponArray.length <= 0 ? true : false;
      }
    },
    buttonText (id, item) {
      return item.bgColor ? '立即使用' : '未生效';
    },
    /**
     * @description  : 处理折和元
     * @return        {*}
     */
    faceValueFilter (item) {
      if (item.couponType && item.couponType == '40') {
        return item.discountValue + '<span style="font-size: 12px;">折</span>';
      } else {
        return (
          '<span class="font-style" style="font-size: 12px">&yen;</span>' + `<span style="font-size: 28px;">${item.amount}</span>`
        );
      }
    },
    /**
     * @description  : 满减券和折扣券说明
     * @return        {*}
     */
    thresholdAmount (item) {
      if (item.thresholdAmount) {
        if (item.couponType && item.couponType == '40') {
          return '满' + item.thresholdAmount + '元可用';
        } else {
          return '满' + item.thresholdAmount + '减' + item.amount;
        }
      } else {
        return '无金额门槛'
      }
    },
    /**
     * @description  : 处理时间(10.08与产品确定列表只展示年月日，详情展示时分秒)
     * @return        {*}
     */
    screen (item) {
      let text = '';
      if (this.selectid == 20) {
        if (item.bgColor) {
          text = '有效期至：' + item.endTime.slice(0, 10);
        } else {
          text = '开始日期：' + item.startTime.slice(0, 10);
        }
      } else {
        text = '有效期至：' + item.endTime.slice(0, 10);
      }
      return text;
    },
    /**
     * @description  : 电子券使用记录
     * @return        {*}
     */
    couponAusageRecord () {
      let url = `/packages/third-coupon-module/pages/coupon-used-record/main`;
      let params = {};
      let type = 'navigateTo';
      this.$sKit.layer.useRouter(url, params, type);
    },
  },
};
</script>

<style scoped lang="scss">
.view {
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        .tab_style {
            margin-right: 22px;
            line-height: 20px;
        }
        .selected {
            color: #e64f22;
        }
    }
    .type-sort {
        width: 100%;
        height: 44px;
        .sort-item {
            height: 30px;
            line-height: 30px;
            margin-right: 12px;
            padding: 0 6.5px; // 上右下左
        }
        .selectSorted {
            color: #e64f22;
        }
    }
    .reminder {
        width: 100%;
        height: 32px;
        line-height: 32px;
    }
    .coupon-wrap {
        .bg-coupon {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);
            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.5;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    padding: 12px 0;
                    .title {
                        // margin-top: 14px;
                        margin-right: 22px;
                        overflow: hidden;
                        max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        font-family: PingFangSC-Medium, PingFang SC;
                    }
                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }
                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }
                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }
        // 券未生效
        .toTakeEffect {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;
            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.3;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    padding: 12px 0;
                    .title {
                        margin-right: 22px;
                        overflow: hidden;
                        // max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        font-family: PingFangSC-Medium, PingFang SC;
                    }
                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }
                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }
                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }
    }
    .btnWrap {
        width: 100%;
        height: 44px;
        line-height: 44px;
        // padding-bottom: 15px;
        margin: 0 auto 10px;
    }
}
</style>
