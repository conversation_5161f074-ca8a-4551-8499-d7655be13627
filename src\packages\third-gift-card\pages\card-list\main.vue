<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="我的礼品卡"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <!-- #ifdef MP-MPAAS -->
                <div class="header-tabs-style p-LR-16 mh-0 fl-row fl-al-cen">
                    <div
                        v-for="(tab, index) in headerTabs"
                        :key="index"
                        :class="{ 'color-E64F22': headerSelectId == tab.id }"
                        class="header-tab_style font-14 weight-500"
                        @click="headerSelectClick(tab, index)"
                        >{{ tab.title }}</div
                    >
                </div>
                <!-- #endif -->
                <div class="f-1 fl-column mh-0" v-if="headerSelectId == 1">
                    <div class="tabs_style p-LR-16 mh-0 fl-row fl-al-cen">
                        <div
                            v-for="(tab, index) in tabs"
                            :key="index"
                            :class="{ 'color-E64F22': selectId == tab.id }"
                            class="tab_style"
                            @click="selectClick(tab, index)"
                        >
                            <div class="font-14 weight-500">{{ tab.title }}({{ tab.quantity || 0 }})</div>
                        </div>
                        <div class="btn_style" @click="addUnclaimed">待领取礼品卡</div>
                    </div>
                    <div class="line_bottom"></div>
                    <div class="coupon-wrap f-1 mh-0">
                        <zj-data-list
                            background="#F7F7FB"
                            ref="dataList"
                            emptyText="暂无数据"
                            :showEmpty="showEmpty"
                            :emptyImage="noPicture"
                            @scrolltolower="scrolltolower"
                            @refreshPullDown="refreshPullDown"
                        >
                            <div class="p-TB-16">
                                <div class="list-wrap" v-for="(item, index) in cardArray" :key="index">
                                    <div class="item-module">
                                        <!-- giftCardStatus	礼品卡账户状态：1—正常；2—冻结； -->
                                        <div v-if="selectId == 1">
                                            <img src="../../images/card-1.png" alt="" class="card-img" v-if="item.giftCardStatus == 1" />
                                            <img src="../../images/card-2.png" alt="" class="card-img" v-if="item.giftCardStatus == 2" />
                                        </div>
                                        <img src="../../images/card-3.png" alt="" class="card-img" v-if="selectId == 2" />
                                        <img src="../../images/card-4.png" alt="" class="card-img" v-if="selectId == 3" />
                                        <div class="modole-bg fl-column">
                                            <div class="fl-row fl-al-cen fl-jus-bet card-div">
                                                <div class="item-left fl-row fl-al-cen">
                                                    <img
                                                        src="../../images/cnpc-logo.png"
                                                        alt=""
                                                        class="logo-img"
                                                        v-if="item.giftCardStatus == 1 && selectId == 1"
                                                    />
                                                    <img
                                                        src="../../images/logo-gray.png"
                                                        alt=""
                                                        class="logo-img"
                                                        v-if="item.giftCardStatus == 2 && selectId != 1"
                                                    />
                                                    <div class="font-16 color-fff marl5 weight-500">{{ item.giftCardName }} </div>
                                                </div>
                                                <div class="item-right fl-row fl-al-cen">
                                                    <div
                                                        class="bg-item name-radius fl-row fl-al-jus-cen weight-400 font-12 color-fff"
                                                        :class="item.giftCardStatus == 1 && selectId == 1 ? 'normal' : 'out'"
                                                        @click="ruleClick(item)"
                                                    >
                                                        使用规则
                                                        <!-- <div class="marl5">{{ item.district || '' }}</div>
                                                        <div class="marl5">{{ item.product || '' }}</div> -->
                                                    </div>
                                                    <div
                                                        class="bg-item sale-radius weight-400 font-12"
                                                        @click="buyClick(item)"
                                                        :class="item.giftCardStatus == 1 && selectId == 1 ? 'color-E64F22' : 'color-686868'"
                                                    >
                                                        消费记录</div
                                                    >
                                                </div>
                                            </div>
                                            <div class="fl-row fl-jus-bet padlr15 f-1">
                                                <div class="item-left fl-row fl-al-cen">
                                                    <div class="weight-400 font-14 mart32 color-fff marr10">余额</div>
                                                    <div class="fl-row color-fff font-18 fl-al-base mart20"
                                                        >&yen;<div class="font-40 font-style height57">{{
                                                            item.availableAmount || '0.00'
                                                        }}</div>
                                                    </div>
                                                </div>
                                                <div class="item-right fl-row fl-al-cen color-fff weight-400 font-14 mart32">
                                                    <div>面值</div>
                                                    <div class="marl5">&yen;{{ item.faceAmount }}</div>
                                                </div>
                                            </div>
                                            <div
                                                class="fl-row bottom-area normal padlr15"
                                                :class="selectId == 1 ? 'fl-jus-bet' : 'fl-sp-end'"
                                            >
                                                <div
                                                    class="font-16"
                                                    :class="item.giftCardStatus == 1 ? 'color-E64F22' : 'color-rgba5'"
                                                    v-if="selectId == 1"
                                                    >{{ item.giftCardStatus == 1 ? '正常' : '冻结' }}</div
                                                >
                                                <div
                                                    class="font-14"
                                                    :class="item.giftCardStatus == 1 && selectId == 1 ? 'color-E64F22' : 'color-rgba5'"
                                                >
                                                    {{ formatTimeFun(item.startDate) }}-{{ formatTimeFun(item.endDate) }}</div
                                                >
                                                <!-- <div class="btn-plain font-12 color-E64F22 weight-400 extension-btn"
                                                    @click="extensionApply" v-if="selectId == 3">申请延期</div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </zj-data-list>
                    </div>
                </div>
                <div class="f-1 fl-column mh-0" v-if="headerSelectId == 2">
                    <couponsList ref="couponsListRef" class="couponsListContent"></couponsList>
                </div>
            </div>
            <div class="bottom-area-list" :style="{ height: '84px' }" v-if="headerSelectId == 2">
                <div class="btnReceive primary-btn btn f-1 font-16 shad-ef color-fff border-rad-8" @click="unclaimedBenefit"
                    >待领取能源锦鲤</div
                >
            </div>
            <!-- #ifndef H5-CLOUD -->
            <div class="bottom-area-list" :style="{ height: '84px' }" v-if="headerSelectId == 1">
                <div
                    class="btn btn-plain font-16 color-E64F22 weight-400 fl-row fl-al-jus-cen f-1"
                    :class="closeForm ? 'mar24' : ''"
                    @click="addPurchase"
                    v-if="cardNumber > 0"
                >
                    <div>购卡订单查询</div>
                    <div class="dot te-center color-fff font-10 weight-500 marl5">
                        {{ cardNumber }}
                    </div>
                </div>
                <div
                    class="btnReceive btn primary-btn font-16 shad-ef color-fff border-rad-8"
                    @click="addCard"
                    :class="{ 'f-1': cardNumber <= 0 }"
                    v-if="closeForm"
                    >绑定礼品卡</div
                >
            </div>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <div class="bottom-area-list" :style="{ height: cardNumber > 0 ? '84px' : '0' }" v-if="headerSelectId == 1">
                <div
                    class="btn btn-plain font-16 color-E64F22 weight-400 fl-row fl-al-jus-cen f-1"
                    @click="addPurchase"
                    v-if="cardNumber > 0"
                >
                    <div>购卡订单查询</div>
                    <div class="dot te-center color-fff font-10 weight-500 marl5">
                        {{ cardNumber }}
                    </div>
                </div>
            </div>
            <!-- #endif -->
        </div>
        <custom-popup :maskClick="false" ref="cardPopup" type="bottom">
            <div class="model-div fl-column" style="height: 337px">
                <div class="fl-row fl-jus-bet fl-al-cen marlr24">
                    <div class="font-17 weight-500 color-333">消费记录</div>
                    <div class="close" @click.stop="closepop">
                        <img src="../../images/close.png" alt />
                    </div>
                </div>
                <div class="f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="dataListConsume"
                        emptyText="暂无数据"
                        :showEmpty="showEmptyConsume"
                        @scrolltolower="scrolltolowerConsume"
                        @refreshPullDown="refreshPullDownConsume"
                    >
                        <div class="card-default fl-column padtb16 marlr12" v-for="(item, index) in consumeList" :key="index">
                            <div class="fl-row fl-jus-bet">
                                <div class="font-14 color-333 weight-500 fl-row f-1">
                                    <div class="marr5">订单{{ item.orderNo }}</div>
                                    <div class="copy-div" @click.stop="handleCopy(item.orderNo)">复制</div>
                                </div>
                                <div class="font-14 color-E64F22 weight-500">{{ item.payAmount }}</div>
                            </div>
                            <div class="fl-row fl-jus-bet weight-400 mart5">
                                <div class="font-12 color-999">{{ item.payTime }}</div>
                                <div class="font-12 color-999">余&yen;{{ item.balance }}</div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
        </custom-popup>
        <custom-popup :maskClick="false" ref="rulePopup" type="bottom">
            <div class="model-div fl-column" style="height: 337px">
                <div class="fl-row fl-jus-bet fl-al-cen marlr24">
                    <div class="font-17 weight-500 color-333">使用规则</div>
                    <div class="close" @click.stop="closeRulePop">
                        <img src="../../images/close.png" alt />
                    </div>
                </div>
                <div class="f-1 mh-0">
                    <div class="card-default fl-column padtb16 marlr12" v-if="ruleDescribe.length > 0">
                        <div v-for="(item, index) in ruleDescribe" :key="index">
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
        </custom-popup>
        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <div class="content">
                        <div class="font-14 color-333 weight-bold">已提交申请延期，24小时内完成审核 审核成功后您可以继续使用该礼品卡</div>
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view
                        class="btn confirm"
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        @click.stop="clickBtn"
                        >确定
                    </view>
                </view>
            </div>
        </custom-popup>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import {
    giftCardNumQuery,
    giftCardList,
    giftCardConsumeList,
    buyGiftCardNumQuery,
} from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import { clientCode } from '../../../../../project.config';
import { mapGetters, mapState } from 'vuex';
import couponsList from '../../components/couponsList.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 页码
            pageNum: 1,
            // 消费页码
            page: 1,
            // 礼品卡列表
            cardArray: [],
            // 总页码
            totalPage: 0,
            // 消费总页码
            totalPageConsume: 0,
            // 使用状态
            selectId: '1',
            // 消费记录列表
            consumeList: [],
            /**
             *
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            tabs: [
                { id: 1, title: '未用完', quantity: '' },
                { id: 2, title: '已用完', quantity: '' },
                { id: 3, title: '已过期', quantity: '' },
            ],
            // 头部使用状态：1-礼品卡，2-能源锦鲤
            headerTabs: [
                { id: 1, title: '礼品卡' },
                { id: 2, title: '能源锦鲤' },
            ],
            // 头部使用状态
            headerSelectId: 1,
            // 暂无电子券图片
            noPicture: require('../../images/lpk-no-data.png'),
            // 是否展示空态标识
            showEmpty: false,
            // 消费列表为空
            showEmptyConsume: false,
            // 购卡数量
            cardNumber: 0,
            // 使用规则
            ruleDescribe: '',
            closeForm: true,
            refer: '',
        };
    },
    async onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.refer = params.myRefer;
            }
            this.$sKit.mpBP.tracker('我的页面', {
                seed: 'minePageBiz',
                pageID: 'giftCardPage',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$sKit.mpBP.tracker('绑定礼品卡', {
                seed: 'bindGiftCardBiz',
                pageID: 'giftListPage',
                refer: this.refer,
                channelID: clientCode,
                address: this.cityName,
            });
        }
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {},
    components: {
        couponsList,
    },
    //生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
        // #ifdef MP-MPAAS
        //小程序上要隐藏掉卡密绑定入口，app上打开
        if (this.isHarmony) {
            this.closeForm = false;
        } else {
            this.closeForm = true;
        }
        // #endif
    },
    onShow() {
        if (this.headerSelectId == 1) {
            this.pageLoad(true);
        } else if (this.headerSelectId == 2) {
            this.$nextTick(() => {
                this.$refs.couponsListRef.pageLoad(true);
            });
        }
    },
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        /**
         * 页面加载
         */
        pageLoad(isInitcard) {
            this.getGiftCardList({ isInitcard: isInitcard });
            this.queryNmuGift();
            this.buyGiftCardNumQuery();
        },
        // 处理时间只展示年月日
        formatTimeFun(time) {
            if (!time) {
                return;
            }
            let date = time.split(' ')[0];
            date = date.replace(/\-/g, '.');
            return date;
        },
        selectClick(tab, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.selectId === tab.id) return;
            // 将选中的id赋值在页面上高亮展示
            this.selectId = tab.id;
            this.getGiftCardList({ isInitcard: true });
        },
        // 头部切换事件
        headerSelectClick(tab, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.headerSelectId === tab.id) return;
            // 将选中的id赋值在页面上高亮展示
            this.headerSelectId = tab.id;
            if (tab.id == 1) {
                // 加载礼品卡
                this.pageLoad(true);
            } else if (tab.id == 2) {
                this.$nextTick(() => {
                    console.log(this.$refs.couponsListRef, 'couponsListRef');

                    this.$refs.couponsListRef.pageLoad(true);
                });
            }
        },
        // 打开使用规则
        ruleClick(item) {
            if (typeof item.describe == 'string') {
                this.ruleDescribe = item.describe.split(/[(\r\n)\r\n]+/);
            }
            // this.ruleDescribe = item.describe || '';
            // if(item.describe){
            this.$refs.rulePopup.open();
            // }else{

            // }
        },
        // 关闭使用规则
        closeRulePop() {
            this.$refs.rulePopup.close();
        },
        // 打开消费记录
        buyClick(item) {
            this.$refs.cardPopup.open();
            this.giftCardConsumeListPost({ isInit: true }, item);
        },
        // 关闭消费记录
        closepop() {
            this.$refs.cardPopup.close();
        },
        // 绑定礼品卡
        addCard() {
            let url = `/packages/third-gift-card/pages/binding/main`;
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, { refer: this.refer }, type);
        },
        // 购物卡查询
        addPurchase() {
            let url = `/packages/third-gift-card/pages/purchase-list/main`;
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, {}, type);
        },
        // 待领取礼品卡
        addUnclaimed() {
            let url = `/packages/third-gift-card/pages/unclaimed-list/main`;
            let type = 'navigateTo';
            let params = {
                type: 'unclaimed',
            };
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         *
         * 根据会员编号、礼品卡状态和礼品卡类型可以查询用户名下对应的购卡数量。
         * status礼品卡状态：
            1—预个人化；
            2—正常；
            3—已绑定；
            4—已冻结；
            5—注销；
          * giftCardType礼品卡类型：
            1—实体卡；
            2—电子卡；
         * */
        async buyGiftCardNumQuery() {
            let params = {
                status: Number(2),
                giftCardType: Number(2),
            };
            let res = await buyGiftCardNumQuery(params);
            if (res.success) {
                this.cardNumber = res.data.cardNumber;
            }
        },
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:被复制的内容
         * @return        {*}
         */
        handleCopy(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        /**
         * @description  : 上拉加载礼品卡列表
         * @return        {*}
         */
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getGiftCardList();
            }
        },
        /**
         * @description  : 上拉加载礼品卡消费记录列表
         * @return        {*}
         */
        scrolltolowerConsume() {
            if (this.$refs.dataListConsume.loadStatus == 'contentdown') {
                this.$refs.dataListConsume.loadStatus = 'loading';
                this.giftCardConsumeListPost();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            this.$refs.dataList.loadStatus = 'loading';
            this.pageLoad(true);
        },

        refreshPullDownConsume(e) {
            this.$refs.dataListConsume.loadStatus = 'loading';
            this.giftCardConsumeListPost({ isInit: true });
        },
        // 查询用户名下礼品卡数量接口
        async queryNmuGift() {
            let res = await giftCardNumQuery({}, { isCustomErr: true });
            // availableCount	可用张数
            // usedCount	已用完张数
            // expirationCount	已过期张数
            if (res.success) {
                let quantityData = {
                    1: res.data.availableCount,
                    2: res.data.usedCount,
                    3: res.data.expirationCount,
                    // 其他需要赋值的数据
                };
                // 使用循环遍历对象，将值赋给对应的属性
                for (let key in quantityData) {
                    // 这里使用parseInt是为了解决for..in..循环中数字类型的key变为字符串的问题，这里也可以不使用严格判断来解决此处的问题
                    let tab = this.tabs.find(tab => tab.id === parseInt(key));
                    if (tab !== undefined) {
                        tab.quantity = quantityData[key];
                    }
                }
            }
        },
        // 查询用户名下礼品卡列表接口
        async getGiftCardList({ isInitcard = false } = {}) {
            console.log('---isInitcard---', isInitcard);
            // console.log('---this.$refs---',this.$refs)
            if (isInitcard) {
                Object.assign(this, {
                    cardArray: [],
                    pageNum: 1,
                });
                // 重置入参页码
            }
            // console.log('---page---',this.pageNum)
            let { pageNum, cardArray, totalPage, selectId } = this;

            /***
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            let params = {
                pageNum: pageNum,
                pageSize: PAGE_SIZE,
                usedStatus: Number(selectId),
            };
            let res = await giftCardList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];

                // 将处理好的数组合并到定义的数组，放到页面渲染
                cardArray = cardArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    cardArray,
                    pageNum: Number(pageNum) + 1,
                });
                console.log('page', pageNum);
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && pageNum >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = cardArray.length <= 0 ? true : false;
            }
        },
        // 查看礼品卡券消费列表接口
        async giftCardConsumeListPost({ isInit = false } = {}, item) {
            if (isInit) {
                Object.assign(this, {
                    consumeList: [],
                    page: 1,
                });
                // 重置入参页码
                // this.$refs.dataListConsume.loadStatus = 'loading';
            }
            let { page, consumeList, totalPageConsume } = this;
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
                giftCardNo: item.giftCardNo,
            };
            let res = await giftCardConsumeList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataListConsume.stopRefresh();
                this.$refs.dataListConsume.pullDownHeight = 0;
                this.$refs.dataListConsume.pullingDown = false;
                let list = res.data.rows || [];
                // 将处理好的数组合并到定义的数组，放到页面渲染
                consumeList = consumeList.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    consumeList,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                totalPageConsume = res.data.pageSum;
                if (res.data && page >= totalPageConsume) {
                    // 没有更多了
                    this.$refs.dataListConsume.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataListConsume.loadStatus = 'contentdown';
                }
                this.showEmptyConsume = consumeList.length <= 0 ? true : false;
            }
        },
        // 申请延期
        extensionApply() {
            this.$refs.popDialogFlag.open();
        },
        //关闭弹窗
        clickBtn() {
            this.$refs.popDialogFlag.close();
        },
        // 待领取能源锦鲤
        unclaimedBenefit() {
            let url = '/packages/third-gift-card/pages/coupons-unclaimed-list/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
};
</script>
<style scoped lang="scss">
/* @import url(); 引入css类 */
.view {
    .couponsListContent {
        height: 100%;
    }
    .header-tabs-style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        justify-content: center;
        .header-tab_style {
            // margin-right: 25px;
            width: 50%;
            line-height: 20px;
            text-align: center;
        }
    }
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        justify-content: space-between;

        .tab_style {
            // margin-right: 25px;
            line-height: 20px;
        }

        .selected {
            color: #e64f22;
        }
        .btn_style {
            padding: 11rpx 22rpx;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #ff6b2c;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #ff6b2c;
        }
    }

    .coupon-wrap {
        .list-wrap {
            position: relative;
            width: 345px;
            margin: 0 auto 15px;

            .item-module {
                position: relative;
                width: 100%;
                height: 175px;
                overflow: hidden;

                .card-img {
                    width: 345px;
                    height: 175px;
                }

                .modole-bg {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;

                    .card-div {
                        padding: 15px 15px 0;
                    }

                    .bottom-area {
                        width: 100%;
                        height: 40px;
                        line-height: 40px;

                        .extension-btn {
                            width: 60px;
                            height: 25px;
                            line-height: 25px;
                            border-radius: 8px;
                            align-self: center;
                            margin-left: 10px;
                        }
                    }

                    .item-left {
                        flex: 1;

                        .logo-img {
                            width: 24px;
                            height: 23px;
                            flex-shrink: 0; /*防止被压缩*/
                        }
                    }

                    .item-right {
                        .bg-item {
                            min-width: 70px;
                            height: 25px;
                            line-height: 25px;
                            text-align: center;
                            box-sizing: border-box;
                        }

                        .name-radius {
                            padding: 0 7px;

                            border-radius: 4px 0px 0px 4px;

                            &.normal {
                                background: #e64f22;
                            }

                            &.out {
                                background: #838282;
                            }
                        }

                        .sale-radius {
                            padding: 0 6px;
                            border-radius: 0px 4px 4px 0px;
                            background: rgba(255, 255, 255, 0.7);
                            backdrop-filter: blur(50px);
                        }
                    }
                }
            }
        }
    }

    .bottom-area-list {
        // width: 345px;
        // height: 84px;
        display: flex;
        align-items: center;
        // margin: 0px 15px;
        // padding: 15px 0 0;
        justify-content: space-between;
        width: 100%;
        padding: 15px 15px 0;
        .btnReceive {
            // width: 100%;
            text-align: center;
            // padding: 8px 0px;
        }

        .btn {
            width: 166px;
            height: 44px;
            line-height: 44px;
            font-weight: bold;
            text-align: center;
            border-radius: 8px;
        }

        .postrelative {
            position: relative;
        }

        // .dot {
        //     position: absolute;
        //     left: 130px;
        //     top: -10px;
        //     width: 20px;
        //     height: 17px;
        //     line-height: 17px;
        //     background: linear-gradient(315deg, #ff634f 0%, #ff000b 100%);
        //     border-radius: 9px 9px 9px 0px;
        // }

        .dot {
            width: 20px;
            height: 17px;
            line-height: 17px;
            background: linear-gradient(315deg, #ff634f 0%, #ff000b 100%);
            border-radius: 9px 9px 9px 0px;
        }
    }

    .marl5 {
        margin-left: 5px;
    }

    .marr10 {
        margin-right: 10px;
    }

    .padlr15 {
        padding: 0 15px;
    }

    .mart32 {
        margin-top: 32px;
    }

    .mart20 {
        margin-top: 20px;
    }

    .height {
        height: 57px;
        line-height: 25px;
    }
}

.mar24 {
    margin-right: 24rpx;
}

.model-div {
    background: #f6f6f6;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 16px;

    .marlr24 {
        margin: 0 16px 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .marr5 {
        margin-right: 5px;
    }

    .marlr12 {
        margin: 0 12px 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
        padding-left: 10px;
        padding-right: 10px;
    }

    .mart5 {
        margin-top: 5px;
    }

    .copy-div {
        width: 29px;
        padding: 3px 0;
        height: 16px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #999999;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        text-align: center;
        box-sizing: border-box;
        overflow: hidden;
        flex-shrink: 0;
        line-height: 0.8;
    }

    .close {
        padding: 20px;

        img {
            width: 13px;
            height: 13px;
        }
    }
}

._modal {
    flex: none;
    width: 280px;
    min-height: 104px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 19px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 34px;
            text-align: center;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}
</style>
