<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            :back-text="isEcard ? '重置支付密码' : '设置支付密码'"
            :back-text-style="pageConfig.titleStyle"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :border-bottom="false"
        ></u-navbar>

        <div class="cell-content">
            <div class="cell-div">
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">姓名</div>
                    <input class="cell-input" type="text" placeholder="请输入姓名" :value="idName" disabled />
                </div>
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">证件类型</div>
                    <div @click="selectShow" class="documentType">
                        <input class="cell-input" type="text" placeholder="请选择证件类型" v-model="nameID" disabled />
                        <!-- <u-icon name="arrow-right" v-show="documentTypeShow"></u-icon> -->
                    </div>
                    <u-select v-model="show" mode="single-column" :list="list" @confirm="confirm" :default-value="defaultValue"></u-select>
                </div>
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <!--   -->
                    <div class="cell-title">证件号码</div>
                    <input
                        class="cell-input"
                        type="text"
                        v-model="idNum"
                        placeholder="请输入证件号码"
                        :focus="idNumInputFocus"
                        @blur="handleidNumInputBlur"
                        @focus="handleidNumInputFocus"
                        :disabled="idNumFlag === '' ? false : true"
                    />
                </div>
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">加油卡号</div>
                    <input class="cell-input" type="text" placeholder="请输入卡号" :value="cardNo" disabled />
                </div>
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">验证码</div>
                    <input
                        class="cell-input"
                        type="text"
                        placeholder="请输入验证码"
                        maxlength="6"
                        v-model="code"
                        :focus="sendCodeInputFocus"
                        @focus="handleSendCodeInputFocus"
                        @blur="handleSendCodeInputBlur"
                    />
                    <div class="cell-code" @click="clickSendCode">{{ sendTest }}</div>
                </div>

                <!-- 密码输入 -->
                <div class="cell">
                    <!-- :class="{ 'input-focus-cursor': clickPasswordIndex === 1 }"  -->

                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">{{ isEcard ? '新密码' : '密码' }}</div>
                    <div class="password-input" @click="showKeyboard1" :class="showBorderOne ? 'inp-bg2' : ''">
                        <input
                            type="text"
                            class="cell-input"
                            :password="isShowPassword"
                            v-model="password"
                            placeholder="请输入密码"
                            @blur="closeKeyboard1"
                            disabled
                        />
                    </div>
                    <!-- focus="true" -->
                    <!-- <img class="cell-icon" @click="clickEye(1)" :src="isShowPassword ? '/	static/eye-none.png' : '/static/eye-block.png'" mode="widthFix"> -->
                </div>
                <div class="cell">
                    <span class="cell-asterisk">*</span>
                    <div class="cell-title">确认密码</div>
                    <!-- :class="{ 'input-focus-cursor': 	clickPasswordIndex === 2 }" -->
                    <div class="password-input" @click="showKeyboard2" :class="showBorderTwo ? 'inp-bg2' : ''">
                        <!-- @click="showKeyboard2" -->
                        <input
                            type="text"
                            class="cell-input"
                            :password="isShowDefinePassword"
                            v-model="definePassword"
                            placeholder="请输入密码"
                            @blur="closeKeyboard2"
                            disabled
                        />
                    </div>
                </div>
            </div>
            <div class="cell-ps">
                <span class="cell-asterisk">*</span>
                <p class="cell-msg">验证码短信会发送到您的办卡预留手机号上</p>
            </div>

            <div class="venicle-btn">
                <div class="venicle-btn-view">
                    <div class="venicle-btn-text" @click="clickDefine">{{ isEcard ? '重置支付密码' : '设置支付密码' }}</div>
                </div>
                <div class="btn-text-sp"></div>
            </div>

            <safe-password id="passwordKeyboardId" title="安全键盘" class="psw-mask">
                <!-- @bindpwdFinish ="onPwdFinish" -->
                <!-- <view @click.stop="showKeyboard1">
					00000000000
					{{password == undefined||password=='' ? '请输入密码1':password}}<view v-if="cursorShow1" class="password-cursor" :style="{position:password == undefined||password=='' ? 'absolute':''}">
        </view></view>-->
            </safe-password>
            <div class="cell-ps">
                <span class="cell-asterisk">*</span>
                <p v-if="isEcard" class="be-careful">注意：油卡移动支付密码为APP、小程序各端共用,重置后各端油卡移动支付密码均将被重置。</p>
                <p v-else class="be-careful">注意：油卡移动支付密码为APP、小程序各端共用。</p>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
let codeTimer = null;
import pageConfig from '@/utils/pageConfig.js';
import { mapGetters } from 'vuex';
import { setPayPassword, sendVerifyCode } from '@/api/home.js';
import card from '../../../../store/modules/card';
export default {
    data() {
        return {
            defaultValue: [0], //下拉列表的默认值
            backText: '', //标题名
            isEcard: '', //电子卡是否设置密码
            btnText: '', //按钮文字
            cardNo: '',
            idName: '',
            pageConfig: pageConfig, //页面配置
            code: '',
            sendTest: '获取验证码', // 验证码文字
            sendCodeInputFocus: false,
            idNumInputFocus: false, //证件号input失焦聚焦
            password: '',
            isShowPassword: true,
            definePassword: '',
            isShowDefinePassword: true,
            clickPasswordIndex: 0,
            showKeyboard: false,
            // passwordKeyboard: "",
            passwordlength: '',
            definePasswordlength: '',
            samepassword: '', //校验密码是否相同
            idType: '1',
            nameID: '身份证',
            idNum: '',
            idNumFlag: '',
            show: false, //证件类型下拉列表显示
            idNumber: '',
            cardType: '',
            showBorderOne: false,
            showBorderTwo: false,
            beforepages: '',
            documentTypeShow: '',
            list: [
                {
                    value: '1',
                    label: '身份证',
                },
                {
                    value: '2',
                    label: '军官证',
                },
                {
                    value: '3',
                    label: '护照',
                },
                {
                    value: '4',
                    label: '营业执照',
                },
                {
                    value: '5',
                    label: '驾驶证',
                },
                {
                    value: '6',
                    label: '组织机构代码证',
                },
                {
                    value: '7',
                    label: '港澳居民往来内地通行证、台胞证',
                },
                // {
                //   idType: "8",
                //   idTypeName: "台胞证"
                // }
            ],
        };
    },
    onLoad(options) {
        let params = JSON.parse(options.params);
        this.idName = params.idName || '';
        this.idNum = params.idNum || '';
        this.idNumFlag = params.idNum || '';
        this.cardNo = params.cardNo || '';
        this.isEcard = options.id * 1;
        this.idType = params.idType;
        // cardType  0 1 2 3 4
        // if (this.isEcard) {
        //   this.idType = options.id == 0 ? params.idType : '1'
        // } else {
        //   this.idType = '1'
        // }
        this.selectComponent('#passwordKeyboardId').init(this.openId, 'number'); //初始化密码键盘
        var pages = getCurrentPages();
        let beforepagesInfo = pages[pages.length - 2];
        this.beforepages = beforepagesInfo.route.split('/')[3];
        console.log(this.beforepages);
        if (this.isEcard && !this.cardType) {
            this.documentTypeShow = true;
        } else if (this.beforepages == 'confirm-order') {
            this.documentTypeShow = true;
        }

        if (params.idType) {
            // 123456789
            this.list.map(item => {
                if (item.value == params.idType) {
                    this.cardType = params.idType;
                    this.nameID = item.label;
                }
            });
        }
    },

    onUnload() {
        clearInterval(codeTimer);
    },
    computed: {
        ...mapGetters(['openId', 'cardList']),
    },

    methods: {
        // input 框闪动问题
        // onInput (e) {
        //
        //   this.idNum = e.target.value;
        // },
        // 证件类型列表
        selectShow() {
            // 判断设置还是重置
            if (this.isEcard) {
                if (this.cardType) {
                    // 电子卡
                    this.show = false;
                } else {
                    // 实体卡
                    this.show = true;
                }
            } else {
                if (!this.cardType) {
                    this.show = true;
                    //  console.log("ppppppppppppppppp",this.cardType)
                }
            }
        },

        //  证件类型选择确定
        confirm(e) {
            this.idType = e[0].value;
            this.nameID = e[0].label;
        },
        // 关闭密码键盘
        closeKeyboard1() {
            this.selectComponent('#passwordKeyboardId').closeKeyboard();
        },
        closeKeyboard2() {
            this.selectComponent('#passwordKeyboardId').closeKeyboard();
        },
        // 密码键盘1
        showKeyboard1() {
            this.showBorderTwo = false;
            this.showBorderOne = true;
            this.handleSendCodeInputBlur();
            this.handleidNumInputBlur();
            this.selectComponent('#passwordKeyboardId').openKeyboard(
                'password_unique1',
                6,
                pwd => {
                    this.passwordlength = this.selectComponent('#passwordKeyboardId').getLength('password_unique1');
                    this.password = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                    this.showBorderOne = false;
                },
            );
        },
        // 密码键盘2
        showKeyboard2() {
            this.showBorderOne = false;
            this.showBorderTwo = true;
            this.handleSendCodeInputBlur();
            this.handleidNumInputBlur();
            // console.log("2222222222222222222222222224444444444",this.showBorderOne,this.showBorderTwo)
            this.selectComponent('#passwordKeyboardId').openKeyboard(
                'password_unique2',
                6,
                pwd => {
                    this.definePasswordlength = this.selectComponent('#passwordKeyboardId').getLength('password_unique2');
                    this.definePassword = pwd;
                },
                () => {
                    console.log('密码键盘2的关闭函数');
                    this.showBorderTwo = false;
                },
            );
        },
        //证件号输入框聚焦
        handleidNumInputFocus(e) {
            this.idNumInputFocus = true;
            this.closeKeyboard1();
            this.closeKeyboard2();
        },
        //证件号输入框失焦
        handleidNumInputBlur(e) {
            this.idNumInputFocus = false;
        },
        handleSendCodeInputBlur(e) {
            setTimeout(() => {
                this.sendCodeInputFocus = false;
            }, 300);
        },
        handleSendCodeInputFocus(e) {
            this.clickPasswordIndex = 0;
            this.showKeyboard = false;
            this.sendCodeInputFocus = true;
            this.closeKeyboard1();
            this.closeKeyboard2();
        },
        // 点击发送验证码
        async clickSendCode() {
            if (!this.idNum) return this.$util.noneToast('请填写证件号码');
            this.idNumber = this.checkIDType(this.idNum, this.idType);
            if (!this.idNumber) return this.$util.noneToast('请输入正确的证件号码');
            let { cardNo, idName, idType, idNum } = this;

            let params = {
                cardNo,
                idName,
                idType,
                idNum,
            };
            if (this.sendTest == '重新获取' || this.sendTest == '获取验证码') {
                // await getCheckCode({
                // 	typeCode:'50001'
                // })

                let { status, info } = await sendVerifyCode(params);
                console.log('33333');
                if (status == -1) {
                    uni.showModal({
                        title: '提示',
                        content: info,
                        confirmColor: '#FF8200',
                        showCancel: false,
                    });
                    // uni.showToast({
                    //     title: "证件类型或证件号不一致",
                    //     icon: "none",
                    //     duration: 2000,
                    // });
                    this.sendTest = '获取验证码';
                } else {
                    let downNum = 60;
                    this.sendTest = downNum + 's';
                    codeTimer = setInterval(() => {
                        downNum--;
                        if (downNum == 0) {
                            clearInterval(codeTimer);
                            this.sendTest = '重新获取';
                        } else {
                            this.sendTest = downNum + 's';
                        }
                    }, 1000);
                }
            }
        },
        // 小眼睛点击事件
        // clickEye(index) {
        // 	if(index == 1) {
        // 		this.isShowPassword = !this.isShowPassword
        // 	} else if (index == 2) {
        // 		this.isShowDefinePassword = !this.isShowDefinePassword
        // 	}
        // },

        // 确认按钮点击事件
        async clickDefine() {
            let cardLastNum = this.cardNo.substr(this.cardNo.length - 4);
            // console.log(cardLastNum,"=======================================")
            this.idNumber = this.checkIDType(this.idNum, this.idType);

            this.samepassword = this.selectComponent('#passwordKeyboardId').equal('password_unique1', 'password_unique2');
            if (!this.idNumber) return this.$util.noneToast('请输入正确的证件号码');
            if (this.sendTest == '获取验证码') return this.$util.noneToast('请先获取验证码');
            if (!/^\d{6}$/.test(this.code)) return this.$util.noneToast('请输入6位验证码');
            if (!this.code) return this.$util.noneToast('验证码不能为空');

            if (this.definePasswordlength < 6 && this.passwordlength < 6) {
                console.log('两个一起清空');
                this.definePassword = '';
                this.password = '';
                return this.$util.noneToast('请输入6位密码');
            }
            if (this.passwordlength < 6) {
                this.password = '';
                return this.$util.noneToast('请输入6位密码');
            }
            if (this.definePasswordlength < 6) {
                this.definePassword = '';
                return this.$util.noneToast('请输入6位密码');
            }
            if (this.samepassword == 1) {
                this.password = '';
                this.definePassword = '';
                return this.$util.noneToast('两次输入的密码不匹配');
            }
            let param = {
                payPassword: this.selectComponent('#passwordKeyboardId').getCipherPWD('password_unique1'), //获取密码键盘的密码
                cardNo: this.cardNo,
                idName: this.idName,
                idType: this.idType,
                idNum: this.idNum,
                verifyCode: this.code,
            };

            console.log('123467890', param);
            let { status, errorCode, info } = await setPayPassword(param);
            // console.log(res)
            if (status == -1) {
                if (errorCode == 60000041) {
                    //页面停留时间过长
                    this.$util.showModal(info, true).then(() => {
                        uni.redirectTo({
                            url: '/pages/home/<USER>',
                        });
                    });
                } else {
                    this.$util.showModal(info, true).then(() => {});
                }
                // if(this.beforepages  == "confirm-order"){ //从支付页面跳转到设置密码页面,设置密码失败后直接跳转到首页
                //     this.$util.showModal(info, true).then(() => {
                //         uni.redirectTo({
                //           url:"/pages/home/<USER>"
                //         })
                //     });
                // }else
                this.password = '';
                this.definePassword = '';
                this.passwordlength = '';
                this.definePasswordlength = '';
            } else {
                // 电子卡开通后进行密码设置,设置成功后跳转到油卡管理页面
                if (!this.isEcard && this.cardType) {
                    //判断是否为设置还是重置和是电子卡还是实体卡
                    //
                    // console.log("pppppppppppppppppppppppppaaaaaaaaaaaaaa",this.cardType)
                    // this.$store.dispatch("card/getAllCardList");
                    // uni.navigateBack({
                    //   delta: 3
                    // })
                    wx.showModal({
                        title: '提示',
                        content: `您的电子卡（${cardLastNum}）已设置移动支付密码成功。`,
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: function (res) {
                            if (res.confirm) {
                                // uni.redirectTo({
                                //   url: "/packages/oil-card/pages/manage-oilcard/main",
                                // });
                                uni.navigateBack({
                                    //返回
                                    delta: 1,
                                });
                            } else if (res.cancel) {
                                // 目前什么也不做处理
                            }
                        },
                    });
                }
                // 支付时油卡未设置密码，设置完密码后跳到首页
                else if (this.beforepages == 'confirm-order') {
                    wx.showModal({
                        title: '提示',
                        content: `您的油卡（${cardLastNum}）已设置移动支付密码成功。`,
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: function (res) {
                            if (res.confirm) {
                                uni.redirectTo({
                                    url: '/pages/home/<USER>',
                                });
                            } else if (res.cancel) {
                                // 目前什么也不做处理
                            }
                        },
                    });
                } else {
                    // await this.$util.showModal("重置支付密码成功，能源e站App支付密码也同步重置。", true)
                    // uni.navigateBack({
                    //   delta: 1
                    // })
                    wx.showModal({
                        title: '提示',
                        content: '重置支付密码成功，能源e站App支付密码也同步重置。',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: function (res) {
                            if (res.confirm) {
                                uni.navigateBack({
                                    delta: 1,
                                });
                            } else if (res.cancel) {
                                // 目前什么也不做处理
                            }
                        },
                    });
                }
                // if(this.isEcard){
                // 	uni.navigateBack({
                // 		url:"/packages/password/pages/home/<USER>"
                // 	})
                // }else{
                // 	uni.navigateBack({
                // 		url:"/packages/password/pages/card-list/main"
                // 	})
                // }
            }
        },
        // 证件号校验
        checkIDType(str, type) {
            console.log(str, type, 'strstrstrstr');
            // let typeData = {
            //   '1': /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
            //   '2': /^[\u4E00-\u9FA5]{3}[0-9]{8}号$/,
            //   '3': /(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/,
            //   '4': /^[0-9]{18}$/,  //营业执照
            //   '5': /(^\d{15}$)|(^\d{18}$)/, //驾驶证 位数15位或18位
            //   '6': /^[0-9A-Z]{8}-[0-9A-Z]{1}$/, /// 6、组织机构代码证规则：由八位数字(或大写字母)+连字符+一位数字(或大写字母)组成
            //   '7': /^C[0-9A-Z]{1}[0-9]{7}$/, //7、港澳通行证规则： 大写字母C+8位数字，或大写字母C+一个大写字母+7位数字
            //   '8': /^[0-9]{10}[a-zA-Z]{1}/   //台胞证规则：10个数字+1个英文字母
            // }
            let typeData = {
                1: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
                2: /^.{5,20}$/,
                3: /^.{5,20}$/,
                4: /^.{5,20}$/,
                5: /^.{5,20}$/,
                6: /^.{5,20}$/,
                7: /^.{5,20}$/,
                8: /^.{5,20}$/,
            };
            let reg = typeData[type];
            console.log(reg);
            return reg.test(str);
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;

    .cell-content {
        margin: 10px;
        .cell-div {
            background-color: #ffffff;
            padding-bottom: 8px;

            .cell {
                position: relative;
                // z-index: 1;
                display: flex;
                align-items: center;
                // margin-left: 10px;
                width: 345px;
                padding-left: 5px;
                padding-right: 10px;
                border-radius: 5px;
                height: 50px;
                border-bottom: 1px solid #e9e9e9;
                .cell-title {
                    width: 30%;
                    color: #333333;
                    font-size: 14px;
                    font-weight: 400;
                }
                .cell-input {
                    position: relative;
                    text-align: left;
                    right: 0;
                    flex: 1;
                    line-height: 50px;
                    color: #333333;
                    text-indent: 2px;
                    width: 100%;
                }
                .password-input {
                    width: 100%;
                    margin-left: 66rpx;
                }
                .cell-icon {
                    padding-left: 20px;
                    width: 16px;
                    max-height: 16px;
                    padding-top: 10px;
                    padding-bottom: 10px;
                }
                .cell-code {
                    margin-left: 10px;
                    color: #ffffff;
                    background-color: $btn-color;
                    font-size: 13px;
                    width: 99px;
                    height: 28px;
                    border-radius: 5px;
                    line-height: 28px;
                    text-align: center;
                }

                .cell-asterisk {
                    text-decoration: none;
                    color: #ff0000;
                    padding-top: 6.5px;
                }
            }
        }
    }
}
.cell-ps {
    width: 345px;
    padding-left: 5px;
    padding-right: 10px;
    display: flex;
    .cell-msg {
        width: 100%;
        color: #333333;
        font-size: 13px;
        display: flex;
        margin: 8px 0;
    }
    span {
        display: inline;
        visibility: hidden;
    }
    .be-careful {
        color: $btn-color;
        font-size: 14px;
        display: flex;
    }
}
.inp-bg1 {
    // background-color: #ffffff;
}
.inp-bg2 {
    // background-color: #eaeaea6b;
    box-shadow: 0px 0px 5px 0 #ff8200;
}
.venicle-btn {
    .venicle-btn-view {
        height: 64px;
        // width: 100vw;
        // background-color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        .venicle-btn-text {
            line-height: 44px;
            width: 100%;
            background-color: $btn-color;
            border-radius: 5px;
            color: #ffffff;
            text-align: center;
            font-weight: 700;
        }
    }
    .btn-text-sp {
        width: 100vw;
        background-color: #ffffff;
        height: env(safe-area-inset-bottom);
    }
}

.documentType {
    width: 70%;
    display: flex;
    justify-content: space-between;
}
</style>
