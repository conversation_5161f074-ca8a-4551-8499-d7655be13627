import layer from '@/s-kit/js/layer';
import post from '@/s-kit/js/v3-http/post';

export default {
    // #ifdef MP-ALIPAY
    mounted() {},
    methods: {
        // 调用预授权下单插件
        async preLicensingPlaceAnOrder(dataInfo, isAuth = false) {
            /**
             * 发起预授权下单接口
             * @param areaCode 地区编码
             * @param bizOrderNo 业务订单编号
             * @param stationCode 站编码
             * @param extendFiled 风控字段
             * @param accountPreType  账户预支付场景：1.扫码支付；2.室外支付；3.支付预授权
             * @param amount 需要冻结的金额
             */
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: dataInfo.preAuthzOrderNo,
                stationCode: this.selectMarkerV3.orgCode,
                extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                // extendFiled: JSON.stringify({
                //     dfp: '',
                //     gps:
                //         this.riskManagementLonV3 && this.riskManagementLatV3
                //             ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                //             : '',
                //     gpsProvince: '',
                //     gpsCity: '',
                //     gpsArea: '',
                // }),
                accountPreType: '3',
                amount: this.preaAuthNumber,
                accountNo: this.walletInfo.ewalletNo,
            };
            console.log(params, '插件加油预下单请求参数');
            console.log(this.accountDataPlugin, '插件实例');
            uni.hideLoading();
            await this.$sKit.aliPayPlugin.QryPreOrder(params, this.accountDataPlugin).then(async res => {
                console.log(res, '插件加油预下单');
                setTimeout(() => {
                    this.isPreaAuthing = false;
                }, 500);
                // PAY_ERROR_004 新增错误码，代表不是正在意义上的失败，可能是超时导致的，这种情况处理逻辑跟成功一样，跳转页面查询授权码，查不到再取消订单
                if (res.code === 'PAY_SUCCESS' || res.code === 'PAY_ERROR_004') {
                    // 获取电子钱包金额
                    this.$store.dispatch('getAccountBalanceAction');
                    uni.showLoading({
                        title: '加载中',
                        mask: true,
                    });
                    //成功要跳转
                    let URL = `/packages/third-oil-charge-payment/pages/authorization-code/main`;
                    let type = 'navigateTo';
                    this.$sKit.layer.useRouter(URL, { ...dataInfo, refer: this.refer }, type);
                    uni.setStorageSync('Define_PreAuth_Price', this.preaAuthNumber);
                    uni.setStorageSync('Define_PreAuth_Fuel', encodeURIComponent(JSON.stringify(this.seletFuel)));
                    uni.hideLoading();
                } else if (res.code === 'PAY_ERROR_003') {
                    // need risk
                    this.isCanClickPay = true;
                    this.isPaying = false;
                    //需要实人认证
                    const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (riskRes == 13) {
                        // 打开实人认证的表单弹窗
                        this.realNameDialogFlag = true;
                    }
                } else {
                    this.$refs['popDialogFlag'].open();
                    this.oilDialogFlagType = 'cipherDialogFlag';
                    this.confirmText = '确认';
                    this.cancelText = '';
                    let errIndex = res.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        this.oilDialogCode = res.msg.slice(0, errIndex);
                        this.oilDialogtitle = res.msg.slice(errIndex + 1, res.msg.length);
                    } else {
                        this.oilDialogtitle = res.msg;
                        this.oilDialogCode = res.code;
                    }
                    this.cancelAction(dataInfo);
                }
            });
        },
    },
    // #endif
};
