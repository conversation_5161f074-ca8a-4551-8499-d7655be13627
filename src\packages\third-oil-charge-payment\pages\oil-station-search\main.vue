<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" :title="openingAServiceStation ? '网点列表' : '网点导航'" :border-bottom="false"></zj-navbar>
            <div class="content fl-column f-1 ov-hid">
                <div class="fl-row">
                    <div style="width: 82%">
                        <searchText @change="handleInput"></searchText>
                    </div>
                    <div class="fl-row fl-al-cen" style="width: 18%">
                        <tags @openScreen="openScreen"></tags>
                    </div>
                </div>

                <div class="f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="list"
                        :emptyImage="require('../../image/kt6wd.png')"
                        :emptyText="productList.length > 0 ? '' : '暂未查询到网点'"
                        :showEmpty="showEmpty"
                        @scrolltolower="scrolltolower"
                        @refreshPullDown="refreshPullDown"
                    >
                        <div class="cells-content">
                            <searchCell
                                v-for="(item, index) in productList"
                                :item="item"
                                :curIndex="index"
                                :key="index"
                                @cellClick="cellClick(item)"
                            ></searchCell>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <ZjPopup ref="screenPopup" :maskClick="true" @maskClick="maskClose" type="bottom">
                <div class="model-div fl-column">
                    <div class="fl-row fl-jus-bet fl-al-cen marlr24">
                        <div class="font-17 weight-500 color-333">网点筛选</div>
                        <div class="close" @click.stop="closeScreenPop">
                            <img src="../../image/close.png" alt />
                        </div>
                    </div>
                    <div class="f-1 mh-0">
                        <div class="fl-column padtb16">
                            <div class="font-14 weight-bold popup-label-title">加油</div>
                            <div class="popup-center">
                                <div
                                    class="popup-label bg-EFEDED"
                                    :class="{ isSelect: refuelIndex == index }"
                                    v-for="(item, index) in stationTagsObj.refuelArr"
                                    :key="index"
                                    @click="selectActionTag(item, index, 'jy')"
                                >
                                    {{ item.name }}
                                </div>
                            </div>
                            <div class="font-14 weight-bold popup-label-title">加气</div>
                            <div class="popup-center">
                                <div
                                    class="popup-label bg-EFEDED"
                                    :class="{ isSelect: aerateIndex == index }"
                                    v-for="(item, index) in stationTagsObj.aerateArr"
                                    :key="index"
                                    @click="selectActionTag(item, index, 'jq')"
                                >
                                    {{ item.name }}
                                </div>
                            </div>
                            <div class="font-14 weight-bold popup-label-title">便利店</div>
                            <div class="popup-center">
                                <div
                                    class="popup-label bg-EFEDED"
                                    :class="{ isSelect: minimartIndex == index }"
                                    v-for="(item, index) in stationTagsObj.minimartArr"
                                    :key="index"
                                    @click="selectActionTag(item, index, 'bld')"
                                >
                                    {{ item.name }}
                                </div>
                            </div>
                            <div class="font-14 weight-bold popup-label-title">新能源</div>
                            <div class="popup-center">
                                <div
                                    class="popup-label bg-EFEDED"
                                    :class="{ isSelect: newEnergyIndex == index }"
                                    v-for="(item, index) in stationTagsObj.newEnergyArr"
                                    :key="index"
                                    @click="selectActionTag(item, index, 'xny')"
                                >
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                        <div class="fl-row fl-jus-cen">
                            <div class="btn-plain submit-btn marright color-E64F22" @click="resetHandle">重置</div>
                            <div class="primary-btn submit-btn color-fff" @click="submitHandle">确定</div>
                        </div>
                    </div>
                </div>
            </ZjPopup>
            <zj-show-modal ref="modalScreen"></zj-show-modal>
        </div>
    </div>
</template>

<script>
const PAGE_SIZE = 10; // 每页多少个
const THROTTLE_TIME = 500;
import tags from '../../components/search-module/oil-station-search-tags.vue';
import searchCell from '../../components/search-module/oil-station-search-cell.vue';
import searchText from '../../components/search-module/search-text.vue';
import { stationListApi, stationServiceLevelListApi, getMarketingRec } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { clientCode } from '../../../../../project.config';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            inputFlag: false, //输入框控制
            curCityName: '北京',
            searchText: '',
            productList: [],
            addressList: [],
            searchLocation: '',
            // 页码
            page: 1,
            // 加载状态 // 0 不显示 1 显示加载动画 2 显示没有更多
            // loadState: 0,
            showEmpty: false,
            // 开通电子账户页面携带过来的参数
            openingAServiceStation: '',
            source: '', //页面跳转来源
            callPath: '', //选择后回调app原生用到
            destinationLocationInfo: {},
            tagsItem: [],
            first: true,
            refuelIndex: -1, // 选中的加油标签下标
            refuelData: null, // 选中的加油标签数据
            aerateIndex: -1, // 选中的加气标签下标
            aerateData: null, // 选中的加气标签数据
            minimartIndex: -1, // 选中的便利店标签下标
            minimartData: null, // 选中的便利店标签数据
            newEnergyIndex: -1, // 选中的新能源标签下标
            newEnergyData: null, // 选中的新能源标签数据
            isSelectOpen: false, //点击打开筛选框时,不触发数据对比
        };
    },
    onLoad(option) {
        this.productList = this.showMarkerArrV3_app;
        if (this.productList.length > 0) {
            this.$nextTick(() => {
                this.$refs.list.loadStatus = 'contentdown';
            });
        }
        // 开通电子账户页面携带过来的参数
        if (option && option.data) {
            this.openingAServiceStation = JSON.parse(decodeURIComponent(option.data));
        }
    },
    onShow() {},
    mounted() {
        // 获取油站标识列表
        this.stationServiceListPost();
    },
    methods: {
        /**
         * @description  : 获取油站标识列表
         * @return        {*}
         */
        async stationServiceListPost() {
            if (this.stationTagsArr.length > 0 && this.stationTagsObj) {
                this.selectAction(this.stationTagsArr[0].chidrenServices[0], true);
            } else {
                let resData = await stationServiceLevelListApi({}, { isload: false });
                if (resData.success) {
                    let stationTagsArr = resData.data;
                    let stationTagsObj = {
                        refuelArr: [],
                        aerateArr: [],
                        minimartArr: [],
                        newEnergyArr: [],
                    };
                    stationTagsArr.forEach(item => {
                        if (item.name == '加油') {
                            // 不能删掉，前端固定
                            let str98 = [{ name: '98#', code: '14' }];
                            stationTagsObj.refuelArr = item.chidrenServices.concat(str98);
                        }
                        if (item.name == '加气') {
                            stationTagsObj.aerateArr = item.chidrenServices;
                        }
                        if (item.name == '便利店') {
                            stationTagsObj.minimartArr = item.chidrenServices;
                        }
                        if (item.name == '新能源') {
                            stationTagsObj.newEnergyArr = item.chidrenServices;
                        }
                    });
                    this.$store.commit('setStationTags', { stationTagsArr: stationTagsArr, stationTagsObj: stationTagsObj });
                    // 初始加载导航列表数据
                    if (stationTagsArr.length > 0) {
                        if (stationTagsArr[0].chidrenServices.length > 0) {
                            this.selectAction(stationTagsArr[0].chidrenServices[0], true);
                        } else {
                            this.selectAction([], true);
                        }
                    } else {
                        this.selectAction([], true);
                    }
                } else {
                    this.selectAction([], true);
                }
            }
        },
        /**
         * @description  : 选择的数据
         * @return        {*}
         */
        async selectActionTag(item, index, type) {
            if (type == 'jy') {
                if (this.refuelIndex == index) {
                    this.refuelIndex = -1;
                    this.refuelData = null;
                } else {
                    this.refuelIndex = index;
                    this.refuelData = item;
                }
            }
            if (type == 'jq') {
                if (this.aerateIndex == index) {
                    this.aerateIndex = -1;
                    this.aerateData = null;
                } else {
                    this.aerateIndex = index;
                    this.aerateData = item;
                }
            }
            if (type == 'bld') {
                if (this.minimartIndex == index) {
                    this.minimartIndex = -1;
                    this.minimartData = null;
                } else {
                    this.minimartIndex = index;
                    this.minimartData = item;
                }
            }
            if (type == 'xny') {
                if (this.newEnergyIndex == index) {
                    this.newEnergyIndex = -1;
                    this.newEnergyData = null;
                } else {
                    this.newEnergyIndex = index;
                    this.newEnergyData = item;
                }
            }
        },
        // 打开筛选框
        openScreen() {
            this.$refs.screenPopup.open();
            this.isSelectOpen = true;
        },
        /**
         * @description  : 拼接标签字符串
         * @param         {*} str:标签名
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        /**
         * @description  : 点击遮罩关闭弹窗触发事件
         * @return        {*}
         */
        maskClose() {},
        // 关闭使用规则
        closeScreenPop() {
            this.$refs.screenPopup.close();
        },
        // 提交
        async submitHandle() {
            let setAry = [];
            if (this.refuelData) {
                setAry.push(this.refuelData);
            }
            if (this.aerateData) {
                setAry.push(this.aerateData);
            }
            if (this.minimartData) {
                setAry.push(this.minimartData);
            }
            if (this.newEnergyData) {
                setAry.push(this.newEnergyData);
            }
            await this.selectAction(setAry);
            this.$refs.screenPopup.close();
        },
        // 重置
        resetHandle() {
            // 重置选中的状态
            this.refuelIndex = -1;
            this.aerateIndex = -1;
            this.minimartIndex = -1;
            this.newEnergyIndex = -1;
            // 重置选中的数据
            this.refuelData = null;
            this.aerateData = null;
            this.minimartData = null;
            this.newEnergyData = null;
        },
        /**
         * @description  : 初始化位置信息，获取油站列表，不改变全局油站列表和选中油站
         * @return        {*}
         */
        init(isSameArr) {
            this.$store.dispatch('initLocationV3_app', {
                callback: res => {
                    this.getGoodsListFun({ isInit: true, isSameArr: isSameArr }, res);
                },
                type: 'onlyLocation',
            });
        },
        /**
         * @description  : 获取油站列表
         * @param         {*} item:stationService油站类型
         * @return        {*}
         */
        selectAction(item = [], isSameArr = false) {
            this.tagsItem = item;
            if (this.first) {
                this.init(isSameArr);
                this.first = false;
            } else {
                this.getGoodsListFun({ isInit: true, isSameArr: isSameArr });
            }
        },
        /**
         * @description  : 下拉刷新触发
         * @return        {*}
         */
        refreshPullDown() {
            this.page = 1;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  : 上拉加载事件
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.list.loadStatus !== 'nomore') {
                this.$refs.list.loadStatus = 'loading';
                this.page + 1;
                this.getGoodsListFun();
            }
        },
        /**
         * @description  : 搜索事件
         * @param         {*} data:输入信息
         * @return        {*}
         */
        handleInput(data) {
            this.destinationLocationInfo = data;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  :选择油站
         * @param         {*} item:油站数据
         * @return        {*}
         */
        async cellClick(item) {
            let centerParam = {
                marker: item,
                upLocation: true,
                oilChangeStation: true,
                refer: this.openingAServiceStation.refer || '',
                bizSeed: this.openingAServiceStation.bizSeed || '',
                bizTitle: this.openingAServiceStation.bizTitle || '',
            };
            // 开通昆仑e享卡页面传进来的标识
            if (this.openingAServiceStation.type) {
                let url = '';
                let params = {};
                let type = 'navigateBack';
                let prevParams = {
                    // 得到上一页面的实例需要传2 若要返回上上页面的实例就传3，以此类推
                    delta: 2,
                    // 返回指定页面的data中的标识，用于在调用接口成功后返回该页面时候，调用指定的函数或更改要返回页面中定义的属性值
                    refreshListFlag: true,
                    stationParam: item,
                };
                this.$sKit.layer.useRouter(url, params, type, prevParams);
                return;
            }
            // if(this.bookingRefueling == 1){

            // }else{
            this.$store.dispatch('setSelectMarkerToMapCenterV3', centerParam);
            console.log(centerParam, '更换油站=====');
            // }
            // #ifdef MP-MPAAS
            this.$cnpcBridge.setValueToNative('Define_Selected_Station', encodeURIComponent(JSON.stringify(item)));
            // #endif
            this.closeEvent();
        },
        /**
         * @description  : 关闭小程序
         * @return        {*}
         */
        closeEvent() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        /**
         *  JavaScript 中判断两个数组的数据是否相同
         * */
        areArraysEqual(arr1, arr2) {
            if (arr1.length > 0 && arr2.length > 0) {
                return arr1[0].orgCode === arr2[0].orgCode && arr1[0].distance === arr2[0].distance;
            }
            return false;
        },
        /**
         * @description  : 获取油站列表
         * @param         {*} isInit: 是否重置油站列表
         * @param         {*} item: 油站类型入参
         * @param         {*} destinationLocationInfo:搜索内容
         * @return        {*}
         */
        async getGoodsListFun({ isInit = false, destinationLocationInfo, isSameArr = false } = {}, longitudeAndLatitude) {
            if (isInit) {
                Object.assign(this, {
                    productList: isSameArr ? (this.productList.length > 0 ? this.productList : []) : [],
                    page: 1,
                });
            }
            let { page, productList } = this;
            //TODO
            let destinationLongitude = '';
            let destinationLatitude = '';
            if (this.destinationLocationInfo && this.destinationLocationInfo.location) {
                if (this.destinationLocationInfo.location.lng) {
                    destinationLongitude = this.destinationLocationInfo.location.lng;
                }
                if (this.destinationLocationInfo.location.lat) {
                    destinationLatitude = this.destinationLocationInfo.location.lat;
                }
            }
            let tagsCodeServices = [];
            if (this.tagsItem.length > 0) {
                this.tagsItem.forEach(item => {
                    if (item.name != '98#') {
                        tagsCodeServices.push(item.code);
                    }
                });
            }
            let params = {
                bookingRefueling: '',
                distance: '10',
                latitude: this.latV3 || longitudeAndLatitude?.latitude,
                longitude: this.lonV3 || longitudeAndLatitude?.longitude,
                pageNum: page,
                pageSize: PAGE_SIZE,
                // stationService: '10',
                stationServices: tagsCodeServices,
                destinationLongitude: destinationLongitude,
                destinationLatitude: destinationLatitude,
                mapType: '1', //地图坐标系标识（0高德，1百度，2腾讯）
                orgCode: this.walletInfo.addressNo || '',
            };
            if (this.tagsItem.length > 0) {
                this.tagsItem.forEach(item => {
                    if (item.name == '98#') {
                        params.fuelType = item.code;
                    }
                });
            }
            let { data } = await stationListApi(params, { isload: false });
            let arr = this.$sKit.layer.filterLocations(data.rows);
            let dataRows = arr.map(item => {
                let strArr = item.businessHoursList.map(time => {
                    return time.startTime.substring(0, 5) + '-' + time.endTime.substring(0, 5);
                });
                item.businessHoursStr = strArr.join(' ');
                return item;
            });
            this.$refs.list.pullDownHeight = 0;
            this.$refs.list.pullingDown = false;
            // 判断选择框是否打开
            if (isSameArr && productList.length > 0) {
                const isSame = this.areArraysEqual(this.showMarkerArrV3_app, dataRows);
                console.log(isSame, 'result一样吗?');
                if (isSame) {
                    console.log('相同走这里');
                } else {
                    console.log('不相同走这里');
                    productList = dataRows || [];
                }
            } else {
                productList = productList.concat(dataRows || []);
            }

            if (data && page >= data.pageSum) {
                this.$refs.list.loadStatus = 'nomore';
            } else {
                this.$refs.list.loadStatus = 'contentdown';
            }
            Object.assign(this, {
                productList,
                page: Number(page) + 1,
            });
            this.showEmpty = this.productList.length <= 0 ? true : false;
            // #ifdef MP-MPAAS
            let newArr = await this.getMarketingAssistant(this.productList);
            if (newArr.length > 0) {
                this.productList = newArr;
            }
            // #endif
        },
        async getMarketingAssistant(stationList) {
            return new Promise(async (resolve, reject) => {
                let copyStationList = JSON.parse(JSON.stringify(stationList));
                let stationCodeList = copyStationList.map(item => {
                    return item.orgCode;
                });
                let newStationCodeList = await this.$sKit.layer.getMarketingJudgment(stationCodeList);
                if (newStationCodeList.length > 0) {
                    let params = {
                        stationCodeList: newStationCodeList,
                        sceneType: 0,
                    };
                    let res = await getMarketingRec(params, { isload: false, isCustomErr: true });
                    if (res && res.success) {
                        let marketingList = res.data || [];
                        if (marketingList.length > 0) {
                            let map = new Map();
                            let newArr = [];
                            let bizContent = [];
                            // 将arr1中的元素以id为键存储在Map中
                            for (let j = 0; j < marketingList.length; j++) {
                                if (!map.get(marketingList[j].stationCode)) {
                                    bizContent.push({
                                        stationcode: marketingList[j].stationCode,
                                        scenetype: marketingList[j].sceneType,
                                        marketingCode: marketingList[j].activityId,
                                        marketingContent: marketingList[j].aiGeneratedCopy,
                                    });
                                    map.set(marketingList[j].stationCode, marketingList[j].aiGeneratedCopy);
                                }
                            }
                            // 遍历arr2，在Map中查找具有相同id的元素
                            for (let i = 0; i < copyStationList.length; i++) {
                                let elementFromMap = map.get(copyStationList[i].orgCode);
                                newArr.push({
                                    ...copyStationList[i],
                                    marketingAssistant: elementFromMap || '',
                                });
                            }
                            bizContent = JSON.stringify(bizContent).replace(/,/g, ' ');
                            this.$sKit.mpBP.tracker('智能营销文档曝光', {
                                seed: 'smart_marketing',
                                pageID: 'branchesPage', // 返回sdk标识
                                refer: this.openingAServiceStation.refer,
                                channelID: clientCode,
                                dateType: 'exposure',
                                content: bizContent,
                            });
                            resolve(newArr);
                        } else {
                            resolve(stationList);
                        }
                    } else {
                        resolve(stationList);
                    }
                } else {
                    resolve(stationList);
                }
            });
        },
    },
    computed: {
        ...mapGetters(['showMarkerArrV3_app', 'latV3', 'lonV3', 'walletInfo']),
        ...mapState({
            stationTagsArr: state => state.thirdIndex.stationTagsArr,
            stationTagsObj: state => state.thirdIndex.stationTagsObj,
        }),
    },
    components: {
        tags,
        searchCell,
        searchText,
        ZjPopup,
    },
};
</script>
<style scoped lang="scss">
.content {
    padding: 0 16px;
}

.model-div {
    background: #ffffff;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 20px;
    overflow: auto;

    .marlr24 {
        margin: 0 16px 0 16px;
    }

    .padtb16 {
        padding-top: 16px;
        // height: 330px;
        overflow: hidden;
        overflow-x: hidden;
        overflow-y: scroll;
    }

    .popup-label-title {
        margin-left: 18px;
    }

    .popup-center {
        display: flex;
        flex-wrap: wrap;
        padding: 10px 12px;

        .popup-label {
            width: 77px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-size: 12px;
            padding: 0px 6px;
            margin: 0 5px 0 5px;
            text-align: center;
            margin-bottom: 10px;
        }

        .isSelect {
            background: #ffe9e3;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }

    .submit-btn {
        font-size: 15px;
        padding: 10px 65px;
        border-radius: 6px;
    }

    .marright {
        margin-right: 18px;
    }

    .close {
        padding: 20px 3px;

        img {
            width: 13px;
            height: 13px;
        }
    }
}
</style>
