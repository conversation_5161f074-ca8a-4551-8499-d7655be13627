<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="overall p-bf bg-F7F7FB fl-column">
            <zj-navbar :height="44" title="我的实体卡"></zj-navbar>
            <div class="f-1" v-if="!showEmpty">
                <div class="title bg-FFF7DC font-14px color-333 weight-400">下方为系统查询到您名下的加油卡，点击绑定进行添加</div>
                <div class="card-wrap p-LR-16">
                    <div
                        class="card-item fl-row bg-fff p-LR-16 fl-al-cen fl-jus-bet p-bf border-rad-8"
                        v-for="(item, index) in listOfPreviouslyUsedCards"
                        :key="index"
                    >
                        <div class="item-left fl-column fl-jus-bet">
                            <div class="font-16 color-222 weight-500">{{ getCardNo(item.cardNo) }}</div>
                            <div class="font-12 color-333 weight-400">{{ item.address }}</div>
                        </div>
                        <div class="item-right te-center" :class="item.status == 0 ? bound : unbound" @click="binding(item)">{{
                            item.status === 0 ? '绑定' : '已绑定'
                        }}</div>
                    </div>
                </div>
            </div>
            <div class="f-1" v-if="showEmpty">
                <zj-no-data :emptyImage="emptyImage" emptyText="暂未查询到您的历史绑卡信息"></zj-no-data>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { unbindCardList, fastBindCard } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { mapState, mapGetters } from 'vuex';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: '',
    data() {
        return {
            // 未绑定加油卡列表
            listOfPreviouslyUsedCards: [],
            // 是否展示空态
            showEmpty: false,
            // 空态图片
            emptyImage: require('../../images/kt7yk.png'),
            refer: '',
        };
    },
    onLoad(options) {
        // 获取未绑定加油卡列表
        this.getCardUnbindCardList();
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) this.refer = params.refer;
        }
        this.$sKit.mpBP.tracker('绑定实体卡', {
            seed: 'bindCardBiz',
            pageID: 'zykBindListPage',
            refer: this.refer,
            channelID: clientCode,
        });
    },
    computed: {
        /**
         * @description  : 绑定卡的按钮样式
         * @return        {*}
         */
        bound() {
            return 'border-FF431E font-14 weight-500 color-E64F22 border-rad-4';
        },
        /**
         * @description  : 未绑定卡按钮的样式
         * @return        {*}
         */
        unbound() {
            return 'border-opcity bg-EDEDF5 border-rad-4 font-14 weight-400 color-666';
        },
    },
    methods: {
        /**
         * @description  : 获取未绑定加油卡列表
         * @param         {Boolean} showEmpty -是否展示空态
         * @return        {*}
         */
        async getCardUnbindCardList() {
            let res = await unbindCardList();
            if (res.success) {
                if (JSON.stringify(res.data) !== '[]') {
                    res.data.map(item => {
                        // 等于0 代表可以绑定
                        item.status = 0;
                    });
                    // 未绑定加油卡列表
                    this.listOfPreviouslyUsedCards = res.data;
                } else {
                    // this.loading = true;
                    this.showEmpty = true;
                }
            }
        },
        /**
         * @description  : 绑定加油卡
         * @param         {*}cardNo
         * @param        {Boolean} refreshCardListFlag -本地存储，返回上一级页面做刷新数据标识
         * @return        {*}
         */
        async binding(item) {
            let params = {
                cardNo: item.cardNo,
            };
            let res = await fastBindCard(params);
            if (res.success) {
                item.status = 1;
                // 获取电子券、余额、积分、能源币、油卡
                // this.$store.dispatch("getBindCardInfo", {});
                this.$store.dispatch('basicCouponAction');
                uni.showToast({
                    title: '绑定成功',
                    duration: 2000,
                });
                this.$sKit.mpBP.tracker('绑定实体卡', {
                    seed: 'bindCardBiz',
                    pageID: 'bindSuccessToast',
                    refer: this.refer,
                    channelID: clientCode,
                });
                uni.setStorageSync('refreshCardListFlag', true);
                //  改成同步存储试试
                // uni.setStorage('refreshCardListFlag', true)
                uni.navigateBack({ delta: 2 });
            }
        },
        /**
         * @description  : 将卡号中的空格去除，并在每4个字符之后插入一个空格。
         * @return        {*}
         */
        getCardNo(str) {
            return str.replace(/\s/g, '').replace(/(.{4})/g, '$1 ');
        },
    },
    components: {
        // ZjNoData,
    },
};
</script>
<style scoped lang="scss">
.overall {
    .title {
        width: 100%;
        height: 36px;
        line-height: 36px;
        margin-bottom: 16px;
        padding-left: 16px;
    }

    .card-wrap {
        width: 100%;
        height: 77px;

        .card-item {
            margin-bottom: 12px;

            .item-left {
                // width: 165px;
                height: 47px;
            }

            .item-right {
                width: 74px;
                height: 32px;
                line-height: 32px;
            }
        }
    }

    .ZjNoData {
        flex: 1;
    }
}
</style>
