<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas bg-fff">
        <!-- 修改登录密码 -->

        <div>
            <zj-navbar :height="44" title="修改登录密码"></zj-navbar>
            <div style="padding: 16px">
                <div class="form">
                    <div class="input">
                        <input
                            :password="!showImg2"
                            placeholder="请输入新密码（6~20位字母数字组合）"
                            placeholder-style="font-size: 14px;color: #999999;"
                            v-model.trim="userPwd"
                            :maxlength="20"
                            :minlength="6"
                        />
                        <div class="eyeImg_div" @click="showImg2 = !showImg2">
                            <img v-if="!showImg2" src="../../image/close_img.png" alt />
                            <img v-else src="../../image/eyes_open.png" alt />
                        </div>
                    </div>

                    <div class="input">
                        <input
                            :password="!showImg3"
                            placeholder="请确认新密码"
                            placeholder-style="font-size: 14px;color: #999999;"
                            v-model.trim="sureuserPwd"
                            :maxlength="20"
                            :minlength="6"
                        />
                        <div class="eyeImg_div" @click="showImg3 = !showImg3">
                            <img v-if="!showImg3" src="../../image/close_img.png" alt />
                            <img v-else src="../../image/eyes_open.png" alt />
                        </div>
                    </div>
                </div>
                <div class="btn primary-btn" @click="complete()">确认修改</div>
            </div>

            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { setPasswordByLogin } from '../../../../s-kit/js/v3-http/https3/user.js';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    computed: {},
    data() {
        return {
            msg: 'msg',
            sureuserPwd: '',
            originPwd: '',
            userPwd: '',
            isShowOriginPwd: '',
            token: '',
            showImg1: false,
            showImg2: false,
            showImg3: false,
            queryInfo: {},
        };
    },
    onLoad(option) {
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        console.log(queryInfo, 'queryInfo========');
        this.queryInfo = queryInfo;
    },
    async created() {
        await this.$cnpcBridge.isCutScreen(true);
    },
    mounted() {},
    methods: {
        // 修改登录密码确认
        async complete() {
            if (!this.userPwd) {
                uni.showToast({
                    title: '请输入密码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.sureuserPwd) {
                uni.showToast({
                    title: '请输入确认密码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.$sKit.test.checkPassWord(this.userPwd) || !this.$sKit.test.checkPassWord(this.sureuserPwd)) {
                uni.showToast({
                    title: '密码必须包含数字和字母长度为6~20位',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.sureuserPwd != this.userPwd) {
                uni.showToast({
                    title: '两次输入的密码不一致',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let params = {
                mobile: this.queryInfo.mobile,
                authInfo: this.queryInfo.authInfo,
                password: this.$cnpcUtils.encryption(this.userPwd),
            };
            console.log(params, 'setPasswordByLogin===============');
            let res = await setPasswordByLogin(params);
            if (res.success) {
                this.showToast('修改密码成功');
                setTimeout(() => {
                    if (this.queryInfo.page == 3) {
                        uni.navigateBack({
                            delta: 3, //返回层数，2则上上页
                        });
                    } else {
                        uni.navigateBack({
                            delta: 2, //返回层数，2则上上页
                        });
                    }
                }, 1000);
            }
        },
        //  this.showToastAndLogout('密码修改成功');
        showToast(message, info = '') {
            uni.showToast({
                title: info ? info : message,
                icon: 'none',
                duration: 2000,
            });
        },
        showToastAndLogout(message) {
            this.showToast(message);
            setTimeout(() => {
                this.$sKit.commonUtil.logoutFun();
            }, 3000);
        },
    },
    async beforeDestroy() {
        await this.$cnpcBridge.isCutScreen(false);
    },
    async destroyed() {
        await this.$cnpcBridge.isCutScreen(false);
    },
};
</script>

<style scoped lang="scss">
.form {
    .input {
        height: 44px;
        background: #f7f7fb;
        margin-bottom: 12px;
        padding: 0 12;
        display: flex;
        align-items: center;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        input {
            width: 100%;
            flex: 1;
            background: #f7f7fb;
            height: 44px;
            position: absolute;
            top: 0;
            left: 0;
            padding-left: 15px;
        }

        // z-index 1
        .eyeImg_div {
            height: 44x;
            width: 22px;
            // z-index 2
            position: absolute;
            right: 14px;

            img {
                width: 20px;
                height: 15px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        // div {
        //   width: 94px;
        //   height: 30px;
        //   font-size: 14px;
        //   line-height: 30px;
        //   text-align: center;
        //   font-weight: 400;
        //   color: #e64f22;

        //   border-left: 1px solid #d3d3d3;
        // }
    }
}

.btn {
    height: 44px;
    line-height: 44px;
    color: #fff;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    border-radius: 8px;
}
</style>
