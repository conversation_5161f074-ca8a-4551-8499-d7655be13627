{"name": "petro-soti-hkyz-app-uni", "version": "5.2.1", "release-version": "5.2.1", "private": true, "scripts": {"dev": "npm run dev:mp-weixin", "devhw": "npm run dev:quickapp-webview-huawei", "dev:quickapp-webview-huawei": "node build/pre-build.js", "postdev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "buildhw": "npm run build:quickapp-webview-huawei", "build:quickapp-webview-huawei": "node build/pre-build.js", "postbuild:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build": "npm run build:mp-weixin", "abuild": "npm run build:mp-alipay", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "buildh5": "npm run build:h5", "build:h5": "node build/pre-build.js", "postbuild:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "dbuild": "npm run build:mp-to<PERSON>ao --watch", "build:mp-toutiao": "node build/pre-build.js --watch", "postbuild:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "build:mp-weixin": "node build/pre-build.js --watch", "postbuild:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "node build/pre-build.js", "postdev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "node build/pre-build.js", "postdev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "devd": "npm run dev:mp-to<PERSON>ao --watch", "dev:mp-toutiao": "node build/pre-build.js --watch", "postdev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "node build/pre-build.js --watch", "postdev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "dev:wx": "cross-env UNI_PLATFORM=mp-weixin node build/pre-build.js --watch", "postdev:wx": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:wx": "cross-env UNI_PLATFORM=mp-weixin node build/pre-build.js --watch --minimize", "postbuild:wx": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build && node build/script/removeStatic.js --watch", "dev:al": "cross-env UNI_PLATFORM=mp-alipay node build/pre-build.js", "postdev:al": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "build:al": "cross-env UNI_PLATFORM=mp-alipay node build/pre-build.js", "postbuild:al": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build && node build/script/removeStatic.js", "dev:sub": "node build/pre-build.js", "sub:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "postdev:sub": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay npm run sub:mp-alipay -- --subpackage=mpaas", "build:sub": "node build/pre-build.js", "postbuild:sub": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay npm run build:mp-alipay -- --subpackage=mpaas", "dev:mpaas": "cross-env UNI_PLATFORM=mp-mpaas node build/pre-build.js --watch", "postdev:mpaas": "npm run dev:custom mp-mpaas --watch", "build:mpaas": "cross-env UNI_PLATFORM=mp-mpaas node build/pre-build.js --no-clean --mode production", "postbuild:mpaas": "npm run build:custom mp-mpaas --watch", "dev:harmony": "cross-env UNI_PLATFORM=harmony node build/pre-build.js --watch", "postdev:harmony": "npm run dev:custom mp-harmony --watch", "build:harmony": "cross-env UNI_PLATFORM=harmony node build/pre-build.js --no-clean --mode production", "postbuild:harmony": "npm run build:custom mp-harmony --watch", "develop:mpaas": "npm run dev:mpaas cnpc-fuel-mini-sit", "develop:al:sit": "npm run dev:al cnpc-zfb-sit", "develop:al:dev": "npm run dev:al cnpc-zfb-dev", "develop:wx": "npm run dev:wx cnpc-wx-sit", "dev:cloud": "cross-env NODE_ENV=development cross-env UNI_PLATFORM=cloud node build/pre-build.js --watch", "postdev:cloud": "npm run dev:custom h5-cloud --watch", "build:cloud": "cross-env UNI_PLATFORM=cloud node build/pre-build.js --no-clean  --mode production", "postbuild:cloud": "npm run build:custom h5-cloud && node build/script/clean.js && node build/script/compressing.js --watch"}, "dependencies": {"@alipay-inc/mpaas-mcdp-wx-render": "0.0.3-alpha.19", "@alipay-inc/mpaas-miniapp-analytics": "^1.1.1-alpha.20", "@dcloudio/uni-app-plus": "^2.0.2-3090820231124001", "@dcloudio/uni-h5": "^2.0.2-3090820231124001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-i18n": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-360": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-alipay": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-baidu": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-jd": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-lark": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-qq": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-vue": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-weixin": "^2.0.2-3090820231124001", "@dcloudio/uni-quickapp-native": "^2.0.2-3090820231124001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3090820231124001", "@dcloudio/uni-stat": "^2.0.2-3090820231124001", "@petro-gsms/paysdk-js": "^1.0.3-20250509T03461", "@qiun/ucharts": "^2.5.0-20230101", "@vue/shared": "^3.2.31", "compressing": "^1.10.1", "crypto-js": "^4.1.1", "currency.js": "^2.0.4", "flyio": "^0.6.14", "gio-miniprogram-sdk-saas": "^3.8.12", "iconv-lite": "^0.6.3", "miniprogram-sm-crypto": "^0.3.13", "mp-html": "^2.4.2", "qrcode": "1.5.3", "qs": "^6.5.3", "regenerator-runtime": "^0.12.1", "rimraf": "^6.0.1", "subpackage-optimize": "^1.0.4", "uglifyjs-webpack-plugin": "^2.2.0", "umtrack-wx": "^2.4.11", "uni-simple-router": "1.5.5", "uniapp-qrcode": "^1.0.2", "uview-ui": "1.8.8", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2", "vuex": "^3.6.2", "wxbarcode": "^1.0.2"}, "devDependencies": {"@dcloudio/types": "^2.0.4", "@dcloudio/uni-automator": "^2.0.2-3090820231124001", "@dcloudio/uni-cli-i18n": "^2.0.2-3090820231124001", "@dcloudio/uni-cli-shared": "^2.0.2-3090820231124001", "@dcloudio/uni-migration": "^2.0.2-3090820231124001", "@dcloudio/uni-template-compiler": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3090820231124001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3090820231124001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3090820231124001", "@types/node": "^17.0.34", "@vue/cli-plugin-babel": "^4.5.2", "@vue/cli-service": "^4.5.2", "autoprefixer": "^9.8.6", "babel-plugin-import": "^1.13.3", "core-js": "^3.6.5", "cross-env": "^7.0.3", "i": "^0.3.6", "image-tools": "^1.4.0", "jest": "^25.5.4", "lodash": "^4.17.21", "mini-types": "^0.1.4", "miniprogram-api-typings": "^3.0.1", "path": "^0.12.7", "postcss-comment": "^2.0.0", "postcss-px-to-viewport": "^1.1.1", "postcss-px2rem": "^0.3.0", "prettier": "^2.8.8", "px2rpx-loader": "^0.1.10", "rimraf": "^3.0.2", "sass": "1.62.0", "sass-loader": "^7.3.1", "string-replace-loader": "^2.3.0", "style-resources-loader": "^1.3.3", "stylus": "^0.54.8", "stylus-loader": "^3.0.2", "ts-loader": "^4.5.0", "tslib": "^2.4.0", "tslint": "^5.20.1", "tslint-config-standard": "^9.0.0", "tslint-loader": "^3.5.4", "typescript": "^3.9.10", "vue-template-compiler": "^2.6.11"}, "license": "", "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {"mp-mpaas": {"title": "m<PERSON><PERSON><PERSON>小程序", "BROWSER": "", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-MPAAS": true, "MP-ALIPAY": false}}, "h5-cloud": {"title": "H5页面", "BROWSER": "chrome", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-CLOUD": true, "H5": false}}}}}