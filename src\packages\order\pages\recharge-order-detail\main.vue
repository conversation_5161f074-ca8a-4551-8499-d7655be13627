<template>
    <div class="detail-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="订单详情"
            :back-text-style="pageConfig.titleStyle"
            :custom-back="clickCustomBackBtn"
            :border-bottom="false"
        ></u-navbar>
        <div class="order-detail">
            <div class="head">
                <img class="logo" src="@/static/rechargeSuccess.png" alt />
                <div class="order_name">充值成功</div>
                <div class="order-money">
                    <div>￥</div>
                    <div>
                        <my-price color="#FF8200" :price="details.amount"></my-price>
                    </div>
                </div>
            </div>
            <div class="order-info">
                <div class="details flex justify-between">
                    <div>订单号</div>
                    <div>{{ details.orderId }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>加油卡号</div>
                    <div>{{ details.cardNo }}</div>
                </div>
                <div class="details flex justify-between">
                    <div>充值时间</div>
                    <div>{{ details.tradeTime }}</div>
                </div>
            </div>
        </div>
        <div class="invoice-header" v-if="details.invoiceStatus == 1" @click="selectInvoiceTitle">
            <div class="invoice flex justify-between">
                <div class="title">发票抬头</div>
                <div class="flex headName">
                    <div>{{ titleName }}</div>
                    <img class="moreImg" src="@/static/homeIcon/rightjt.png" alt />
                </div>
            </div>
        </div>
        <div v-if="details.invoiceStatus == 1">
            <div class="horn-text-wrap">
                <img class="horn-img" src="@/static/horn.png" alt />
                <div class="horn-text">温馨提示：</div>
            </div>

            <div class="oil-prompt">· 电子发票的销售方名称与您的昆仑加油卡归属地或交易消费加油站主体一致；</div>
        </div>
        <div class="btn" v-if="details.invoiceStatus == 1">
            <div class="btntext" @click="clickKaiPiao">提交</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import myPrice from '@/components/price/price.vue';
import { getInvoiceTitleList } from '@/api/my-center';
import { getRechargeDetailTwo, billingInvoice } from '@/api/home';

export default {
    name: 'orderDetail',
    components: {
        myPrice, //价格
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            details: {}, //订单详情
            titleName: '', //发票抬头
            invoiceDetail: {},
            invoiceStatus: Number,
            type: '', // 开票类型  充值 0  消费 1
        };
    },
    onLoad() {
        if (this.$mp.query.invoiceStatus) {
            this.invoiceStatus = Number(this.$mp.query.invoiceStatus);
        }
        if (this.$mp.query.type) {
            this.type = this.$mp.query.type;
        }
        // 服务通知点击进入传递的参数  orderType=1&id=订单号  不显示开票按钮和抬头
        let orderId = this.$mp.query.id;
        console.log('充值订单id', orderId);
        this.getRoDetail(orderId);
        if (
            !this.$mp.query.hasOwnProperty('orderType') &&
            this.$mp.query.hasOwnProperty('invoiceStatus') &&
            this.$mp.query.invoiceStatus == 1
        ) {
            this.getInvoiceTitle();
        }
        uni.$on('chooseTitle', data => {
            this.titleName = data.invoicetitle;
            this.invoiceDetail = data;
        });
    },
    onShareAppMessage() {},
    onUnload() {
        uni.$off('chooseTitle', this.invoiceDetail);
    },
    methods: {
        async getRoDetail(orderId) {
            let { data } = await getRechargeDetailTwo({ orderId });
            if (this.$mp.query.hasOwnProperty('invoiceStatus')) {
                data.invoiceStatus = this.$mp.query.invoiceStatus;
            }
            if (this.$mp.query.hasOwnProperty('orderType') && this.$mp.query.orderType == 1) {
                data.invoiceStatus = 0;
            }
            this.details = data;
        },
        //获取企业抬头
        async getInvoiceTitle() {
            let res = await getInvoiceTitleList();
            if (res.status == 0 && res.data[0]) {
                this.titleName = res.data[0].invoicetitle;
                this.invoiceDetail = res.data[0]; //企业抬头中第一个企业
            }
        },
        async clickKaiPiao() {
            let selectOrderArr = [this.details];
            if (selectOrderArr[0].invoiceStatus == 2) {
                this.$util.tipsToastNoicon('该订单已经开过发票了');
                return;
            }
            if (selectOrderArr[0].invoiceStatus == 0 || selectOrderArr[0].invoiceStatus == 3 || selectOrderArr[0].invoiceStatus == 4) {
                this.$util.tipsToastNoicon('该订单不可开票');
                return;
            }
            let { details, invoiceDetail, type } = this;
            if (JSON.stringify(invoiceDetail) == '{}') {
                return this.$util.tipsToastNoicon('请先选择发票抬头');
            }
            wx.showLoading({
                title: '加载中...',
                mask: true,
            });
            let params = {
                id: details.orderId,
                invoiceTitleId: invoiceDetail.id,
                type,
                invoicetitle: invoiceDetail.invoicetitle,
                islogo: invoiceDetail.islogo,
                addresstax: invoiceDetail.addresstax || '',
                bankaccount: invoiceDetail.bankaccount || '',
                openingbank: invoiceDetail.openingbank || '',
                taxcode: invoiceDetail.taxcode || '',
                telephone: invoiceDetail.telephone || '',
                businebsscard: invoiceDetail.businebsscard || '',
            };
            let res = await billingInvoice(params);
            if (res.status == 0) {
                wx.hideLoading();
                await this.$util.showModal('开票申请已经提交，稍候您将会收到发票信息，请到我的发票列表查询', true);
                uni.navigateBack();
            } else {
                wx.hideLoading();
                this.$util.showModal(res.info, true);
            }

            /* uni.redirectTo({
          url: '/packages/invoice-center/pages/add-invoice/main?list=' + encodeURIComponent(JSON.stringify(
              selectOrderArr))+'&rechargeOrder=1'+ '&invoiceDetail='+JSON.stringify(this.invoiceDetail)
      }) */
        },
        //选择发票抬头
        selectInvoiceTitle() {
            let url = '/packages/invoice-center/pages/choose-invoice-title/main';
            if (this.invoiceDetail.id) {
                // url = url + '?id=' + this.invoiceDetail.id
                url = url + '?invoiceDetail=' + JSON.stringify(this.invoiceDetail);
            }
            uni.navigateTo({
                url,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}

.flex {
    display: flex;
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.detail-center {
    width: 100%;
    height: 100%;
    background: #f6f6f6;
    .order-detail {
        width: 93%;
        margin: 0 auto;
        border-radius: 4px;
        background-color: #fff;
        margin-top: 18px;
        .head {
            text-align: center;
            .logo {
                width: 45px;
                height: 45px;
                border: 10px solid #fff;
                margin-top: 22px;
                border-radius: 50%;
            }
            .order_name {
                color: #333333;
                font-size: 18px;
                margin-top: 3px;
                font-weight: 500;
            }
            .order-money {
                line-height: 50px;
                font-weight: 500;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                color: #ff8200;
                font-size: 24px;
            }
        }
        .order-info {
            color: #333333;
            font-size: 13px;
            width: 94%;
            margin: 0 auto;
            padding: 12px 0;
            margin-top: 15px;
            border-top: 1px solid #eeeeee;
            .details {
                padding: 6px 0;
            }
        }
    }
    .invoice-header {
        width: 93%;
        margin: 0 auto;
        border-radius: 4px;
        background-color: #fff;
        margin-top: 10px;
        color: #333333;
        .invoice {
            width: 94%;
            padding: 10px 0;
            align-items: center;
            display: flex;
            margin: 0 auto;
            .title {
                font-size: 15px;
                flex: 1;
                font-weight: 700;
            }
            .headName {
                flex: 2;
                text-align: right;
                justify-content: flex-end;
                font-size: 12px;
                .moreImg {
                    width: 7px;
                    height: 12px;
                    margin-left: 10px;
                }
            }
        }
    }
    .horn-text-wrap {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
        margin-top: 10px;
        .horn-text {
            font-size: 28rpx;
            color: #ef8934;
        }
        .horn-img {
            width: 18px;
            height: 16px;
            margin-right: 5px;
        }
    }

    .oil-prompt {
        font-size: 24rpx;
        color: #999999;
        padding-left: 15px;
        padding-right: 15px;
        margin-top: 5px;
    }
    .btn {
        position: fixed;
        left: 0;
        width: 100%;
        bottom: env(safe-area-inset-bottom);
        background-color: #fff;
        text-align: center;
        padding: 9px 0;
        .btntext {
            background-color: #ff8200;
            border-radius: 4px;
            color: #fff;
            font-size: 15px;
            font-weight: 600;
            width: 93%;
            margin: 0 auto;
            padding: 10px 0;
        }
    }
}
</style>
