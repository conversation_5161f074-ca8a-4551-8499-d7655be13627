<template>
    <zj-data-list
        @scrolltolower="uploadMoreEvent"
        ref="dataList"
        @refreshPullDown="refreshPullDown"
        :emptyImage="require('../../image/kt2fp.png')"
        emptyText="您目前还没有开具发票"
        :showEmpty="showEmpty"
    >
        <div class="containerWrap">
            <div class="item" v-for="(item, index) in invoiceList" :key="index">
                <div class="itemBox" @click="invoiceDetail(item)">
                    <div class="itemLeft">
                        <div class="title">{{ item.sellerName }}</div>
                        <div class="name">购买方：{{ item.buyerName }}</div>
                        <div class="time">开票日期：{{ item.issueDate }}</div>
                    </div>
                    <div class="itemRight j-c-e">
                        <div class="amount">&yen;{{ item.taxInclusiveTotalAmount }}</div>
                    </div>
                </div>
                <div class="btns">
                    <div class="btn" @click.stop="invoiceDetail(item)"> 查看发票 </div>
                    <div class="btn btn2" @click.stop="exchangeInvoice(item)" v-if="item.isShowReplace"> 发票抬头换开 </div>
                </div>
            </div>
        </div>
    </zj-data-list>
</template>

<script>
import { invoiceListApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
export default {
    // 发票列表组件
    name: 'invoiceList',
    props: {
        // 父页面传入的第一层tab值
        navActive: {
            default: 1,
            type: Number,
        },
        // 父页面传入的第二层tab值
        secondNavActive: {
            default: '1',
            type: String,
        },
        // 排序筛选项值
        sortType: {
            default: 1,
            type: Number,
        },
        amountRange: {
            type: Object,
            default: {},
        },
        refer: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            //发票列表
            invoiceList: [],
            pageNum: 1,
            pageSize: 10,
            showEmpty: false,
        };
    },
    watch: {},
    mounted() {},
    methods: {
        /**
         * @description  : 获取发票列表
         * @param         {*} isInit:是否刷新数据重置接口入参
         * @return        {*}
         */
        async getInvoiceListApi({ isInit = false } = {}) {
            // 重置页码
            if (isInit) {
                this.invoiceList = [];
                this.pageNum = 1;
                this.$refs.dataList.loadStatus = 'loading';
            }
            let params = {
                sortType: this.sortType,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };
            if (this.amountRange.amountRangeActive) {
                params.minAmount = this.amountRange.minAmount;
                params.maxAmount = this.amountRange.maxAmount;
            }
            // 根据不同的第二层tab值，判断对应入参
            if (this.secondNavActive == '3100') {
                params.invoiceType = 1;
            } else if (this.secondNavActive == '3150') {
                params.invoiceType = 2;
            } else if (this.secondNavActive == '') {
                params.invoiceType = 3;
            }
            let res = await invoiceListApi(params);
            this.$refs.dataList.stopRefresh();
            if (res && res.success) {
                let arr = res.data.rows || [];
                // 判断每个发票是否可以抬头换开
                arr = arr.map(item => {
                    item.isShowReplace = this.getIsShowReplace(item);
                    return item;
                });
                this.invoiceList = this.invoiceList.concat(arr);

                if (this.pageNum >= res.data.pageSum) {
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.pageNum++;
            }
            if (this.invoiceList.length == 0) {
                this.showEmpty = true;
            } else {
                this.showEmpty = false;
            }
        },
        /**
         * @description  : 下拉刷新事件
         * @return        {*}
         */
        refreshPullDown() {
            this.getInvoiceListApi({ isInit: true });
        },
        /**
         * @description  : 上拉加载事件
         * @return        {*}
         */
        uploadMoreEvent() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getInvoiceListApi();
            }
        },
        /**
         * @description  : 发票抬头换开逻辑跳转
         * @param         {*} item:发票数据
         * @return        {*}
         */
        exchangeInvoice(item) {
            const newItem = JSON.parse(JSON.stringify(item));
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'invoice_hk_toast',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$emit('changeModalMode', 'invoiceChange');
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认换开',
                cancelText: '我再想想',
                confirmColor: '#000',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'confirmReplaceBut',
                            refer: this.refer,
                            channelID: clientCode,
                        });
                        let params = { invoiceInfo: newItem, type: 'change', refer: this.refer };
                        let url = '/packages/third-invoice/pages/invoice-form/main';
                        this.$sKit.layer.useRouter(url, params);
                    } else if (res.cancel) {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'thinkBut',
                            refer: this.refer,
                            channelID: clientCode,
                        });
                        console.log('用户点击取消');
                    }
                },
            });
        },
        /**
         * @description  : 判断发票是否可以抬头换开，开票时间到现在超过90天无法换开，已换开发票无法换开
         * @param         {*} item:发票数据
         * @return        {*}
         */
        getIsShowReplace(item) {
            if (item.invoiceTypeCode == '025') {
                return false;
            }
            let date = Date.parse(new Date()); //获取当前时间戳（毫秒）
            let timestamp = Date.parse(new Date(item.issueDate.replace(/-/g, '/'))); //获取开票时间戳（毫秒）
            let ninetyDays = 31536000000; //365天的毫秒数
            let noExpiration = timestamp + ninetyDays > date; //开票时间加365天大于当前时间说明没有过期
            console.log(date, '当前时间戳');
            console.log(timestamp + ninetyDays, '开票时间戳加365天');
            console.log(timestamp, '开票时间戳');
            console.log(noExpiration, '开票时间戳加365天，大于当前时间戳，没过期');
            // 等于1是换开过的不在展示 90天以后不在显示换开
            return !item.changeTitleMark && noExpiration;
        },
        /**
         * @description  : 查看发票详情跳转
         * @param         {*} item:发票数据
         * @return        {*}
         */
        invoiceDetail(item) {
            const newItem = JSON.parse(JSON.stringify(item));
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'invoice_details_But',
                refer: this.refer,
                channelID: clientCode,
            });
            let params = { ...newItem, refer: this.refer };
            let url = '/packages/third-invoice/pages/invoice-detail/main';
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style scoped lang="scss">
.containerWrap {
    padding: 32rpx;

    .item {
        display: flex;
        flex-direction: column;
        margin-top: 24rpx;
        background: #ffffff;
        border-radius: 16rpx;
        padding: 27rpx;

        &:nth-of-type(1) {
            margin-top: 0;
        }

        .itemBox {
            display: flex;
            justify-content: space-between;

            .itemLeft {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .time {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #666666;
                    // line-height: 33rpx;
                    margin-top: 2px;
                    font-family: PingFangSC-Regular, PingFang SC;
                }

                .title {
                    max-width: 440rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #000000;
                    line-height: 40rpx;
                    margin-top: 14rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                    font-family: PingFangSC-Regular, PingFang SC;
                }

                .name {
                    max-width: 440rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                    margin-top: 14rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                    font-family: PingFangSC-Regular, PingFang SC;
                }
            }

            .itemRight {
                display: flex;
                align-items: center;

                .amount {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 40rpx;
                }
            }
        }

        .btns {
            display: flex;
            justify-content: flex-end;
            margin-top: 20rpx;

            .btn {
                position: relative;
                width: 170rpx;
                height: 48rpx;
                background: #ffffff;
                border-radius: 4rpx;
                border: 1px solid #333333;
                font-size: 26rpx;
                font-weight: 400;
                color: #333333;
                line-height: 46rpx;
                text-align: center;
                margin-left: 20rpx;
            }
            .btn2 {
                width: 180rpx;
            }
        }
    }
}

.j-c-e {
    justify-content: flex-end;
}

.j-c-c {
    justify-content: center;
}
</style>
