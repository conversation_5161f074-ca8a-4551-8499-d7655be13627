<template>
    <div class="one_lick_login">
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>

        <div class="padding_input">
            <div class="main_heading">请输入验证码</div>
            <div class="subheading">已发送至156****5722</div>
            <zjCode @returnresult="returnresult"></zjCode>
            <div class="short_message" v-if="type1">重新获取验证码</div>
            <div class="other_login_methods_btn" v-if="type2">其他登录/注册方式</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import zjCode from '../../components/third-verify/main';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { zjCode },
    name: 'one-click-login',
    data() {
        return {
            type1: false,
            type2: false,
            forgetpw: '',
            appeal: false,
        };
    },
    mounted() {},
    onLoad(option) {
        let routerInfo = JSON.parse(decodeURIComponent(option.data));
        this.type1 = routerInfo.type1 || false;
        this.type2 = routerInfo.type2 || false;
        this.forgetpw = routerInfo.forgetpw || '';
        this.appeal = routerInfo.appeal || false;
        console.log(JSON.parse(decodeURIComponent(option.data)), '============');
    },
    methods: {
        loginBtn() {},
        returnresult(code) {
            if (code.length == 6 && !this.appeal) {
                this.$sKit.layer.useRouter(
                    '/packages/third-login/pages/set-password/main',
                    { type1: this.type1, type2: this.type2 },
                    'navigateTo',
                );
            } else {
                this.$sKit.layer.useRouter('/packages/third-login/pages/appeal-result/main', {}, 'navigateTo');
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../style/style.scss';

.padding_input {
    padding: 16px;
}

.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
}

.operator {
    margin-bottom: 16px;
    font-size: 12px;
    color: #999;
}

input {
    height: 51px;
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    font-size: 21px;
    font-weight: 600;
    color: #333333;
    line-height: 30px;
    margin-bottom: 12px;
    padding-left: 20px;
    box-sizing: border-box;
}

.agreement_div {
    color: #999999;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.argreement_rules {
    color: #e64f22;
}
</style>

<style lang="scss">
.phone_input {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
}
</style>
