<script>
import { mapGetters, mapState } from 'vuex';

export default {
    globalData: {
        isThisTimeTemCancel: false, // 本次进入小程序，消息模板是否被取消
        isfirst: false,
        systemBar: '40',
        systemInfo: {},
    },
    onLaunch: function (options) {
        getApp().globalData.pageParams = options.query || {};
        // 临时调整2.0token过期导致的在页面弹窗问题
        // #ifdef MP-WEIXIN
        let tokenInfo = uni.getStorageSync('tokenInfo');
        if (tokenInfo && tokenInfo.token) {
            tokenInfo.token = '';
            uni.setStorageSync('tokenInfo', tokenInfo);
        }
		// 预约加油优惠券兜底清除缓存信息
		// #ifndef MP-MPAAS
		uni.setStorageSync('setSelectCoupon', '');
		// #endif
        // #endif
        // const data =
        //     { "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzIxIiwiaXNzIjoiZ3Ntc3NpdCIsInRpbWUiOjE3NDY2MDk3NDU3MzAsInVzZXJJZCI6IjEzNTIwMjk2MzEifQ.BtYmfsqEOYz8FbgMiO4njek0e7SVZQn3bV0ApYd38TY", "expiresIn": "1747213945732", "needBindPhone": false, "gsmsToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQtY29kZSI6IkMyMSIsImdyYW50X3R5cGUiOiJ1bmlvbl9wYXlfYXBwbGV0IiwidXNlcl9pZCI6MTM1MjAyOTYzMSwidXNlcl9uYW1lIjoibWJyMTc2MTEzNzY3NzAwMDAiLCJwb3N0TG9naW5BY3Rpb25Kc29uU3RyIjoie1wibG9uZ1RpbWVOb3RMb2dpblwiOm51bGwsXCJybHl6RmxhZ1wiOm51bGwsXCJpc09wZW5GaW5nZXJPckZhY2VcIjpudWxsLFwiYmluZFBob25lRmxhZ1wiOnRydWUsXCJyZWdpc3RlckZsYWdcIjpudWxsfSIsIm9wZW5pZCI6IlMybnNOV3Zpc3hZbEpPTkxXR2xEMUNyVVk2eGFhWXBieU9lT002ekVWU3huNVdKc1lFeE5UT0lJMlpNS0U0dFUiLCJzY29wZSI6WyJzZXJ2ZXIiXSwiZXhwIjoxNzQ3MjE0NTQ1LCJhdXRob3JpdGllcyI6WyJPVVRFUiJdLCJqdGkiOiJhOWZlMTQzOS02MDllLTQzNmMtOTIxYy1lYTA4YzJhMmQ2YmYiLCJyZWFsbmFtZSI6IuWPpOW6t-W6t0AzIn0.fpLP0GOKCmlh2AtrAHoDmQpVHaxvZK6T41n0UQFzY8o", "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzIxIiwiZXhwaXJlIjoxNzQ4NDIyMzQ1NzMyLCJpc3MiOiJnc21zc2l0IiwiZ3Ntc1JlZnJlc2hUb2tlbiI6ImV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpqYkdsbGJuUXRZMjlrWlNJNklrTXlNU0lzSW1keVlXNTBYM1I1Y0dVaU9pSjFibWx2Ymw5d1lYbGZZWEJ3YkdWMElpd2lkWE5sY2w5cFpDSTZNVE0xTWpBeU9UWXpNU3dpZFhObGNsOXVZVzFsSWpvaWJXSnlNVGMyTVRFek56WTNOekF3TURBaUxDSndiM04wVEc5bmFXNUJZM1JwYjI1S2MyOXVVM1J5SWpvaWUxd2liRzl1WjFScGJXVk9iM1JNYjJkcGJsd2lPbTUxYkd3c1hDSnliSGw2Um14aFoxd2lPbTUxYkd3c1hDSnBjMDl3Wlc1R2FXNW5aWEpQY2taaFkyVmNJanB1ZFd4c0xGd2lZbWx1WkZCb2IyNWxSbXhoWjF3aU9uUnlkV1VzWENKeVpXZHBjM1JsY2tac1lXZGNJanB1ZFd4c2ZTSXNJbTl3Wlc1cFpDSTZJbE15Ym5OT1YzWnBjM2haYkVwUFRreFhSMnhFTVVOeVZWazJlR0ZoV1hCaWVVOWxUMDAyZWtWV1UzaHVOVmRLYzFsRmVFNVVUMGxKTWxwTlMwVTBkRlVpTENKelkyOXdaU0k2V3lKelpYSjJaWElpWFN3aVlYUnBJam9pWVRsbVpURTBNemt0TmpBNVpTMDBNelpqTFRreU1XTXRaV0V3T0dNeVlUSmtObUptSWl3aVpYaHdJam94TnpRNE5ESTBNVFExTENKaGRYUm9iM0pwZEdsbGN5STZXeUpQVlZSRlVpSmRMQ0pxZEdraU9pSTBOekkyTW1Sa015MWlZalEzTFRRNU9UTXRPVE01T0MwME5qVmtZakUwWmpReVpqY2lMQ0p5WldGc2JtRnRaU0k2SXVXUHBPVzZ0LVc2dDBBekluMC5ZS2ZKUnh6TmthbEJMOUlQZ2gtellOX3ZvNUJFamlMTVFoOVVBVDc5VU5jIiwidGltZSI6MTc0NjYwOTc0NTczMiwidXNlcklkIjoiMTM1MjAyOTYzMSJ9.MmkF1fKIM3cs8bDj9V-PMxUdVGFEthCbCI1W_MHfybY", "memberNo": "1352029631", "cppeiLoginInfo": null, "newMember": true, "postLoginActionJsonStr": "{\"longTimeNotLogin\":null,\"rlyzFlag\":null,\"isOpenFingerOrFace\":null,\"bindPhoneFlag\":true,\"registerFlag\":null}", "openId": "S2nsNWvisxYlJONLWGlD1CrUY6xaaYpbyOeOM6zEVSxn5WJsYExNTOII2ZMKE4tU" }
        // uni.setStorageSync('tokenInfo', data);
        // 预约加油优惠券兜底清除缓存信息
        // #ifndef MP-MPAAS
        uni.setStorageSync('setSelectCoupon', '');
        // #endif
    },
    onShow: async function (option) {
        // #ifdef MP-MPAAS
        let systemInfo = my.getSystemInfoSync();
        console.log('system---', systemInfo.system);
        if (systemInfo && (systemInfo.system.includes('Harmony') || systemInfo.platform == 'Harmony')) {
            this.$store.commit('setIsHarmony', true);
        }
        // #endif
        // #ifdef MP-WEIXIN
        // 获取页面路由
        let pages = getCurrentPages(); // 获取栈实例
        let page = pages[pages.length - 1]; // 获取当前页面的数据，包含页面路由
        let prevPage = pages[pages.length - 2]; // 获取上个页面的数据，包含页面路由
        console.log('当前页面的路由：', pages[pages.length - 1].$page.fullPath);
        console.log(pages[pages.length - 1].$page.fullPath.includes('third'), '包含third吗');
        console.log(pages[pages.length - 1].$page.fullPath, 'pages[pages.length - 1].$page.fullPath=======');
        let routerUrl = pages[pages.length - 1].$page.fullPath;
        //推荐注册需求 新增字段 staffStationId 推荐码
        console.log('option---staffStationId', option);
        if (option.query && option.query.scene) {
            let staffStationId = decodeURIComponent(option.query.scene) || '';
            let sceneId = this.$sKit.commonUtil.parseQueryString(staffStationId);
            console.log('sceneIdsceneIdsceneIdsceneIdsceneId', sceneId);
            if (sceneId.yg || sceneId.t) {
                // 只有t存在代表是员工邀请开卡   只有yg值是推荐注册需求
                this.$store.dispatch('setOfficialAccountParams', sceneId.t);
                this.$store.commit('setStaffStationId', sceneId.yg);
                console.log(sceneId, '推荐注册或开卡接收到的参数');
                // if (sceneId.yg) {
                //     let tokenInfo = uni.getStorageSync('tokenInfo');
                //     if (!tokenInfo?.newMember) {
                //         this.$store.dispatch('getToken3', 'upgrade');
                //     }
                // }
                // // 以下判断是处理 ，小程序至于后台，然后扫员工推荐开卡码进入2.0页面问题
                // if (tokenInfo?.newMember && this.$store.state.token3) {
                //     console.log(this.$store.state.token3, this.walletStatus.status, routerUrl, '测试这里的数据是否都是正常的数据');
                //     // // 如果员工邀请开卡存在值
                //     // if (sceneId.t) {
                //     //     // 判断当前昆仑e享卡是什么状态，如果是未开卡跳转到开通昆仑e享卡页面
                //     //     // 否则去钱包设置页面
                //     //     if (!this.walletStatus.status) {
                //     //         let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                //     //         let params = { refer: 'r04' };
                //     //         let type = 'reLaunch';
                //     //         this.$sKit.layer.useRouter(url, params, type);
                //     //     } else {
                //     //         let url = '/packages/third-my-wallet/pages/home/<USER>';
                //     //         let params = { refer: 'r03' };
                //     //         let type = 'reLaunch';
                //     //         this.$sKit.layer.useRouter(url, params, type);
                //     //     }
                //     //     return;
                //     //     // 这里是处理除了扫码的情况下，其他情况将小程序置于后台，再次扫码，或者搜索小程序页面跳转到2.0页面
                //     // } else
                //     if (routerUrl.includes('third')) {
                //         console.log(routerUrl, 'routerUrl===');
                //         uni.redirectTo({
                //             url: decodeURIComponent(routerUrl),
                //         });
                //         return;
                //     }
                // }
            }
        }
        // console.log(routerUrl, 'routerUrl======');
        return;
        if (getApp().globalData.isfirst) {
            if (
                (Number(option.scene) == 1011 && routerUrl == '/pages/thirdHome/main') ||
                (Number(option.scene) == 1017 && routerUrl == '/pages/thirdHome/main')
            )
                return;
            if (option.scene == 1038 || option.path?.includes('packages/transferAccount/pages/home/<USER>')) return;
            if (
                option.scene &&
                [1005, 1006, 1011, 1017, 1026, 1027, 1037, 1045, 1047, 1048, 1049, 1053, 1065, 1069, 1106].includes(option.scene)
            ) {
                console.log(option.scene, 'option.scene======option.scene');
                //扫描小程序码进来的
                if (routerUrl.includes('third')) {
                    if (
                        routerUrl.includes('/packages/third-oil-charge-payment/pages/oil-charge-payment/main') ||
                        routerUrl.includes('/packages/third-scan-code-payment/pages/confir-order/main')
                    ) {
                        uni.reLaunch({
                            url: '/pages/thirdHome/main',
                        });
                        return;
                    }
                    console.log(routerUrl, 'routerUrl===');
                    // let tokenInfo = uni.getStorageSync('tokenInfo');
                    // if (this.token3) {
                    //     this.$store.commit('jumpToOfficialAccount', tokenInfo);
                    // }

                    uni.redirectTo({ url: decodeURIComponent(routerUrl) });
                } else {
                    if (routerUrl.includes('/packages/web-view/pages/home/<USER>')) return;
                    uni.redirectTo({ url: decodeURIComponent(routerUrl) });
                }
            }
        } else {
            getApp().globalData.isfirst = true;
        }

        // #endif
    },
    onHide: function () {},
    onLoad: function () {
        // #ifdef MP-WEIXIN
        wx.hideHomeButton(); // 隐藏返回首页图标
        // #endif
    },
    methods: {},
    computed: {
        ...mapState({
            token3: state => state.token3, //
            staffStationId: state => state.staffStationId, // 邀请开卡接口传值
            walletStatus: state => state.wallet.walletStatus, // 昆仑e享卡状态
            loginThirdFlag: state => state.location.loginThirdFlag, // 昆仑e享卡状态
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 昆仑e享卡状态
            // #endif
        }),
    },
};
</script>
<style lang="scss">
@import 'uview-ui/index.scss';
@import '~@/s-kit/css/index.scss';

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

/* 隐藏所有页面的 navigationBar */
.uni-app--showtopwindow uni-page-head {
    display: none;
}

/* input 光标呼吸灯动画 */
@keyframes flashAnimation {
    0% {
        width: 2px;
    }

    100% {
        width: 0;
    }
}

@keyframes cursor {
    50% {
        border-color: #ff8200;
    }
}

/* 目标input必须设置相对定位 */
.input-focus-cursor::after {
    content: '';
    position: absolute;
    top: 2px;
    right: -1px;
    height: 80%;
    border-left: 2px solid transparent;
    animation: flashAnimation 3s steps(16) forwards, cursor 1s infinite;
}
</style>
