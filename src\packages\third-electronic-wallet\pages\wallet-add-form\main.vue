<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="开通昆仑e享卡">
                <!-- #ifdef MP-ALIPAY || H5-->
                <img
                    v-if="showBackHomeIcon"
                    slot="title_img"
                    alt=""
                    class="title_img"
                    src="../../images/backHomeIcon.png"
                    @click="backThirdHome"
                />
                <!-- #endif -->
            </zj-navbar>
            <div class="f-1 bg-F7F7FB padding-16">
                <div class="header">
                    <img src="../../images/cnpc-logo.png" alt />
                    <div class="footer-text font-14 color-333 mar-top-12">畅享生活每一站</div>
                </div>
                <div class="fl-column fl-al-sta border-rad-8 bg-fff width100" style="padding: 10px 0">
                    <div class="footer-text font-12 color-333 te-center mar-top-8" style="line-height: 20px; width: 96%; margin-left: 2%"
                        >根据相关监管要求，您需要完成实名认证才能使用昆仑e享卡功能，请输入本人的身份证信息
                    </div>
                    <div class="current mar-top-8">
                        <input
                            v-model="current.name"
                            :disabled="current.authCode || zfbUserInfoEcho"
                            :placeholder-style="fromInput"
                            class="border-rad-4 bg-F7F7FB"
                            placeholder="请输入真实姓名(必填)"
                            type="text"
                        />
                    </div>
                    <div class="current border-rad-4" v-if="zfbUserInfoEcho">
                        <input
                            v-model="zfbEchoIdNo"
                            :placeholder-style="fromInput"
                            class="border-rad-4 bg-F7F7FB"
                            disabled
                            maxlength="18"
                            placeholder="请输入身份证号码(必填)"
                            type="idcard"
                        />
                    </div>
                    <!-- #ifndef H5-CLOUD -->
                    <div class="current border-rad-4" v-else>
                        <input
                            v-model="current.idNo"
                            :disabled="current.authCode"
                            :placeholder-style="fromInput"
                            class="border-rad-4 bg-F7F7FB"
                            maxlength="18"
                            placeholder="请输入身份证号码(必填)"
                            type="idcard"
                        />
                    </div>
                    <!-- #endif -->
                    <!-- #ifdef H5-CLOUD -->
                    <div class="current border-rad-4" v-else>
                        <input
                            v-model="current.idNo"
                            :placeholder-style="fromInput"
                            class="border-rad-4 bg-F7F7FB"
                            maxlength="18"
                            placeholder="请输入身份证号码(必填)"
                            type="idcard"
                        />
                    </div>
                    <!-- #endif -->
                    <div class="current border-rad-4" @click="selectPlaceOfDeposit">
                        <!-- <input
                            class="put-select border-rad-4 color-333 bg-F7F7FB"
                            :placeholder-style="fromInput"
                            v-model="current.address"
                            readonly
                            placeholder="请选择常用地(必填)"
                        /> -->
                        <div v-if="current.address" class="current_address put-select border-rad-4 color-333 bg-F7F7FB">
                            <div class="fl-row fl-al-cen">
                                <img v-if="isGetLocation" alt="" class="loca" src="../../images/loca.png" />
                                <div>{{ current.address }}</div>
                            </div>

                            <div class="fl-row fl-al-cen">
                                <div class="select_text font-14 color-999 weight-400">去修改</div>
                                <img alt="" class="select" src="../../images/select-area-icon/arrow-right.png" />
                            </div>
                        </div>
                        <div v-else class="current_address put-select border-rad-4 bg-F7F7FB address_placeholder">
                            <div class="fl-row fl-al-cen">
                                <img v-if="isGetLocation" alt="" class="loca" src="../../images/loca.png" />
                                <div>请选择常用地(必填)</div>
                            </div>
                            <div class="fl-row fl-al-cen">
                                <div class="select_text font-14 color-999 weight-400">{{ isGetLocation ? '去修改' : '去选择' }}</div>
                                <img alt="" class="select" src="../../images/select-area-icon/arrow-right.png" />
                            </div>
                        </div>
                    </div>
                    <div class="current border-rad-4" @click="selectStation" v-if="false">
                        <!--<div class="current border-rad-4" @click="selectStation" v-if="closeForm">-->
                        <input
                            v-model="current.stationName"
                            :placeholder-style="fromInput"
                            class="put-select border-rad-4 bg-F7F7FB"
                            disabled
                            placeholder="加油站(必填)"
                            type="number"
                        />
                    </div>
                    <div class="current border-rad-4" v-if="closeForm">
                        <input
                            v-model="current.inviteCode"
                            :placeholder-style="fromInput"
                            class="border-rad-4 bg-F7F7FB"
                            maxlength="18"
                            placeholder="员工邀请码"
                            type="text"
                        />
                    </div>
                </div>
                <div class="footer mar-top-8">
                    <div class="footer-text weight-500 font-13 color-333">温馨提示:</div>
                    <div class="footer-text font-13 color-333">1. 昆仑e享卡充值时不提供发票，消费后方可开具电子发票。</div>
                    <div class="footer-text font-13 color-333"
                        >2. 昆仑e享卡充值金额上限为&yen;5000;仅限使用本人付款账户为昆仑e享卡充值。</div
                    >
                    <div class="footer-text font-13 color-333">3. 每个账户只能开通一张昆仑e享卡。</div>
                    <div class="footer-text font-13 color-333">4. 昆仑e享卡常用地一年可变更5次。</div>
                </div>
                <div class="floatBtn">
                    <div class="protocol fl-row fl-al-cen font-12 color-999 weight-400">
                        <div @click="checkTheAgreement" class="checked-div" style="padding-top: 1px">
                            <img class="img_style" v-if="!agreementchecked" src="../../images/empty.png" alt="" />
                            <img class="img_style" v-if="agreementchecked" src="../../images/successSel.png" alt="" />
                        </div>
                        <div class="fl-row mar-left-4"
                            >我已阅读并同意<div class="font-12 color-E64F22 weight-400" @click="getAgreeOn">《用户服务协议书》</div>
                        </div>
                    </div>
                    <div class="btn primary-btn border-rad-8 shad-ef color-fff" @click="activateECard">开通昆仑e享卡</div>
                </div>
            </div>
            <!-- <uniPopup ref="codePopup" background="#f7f7fb" type="bottom"></uniPopup> -->
            <SelectCity
                v-if="showPopup"
                :provinceCityArray="provincesAndCitiesList"
                :show="showPopup"
                @hideShow="hideShow"
                @sureSelectArea="sureSelectArea"
            ></SelectCity>
            <zj-show-modal>
                <div v-if="!agreementchecked" class="pop_div">
                    申请开通昆仑e享卡，请先阅读并同意能源e站<div
                        class="font-12 color-E64F22 weight-400"
                        style="display: inline-block"
                        @click="getAgreeOn"
                        >《用户服务协议书》</div
                    >
                </div>
            </zj-show-modal>
            <zj-old-account v-if="isTransfer" :activate="activate"></zj-old-account>
            <!-- #ifdef MP-WEIXIN -->
            <custom-popup
                ref="oilPopup"
                :safeArea="true"
                class="popupBg"
                overlay="true"
                type="bottom"
            >
                <div class="model-div">
                    <div class="marlr16 padtb16">
                        <div class="name">能源e站 申请</div>
                        <div class="explain">
                            您知悉并同意，中国石油天然气股份有限公司有权通过财付通公司获取您的姓名、身份证号等实名信息，以用于昆仑e享卡的开通及掌纹支付相关事宜。
                        </div>
                        <div class="btn-wrap">
                            <div class="cancle" @click="cancle">取消</div>
                            <div class="agree" @click="agreeToAuthorize">同意</div>
                        </div>
                    </div>
                </div>
            </custom-popup>
            <!-- #endif -->
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import projectConfig from '../../../../../project.config';
import appWalletAddForm from './diff-environment/app-wallet-add-form';
import wxWalletAddForm from './diff-environment/wx-wallet-add-form';
import zfbWalletAddForm from './diff-environment/zfb-wallet-add-form';
import h5WalletAddForm from './diff-environment/h5-wallet-add-form';
import { accountStatus, staffInfoGet, staffMemberBind } from '../../../../s-kit/js/v3-http/https3/wallet';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    components: {
        SelectCity,
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appWalletAddForm,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxWalletAddForm,
        zfbWalletAddForm,
        // #endif
        // #ifdef H5-CLOUD
        h5WalletAddForm,
        // #endif
    ],
    data() {
        return {
            // 表单数据
            current: {
                // 姓名
                name: '',
                // 身份证号
                idNo: '',
                // 开户地
                address: '',
                // 地区编码
                areaCode: '',
                // 油站名称
                stationName: '',
                // 油站编码
                stationCode: '',
                // 员工推荐码
                inviteCode: '',
                // 开通掌纹支付（true:同时开通掌纹和昆仑E享卡及积分账户；false:只开通昆仑E享卡及积分账户）
                isOpenPalmPay: false,
                // 微信openid，掌纹识别为true的时候必填
                openid: '',
                authCode: '',
                // 掌纹开卡和普通开卡成功后跳转不同页面标识
                isPalmPay: false,
                // 普通开卡
                current: false,
                isWxAuth: false,
            },
            // 是否禁用当前input
            visible: true,
            // 表单样式
            fromInput: 'color:#999999;font-size:14px;',
            // 协议勾选标识
            agreementchecked: false,
            // 选择开户地区弹窗
            showPopup: false,
            // 省市区数组
            provincesAndCitiesList: [],
            // 选中油站返回的数据
            prveDataObject: {
                refreshListFlag: false,
                stationParam: {},
            },
            // #ifdef MP-MPAAS
            // 低于当3.5.9的版本不显示指定表单
            closeForm: false,
            // #endif
            // #ifndef MP-MPAAS
            closeForm: true,
            // #endif
            // wxPalmprintAuth 接口调用标识防止通过onshow重复调用
            wxPalmprintAuthFlag: false,
            // 区分功能限制电子卡迁移成功后返回的到哪个页面
            activate: '',
            showBackHomeIcon: false,
            refer: '', //埋点页面来源
            zfbUserInfoEcho: false,
            zfbEchoIdNo: '',
            isGetLocation: false,
        };
    },
    computed: {
        ...mapState({
            staffStationId: state => state.staffStationId, // 邀请开卡接口传值
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app, // 油站数组
            district: state => state.locationV3_app.district, // 油站数组
            cityV3: state => state.locationV3_app.cityV3, // 油站数组
            locationState: state => state.locationV3_app.locationState, // 油站数组
            cityCode: state => state.locationV3_app.cityCode, // 油站数组
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            walletStatus: state => state.wallet.walletStatus, //是否显示弹窗
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 存储公众号参数和小程序跳转过来的参数
            // #endif
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        ...mapGetters(['memberAccountInfo', 'walletSkin', 'walletInfo', 'openId']),
    },
    onShow(options) {
        // #ifdef MP-WEIXIN
        if (this.wxPalmprintAuthFlag) {
            var obj = wx.getEnterOptionsSync();
            // 微信掌纹开卡需求储存数据
            console.log(obj.referrerInfo.extraData, '掌纹开卡认证完成后返回数据了吗');
            if (obj?.referrerInfo?.extraData?.auth_code) {
                this.current.authCode = obj.referrerInfo.extraData.auth_code;
                // this.current.name = res.name;
                // this.current.idNo = res.idNo;
                this.current.openid = this.openId;
                let params = {
                    openId: this.openId,
                    authCode: this.current.authCode,
                };
                this.palmprintCertificationApplication(params);
            }
        }
        // #endif
        // 判断是否是在网点导航页面返回
        if (this.prveDataObject.refreshListFlag) {
            this.current.stationCode = this.prveDataObject.stationParam.orgCode;
            this.current.stationName = this.prveDataObject.stationParam.orgName;
            console.log(this.prveDataObject.stationParam, '油站信息');
            // this.prveDataObject.refreshListFlag = false;
        }
    },
    async onLoad(options) {
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        if (getCurrentPages().length == 1) {
            this.showBackHomeIcon = true;
        }
        this.getUserNameId();
        // #endif
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data)) || {};
            this.refer = params.refer;

            // #ifdef MP-ALIPAY
            try {
                // 支付宝不走绑定接口逻辑，做回显开卡逻辑
                if (Number(params.t) === 2) {
                    if (params.ygid) this.current.inviteCode = params.ygid;
                    let [staffInfoRes, asRes] = await Promise.all([
                        staffInfoGet({ staffUid: params.yg }, { isCustomErr: true }),
                        accountStatus(),
                    ]);
                    if (staffInfoRes?.success) {
                        this.current.inviteCode = staffInfoRes?.data?.staffENo;
                    } else {
                        setTimeout(() => {
                            uni.showToast({ title: staffInfoRes.message });
                        }, 1000);
                    }
                    // 接口异常失败，或者状态为已开通，则跳转钱包首页
                    if (!asRes?.success || asRes?.data?.status) {
                        throw new Error('接口异常或已开通昆仑e享卡');
                    }
                }
            } catch (err) {
                console.error(err);
                this.$sKit.layer.useRouter('/pages/thirdHome/main', { qKey: 'wallet' }, 'reLaunch');
                return;
            }
            // #endif
        }
        this.$sKit.mpBP.tracker('e享卡开通', {
            seed: 'eCardActiveBiz',
            pageID: 'openCardPage', // 页面名
            refer: this.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
        });
        // 获取省市地区
        this.getProvincesCities();
        // #ifdef MP-WEIXIN
        console.log(this.officialAccountParams, 'officialAccountParams');
        if (this.officialAccountParams == 2) {
            this.current.inviteCode = this.staffStationId;
            this.bind();
        }
        // #endif

        // 开卡默认选择开户地
        await this.defaultAccountOpeningLocation();
    },
    mounted() {
        // #ifdef MP-WEIXIN
        setTimeout(() => {
            this.$refs.oilPopup.open();
        }, 500);
        // #endif
    },
    methods: {
        // 开卡默认选择开户地
        async defaultAccountOpeningLocation() {
            // 判断当前App的版本号
            // #ifdef MP-MPAAS
            this.closeForm = await this.$cnpcBridge.judgeProtocolCall();
            console.log(this.closeForm, 'this.closeForm====开通e享卡');
            // 当前APP版本号大于3.6.0
            if (this.closeForm) {
                //检查用户定位权限是否开启
                let locationRes = await this.$cnpcBridge.checkPermission();
                if (locationRes.appStatus && locationRes.osStatus) {
                    // 获取定位
                    this.$cnpcBridge.getLocation(res => {
                        this.current.address = res.province + res.city;
                        this.current.areaCode = res.cityCode;
                    });
                }
            }
            // #endif
            // #ifdef MP-WEIXIN || H5-CLOUD
            // 微信小程序回显开卡地址
            console.log(this.cityV3, this.district, this.locationState, '位置信息');
            if (this.cityV3 && this.district && this.locationState) {
                this.isGetLocation = true;
                this.current.address = this.cityV3 + this.district;
                this.current.areaCode = this.cityCode;
            } else {
                this.isGetLocation = false;
            }
            // #endif
            // #ifdef MP-ALIPAY
            my.getLocation({
                type: 1,
                success: async res => {
                    // 直辖市城市编码取的是区编码
                    this.current.address = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city)
                        ? res.province + res.district
                        : res.province + res.city;
                    this.current.areaCode = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city)
                        ? res.districtAdcode
                        : res.cityAdcode;
                },
                fail(err) {},
            });
            // #endif
        },
        backThirdHome() {
            this.$sKit.layer.backHomeFun();
        },
        handleBlurA() {
            this.$nextTick(() => {
                const inputB = this.$refs.inputB;
                inputB.focus();
            });
        },
        /**
         * @description : 员工推荐开卡，调用员工绑定关系
         * @return        {*}
         */
        async bind() {
            this.$store.commit('setLoginThirdFlag', false);
            let res = await staffMemberBind({ staffId: Number(this.staffStationId) }, { isCustomErr: true, isload: false });
            // this.$store.commit('setStaffStationId', '');
            this.$store.dispatch('setOfficialAccountParams', '');
            console.log('接口res----绑定关系---开卡表单页', res);
            if (res.success) {
                setTimeout(() => {
                    uni.showToast({
                        title: '推荐关系已记录，请开通e享卡',
                        icon: 'none',
                        duration: 2000,
                        // mask: true,
                    });
                }, 1000);
            }
            if (!res.success) {
                if (res.errorCode !== 'P_B02_003417') {
                    setTimeout(() => {
                        uni.showToast({
                            title: res.message,
                            icon: 'none',
                            duration: 2000,
                        });
                    }, 1000);
                }
            }
        },
        /**
         * @description  : 选择开户地
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @return        {*}
         */
        selectPlaceOfDeposit() {
            if (this.provincesAndCitiesList.length > 0) {
                this.showPopup = true;
            } else {
                this.getProvincesCities().then(() => {
                    this.showPopup = true;
                });
            }
        },
        /**
         * @description  : 确定选择当前开户地
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @param         {Object} detail -选择常用地城市和编码
         * @return        {*}
         */
        sureSelectArea({ detail }) {
            this.showPopup = false;
            this.current.address = detail.province + detail.city;
            this.current.areaCode = detail.cityCode;
        },
        /**
         * @description  : 勾选协议
         * @param         {Boolean} agreementchecked -是否勾选协议
         * @return        {*}
         */
        checkTheAgreement() {
            this.agreementchecked = !this.agreementchecked;
        },
        /**
         * @description  : 开通昆仑e享卡--跳转输入手机号密码页面(微信和支付宝小程序跳转输入密码页面，APP调用SDK)
         * @param         {String} name -用户输入的姓名
         * @param         {String} idNo -用户输入的身份证号
         * @param         {String} address -用户选择的开户地
         * @param         {Boolean} agreementchecked -用户是否勾选协议
         * @return        {*}
         */
        activateECard() {
            this.current.name = this.current.name.replace(/\s/g, ' ');
            if (this.current.name === '') {
                uni.showToast({
                    title: '请您输入真实姓名',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // else if (this.current.name !== '' && !this.$test.checkName(this.current.name)) {
            //     uni.showToast({
            //         title: '请您输入真实姓名',
            //         icon: 'none',
            //         duration: 2000,
            //     });
            //     return;
            // }
            // // 校验身份证号
            if (this.current.idNo === '') {
                uni.showToast({
                    title: '证件号不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.$test.newCheckIdNumber(this.current.idNo)) {
                uni.showToast({
                    title: '证件号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 校验城市
            if (this.current.address === '') {
                uni.showToast({
                    title: '请您选择开户地',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // if (!this.current.authCode) {
            //     //
            //     if (this.closeForm) {
            //         // 校验油站
            //         if (this.current.stationCode === '') {
            //             uni.showToast({
            //                 title: '请您选择油站',
            //                 icon: 'none',
            //                 duration: 2000,
            //             });
            //             return;
            //         }
            //     }
            // }
            // 校验是否同意协议
            if (!this.agreementchecked) {
                this.$store.dispatch('zjShowModal', {
                    confirmText: '同意并继续',
                    cancelText: '我在想想',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            this.agreementchecked = true;
                            setTimeout(() => {
                                this.skipNext();
                            }, 500);
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            return;
                        }
                    },
                });
                return;
            } else {
                this.skipNext();
            }
        },
        skipNext() {
            // #ifdef MP-MPAAS
            let url;
            if (this.isHarmony) {
                url = '/packages/third-electronic-wallet/pages/wallet-password/main';
                this.current.local = this.current.areaCode;
            } else {
                url = '/packages/third-electronic-wallet/pages/wallet-authorization-description/main';
            }
            let params = { ...this.current, refer: this.refer };
            let type = 'redirectTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
            // #endif
            // #ifndef MP-MPAAS
            this.current.local = this.current.areaCode;
            let url = '/packages/third-electronic-wallet/pages/wallet-password/main';
            let params = { ...this.current, refer: this.refer };
            let type = 'redirectTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
            // #endif
        },
        /**
         * @description  : 获取省市区
         * @return        {*}
         */
        async getProvincesCities() {
            return new Promise(async (resolve, reject) => {
                let res = await provinceAndCityList({}, { isload: false });
                if (res.success) {
                    let arr2 = res.data.map(item => {
                        let newItem = {
                            name: item.parentAreaName,
                            code: item.parentAreaCode,
                            city: item.areaList,
                        };
                        return newItem;
                    });
                    this.provincesAndCitiesList = arr2;
                    resolve();
                }
            });
        },
        /**
         * @description  : 关闭选择城市弹窗
         * @param         {Boolean} showPopup -选择常用地弹窗标识 开启或关闭
         * @return        {*}
         */
        hideShow() {
            this.showPopup = false;
        },
    },
    beforeDestroy() {
        this.prveDataObject.refreshListFlag = false;
    },
};
</script>

<style scoped lang="scss">
.put-select {
    pointer-events: none;
}

.title_img {
    width: 17px;
    height: 17px;
    position: absolute;
    top: 24%;
    left: -80%;
}

.view {
    overflow: scroll;
    padding-bottom: 205rpx;

    .header {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 10px;

        img {
            width: 62px;
            height: 60px;
        }

        .header_div_text {
            margin-top: 10px;
            font-weight: bold;
        }
    }

    .popupBg {
        background: #f7f7fb;
    }

    .current {
        width: 96%;
        height: 44px;
        display: flex;
        margin-left: 2%;
        margin-bottom: 16px;

        input {
            width: 100%;
            height: 44px;
            line-height: 44px;
            padding-left: 5px;
        }

        // input[placeholder] {
        //   display: flex;
        //   align-items: center;
        // }
    }

    .footer {
        .footer-text {
            margin-bottom: 7px;
        }
    }

    .floatBtn {
        position: fixed;
        left: 0;
        right: 0;
        bottom: env(safe-area-inset-bottom);
        padding: 0 32rpx;
        background: #ffffff;

        .protocol {
            display: flex;
            margin-top: 16px;
            margin-bottom: 16px;

            .checked-div {
                // width: 25px;
                .img_style {
                    width: 15px;
                    height: 15px;
                }
            }
        }

        .btn {
            height: 44px;
            line-height: 44px;
            width: 100%;
            margin-bottom: 12px;
        }
    }

    .picker-view {
        width: 750rpx;
        height: 600rpx;
        margin-top: 20rpx;
    }

    .item {
        line-height: 100rpx;
        text-align: center;
    }

    .model-div {
        background: #f7f7fb;
        border-radius: 10px 10px 0px 0px;
        padding-bottom: 16px;
        height: 200px;

        .marlr16 {
            margin: 0 16px;

            .name {
                padding-top: 20px;
            }

            .explain {
                margin-top: 15px;
            }

            .btn-wrap {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;

                div {
                    width: 48%;
                    text-align: center;
                    height: 44px;
                    line-height: 44px;
                    border-radius: 8px;
                }

                .cancle {
                    background-color: #ececec;
                    color: #57be6a;
                }

                .agree {
                    background-color: #57be6a;
                    color: #ececec;
                }
            }
        }

        .marb16 {
            margin-bottom: 16px;
        }
    }
}

.current_address {
    width: 100%;
    height: 44px;
    text-indent: 10rpx;
    //line-height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .loca {
        width: 25rpx;
        height: 32rpx;
        margin-right: 10rpx;
        margin-left: 5px;
    }

    .select_text {
        margin-right: 5rpx;
    }

    .select {
        height: 16px;
        margin-right: 8px;
        width: 16px;
    }
}

.address_placeholder {
    color: #999999;
    font-size: 14px;
}

.pop_div {
    text-align: center;
    padding: 10px 12px;
    display: inline-block;
    color: #333;
    font-size: 13px;
}
</style>
<style scoped>
/*input {*/
/*background: #fff;*/
/*}*/
</style>
