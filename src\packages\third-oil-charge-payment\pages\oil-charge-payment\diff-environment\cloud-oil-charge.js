// #ifdef H5-CLOUD
import { mapGetters, mapState } from 'vuex';
import * as paysdk from '@petro-gsms/paysdk-js';
console.log('Plugin---', paysdk);
import { calculateOrdeDiscountsApi } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import post from '../../../../../s-kit/js/v3-http/post';
import { clientCode } from '../../../../../../project.config';
import { realPersonIdentify } from '../../../../../s-kit/js/v3-http/https3/oilCard/index'

export default {
    computed: {},
    mounted() { },
    data() {
        return {
            // 支付插件参数
            resData: {},
            // 支付参数
            payParams: {},
            // 微信加券二次支付 payMethod == 11的数据
            order: null,
            wxSecondaryPaymentObj: {},
            faceValueResponse: null,
            isAuthCloud: false,
        };
    },
    onShow() {
        if (this.isAuthCloud) {
            this.isAuthCloud = false
            if (this.faceValueResponse) {
                this.realNameAuthentication(this.faceValueResponse)
            }
            return
        }
    },
    methods: {
        //优惠券
        calculateOrdeDiscountsPost(selectTicketsInfo) {
            // let dataParams = JSON.parse(JSON.stringify(this.unPaidInfo));
            let newProductList = this.unPaidInfo.productList;
            let newSelectTicketsInfo = JSON.parse(JSON.stringify(selectTicketsInfo));
            newProductList.forEach(item => {
                if (!item.productType) {
                    delete item.productType;
                }
                item.activityDiscountList.forEach(acItem => {
                    if (!acItem.groupId) {
                        delete acItem.groupId;
                    }
                });
            });
            console.log('newProductList----', newProductList);
            const params = {
                stationCode: this.unPaidInfo.stationCode,
                orderNo: this.unPaidInfo.orderNo,
                channelType: this.unPaidInfo.channelType,
                orderMoney: this.unPaidInfo.orderMoney,
                productInOrderVoList: newProductList,
                businessDay: this.businessDay,
                usedCouponList: newSelectTicketsInfo,
            };
            calculateOrdeDiscountsApi(params)
                .then(res => {
                    if (res.success) {
                        console.log('OrdeDiscounts---', res.data);
                        this.unPaidInfo = { ...res.data, cancel: this.unPaidInfo?.cancel };
                        this.calculateGiftCard();
                        this.isPaymentMethodAvailable();
                    } else {
                        this.getPayChannelInfo();
                    }
                    this.isCanClickPay = true;
                })
                .catch(err => {
                    this.getPayChannelInfo();
                    this.isCanClickPay = true;
                });
        },
        // 油卡或电子账户支付
        async sdkRealPay(isAuth = false) {
            // uni.showLoading({
            //     title: '支付中',
            //     mask: true,
            // });
            this.isOrderShow = true;
            this.payParams = {
                stationCode: this.unPaidInfo.stationCode,
                bizOrderNo: this.unPaidInfo.orderNo,
                rcvAmt: this.unPaidInfo.orderMoney,
                // realAmt: this.order ? Number(this.order.rcvAmt) : this.unPaidInfo.payMoney,
                realAmt: this.orderPayMoney + '',
                payType: this.curPayChannelInfo.payType,
                bizDay: this.businessDay,
                // 3.0.4风控字段
                extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                insidePayList: [],
            };
            if (this.selectCard.length > 0 && this.orderPayMoney == 0) {
                this.payParams.payType = 5;
                // this.curPayChannelInfo.payType = 5
                let item = this.payList.find(item => item.payType == 5);
                if (item) this.curPayChannelInfo = item;
            }
            if (this.curPayChannelInfo.payType == '6') {
                this.payParams.cardIdx = this.curCardPayInfo.cardSequence + '';
                this.payParams.fuelCardNo = this.curCardPayInfo.fuelCardNo;
            } else if (this.curPayChannelInfo.payType == '5') {
                this.payParams.cardIdx = '1';
                this.payParams.accountNo = this.walletInfo.ewalletNo;
            }
            if (JSON.stringify(this.wxSecondaryPaymentObj) !== '{}') {
                // this.payParams.insidePayList.push({
                //   payType: '11',
                //   payAmt: this.wxSecondaryPaymentObj.payAmount + '',
                //   // 已经核销过得券再次支付时不用携带
                //   // couponNo: this.wxSecondaryPaymentObj.couponNo,
                //   couponTemplateNo: this.wxSecondaryPaymentObj.couponTemplateNo,
                // });
            } else {
                if (this.unPaidInfo.couponList.length > 0) {
                    for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                        if (this.unPaidInfo.couponList[i].used == 1) {
                            this.payParams.insidePayList.push({
                                payType: '11',
                                payAmt: this.unPaidInfo.couponList[i].couponDiscount + '',
                                couponNo: this.unPaidInfo.couponList[i].couponNo,
                                couponTemplateNo: this.unPaidInfo.couponList[i].couponTemplateNo,
                            });
                        }
                    }
                }
            }
            this.payParams.insidePayList.push(...(await this.calculateGiftCardPay()));
            console.log('油卡或电子账户支付参数', this.payParams);
            console.log('SDK支付方式', this.curPayChannelInfo.payType);

            try {
                this.$sKit.mpBP.tracker('后支付加油', {
                    seed: 'hpayoilBiz',
                    pageID: 'inputPsdbut', // 返回sdk标识
                    refer: this.refer || '',
                    channelID: clientCode,
                    address: this.cityName,
                });
                const passwordInstance = this.$refs.cloudKeyboardRef.keyboardInstance();
                this.resData = await paysdk.Buy(JSON.parse(JSON.stringify(this.payParams)), passwordInstance);

                console.log('zfb---res---成功', this.resData);
                if (this.resData.code === 'PAY_SUCCESS') {
                    this.beginTime = new Date().getTime();
                    let URL = `/packages/third-oil-charge-payment/pages/query-payment-results/main`;
                    let params = { ...this.unPaidInfo, refer: this.refer };
                    let type = 'redirectTo';
                    this.$sKit.layer.useRouter(URL, params, type);
                    this.isOrderShow = false;
                } else if (this.resData.code === 'PAY_ERROR_003') {
                    // need risk
                    this.isCanClickPay = false;
                    this.isPaying = true;
                    //需要实人认证
                    const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (riskRes == 13) {
                        // 打开实人认证的表单弹窗
                        this.realNameDialogFlag = true;
                    }
                } else {
                    console.log('支付失败');
                    // 支付失败的情况查询待支付订单
                    this.getPayChannelInfo();
                    // 关闭遮罩
                    this.isOrderShow = false;
                    uni.hideLoading();
                    // 截取字符串后面的数据
                    let errIndex = this.resData.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = this.resData.msg.slice(0, errIndex);
                        customErr = this.resData.msg.slice(errIndex + 1, this.resData.msg.length);
                    } else {
                        customErr = this.resData.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            } catch (error) {
                uni.hideLoading();
                this.isCanClickPay = true;
                this.isPaying = false;
                console.log(error);
            }
        },
        // 获取支付方式
        async getBuyPayTypeList() {
            console.log(11111111111111, paysdk);
            let params = {
                stationCode: this.unPaidInfo.stationCode,
                amount: this.unPaidInfo.payMoney,
            };
            uni.showLoading({
                title: '加载中',
                mask: true,
            });
            const res = await paysdk.GetBuyTypeList(params);
            uni.hideLoading();
            if (res.code === 'PAY_SUCCESS') {
                // this.$store.commit('mSetRefuelingMigrationFlag', false)
                this.payList = res.data;
                // this.payList = res.data.filter(item => item.payType != 6)
                this.getRechargeMarketingCopy(this.unPaidInfo);
                console.log('支付方式', this.payList);

                if (this.payList && this.payList.length > 0) {
                    this.curPayChannelInfo = this.payList[0];
                    if (this.payList[0].payType == '6' && this.payList[0].memberAssets) {
                        this.isShowOil = true;
                    } else {
                        this.curCardPayInfo = '';
                        this.isShowOil = false;
                    }
                    this.isPaymentMethodAvailable();
                }
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: res.msg,
                    content: `${res.code}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }

            console.log('payList---', res);
        },
        realNameAuthentication(faceValue) {
            if (faceValue) {
                return new Promise(async (resolve, reject) => {

                    let params = {
                        // 认证校验码，初始化实人认证接口返回的数据。
                        authInfo: faceValue.authInfo,
                        // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡；10-资金转出；
                        type: faceValue.type || 1,
                        // 认证接入方式：1—APP接入；2—PC或H5接入；
                        verifyMode: '2',
                        // 当type=1时该字段为必填项。
                        idNo: faceValue.idNo,
                        // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
                        verifyUnique: faceValue.verifyUnique,
                        // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
                        certifyId: faceValue.certifyId,
                        gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
                    };
                    console.log(params, 'H5传入实人认证接口的参数');


                    let res = await realPersonIdentify(params);
                    if (res.success && res.data.authInfo) {
                        console.log(res, '实人认证方法接口返回结果');
                        this.sdkRealPay(true);
                        resolve(res);
                    } else {
                    }

                });
            }
        },
    },
};
// #endif
