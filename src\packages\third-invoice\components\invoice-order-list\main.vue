<template>
    <zj-data-list
        ref="dataList"
        @scrolltolower="scrolltolower"
        @refreshPullDown="refreshPullDown"
        :emptyImage="require('../../image/kt4dd.png')"
        emptyText="您目前没有任何订单"
        emptyType="invoiceOrder"
        :showEmpty="showEmpty"
    >
        <div class="containerWrap">
            <div class="tips" v-if="orderType != 'charge'">
                <div class="color">温馨提示：</div>
                <div class="fl-column">
                    <div>消费后365天内可开具电子发票</div>
                    <div class="fl-row"
                        >实体卡线上充值开具电子发票<div class="color" style="display: inline-block" @click="toOrderList()">点击此处</div>
                    </div>
                </div>
            </div>
            <div class="item" v-for="(item, index) in orderList" :key="index">
                <div class="invoice-check">
                    <div @click="checkItemChange(item)">
                        <!--<radio color="#e64f22" :checked="item.checked" :name="item.id" style="transform: scale(0.65)" />-->
                        <img
                            class="check-radio"
                            :src="item.checked ? require('../../image/type-checked.png') : require('../../image/type-unchecked.png')"
                            alt=""
                        />
                    </div>
                </div>
                <div class="itemBox" @click="invoiceDetail(item)">
                    <div class="itemLeft">
                        <div class="time">{{ item.createTime }}</div>
                        <div class="title">{{ item.stationName }}</div>
                        <div class="name" v-if="orderType != 'charge'">{{ item.productNames }}</div>
                    </div>
                    <div class="itemRight j-c-c">
                        <div class="amount">&yen;{{ item.actualPayTotalAmount }}</div>
                    </div>
                </div>
            </div>
        </div>
    </zj-data-list>
</template>
<script>
// 金额计算js库
const currency = require('currency.js');
import { orderListApi, chargeOrderList } from '../../../../s-kit/js/v3-http/https3/order/index';
export default {
    name: 'orderList',
    props: {
        // 父页面传入的第一层tab值
        navActive: {
            default: 1,
            type: Number,
        },
        // 父页面传入的第二层tab值
        secondNavActive: {
            default: '',
            type: String,
        },
        // 时间筛选范围
        timeObj: {
            type: Object,
            default: () => {
                return {};
            },
        },
        orderType: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            // 可开票订单列表
            orderList: [],
            pageNum: 1,
            pageSize: 10,
            showEmpty: false,
        };
    },
    watch: {},
    methods: {
        /**
         * @description  : 获取可开票订单列表
         * @param         {*} isInit:是否刷新数据重置接口入参
         * @return        {*}
         */
        async getOrderListApi({ isInit = false } = {}) {
            // 重置页码
            if (isInit) {
                this.orderList = [];
                this.pageNum = 1;
                this.$refs.dataList.loadStatus = 'loading';
            }
            let params = {};
            let postFun = () => {};
            if (this.orderType == 'charge') {
                params = {
                    startTime: this.timeObj.startTime + ' 00:00:00',
                    endTime: this.timeObj.endTime + ' 23:59:59',
                    orderStatus: '4',
                    invoiceFlag: '0',
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                };
                postFun = chargeOrderList;
            } else {
                params = {
                    startTime: this.timeObj.startTime + ' 00:00:00',
                    endTime: this.timeObj.endTime + ' 23:59:59',
                    orderStatus: 4,
                    type: 3,
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                };
                // 如果secondNavActive传入为'',入参orderProductType不传
                if (this.secondNavActive != '') {
                    params.orderProductType = [this.secondNavActive];
                    params.orderProductType.push('33');
                }
                postFun = orderListApi;
            }
            let res = await postFun(params);
            this.$refs.dataList.stopRefresh();
            if (res.success) {
                let arr = res.data.rows || [];
                // 为每条订单数据增加是否选中的绑定值，默认不选中
                arr.forEach(item => {
                    item.checked = false;
                    let productNameArr = (item.orderIterms || []).map(item => {
                        return item.productName;
                    });
                    item.productNames = productNameArr.join(',');
                });
                this.orderList = this.orderList.concat(arr);
                if (this.pageSize > res.data.rows.length) {
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.pageNum++;
            }
            if (this.orderList.length == 0) {
                this.showEmpty = true;
            } else {
                this.showEmpty = false;
            }
        },
        /**
         * @description  : 下拉加载事件
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getOrderListApi();
            }
        },
        /**
         * @description  : 上拉刷新时间
         * @return        {*}
         */
        refreshPullDown() {
            this.$emit('exData', { isAll: false, checkAllAmount: 0, orderNoList: [], checkOrderList: [] });
            this.getOrderListApi({ isInit: true });
        },
        /**
         * @description  : 订单详情跳转
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        invoiceDetail(item) {
            // 订单详情跳转
            let params = item;
            let url = '';
            if (this.orderType == 'charge') {
                url = '/packages/third-order/pages/charge-order-detail/main';
            } else {
                url = '/packages/third-order/pages/order-detail/main';
            }
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 点击radio触发，改变绑定值，判断是否全选，计算选中订单总金额，将数据抛出到父组件展示
         * @param         {*} item:订单数据
         * @return        {*}
         */
        checkItemChange(item) {
            item.checked = !item.checked;
            let boolean = this.orderList.every(item => item.checked == true);
            let checkAllAmount = 0;
            let orderNoList = [];
            let checkOrderList = [];
            this.orderList.forEach((item, idx) => {
                if (item.checked) {
                    orderNoList.push(item.orderNo);
                    checkOrderList.push(item);
                    checkAllAmount = currency(checkAllAmount).add(Number(item.actualPayTotalAmount) || 0).value;
                }
            });
            this.$emit('exData', {
                isAll: boolean,
                checkAllAmount: checkAllAmount,
                orderNoList: orderNoList,
                checkOrderList: checkOrderList,
            });
        },
        /**
         * @description  : 全选按钮事件，全选中全部订单或全不选，将数据抛出到父组件展示
         * @param         {Boolean} boolean: true全选  false全不选
         * @return        {*}
         */
        allChecked(boolean) {
            let checkAllAmount = 0;
            let orderNoList = [];
            let checkOrderList = [];
            this.orderList = this.orderList.map(item => {
                item.checked = boolean;
                if (item.checked) {
                    orderNoList.push(item.orderNo);
                    checkOrderList.push(item);
                    checkAllAmount = currency(checkAllAmount).add(Number(item.actualPayTotalAmount) || 0).value;
                }

                return item;
            });
            this.$emit('exData', {
                isAll: boolean,
                checkAllAmount: checkAllAmount,
                orderNoList: orderNoList,
                checkOrderList: checkOrderList,
            });
        },
        /**
         * @description  : 点击跳转卡充值记录
         * @return        {*}
         */
        toOrderList() {
            let url = '/packages/third-order/pages/home/<USER>';
            let params = {
                navActive: 2,
                secondNavActive: 4,
                refer: 'r53',
            };
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style lang="scss" scoped>
.containerWrap {
    padding: 32rpx 32rpx 0;

    .tips {
        font-size: 24rpx;
        font-weight: 400;
        line-height: 33rpx;
        color: #333333;
        margin-left: 26rpx;
        margin-bottom: 24rpx;
        display: flex;

        .tipsRight {
            display: flex;
            flex-direction: column;
        }

        .color {
            color: #e64f22;
        }
    }

    .item {
        display: flex;
        align-items: center;
        margin-top: 24rpx;

        &:nth-of-type(1) {
            margin-top: 0;
        }

        .invoice-check {
            width: 73rpx;

            .check-radio {
                width: 40rpx;
                height: 40rpx;
                margin-right: 33rpx;
            }
        }

        .itemBox {
            flex: 1;
            background: #ffffff;
            border-radius: 16rpx;
            padding: 27rpx;
            display: flex;
            justify-content: space-between;

            .itemLeft {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .time {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 33rpx;
                }

                .title {
                    max-width: 440rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #000000;
                    line-height: 40rpx;
                    margin-top: 14rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                }

                .name {
                    max-width: 440rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                    margin-top: 14rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                }
            }

            .itemRight {
                display: flex;
                flex-direction: column;
                align-items: flex-end;

                .amount {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 40rpx;
                }

                .btn {
                    width: 171rpx;
                    height: 48rpx;
                    background: #ffffff;
                    border-radius: 4rpx;
                    border: 1rpx solid #333333;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 48rpx;
                    text-align: center;
                    margin-top: 20rpx;
                }
            }
        }
    }
}

.j-c-e {
    justify-content: flex-end;
}

.j-c-c {
    justify-content: center;
}
</style>
