<template>
    <div class="pageMpaas">
        <div class="fl-column p-bf bg-F7F7FB">
            <zj-navbar :border-bottom="false" :height="44" :title="'自营网点列表'"></zj-navbar>
            <div class="content fl-column f-1 ov-hid">
                <div class="f-1 mh-0">
                    <zj-data-list
                        ref="list"
                        :emptyImage="require('../../image/kt6wd.png')"
                        :emptyText="productList.length > 0 ? '' : '暂未查询到网点'"
                        :showEmpty="showEmpty"
                        background="#F7F7FB"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                    >
                        <div v-if="productList.length > 0" class="cells-content">
                            <searchCell
                                v-for="(item, index) in productList"
                                :key="index"
                                :curIndex="index"
                                :item="item"
                                @cellClick="cellClick(item)"
                            ></searchCell>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <zj-show-modal ref="modalScreen"></zj-show-modal>
        </div>
    </div>
</template>

<script>
const PAGE_SIZE = 10; // 每页多少个
const THROTTLE_TIME = 500;
import searchCell from '../../components/search-module/oil-station-search-cell.vue';
import { stationListApi, getMarketingRec } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { clientCode } from '../../../../../project.config';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            productList: [],
            addressList: [],
            // 页码
            page: 1,
            // 加载状态 // 0 不显示 1 显示加载动画 2 显示没有更多
            // loadState: 0,
            showEmpty: false,
            first: true,
        };
    },
    onLoad(option) {},
    onShow() {},
    mounted() {
        setTimeout(() => {
            this.selectAction([], true);
        }, 300);
    },
    methods: {
        /**
         * @description  : 获取油站列表
         * @param         {*} item:stationService油站类型
         * @return        {*}
         */
        selectAction(item = [], isSameArr = false) {
            if (this.first) {
                // this.$refs.list.loadStatus = 'contentdown';
                this.init(isSameArr);
                this.first = false;
            } else {
                this.getGoodsListFun({ isInit: true, isSameArr: isSameArr });
            }
        },
        /**
         * @description  : 初始化位置信息，获取油站列表
         * @return        {*}
         */
        init(isSameArr) {
            this.$store.dispatch('initLocationV3_app', {
                callback: res => {
                    this.getGoodsListFun({ isInit: true }, res);
                },
                type: 'getLocationed',
            });
        },
        /**
         * @description  : 获取油站列表
         * @param         {*} isInit: 是否重置油站列表
         * @param         {*} item: 油站类型入参
         * @return        {*}
         */
        async getGoodsListFun({ isInit = false, isSameArr = false } = {}, longitudeAndLatitude) {
            if (isInit) {
                Object.assign(this, {
                    productList: isSameArr ? (this.productList.length > 0 ? this.productList : []) : [],
                    page: 1,
                });
            }
            let { page, productList } = this;
            //TODO

            let tagsCodeServices = [];
            let params = {
                bookingRefueling: '',
                distance: '50',
                latitude: this.latV3 || longitudeAndLatitude.latitude,
                longitude: this.lonV3 || longitudeAndLatitude.longitude,
                pageNum: page,
                pageSize: PAGE_SIZE,
                // stationService: '10',
                stationServices: tagsCodeServices,
                destinationLongitude: '',
                destinationLatitude: '',
                mapType: '1', //地图坐标系标识（0高德，1百度，2腾讯）
                orgCode: this.walletInfo.addressNo || '',
                manageType: 1, // 管理模式：非自营：0，自营：1
            };
            this.$refs.list.loadStatus = 'loading';
            let { data } = await stationListApi(params, { isload: false });
            let arr = this.$sKit.layer.filterLocations(data.rows)
            let dataRows = arr.map(item => {
                let strArr = item.businessHoursList.map(time => {
                    return time.startTime.substring(0, 5) + '-' + time.endTime.substring(0, 5);
                });
                item.businessHoursStr = strArr.join(' ');
                return item;
            });
            this.$refs.list.pullDownHeight = 0;
            this.$refs.list.pullingDown = false;
            // 判断选择框是否打开
            if (isSameArr && productList.length > 0) {
                const isSame = this.areArraysEqual(this.showMarkerArrV3_app, dataRows);
                console.log(isSame, 'result一样吗?');
                if (isSame) {
                    console.log('相同走这里');
                } else {
                    console.log('不相同走这里');
                    productList = dataRows || [];
                }
            } else {
                productList = productList.concat(dataRows || []);
            }

            if (data && page >= data.pageSum) {
                this.$refs.list.loadStatus = 'nomore';
            } else {
                this.$refs.list.loadStatus = 'contentdown';
            }
            Object.assign(this, {
                productList,
                page: Number(page) + 1,
            });
            this.showEmpty = this.productList.length <= 0 ? true : false;
        },
        /**
         * @description  : 下拉刷新触发
         * @return        {*}
         */
        refreshPullDown() {
            this.page = 1;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  : 上拉加载事件
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.list.loadStatus !== 'nomore') {
                this.$refs.list.loadStatus = 'loading';
                this.page + 1;
                this.getGoodsListFun();
            }
        },
        /**
         * @description  :选择油站跳转到去加油页面
         * @param         {*} item:油站数据
         * @return        {*}
         */
        async cellClick(item) {
            let URL = '/packages/third-scan-code-payment/pages/home-code/main';
            let params = {
                noInitLocation: true,
                tabType: 'charge',
            };
            let type = 'reLaunch';
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$sKit.layer.useRouter(URL, params, type);
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, type);
            // #endif
            let centerParam = {
                marker: item,
                upLocation: true,
                oilChangeStation: true
            };
            this.$store.dispatch('setSelectMarkerToMapCenterV3', centerParam);
        },
        /**
         *  JavaScript 中判断两个数组的数据是否相同
         * */
        areArraysEqual(arr1, arr2) {
            if (arr1.length > 0 && arr2.length > 0) {
                return arr1[0].orgCode === arr2[0].orgCode && arr1[0].distance === arr2[0].distance;
            }
            return false;
        },
    },
    computed: {
        ...mapGetters(['showMarkerArrV3_app', 'latV3', 'lonV3', 'walletInfo']),
    },
    components: {
        searchCell,
        ZjPopup,
    },
};
</script>
<style lang="scss" scoped>
.content {
    padding: 16px 16px 0 16px;
}

.model-div {
    background: #ffffff;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 20px;
    overflow: auto;

    .marlr24 {
        margin: 0 16px 0 16px;
    }

    .padtb16 {
        padding-top: 16px;
        // height: 330px;
        overflow: hidden;
        overflow-x: hidden;
        overflow-y: scroll;
    }

    .popup-label-title {
        margin-left: 18px;
    }

    .popup-center {
        display: flex;
        flex-wrap: wrap;
        padding: 10px 12px;

        .popup-label {
            width: 77px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-size: 12px;
            padding: 0px 6px;
            margin: 0 5px 0 5px;
            text-align: center;
            margin-bottom: 10px;
        }

        .isSelect {
            background: #ffe9e3;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }

    .submit-btn {
        font-size: 15px;
        padding: 10px 65px;
        border-radius: 6px;
    }

    .marright {
        margin-right: 18px;
    }

    .close {
        padding: 20px 3px;

        img {
            width: 13px;
            height: 13px;
        }
    }
}
</style>
