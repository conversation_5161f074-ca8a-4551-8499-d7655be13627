<template>
    <div> </div>
</template>

<script>
export default {
    data() {
        return {
            src: '',
        };
    },
    created() {
        let pageParams = getApp().globalData.pageParams;

        let realPage = '',
            params = [];
        for (let key in pageParams) {
            if (key === 'realPage') {
                realPage = pageParams['realPage'] || '';
            } else {
                params.push(key + '=' + pageParams[key] || '');
            }
        }

        if (realPage) {
            let url = realPage + '?' + params.join('&');

            uni.navigateTo({
                url: url,
            });
        }
    },
};
</script>

<style scoped lang="scss"></style>
