export default {
    state: {
        // 触发隐私协议，显示弹窗，传递函数赋值给privacySuccess，
        // 并在弹窗内调用 hidePrivacy(隐藏弹窗)，
        // setGetPhoneNumber (同意授权)，回调到get- phone - number页面进行回调
        privacySuccess: null,
        // 是否展示隐私协议弹窗
        privacyIsShow: false,
        // 中转页授权登陆后置灰按钮标识
        transferAccountGrayBtnFlag: true,
    },
    mutations: {
        hidePrivacy(state, info) {
            state.privacyIsShow = false;
        },
        setGetPhoneNumber(state, info) {
            let cb = state.privacySuccess;
            let resObj = {
                cancel: false,
                confirm: false,
            };
            info == 'confirm' ? (resObj.confirm = true) : (resObj.cancel = true);
            cb && cb(resObj);
        },
        // 整合隐私协议参数与功能按钮参数及显示隐私协议弹窗的参数
        integrationParameters(state, info) {
            state = Object.assign(state, info);
            console.log(state, '自定义弹窗开关');
            state.privacyIsShow = true;
            console.log(state, '合并后的state');
        },
        // 点击中转页授权登陆后置灰按钮
        steTransferAccountGrayBtnFlag(state, info) {
            state.transferAccountGrayBtnFlag = info;
        },
    },
    actions: {
        // 接收功能按钮参数
        receiveButtonParameters({ state, commit, dispatch }, option) {
            commit('integrationParameters', option);
        },
    },
    getters: {},
};
