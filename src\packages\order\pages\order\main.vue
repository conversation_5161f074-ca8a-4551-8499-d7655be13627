<template>
    <div class="detail-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="加油订单"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- <scroll-view class="scroll-view_H" :scroll-left="scrollLeft" scroll-x="true">
			<div class="slide_type_list">
				<div class="slide_type_list_view" v-for="(item,index) in typeList" :key="index"
					:class="{is_selected:active==index}" @click="changeType(item,index)">
					<div>{{ item.name }}</div>
				</div>
			</div>
    </scroll-view>-->
        <!-- <div :class="active == 0 ? 'ticket-btn' : 'ticket-btn disable-ticket-btn'" @click="alltypetap">
			一键开票<span>（{{active == 0 ?  activeFuelNum : '敬请期待'}}）</span>
    </div>-->
        <div :class="active == 0 ? 'ticket-btn' : ''" v-if="active == 0" @click="toInvoicing">开发票</div>
        <scroll-view
            :style="{
                height: 'calc(100vh - 268rpx - ' + navh + 'px)',
                marginTop: '20rpx',
            }"
            scroll-y="true"
            @scrolltolower="actionBottom"
        >
            <div class="content" v-if="active == 0">
                <div class="order" v-for="(item, index) in fuelList" :key="item.id">
                    <u-swipe-action :show="item.show" :index="index" @open="orderOpen" :options="options" @click="clickDelectOrder">
                        <div class="order-list" @click="detailtap(item)">
                            <img class="order-type" :src="'@/static/' + (item.payMode == 'wx' ? 'pay-wx.png' : 'pay-oilcard.png')" mode />
                            <div class="order-left">
                                <div class="time">{{ item.orderTime }}</div>
                                <div class="title">{{ item.stationName }}</div>
                                <div class="info">{{ item.oils }}#汽油 ({{ item.unitPrice }}元/升) x{{ item.quantity }}L</div>
                            </div>
                            <div class="order-right">
                                <div class="right-money">
                                    <span>¥{{ item.realAmt + (isInteger(item.realAmt) ? '.00' : '') }}</span>
                                </div>
                                <!-- 未开票并且已支付的才可以开发票 -->
                                <!-- <div class="right-div">
									<div v-if="item.isInvoice == 1" class="used-div" >已开票</div>
									<block v-else>
										<div 
											v-if="item.state == 2" 
											class="open-div" 
											@click.stop="typetap(index)" 
										>开发票</div>
									</block>
                </div>-->
                            </div>
                        </div>
                    </u-swipe-action>
                </div>
            </div>
            <div class="content" v-if="active == 1">
                <div class="action-list" bg-color="#f6f6f6" v-for="(item, index) in fuelList" :key="index">
                    <u-swipe-action :show="item.show" :index="index" @open="orderOpen" :options="options" @click="clickDelectOrder">
                        <div class="flex-list" @click="goRechargeDetail(item)">
                            <div class="flex-left">
                                <div class="cell">
                                    <!-- <div class="cell-title">充值时间</div>
									<div class="cell-detail">{{
								      item.tradeTime != '' ? item.tradeTime.replace(/\-/g, ".") : ''
                  }}</div>-->
                                    <div class="title">{{ item.orgName }}</div>
                                    <div :class="'cell-btn' + (item.invoiceStatus == 0 ? '' : ' disable-cell-btn')">{{
                                        item.invoiceStatus == 0 ? '未开票' : '已开票'
                                    }}</div>
                                </div>
                                <div class="celltime">
                                    <!-- <div class="cell-title">订单号</div>
                  <div class="cell-detail">{{ item.orderId }}</div>-->
                                    <div class="cell-title">创建时间：</div>
                                    <div class="cell-detail">
                                        {{ item.tradeTime != '' ? item.tradeTime.replace(/\-/g, '.') : '' }}
                                    </div>
                                </div>
                                <!-- <div class="cell">
									<div class="cell-title">充值金额</div>
									<div class="cell-theme"> +{{ item.amount }}</div>
                </div>-->
                                <div class="order-money">
                                    <div>￥</div>
                                    <div>
                                        <my-price color="#FF8200" intFont="18px" floatFont="12px" :price="item.amount"></my-price>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </u-swipe-action>
                </div>
            </div>
            <div class="loadmore">
                <u-loadmore @loadmore="clickLoadMore" :status="loadMortState" :load-text="loadText" />
            </div>
        </scroll-view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import myPrice from '@/components/price/price.vue';
import { getFuelListPost, deleteOrder, getRechargeList } from '@/api/home.js';
export default {
    name: 'order-detail',
    components: {
        myPrice, //价格
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            active: 0, // 上方tabbar选中index
            page: 1, // 分页页数
            pageSize: 5, // 分页数量
            loadMortState: 'loading', // 上拉加载状态
            loadText: {
                loadmore: '点击或上拉加载更多',
                loading: '努力加载中',
                nomore: '暂无更多数据',
            }, // 下拉刷新状态
            typeList: [
                {
                    name: '加油订单',
                },
                {
                    name: '充值订单',
                },
            ], // 上方tabbar
            fuelList: [], //全部订单
            navh: 64, // 导航高度
            options: [
                {
                    text: '删除',
                    style: {
                        backgroundColor: '#dd524d',
                        borderTopRightRadius: '5px',
                        borderBottomRightRadius: '5px',
                    },
                },
            ],
        };
    },
    async onLoad(option) {
        // let res = await getRechargeList({
        // 			pageNo: this.page,
        // 			pageSize: this.pageSize,
        // 			cardNo:'9045790000059605'
        // 		})
        // 		this.fuelList = res.data
        // 		return
        this.navh = this.$GlobalData.systemInfo.statusBarHeight + 40;
        if (typeof option.state !== 'undefined') {
            this.active = option.state;
        }
        this.fuelList = [];
        this.getFuelList();
        uni.$on('uploadPage', () => {
            this.loadMortState = 'loading';
            this.getFuelList();
        });
    },
    onReady() {},
    onUnload() {
        uni.$off('');
    },
    computed: {
        // 当前页可开发票的订单数量
        activeFuelNum() {
            const list = this.fuelList || [];
            let num = 0;
            list.forEach(item => {
                if (item.isInvoice !== 1 && item.state == 2) num++;
            });
            return num;
        },
    },
    methods: {
        // 删除订单
        async clickDelectOrder(index) {
            await this.$util.showModal('订单删除后将无法恢复，确定删除？');
            await deleteOrder({
                orderType: this.active == 1 ? 1 : 2,
                orderId: this.fuelList[index].id,
            });
            this.fuelList.splice(index, 1);
            await this.$util.showModal('删除成功', true);
        },
        // 触底监听
        actionBottom() {
            this.getFuelList(true);
        },
        // 点击更多加载按钮
        clickLoadMore() {
            this.getFuelList(true);
        },
        // 判断是否是整数
        isInteger(obj) {
            return Number.isInteger(obj);
        },
        // 上方tab切换
        changeType(item, index) {
            this.active = index;
            this.loadMortState = 'loading';
            this.fuelList = []; // 清空上一次tab留存下的数据，防止出现数据渲染与当前tab页不符
            this.getFuelList();
        },
        // 一键开票点击事件
        alltypetap() {
            if (this.active == 1) return;
            let selectOrderArr = [];
            let orderArr = this.fuelList;
            for (let i = 0; i < orderArr.length; i++) {
                let item = orderArr[i];
                item.isInvoice !== 1 && item.state == 2 ? selectOrderArr.push(orderArr[i]) : '';
                // orderArr[i].state == -1 || orderArr[i].state == 2 ? selectOrderArr.push(orderArr[i]) : ''
            }
            console.log('选中的数据', selectOrderArr);
            if (selectOrderArr.length == 0 || this.activeFuelNum <= 0) {
                return uni.showToast({
                    icon: 'none',
                    title: '您没有可开发票的订单',
                });
            }
            uni.navigateTo({
                url:
                    '/packages/invoice-center/pages/add-invoice/main?list=' +
                    encodeURIComponent(JSON.stringify(selectOrderArr)) +
                    '&ordertype=' +
                    (this.active == 1 ? 'recharge' : ''),
            });
        },
        //跳转可以开票页面
        toInvoicing() {
            uni.navigateTo({
                url: `/packages/order/pages/invoicable-orders/main`,
            });
        },
        // 单个开发票
        typetap(index) {
            // if (this.fuelList[index].isInvoice) return
            let selectOrderArr = [this.fuelList[index]];
            uni.navigateTo({
                url:
                    '/packages/invoice-center/pages/add-invoice/main?list=' +
                    encodeURIComponent(JSON.stringify(selectOrderArr)) +
                    '&ordertype=' +
                    (this.active == 1 ? 'recharge' : ''),
            });
        },
        // 详情按钮点击事件
        detailtap(item) {
            uni.navigateTo({
                url: `/packages/order/pages/order-detail-before/main?id=${item.id}&orderType=${this.active == 0 ? 2 : 1}`,
            });
        },
        //点击进入充值订单详情
        goRechargeDetail(item) {
            uni.navigateTo({
                url: '/packages/order/pages/recharge-order-detail/main?obj=' + JSON.stringify(item),
            });
        },
        // 订单左滑事件
        orderOpen(index) {
            // 先将正在被操作的swipeAction标记为打开状态，否则由于props的特性限制，
            // 原本为'false'，再次设置为'false'会无效
            this.fuelList[index].show = true;
            this.fuelList.map((val, idx) => {
                if (index != idx) this.fuelList[idx].show = false;
            });
        },
        //获取加油订单列表
        async getFuelList(isLoadMore = false) {
            if (this.loadMortState == 'nomore') {
                return;
            }
            this.page = isLoadMore ? this.page + 1 : 1;
            this.loadMortState = 'loading';
            if (this.active == 0) {
                let res = await getFuelListPost({
                    pageNo: this.page,
                    pageSize: this.pageSize,
                    // isInvoice:0,
                    // hasDel:1
                });
                res.data.map((val, idx) => {
                    val.show = false;
                });
                this.loadMortState = res.data.length < this.pageSize ? 'nomore' : 'loadmore';
                this.fuelList = isLoadMore ? [...this.fuelList, ...res.data] : res.data;
            } else if (this.active == 1) {
                let res = await getRechargeList({
                    pageNo: this.page,
                    pageSize: this.pageSize,
                    cardNo: '9000100000197217',
                    // isInvoice:0,
                    // hasDel:1
                });
                res.data.map((val, idx) => {
                    val.show = false;
                });
                this.loadMortState = res.data.length < this.pageSize ? 'nomore' : 'loadmore';
                for (let i = 0; i < res.data.length; i++) {
                    res.data[i].isChecked = false;
                }
                this.fuelList = isLoadMore ? [...this.fuelList, ...res.data] : res.data;
            } else {
            }
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 12px;
.solid {
    border: 1px solid red;
}
.detail-center {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    box-sizing: border-box;

    // 横向滑动tab
    .scroll-view_H {
        background-color: #f6f6f6;

        .slide_type_list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            //padding: 23px 0 22px 0;
            height: 70px;

            .slide_type_list_view {
                padding-bottom: 5px;
                font-size: 15px;
                color: #333333;
            }

            .is_selected {
                color: $btn-color;
                font-weight: bold;
                position: relative;
            }

            .is_selected:before {
                content: '';
                position: absolute;
                width: 24px;
                height: 3px;
                background: linear-gradient(270deg, rgba(255, 130, 0, 0) 0%, $btn-color 100%);
                left: 28%;
                bottom: -2px;
            }
        }
    }

    .ticket-btn {
        margin-left: 15px;
        width: 345px;
        height: 44px;
        box-sizing: border-box;
        background: $btn-mantle-color;
        border-radius: 5px;
        border: 1px solid $btn-color;
        font-size: 18px;
        font-weight: bold;
        color: $btn-color;
        margin: 0 auto;
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            font-size: $font14;
        }
    }
    .disable-ticket-btn {
        background: #f1f1f1;
        border: 1px solid #dcdcdc;
        color: #909090;
    }
    .loadmore {
        padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
        ::v-deep .u-load-more-wrap {
            background-color: transparent !important;
            view {
                background-color: transparent !important;
            }
        }
    }
    .content {
        padding: 0px 15px;
        position: relative;
        overflow: hidden;

        .action-list {
            margin-bottom: 10px;
            overflow: hidden;
            border-radius: 5px;
            .flex-list {
                background: #ffffff;
                display: flex;
                align-items: center;
                padding: 10px 15px 10px 15px;
                overflow: hidden;
                border-radius: 5px;
                // height: 90px;
                .flex-left {
                    flex: 1;
                    .cell {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        color: #333333;
                        font-size: 15px;
                        font-weight: 700;
                        .title {
                            width: 250px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        // display: flex;
                        // align-items: center;
                        // .cell-title {
                        // 	line-height: 24px;
                        // 	font-size: 12px;
                        // 	font-weight: 700;
                        // 	width: 60px;
                        // }
                        // .cell-detail {
                        // 	font-size: 12px;
                        // 	color: #909090;
                        // }
                        // .cell-theme {
                        // 	color: $btn-color;
                        // 	font-size: 15px;
                        // 	font-weight: 700;
                        // 	line-height: 24px;
                        // }
                    }
                    .celltime {
                        display: flex;
                        align-items: center;
                        color: #666666;
                        font-size: 12px;
                        margin-top: 3px;
                    }
                    .order-money {
                        margin-top: 10px;
                        font-weight: 500;
                        display: flex;
                        align-items: flex-end;
                        color: #ff8200;
                    }
                }
                .cell-btn {
                    background-color: $btn-mantle-color;
                    line-height: 22px;
                    font-size: 12px;
                    padding-left: 7px;
                    padding-right: 7px;
                    border: 1px solid $btn-color;
                    color: $btn-color;
                    border-radius: 15px;
                }

                .disable-cell-btn {
                    border: 1px solid #dcdcdc;
                    color: #909090;
                    background-color: #f6f6f6;
                }

                .flex-right {
                    text-align: center;
                }
            }
        }
        .order {
            width: 100%;
            overflow: hidden;
            margin-bottom: 10px;
            border-radius: 5px;

            .order-list {
                background: #ffffff;
                border-radius: 5px;
                display: flex;
                align-items: center;
                padding: 9px 10px 9px 10px;
                height: 90px;
                .order-type {
                    margin-right: 15px;
                    width: 18px;
                    height: 16px;
                }
                .order-left {
                    flex: 1;

                    .time {
                        font-size: 12px;
                        font-weight: 400;
                        color: #909090;
                        line-height: 24px;
                    }

                    .title {
                        font-size: 15px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 24px;
                    }

                    .info {
                        font-size: $font14;
                        font-weight: 400;
                        color: #333333;
                        line-height: 24px;
                    }
                }

                .order-img {
                    flex: 0.5;

                    img {
                        width: 44px;
                    }
                }

                .order-right {
                    height: 72px;
                    line-height: 72px;

                    .right-money {
                        color: $btn-color;
                        font-size: $font14;
                        text-align: right;

                        span {
                            font-size: 18px;
                            font-weight: 700;
                            line-height: 21px;
                            margin-top: 3px;
                        }

                        .arrow-right {
                            margin-left: 9px;
                        }
                    }

                    .right-div {
                        display: flex;
                        padding-top: 19px;
                        justify-content: flex-end;

                        .used-div {
                            width: 50px;
                            border-radius: 3px;
                            color: #909090;
                            background: #f6f6f6;
                            border: 1px solid #dcdcdc;
                            line-height: 23px;
                            text-align: center;
                            font-size: 12px;
                        }

                        .open-div {
                            font-size: 12px;
                            width: 50px;
                            background: $btn-mantle-color;
                            border-radius: 3px;
                            border: 1px solid $btn-color;
                            font-size: $font14;
                            font-family: PingFangSC-Regular, PingFang SC;
                            font-weight: 400;
                            color: $btn-color;
                            line-height: 23px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }

    .checkbox {
        margin-right: 8px;

        img {
            width: 20px;
        }

        .marginlft10 {
            margin-left: 8px;
        }
    }

    .right-arrow {
        width: 5px;
        margin-left: 10px;
        margin-top: -4px;
    }

    .footer {
        position: fixed;
        z-index: 10;
        bottom: 0;
        width: 100%;
        background: #ffffff;
        padding-bottom: env(safe-area-inset-bottom);

        .oil-btn {
            height: 64px;

            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .next-btn {
            width: 120px;
            height: 44px;
            background: $btn-color;
            border-radius: 5px;
            font-size: 15px;
            font-weight: 500;
            color: #ffffff;
            line-height: 44px;
            text-align: center;
        }
    }
}
</style>
