const fs = require('fs');
const path = require('path');
const utils = {
    readFile: path => {
        return fs.readFileSync(path, 'utf8');
    },
    resolve: file => {
        return path.resolve(__dirname, file);
    },
    formatTime: _date => {
        const date = new Date(_date);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const hour = date.getHours();
        const minute = date.getMinutes();
        const second = date.getSeconds();

        return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':');
    },
    formatNumber: n => {
        n = n.toString();
        return n[1] ? n : '0' + n;
    },
    isExistingFile: filename => {
        try {
            return fs.statSync(filename).isFile();
        } catch (err) {
            if (err.code === 'ENOENT') {
                return false;
            }
            throw err;
        }
    },
    getFileContent: path => {
        return JSON.parse(utils.readFile(utils.resolve(path)).toString());
    },
};
module.exports = utils;
