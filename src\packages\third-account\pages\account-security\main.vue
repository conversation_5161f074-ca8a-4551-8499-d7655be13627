<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="账号与安全" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <div class="card-default mar16">
                    <!-- #ifdef MP-MPAAS -->
                    <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('phone')">
                        <div class="f-1 font-14 weight-400 color-333">手机号</div>
                        <div class="fl-row fl-al-cen">
                            <div class="font-14 weight-400 color-999 marr6">{{ memberBaseInfo.phone }}</div>
                            <div class="arroe-right-small-1"></div>
                        </div>
                    </div>
                    <!-- #endif -->
                    <!-- #ifndef MP-MPAAS -->
                    <div class="fl-row fl-al-cen padd12 marlr12" v-if="!isHarmony">
                        <div class="f-1 font-14 weight-400 color-333">注册手机号</div>
                        <div class="fl-row fl-al-cen">
                            <div class="font-14 weight-400 color-999 marr6">{{ memberBaseInfo.phone }}</div>
                        </div>
                    </div>
                    <!-- #endif -->
                    <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('passowd')">
                        <div class="f-1 font-14 weight-400 color-333">修改登录密码</div>
                        <div class="fl-row fl-al-cen">
                            <div class="arroe-right-small-1"></div>
                        </div>
                    </div>
                    <!-- #ifdef MP-MPAAS -->
                    <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="headersClick('forgotPassowd')">
                        <div class="f-1 font-14 weight-400 color-333">忘记登录密码</div>
                        <div class="fl-row fl-al-cen">
                            <div class="arroe-right-small-1"></div>
                        </div>
                    </div>
                    <div class="fl-row fl-al-cen padd12 line_bottom marlr12" v-if="supportType !== 0 && !isHarmony">
                        <div class="f-1 font-14 weight-400 color-333">{{ supportType === 1 ? '指纹登录' : '人脸登录' }}</div>
                        <div class="fl-row fl-al-cen" @click="loginVerificeEvent()">
                            <!-- <switch checked color="#E64F22" style="transform:scale(0.7)" /> -->
                            <img class="img_style" src="../../image/switch_on.png" v-if="switchChecked" />
                            <img class="img_style" src="../../image/switch_off.png" v-else />
                        </div>
                    </div>
                    <div class="fl-row fl-al-cen padd12 marlr12" @click="headersClick('login')">
                        <div class="f-1 font-14 weight-400 color-333">查看登录设备</div>
                        <div class="fl-row fl-al-cen">
                            <div class="arroe-right-small-1"></div>
                        </div>
                    </div>

                    <div class="fl-row fl-al-cen padd12 marlr12" @click="headersClick('bind')" v-if="false">
                        <div class="f-1 font-14 weight-400 color-333">第三方账号绑定</div>
                        <div class="fl-row fl-al-cen">
                            <img src="../../image/wx.png" alt="" class="icon-20" />
                            <img src="../../image/zfb.png" alt="" class="icon-20 mal5" />
                            <div class="arroe-right-small-1 mal5"></div>
                        </div>
                    </div>
                    <!-- #endif -->
                </div>
                <div class="card-default back-btn" @click="outAcoount" v-if="isShowLogoff"> 账号注销 </div>
            </div>
            <zj-show-modal>
                <div class="fl-column fl-al-jus-cen font-14 color-333 weight-400" v-if="isMore">
                    <div class="fl-row fl-al-cen">1、{{ frequency }}</div>
                    <div class="fl-row fl-al-cen" v-if="couponHave">2、您当前账户下有未使用完的电子券，建议您使用完后进行变更手机号。</div>
                </div>
                <div class="fl-column fl-al-jus-cen font-14 color-333 weight-400" v-else>
                    <div class="fl-row fl-al-cen">您今年已变更手机号 2次，剩余更换次数为0，无法变更手机号。</div>
                </div>
            </zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userModifyPhoneTimesQuery, userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { mapGetters, mapState } from 'vuex';
import { cancelAccountApi } from '../../../../s-kit/js/v3-http/https3/user.js';
import { appJsonCouponUnusedcoupons } from '../../../../s-kit/js/v3-http/https3/wallet';
import { openLocalAuthLogin, closeLocalAuthLogin, openFingerOrFaceLogin } from '../../../../s-kit/js/v3-http/https3/user.js';
import Config from '../../../../s-kit/js/third-config.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            isMore: true,
            frequency: '',
            identityAuthStatus: '', //实人认证状态
            couponHave: false, //有没有未使用的电子券
            phone: '',
            switchChecked: false,
            token: '',
            supportType: 0, //设备支持的指纹或人脸类型 1指纹2人脸
            isShowLogoff: true, //是否显示注销账号入口
            newBiometric: false, //判断是否为3.0接口的人脸/指纹开通
        };
    },
    onLoad(option) {},
    async onShow() {
        await this.$store.dispatch('memberBaseInfoAction');
    },
    async created() {
        // #ifdef MP-MPAAS
        this.newBiometric = await this.$cnpcBridge.judgeProtocolCall('3.6.6');
        // #endif
    },
    async mounted() {
        // #ifndef MP-WEIXIN
        await this.$store.dispatch('memberBaseInfoAction');
        this.phone = this.memberBaseInfo.phone;
        // #endif
        // #ifdef MP-MPAAS
        let userInfo = await this.$cnpcBridge.getUserTokenInfo(); // 获取用户信息
        this.token = userInfo.token;
        this.getuserModifyPhoneTimesQuery(); //查询用户修改手机号次数是否超限
        this.getuserBasicInfoQuery(); //查询用户详细信息--获取用户认证状态
        this.getcouponList(); //查询有没有未使用的电子券
        if (!this.isHarmony) {
            this.supportType = await this.$cnpcBridge.getSupportType(); // 获取设备支持生物识别的类型
            if (this.supportType) {
                let vuexObj = await this.$cnpcBridge.getVuex2Info(); // 获取vuex2.0中的信息
                if (this.newBiometric) {
                    this.switchChecked =
                        vuexObj.thirdLocalAuthLoginInfo.thirdLoginState == 1 && vuexObj.thirdLocalAuthLoginInfo.thirdLocalAuthLoginId != ''
                            ? true
                            : false;
                } else {
                    this.switchChecked = vuexObj.userInfo.loginState == 1 ? true : false;
                }
            }
        }
        // 获取开关项
        // this.$cnpcBridge.getSwitch('account_logoff', value => {
        //     this.isShowLogoff = value === 'yes';
        // });
        // #endif
        // #ifndef MP-MPAAS
        if (JSON.stringify(Config.name).includes('-zfb')) {
            await this.$store.dispatch('memberBaseInfoAction');
            this.phone = this.memberBaseInfo.phone;
        }
        // #endif
    },
    methods: {
        // 指纹登录、人脸登录
        loginVerificeEvent() {
            this.$cnpcBridge.localAuthVerifica(async res => {
                if (res && Number(res)) {
                    let commonArgs = await this.$cnpcBridge.getCommonArgs();
                    if (this.newBiometric) {
                        this.newThirdLink(commonArgs);
                    } else {
                        this.oldLink(commonArgs);
                    }
                } else {
                    uni.showToast({ title: '验证失败' });
                }
            });
        },
        async newThirdLink(commonArgs) {
            if (this.switchChecked) {
                //已开通，需要调用关闭
                let params = {
                    type: 0,
                };
                let res = await openFingerOrFaceLogin(params);
                if (res.success) {
                    let vuexObj = await this.$cnpcBridge.getVuex2Info();
                    vuexObj.thirdLocalAuthLoginInfo.thirdLoginState = 0;
                    vuexObj.thirdLocalAuthLoginInfo.thirdLocalAuthLoginId = '';
                    vuexObj.userInfo.loginState = 0;
                    vuexObj.userInfo.localAuthLoginId = '';
                    this.$cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
                        uni.showToast({ title: '关闭成功' });
                        this.switchChecked = false;
                    });
                } else {
                    uni.showToast({ title: '关闭快捷登录异常' });
                }
            } else {
                //已关闭，需要调用开通
                let params = {
                    type: 1,
                };
                let res = await openFingerOrFaceLogin(params);
                if (res.success) {
                    let vuexObj = await this.$cnpcBridge.getVuex2Info();
                    console.log(vuexObj, 'vuexObj==========');
                    vuexObj.thirdLocalAuthLoginInfo.thirdLoginState = 1;
                    vuexObj.thirdLocalAuthLoginInfo.thirdLocalAuthLoginId = res.data.id;
                    this.$cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
                        uni.showToast({ title: '开通成功' });
                        console.log(vuexObj, 'vuexObj==22222========');
                        this.switchChecked = true;
                    });
                } else {
                    uni.showToast({ title: '开通快捷登录异常' });
                }
            }
        },
        async oldLink(commonArgs) {
            let params = {
                sysDeviceId: this.$cnpcUtils.encryption(commonArgs.deviceId),
            };
            if (this.switchChecked) {
                //已开通，需要调用关闭
                let res = await closeLocalAuthLogin(params);
                if (res.status == 0) {
                    let vuexObj = await this.$cnpcBridge.getVuex2Info();
                    vuexObj.userInfo.loginState = 0;
                    vuexObj.userInfo.localAuthLoginId = '';
                    this.$cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
                        uni.showToast({ title: '关闭成功' });
                        this.switchChecked = false;
                    });
                } else {
                    uni.showToast({ title: '关闭快捷登录异常' });
                }
            } else {
                //已关闭，需要调用开通
                let res = await openLocalAuthLogin(params);

                if (res.status == 0) {
                    let vuexObj = await this.$cnpcBridge.getVuex2Info();
                    vuexObj.userInfo.loginState = 1;
                    vuexObj.userInfo.localAuthLoginId = res.data;
                    this.$cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
                        uni.showToast({ title: '开通成功' });
                        this.switchChecked = true;
                    });
                } else {
                    uni.showToast({ title: '开通快捷登录异常' });
                }
            }
        },
        // 变更手机号、修改登录密码、查看登录设备、第三方账号绑定
        headersClick(data) {
            let url;
            let params = {};
            let type = 'navigateTo';
            if (data == 'phone') {
                let confirmText, cancelText;
                if (this.isMore) {
                    confirmText = '我知道了';
                    cancelText = '继续变更';
                } else {
                    cancelText = '';
                    confirmText = '确认';
                }
                let _this = this;
                this.$store.dispatch('zjShowModal', {
                    confirmText: confirmText,
                    cancelText: cancelText,
                    cancelColor: '#333333',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击取消');
                        } else if (res.cancel) {
                            if (_this.isMore) {
                                if (_this.couponHave) {
                                    //判断用户有无未使用的电子券
                                    url = '/packages/third-account/pages/change-phone-result/main'; //去电子券提示页
                                } else {
                                    if (_this.identityAuthStatus == 15) {
                                        url = '/packages/third-my-center/pages/real-person/main'; //去实人认证
                                    } else {
                                        url = '/packages/third-account/pages/account-phone-verify/main'; //去短信验证码验证
                                    }
                                }

                                _this.$sKit.layer.useRouter(url, params, type);
                            }
                        }
                    },
                });
            } else if (data == 'passowd') {
                url = '/packages/third-my-center/pages/replacement-verification/main';
                params.pageType = 1;
            } else if (data == 'login') {
                url = '/packages/third-my-center/pages/logged-device/main';
            } else if (data == 'bind') {
                url = '/packages/third-my-center/pages/tripartite-binding/main';
            } else if (data == 'forgotPassowd') {
                url = '/packages/third-my-center/pages/forgot-login-password/main';
            }

            this.$sKit.layer.useRouter(url, params, type);
        },
        //查询用户修改手机号次数是否超限
        async getuserModifyPhoneTimesQuery() {
            let res = await userModifyPhoneTimesQuery();
            if (res && res.success) {
                this.isMore = !res.data.result;
                if (this.isMore) {
                    this.frequency = res.data.msg;
                }
            }
        },
        //查询用户详细信息--获取用户认证状态
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.identityAuthStatus = res.data.identityAuthStatus;
            }
        },
        // 获取电子券
        async getcouponList() {
            let params = {
                pageNum: 1,
                pageSize: 10,
                categoryAlias: '',
                token: this.token,
                sortName: '',
                sortType: '',
            };

            let res = await appJsonCouponUnusedcoupons(params);
            if (res && res.data) {
                this.couponHave = res.data.length > 0 ? true : false;
            }
        },
        // 账号注销
        async outAcoount() {
            let url = '/packages/third-account/pages/account-cancellation/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    components: {},
};
</script>
<style scoped lang="scss">
.mar16 {
    margin: 16px 16px 0;
}

.marr6 {
    margin-right: 6px;
}

.padd12 {
    padding: 12px 0;
}

.marlr12 {
    margin: 0 12px;
}

.mal5 {
    margin-left: 5px;
}

.icon-20 {
    width: 20px;
    height: 20px;
}

.back-btn {
    height: 44px;
    line-height: 44px;
    font-size: 18px;
    font-weight: 500;
    color: #e64f22;
    text-align: center;
    margin: 16px 16px 0;
}

.img_style {
    width: 42px;
    height: 23px;
}
</style>
