<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <zj-navbar :height="44" title="订单详情" :border-bottom="false"></zj-navbar>
        <div class="content">
            <div class="show-title">请向加油员出示此页</div>
            <div class="qrCode-box box">
                <div class="qrCode-box-title">加油员【扫码完成】订单确认</div>
                <canvas v-if="!imagePath" canvas-id="myCanvas"></canvas>
                <img v-if="imagePath" :src="imagePath" class="qrcode" />
                <div v-if="result.voucher" class="qrCode-box-orderNo">{{ result.voucher.slice(-10) }}</div>
            </div>
            <div class="info-box box">
                <div class="info-box-title">实付金额</div>
                <div class="info-box-price"><span>&yen;</span>{{ result.actualPayTotalAmount }}</div>
                <div class="info-box-list">
                    <div class="list-item">
                        <div class="item-left item-value">支付时间</div>
                        <div class="item-right item-value">{{ result.payConfirmationTime }}</div>
                    </div>
                    <div class="list-item">
                        <div class="item-left item-value">订单金额</div>
                        <div class="item-right item-value">{{ result.orderTotalAmount }}元</div>
                    </div>
                    <div class="list-item">
                        <div class="item-left item-value">优惠金额</div>
                        <div class="item-right item-value">{{ result.discountTotalAmount }}元</div>
                    </div>
                    <div class="list-item">
                        <div class="item-left item-value">在线订单编号</div>
                        <div class="item-right item-value">{{ result.orderNo }}</div>
                    </div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import QRCode from 'qrcode';
import { mapState, mapGetters } from 'vuex';
import { clientCode } from '../../../../../project.config';
import { consumeOrderDetailApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {},
    data() {
        return {
            orderInfo: {},
            result: {},
            imagePath: '',
        };
    },
    onLoad(options) {
        this.orderInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
        this.resultPost();
    },
    mounted() {},
    onShow() {},
    methods: {
        offlineQRCodePageBiz() {
            const payMethod = this.result.payDetailList
                .map(item => {
                    const newItem = item.payMethodName;
                    return newItem;
                })
                .join(' ');
            this.$sKit.mpBP.tracker('后支付加油', {
                seed: 'hpayoilBiz',
                pageID: 'offlineQRCodePage', // 返回sdk标识
                refer: this.orderInfo.refer || '',
                channelID: clientCode,
                stationCode: this.result.stationCode,
                oilNumber: this.result.productName,
                oilGun: this.result.gunNo,
                payMethod: payMethod,
                xfAmount: this.result.actualPayTotalAmount,
                yhAmount: this.result.discountTotalAmount,
                couponsAmount: this.getDiscountAmount('优惠券'),
                zkAmount: this.getDiscountAmount('支付折扣'),
                hdSellAmount: this.getDiscountAmount('活动促销'),
                address: this.cityName || '',
            });
        },
        getDiscountAmount(key) {
            let value = '';
            if (this.result.discountList.length > 0) {
                value = this.result.discountList.find(item => item.payMethodName == key)?.payAmount || '';
            }
            return value;
        },
        async resultPost() {
            let res = await consumeOrderDetailApi({
                orderNo: this.orderInfo.orderNo,
                stationCode: this.orderInfo.stationCode,
            });
            if (res.success) {
                this.result = res.data || '';
                console.log(JSON.stringify(this.result), '订单信息');
                this.generateQRCode(this.result.voucher);
            }
        },
        generateQRCode(qrCode) {
            console.log(qrCode, 'qrCode=====');
            const canvasId = 'myCanvas';
            const ctx = uni.createCanvasContext(canvasId, this);
            ctx.setFillStyle('#00000000'); // 红色
            ctx.fillRect(0, 0, 300, 300); // 绘制一个红色矩形
            ctx.draw(false); // 提交绘制
            // 生成二维码的文本数据
            QRCode.toString(
                qrCode, // 二维码内容
                { type: 'utf8' }, // 配置选项
                (err, qrCodeText) => {
                    if (err) {
                        console.error('生成二维码失败', err);
                        return;
                    }

                    // 解析二维码文本数据并绘制到 Canvas
                    this.drawQRCodeToCanvas(ctx, qrCodeText, 300, 300);
                },
            );
        },
        drawQRCodeToCanvas(ctx, qrCodeText, width, height) {
            console.log('二维码文本数据:', qrCodeText);
            // 将 SVG 转换为 Base64 格式
            this.imagePath = `data:image/svg+xml;base64,${Buffer.from(qrCodeText).toString('base64')}`;
            console.log(this.imagePath, 'this.imagePath');
        },
    },
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
        }),
    },
};
</script>
<style scoped lang="scss">
.content {
    height: 100%;
    background-color: #f7f7fb;
    padding: 50rpx 32rpx 0;

    .show-title {
        font-size: 24px;
        color: #333;
        font-weight: bold;
        margin-bottom: 5rpx;
        text-align: center;
        line-height: 67rpx;
    }

    .box {
        margin-top: 38rpx;
        width: 100%;
        background: #ffffff;
        border-radius: 16rpx;
        padding: 32rpx 0 44rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .qrCode-box {
        padding: 32rpx 0 44rpx;

        .qrCode-box-title {
            font-weight: bold;
            font-size: 32rpx;
            color: #666666;
            line-height: 45rpx;
        }

        .qrcode {
            width: 100%;
            height: 550rpx;
        }

        .qrCode-box-orderNo {
            font-size: 36rpx;
            color: #666666;
            line-height: 50rpx;
        }
    }

    .info-box {
        padding: 32rpx 24rpx 20rpx;

        .info-box-title {
            font-weight: bold;
            font-size: 32rpx;
            color: #000000;
            line-height: 45rpx;
        }

        .info-box-price {
            font-weight: bold;
            font-size: 72rpx;
            color: #333333;
            line-height: 102rpx;
            display: inline;
            vertical-align: bottom;

            span {
                font-size: 48rpx;
            }
        }

        .info-box-list {
            margin-top: 22rpx;
            width: 100%;

            .list-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .item-value {
                    font-size: 28rpx;
                    line-height: 60rpx;
                }

                .item-left {
                    color: #333333;
                }

                .item-right {
                    color: #666666;
                }
            }
        }
    }
}
</style>
