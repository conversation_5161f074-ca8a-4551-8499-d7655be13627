<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="remaining_div fl-column bg-F2F3F5">
            <div class="remaining_div_top bg-fff">
                <div class="card_bg">
                    <img class="card_bg_img" :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt />
                    <!-- <img class="card_bg_img" v-else src="../../images/card-default.png" alt /> -->
                    <img class="mask_img" src="../../images/mask2.png" />
                </div>
                <zj-navbar
                    :background="{ background: 'rgba(255, 255, 255, 0)' }"
                    titleColor="#fff"
                    title="昆仑e享卡"
                    :border-bottom="false"
                ></zj-navbar>
            </div>
            <div class="scroll_div" :style="{ height: topNavbarHeight + 'px' }"></div>
            <div class="f-1 bg-F7F7FB fl-column">
                <zj-pull-down-refresh class="f-1" style="z-index: 10" @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="content_div fl-column mh-0">
                        <div class="card_top">
                            <div class="card_div">
                                <div class="card_div_top">
                                    <img class="card_img" :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt />
                                    <!-- <img v-else class="card_img" src="../../images/card-default.png" alt /> -->
                                    <!-- <img class="card_code" src="../../images/code.png" alt /> -->
                                </div>

                                <div class="card_info bg-fff">
                                    <div class="card_info_top fl-row fl-jus-bet fl-al-cen">
                                        <div class="card_name font-18 color-333 weight-bold">昆仑e享卡</div>
                                        <div class="frozen_amount font-14 color-999 fl-row">
                                            冻结金额(元)
                                            <div class="font-14 color-999 font-style">{{ walletInfo.freezeBalance || 0 }} </div>
                                        </div>
                                    </div>
                                    <div class="card_info_bot fl-row fl-jus-bet fl-al-cen">
                                        <div class="fl-column fl-wrap">
                                            <div class="font-15 color-999 title">卡余额（元）</div>
                                            <div class="font-20 color-333 amount font-style">{{ walletInfo.walletBalance || 0 }}</div>
                                        </div>
                                        <div class="recharge_btn font-15" @click="rechargeClick()">充值</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card_list fl-column">
                            <div class="fl-row fl-jus-bet fl-al-cen more_div">
                                <div class="font-16 color-666 weight-bold">交易明细</div>
                                <div class="data-list-title-left" @click="checkMore()">
                                    更多
                                    <div class="arrow_down_div">
                                        <img src="../../images/arrow_down.png" alt />
                                    </div>
                                </div>
                            </div>
                            <div class="bg-fff border-rad-8 info_list">
                                <div class="info_list_scroll" v-if="list.length > 0">
                                    <div v-for="(item, index) in list" :key="index" class="fl-row fl-wrap info_div">
                                        <div class="info_left fl-column">
                                            <div class="info_text fl-jus-bet fl-row">
                                                <div class="info_title font-15 color-333">{{ item.flowDirection }}</div>
                                                <div
                                                    class="info_amount font-15 fl-row font-style"
                                                    :style="{ color: item.amountChange > 0 ? '#FF6B2C' : '#333' }"
                                                >
                                                    <div v-if="item.amountChange > 0">+</div>
                                                    {{ item.amountChange }}
                                                </div>
                                            </div>
                                            <div class="info_time font-12 color-999">{{ item.time }}</div>
                                        </div>
                                    </div>
                                </div>
                                <zj-no-data
                                    v-if="list.length == 0"
                                    :emptyImage="require('../../images/kt10qb.png')"
                                    emptyText="暂未查询到昆仑e享卡明细"
                                ></zj-no-data>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { detailList } from '../../../../s-kit/js/v3-http/https3/wallet.js';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-remaining-sum',
    data() {
        return {
            systemInfo: '',
            topNavbarHeight: '',
            statusBarHeight: '',
            list: [],
            pageSize: 6,
            pageNum: 1,
            walletBalance: '', //钱包余额
            freezeBalance: '', //冻结余额
        };
    },
    onShow() {
        //获取存入的标识，然后进行删除
        const value = uni.getStorageSync('refreshWalletBlanaceFlag');
        uni.removeStorageSync('refreshWalletBlanaceFlag');
        //拿到标识进行页面刷新
        if (value) {
            this.$store.dispatch('getAccountBalanceAction');
            this.initData();
        }
    },
    onLoad(query) {},
    created() {},
    onShow() {
        this.$nextTick(() => {
            this.refreshPullDown();
        });
    },
    mounted() {
        //获取系统信息
        this.bgHrightStyle();
        // this.walletBalance = this.walletInfo.walletBalance
        // this.freezeBalance = this.walletInfo.freezeBalance
    },

    computed: {
        ...mapGetters(['walletSkin', 'walletInfo']),
    },
    methods: {
        //下拉刷新
        refreshPullDown() {
            this.$store.dispatch('getAccountBalanceAction');
            this.$store.dispatch('getCurrentImg', {
                callback: () => {
                    this.$refs.pullDownRefreshRef.stopRefresh();
                },
            });
            this.$refs.pullDownRefreshRef.stopRefresh();
            this.initData();
        },
        /**
         * @description  : 初始化数据
         * @param         {String} pageSize -页大小
         * @param         {String} pageNum -当前页
         * @param         {String} filter -筛选条件（null-全部；10-充值；1-支出）
         * @return        {*}
         */
        async initData() {
            let res = await detailList({ pageSize: this.pageSize, pageNum: this.pageNum, filter: 'null' });
            this.$refs.pullDownRefreshRef.stopRefresh();
            if (res && res.success && res.data) {
                this.list = res.data.accountInfoList;
            }
            console.log(res, '======detailList');
        },
        //查看更多
        checkMore() {
            this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/third-transaction-details/main', {}, 'navigateTo');
        },
        //充值按钮
        rechargeClick() {
            this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/third-wallet-recharge/main', { refer: 'r14' }, 'navigateTo');
        },
        //获取顶部高度
        bgHrightStyle() {
            // 获取系统宽高信息，根据公式转化为rpx
            let systemInfo = uni.getSystemInfoSync();
            // #ifdef MP-ALIPAY
            this.topNavbarHeight = systemInfo.statusBarHeight + systemInfo.titleBarHeight;
            // #endif
            this.topNavbarHeight = systemInfo.statusBarHeight + 44;

            console.log(systemInfo, this.topNavbarHeight, 'systemInfo=======');
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;

    // overflow: hidden;
    .remaining_div_top {
        height: 255px;
        width: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;

        .card_bg {
            height: 255px;
            // overflow: hidden;

            .card_bg_img {
                width: 140%;
                height: 145%;
                -webkit-filter: blur(28.5px);
                filter: blur(28.5px);
                position: absolute;
                top: -50px;
                left: -90px;
            }

            .mask_img {
                width: 100%;
                height: 100%;
                position: relative;
                top: 0;
            }
        }
    }

    .content_div {
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 10;

        // background: #f2f3f5;
        .card_top {
            .card_div {
                padding-top: 15px;

                .card_div_top {
                    width: 100%;
                    // padding: 0 18px;
                    height: 216px;
                    position: relative;
                    display: flex;
                    justify-content: center;

                    .card_img {
                        width: 342px;
                        height: 216px;
                        border-radius: 16px;
                    }

                    .card_code {
                        width: 32px;
                        height: 32px;
                        position: absolute;
                        bottom: 0;
                        right: 18px;
                    }
                }

                .card_info {
                    padding: 14px 32px 19px 32px;

                    .card_info_top {
                        .card_name {
                            line-height: 25px;
                        }

                        .frozen_amount {
                            line-height: 20px;

                            div {
                                margin-left: 8px;
                            }
                        }
                    }

                    .card_info_bot {
                        margin-top: 12px;

                        div {
                            .title {
                                line-height: 21px;
                            }

                            .amount {
                                line-height: 28px;
                                margin-top: 3px;
                            }
                        }

                        .recharge_btn {
                            width: 78px;
                            height: 37px;
                            border-radius: 200px;
                            border: 1px solid #ff6b2c;
                            color: #ff6b2c;
                            text-align: center;
                            line-height: 37px;
                        }
                    }
                }
            }
        }

        .card_list {
            padding: 0 12px 10px 12px;
            width: 100%;
            padding-top: 24px;
            background: #f2f3f5;

            .more_div {
                margin-bottom: 8px;
                padding: 0 12px 0 6px;
            }

            .info_list {
                margin-top: 8px;
                height: 414px;
                margin-bottom: 10px;

                .info_list_scroll {
                    // overflow-y: scroll;
                    height: 100%;
                    padding: 0 12px;

                    .info_div {
                        padding: 32rpx 0 27rpx;
                        border-bottom: 1px solid #eee;

                        .info_left {
                            width: 100%;

                            .info_text {
                                .info_title {
                                    line-height: 21px;
                                }
                            }

                            .info_time {
                                line-height: 17px;
                                margin-top: 2px;
                            }
                        }
                    }

                    .info_div:last-child {
                        border: none;
                    }
                }
            }
        }
    }
}

.data-list-title-left {
    font-size: 13px;
    color: #666;
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: 14px;

    img {
        margin-left: 5px;
        width: 16px;
        height: 16px;
    }
}

.arrow_down_div {
    width: 16px;
    height: 16px;
}
</style>
