<template>
    <div class="view" v-if="couponData">
        <div class="pure_top" :style="{ backgroundImage: 'url(' + naviBg + ')' }">
            <u-navbar
                :background="pageConfig.bgColor"
                :back-icon-size="40"
                :back-icon-color="pageConfig.titleColor.backIconColor"
                :height="44"
                :title-color="pageConfig.titleColor.color"
                back-text="电子券详情页"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>
            <!-- <u-navbar :border-bottom="false" :back-icon-size='40' :height='44' :background="none" :title-color="pageConfig.titleColor" title='电子券详情页'></u-navbar> -->
            <div class="view-content">
                <div class="coupon-content">
                    <div class="coupon-detail-wrap">
                        <div class="coupon-detail">
                            <!-- <div>{{couponData.imgUrl}}</div> -->
                            <img class="setimg" :src="couponData.imgUrl" alt v-if="couponData.imgUrl" />
                            <div class="title-time">
                                <!-- <div class="title" v-if="status == 0">{{`${couponData.faceValue}元`}}{{couponData.bizTypetext}}{{couponData.limit > 0? `（每人每天限使用${couponData.limit}张）`: ""}}</div>
                <div class="title" v-if="status == 1">{{`${couponData.facevalue}元${bizTypetext}`}}</div>-->
                                <div class="title-view">
                                    <div class="title" v-if="status == 0">{{ couponData.typeTitle }}</div>
                                    <div class="title" v-if="status == 1">
                                        {{ `${couponData.facevalue}元` }}
                                        <span class="biz-type" v-if="couponData.bizTypetext">{{ bizTypetext }}</span>
                                    </div>
                                    <div v-if="couponData.bizTypetext" :class="couponData.bizType == '5' ? 'bizType-yp' : 'bizType-fy'">{{
                                        couponData.bizTypetext
                                    }}</div>
                                </div>
                                <div class="time" v-if="status == 0 && couponData.couStartDate && couponData.couEndDate"
                                    >{{ couponData.couStartDate.replace(/\-/g, '.') }}-{{ couponData.couEndDate.replace(/\-/g, '.') }}</div
                                >
                                <div class="time" v-if="status == 1 && couponData.usedtime">{{ couponData.usedtime }}</div>
                            </div>
                            <img class="icon-coupon" src="@/static/icon-coupon1.png" alt v-if="status == 1" />
                        </div>
                    </div>
                    <div class="use-info" v-show="!newStationFlag">
                        <!-- 判断  checkCode  是否存在 存在展示二维码和条形码 -->
                        <!--  v-if="!newStationFlag && couponData.checkCode !== '' && couponData.checkCode!== undefined&& couponData.checkCode !== null " -->
                        <div>
                            <div
                                class="count-div"
                                v-if="
                                    status == 0 &&
                                    couponData.checkCode !== '' &&
                                    couponData.checkCode !== undefined &&
                                    couponData.checkCode !== null
                                "
                            >
                                <div class="item-count">{{ second }}</div>
                                <span class="item-icon">:</span>
                                <div class="item-count">{{ millisecond }}</div>
                            </div>
                            <div class="user-use-info" v-if="status != 0">
                                <div class="item">
                                    <div class="item-title">电子券号</div>
                                    <div class="item-content">{{ couponData.coucode }}</div>
                                </div>
                                <div class="item">
                                    <div class="item-title">面值金额</div>
                                    <div class="item-content">￥{{ couponData.facevalue }}</div>
                                </div>
                                <div class="item">
                                    <div class="item-title">交易单号</div>
                                    <div class="item-content">{{ couponData.tradeno }}</div>
                                </div>
                                <div class="item">
                                    <div class="item-title">交易站点</div>
                                    <div class="item-content">{{ couponData.tradeorg }}</div>
                                </div>
                                <div class="item">
                                    <div class="item-title">ESP流水号</div>
                                    <div class="item-content">{{ couponData.epsno }}</div>
                                </div>
                            </div>
                            <div class="txm-view" v-if="status == 1">
                                <div class="txm-text">条形码: {{ couponData.epsno }}</div>
                            </div>
                            <div
                                class="barcode-wrap"
                                v-if="
                                    status == 0 &&
                                    couponData.checkCode !== '' &&
                                    couponData.checkCode !== undefined &&
                                    couponData.checkCode !== null
                                "
                            >
                                <div class="qrcode-box" @click="updateClick">
                                    <canvas class="qrcode" canvas-id="qrcode"></canvas>
                                    <!-- <cover-image class="logo" src="@/static/user.png" alt=""></cover-image> -->
                                </div>
                                <!-- <div class="barcode-box">
                            <canvas class="barcode" canvas-id="barcode"></canvas>
                </div>-->
                                <!-- <div class="code-num" v-if="status == 0">{{couponCode}}</div> -->
                                <!-- <div class="code-num" v-if="status == 1">{{couponData.epsno}}</div> -->
                            </div>
                            <template v-if="status == 1">
                                <div class="qrcode-wrap">
                                    <div class="barcode-box">
                                        <canvas class="barcode" canvas-id="barcode"></canvas>
                                    </div>
                                </div>
                            </template>
                            <template
                                v-if="
                                    status == 0 &&
                                    couponData.checkCode !== '' &&
                                    couponData.checkCode !== undefined &&
                                    couponData.checkCode !== null
                                "
                            >
                                <div class="qrcode-wrap">
                                    <div class="barcode-box">
                                        <canvas class="barcode" canvas-id="barcode"></canvas>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="use-explain" v-if="status == 0">
                            <div class="title">使用说明</div>
                            <div class="content">
                                <rich-text :nodes="couponData.useDeclare"></rich-text>
                            </div>
                        </div>
                        <div
                            class="reminder-div"
                            v-if="
                                status == 0 &&
                                couponData.checkCode !== '' &&
                                couponData.checkCode !== undefined &&
                                couponData.checkCode !== null
                            "
                        >
                            <div class="title">温馨提示</div>
                            <div class="tip">为了保证本券的正常使用，当前页面会每1分钟自动刷新，如遇使用失败，可手动点击二维码“刷新”</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ZjNewStation
            class="new_station"
            @close="closeNewStationDialog"
            @submit="submitUpgrade"
            v-if="newStationFlag && status == 0"
        ></ZjNewStation>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import wxbarcode from 'wxbarcode';
import pageConfig from '../../../../utils/pageConfig';
import ZjNewStation from '@/components/zj-new-station/index.vue';
import { mapState } from 'vuex';
import { getUsedDetail, getUseableDetail, getCouponQrcode } from '@/api/home';
import projectConfig from '../../../../../project.config';
//倒计时时间 单位ms
// let time = 60000;
// let time = 60;
// let msTime = 999
//计时器
let timer = null;
// let msTimer = null

export default {
    data() {
        return {
            // 页面配置
            pageConfig,
            projectConfig,
            // 券号
            voucher: '',
            // 状态 0:未使用    1：已使用   2：已过期
            status: -1,
            //优惠券数据
            couponData: null,
            //生成二维码code
            couponCode: '',
            //优惠券title  status为1时使用
            bizTypetext: '',
            naviBg: '',
            //定时器
            timer: null,
            millisecond: 1000,
            second: 60,
            checkCode: '',
        };
    },
    async onLoad(options) {
        this.submitUpgrade = this.$util.throttleUtil(this.submitUpgrade);
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/bg.png', 'base64');
        this.voucher = this.$mp.query.voucher;
        this.status = this.$mp.query.status;
        this.bizTypetext = this.$mp.query.bizTypeText || '';
        console.log(options, 'options---');
        clearTimeout(timer);
        if (options.status == 0) {
            this.$store.dispatch('getSatationList', {
                callBack: () => {},
            });
        }

        this.getCouponDetailFun();
        if (this.status == 0) {
            this.getCouponQrcodeFun();
        }
        this.loadImages();
    },
    async onShow() {},
    onUnload() {
        // timer && clearInterval(timer);
        clearTimeout(timer);
    },
    methods: {
        async loadImages() {
            this.naviBg = await this.fetchAndConvertToBase64(this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/bg.png');
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        /**
         * 事件
         */
        //刷新事件
        async updateClick() {
            // timer && clearInterval(timer);
            clearTimeout(timer);

            if (this.status == 0) {
                await this.getCouponQrcodeFun();
                this.countDownfun();
                // this.msCountDown()
            }
        },
        /**
         * 方法
         */
        //获取优惠详情数据
        async getCouponDetailFun() {
            const { status, voucher } = this;
            let networkApi = [getUsedDetail, getUseableDetail][status];
            let res = await networkApi({
                voucher,
            });
            if (res.status == 0) {
                this.couponData = res.data;
                this.checkCode = res.data.checkCode;
                console.log(this.couponData, ' this.couponData');
                if (status == 1) {
                    this.$store.commit('setCloseDialog', false);

                    wxbarcode.barcode('barcode', this.couponData.epsno, 600, 145);
                }
            }
        },
        //获取动态码
        async getCouponQrcodeFun(isload = true) {
            // this.count = time;
            const { status, voucher } = this;
            let res = await getCouponQrcode(
                {
                    voucher,
                },
                isload,
            );
            if (res.status == 0) {
                this.couponCode = res.data;
                this.checkCode = res.data;
                this.$nextTick(() => {
                    wxbarcode.barcode('barcode', this.couponCode, 600, 145);
                    wxbarcode.qrcode('qrcode', this.couponCode, 398, 398);
                    // this.countDownfun()
                    this.second = 60;
                    this.millisecond = 1000;
                    this.countDownfun();
                });
            }
        },
        //定计时方法
        countDownfun() {
            clearTimeout(timer);
            timer = setInterval(() => {
                this.millisecond = this.millisecond - 50;
                if (this.millisecond <= 1000) {
                    if (this.millisecond === 0) {
                        this.millisecond = 1000;
                        this.second = this.second - 1;
                    }
                    if (this.second === 0) {
                        this.second = 60;
                        if (this.checkCode) {
                            this.getCouponQrcodeFun();
                        }
                    }
                }
            }, 40);

            // timer = setTimeout(() => {
            //   if (Number(this.count) === 0 && Number(this.msCount) === 0) {
            //     this.getCouponQrcodeFun()
            //   } else {
            //     if (Number(this.msCount) === 0) {
            //       this.count--
            //       this.msCount = 1000
            //     } else {
            //       let dou = this.msCount
            //       if (Number(this.msCount) < 100 && Number(this.msCount) > 60) {
            //         dou--
            //         this.msCount = '0' + dou
            //       } else if (Number(this.msCount) < 60) {
            //         dou--
            //         this.msCount = '00' + dou
            //       } else {
            //         this.msCount--
            //       }
            //     }
            //     this.countDownfun();
            //   }
            // },1);
        },
        // msCountDown(){
        //     clearTimeout(this.msTimer);
        //     this.msTimer = setTimeout(() => {
        //     if (this.msCount === 0) {
        //       this.getCouponQrcodeFun()
        //     } else {
        //       this.msCount -= 60;
        //       console.log('this.msCount',this.msCount)
        //     //   this.msCountDown();
        //     }
        //   }, 100);
        // },
        // countDownfun(){
        // 	timer = setInterval(async () => {
        //         await this.getCouponQrcodeFun(false)
        // 	}, time)
        // },
        // 关闭升级新站弹窗
        closeNewStationDialog() {
            this.$store.commit('setCloseDialog', false);
            // this.getCouponQrcodeFun()
        },
        // 立即升级
        async submitUpgrade() {
            this.$store.dispatch('getToken3', 'upgrade');
        },
    },
    computed: {
        ...mapState({
            newStationFlag: state => state.location.newStationFlag, // 打开升级弹窗
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            markerArr: state => state.location.markerArr, // marker数据
        }),
    },

    watch: {},
    components: {
        ZjNewStation,
    },
    beforeDestroy() {
        this.$store.commit('setCloseDialog', false);
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100vw;
    min-height: 100vh;
    background-color: #f6f6f6;

    .pure_top {
        width: 100%;
        // background-size: 375px 200px;
        // background-position: bottom right;
        background-size: 375px 168px;
        background-repeat: no-repeat;
        // padding: 20px 12px;
    }

    .view-content {
        // padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
        padding: 0 24rpx calc(20rpx + env(safe-area-inset-bottom));
        // margin-top: 45rpx;

        .coupon-content {
            background-color: #fff;
            border-radius: 10rpx;
            overflow: hidden;

            .coupon-detail-wrap {
                padding: 6rpx 20rpx 0;
                position: relative;

                &::after,
                &::before {
                    content: '';
                    position: absolute;
                    bottom: -10rpx;
                    width: 20rpx;
                    height: 20rpx;
                    border-radius: 50%;
                    background-color: #f6f6f6;
                }

                &::before {
                    left: -10rpx;
                }

                &::after {
                    right: -10rpx;
                }

                .coupon-detail {
                    padding-bottom: 6rpx;
                    // border-bottom: 1rpx solid #D6D6D6;
                    // border-bottom: 1rpx dotted #d6d6d6;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    .setimg {
                        width: 40px !important;
                        height: 40px;
                        border-radius: 2px;
                        margin-right: 3px;
                    }
                    .title-time {
                        flex: 1;
                        padding: 24rpx 0;
                        margin: 0 0 0 5px;
                        .title-view {
                            display: flex;
                            align-items: center;
                            flex-wrap: wrap;
                            // justify-content: space-between;
                            .title {
                                font-size: 32rpx;
                                color: #333;
                                font-weight: bold;
                                line-height: 45rpx;
                                word-break: break-all;
                                margin-right: 10px;
                            }
                            .bizType-yp {
                                background-color: #ffcccc;
                                font-size: 22rpx;
                                padding: 3px;
                                border-radius: 2px;
                                color: #ff0000;
                                // margin: 0 0 0 6px;
                                // width: 100px;
                                // height: 20px;
                            }
                            .bizType-fy {
                                background-color: #fff6ec;
                                padding: 3px;
                                border-radius: 2px;
                                font-size: 22rpx;
                                color: #ff8200;
                                // margin-left: 5px;
                                // width: 100px;
                                // height: 20px;
                            }
                        }

                        .xz-title {
                            font-size: 28rpx;
                            color: #909090;
                            line-height: 45rpx;
                        }
                        .biz-type {
                            opacity: 0.1;
                            background: #f96702;
                            border-radius: 4rpx;
                            color: #ff6010;
                            text-align: center;
                            width: 80rpx;
                            height: 30rpx;
                            line-height: 30rpx;
                        }

                        .time {
                            margin-top: 20rpx;
                            font-size: 24rpx;
                            color: #909090;
                            line-height: 33rpx;
                        }
                    }

                    .icon-coupon {
                        width: 155rpx;
                        height: 148rpx;
                    }
                }
            }

            .use-info {
                padding: 10rpx 30rpx 30rpx;
                border-top: 1rpx dotted #d6d6d6;
                .count-div {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .item-count {
                        width: 75px;
                        border: 1rpx solid #ff8200;
                        border-radius: 6rpx;
                        background-color: #fff6ec;
                        font-size: 48rpx;
                        font-weight: 500;
                        color: #ff8200;
                        padding: 10px;
                        text-align: center;
                    }
                    .item-icon {
                        color: #ff8200;
                        padding: 0 10px;
                        font-size: 36rpx;
                    }
                }

                .user-use-info {
                    padding-top: 20rpx;

                    .item {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 24rpx;
                        line-height: 48rpx;
                        color: #333;
                    }
                }

                .txm-view {
                    border-top: 1px dashed #d6d6d6;
                    margin-top: 30rpx;
                    padding: 40rpx 70rpx;
                    .txm-text {
                        background-color: #f6f6f6;
                        color: #999999;
                        font-size: 26rpx;
                        line-height: 90rpx;
                        border-radius: 10rpx;
                        text-align: center;
                    }
                }

                .barcode-wrap {
                    // padding-top: 50rpx;
                    .qrcode-box {
                        // border: 1rpx solid #333;
                        width: 398rpx;
                        height: 398rpx;
                        margin: 0 auto;
                        position: relative;

                        .qrcode {
                            width: 100%;
                            height: 100%;
                        }

                        // .logo{
                        //     width: 88rpx;
                        //     height: 88rpx;
                        //     border-radius: 50%;
                        //     position: absolute;
                        //     top: 50%;
                        //     left: 50%;
                        //     transform: translate(-50%, -50%);
                        // }
                    }

                    // .barcode-box{
                    //     // border: 1rpx solid #000;
                    //     width: 600rpx;
                    //     height: 145rpx;
                    //     margin: 0 auto;
                    //     .barcode{
                    //         width: 100%;
                    //         height: 100%;
                    //     }
                    // }
                    // .code-num{
                    //     font-size: 30rpx;
                    //     color: #333;
                    //     font-weight: bold;
                    //     line-height: 42rpx;
                    //     text-align: center;
                    // }
                }

                .qrcode-wrap {
                    padding-bottom: 50rpx;

                    .barcode-box {
                        // border: 1rpx solid #000;
                        width: 600rpx;
                        height: 145rpx;
                        margin: 0 auto;

                        .barcode {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    // .qrcode-box{
                    //     // border: 1rpx solid #333;
                    //     width: 398rpx;
                    //     height: 398rpx;
                    //     margin: 0 auto;
                    //     position: relative;
                    //     .qrcode{
                    //         width: 100%;
                    //         height: 100%;
                    //     }
                    //     .logo{
                    //         width: 88rpx;
                    //         height: 88rpx;
                    //         border-radius: 50%;
                    //         position: absolute;
                    //         top: 50%;
                    //         left: 50%;
                    //         transform: translate(-50%, -50%);
                    //     }
                    // }
                    .update-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .update-image {
                            width: 24rpx;
                            height: 28rpx;
                            margin-right: 20rpx;
                        }

                        .update-text {
                            font-size: 30rpx;
                            color: #333;
                        }
                    }
                }

                .use-explain {
                    // border-top: 1rpx dotted #d6d6d6;
                    // padding-top: 10rpx;

                    .title {
                        // padding: 10rpx 0;
                        font-size: 24rpx;
                        // font-size: 16px;
                        // color: #898989;
                        color: #333;
                        font-weight: bold;
                        line-height: 40rpx;
                    }

                    .content {
                        font-size: 24rpx;
                        color: #999;
                        line-height: 40rpx;
                        font-weight: bold;
                    }
                }

                .reminder-div {
                    // padding-top: 10rpx;

                    .title {
                        // padding: 10rpx 0;
                        font-size: 24rpx;
                        color: #333;
                        font-weight: bold;
                        line-height: 48rpx;
                    }

                    .tip {
                        font-size: 22rpx;
                        color: #999;
                        line-height: 30rpx;
                        // line-height: 40rpx;
                    }
                }
            }
        }
    }
}
</style>
