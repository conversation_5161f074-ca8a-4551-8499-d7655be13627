<template>
    <div class="add-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :custom-back="clickCustomBackBtn"
            back-text="添加油卡"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- <div class="oil-card-tab">
			<div @click='clickCardTab(0)' :class='tabSelectIndex == 0 ? "selectTab" : "normalTab"'>
				<img class="tab-image" v-if="tabSelectIndex == 0" src="@/static/tab-L.png">
				<div class='tab-text'>油卡绑定</div>
			</div>
			<div @click='clickCardTab(1)' :class='tabSelectIndex == 1 ? "selectTab" : "normalTab"'>
				<img class="tab-image" v-if="tabSelectIndex == 1" src="@/static/tab-R.png">
				<div class='tab-text'>在线开卡</div>
			</div>
    </div>-->
        <div class="section">
            <div class="table-view">
                <div class="table-view-cell">
                    <div class="table-title">请填写油卡信息</div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text">
                        <div class="left info-left">本人姓名</div>
                        <input class="center info-right" placeholder="请输入本人姓名" v-model.trim="oilInfo.realName" />
                    </div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text" @click="cardTypeshow = true">
                        <div class="left info-left">证件类型</div>
                        <div class="center place-txt">{{ cardTpye }}</div>
                        <div class="right table-view-cell-arrow"></div>
                    </div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text">
                        <div class="left info-left">证件号码</div>
                        <input class="center info-right" placeholder="请输入证件号码" v-model.trim="oilInfo.cardNumber" @blur="cardInput" />
                    </div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text">
                        <div class="left info-left info-left-marr">加油卡</div>
                        <input
                            class="center info-right"
                            placeholder="请输入本人加油卡卡号"
                            v-model.trim="oilInfo.oilcard"
                            @blur="oilInput"
                        />
                    </div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text">
                        <div class="left info-left-marr">验证码</div>
                        <input class="center info-right" placeholder="请输入验证码" maxlength="4" v-model.trim="oilInfo.codeNumber" />
                        <div class="right oil-money-info" @click="getNum" v-show="numShow">发送验证码</div>
                        <div class="right oil-money-info" v-show="!numShow">{{ count }}s</div>
                    </div>
                </div>
                <div class="table-view-cell">
                    <div class="table-view-cell-text">
                        <div class="left info-left-marr">设为默认卡片</div>
                        <div class="right oil-money-info">
                            <u-switch v-model="oilInfo.setDefaultCard" active-color="#FF8200"></u-switch>
                        </div>
                    </div>
                </div>
            </div>
            <div class="add-tip">
                温馨提示：验证码短信会发送到您的办卡预留手机上
                <!-- *验证码短信会发送到您的办卡预留手机上，请输入正确信息，以免造成损失。 -->
            </div>
        </div>
        <div class="footer">
            <!-- <div class="footer-btn" @click="oilInfobtn">{{tabSelectIndex == 0 ? '添加' : '提交'}}</div> -->
            <div class="footer-btn" @click="oilInfobtn">确认绑定</div>
        </div>
        <div class="popup">
            <u-popup v-model="cardTypeshow" mode="bottom" border-radius="20">
                <div class="popup-content">
                    <div class="popup-list" v-for="(item, index) in cardTypeList" :key="index" @click="selectCardType(item)">{{
                        item.cardType
                    }}</div>
                </div>
            </u-popup>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { querySetPayPassword } from '@/api/querySetPassword.js';
import { getOilCardPhoneCodePost, getOilCardSubmitPost } from '@/api/my-center';
import { mapState, mapGetters } from 'vuex';
export default {
    name: 'add-oilcard',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            oilInfo: {
                oilcard: '',
                cardNumber: '',
                phoneNumber: '',
                codeNumber: '',
                realName: '',
                // checked:true,
                setDefaultCard: 0,
            },
            numShow: true,
            count: '',
            timer: null,
            cardTypeshow: false,
            cardTpye: '身份证',
            idType: 1,
            cardTypeList: [
                {
                    cardType: '身份证',
                    idType: 1,
                },
                {
                    cardType: '军官证',
                    idType: 2,
                },
                {
                    cardType: '护照',
                    idType: 3,
                },
                {
                    cardType: '营业执照',
                    idType: 4,
                },
                {
                    cardType: '驾驶证',
                    idType: 5,
                },
                {
                    cardType: '组织机构代码证',
                    idType: 6,
                },
                {
                    cardType: '港澳通行证',
                    idType: 7,
                },
                {
                    cardType: '台胞证',
                    idType: 7,
                },
            ],
            tabSelectIndex: 0, // 顶部选中的index
            addCardFlag: '', // 用来解决油卡管理页面点击添加油卡绑定成功后返回油卡页面 点击左上角返回需要点击两次才会返回首页的标识
        };
    },
    computed: {
        ...mapState({
            showMarkerArr: state => state.location.showMarkerArr, // marker数据
        }),
    },
    onLoad(option) {
        console.log(this.showMarkerArr, '-----');
        console.log(
            (this.showMarkerArr[0].provinceName ? this.showMarkerArr[0].provinceName : '') +
                (this.showMarkerArr[0].cityName ? this.showMarkerArr[0].cityName : ''),
            'this.showMarkerArr',
        );
        this.oilInfobtn = this.$util.throttleUtil(this.oilInfobtn);
        this.addCardFlag = option.addCardFlag;
    },
    methods: {
        // 上方选项卡切换
        clickCardTab(index) {
            if (index == 1) {
                return uni.showToast({
                    title: '敬请期待',
                    icon: 'none',
                });
            }
            this.tabSelectIndex = 0;
        },
        // 选择卡片类型
        selectCardType(item) {
            this.cardTpye = item.cardType;
            this.idType = item.idType;
            console.log(this.idType, ' this.idType');
            this.cardTypeshow = false;
        },
        //获取验证码
        getNum: async function () {
            // console.log(this.idType, ' this.idType')
            // return
            // 校验名字
            if (this.oilInfo.realName === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.oilInfo.realName !== '' && !this.$test.checkName(this.oilInfo.realName)) {
                uni.showToast({
                    title: '姓名格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.oilInfo.cardNumber == '') {
                uni.showToast({
                    title: '请输入证件号码',
                    icon: 'none',
                });
                return;
            }
            if (this.oilInfo.oilcard == '') {
                uni.showToast({
                    title: '请输入油卡卡号',
                    icon: 'none',
                });
                return;
            }
            let params = {
                idType: this.idType,
                idNum: this.oilInfo.cardNumber,
                idName: this.oilInfo.realName,
                cardNo: this.oilInfo.oilcard,
                token: this.$store.state.token,
            };
            let res = await getOilCardPhoneCodePost(params);
            console.log(res);
            if (res) {
                uni.showToast({
                    title: '获取验证码成功',
                    icon: 'none',
                });
                const TIME_COUNT = 60;
                if (!this.timer) {
                    this.count = TIME_COUNT;
                    this.numShow = false;
                    this.timer = setInterval(() => {
                        if (this.count > 0 && this.count <= TIME_COUNT) {
                            this.count--;
                        } else {
                            this.numShow = true;
                            clearInterval(this.timer);
                            this.timer = null;
                        }
                    }, 1000);
                }
            }
        },
        //油卡号
        oilInput() {
            // // 校验名字
            // if (this.applyInfo.name === "") {
            //   uni.showToast({
            //     title: "姓名不能为空",
            //     icon: "none",
            //     duration: 2000,
            //   });
            //   return;
            // } else if (
            //   this.applyInfo.name !== "" &&
            //   !this.$test.checkName(this.applyInfo.name)
            // ) {
            //   uni.showToast({
            //     title: "姓名格式错误",
            //     icon: "none",
            //     duration: 2000,
            //   });
            //   return;
            // }
            if (this.oilInfo.oilcard == '') {
                uni.showToast({
                    title: '请输入油卡卡号',
                    icon: 'none',
                });
                return;
            } else if (this.$test.enOrNum(this.oilInfo.oilcard) == false) {
                uni.showToast({
                    title: '请输入正确格式油卡卡号',
                    icon: 'none',
                });
            }
        },
        // 证件输入
        cardInput() {
            // 身份证输入
            if (!this.oilInfo.cardNumber) {
                uni.showToast({
                    title: '请输入证件号码',
                    icon: 'none',
                });
                return;
            } else {
                if (this.cardTpye == '身份证' && this.$test.idCardComplex(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式身份证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '护照' && this.$test.checkPassport(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式护照号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '港澳通行证' && this.$test.checkGangAoTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式港澳通行证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '台胞证' && this.$test.checkTaiWanTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式台胞证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '军官证' && this.$test.checkTaiWanTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式军官证号',
                        icon: 'none',
                    });
                }
            }
        },
        //添加
        async oilInfobtn() {
            if (this.oilInfo.realName == '') {
                return uni.showToast({
                    title: '请输入姓名',
                    icon: 'none',
                });
            }
            if (!this.oilInfo.cardNumber) {
                uni.showToast({
                    title: '请输入证件号码',
                    icon: 'none',
                });
                return;
            } else {
                if (this.cardTpye == '身份证' && this.$test.idCardComplex(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式身份证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '护照' && this.$test.checkPassport(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式护照号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '港澳通行证' && this.$test.checkGangAoTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式港澳通行证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '台胞证' && this.$test.checkTaiWanTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式台胞证号',
                        icon: 'none',
                    });
                }
                if (this.cardTpye == '军官证' && this.$test.checkTaiWanTraffic(this.oilInfo.cardNumber) == false) {
                    return uni.showToast({
                        title: '请输入正确格式军官证号',
                        icon: 'none',
                    });
                }
            }
            if (this.oilInfo.oilcard == '') {
                return uni.showToast({
                    title: '请输入油卡卡号',
                    icon: 'none',
                });
            } else {
                if (this.$test.enOrNum(this.oilInfo.oilcard) == false) {
                    return uni.showToast({
                        title: '请输入正确格式油卡卡号',
                        icon: 'none',
                    });
                }
            }
            if (this.oilInfo.codeNumber == '') {
                return uni.showToast({
                    title: '请输入验证码',
                    icon: 'none',
                });
            }
            if (this.idType === 1) {
                // this.idType === 1 的时候就是身份证此时开卡调用人脸识别
                this.face();
            } else {
                // 如果不是身份证 正常调用开卡接口 不需要拉起微信人脸识别
                this.entityToOpenCard();
            }
        },
        // 人脸识别
        face() {
            wx.startFacialRecognitionVerify({
                name: this.oilInfo.realName, //姓名
                idCardNumber: this.oilInfo.cardNumber, //身份证号
                success: res => {
                    // 验证成功后触发
                    this.entityToOpenCard(res.verifyResult);
                    // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                    setTimeout(() => {
                        // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                        console.log(res);
                    }, 500);
                },
                fail: err => {
                    // 验证失败时触发
                    // err 包含错误码，错误信息，弹窗提示错误
                    setTimeout(() => {
                        console.log(err);
                        wx.showModal({
                            title: '提示',
                            content: err.ErrorMsg,
                            showCancel: false,
                        });
                    }, 500);
                },
            });
        },
        // 人脸识别通过后调用开卡接口
        entityToOpenCard(verifyResult) {
            wx.showLoading({
                title: '加载中',
                mask: true,
            });
            // this.payPasswordQuerySet()
            // return
            let orgName = '';
            if (this.showMarkerArr > 0) {
                orgName = this.showMarkerArr[0].provinceName
                    ? this.showMarkerArr[0].provinceName
                    : '' + this.showMarkerArr[0].cityName
                    ? this.showMarkerArr[0].cityName
                    : '';
            }
            let params = {
                verifyResult: verifyResult === undefined ? '' : verifyResult,
                idType: this.idType,
                idNum: this.oilInfo.cardNumber,
                idName: this.oilInfo.realName,
                cardNo: this.oilInfo.oilcard,
                verifyCode: this.oilInfo.codeNumber,
                setDefaultCard: this.oilInfo.setDefaultCard,
                orgName: orgName,
            };
            getOilCardSubmitPost(params).then(res => {
                if (res.status === 0) {
                    this.$store.dispatch('card/getAllCardList');
                    wx.hideLoading();
                    wx.showModal({
                        title: '提示',
                        content: `绑定成功`,
                        confirmText: '确定',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: res => {
                            if (res.confirm) {
                                // 查询是否设置支付密码
                                this.payPasswordQuerySet();
                            } else if (res.cancel) {
                                console.log('*************');
                                // 目前什么也不做处理
                            }
                        },
                    });
                } else {
                    wx.hideLoading();
                    wx.showModal({
                        title: '提示',
                        content: res.info,
                        confirmText: '确定',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: res => {
                            // this.$refs.windows.password = "";
                        },
                    });
                }
            });
        },
        // 查询是否设置支付密码
        payPasswordQuerySet() {
            let params = {
                idName: this.oilInfo.realName, //  姓名
                idType: this.idType, // 证件类型
                cardNo: this.oilInfo.oilcard, //  卡号
                idNum: this.oilInfo.cardNumber, // 身份证
            };
            console.log(params, '添加油卡');
            querySetPayPassword({ cardNo: this.oilInfo.oilcard }).then(res => {
                // 如果拿不到 res.status 的其他状态 就去https中加路径
                if (res.data) {
                    console.log(this.addCardFlag, '+++++++++++++++++++++++++');
                    // 如果设置了 就去油卡管理页面
                    if (this.addCardFlag === 'addCard') {
                        // 用来解决油卡管理页面点击添加油卡绑定成功后返回油卡页面 点击左上角返回需要点击两次才会返回首页的标识
                        uni.navigateBack({
                            //返回
                            delta: 1,
                        });
                    } else {
                        uni.redirectTo({
                            url: '/packages/oil-card/pages/manage-oilcard/main',
                        });
                    }
                } else {
                    // 如果没设置就去设置密码的引导页  携带参数跳转
                    uni.redirectTo({
                        url: `/packages/ecard-apply/pages/transferPage/main?params=${JSON.stringify(params)}&id=${1}`,
                    });
                }
            });
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
input {
    color: #333333;
}
.add-center {
    width: 100%;
    height: 100%;
    background: #f6f6f6;
    position: relative;
    overflow: hidden;

    .oil-card-tab {
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;
        display: flex;
        background-color: #f2f2f2;
        border-radius: 5px;
        .selectTab {
            height: 50px;
            width: 182px;
            font-size: 18px;
            color: $btn-color;
            font-weight: 700;
            position: relative;
        }
        .normalTab {
            height: 50px;
            flex: 1;
            font-size: 18px;
            color: #333333;
            font-weight: 700;
            position: relative;
        }
        .tab-image {
            height: 100%;
            width: 100%;
        }
        .tab-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .section {
        box-sizing: border-box;
        font-size: $font14;
        color: #909090;
        margin-left: 15px;
        width: 345px;
        margin-top: 10px;
        .table-view {
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            overflow: hidden;
        }
        .table-view-cell {
            padding: 13px 10px;
        }

        .table-title {
            font-size: 16px;
            color: #333333;
        }
        .info-left {
            font-weight: 600;
            color: #000;
            margin-right: 30px;
        }

        .info-left-mar {
            margin-right: 19px;
        }

        .info-left-marr {
            font-weight: 600;
            color: #000;
            margin-right: 44px;
        }

        .place-txt {
            color: #333333;
        }
    }

    .add-tip {
        font-size: 12px;
        font-weight: 400;
        // color: $btn-color;
        color: #999999;
        padding-top: 14px;
    }

    .oil-money-info {
        // color: $btn-color;
        // font-size: $font14;
        font-size: 17px;
        color: #4b6b99;
    }

    .footer {
        width: 100%;
        background: #ffffff;
        position: fixed;
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom);
        .footer-btn {
            width: 91%;
            height: 44px;
            background: $btn-color;
            border-radius: 5px;
            font-size: 15px;
            text-align: center;
            font-weight: bold;
            color: #ffffff;
            line-height: 44px;
            margin: 12px auto 9px;
        }
    }

    .popup-list {
        width: 100%;
        height: 50px;
        font-size: 15px;
        font-weight: bold;
        color: #222222;
        line-height: 50px;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-bottom: 1px solid #f3f3f3;
    }

    .popup-list:last-child {
        border-bottom: none;
    }
}
</style>
