<template>
    <view v-if="!isLogin">
        <!-- 毛玻璃 -->
        <div class="wrap">
            <!-- <img class="gaoshi-bendi" src="../../static/touming.png" alt /> -->
        </div>
        <div class="setBtn" v-if="frostedglassIsLoading">
            <u-circle-progress active-color="#f96702" :percent="80" bg-color="#00000000" duration="3000" width="150">
                <view class="u-progress-content">
                    <text class="u-progress-info">加载中</text>
                </view>
            </u-circle-progress>
        </div>
        <div class="setBtn" v-else>
            <get-phone-number @change="getPhoneNumber" open-type="getPhoneNumber" hover-class="none">
                <div class="btn">授权登录</div>
            </get-phone-number>
        </div>
    </view>
</template>

<script>
import GetPhoneNumber from '@/components/login/get-phone-number';
import { mapGetters } from 'vuex';

export default {
    name: '',
    data() {
        return {};
    },
    components: {
        GetPhoneNumber,
    },
    props: {},
    computed: {
        ...mapGetters(['isLogin', 'frostedglassIsLoading']),
    },
    watch: {
        deep: true,
        immediate: true,
        isLogin(val) {
            if (val) {
                this.isLogin = val;
            }
        },
    },
    onLoad() {
        console.log(this.isLogin);
        this.getPhoneNumber = this.$util.throttleUtil(this.getPhoneNumber);
    },
    methods: {
        getPhoneNumber(info) {
            console.log('授权登录');
            if (info.isLogin) {
                this.$emit('change');
            } else {
            }
            // this.$wxLogin
            // this.isLogin = true
            // this.handleReloadGetCouponList()
            // this.$parent.handleReloadGetCouponList()
        },
    },
};
</script>

<style scoped lang="scss">
/* 实现毛玻璃的样式 */

.wrap {
    /* 这一步设置是关键设置 */
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    backdrop-filter: blur(10px);
    z-index: 12;
}

.setBtn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 12;
    .btn {
        width: 120px;
        height: 50px;
        line-height: 50px;
        border-radius: 98rpx;
        color: #fff;
        background-color: #f96702;
    }
    .u-progress-content {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .u-progress-dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background-color: #fb9126;
    }

    .u-progress-info {
        font-size: 28rpx;
        padding-left: 16rpx;
        letter-spacing: 2rpx;
    }
}
</style>
