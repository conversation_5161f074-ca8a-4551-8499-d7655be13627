import { POST, POST2 } from '../../index';
import { api, apiGsms } from '../../../../../../project.config';
import { config } from 'process';

// 订单列表
export const orderListApi = (params, config = {}) => {
    config.apiVersion = 'v2';
    return POST('order.consume.list', params, config);
};
// 退款订单列表
export const refundOrderListApi = (params, config) => {
    return POST('order.consume.returnList', params, config);
};
// 退款订单详情
export const refundOrderDetailApi = (params, config) => {
    return POST('order.consume.returnDetail', params, config);
};
// 订单详情
export const orderDetailApi = (params, config) => {
    return POST('order.consume.detail', params, config);
};
// 查询待支付订单
export const unPaidOrderApi = (params, config) => {
    return POST('order.unPaidOrder.query', params, config);
};
// 发票关联订单详情接口 /order/consume/detailList
export const detailList = (params, config) => {
    return POST('order.consume.detailList', params, config);
};
// 发票关联订单详情接口 /order/consume/detailList
export const chargeDetailList = (params, config) => {
    return POST('charge.order.consume.detailList', params, config);
};
// 商城订单列表 /app/json/fuel/queryOrderList
export const queryOrderList = async (params, config) => {
    let url = '';
    // #ifdef MP-WEIXIN
    url = '/app/json/fuel/queryOrderList';
    return POST2(url, params, config);
    // #endif
    // #ifdef MP-MPAAS
    url = '/app/json/refuel/queryCSBOrderList';
    return POST2(url, params, config);
    // #endif
    // #ifdef MP-ALIPAY
    url = '/v1/queryShoppingOrder';
    params.currentPage = params.pageNum;
    delete params.pageNum;
    const res = await POST2(api + apiGsms + url, params, config);
    res.status = res.InfoCode == 1 ? 0 : 1;
    return res;
    // #endif
};

// 查询订单评价状态接口 /order/orderCommentFlag/query
export const orderCommentFlag = (params, config) => {
    return POST('order.orderCommentFlag.query', params, config);
};

//查询扫码评价配置评价选项列表接口 /order/QRCodeCommentConfig/list
export const QRCodeCommentConfigList = (params, config) => {
    return POST('order.QRCodeCommentConfig.list', params, config);
};

//[查询订单评价配置评价选项列表接口 /order/orderCommentConfig/list]
export const orderCommentConfigList = (params, config) => {
    return POST('order.orderCommentConfig.list', params, config);
};
// [订单评价接口 /order/userOrder/comment]
export const orderUserComment = (params, config) => {
    return POST('order.userOrder.comment', params, config);
};
// 扫码评价接口 /order/QRCode/comment
export const orderQRCodeComment = (params, config) => {
    return POST('order.QRCode.comment', params, config);
};
// 订单查看发票接口 order/consume/getInvoiceByOrderNo
export const getInvoiceByOrderNoApi = (params, config) => {
    return POST('order.consume.getInvoiceByOrderNo', params, config);
};
// 消费订单完成页的订单详情 /order/consumeOrder/detail
export const consumeOrderDetailApi = (params, config) => {
    return POST('order.consumeOrder.detail', params, config);
};
// [获取调查问卷配置信息接口 /user/satisfyQuestionConfig/query]
export const getQuestionConfigList = (params, config) => {
    return POST('user.satisfyQuestionConfig.query', params, config);
};
// [提交调查问卷接口 /user/satisfyQuestion/submit]
export const surveyQuestion = (params, config) => {
    return POST('user.satisfyQuestion.submit', params, config);
};
// [获取差评调查问卷配置信息接口 /user/negativeQuestionConfig/query]
export const getNegativeQuestionConfigList = (params, config) => {
    return POST('user.negativeQuestionConfig.query', params, config);
};
// [提交差评调查问卷接口 /user/negativeQuestion/submit]
export const surveyNegativeQuestion = (params, config) => {
    return POST('user.negativeQuestion.submit', params, config);
};
// [查询当前用户优惠账单已省总金额 /order/discountsBill/getTotalDiscounts]
export const getTotalDiscounts = (params, config) => {
    return POST('order.discountsBill.getTotalDiscounts', params, config);
};
// [获取会员升级3.0天数和日期 /user/getMemberUpgradedDayCount]
export const getMemberUpgradedDayCount = (params, config) => {
    return POST('user.getMemberUpgradedDayCount', params, config);
};
// [按月份进行订单总折扣统计-旧 /order/discountsBill/getDiscountsLastSixMonths]
export const getDiscountsLastSixMonth = (params, config) => {
    return POST('order.discountsBill.getDiscountsLastSixMonths', params, config);
};
// [按月份进行订单总折扣统计-新 /order/discountsBill/getDiscountsAllMonthsNew]
export const getDiscountsAllMonthsNew = (params, config) => {
    return POST('order.discountsBill.getDiscountsAllMonthsNew', params, config);
};
// [按月份查询优惠订单列表 /order/discountsBill/getDiscountsByMonth]
export const getDsicountsByMonth = (params, config) => {
    return POST('order.discountsBill.getDiscountsByMonth', params, config);
};

// 支付方式营销文案（e享卡、加油卡） /order/payment/marketing
export const paymentMarketing = (params, config) => {
    return POST('order.payment.marketing', params, config);
};
// 查询营销活动配置的距离信息接口 /order/businessActivityConfig/list
export const businessActivityConfigList = (params, config) => {
    return POST('order.businessActivityConfig.list ', params, config);
};

// 业务完成后查询营销活动信息接口 /order/businessActivity/query
export const businessActivityQuery = (params, config) => {
    return POST('order.businessActivity.query', params, config);
};

// 充电消费订单列表 /charge/order/consume/list
export const chargeOrderList = (params, config) => {
    return POST('charge.order.consume.list', params, config);
};

// 充电消费订单详情/charge/order/consume/details
export const chargeOrderDetails = (params, config) => {
    return POST('charge.order.consume.details', params, config);
};

// 充电消费退款订单列表/charge/order/consume/returnList
export const chargeOrderReturnList = (params, config) => {
    return POST('charge.order.consume.returnList', params, config);
}

// 充电消费退款订单详情/charge/order/consume/returnDetail
export const chargeOrderReturnDetail = (params, config) => {
    return POST('charge.order.consume.returnDetail', params, config);
}