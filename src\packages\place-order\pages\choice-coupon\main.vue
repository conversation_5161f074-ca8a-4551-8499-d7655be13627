<template>
    <div class="view">
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="选择优惠券"
            :back-text-style="pageConfig.titleStyle"
        ></u-navbar>
        <scroll-view class="coupon-scroll-view" scroll-y>
            <div class="coupon-list-item" v-for="(item, index) in couponArr" :key="index" @click="clickItem(item, 1)">
                <div class="coupon-title-view">
                    <img class="coupon-price-view" v-if="item.imgUrl" :src="item.imgUrl" alt />
                    <div v-else class="coupon-price-view unavailable-coupon-top"></div>
                    <!-- <div class="coupon-price-condition unavailable-coupon-bottom">
            {{ item.bizTypeText }}
                    </div>-->
                </div>
                <div class="coupon-price-detail">
                    <div class="coupon-price-title unavailable-coupon-top">{{ item.typeTitle }}</div>
                    <div class="coupon-price-title unavailable-coupon-top">{{ item.typeName }}</div>
                    <div class="coupon-price-time unavailable-coupon-bottom">有效期{{ item.couStartDate }}至{{ item.couEndDate }}</div>
                </div>
                <div class="coupon-use-btn">立即使用</div>
            </div>
            <div class="loadmore" v-if="noMore">
                <u-loadmore status="nomore" load-text="暂无更多数据" />
            </div>
        </scroll-view>
        <div class="btm-area">
            <div @click="clickItem(false, 2)">不使用电子券</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { getUseableList } from '@/api/home.js';
export default {
    data() {
        return {
            pageConfig, // 页面配置
            couponArr: [],
            page: 1,
            noMore: false,
            params: {},
        };
    },
    onLoad(option) {
        console.log(option);
        this.params = option;
        this.handleGetCouponList();
    },
    methods: {
        // 获取优惠券列表
        async handleGetCouponList() {
            let { data } = await getUseableList(this.params);
            this.couponArr = data;

            /* if(!data || data.length <= 0) {
        this.noMore = true
      } else {
        this.couponArr.concat(data)
      } */
        },
        // 获取下一页
        handleGetNextPage() {
            if (!this.noMore) {
                this.page++;
                this.handleGetCouponList();
            }
        },
        //优惠券点击事件
        clickItem(item, idx) {
            if (idx == 1) {
                uni.$emit('choseUnUsedCoupon', item);
            } else {
                uni.$emit('choseUnUsedCoupon', true);
            }
            uni.navigateBack();
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100vw;
    height: 100vh;
    background-color: #f6f6f6;
    display: flex;
    flex-direction: column;
    .coupon-scroll-view {
        min-height: 0;
        flex: 1;
        padding: 24rpx 0;
    }
    .coupon-list-item {
        margin: 0 24rpx 24rpx;
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: 16rpx;
        padding: 24rpx;
        .coupon-title-view {
            display: flex;
            align-items: center;
            flex-direction: column;
            .coupon-price-view {
                // color: $btn-color;
                // font-weight: 700;
                // display: flex;
                // align-items: flex-end;
                width: 130rpx;
                height: 130rpx;
                border-radius: 10rpx;
                background: linear-gradient(to bottom right, #223861, #1d4776);
            }
            .coupon-price-condition {
                color: $btn-color;
                padding-top: 3.5px;
                font-size: 12px;
            }
        }
        .coupon-price-detail {
            margin-left: 10px;
            flex: 1;
            .coupon-price-title {
                font-size: 15px;
                color: #333333;
                font-weight: 700;
                line-height: 24px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }
            .coupon-price-time {
                font-size: 12px;
                color: #909090;
                line-height: 24px;
            }
        }
        .coupon-use-btn {
            padding: 10rpx 24rpx;
            border: 1rpx solid #f96702;
            border-radius: 30rpx;
            background: #fff;
            font-size: 24rpx;
            color: #f96702;
            text-align: center;
        }
    }
    .loadmore {
        text-align: center;
        ::v-deep .u-load-more-wrap {
            background-color: transparent !important;
            view {
                background-color: transparent !important;
            }
        }
    }
    .btm-area {
        padding: 24rpx 24rpx env(safe-area-inset-bottom);
        div {
            width: 100%;
            padding: 24rpx 0;
            text-align: center;
            background: #f96702;
            color: #fff;
            font-size: 30rpx;
            margin-bottom: 20rpx;
            border-radius: 8px;
        }
    }
}
</style>
