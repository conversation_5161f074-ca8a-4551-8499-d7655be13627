
 import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
 import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
 
 export default {
     // #ifdef MP-TOUTIAO
     mounted() {
      console.log('ttt抖音')
     },
     methods: {
         /**
          * @description     : 退出登录
          * @return        {*}
          */
          async logOut() {
            let tokenInfo = await uni.getStorageSync('tokenInfo');
            if (tokenInfo?.accessToken) {
                let res = await logoutApi();
                if (res.success) {
                    uni.clearStorageSync();
                    this.$store.commit('setLongTimeNotLogin', null);
                    this.$sKit.layer.backHomeFun();
                }
            }
        },
     },
     // #endif
 };
 