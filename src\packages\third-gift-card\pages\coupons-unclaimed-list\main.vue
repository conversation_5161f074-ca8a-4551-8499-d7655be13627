<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <div class="view f-1 fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="待领取能源锦鲤"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <zj-data-list
                    ref="dataList"
                    emptyText="暂无数据"
                    :showEmpty="showEmpty"
                    :emptyImage="noPicture"
                    @scrolltolower="scrolltolower"
                    @refreshPullDown="refreshPullDown"
                    style="height: 100%"
                >
                    <div style="background: #f7f7fb" class="mart10">
                        <div class="list-wrap" v-for="(item, index) in cardBuyArray" :key="index">
                            <div class="item-module">
                                <div class="fl-row fl-jus-bet paddb13 fl-al-cen">
                                    <div class="f-1 fl-row">
                                        <img src="../../images/cnpc-logo.png" alt="" class="logo-img" />
                                        <div class="font-14 color-333 marl5 weight-500">{{ item.templetName }}</div>
                                    </div>
                                    <div class="status-div fl-row">
                                        <div class="fl-row fl-jus-bet">
                                            <div
                                                class="btn primary-btn border-rad-4 color-fff font-13 weight-400 marl10"
                                                @click="bindCard(item)"
                                                >绑定
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="fl-row fl-jus-bet fl-al-cen color-333 font-18 weight-bold paddb13" v-if="item.giftCardAsn">
                                    NO.{{ item.giftCardAsn }}
                                </div>
                                <div class="line"></div>
                                <div class="fl-row fl-jus-bet fl-al-cen">
                                    <div class="color-666 font-12 f-1">
                                        有效期：{{ formatTimeFun(item.effectTime) }}-{{ formatTimeFun(item.expireTime) }}
                                    </div>
                                    <div class="color-FF6B2C fl-row weight-500 fl-al-base">
                                        <div class="font-14">¥</div>
                                        <div class="font-18">{{ item.faceValue || '0.00' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div></div
                    >
                </zj-data-list>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import { buyGiftCardList, giftCardBind, unclaimedList } from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 是否展示空态标识
            showEmpty: false,
            // 暂无电子券图片
            noPicture: require('../../images/lpk-no-data.png'),
            cancelText: '取消',
            cancelColor: '#666',
            confirmText: '确认',
            confirmColor: '#000',
            cardPassword: '',
            // 查看卡密
            isCardPassword: false,
            // 页码
            page: 1,
            // 购卡列表
            cardBuyArray: [],
            // 总页码
            totalPage: 0,
            //礼品卡号
            giftCardNo: '',
            //卡密
            desString: '',
            cardType: 'unbind',
        };
    },
    onLoad(option) {
        this.getBuyGiftCardList();
    },
    async mounted() {},
    onShow() {},
    methods: {
        // 处理时间只展示年月日
        formatTimeFun(time) {
            if (!time) {
                return;
            }
            let date = time.split(' ')[0];
            date = `${date.split('-')[0]}年${date.split('-')[1]}月${date.split('-')[2]}日`;
            return date;
        },
        /**
         * @description  : 上拉加载查看用户名下购卡列表
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getBuyGiftCardList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            this.getBuyGiftCardList({ isInit: true });
        },
        // 查询用户名下待领取能源锦鲤列表接口
        async getBuyGiftCardList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    cardBuyArray: [],
                    page: 1,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, cardBuyArray, totalPage } = this;
            /***
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
                cardTypeNo: 12, // 卡片类型编号
            };
            let res = await unclaimedList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];

                // 将处理好的数组合并到定义的数组，放到页面渲染
                cardBuyArray = cardBuyArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    cardBuyArray,
                    page: Number(page) + 1,
                });
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && page >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = cardBuyArray.length <= 0 ? true : false;
            }
        },
        //绑定自己的能源锦鲤。
        async bindCard(item) {
            let params = {
                type: 3, // 卡号绑定
                giftCardNo: item.giftCardAsn, // 能源锦鲤卡号
            };
            let res = await giftCardBind(params);
            if (res.success) {
                this.$store.dispatch('zjShowModal', {
                    title: '绑定成功',
                    confirmText: '确认',
                    success: res => {
                        if (res.confirm) {
                            uni.navigateBack({
                                delta: 1, //返回的页面数
                            });
                        } else if (res.cancel) {
                        }
                    },
                });
            } else {
                this.$util.tipsToastNoicon('绑定失败');
            }
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.list-wrap {
    position: relative;
    width: 345px;
    margin: 0 auto 15px;

    .item-module {
        position: relative;
        min-height: 110px;
        overflow: hidden;
        background: #ffffff;
        border-radius: 8px;
        padding: 11px 11px 10px 13px;
        box-sizing: border-box;
        margin-bottom: 10px;

        .logo-img {
            width: 24px;
            height: 23px;
            flex-shrink: 0; /*防止被压缩*/
        }

        .status-div {
            .btn {
                width: 60px;
                height: 24px;
                line-height: 24px;
            }
        }
    }

    .marl5 {
        margin-left: 5px;
    }

    .marl10 {
        margin-left: 10px;
    }

    .paddb13 {
        padding-bottom: 13px;
    }
    .line {
        // border: 1px solid #EDEDED;
        margin-bottom: 14px;
        height: 1px;
        background: #ededed;
    }
}

._modal {
    flex: none;
    width: 280px;
    min-height: 227px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 18px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 147px;
        }

        .price-input-area {
            border-radius: 10px;
            height: 44px;

            .price-input {
                width: 100%;
                height: 100% !important;
                background: #f7f7fb;
                border-radius: 8px;
                text-align: center;
                box-sizing: border-box;
            }
        }
    }

    .copy-div {
        width: 29px;
        padding: 3px 0;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #999999;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        text-align: center;
        margin-left: 5px;
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }

    .padb18 {
        padding-bottom: 18px;
    }

    .padb11 {
        padding-bottom: 11px;
    }
}

.padt15 {
    padding-top: 15px;
}
.padt35 {
    padding-top: 35px;
}

.mart10 {
    margin-top: 10px;
}
</style>
