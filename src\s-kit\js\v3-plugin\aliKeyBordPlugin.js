import { singleton } from '../singleton';
import { app, baseType } from '../../../../project.config';

class aliKeyBordPlugin {
    // #ifdef MP-ALIPAY
    ref;
    sRef;

    constructor() {}

    initRef(ref, type = 'number') {
        this.sRef = ref;
        uni.showLoading({
            title: '加载中',
            mask: true,
        });
        const { gsmsToken, openId } = uni.getStorageSync('tokenInfo') || {};
        if (!gsmsToken || !openId) {
            console.log('账户插件未登录');
            uni.hideLoading();
            return;
        }
        console.log(gsmsToken || '', openId || '', type, baseType, '');
        return new Promise((resolve, reject) => {
            my.loadPlugin({
                plugin: `${app.zfbplugins.securityPlugin.provider}@*`,
                success: async () => {
                    try {
                        const securityUtilsPlugin = requirePlugin(`dynamic-plugin://${app.zfbplugins.securityPlugin.provider}`);
                        let result = await this.sRef.init(gsmsToken || '', openId || '', type, baseType);
                        console.log('ali keybord初始化', securityUtilsPlugin, result);
                        if (Number(result.code) !== 0) {
                            my.showToast({
                                title: result.msg,
                                icon: 'none',
                                duration: 2000,
                            });
                            uni.hideLoading();
                            return reject(result);
                        }
                        Object.assign(this, securityUtilsPlugin);
                        this.ref = this.sRef;
                        uni.hideLoading();
                        resolve(this.sRef);
                    } catch (err) {
                        uni.hideLoading();
                        console.log('ali keybord初始化失败', err);
                        reject(err);
                    }
                },
            });
        });
    }

    async reInitRef(type = 'number') {
        return this.ref || (await this.initRef(this.sRef, type));
    }

    // #endif
}

export default singleton(aliKeyBordPlugin);
