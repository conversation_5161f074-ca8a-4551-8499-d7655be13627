<template>
    <div class="p-bf fl-column" :style="{ position: 'relative', paddingTop: (pageType == 'cube' ? 10 + Number(systemBar) : 0) + 'px' }">
        <zj-navbar
            :border-bottom="false"
            v-if="pageType !== 'cube'"
            :title="pageType == 'page' ? '我的订单' : ''"
            :isBack="isBackHide"
        ></zj-navbar>
        <!-- 第一层tab -->
        <div class="nav-contetn">
            <div v-for="(item, index) in navList" :key="index" class="nav" @click="handelnavList(item.id, 'click')">
                <div class="item-name">
                    <div class="item" :class="{ active: navActive === item.id }">{{ item.name }}</div>
                </div>
            </div>
        </div>
        <!-- 第二层tab -->
        <!-- #ifndef H5-CLOUD -->
        <div class="nav-contetn borderB" :class="{ 'nav-contetn2': navActive == 1 }" v-if="secondNavList[navActive].length > 0">
            <div
                v-for="(item, index) in secondNavList[navActive]"
                :key="index"
                class="nav"
                :class="{ nav2: navActive == 1 }"
                @click="handelSecondNavList(item.id)"
            >
                <div class="item-name">
                    <div class="item" :class="{ active: listParams.secondNavActive == item.id }">{{ item.name }}</div>
                </div>
            </div>
        </div>
        <!-- #endif -->
        <!-- #ifdef H5-CLOUD -->
        <div
            v-if="secondNavList[navActive].length > 0 && navActive == 1"
            :class="{ 'nav-contetn2': navActive == 1 }"
            class="nav-contetn borderB"
        >
            <scroll-view class="scroll-view-container" scroll-x="true">
                <div
                    class="item-name"
                    v-for="(item, index) in secondNavList[navActive]"
                    :key="index"
                    :class="{ nav2: navActive == 1 }"
                    @click="handelSecondNavList(item.id)"
                >
                    <div class="item" :class="{ active: listParams.secondNavActive == item.id }">{{ item.name }}</div>
                </div>
            </scroll-view>
        </div>
        <div v-if="secondNavList[navActive].length > 0 && navActive == 2" class="nav-contetn borderB">
            <div
                v-for="(item, index) in secondNavList[navActive]"
                :key="index"
                :class="{ nav2: navActive == 1 }"
                class="nav"
                @click="handelSecondNavList(item.id)"
            >
                <div class="item-name">
                    <div class="item" :class="{ active: listParams.secondNavActive == item.id }">{{ item.name }}</div>
                </div>
            </div>
        </div>
        <!-- #endif -->
        <div class="refund-tip" v-if="navActive == 1 && listParams.secondNavActive == 9"
            >若需退款，请您携带消费凭证和原商品到消费加油站退款</div
        >
        <div class="search-content" v-if="navActive == 1 || navActive == 2 || navActive == 3">
            <div class="fl-row">
                <div @click="handelScreen" class="search-size"> <img src="./image/search.png" alt class="search-img" />筛选 </div>
                <!-- 卡筛选按钮 -->
                <div
                    class="wallet-order-title"
                    v-if="
                        isShowSelectCard &&
                        ((navActive == 1 && listParams.secondNavActive == 4) || (navActive == 2 && listParams.secondNavActive == 4))
                    "
                    @click="selectCard()"
                >
                    <div>{{ getCardName(listParams.refuelCardAccountNo) }}</div>
                    <img :class="{ down: !showSelectCard, top: showSelectCard }" src="./image/arrowD.png" alt />
                </div>
            </div>
            <!-- TODO @version? -->
            <!-- <div class="select-btn" v-if="navActive == 1 || navActive == 3" @click="selectOrder">批量开票</div> -->
        </div>
        <div class="f-1 mh-0 bg-F7F7FB" style="position: relative">
            <!-- 卡筛选列表弹窗 -->
            <div v-if="showSelectCard" class="select-content" :class="{ bottom0: pageType == 'page' }" @click="showSelectCard = false">
                <div class="select-box">
                    <div
                        class="select-box-item"
                        v-for="(item, index) in orderWalletList"
                        :key="index"
                        @click="handelOrderWalletActive(index, item.cardNo)"
                    >
                        <div class="left" :class="{ select: item.cardNo == listParams.refuelCardAccountNo }">
                            <img v-if="item.cardNo == listParams.refuelCardAccountNo" src="./image/card-select.png" alt />
                            <img v-else src="./image/card.png" alt />
                            {{ getCardName(item.cardNo) }}
                        </div>
                        <img v-if="item.cardNo == listParams.refuelCardAccountNo" class="right" src="./image/select.png" alt />
                    </div>
                </div>
            </div>
            <zj-data-list
                ref="dataList"
                @scrolltolower="scrolltolower"
                @refreshPullDown="refreshPullDown"
                :emptyImage="require('./image/kt4dd.png')"
                :emptyText="emptyText"
                :showEmpty="showEmpty"
                :pageType="pageType"
                v-if="isLogin"
            >
                <div class="containerWrap">
                    <orderList
                        v-if="navActive == 1 && listParams.secondNavActive != 9"
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        :secondNavActive="listParams.secondNavActive"
                        :pageType="pageType"
                        ref="orderList"
                    ></orderList>
                    <refundOrderList
                        v-if="navActive == 1 && listParams.secondNavActive == 9"
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        ref="refundOrderList"
                    ></refundOrderList>
                    <walletOrderList
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        v-if="navActive == 2"
                        ref="walletOrderList"
                    ></walletOrderList>
                    <chargeOrderList
                        v-if="navActive == 3 && listParams.secondNavActive != 9"
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        ref="chargeOrderList"
                    ></chargeOrderList>
                    <chargeRefundOrderList
                        v-if="navActive == 3 && listParams.secondNavActive == 9"
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        ref="chargeRefundOrderList"
                    ></chargeRefundOrderList>
                    <mallOrderList
                        @stopRefresh="stopRefresh"
                        @loadStatusChange="loadStatusChange"
                        @showEmptyChange="showEmptyChange"
                        ref="mallOrderList"
                        v-if="navActive == 4"
                    ></mallOrderList>
                </div>
            </zj-data-list>
            <div v-else class="noLoginPage">
                <img class="noLoginImg" src="./image/kt4dd.png" alt="" />
                <div class="noLoginText">登录后才可查看订单</div>
                <div class="noLoginBtn" :class="{ 'bg-opacity-288': !loginButtonGrayedOut }" @click="toLoginPage">去登录</div>
            </div>
        </div>
        <!-- 消费订单筛选页 -->
        <zj-popup ref="maskPopup" :maskClick="true" @maskClick="maskClose" type="bottom">
            <div class="isScreen">
                <div class="closeImgWrap fl-row">
                    <div>筛选</div>
                    <img @click="closePopup" src="./image/black-close.png" alt />
                </div>
                <div class="screen-dete title">消费时间</div>
                <div class="dete-content">
                    <div
                        v-for="(item, index) in deteList"
                        :key="index"
                        class="dete-item"
                        :class="{ isActive: isdeteActive === item.id }"
                        @click="handelIsdeteActive(index, item.id)"
                        >{{ item.name }}</div
                    >
                </div>
                <div class="calendar-dete">
                    <div class="picker">
                        <picker mode="date" :value="timeObj.startTime" @change="bindStartDateChange" fields="day">
                            <div class="picker-content">
                                {{ timeObj.startTime }}
                                <img src="./image/arrow-right.png" alt />
                            </div>
                        </picker>
                    </div>
                    <div class="flagstaff">-</div>
                    <div class="picker">
                        <picker mode="date" :value="timeObj.endTime" @change="bindEndDateChange" fields="day">
                            <div class="picker-content">
                                {{ timeObj.endTime }}
                                <img src="./image/arrow-right.png" alt />
                            </div>
                        </picker>
                    </div>
                </div>
                <!-- <div class="tips">可查询近2年内的消费订单</div> -->
                <div v-if="(navActive == 1 || navActive == 3) && listParams.secondNavActive != 9">
                    <template v-if="navActive == 1">
                        <div class="order-category title">订单品类</div>
                        <div class="category-content">
                            <div
                                v-for="(item, index) in orderCategoryList"
                                :key="index"
                                class="dete-item"
                                :class="{ isActive: orderProductType === item.id, 'width-data-item': index == 2 }"
                                @click="handelOrderCategoryActive(index, item.id)"
                                >{{ item.name }}</div
                            >
                        </div>
                    </template>
                    <div class="order-category title">开票状态</div>
                    <div class="category-content">
                        <div
                            v-for="(item, index) in invoiceStatusList"
                            :key="index"
                            class="dete-item"
                            :class="{ isActive: invoiceStatusActive === item.id }"
                            @click="handelInvoiceStatusActive(index, item.id)"
                        >
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <div class="btn">
                    <div class="reset" @click="dateReset('res')">重置</div>
                    <div class="confirm" @click="dateConfirm('ok')">确认</div>
                </div>
            </div>
        </zj-popup>
        <!-- 充值订单筛选页 -->
        <zj-popup ref="walletPopup" :maskClick="true" @maskClick="maskClose" type="bottom">
            <div class="isScreen">
                <div class="closeImgWrap fl-row">
                    <div>筛选</div>
                    <img @click="closePopup" src="./image/black-close.png" alt />
                </div>
                <div class="screen-dete title">充值时间</div>
                <div class="wallet-dete-content">
                    <div
                        v-for="(item, index) in walletDeteList"
                        :key="index"
                        class="wallet-dete-item"
                        :class="{ isActive: isdeteActive === item.id }"
                        @click="handelIsdeteActive(index, item.id)"
                        >{{ item.name }}</div
                    >
                </div>
                <div class="calendar-dete">
                    <div class="picker">
                        <picker mode="date" :value="timeObj.startTime" @change="bindStartDateChange" fields="day">
                            <div class="picker-content">
                                {{ timeObj.startTime }}
                                <img src="./image/arrow-right.png" alt />
                            </div>
                        </picker>
                    </div>
                    <div class="flagstaff">-</div>
                    <div class="picker">
                        <picker mode="date" :value="timeObj.endTime" @change="bindEndDateChange" fields="day">
                            <div class="picker-content">
                                {{ timeObj.endTime }}
                                <img src="./image/arrow-right.png" alt />
                            </div>
                        </picker>
                    </div>
                </div>
                <div class="tips">实体卡和昆仑e享卡只可查询近3个月内的充值订单</div>
                <div class="btn">
                    <div class="reset" @click="dateReset('res')">重置</div>
                    <div class="confirm" @click="dateConfirm('ok')">确认</div>
                </div>
            </div>
        </zj-popup>
        <zj-show-modal v-if="isHarmony"></zj-show-modal>
    </div>
</template>

<script>
import orderList from './components/orderList.vue';
import refundOrderList from './components/refundOrderList.vue';
import walletOrderList from './components/walletOrderList.vue';
import chargeOrderList from './components/chargeOrderList.vue';
import chargeRefundOrderList from './components/chargeRefundOrderList.vue';
import mallOrderList from './components/mallOrderList.vue';
import { cardList } from '../../../s-kit/js/v3-http/https3/oilCard/index.js';
import { mapGetters, mapState } from 'vuex';
import { clientCode } from '../../../../project.config';

export default {
    components: {
        orderList,
        refundOrderList,
        walletOrderList,
        chargeOrderList,
        chargeRefundOrderList,
        mallOrderList,
    },
    props: {
        // 页面类型 cube App一级web小程序页面   home 小程序一级页面   page二级页面
        pageType: {
            type: String,
            default: 'normal',
        },
        // 跳转到本页面后第一层tab初始值
        navActiveProp: {
            type: Number,
            default: 1,
        },
        // 跳转到本页面后第二层tab初始值
        secondNavActiveProp: {
            type: Number,
            default: 0,
        },
        // 跳转到本页面后卡筛选初始值
        refuelCardAccountNoProp: {
            type: String,
            default: '',
        },
        refer: {
            type: String,
            default: '',
        },
        isLogin: {
            type: Boolean,
            default: true,
        },
        systemBarNum: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            // 空态文本
            emptyText: '您目前没有任何订单',
            // 第一层tab配置数组
            // #ifdef H5-CLOUD
            navList: [
                { name: '消费订单', id: 1 },
                { name: '充值订单', id: 2 },
            ],
            // #endif
            // #ifndef H5-CLOUD
            navList: [
                { name: '消费订单', id: 1 },
                { name: '充值订单', id: 2 },
                // TODO @version?
                // { name: '充电订单', id: 3 },
                // { name: '商城订单', id: 4 },
            ],
            // #endif
            // 第二层tab配置数组，根据不同对象对应第一层tab渲染不同的第二层tab
            secondNavList: {
                // 消费订单
                1: [
                    { name: '全部', id: 0 },
                    { name: '待支付', id: 1 },
                    { name: '已完成', id: 4 },
                    { name: '已取消', id: 5 },
                    { name: '待取货', id: 8 },
                    { name: '待收货', id: 10 },
                    { name: '已退货', id: 9 },
                ],
                // 充值订单
                2: [
                    { name: '全部', id: '' },
                    { name: '已完成', id: 4 },
                    { name: '待支付', id: 1 },
                    { name: '已取消', id: 6 },
                ],
                // 充电订单
                3: [
                    { name: '全部', id: '0' },
                    { name: '充电中', id: '2' },
                    { name: '待结算', id: '1' },
                    { name: '已完成', id: '4' },
                    { name: '已退款', id: '9' },
                    { name: '已取消', id: '3' },
                ],
                // 商城订单
                4: [
                    { name: '全部', id: '' },
                    { name: '待支付', id: 'SUBMITED' },
                    { name: '待发货', id: 'PENDINGSHIP' },
                    { name: '待收货', id: 'PENDINGRECEIVE' },
                    { name: '待评价', id: 'PENDINGEVALUATION' },
                ],
                //订单类型1.全部：不传该字段 2.待支付：UNPAID 3.待发货：PENDINGSHIP 4.待收货：PENDINGRECEIVE 5.待评价：PENDINGEVALUATION
            },
            // 第一层tab按钮绑定值
            navActive: 1,
            // 消费订单时间筛选按钮配置数组
            deteList: [
                { name: '近1个月', id: 1 },
                { name: '近3个月', id: 3 },
                { name: '近6个月', id: 6 },
                { name: '近1年', id: 12 },
            ],
            // 充值订单时间筛选按钮配置数组
            walletDeteList: [
                { name: '近1个月', id: 1 },
                { name: '近3个月', id: 3 },
            ],
            // 时间筛选按钮绑定值
            isdeteActive: null,
            // 时间筛选范围绑定值
            timeObj: {
                startTime: '',
                endTime: '',
            },
            // 订单品类配置数组
            orderCategoryList: [
                { name: '油品', id: '3100' },
                { name: '非油品', id: '3150' },
                { name: '油品+非油品混合', id: '33' },
            ],
            // 油卡筛选配置数组
            orderWalletList: [],
            // 开票状态配置数组
            invoiceStatusList: [
                { name: '待开票', id: 3 },
                { name: '已开票', id: 2 },
                { name: '不可开票', id: 4 },
            ],
            // 订单品类绑定值
            orderProductType: '',
            // 开票状态绑定值
            invoiceStatusActive: 1,
            // 是否展示空态标识  true展示  false不展示
            showEmpty: false,
            // 消费订单时间筛选范围限制开始时间
            timeRangeStart: '',
            // 消费订单时间筛选范围限制结束时间
            timeRangeEnd: '',
            // 充值订单时间筛选范围限制开始时间
            walletTimeRangeStart: '',
            // 充值订单时间筛选范围限制结束时间
            walletTimeRangeEnd: '',
            // 是否展示卡筛选列表
            showSelectCard: false,
            // 接口入参集合
            listParams: {
                secondNavActive: 0,
                timeObj: {
                    startTime: '',
                    endTime: '',
                },
                orderProductType: '',
                invoiceStatusActive: 1,
                refuelCardAccountNo: 'all',
                isdeteActive: null,
            },
            // 是否展示卡筛选按钮
            isShowSelectCard: false,
            // App端顶部安全距离
            systemBar: '',
            isFirst: true,
        };
    },
    created() {
        // #ifdef MP-MPAAS
        // App端获取顶部安全距离
        console.log('pageType', this.pageType);
        this.$cnpcBridge.getBarHeight(res => {
            this.systemBar = res;
        });
        // #endif
        // #ifdef H5-CLOUD
        // 消费订单去掉待取货、待收货、已退货
        this.secondNavList[1] = this.secondNavList[1]?.filter(item => item.id !== 8 && item.id !== 10 && item.id !== 9);
        this.systemBar = Number(getApp().globalData.systemBar) + Number(28);
        // #endif
        this.toLoginPage = this.$sKit.commonUtil.throttleUtil(this.toLoginPage);
    },
    mounted() {
        uni.hideLoading();
        if (!this.isLogin) return;
        // 执行初始化操作
        this.init();
        // 创建页面展示监听，触发后清除筛选条件，重新获取数据
        // #ifndef MP-MPAAS
        this.$store.commit('setIsOrderDot', false);
        // #endif
    },
    watch: {
        // 卡筛选入参绑定值，值变更后
        // 当第一层tab为充值订单且第二层tab为待支付，且筛选卡绑定值为实体卡卡号时，修改空态文本
        // 当第一层tab为充值订单且第二层tab为已取消，且筛选卡绑定值为实体卡卡号时，修改空态文本
        'listParams.refuelCardAccountNo': {
            handler(newValue) {
                this.emptyText = '您目前没有任何订单';
                if (this.navActive == 2 && this.listParams.secondNavActive == 1) {
                    if (newValue != '' && newValue != 'all' && newValue != 'eCard') {
                        this.emptyText = '暂不支持查询实体卡的待支付订单';
                    }
                }
                if (this.navActive == 2 && this.listParams.secondNavActive == 8) {
                    if (newValue != '' && newValue != 'all' && newValue != 'eCard') {
                        this.emptyText = '暂不支持查询实体卡的已取消订单';
                    }
                }
            },
            deep: true,
        },
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
            // 登录按钮标识
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
        }),
        // 获取昆仑e享卡钱包状态
        ...mapGetters(['walletStatus']),
        // 支付宝隐藏返回按钮
        isBackHide() {
            if (this.pageType == 'home') {
                return false;
            } else {
                return true;
            }
        },
    },
    methods: {
        toLoginPage() {
            if (!this.loginButtonGrayedOut) return;
            let url = '';
            // #ifdef MP-WEIXIN
            url = `/packages/transferAccount/pages/home/<USER>/pages/thirdHome/main&curTabIndex=2`;
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/union/main?qKey=orderList';
            // #endif
            // #ifdef H5-CLOUD
            url = '/packages/third-cloud-login/pages/home/<USER>';
            // #endif
            this.$sKit.layer.useRouter(url);
        },
        bizAction() {
            if (this.navActive == 1) {
                if (this.listParams.secondNavActive == 0) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'xfOrderListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 1) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'paidListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 4) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'completedListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 5) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'cancelListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 8) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'pickListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 9) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'returnListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
            }
            if (this.navActive == 2) {
                if (this.listParams.secondNavActive == '') {
                    this.$sKit.mpBP.tracker('我的充值订单', {
                        seed: 'rechargeOrderBiz',
                        pageID: 'rechargeListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 4) {
                    this.$sKit.mpBP.tracker('我的充值订单', {
                        seed: 'rechargeOrderBiz',
                        pageID: 'completedListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 1) {
                    this.$sKit.mpBP.tracker('我的充值订单', {
                        seed: 'rechargeOrderBiz',
                        pageID: 'paidListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
                if (this.listParams.secondNavActive == 6) {
                    this.$sKit.mpBP.tracker('我的充值订单', {
                        seed: 'rechargeOrderBiz',
                        pageID: 'cancelListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                    return;
                }
            }
        },
        /**
         * @description  : 根据传入卡号，判断展示对应文案
         * @param         {String/Number} cardNo:当前选中的卡号（卡筛选绑定值）
         * @return        {String}展示到卡筛选按钮上的文本
         */
        getCardName(cardNo) {
            let text = '全部';
            if (cardNo == 'all') {
                text = '全部';
            } else if (cardNo == 'eCard') {
                text = '昆仑e享卡';
            } else if (cardNo == '') {
                text = '';
            } else {
                text = '实体卡 ' + cardNo;
            }
            return text;
        },
        /**
         * @description  :  初始化时间范围，筛选项，获取订单列表等
         * @return        {*}
         */
        init() {
            this.$store.dispatch('getSetWalletStatus');
            this.timeRangeStart = this.getDate('start');
            this.timeRangeEnd = this.getDate('end');
            this.walletTimeRangeStart = this.getDate('walletStart');
            this.walletTimeRangeEnd = this.getDate('end');
            this.handelnavList(this.navActiveProp, 'init');
            if (!this.secondNavActiveProp) {
                if (this.navActiveProp == 1 || this.navActiveProp == 3) {
                    this.handelSecondNavList(0, 'init');
                } else if (this.navActiveProp == 2 || this.navActiveProp == 4) {
                    this.handelSecondNavList('', 'init');
                }
            } else {
                this.handelSecondNavList(this.secondNavActiveProp, 'init');
            }

            this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            // this.$store.commit('setLoadingStatus', false);
        },
        /**
         * @description  : 获取实体卡列表，并获取订单列表
         * @return        {*}
         */
        async getCardList(type) {
            let res = await cardList({}, { isload: false, isCustomErr: true });
            let arr = [];
            if (res && res.success) {
                let cardList = res.data.rows || [];
                if (this.walletStatus.status) {
                    if (this.walletStatus.accountStatus == 1) {
                        if (this.navActive == 1) {
                            arr = arr.concat({ cardNo: 'all' });
                        }
                      // #ifndef H5-CLOUD
                      arr = arr.concat({ cardNo: 'eCard' });
                         arr = arr.concat(cardList);
                        // #endif
                        // #ifdef H5-CLOUD
                        if (this.navActive == 1) {
                            arr = arr.concat(cardList);
                        }
                        // #endif
                        this.isShowSelectCard = true;
                    } else if (this.walletStatus.accountStatus == 2) {
                        if (cardList.length > 0) {
                            if (this.navActive == 1) {
                                arr = arr.concat({ cardNo: 'all' });
                            }
                            // #ifndef H5-CLOUD
                            arr = arr.concat(cardList);
                            // #endif
                            // #ifdef H5-CLOUD
                            if (this.navActive == 1) {
                                arr = arr.concat(cardList);
                            }
                            // #endif
                            this.isShowSelectCard = true;
                        } else {
                            this.isShowSelectCard = false;
                        }
                    } else if (this.walletStatus.accountStatus == 3) {
                        if (cardList.length > 0) {
                            if (this.navActive == 1) {
                                arr = arr.concat({ cardNo: 'all' });
                            }
                            // #ifndef H5-CLOUD
                            arr = arr.concat(cardList);
                            // #endif
                            // #ifdef H5-CLOUD
                            if (this.navActive == 1) {
                                arr = arr.concat(cardList);
                            }
                            // #endif
                            this.isShowSelectCard = true;
                        } else {
                            this.isShowSelectCard = false;
                        }
                    }
                } else {
                    this.isShowSelectCard = false;
                }
            } else {
                if (this.walletStatus.status) {
                    if (this.walletStatus.accountStatus == 1) {
                        if (this.navActive == 1) {
                            arr = arr.concat({ cardNo: 'all' });
                        }
                        arr = arr.concat({ cardNo: 'eCard' });
                        // #ifndef H5-CLOUD
                        arr = arr.concat(cardList);
                        // #endif
                        // #ifdef H5-CLOUD
                        if (this.navActive == 1) {
                            arr = arr.concat(cardList);
                        }
                        // #endif
                        this.isShowSelectCard = true;
                    } else if (this.walletStatus.accountStatus == 2) {
                        this.isShowSelectCard = false;
                    } else if (this.walletStatus.accountStatus == 3) {
                        this.isShowSelectCard = false;
                    }
                } else {
                    this.isShowSelectCard = false;
                }
            }
            this.orderWalletList = arr;
            if (this.refuelCardAccountNoProp !== '') {
                // #ifndef H5-CLOUD
                this.listParams.refuelCardAccountNo = this.refuelCardAccountNoProp;
                // #endif
                // #ifdef H5-CLOUD
                if (this.navActive == 2) {
                    this.listParams.refuelCardAccountNo = 'eCard';
                } else {
                    this.listParams.refuelCardAccountNo = this.refuelCardAccountNoProp;
                }
                // #endif
            } else {
                if (this.navActive == 1) {
                    this.listParams.refuelCardAccountNo = 'all';
                } else if (this.navActive == 2) {
                    if (this.walletStatus.accountStatus == 1) {
                        this.listParams.refuelCardAccountNo = 'eCard';
                    } else {
                        if (this.orderWalletList.length > 0) {
                            this.listParams.refuelCardAccountNo = this.orderWalletList[0].cardNo;
                        } else {
                            this.listParams.refuelCardAccountNo = '';
                        }
                    }
                }
            }
            this.refreshPullDown();
        },
        /**
         * @description  : 第一层tab点击事件，并初始化第二层tab按钮绑定值
         * @param         {*} id: 第一层tab按钮对应的id
         * @param         {*} type: init初始化：不能初始化第二层tab，不能获取订单列表    click点击：可初始化第二层tab，可获取订单列表
         * @return        {*}
         */
        handelnavList(id, type) {
            this.navActive = id;
            if (!this.isLogin) return;
            this.dateReset('handelRes');
            if (type != 'init') {
                this.listParams.secondNavActive = this.secondNavList[`${this.navActive}`][0].id;
                this.bizAction();
            }
            this.listParams.refuelCardAccountNo = '';
            this.showSelectCard = false;

            if (this.navActive == 1 || this.navActive == 3) {
                this.handelIsdeteActive(null, 24); // 默认时间两年
                this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            } else if (this.navActive == 2) {
                this.handelIsdeteActive(null, 3); // 默认时间3月
                this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            }
            if (
                (this.navActive == 1 && this.listParams.secondNavActive == 4) ||
                (this.navActive == 2 && this.listParams.secondNavActive == 4)
            ) {
                this.getCardList(type);
            } else {
                if (type != 'init') {
                    this.refreshPullDown();
                }
            }
        },
        /**
         * @description  : 第二层tab点击事件，重置筛选项，并根据筛选项判断，走直接获取订单列表或 现获取实体卡列表再获取订单列表
         * @param         {*} id:第二层tab按钮对应的id
         * @param         {*} type:  init 初始化
         * @return        {*}
         */
        handelSecondNavList(id, type) {
            this.listParams.secondNavActive = id;
            if (!this.isLogin) return;
            this.dateReset('handelRes');
            this.showSelectCard = false;
            this.listParams.refuelCardAccountNo = '';
            if (
                (this.navActive == 1 && this.listParams.secondNavActive == 4) ||
                (this.navActive == 2 && this.listParams.secondNavActive == 4)
            ) {
                this.$refs.dataList.loadStatus = 'loading';
                if (this.navActive == 1) {
                    this.$nextTick(() => {
                        this.$refs.orderList.orderList = [];
                    });
                } else if (this.navActive == 2) {
                    this.$nextTick(() => {
                        this.$refs.walletOrderList.orderList = [];
                    });
                }
                this.getCardList(type);
            } else {
                this.refreshPullDown();
            }
            this.bizAction();
        },
        /**
         * @description  : 根据不同筛选项打开不同筛选弹窗
         * @return        {*}
         */
        handelScreen() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.showSelectCard = false;
            if (this.navActive == 1 || this.navActive == 3) {
                if (this.navActive == 1) {
                    this.$sKit.mpBP.tracker('我的消费订单', {
                        seed: 'xfOrderBiz',
                        pageID: 'screenBut',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                this.$refs.maskPopup.open();
            } else if (this.navActive == 2) {
                this.$sKit.mpBP.tracker('我的充值订单', {
                    seed: 'rechargeOrderBiz',
                    pageID: 'screenBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
                this.$refs.walletPopup.open();
            }
        },
        /**
         * @description  : 打开下拉选择框，选卡
         * @return        {*}
         */
        selectCard() {
            if (!this.showSelectCard && this.navActive == 2) {
                this.$sKit.mpBP.tracker('我的充值订单', {
                    seed: 'rechargeOrderBiz',
                    pageID: 'eCard_screenBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            this.showSelectCard = !this.showSelectCard;
        },
        /**
         * @description  : 批量选择跳转
         * @return        {*}
         */
        selectOrder() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            // 批量开票按钮点击次数埋点
            // #ifdef MP-MPAAS
            this.$cnpcBridge.setClickEvent('go_to_invoice');
            // #endif
            let url = '/packages/third-invoice/pages/home/<USER>';
            let params = {};
            if (this.navActive == 1) {
                this.$sKit.mpBP.tracker('我的消费订单', {
                    seed: 'xfOrderBiz',
                    pageID: 'plInvoicingBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
                params = {
                    refer: 'r32',
                };
            } else if (this.navActive == 3) {
                params = {
                    refer: 'r32',
                    orderType: 'charge',
                };
            }
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 日期选择开始时间改变事件，重置时间按钮重置
         * @param         {Object} e:时间选择组件实例
         * @return        {*}
         */
        bindStartDateChange(e) {
            this.isdeteActive = null;
            this.listParams.isdeteActive = null;
            this.timeObj.startTime = e.target.value.replace(/\//g, '-');
        },
        /**
         * @description  : 日期选择结束时间改变事件，重置时间按钮重置
         * @param         {*} e:时间选择组件实例
         * @return        {*}
         */
        bindEndDateChange(e) {
            this.isdeteActive = null;
            this.listParams.isdeteActive = null;
            this.timeObj.endTime = e.target.value.replace(/\//g, '-');
        },
        /**
         * @description  : 处理时间筛选按钮和时间范围的交互，处理时间格式
         * @param         {*} index:时间范围按钮序号
         * @param         {*} number:时间范围按钮对应值
         * @return        {*}
         */
        handelIsdeteActive(index, number) {
            this.isdeteActive = number;
            var date = new Date();
            var year = date.getFullYear(); // 年
            var month = date.getMonth() + 1; // 月
            var day = date.getDate(); // 日
            this.timeObj.endTime = this.getDate();
            month = month - (number % 12);
            year = year - parseInt(number / 12);
            day = day + 1;
            var lastDay = new Date(year, month, 0).getDate();
            if (day > lastDay) {
                day = 1;
                month += 1;
            }
            if (month > 12) {
                month = 12 - month;
                year += 1;
            }
            if (month <= 0) {
                month = month + 12;
                year = year - 1;
            }
            if (month >= 0 && month <= 9) {
                month = '0' + month;
            }
            if (day >= 0 && day <= 9) {
                day = '0' + day;
            }
            this.timeObj.startTime = year + '-' + month + '-' + day;
        },
        /**
         * @description  : 订单品类选择事件
         * @param         {*} index:订单品类选择按钮序号
         * @param         {*} id:订单品类选择按钮对应值
         * @return        {*}
         */
        handelOrderCategoryActive(index, id) {
            this.orderProductType = id;
        },
        /**
         * @description  : 选择筛选卡号事件
         * @param         {*} index:筛选卡号列表序号
         * @param         {*} id:筛选卡号列表卡号
         * @return        {*}
         */
        handelOrderWalletActive(index, id) {
            this.listParams.refuelCardAccountNo = id;
            // if (this.navActive == 1) {
            //     this.handelIsdeteActive(null, 24); // 默认时间两年
            // } else if (this.navActive == 2) {
            //     this.handelIsdeteActive(null, 3); // 默认时间两年
            // }
            // this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            this.refreshPullDown();
            this.showSelectCard = false;
        },
        /**
         * @description  : 选择开票状态事件
         * @param         {*} index:开票状态按钮序号
         * @param         {*} id:开票状态按钮对应值
         * @return        {*}
         */
        handelInvoiceStatusActive(index, id) {
            this.invoiceStatusActive = id;
        },
        /**
         * @description  : 获取并处理当前时间
         * @param         {*} type:时间类型 start为获取时间范围开始时间，当前时间两年前；end为获取时间范围结束时间，当前时间
         * @return        {*} yyyy-MM-DD年月日时间
         */
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();

            if (type === 'start') {
                year = year - 2;
                day += 1;
            } else if (type === 'walletStart') {
                day += 1;
                month -= 3;
                if (month <= 0) {
                    month += 12;
                    year -= 1;
                }
            } else if (type === 'end') {
            }
            var lastDay = new Date(year, month, 0).getDate();
            if (day > lastDay) {
                day = lastDay;
                month += 1;
            }
            if (month > 12) {
                year + 1;
            }
            month = month > 9 ? month : '0' + month;
            day = day > 9 ? day : '0' + day;
            return `${year}-${month}-${day}`;
        },
        /**
         * @description  : 关闭筛选弹窗，如未点击确认绑定值恢复开启弹窗时的数据
         * @return        {*}
         */
        closePopup() {
            this.timeObj = JSON.parse(JSON.stringify(this.listParams.timeObj));
            this.orderProductType = this.listParams.orderProductType;
            this.invoiceStatusActive = this.listParams.invoiceStatusActive;
            this.isdeteActive = this.listParams.isdeteActive;
            this.$refs.maskPopup.close();
            this.$refs.walletPopup.close();
        },
        /**
         * @description  : 点击遮罩关闭弹窗触发事件，如未点击确认绑定值恢复开启弹窗时的数据
         * @return        {*}
         */
        maskClose() {
            this.timeObj = JSON.parse(JSON.stringify(this.listParams.timeObj));
            this.orderProductType = this.listParams.orderProductType;
            this.invoiceStatusActive = this.listParams.invoiceStatusActive;
            this.isdeteActive = this.listParams.isdeteActive;
        },
        /**
         * @description  : 重置筛选
         * @param         {*} type:handelRes tab切换触发重置筛选   ok 回到订单页面触发重置筛选并刷新列表  res 点击重置按钮重置
         * @return        {*}
         */
        dateReset(type) {
            if (type == 'onShow' && this.isFirst) {
                this.isFirst = false;
                return;
            }
            this.orderProductType = '';
            this.invoiceStatusActive = 1;
            if (this.navActive == 1 || this.navActive == 3) {
                this.handelIsdeteActive(null, 24);
            } else if (this.navActive == 2) {
                this.handelIsdeteActive(null, 3);
            }
            this.dateConfirm(type);
        },
        /**
         * @description  : 确认筛选，将筛选绑定值赋给接口入参集合
         * @param         {*} type:handelRes tab切换触发重置筛选   ok 回到订单页面触发重置筛选并刷新列表  res 点击重置按钮重置
         * @return        {*}
         */
        dateConfirm(type) {
            this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            this.listParams.isdeteActive = this.isdeteActive;
            this.listParams.orderProductType = this.orderProductType;
            this.listParams.invoiceStatusActive = this.invoiceStatusActive;
            // if (this.isShowSelectCard) {
            //     this.listParams.refuelCardAccountNo = this.orderWalletList[0].cardNo;
            // }

            if (type == 'handelRes') {
                this.$refs.maskPopup.close();
                this.$refs.walletPopup.close();
            }
            if (type == 'res') {
            }
            if (type == 'ok' || type == 'onShow') {
                this.refreshPullDown();
                this.$refs.maskPopup.close();
                this.$refs.walletPopup.close();
            }
        },
        /**
         * @description  : 上拉加载事件，接口不重置
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.$nextTick(() => {
                    if (this.navActive == 1 && this.listParams.secondNavActive != 9) {
                        this.$refs.orderList.getOrderList({ listParams: this.listParams });
                    } else if (this.navActive == 1 && this.listParams.secondNavActive == 9) {
                        this.$refs.refundOrderList.getRefundOrderList({ listParams: this.listParams });
                    } else if (this.navActive == 2) {
                        setTimeout(() => {
                            this.$nextTick(() => {
                                this.$refs.walletOrderList.getWalletOrderList({ listParams: this.listParams });
                            });
                        }, 100);
                    } else if (this.navActive == 3 && this.listParams.secondNavActive != 9) {
                        setTimeout(() => {
                            this.$nextTick(() => {
                                this.$refs.chargeOrderList.getOrderList({ listParams: this.listParams });
                            });
                        }, 100);
                    } else if (this.navActive == 3 && this.listParams.secondNavActive == 9) {
                        this.$refs.chargeRefundOrderList.getRefundOrderList({ listParams: this.listParams });
                    } else if (this.navActive == 4) {
                        this.$refs.mallOrderList.getMallOrderList({ listParams: this.listParams });
                    }
                });
            }
        },
        /**
         * @description  : 下拉刷新加载，重置接口，初始化加载
         * @return        {*}
         */
        refreshPullDown() {
            this.showEmpty = false;
            this.$nextTick(() => {
                if (this.navActive == 1 && this.listParams.secondNavActive != 9) {
                    this.$refs.orderList.getOrderList({
                        isInit: true,
                        listParams: this.listParams,
                    });
                } else if (this.navActive == 1 && this.listParams.secondNavActive == 9) {
                    this.$refs.refundOrderList.getRefundOrderList({ isInit: true, listParams: this.listParams });
                } else if (this.navActive == 2) {
                    setTimeout(() => {
                        this.$nextTick(() => {
                            this.$refs.walletOrderList.getWalletOrderList({ isInit: true, listParams: this.listParams });
                        });
                    }, 100);
                } else if (this.navActive == 3 && this.listParams.secondNavActive != 9) {
                    setTimeout(() => {
                        this.$nextTick(() => {
                            this.$refs.chargeOrderList.getOrderList({ isInit: true, listParams: this.listParams });
                        });
                    }, 100);
                } else if (this.navActive == 3 && this.listParams.secondNavActive == 9) {
                    this.$refs.chargeRefundOrderList.getRefundOrderList({ listParams: this.listParams });
                } else if (this.navActive == 4) {
                    setTimeout(() => {
                        this.$nextTick(() => {
                            this.$refs.mallOrderList.getMallOrderList({ isInit: true, listParams: this.listParams });
                        });
                    }, 100);
                }
            });
        },
        /**
         * @description  : 停止下拉刷新自定义事件
         * @return        {*}
         */
        stopRefresh() {
            this.$refs.dataList.stopRefresh();
        },
        /**
         * @description  : 改变列表组件状态事件
         * @param         {*} status:订单组件状态  loading加载中  nomore没有更多了  contentdown下拉加载更多
         * @return        {*}
         */
        loadStatusChange(status) {
            this.$refs.dataList.loadStatus = status;
        },
        /**
         * @description  : 是否展示空态事件
         * @param         {*} boolean: true展示  false不展示
         * @return        {*}
         */
        showEmptyChange(boolean) {
            this.showEmpty = boolean;
        },
    },
};
</script>
<style scoped lang="scss">
.nav-contetn {
    padding: 0 57rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;

    .nav {
        display: flex;
        align-items: center;
        flex-direction: column;

        .item-name {
            height: 88rpx;

            .item {
                height: 100%;
                font-size: 32rpx;
                font-weight: 400;
                line-height: 88rpx;
                color: #333333;
                text-align: center;
            }

            .active {
                height: 100%;
                font-size: 32rpx;
                font-weight: bold;
                color: #e64f22;
                line-height: 88rpx;
                text-align: center;
                border-bottom: 2px solid #e64f22;
            }
        }
    }
}
.nav-contetn2 {
    overflow-x: scroll;
    padding: 0;
    // #ifndef H5-CLOUD
    .nav2 {
        flex-shrink: 0;
        margin-right: 15px;

        &:nth-of-type(1) {
            .item-name {
                margin-left: 57rpx;
            }
        }

        &:nth-last-of-type(1) {
            margin-right: 0;
            .item-name {
                margin-right: 57rpx;
            }
        }
    }
    // #endif
    // #ifdef H5-CLOUD
    .scroll-view-container {
        width: 100%;
        white-space: nowrap;

        /* 让内部子元素不换行，保证横向排列 */
        ::v-deep .uni-scroll-view-content {
            display: flex;
            justify-content: space-between;
        }

        .item-name {
            display: inline-block;
            /* 将子元素设置为行内块元素，使其能横向排列 */
            height: 44px;
            margin-right: 15px;

            .item {
                height: 100%;
                font-size: 16px;
                font-weight: 400;
                line-height: 44px;
                color: #333333;
                text-align: center;
            }

            .active {
                height: 100%;
                font-size: 16px;
                font-weight: bold;
                color: #e64f22;
                line-height: 44px;
                text-align: center;
                border-bottom: 2px solid #e64f22;
            }
        }

        .item-name:nth-of-type(1) {
            margin-left: 28.5px;
        }

        .item-name:nth-last-of-type(1) {
            margin-right: 28.5px;
        }
    }
    // #endif
}

.refund-tip {
    font-size: 24rpx;
    font-weight: 400;
    color: #333333;
    line-height: 64rpx;
    text-align: center;
    height: 64rpx;
    background: #fff7dc;
}

.search-content {
    padding: 0 30px;
    width: 100%;
    height: 106rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    border-bottom: 2rpx solid #efeff4;

    .wallet-order-title {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
        display: flex;
        align-items: center;

        img {
            width: 32rpx;
            height: 32rpx;
        }

        .down {
            transform: rotate(0deg);
        }

        .top {
            transform: rotate(180deg);
        }
    }

    .search-size {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
        line-height: 27rpx;
        margin-right: 56rpx;
    }

    .search-img {
        width: 27rpx;
        height: 27rpx;
        margin-right: 4px;
    }

    .select-btn {
        height: 42rpx;
        font-size: 30rpx;
        font-weight: 400;
        color: #e64f22;
    }
}

.select-content {
    position: absolute;
    top: 0;
    bottom: -122rpx;
    left: 0;
    right: 0;
    z-index: 99;
    background: rgba(0, 0, 0, 0.5);

    .select-box {
        display: flex;
        flex-direction: column;
        background-color: #ffffff;
        border-radius: 0rpx 0rpx 16rpx 16rpx;

        .select-box-item {
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30rpx 0 32rpx;

            .left {
                display: flex;
                font-size: 28rpx;
                font-weight: 600;
                color: #000;
                line-height: 40rpx;

                img {
                    width: 36rpx;
                    height: 26rpx;
                    margin-right: 20rpx;
                    line-height: 40rpx;
                }
            }

            .right {
                width: 24rpx;
                height: 22rpx;
            }

            .select {
                color: #e64f22;
            }
        }
    }
}

.noLoginPage {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .noLoginImg {
        width: 250px;
        height: 225px;
    }
    .noLoginBtn {
        width: 120px;
        height: 42px;
        border-radius: 200px;
        color: #fff;
        background: linear-gradient(135deg, #ff4000 0%, #ff6a00 100%);
        text-align: center;
        line-height: 42px;
        font-size: 18px;
        margin-top: 10px;
    }
}

.bottom0 {
    bottom: 0;
}

.containerWrap {
    padding: 32rpx 32rpx 0;
}

.closeAnAccount {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 15;
    width: 100%;
    height: 152rpx;
    background: #ffffff;
    border-radius: 4rpx 4rpx 0rpx 0rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;

    .order-operation {
        display: flex;
        align-items: center;

        .all {
            margin-right: 15px;
        }

        .order-operation-price {
            span {
                font-size: 24rpx;
                font-weight: bold;
                color: #333333;
                line-height: 33rpx;
            }

            .heightLight {
                color: #e64f22;
            }
        }
    }

    .error {
        width: 120px;
        height: 44px;
        background: #e64f22;
        border-radius: 8px;
        text-align: center;
        line-height: 44px;
        font-size: 14px;
        color: #ffffff;
        opacity: 0.2;
    }
}

.isScreen {
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding: 32rpx 32rpx 50rpx;

    .closeImgWrap {
        width: 100%;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        div {
            font-size: 26rpx;
            font-weight: 400;
            color: #999999;
            line-height: 46rpx;
        }

        img {
            width: 25rpx;
            height: 25rpx;
        }
    }

    .title {
        height: 46rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
    }

    .screen-dete {
        margin-top: 20rpx;
    }

    .dete-content {
        margin-top: 41rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .dete-item {
            flex: 1;
            margin-right: 24rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;

            &:nth-of-type(4n) {
                margin-right: 0;
            }
        }
    }

    .wallet-dete-content {
        margin-top: 41rpx;
        display: flex;
        flex-wrap: wrap;

        .wallet-dete-item {
            width: 154rpx;
            margin-right: 24rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;

            &:nth-last-of-type(1) {
                margin-right: 0;
            }
        }
    }

    .calendar-dete {
        display: flex;
        align-items: center;
        margin-top: 24rpx;
        justify-content: space-between;

        .picker {
            width: 320rpx;
            height: 80rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            padding: 0 24rpx 0 34rpx;
            box-sizing: border-box;

            .picker-content {
                height: 80rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }

    .tips {
        margin-top: 12px;
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 18.5px;
    }

    .order-category {
        margin-top: 24rpx;
    }

    .category-content {
        display: flex;
        flex-wrap: wrap;
        margin-right: -24rpx;

        .dete-item {
            width: 155rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
            margin-right: 24rpx;
            margin-top: 24rpx;
        }

        .width-data-item {
            width: 300rpx;
        }
    }

    .wallet-content {
        margin-top: 41rpx;
        display: flex;
        gap: 24rpx 24rpx;
        flex-wrap: wrap;

        .dete-item {
            padding: 0 16rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
        }
    }

    .btn {
        display: flex;
        justify-content: space-between;
        margin-top: 22px;

        div {
            width: 330rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            font-size: 32rpx;
        }

        .reset {
            background: #ffffff;
            border-radius: 16rpx;
            border: 1rpx solid #e64f22;
            color: #e64f22;
            text-align: center;
        }

        .confirm {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 16rpx;
            color: #ffffff;
        }
    }
}

.selectPB {
    padding-bottom: 152rpx;
}

.borderB {
    border-bottom: 1rpx solid #efeff4;
}

.isActive {
    border: 1px solid #e64f22;
    background: #ffffff !important;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #e64f22 !important;
}
</style>
