<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="remaining_div fl-column bg-F7F7FB">
            <zj-navbar title="设置" :border-bottom="false"></zj-navbar>
            <div class="set_div card-default marlr16 mart12">
                <div
                    class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_13"
                    :class="{ disabled: walletInfo.leftChangeCount ? false : true }"
                    @click="toDetail(1)"
                >
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">更换常用地</div>
                        <div v-if="walletInfo.leftChangeCount == 0" class="font-12 color-999">您的变更次数已达当年上限</div>

                        <div v-else class="font-12 color-999">昆仑e享卡常用地</div>
                    </div>
                    <div class="right_btn_div fl-row fl-al-cen">
                        <div class="color-999 font-12">{{ walletInfo.addressName || '' }}</div>
                        <div class="right_btn"></div>
                    </div>
                </div>
                <!-- #ifdef MP-MPAAS -->
                <div
                    v-if="supportType !== 0 && !isHarmony"
                    class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_6"
                    :class="{ disabled: !supportType }"
                >
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 color-333 item_title">{{ supportType === 1 ? '指纹支付' : '人脸支付' }}</div>
                        <div class="font-12 color-999">昆仑e享卡与实体卡{{ supportType === 1 ? '指纹支付' : '人脸支付' }}</div>
                    </div>
                    <div class="right_btn_div" style="margin-right: -10px">
                        <img class="img_style" src="../../images/switch_on.png" v-if="switchChecked" @click="switch2Change" />
                        <img class="img_style" src="../../images/switch_off.png" v-else @click="switch2Change" />
                    </div>
                </div>
                <!-- #endif -->
                <!-- #ifdef MP-MPAAS -->
                <div
                    class="fl-row fl-jus-bet fl-al-cen line_bottom bg-fff set_div_item padding_13"
                    @click="cardRefund()"
                    v-if="walletStatus.status && isShowRefund && !isHarmony"
                >
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">昆仑e享卡退款</div>
                        <div class="font-12 color-999">昆仑e享卡仅支持第三方充值退款</div>
                    </div>
                    <div class="right_btn_div">
                        <div class="right_btn"></div>
                    </div>
                </div>
                <!-- #endif -->
                <div v-if="walletStatus.status && isShowECardApply && !isHarmony">
                    <div class="fl-row fl-jus-bet fl-al-cen line_bottom bg-fff set_div_item padding_13" @click="cardApply()">
                        <div class="set_div_item_left fl-row fl-al-cen">
                            <div class="font-15 item_title">昆仑e享卡申领</div>
                            <div class="font-12 color-999">申领并管理昆仑e享卡实体卡</div>
                        </div>
                        <div class="right_btn_div">
                            <div class="right_btn"></div>
                        </div>
                    </div>
                    <div
                        class="fl-row fl-jus-bet fl-al-cen line_bottom bg-fff set_div_item padding_13"
                        v-if="eCardStatus == 3 && (eCardCardStatus == 2 || eCardCardStatus == 3)"
                    >
                        <div class="set_div_item_left fl-row fl-al-cen">
                            <div class="font-15 item_title">昆仑e享卡挂失</div>
                            <div class="font-12 color-999">昆仑e享卡实体卡冻结挂失</div>
                        </div>
                        <div class="right_btn_div" style="margin-right: -10px">
                            <img
                                class="img_style"
                                src="../../images/switch_off.png"
                                v-if="eCardCardStatus == 2"
                                @click="eCardReportingLoss(1)"
                            />
                            <img
                                class="img_style"
                                src="../../images/switch_on.png"
                                v-else-if="eCardCardStatus == 3 && eCardBlacklistType == 1"
                                @click="eCardReportingLoss(2)"
                            />
                        </div>
                    </div>
                </div>
                <!-- #ifndef MP-TOUTIAO -->
                <div class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_13" @click="changePassword()">
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">修改支付密码</div>
                        <div class="font-12 color-999">昆仑e享卡与实体卡支付密码</div>
                    </div>
                    <div class="right_btn_div">
                        <div class="right_btn"></div>
                    </div>
                </div>
                <div class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_13" @click="toDetailPW()">
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">忘记支付密码</div>
                        <div class="font-12 color-999">昆仑e享卡与实体卡支付密码</div>
                    </div>
                    <div class="right_btn_div">
                        <div class="right_btn"></div>
                    </div>
                </div>
                <!-- #endif -->
                <!-- #ifdef MP-WEIXIN || H5 -->
                <!-- TODO @version? -->
                <div v-if="0" class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_13">
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">开通掌纹支付</div>
                    </div>
                    <div class="right_btn_div" @click="showKeyboard">
                        <div class="right_btn_div_switch">
                            <img v-if="!openOrNot" src="../../images/switch_off.png" alt />
                            <img v-if="openOrNot" src="../../images/switch_on.png" alt
                        /></div>
                        <img class="dynamicEffectFinger" v-if="hideHand" src="../../images/finger.png" alt="" />
                    </div>
                </div>
                <!-- #endif -->
                <!-- <div
                    class="fl-row fl-jus-bet fl-al-cen set_div_item padding_13"
                    :class="{ disabled: cancelInfo.leftCancelTimes ? false : true }"
                    @click="toDetail(4)"
                >
                    <div class="set_div_item_left fl-row fl-al-cen">
                        <div class="font-15 item_title">昆仑e享卡注销</div>

                        <div
                            v-if="cancelInfo.leftCancelTimes == 0 && walletStatus.status && walletStatus.accountStatus != '3'"
                            class="font-12 color-999"
                            >您的注销次数已达到当年上限</div
                        >
                    </div>
                    <div class="right_btn_div">
                        <div class="right_btn"></div>
                    </div>
                </div> -->
            </div>
            <zj-show-modal>
                <!-- <div v-if="!cancelPop"> -->
                <div class="tc_div" v-if="showModalType == 1">
                    <div class="title">温馨提示</div>
                    <div class="text">支付密码为6位数字;</div>
                    <div class="text">昆仑e享卡与实体卡移动支付共用同一个支付密码，修改后实体卡线下支付密码不受影响;</div>
                    <div class="text">能源e站App和小程序采用统一的支付密码，修改后同时生效。</div>
                    <div style="float: right; overflow: hidden; color: #e64f22" @click="toDetailPW()">忘记密码</div>
                </div>
                <div class="tc_div" v-else-if="showModalType == 2">
                    <div class="title-cancel">您是否取消退款？</div>
                </div>
                <div class="tc_div" v-else-if="showModalType == 3">
                    <div class="title-cancel">取消退款成功。</div>
                </div>
                <div class="tc_div" v-else>
                    <div class="title">常用地由“{{ walletInfo.addressName }}”变更为 “{{ choseCity }}”</div>
                    <div class="number_of_times fl-row">
                        每年可更换5次，还剩余
                        <div>{{ walletInfo.leftChangeCount }}</div
                        >次
                    </div>
                    <div class="text text_title">更换后：</div>
                    <div class="text">您当前电子券只能在当前常用地使用，不会随着常用地变更而改变;</div>
                    <div class="text">您会员权益（包括会员折扣、积分、能源币等权益）将随你变更归属地而变更，原有会员折扣会失效。</div>
                </div>
                <!-- </div> -->
            </zj-show-modal>
            <custom-popup ref="popDialogFlag" type="center">
                <div class="model-div">
                    <div class="te-center">
                        <div class="fl-row fl-jus-bet fl-al-cen padtb-37-32">
                            <div class="font-16 weight-500 color-333">您目前存在退款流程，当前账户已锁定，直至退款完成</div>
                        </div>
                        <div class="height44" @click="changeCell('cancle')" v-if="walletRefundStatus == 1">
                            <div class="font-14 color-E64F22 weight-600">取消退款</div>
                        </div>
                        <div class="height44" @click="changeCell('look')">
                            <div class="font-14 color-333 weight-500">查看退款</div>
                        </div>
                        <div class="height44" @click="changeCell('yes')">
                            <div class="font-14 color-666 weight-400">我知道了</div>
                        </div>
                    </div>
                </div>
            </custom-popup>
            <SelectCity
                v-if="showPopup"
                :provinceCityArray="provincesAndCitiesList"
                :show="showPopup"
                @sureSelectArea="sureSelectArea"
                @hideShow="hideShow"
            ></SelectCity>
            <zj-old-account v-if="isTransfer"></zj-old-account>
        </div>
        <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        <!-- #ifdef MP-WEIXIN -->
        <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
        <!-- #endif -->
    </div>
</template>
<script>
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import { balance, lockOrUnlockApi, addressModify, accountLeftCancelTimesQuery } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import { getMemberCardApply, cancelMemberCardApply } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { mapState, mapGetters } from 'vuex';
import wxSetting from './diff-environment/wx-setting';
import Vue from 'vue';
import { applyRefundQuery, applyRefundCancel } from '../../../../s-kit/js/v3-http/https3/oilCard/index.js';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    components: {
        SelectCity,
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-WEIXIN
        wxSetting,
        // #endif
    ],
    data() {
        return {
            areaName: '',
            showInfo: false,
            showPopup: false,
            choseCity: '',
            switchChecked: false,
            supportType: 0, //设备支持的指纹或人脸类型 1指纹2人脸
            provincesAndCitiesList: [],
            cancelInfo: {
                leftCancelTimes: 0,
                limitCancelTimes: 0,
            },
            addressName: '',
            showModalType: 0,
            cancelBtnShow: true,
            changePlace: true,
            openOrNot: false,
            passwordKeyboardRef: {},
            originalPasswordLength: '',
            originalPassword: '',
            // 默认不展示退款入口
            isShowRefund: false,
            // 掌纹支付手势图
            hideHand: false,
            walletRefundStatus: null,
            // // 低于当3.5.9的版本不显示指定表单
            // closeForm: true,
            eCardReportFlag: null,
            eCardStatus: null,
            eCardCardStatus: null,
            eCardBlacklistType: null,
            isShowECardApply: false,
        };
    },
    onLoad() {
        // 获取省市地区
        // this.getProvincesCities();
        this.cardApply('query');
    },
    created() {
        this.switch2Change = this.$sKit.commonUtil.throttleUtil(this.switch2Change);
    },
    async mounted() {
        this.refreshPullDown();
        // #ifdef MP-MPAAS
        // 生物识别验证 是否需要拉起指纹或者人脸，默认需要
        this.supportType = await this.$cnpcBridge.getSupportType();
        if (this.supportType) {
            this.switchChecked = await this.$sKit.commonUtil.judgeBiometricPayStatus();
        }
        // 昆仑e享卡入口是否显示
        this.$cnpcBridge.getSwitch('apply_refund', res => {
            if (res == 'yes') {
                this.isShowRefund = true;
            }
        });
        // 昆仑e享卡实体卡申领入口是否显示
        this.$cnpcBridge.getSwitch('ecard_apply', res => {
            if (res == 'yes') {
                this.isShowECardApply = true;
            }
        });
        // // 判断当前App版本号
        // this.closeForm = await this.$cnpcBridge.judgeProtocolCall();
        // console.log(this.closeForm, '版本号');
        // #endif
    },
    computed: {
        ...mapGetters(['memberAccountInfo', 'walletInfo', 'walletStatus']),
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 存储外部跳转进来携带的参数和小程序跳转过来的参数
            // #endif
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        async enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.$sKit.commonUtil.nextOilTriggerRisk(13).then(
                async res => {
                    console.log(res, '人脸返回');
                    let params = { type: this.eCardReportFlag, authInfo: res.data.authInfo };
                    let apiRes = await lockOrUnlockApi(params);
                    if (apiRes && apiRes.success) {
                        this.cardApply('query');
                        this.$store.dispatch('getSetWalletStatus');
                        let showToast = '';
                        if (this.eCardReportFlag == 1) {
                            showToast = '挂失成功';
                        }
                        if (this.eCardReportFlag == 2) {
                            showToast = '解除挂失成功';
                        }
                        uni.showToast({ title: showToast });
                    }
                },
                () => {},
            );
        },
        openAgreement() {
            this.$store.dispatch('changeFacePop', true);
        },
        // 昆仑e享卡挂失解挂
        async eCardReportingLoss(flag) {
            let content = '';
            this.eCardReportFlag = flag;
            if (this.eCardReportFlag == 1) {
                content = '挂失系统会冻结您的昆仑e享卡，昆仑e享卡将被限制消费';
            }
            if (this.eCardReportFlag == 2) {
                content = '解除挂失系统会解冻您的昆仑e享卡，昆仑e享卡可以进行消费';
            }
            this.$store.dispatch('zjShowModal', {
                content: content,
                confirmText: '我已确认',
                cancelText: '返回',
                success: async res => {
                    if (res.confirm) {
                        this.openAgreement();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 下拉刷新
        refreshPullDown() {
            this.$store.dispatch('getSetWalletStatus');
            this.$store.dispatch('getAccountBalanceAction');
            this.$store.dispatch('memberBaseInfoAction');
            this.getaccountLeftCancelTimesQuery();
            // this.initDate()
        },
        //获取注销次数
        async getaccountLeftCancelTimesQuery() {
            let res = await accountLeftCancelTimesQuery({}, { isCustomErr: true });
            if (res && res.success) {
                this.cancelInfo = res.data;
                // this.cancelInfo.leftCancelTimes = 0
            }
        },
        //获取城市列表
        async getProvincesCities() {
            return new Promise(async (resolve, reject) => {
                let res = await provinceAndCityList();
                if (res.success) {
                    let arr2 = res.data.map(item => {
                        let newItem = {
                            name: item.parentAreaName,
                            code: item.parentAreaCode,
                            city: item.areaList,
                        };
                        return newItem;
                    });
                    this.provincesAndCitiesList = arr2;
                    resolve();
                }
            });
        },
        //开通指纹/人脸
        async openOrUpdateBiometricCode() {
            uni.showLoading();
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            this.$accountCenter.openOrUpdateBiometricCode(
                {
                    biometricCode: commonArgs.deviceId,
                    type: this.supportType,
                },
                res => {
                    uni.hideLoading();
                    if (res.isSuccessed) {
                        uni.showToast({
                            title: '开通成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        this.switchChecked = true;
                    } else {
                        // this.$toast(res.desString || '开通失败')
                        uni.showToast({
                            title: res.desString || '开通失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                },
            );
        },
        // 关闭生物识别
        closeBiometricCode() {
            this.$accountCenter.closeBiometricCode({ type: this.supportType }, res => {
                if (res.isSuccessed) {
                    // this.$toast('关闭成功')
                    uni.showToast({
                        title: '关闭成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.switchChecked = false;
                } else {
                    // this.$toast(res.desString || '关闭失败')
                    uni.showToast({
                        title: res.desString || '关闭失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        },
        //人脸或指纹
        async operatePayState() {
            let denied = await this.$cnpcBridge.getSupportType(true);
            if (!this.$sKit.test.isAndroidClient() && denied) {
                this.$store.dispatch('zjShowModal', {
                    content: '请检查"能源e站"是否开启使用面容ID权限。',
                    confirmText: '去检查',
                    cancelText: '取消',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            this.$cnpcBridge.openPermissionsSetting();
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }
            //调起验证
            this.$cnpcBridge.localAuthVerifica(res => {
                if (res && Number(res)) {
                    //验证成功
                    if (!this.switchChecked) {
                        setTimeout(() => {
                            this.openOrUpdateBiometricCode();
                            this.$sKit.mpBP.tracker('指纹支付', {
                                seed: 'fingerPayBiz',
                                pageID: 'onFingerPayBut',
                                refer: 'r43',
                                channelID: clientCode,
                            });
                        }, 1000);
                    } else {
                        this.closeBiometricCode();
                        this.$sKit.mpBP.tracker('指纹支付', {
                            seed: 'fingerPayBiz',
                            pageID: 'offFingerPayBut',
                            refer: 'r43',
                            channelID: clientCode,
                        });
                    }
                } else {
                    if (!this.switchChecked) {
                        uni.showToast({
                            title: '开通失败,请在30秒后重新开通',
                            icon: 'none',
                            duration: 2000,
                        });
                    } else {
                        uni.showToast({
                            title: '关闭失败,请在30秒后重新关闭',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                }
            });
        },
        //关闭更换开户地弹窗
        selectPlaceOfDeposit() {
            if (this.provincesAndCitiesList.length > 0) {
                this.showPopup = true;
            } else {
                this.getProvincesCities().then(() => {
                    this.showPopup = true;
                });
            }
        },
        //更换开户地事件
        async addressModifyClick(code) {
            let res = await addressModify({ city: code });
            if (res && res.success) {
                console.log('用户点击确定');
                this.areaName = this.choseCity;
                await this.$store.dispatch('getAccountBalanceAction');
                this.$sKit.layer.showToast({
                    title: '更换成功',
                });
            }
        },
        // 修改支付密码
        changePassword() {
            this.$sKit.mpBP.tracker('修改支付密码', {
                seed: 'modifyPsdBiz',
                pageID: 'reminderToast', // 页面名
                refer: 'r55', // 来源
                channelID: clientCode, // C10/C12/C13
            });
            this.showModalType = 1;
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                confirmColor: '#E64F22',
                success: res => {
                    if (res.confirm) {
                        // #ifdef MP-MPAAS
                        if (this.isHarmony) {
                            this.toPassword();
                        } else {
                            uni.showLoading();
                            this.$sKit.commonUtil.cardOperationPwd({
                                nextFun: res => {
                                    //验证通过，进一步操作
                                    this.$accountCenter.changePW(res => {
                                        uni.hideLoading();
                                        if (res.isSuccessed) {
                                            uni.showToast({
                                                title: '密码修改成功',
                                                icon: 'none',
                                                duration: 2000,
                                            });
                                        } else {
                                            uni.showToast({
                                                title: res.desString || '密码修改失败',
                                                icon: 'none',
                                                duration: 2000,
                                            });
                                        }
                                    });
                                },
                                walletAddParams: {
                                    refer: 'r11',
                                },
                            });
                        }
                        // #endif
                        // #ifndef MP-MPAAS
                        this.toPassword();
                        // #endif
                    } else if (res.cancel) {
                    }
                },
            });
        },
        toPassword() {
            let url = '/packages/third-my-wallet/pages/change-payment-password/main';
            this.$sKit.layer.useRouter(url, { refer: 'r55' }, 'navigateTo');
        },
        //更换开户地-确定事件
        sureSelectArea(val) {
            this.showPopup = false;
            this.showModalType = 0;
            this.choseCity = val.detail.province + ' ' + val.detail.city;
            if (this.showModalType) {
                return;
            }
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        this.addressModifyClick(val.detail.cityCode);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        //更换开户地-取消事件
        hideShow() {
            this.showPopup = false;
        },
        toDetail(num) {
            if (num == 4 && !this.cancelInfo.leftCancelTimes) {
                return;
            }

            if (!this.walletInfo.leftChangeCount && num == 1) {
                return;
            }
            switch (num) {
                case 1:
                    this.$sKit.commonUtil.eWalletNormal({
                        nextFun: res => {
                            this.selectPlaceOfDeposit();
                        },
                        freezeReasonArr: [],
                        walletAddParams: {
                            refer: 'r11',
                        },
                    });
                    // if (this.walletStatus.status && this.walletStatus.accountStatus != '3') { }
                    break;
                case 2:
                    this.$sKit.mixApi.mixChangePW();
                    break;
                case 4:
                    this.$sKit.commonUtil.eWalletNormal({
                        nextFun: res => {
                            this.$sKit.layer.useRouter(
                                '/packages/third-my-wallet/pages/card_cancel/main',
                                { cancelInfo: this.cancelInfo },
                                'navigateTo',
                            );
                        },
                        freezeReasonArr: [9],
                        cancelCallback: res => {
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                        walletAddParams: {
                            refer: 'r11',
                        },
                    });
                    break;
                default:
                    break;
            }
        },

        //前往忘记密码页面
        toDetailPW() {
            // #ifndef MP-MPAAS
            let url = '/packages/third-my-wallet/pages/change-password/main';
            this.$sKit.layer.useRouter(url, { refer: 'r55' }, 'navigateTo');
            // #endif
            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                let url = '/packages/third-my-wallet/pages/change-password/main';
                this.$sKit.layer.useRouter(url, {}, 'navigateTo');
            } else {
                this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/forgot-password/main', { isClose: 1 }, 'navigateTo');
            }
            // #endif
        },
        //开通指纹/人脸按钮开关
        switch2Change: function (e) {
            if (this.supportType) {
                this.operatePayState();
            }
        },
        // 昆仑e享卡退款
        async cardRefund() {
            // return;
            // 冻结原因是12，就是他正在审核中，或者是有退款申请
            // 请求账户中心查询客户当前是否已有退款流程，如果已有退款流程，弹窗进行提示，客户可选择查看退款、取消退款、我知道了，如果客户没有退款申请，则跳转到退款协议页面，客户阅读退款申请后点击“申请退款”，跳转到退款申请页面，
            // let res = await applyRefundQuery({}, { isCustomErr: true });
            if (this.walletStatus.accountStatusReason == 12) {
                let res = await applyRefundQuery();
                if (res && res.success) {
                    this.walletRefundStatus = res.data.refundStatus;
                }
                this.$refs.popDialogFlag.open();
                return;
            } else {
                let url = '/packages/third-my-wallet/pages/refund-agreement/main';
                this.$sKit.layer.useRouter(url);
                return;
            }
        },
        // 取消退款
        changeCell(data) {
            this.$refs.popDialogFlag.close();
            if (data == 'cancle') {
                this.showModalType = 2;
                this.$store.dispatch('zjShowModal', {
                    confirmText: '确认',
                    cancelText: '取消',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            this.applyRefundCancelPost();
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            } else if (data == 'look') {
                let url = '/packages/third-my-wallet/pages/refund-process-application/main';
                this.$sKit.layer.useRouter(url);
            }
        },
        // 根据会员编号取消退款申请
        async applyRefundCancelPost() {
            let res = await applyRefundCancel();
            if (res.success) {
                await this.$store.dispatch('getSetWalletStatus');
                this.showModalType = 3;
                this.$store.dispatch('zjShowModal', {
                    confirmText: '确认',
                    cancelText: '',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击取消');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                // uni.showToast({
                //     title: '取消成功',
                //     icon: 'none',
                //     duration: 2000,
                // });
                return;
            }
        },
        // 昆仑e享卡申领
        async cardApply(type) {
            let res = await getMemberCardApply();
            if (res && res.success) {
                if (type == 'query') {
                    this.eCardStatus = res.data.status;
                    this.eCardCardStatus = res.data.cardStatus;
                    this.eCardBlacklistType = res.data.blacklistType;
                } else {
                    if (res.data.status == 1) {
                        let url = '/packages/third-my-wallet/pages/card-apply/main';
                        this.$sKit.layer.useRouter(url);
                    } else if (res.data.status == 3) {
                        this.$store.dispatch('zjShowModal', {
                            title: '您已申请昆仑e享卡实体卡，请勿重复申请',
                            success: res => {},
                        });
                    } else if (res.data.status == 2) {
                        this.$store.dispatch('zjShowModal', {
                            title: '您已申请昆仑e享卡实体卡，请到网点领取实体卡',
                            content: `领取网点：${res.data.stationName}`,
                            content2: `申请时间：${res.data.applyTime}`,
                            confirmText: '我知道了',
                            cancelText: '取消申请',
                            cancelColor: '#E64F22',
                            success: modalRes => {
                                if (modalRes.cancel) {
                                    this.$store.dispatch('zjShowModal', {
                                        title: '确认取消昆仑e享卡实体卡申请？',
                                        cancelText: '取消',
                                        cancelColor: '#333333',
                                        success: async modalRes2 => {
                                            if (modalRes2.confirm) {
                                                let cancelRes = await cancelMemberCardApply({ applyNo: res.data.applyNo });
                                                if (cancelRes && cancelRes.success) {
                                                    this.$store.dispatch('zjShowModal', {
                                                        title: '取消成功',
                                                        success: res => {
                                                            if (res.confirm) {
                                                            }
                                                        },
                                                    });
                                                }
                                            }
                                        },
                                    });
                                }
                            },
                        });
                    }
                }
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;

    .bg-page {
        width: 100%;
        height: 476px;
        position: absolute;
        top: 0;
        left: 0;
        display: block;
    }
}

.set_div {
    // padding: 4px 12px;
    .set_div_item {
        // margin-top: 12px;
        // width: 100%;
        margin-left: 12px;
        margin-right: 12px;

        //  这个样式为bug表中要求的"提示信息左对齐"的效果
        .set_div_item_left {
            // width: 93%;
            flex: 1;

            .item_title {
                width: 110px;
                color: #000000;
            }
        }

        .right_btn_div {
            position: relative;
            .dynamicEffectFinger {
                position: absolute;
                right: 12px;
                top: 12px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 50px;
                height: 60px;
            }

            // .dynamicEffectFinger {
            //     animation: pulse 1s linear infinite;
            // }

            // .dynamicEffectFinger {
            //     animation: pulse 1s 300ms linear infinite;
            // }

            .dynamicEffectFinger {
                // animation: pulse 1s 1s linear infinite;
                animation: pulse 2s ease-in-out infinite alternate;
            }
            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(0.85);
                }
                100% {
                    transform: scale(1);
                }
            }
            .right_btn {
                width: 8px;
                height: 8px;
                box-sizing: border-box;
                border: solid #666666;
                border-width: 0 1px 1px 0;
                transform: rotate(-45deg);
            }
            .right_btn_div_switch {
                height: 25px;
                width: 44px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
.marlr16 {
    margin: 0 16px;
}
.mart12 {
    margin-top: 12px;
}

.padding_13 {
    padding-top: 13px;
    padding-bottom: 13px;
}

.padding_6 {
    padding-top: 10px;
    padding-bottom: 10px;
}

.img_style {
    width: 42px;
    height: 23px;
    margin-right: 5px;
}
.model-div {
    width: 280px;
    height: 246px;
    background: #ffffff;
    border-radius: 10px;
    .marlr23 {
        margin: 0 23px;
    }
    .padtb-37-32 {
        padding-top: 37px;
        padding-bottom: 32px;
        padding-left: 22px;
        padding-right: 22px;
    }
    .height44 {
        height: 44px;
        line-height: 44px;
        border-top: 1px solid #efeff4;
    }
    .line-top {
        position: relative;
        &.line-top {
            content: '';
            position: absolute;
            width: 100%;
            height: 1px;
            border: 1px solid #efeff4;
            top: 0;
            left: 0;
        }
    }
}
.tc_div {
    .title {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        text-align: center;
        margin-top: 6px;
    }
    .title-cancel {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        margin-top: 6px;
        text-align: center;
    }

    .number_of_times {
        font-size: 10px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        flex-wrap: wrap;
        margin-bottom: 16px;
        justify-content: center;

        div {
            color: #e64f22;
        }
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
    }
}

.disabled {
    .set_div_item_left {
        .item_title {
            color: #999999 !important;
        }
    }
}
</style>
