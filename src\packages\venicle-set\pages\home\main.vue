<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="车牌管理"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="venicle-list-view">
            <div class="venicle-list-item" v-for="(item, index) in defaultVenicleList" :key="item.id" @click="clickVenicleItem(item)">
                <div class="venicle-text-view">
                    <div class="venicle-top-text">
                        <div class="top-text-title">{{ item.carNo }}</div>
                        <div class="top-text-isdefault" v-if="item.isDefault">默认</div>
                    </div>
                    <div class="venicle-bottom-text">
                        <div class="bottom-text">常用油品</div>
                        <div class="bottom-text-select">{{ item.oilNo }}</div>
                        <div class="bottom-text">、单次最大加油金额</div>
                        <div class="bottom-text-select">{{ item.refuelAmount }}元</div>
                    </div>
                </div>
                <img src="@/static/homeIcon/rightjt.png" alt class="btn-icon" />
                <!-- <div class='btn-view'>
					<div class='btn edit' @click='clickVenicleItem(item)'>编辑</div>
					<div class='btn delete ml-1' @click='clickDeleteItem(item)'>删除</div>
				</div> -->
            </div>
        </div>
        <div class="venicle-btn">
            <div class="venicle-btn-view">
                <div class="venicle-btn-text" @click="clickAdd">添加新车牌</div>
            </div>
            <div class="btn-text-sp"></div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import { deteleDefdaultCar } from '@/api/home.js';
import util from '@/utils/index.js';
import { mapState } from 'vuex';
export default {
    computed: {
        ...mapState({
            defaultVenicleList: state => state.venicle.defaultVenicleList, // 车辆列表
        }),
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
        };
    },
    methods: {
        // 编辑车牌点击事件
        clickVenicleItem(item) {
            uni.navigateTo({
                url: '/packages/venicle-set/pages/add-venicle/main?obj=' + encodeURIComponent(JSON.stringify(item)),
            });
        },
        // 删除车牌点击事件
        // async clickDeleteItem(item) {
        // 	await util.showModal('您确定删除此常用车辆么', true)
        // 	let res = await deteleDefdaultCar({id: item.id})
        // 	this.$store.dispatch('uploadVenicleList')
        // },
        // 新增点击事件
        clickAdd() {
            uni.navigateTo({
                url: '/packages/venicle-set/pages/add-venicle/main',
            });
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
}
.venicle-list-view {
    padding-bottom: calc(env(safe-area-inset-bottom) + 64px);
    padding-top: 10px;
    overflow: hidden;
    .venicle-list-item {
        margin-left: 15px;
        width: 345px;
        height: 75px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        border-radius: 5px;
        background-color: #ffffff;
        .venicle-text-view {
            margin-left: 10px;
            flex: 1;
            .venicle-top-text {
                height: 24px;
                display: flex;
                align-items: center;
                .top-text-title {
                    line-height: 20px;
                    font-size: 18px;
                    font-weight: 700;
                    color: #333333;
                }
                .top-text-isdefault {
                    background-color: $btn-mantle-color;
                    margin-left: 5px;
                    border-radius: 3px;
                    border: 1px solid $btn-color;
                    padding-left: 5px;
                    padding-right: 5px;
                    line-height: 18px;
                    font-size: 10px;
                    color: $btn-color;
                }
            }
            .venicle-bottom-text {
                margin-top: 5px;
                height: 24px;
                display: flex;
                align-items: center;
                .bottom-text {
                    font-size: 12px;
                    color: #909090;
                    line-height: 24px;
                }
                .bottom-text-select {
                    font-size: 12px;
                    line-height: 24px;
                    color: $btn-color;
                }
            }
        }
        // .btn-view {
        // 	display: flex;
        // 	margin-right: 10px;
        // 	.btn {
        // 		line-height: 26px;
        // 		border-radius: 3px;
        // 		padding: 0 5px;
        // 		font-size: 12px;
        // 	}
        // 	.edit {
        // 		color: $bg-color;
        // 		border: 0.5px solid $bg-color;
        // 		background-color: #EDFFEF;
        // 	}
        // 	.delete {
        // 		color: #C7000B;
        // 		border: 0.5px solid #C7000B;
        // 		background-color: #FFF3F3;
        // 	}
        // 	.ml-1 {
        // 		margin-left: 5px;
        // 	}
        // }
        .btn-icon {
            width: 4px;
            height: 8px;
            padding: 10px;
        }
    }
}
.venicle-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    .venicle-btn-view {
        height: 64px;
        width: 100vw;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .venicle-btn-text {
            line-height: 44px;
            width: 345px;
            background-color: $btn-color;
            border-radius: 5px;
            color: #ffffff;
            text-align: center;
            font-weight: 700;
        }
    }
    .btn-text-sp {
        width: 100vw;
        background-color: #ffffff;
        height: env(safe-area-inset-bottom);
    }
}
</style>
