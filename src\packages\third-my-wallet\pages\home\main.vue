<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div style="height: 100%">
            <thirdWallet ref="thirdWallet" :pageType="pageType" style="height: 100%" :refer="refer" :myRefer="myRefer"> </thirdWallet>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import thirdWallet from '../../../../s-kit/first/third-wallet/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-Wallet',
    components: { thirdWallet },
    data() {
        return {
            refer: '',
            myRefer: '',
            pageType: 'page',
        };
    },
    onLoad(options) {
        console.log(options, 'options---onLoad');
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        if (Object.keys(options).length > 0 && options.data) {
            try {
                const params = JSON.parse(decodeURIComponent(options.data));
                this.refer = params.refer || '';
                this.myRefer = params.myRefer || '';
            } catch (e) {
                console.error('Failed to parse options.data', e);
            }
        }
        // #ifdef MP-MPAAS
        this.pageType = getApp().globalData.pageParams?.pageType || 'page';
        // #endif
    },
    onShow(options) {
        console.log(options, 'options---onShow');
        this.$nextTick(() => {
            console.log(this.$refs, 'this.$refs.thirdWallet');
            this.$refs.thirdWallet.refreshPullDown('onShow');
        });
    },
    methods: {},
};
</script>
<style scoped lang="scss"></style>
