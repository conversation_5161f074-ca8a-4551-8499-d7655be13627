import { userLogin, initWechatFaceLoginByCode } from '../../v3-http/https3/thirdLogin/index';
import { getCppeiLoginInfo, refreshTokenApi } from '../../v3-http/https3/user';
import store from '../../../../store/index';
import sKit from '@/s-kit/js';
import Layer from '../../../../s-kit/js/layer';
import { basicInfoQuery } from '../../v3-http/https3/oilCard/index';
// 2.0卡列表
import { cardListPost } from '@/api/home.js';
import projectConfig from '../../../../../project.config';
let wxCode = '';
export default {
    state: {
        // 存储外部跳转进来携带的参数
        // officialAccountParams: '',
        // 微信支付携带的跳转链接上是否登录再进行跳转
        wxPayIsLoginFlag: Boolean,
        // 优途小程序手机号
        ytPhone: '',
        // 支付活动在活动页面登录的手机号
        zfPhone: '',
        // 领券登录跳转3.0电子券页面标识
        logInAgain: false,
        // 微信小程序登录临时码
        loginTemporaryCode: '',
        // 打开实人认证的表单弹窗
        realNameDialogFlag: false,
        // #ifdef MP-WEIXIN || H5-CLOUD
        // 登录按钮是否可以点击
        loginButtonGrayedOut: false,
        // #endif
        // #ifdef MP-ALIPAY || H5-CLOUD
        // 登录按钮是否可以点击
        loginButtonGrayedOut: true,
        // #endif
        // 微信小程序首页顶部tab标识
        homepageTags: '',
        // 支付宝返回指定页面
        returnToTheSpecifiedPage: '',
        // 长时间未登录
        longTimeNotLogin: null,
        // 是否是3.0会员
        newMember: null,
        // 支付宝小程序是否授权手机号
        isUserRefusesAuthorization: false,
        // 运营活动链接中提取出来的参数
        activityStr: '',
        outsideRefer: '',
        // 登录加载loading
        loginLoading: true,
    },
    mutations: {
        // 微信支付活动页携带的参数为false标识不需要登录
        setWxPayIsLoginFlag(state, info) {
            state.wxPayIsLoginFlag = info;
        },
        //
        setActivityStr(state, info) {
            state.activityStr = info;
        },
        // 存储优途小程序的手机号
        setYtPhone: (state, info) => {
            state.ytPhone = info;
        },
        // 支付活动的手机号
        setZfPhone: (state, info) => {
            console.log('vuex中的支付活动的手机号', info);
            state.zfPhone = info;
        },
        // 领券登录跳转3.0电子券页面标识
        setLogInAgain(state, info) {
            state.logInAgain = info;
        },
        // 微信小程序登录临时码
        setLoginTemporaryCode(state, info) {
            state.loginTemporaryCode = info;
        },
        // 打开实人认证的表单弹窗
        setRealNameDialogFlag(state, info) {
            state.realNameDialogFlag = info;
        },
        // 3.0token
        setAccessToken(state, info) {
            state.accessToken = info;
        },
        // 登录按钮是否可以被点击
        setLoginButtonGrayedOut(state, info) {
            state.loginButtonGrayedOut = info;
        },
        // 首页顶部tab标签存储
        setHomepageTags(state, info) {
            state.homepageTags = info;
        },
        // 支付宝登录成功后返回指定页面标识
        setReturnToTheSpecifiedPage(state, info) {
            state.returnToTheSpecifiedPage = info;
        },
        // 长时间未登录标识
        setLongTimeNotLogin(state, info) {
            state.longTimeNotLogin = info;
        },
        // 是否是3.0会员
        setNewMember(state, info) {
            state.newMember = info;
        },
        // 支付宝小程序是否授权手机号
        setIsUserRefusesAuthorization(state, info) {
            console.log(info, '支付宝小程序是否授权手机号');
            state.isUserRefusesAuthorization = info;
        },
        setOutsideRefer(state, info) {
            state.outsideRefer = info;
        },
        // 微信小程序登录加载loading
        setLoginLoading(state, info) {
            state.loginLoading = info;
        },
    },
    actions: {
        // 进入页面初始化
        async init({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                try {
                    // 查询隐私授权情况
                    wx.getPrivacySetting({
                        success: async resPrivacy => {
                            // needAuthorization == true 需要弹出隐私协议
                            let res = {};
                            console.log(resPrivacy, 'resPrivacy===');
                            if (resPrivacy.needAuthorization) {
                                try {
                                    // 弹出隐私协议弹窗
                                    res = await dispatch('handleAuthorization', payload);
                                    resolve(res);
                                } catch (error) {
                                    console.error(error);
                                }
                            } else {
                                if (payload.type === 'privacyAgreement') {
                                    res.result = 'success';
                                    resolve(res);
                                    return;
                                }
                                try {
                                    // 隐私协议的确认和取消
                                    res = await dispatch('getInfoAndResolve', payload);
                                    resolve(res);
                                } catch (error) {
                                    console.error(error);
                                }
                            }
                        },
                        fail: error => {
                            console.error(error, '查询隐私授权情况====fail');
                        },
                    });
                } catch (e) {
                    let res = await dispatch('getInfoAndResolve', payload);
                    resolve(res);
                }
            });
        },
        // 弹出隐私协议弹窗
        async handleAuthorization({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                try {
                    let res = {};
                    await store.dispatch('receiveButtonParameters', {
                        async privacySuccess(resCb) {
                            if (resCb.confirm) {
                                // noLogin-view.vue 页面  noLoginOpenLocation 方法使用
                                if (payload.type === 'privacyAgreement') {
                                    res.result = 'success';
                                    resolve(res);
                                    return;
                                }
                                res = await dispatch('getInfoAndResolve', payload);
                                if (res.success && res.data.newMember) {
                                    store.dispatch('initLocationV3_app');
                                }
                                resolve(res);
                            } else {
                                res.result = 'fail';
                                resolve(res);
                                console.log(res, '拒绝使用隐私协议');
                            }
                        },
                    });
                } catch (error) {
                    console.error(error, 'handleAuthorization====error');
                    // reject();
                }
            });
        },
        // 隐私协议的确认和取消
        async getInfoAndResolve({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                try {
                    // 获取用户基本信息
                    let res = await dispatch('getInfo', payload);
                    resolve(res);
                } catch (error) {
                    console.error(error);
                    reject();
                }
            });
        },

        // 获取用户基本信息
        async getInfo({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                try {
                    // this.wxCode = await this._login();
                    // wxCode = await dispatch('login');
                    await dispatch('checkScopeUserInfo');
                    const userInfo = await dispatch('getUserInfo');
                    console.log(userInfo, 'getUserInfo');
                    store.commit('setUserInfo', userInfo);
                    store.commit('setMpUserInfo', userInfo);
                    let res = await dispatch('thirdLoginFun', payload);
                    resolve(res);
                } catch (error) {
                    console.error(error);
                    store.commit('setMaskDialogFlag', false);
                    reject();
                }
            });
        },
        // 获取用户的当前设置。
        checkScopeUserInfo({ state, commit, dispatch }) {
            // #ifndef H5-CLOUD
            return new Promise((resolve, reject) => {
                uni.getSetting({
                    success: res => {
                        if (res.authSetting['scope.userInfo']) {
                            resolve();
                        } else {
                            reject('scope.userInfo 未授权');
                        }
                    },
                    fail: err => {
                        reject('scope.userInfo 未授权');
                        console.log(err);
                    },
                });
            });
            // #endif
        },
        // 获取授权信息
        getUserInfo({ state, commit, dispatch }) {
            return new Promise((resolve, reject) => {
                uni.getUserInfo({
                    // 获取授权信息
                    withCredentials: true,
                    success: res => {
                        resolve(res);
                    },
                    fail: error => {
                        reject(error);
                    },
                });
            });
        },
        thirdLoginFun({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                let wxCode = '';
                // 是否成功登入3.0
                let isThirdDialog = false;
                // #ifndef H5-CLOUD
                wxCode = await dispatch('login');
                console.log(wxCode, 'wxCode');
                // #endif
                console.log(store.state.staffStationId, '接收的参数');
                let params = {
                    city: '',
                    staffInviteCode: store.state.staffStationId || '',
                    mobile: payload?.mobile || '',
                    messageCode: payload?.messageCode || '',
                    tmpCode: state.loginTemporaryCode || '',
                    loginType: payload?.type || '',
                    ignoreCppeiToken: true,
                };
                // 邀请人记录功能
                if (Number(store.state.location?.officialAccountParams?.invitationType) === 7 && !store.state.token3) {
                    const { invitationType, memberNo, shareId, flowNo } = store.state.location.officialAccountParams;
                    console.log(invitationType, memberNo, shareId, flowNo, '记录参数');
                    params.inviteMemberNo = memberNo || '';
                    params.inviteType = invitationType || '';
                    params.memberInviteCode = Number(shareId) || '';
                }
                console.log(payload, payload?.detail, 'thirdLoginFun===接受的参数');
                if (!payload?.detail?.code && payload?.type === '11') {
                    store.dispatch('zjShowModal', {
                        title: '提示',
                        content: '当前微信版本过低,请升级微信到最新版本',
                        success: res => {
                            if (res.confirm) {
                                store.commit('setLoginButtonGrayedOut', true);
                                console.log('登录用户点击确定');
                            } else if (res.cancel) {
                                console.log('登录用户点击取消');
                            }
                        },
                    });
                    return;
                }
                if (payload?.type === '4') {
                    params.authInfo = wxCode || '';
                } else if (payload?.type === '11') {
                    params.authInfo = payload?.detail?.code;
                } else if (payload?.type === '1') {
                    params.jsCode = wxCode;
                } else if (payload?.type === '12') {
                    params.authInfo = payload?.authInfo;
                    params.wxVerifyResult = payload.wxVerifyResult;
                } else if (payload?.type === '13') {
                    params.authInfo = payload?.authInfo;
                }
                console.log(params, '登录的参数');
                let tokenInfo = uni.getStorageSync('tokenInfo');
                if (tokenInfo && tokenInfo.accessToken && tokenInfo.openId) {
                    let times = new Date().getTime();
                    if (times > Number(tokenInfo.expiresIn)) {
                        // 调用刷新接口
                        store.dispatch('tokenExpired', {
                            success: async RES => {
                                console.log(RES, '刷新token返回的数据');
                                RES.data.loginType = params.loginType;
                                let res = await dispatch('loginFun', RES);
                                resolve(res);
                            },
                            fail: async err => {
                                let res = await userLogin(params, { isCustomErr: true, is_error: true });
                                if (res.success) {
                                    res.data.loginType = params.loginType;
                                }
                                let loginRes = await dispatch('loginFun', res);
                                resolve(loginRes);
                            },
                        });
                    } else {
                        console.log(tokenInfo, '登录页面获取的tokenInfo');
                        tokenInfo.loginType = params.loginType;
                        let res = {
                            data: tokenInfo,
                        };
                        console.log(res, '=====token');
                        store.commit('setNewMember', res.data.newMember);
                        res.data.loginType = params.loginType;
                        store.commit('setLoginButtonGrayedOut', true);
                        let RES = await dispatch('loginSuccessful', res);
                        RES.success = true;
                        resolve(RES);
                    }
                } else {
                    try {
                        console.log('开始调用登录接口');
                        let res = await userLogin(params, { isCustomErr: true, is_error: true });
                        if (res.success) {
                            res.data.loginType = params.loginType;
                        }
                        let loginRes = await dispatch('loginFun', res);
                        console.log(loginRes, 'loginRes========');
                        resolve(loginRes);
                    } catch (e) {
                        console.log('没能调用到登录接口');
                        store.commit('setLoginButtonGrayedOut', true);
                    }
                }
            });
        },
        // 调用接口成功后判断各种code
        loginFun({ state, commit, dispatch }, res) {
            return new Promise(async (resolve, reject) => {
                if (res.success) {
                    // 风控
                    if (res.data.postLoginActionJsonStr) {
                        let postLoginActionJsonStr = JSON.parse(res.data.postLoginActionJsonStr);
                        commit('setAccessToken', res.data.accessToken);
                        store.commit('setLongTimeNotLogin', postLoginActionJsonStr.longTimeNotLogin);
                        // postLoginActionJsonStr.rlyzFlag = true
                        if (postLoginActionJsonStr.rlyzFlag) {
                            // 临时存储，触发风控时先将数据存储起来用户认证通过后，存储到tokenInfo中
                            let temporary2 = JSON.parse(res.data.cppeiLoginInfo);
                            let loginData = { ...temporary2, ...res.data };
                            res.data = loginData;
                            uni.setStorageSync('loginData', res);
                            console.log(res, 'loginData');
                            const basicInfo = await basicInfoQuery({}, { accessToken: res.data.accessToken });
                            // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                            let riskRes = basicInfo.data.identityAuthStatus;

                            // riskRes = 13;
                            if (riskRes == 13) {
                                // 打开实人认证的表单弹窗
                                store.commit('setRealNameDialogFlag', true);
                            } else if (riskRes == 15 || riskRes == 14) {
                                // 关闭人脸认证协议弹窗
                                store.dispatch('changeFacePop', true);
                            }
                            return;
                        }
                    }
                    store.commit('setNewMember', res.data.newMember);
                    // res.data.loginType = params.loginType;
                    store.commit('setLoginButtonGrayedOut', true);
                    let resBack = await dispatch('loginSuccessful', res);
                    console.log(resBack, 'resBack=========');
                    resolve(resBack);
                    sKit.mpBP.tracker('登录/注册', {
                        seed: 'loginOrRegistBiz',
                        pageID: 'loginSucessToast', // 页面名
                        channelID: projectConfig.clientCode, // C10/C12/C13
                        registerFlag: JSON.parse(res?.data?.postLoginActionJsonStr)?.registerFlag || false,
                    });
                } else {
                    uni.setStorageSync('tokenInfo', '');
                    // 用户拒绝授权后隐藏登录中状态，显示授权登录
                    store.commit('setLoginLoading', false);
                    if (res.errorCode == 'B_C20_008030') {
                        // 自动登录获取临时
                        commit('setLoginTemporaryCode', res.message);
                        res.result = 'fail';
                        resolve(res);
                        return;
                    } else if (res.errorCode == 'B_C20_008028') {
                        // 临时码过期清空临时码
                        commit('setLoginTemporaryCode', '');
                    } else if (res.errorCode == 'B_B00_200204') {
                        // 换取拉起人脸的key
                        store.dispatch('zjShowModal', {
                            content: '系统检测到您已更换设备登录，请实人认证通过后继续登录',
                            confirmText: '同意并继续',
                            cancelText: '我再想想',
                            success: async val => {
                                if (val.confirm) {
                                    let riskRes = await dispatch('toFace', res);
                                    console.log('登录用户点击确定');
                                    console.log(riskRes, 'riskRes======触发风控人脸验证通过返回的');
                                    resolve(riskRes);
                                    // if (handleErrorFn) handleErrorFn(result);
                                } else if (val.cancel) {
                                    let url = '/packages/third-new-third-login/pages/login/main';
                                    let params = {};
                                    let type = 'redirectTo';
                                    sKit.layer.useRouter(url, params, type);
                                }
                            },
                        });
                        return;
                    }
                    store.commit('setLoginButtonGrayedOut', true);
                    if (res.errorCode != 'B_B00_200204') {
                        store.dispatch('zjShowModal', {
                            title: res.message,
                            content: res.errorCode || '',
                            type: 'http',
                            success: res => {
                                if (res.confirm) {
                                    console.log('登录用户点击确定');
                                    // if (handleErrorFn) handleErrorFn(result);
                                } else if (res.cancel) {
                                    console.log('登录用户点击取消');
                                }
                            },
                        });
                    }
                    console.log(res, '登录失败或者刷新token失败执行打印');
                    resolve(res);
                }
            });
        },
        // 更换微信人脸验证
        toFace({ state, commit, dispatch }, res) {
            return new Promise(async (resolve, reject) => {
                let initKeyParams = {
                    faceCode: res.message,
                };
                let initKeyRes = await initWechatFaceLoginByCode(initKeyParams, { isCustomErr: true, is_error: true });
                if (initKeyRes.success) {
                    console.log(initKeyRes.data.userIdKey, 'initKeyRes.data.userIdKey');
                    wx.startFacialRecognitionVerify({
                        userIdKey: initKeyRes.data.userIdKey,
                        success: async r => {
                            if (r) {
                                let faceLoginParams = {
                                    wxVerifyResult: r.verifyResult,
                                    type: '12', // 更换设备，人脸认证登录；
                                    authInfo: res.message,
                                };
                                let toFaceRes = await dispatch('thirdLoginFun', faceLoginParams);
                                console.log(toFaceRes, 'toFaceRes=====人脸验证通过登录成功');
                                resolve(toFaceRes);
                            }
                        },
                        fail: e => {
                            store.dispatch('zjShowModal', {
                                title: e.errMsg,
                                content: e.errCode || '',
                                success: res => {
                                    if (res.confirm) {
                                        console.log('登录用户点击确定');
                                        // if (handleErrorFn) handleErrorFn(result);
                                    } else if (res.cancel) {
                                        console.log('登录用户点击取消');
                                    }
                                },
                            });
                        },
                    });
                    //  登录返回的临时码过期调用initWechatFaceLoginByCode的报错
                } else if (res.errorCode == 'B_B02_000000') {
                    //  清空下临时码放置一直使用过期的临时码调用接口
                    commit('setLoginTemporaryCode', '');
                    store.dispatch('zjShowModal', {
                        title: res.message,
                        content: res.errorCode || '',
                        type: 'http',
                        success: res => {
                            if (res.confirm) {
                                console.log('登录用户点击确定');

                                // if (handleErrorFn) handleErrorFn(result);
                            } else if (res.cancel) {
                                console.log('登录用户点击取消');
                            }
                        },
                    });
                }
            });
        },
        // 登录接口调用成功保存信息
        loginSuccessful({ state, commit, dispatch }, res) {
            return new Promise(async (resolve, reject) => {
                let loginData = uni.getStorageSync('loginData');
                if (loginData) {
                    res = loginData;
                    uni.setStorageSync('loginData', '');
                }
                //目前能源运营可能会用
                uni.setStorageSync('wxMiniProgramOpenId', res.data.openId);
                // #ifdef MP-WEIXIN
                uni.setStorageSync('userId', res.data.memberNo);
                // #endif
                // res.data.expiresIn = '1748334335000';
                uni.setStorageSync('tokenInfo', res.data);
                // #ifndef H5-CLOUD
                store.commit('setOpenId', res.data.openId);
                sKit.mpBP.setUserInfo({
                    user_id: res.data.memberNo,
                });
                // #endif

                state.loginTemporaryCode = '';
                // 保存3.0个人信息
                await store.commit('mSetPersonalInformation3', res.data);
                let newMember = res.data.newMember;
                console.log(res.data, '登录信息');
                console.log(newMember, '3.0还是2.0用户');
                // 如果newMember == true代表是3.0用户
                if (newMember) {
                    // 存储3.0登录信息
                    await dispatch('storeLoginInformation', res.data);
                } else {
                    //如果是2.0会员
                    let tokenInfo = uni.getStorageSync('tokenInfo');
                    let params = {
                        openId: tokenInfo.openId,
                    };
                    let res2 = await getCppeiLoginInfo(params, { isCustomErr: true });
                    if (res2.success) {
                        console.log(JSON.parse(res2.data.cppeiLoginInfo), res.data, '=====2.0数据');
                        let login2 = JSON.parse(res2.data.cppeiLoginInfo);
                        // 保存用户登录信息
                        dispatch('saveUserInfo', login2);
                        // 这里是因为APP.vue中清除了2.0token。合并会将2.0token覆盖
                        delete res.data.token;
                        let mergedObj2 = Object.assign({}, login2, res.data);
                        console.log(mergedObj2, '合并的数据222222');
                        uni.setStorageSync('tokenInfo', mergedObj2);
                    } else {
                        //如果是2.0会员，但是获取2.0token失败，怎么处理？待完善
                        uni.setStorageSync('tokenInfo', '');
                        store.dispatch('zjShowModal', {
                            title: res.message,
                            content: res.errorCode || '',
                            type: 'http',
                            success: res => {
                                if (res.confirm) {
                                    console.log('登录用户点击确定');
                                    // if (handleErrorFn) handleErrorFn(result);
                                } else if (res.cancel) {
                                    console.log('登录用户点击取消');
                                }
                            },
                        });
                        resolve(res);
                        return;
                    }
                    console.log(store.state.staffStationId, '登录完是2.0会员，然后看下是不是推进开卡或者注册进来的');
                    //如果是2.0的话，判断是不是推荐开卡或者注册进来的，如果是的话需要走升级流程。
                    if (store.state.staffStationId) {
                        store.dispatch('getToken3', 'upgrade');
                        return;
                    }
                    uni.reLaunch({
                        url: '/pages/home/<USER>',
                    });
                    resolve(res);
                    console.log(res, '2.0登录成功');
                    return;
                }
                console.log(res, '========查看loginType');
                resolve(res);
                console.log(res, '3.0登录成功');
            });
        },
        // 获取WXcode
        login({ state, commit, dispatch }, payload) {
            return new Promise(resolve => {
                uni.login({
                    success: res => {
                        console.log(res.code, 'res.code===获取成功');
                        resolve(res.code);
                    },
                    fail: err => {
                        console.log(err, '获取微信code失败的原因');
                    },
                });
            });
        },
        // 保存2.0用户信息
        saveUserInfo({ state, commit, dispatch }, payload) {
            return new Promise((resolve, reject) => {
                store.state.token = payload?.token || '';
                store.state.token3 = payload?.accessToken || '';
                store.state.phone = payload?.phone;
                store.state.registerLoginInformation = payload;
                store.state.openId = payload?.openid;
                store.state.unionId = payload?.unionId;
                store.state.newUserTag = payload?.newUserTag;
                store.commit('setLoginStatus', true);
                // if (store.state.token3) {
                //     callback && callback(res);
                // }
            });
        },
        // 存储登录信息
        storeLoginInformation({ state, commit, dispatch }, payload) {
            return new Promise((resolve, reject) => {
                uni.setStorageSync('tokenInfo', payload);
                store.commit('setToken3', payload.accessToken);
                //token失效时的时间戳（未来时间）,客户端可以根据该数值比对当前的时间戳，来判断token是否需要续期。
                uni.setStorageSync('expiresInV3', payload.expiresIn);
                // 用于token过期时 调用刷新token的入参
                uni.setStorageSync('refreshTokenV3', payload.refreshToken);
                //3.0token，只用于sdk访问
                store.commit('mSetGsmsToken', payload.gsmsToken);
                // // 登陆成功给标识赋值
                // isThirdDialog = true;
                resolve();
            });
        },
        // 根据公众号或者小程序跳转过来携带的参数以及是否存在3.0token跳转2.0或者3.0相应页面
        async jumpToOfficialAccount({ state, commit, dispatch }, payload) {
            console.log(payload, '跳转时接受的参数');
            console.log(state.logInAgain, '领券登录跳转3.0电子券页面标识');
            let token3 = payload.accessToken;
            const { wxPayIsLoginFlag } = state;
            let officialAccountParams = store.state.location.officialAccountParams;
            console.log(officialAccountParams, token3, 'officialAccountParams====登录成功的打印');
            let loginInfo2 = JSON.parse(payload.cppeiLoginInfo);
            console.log(loginInfo2?.token, '2.0token');
            if (officialAccountParams == 'zf' && !state.wxPayIsLoginFlag) {
                return;
            }
            if (officialAccountParams?.invitationType == 7) {
                if (payload.postLoginActionJsonStr) {
                    let postLoginActionJsonStr = JSON.parse(payload.postLoginActionJsonStr);
                    officialAccountParams.registerFlag = postLoginActionJsonStr.registerFlag;
                }
                sKit.layer.useRouter('/packages/thirdMarketing/pages/fissionSharing/main', officialAccountParams, 'reLaunch');
                return;
            }
            // 微信支付活动领券禁止向下跳转
            if (!wxPayIsLoginFlag) return;
            // 如果参数是 /packages/third || third/ 代表参数是路由页面的路径，直接进行跳转
            if (
                typeof officialAccountParams === 'string' &&
                (officialAccountParams.includes('/packages/third') ||
                    officialAccountParams.includes('third/') ||
                    officialAccountParams.includes('/pages/thirdHome/main'))
            ) {
                uni.reLaunch({
                    url: officialAccountParams,
                });
                return;
            }
            // 在进行跳转开通钱包和钱包设置页面
            if (token3 && payload.newMember) {
                let res = await store.dispatch('getSetWalletStatus', {});
                if (!res.success) {
                    uni.reLaunch({
                        url: '/pages/thirdHome/main',
                    });
                    store.dispatch('setOfficialAccountParams', '');
                    return;
                }
                // e享卡是否开通状态
                let status = store.state.wallet.walletStatus.status;
                // e享卡开通以后的状态
                let accountStatus = store.state.wallet.walletStatus.accountStatus;

                // 开通昆仑e享卡页面
                let activateECardUrl = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                // e享卡设置页面
                let eCardSettingsUrl = '/packages/third-my-wallet/pages/wallet-setting/main';
                // 点击我的钱包--->详情页面
                let walletDetailsUrl = '/packages/third-my-wallet/pages/home/<USER>';
                // 掌纹开卡页面跳转判断
                const brushPalmUrl = !status ? activateECardUrl : accountStatus == 3 ? activateECardUrl : eCardSettingsUrl;
                // 员工邀请开卡
                const employeeInvitationCardIssuanceUrl = !status
                    ? activateECardUrl
                    : accountStatus == 3
                    ? activateECardUrl
                    : walletDetailsUrl;
                const EcardUrl = !status ? activateECardUrl : accountStatus == 3 ? activateECardUrl : walletDetailsUrl;
                // 充值页面内需要考虑未开通电子钱包的情况
                // 会员码页面可能存在插件未初始化的情况
                // cz = 充值 dzq = 电子券 fp = 发票 brushPalm = 掌纹开卡 2 = 员工邀请开卡 hym = 会员码 dd = 订单 wd = 我的 yt = 优途小程序跳转 '' = 没有任何跳转(默认值)

                const routerMapThird = {
                    cz: '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                    dzq: '/packages/third-coupon-module/pages/coupon-list/main',
                    fp: '/packages/third-invoice/pages/home/<USER>',
                    brushPalm: `${brushPalmUrl}`,
                    2: `${employeeInvitationCardIssuanceUrl}`,
                    Ecard: EcardUrl,
                    hym: '/packages/third-scan-code-payment/pages/home-code/main',
                    dd: '/pages/thirdHome/main',
                    wd: '/pages/thirdHome/main',
                    yt: '/pages/thirdHome/main',
                    qb: '/pages/thirdHome/main',
                    fissionSharing: '/packages/thirdMarketing/pages/fissionSharing/main',
                    couponCollection: '/packages/thirdMarketing/pages/couponCollection/main',
                    luckyDraw: '/packages/thirdMarketing/pages/luckyDraw/main',
                    hdshare: '/packages/third-helpShare/pages/helpShare/main',
                    helpShare: '/packages/third-helpShare/pages/helpShare/main',
                    undefined: '/pages/thirdHome/main',
                    '': '/pages/thirdHome/main',
                };
                let routerKey = officialAccountParams;
                let routerParams = '';

                if (typeof officialAccountParams === 'string' && officialAccountParams?.includes('#;')) {
                    routerKey = officialAccountParams.split('#;')[0];
                    routerParams = officialAccountParams.split('#;')[1];
                }
                console.log(routerMapThird[routerKey], '[officialAccountParams]');
                let urlThird = routerMapThird[routerKey];
                // 定义参数映射对象，存储不同条件下的参数设置
                const paramsMap = {
                    2: {
                        [walletDetailsUrl]: { refer: 'r03' },
                        [activateECardUrl]: { refer: 'r04' },
                    },
                    brushPalm: {
                        [activateECardUrl]: { refer: 'r19' },
                    },
                    Ecard: {
                        [walletDetailsUrl]: { refer: 'r08' },
                        [activateECardUrl]: { refer: 'r20' },
                    },
                    cz: {
                        '/packages/third-remaining-sum/pages/third-wallet-recharge/main': { refer: 'r12' },
                    },
                    hym: {
                        '/packages/third-scan-code-payment/pages/home-code/main': { refer: 'r30' },
                    },
                    fp: {
                        '/packages/third-invoice/pages/home/<USER>': { refer: 'r37' },
                    },
                    dzq: {
                        '/packages/third-coupon-module/pages/coupon-list/main': { refer: 'r42' },
                    },
                    dd: { '/pages/thirdHome/main': { refer: 'r52' } },
                };
                if (state.activityStr.length != 0) {
                    console.log(typeof state.activityStr, '是字符串吗');
                    urlThird = `${urlThird}?${state.activityStr}`;
                    console.log(urlThird, 'state.activityStr ====');
                }
                // 检查是否存在对应的routerKey和routerMapThird值
                if (paramsMap.hasOwnProperty(routerKey) && routerMapThird.hasOwnProperty(routerKey)) {
                    const url = routerMapThird[routerKey];
                    // 检查是否存在对应的参数设置
                    if (paramsMap[routerKey].hasOwnProperty(url)) {
                        // 设置routerParams参数值
                        if (state.outsideRefer) {
                            routerParams = `data=${encodeURIComponent(JSON.stringify({ refer: state.outsideRefer }))}`;
                        } else {
                            routerParams = `data=${encodeURIComponent(JSON.stringify(paramsMap[routerKey][url]))}`;
                        }
                    }
                }
                if (typeof officialAccountParams === 'string' && officialAccountParams == 'cz' && !status) {
                    store.dispatch('zjShowModal', {
                        title: '',
                        content: '您还没有开通昆仑e享卡，是否开通？',
                        confirmText: '去开通',
                        cancelText: '取消',
                        confirmColor: '',
                        cancelColor: '',
                        type: '',
                        success: res => {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                                let params = { refer: 'r08' };
                                let type = 'reLaunch'; // 默认  uni.navigateTo({})
                                Layer.useRouter(url, params, type);
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                                uni.reLaunch({
                                    url: '/pages/thirdHome/main',
                                });
                                return;
                            }
                        },
                    });
                    return;
                }
                if (urlThird) {
                    let url = routerParams ? urlThird + '?' + routerParams : urlThird;
                    console.log('哪里先执行======111111', url);
                    uni.reLaunch({ url: url });
                } else {
                    // 微信扫码拿到的活动链接
                    if (typeof officialAccountParams === 'string' && officialAccountParams.includes('https')) {
                        await store.dispatch('memberBaseInfoAction');
                        let tokenInfo = uni.getStorageSync('tokenInfo');
                        let memberNo = store.state.member.memberBaseInfo.memberNo || tokenInfo.memberNo;
                        if (officialAccountParams.includes('openId')) {
                            officialAccountParams = officialAccountParams.replace('&openId=', `&openId=${store.state.openId || ''}`);
                        }
                        // 3.1.3.2版本 先定的是gsmsToken 后来换为 accessToken ，参数名没说要变
                        console.log(`${officialAccountParams}`, '拼接参数后的链接');
                        console.log(`${officialAccountParams}&gsmsToken=${token3}&clientCode=C12&userId=${memberNo}`, '拼接参数后的链接');
                        uni.reLaunch({
                            url: `/packages/web-view/pages/home/<USER>
                                `${officialAccountParams}&gsmsToken=${token3}&clientCode=C12&userId=${memberNo}&openId=${tokenInfo.openId}`,
                            )}`,
                        });
                    } else {
                        console.log('测试跳转首页');
                        uni.reLaunch({
                            url: '/pages/thirdHome/main',
                        });
                    }
                }
            } else if (payload.token && !payload.newMember) {
                console.log('2.0用户跳转====JS');
                if (typeof officialAccountParams === 'string' && officialAccountParams == 'dzk') {
                    officialAccountParams = await dispatch('judgingElectronicCards', payload);
                    const routerMap = {
                        cz: '/packages/oil-card/pages/manage-oilcard/main',
                        dzq: '/packages/coupon/pages/home/<USER>',
                        fp: '/packages/invoice-center/pages/home/<USER>',
                        brushPalm: '/pages/home/<USER>',
                        ykgl: '/packages/oil-card/pages/manage-oilcard/main',
                        dzk: '/packages/ecard-apply/pages/apply/main',
                        xfjl: '/packages/my-center/pages/xf-records/main',
                        yt: '/pages/home/<USER>',
                        '': '/pages/home/<USER>',
                    };

                    const url = routerMap[officialAccountParams] || '/pages/home/<USER>';
                    if (url) {
                        console.log({ url }, 'url是什么');
                        uni.reLaunch({ url: url });
                    } else {
                        if (officialAccountParams == 2) {
                            dispatch('getToken3', 'upgrade');
                        } else if (officialAccountParams.includes('https')) {
                            uni.showModal({
                                title: '提示',
                                content: `请先进行系统升级（3.0），再扫码。`,
                                confirmColor: '#FF8200',
                                confirmText: '确认',
                                showCancel: false,
                                success: res => {
                                    if (res.confirm) {
                                        uni.reLaunch({
                                            url: `/pages/home/<USER>
                                        });
                                    }
                                },
                            });
                        } else {
                            uni.reLaunch({
                                url: `/pages/home/<USER>
                            });
                        }
                    }
                }
            }
        },
        // 2.0用户跳转油卡列表或者油卡管理页面
        async judgingElectronicCards({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                try {
                    console.log(store.state.card.cardList, '油卡列表====');
                    let res = await cardListPost();
                    console.log(res, '油卡信息');
                    if (res.status == 0 && res.data.length > 0) {
                        store.state.card.cardList = res.data;
                        let cardRes = res.data.filter(item => item.cardType == 1);
                        console.log(cardRes, 'cardRes');

                        if (cardRes.length > 0 && cardRes[0].cardType == 1 && !payload.newMember) {
                            store.dispatch('setOfficialAccountParams', 'ykgl');
                            resolve('ykgl');
                        }
                    } else {
                        // if (officialAccountParams == 'dzk' || 'ykgl') {
                        //     store.dispatch('setOfficialAccountParams', '');
                        // }
                        resolve('');
                    }
                } catch (error) {
                    console.error('cardListPost() 接口调用出错:', error);
                    resolve('');
                    // 可以根据需要处理错误，或者不做任何处理继续执行下面的逻辑
                }
            });
        },
        // 登录失败时点击立即登录需要再次调用自动登录接口
        callAutomaticLoginAgain({ state, commit, dispatch }, payload) {
            return new Promise(async (resolve, reject) => {
                // 如果自动登录获取临时码失败执行下面的代码
                let res = await dispatch('init', { type: '4' });
                // 登录按钮变亮
                store.commit('setLoginButtonGrayedOut', true);
                if (res.success) {
                    resolve(res);
                } else {
                    if (res.errorCode == 'B_C20_008030') {
                        commit('setLoginTemporaryCode', res.message);
                        if (payload) {
                            let url = '/packages/third-new-third-login/pages/login/main';
                            let params = {};
                            sKit.layer.useRouter(url, params);
                        } else {
                            resolve(res);
                        }
                    }
                }
            });
        },
    },
};
