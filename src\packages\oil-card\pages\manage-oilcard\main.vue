<!--  -->
<template>
    <div>
        <div>
            <FrostedGlass @change="getCardList"></FrostedGlass>
        </div>
        <div class="manage-page">
            <div class="manage-content">
                <u-navbar
                    :background="pageConfig.bgColor"
                    :back-icon-size="40"
                    :back-icon-color="pageConfig.titleColor.backIconColor"
                    :height="44"
                    :title-color="pageConfig.titleColor.color"
                    back-text="油卡管理"
                    :back-text-style="pageConfig.titleStyle"
                    :border-bottom="false"
                ></u-navbar>
                <swiper
                    class="swiper"
                    :indicator-dots="indicatorDots"
                    :autoplay="autoplay"
                    :interval="interval"
                    :duration="300"
                    :current="swiperIndex"
                    :previous-margin="previousMargin"
                    :next-margin="nextMargin"
                    @change="handleChangeSwiperItem"
                >
                    <swiper-item v-if="cardList.length > 0" v-for="(item, index) in cardList" :key="index" class="swiper-box">
                        <!-- backgroundImage: item.cardTypeImg!== "" && item.cardType == 1 ? `url(${item.cardTypeImg})` : "url(" + (item.cardType == 1 ? naviBgdianzika:naviBgshitika)  + ")", -->
                        <div
                            class="manage-card"
                            :style="{
                                backgroundImage:
                                    item.cardTypeImg && item.cardType == 1
                                        ? `url(${item.cardTypeImg})`
                                        : 'url(' + (item.cardType == 0 ? naviBgshitika : naviBg) + ')',
                                marginRight: swiperMargin.marginRight,
                                borderRadius: item.cardTypeImg && item.cardType == 1 ? '8px' : '',
                            }"
                        >
                            <div class="header-div">
                                <div class="info-div">
                                    <div class="logo-div">
                                        <img src="@/static/cnpc-logo.png" alt mode="widthFix" />
                                    </div>
                                    <div class="right-div">
                                        <div class="top-view">
                                            <span class="top-div">
                                                {{ item.cardType == 1 ? '中国石油电子油卡' : '中国石油加油卡' }}
                                            </span>
                                            <span class="top-icon">移动支付</span>
                                            <span class="top-icon" v-if="item.isDefaultCard">默认卡</span>
                                        </div>
                                        <div class="name-div" @click="eyebtn($event, index)">
                                            <span class="name" v-if="!item.activeed">{{ item.userNameShow }}</span>
                                            <span v-else class="name">{{ item.userName }}</span>
                                            <span class="card-number" v-if="!item.activeed">{{ item.cardNoShow }}</span>
                                            <span v-else class="card-number">{{ item.cardNo }}</span>
                                            <!-- <img
                        :src="
                          !item.activeed
                            ? '/static/white-eye-close.png'
                            : '/static/white-eye-open.png'
                        "
                        alt
                        class="eye-block-iocn1"
                        mode="widthFix"
                        :data-item="item"
                      />-->
                                            <img
                                                class="eye-block-iocn1"
                                                src="@/static/white-eye-close.png"
                                                v-if="!item.activeed"
                                                mode="widthFix"
                                                :data-item="item"
                                                alt
                                            />
                                            <img
                                                class="eye-block-iocn1"
                                                src="@/static/white-eye-open.png"
                                                v-if="item.activeed"
                                                mode="widthFix"
                                                :data-item="item"
                                                alt
                                            />
                                        </div>
                                        <div class="address-text">{{ item.allAddress }}</div>
                                    </div>
                                </div>
                                <div class="bottom-div" v-if="cardList.length > 0">
                                    <div class="flex-row">
                                        <div class="flex-item" v-if="item.cardType == 0">
                                            <div class="row-div">
                                                <span class="row-left">圈存金</span>
                                                <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                <template v-else>
                                                    <span class="row-price">¥</span>
                                                    <span class="row-text">{{ item.cardBalance }}</span>
                                                </template>
                                            </div>
                                            <div class="row-div">
                                                <span class="row-left">积分</span>
                                                <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                <span class="row-text" style="margin-left: 10px" v-else>{{ item.cardLoyaltyBalance }}</span>
                                            </div>
                                        </div>
                                        <div class="flex-item">
                                            <div class="row-div">
                                                <span class="row-left">备用金</span>
                                                <span class="row-price" v-if="item.activeed">¥</span>
                                                <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                <span class="row-text" v-else>{{ item.balance }}</span>
                                            </div>
                                            <div class="row-div">
                                                <span class="row-left">积分</span>
                                                <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                <span class="row-text" style="margin-left: 10px" v-else>{{ item.loyaltyBalance }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row-div">
                                        <span class="row-left" v-if="item.cardExpiredTime">有效期至：{{ item.cardExpiredTime }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </swiper-item>
                </swiper>
                <div class="section-list">
                    <div class="table-view-list" @click="hangleCard('oilCard')">
                        <div class="table-view-list-text">
                            <div class="left">
                                <img src="@/static/icon-1.png" alt class="left-icon" mode="widthFix" />
                            </div>
                            <div class="center">
                                <div class="center-txt">油卡充值</div>
                            </div>
                            <div class="table-view-list-arrow"></div>
                        </div>
                    </div>
                    <div class="table-view-list" @click="hangleCard('xf-records')">
                        <div class="table-view-list-text">
                            <div class="left">
                                <img src="@/static/icon-2.png" alt class="left-icon" mode="widthFix" />
                            </div>
                            <div class="center">
                                <div class="center-txt">消费记录</div>
                            </div>
                            <div class="table-view-list-arrow"></div>
                        </div>
                    </div>
                    <div class="table-view-list" @click="hangleCard('cz-records')">
                        <div class="table-view-list-text">
                            <div class="left">
                                <img src="@/static/icon-3.png" alt class="left-icon" mode="widthFix" />
                            </div>
                            <div class="center">
                                <div class="center-txt">充值记录</div>
                            </div>
                            <div class="table-view-list-arrow"></div>
                        </div>
                    </div>
                    <div
                        class="table-view-list"
                        @click="hangleCard('qc-records')"
                        v-if="cardList.length > 0 && cardList[swiperIndex].cardType == 0"
                    >
                        <div class="table-view-list-text">
                            <div class="left">
                                <img src="@/static/qc-iocn.png" alt class="left-icon" mode="widthFix" />
                            </div>
                            <div class="center">
                                <div class="center-txt">圈存记录</div>
                            </div>
                            <div class="table-view-list-arrow"></div>
                        </div>
                    </div>
                    <!-- <div
            class="table-view-list"
            @click="hangleCard('fuelJam')"
            v-if="cardList[swiperIndex].cardType == 0"
          >
            <div class="table-view-list-text">
              <div class="left">
                <img src="@/static/qc-iocn.png" alt class="left-icon" mode="widthFix" />
              </div>
              <div class="center">
                <div class="center-txt">油卡换肤</div>
              </div>
              <div class="table-view-list-arrow"></div>
            </div>
          </div>-->
                    <!-- 优惠合同 -->
                    <!-- <div class="table-view-list" @click="hangleCard('yh-records')">
            <div class="table-view-list-text">
              <div class="left">
                <img src="@/static/card-manage-yhht.png" alt class="left-icon" mode="widthFix" />
              </div>
              <div class="center">
                <div class="center-txt">优惠合同</div>
              </div>
              <div class="table-view-list-arrow"></div>
            </div>
          </div>-->
                    <div class="table-view-list">
                        <div class="table-view-list-text">
                            <div class="left">
                                <img src="@/static/my-oil.png" alt class="left-icon" mode="widthFix" />
                            </div>
                            <div class="center">
                                <div class="center-txt">设为默认卡</div>
                            </div>
                            <div class="right">
                                <u-switch
                                    v-model="cardList[swiperIndex].isDefaultCard"
                                    @change="setDefaultCard"
                                    active-color="#FF8200"
                                ></u-switch>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="add-btn" v-if="cardList[swiperIndex].cardType !=0" @click="addbtn">
              <img src="@/static/add-btn.png" alt="" class="add-img">
              <div>添加油卡</div>
        </div>-->
                <div class="add-btn" v-if="(!isHaveEntityCard && cardList.length < 2) || cardList.length == 0" @click="addbtn">
                    <img src="@/static/add-btn.png" alt class="add-img" />
                    <div>添加油卡</div>
                </div>
                <div class="clear-btn" v-if="cardList[swiperIndex].cardType == 0" @click="unoilcardtap">
                    <div>解除绑定</div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import { mapGetters } from 'vuex';
import { getMsgTemplates, cardListPost, getCardDetailApi, setDefaultCardApi, unBinding } from '@/api/home.js';
import projectConfig from '../../../../../project.config';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            projectConfig,
            naviBg: '',
            naviBgshitika: '',
            indicatorDots: false,
            autoplay: false,
            isDefaultCard: false,
            swiperIndex: 0,
            isClearBtn: 0,
            nextMargin: '0',
            previousMargin: '0',
        };
    },
    components: {
        FrostedGlass,
    },
    //生命周期 - 创建完成（访问当前this实例）
    onShow() {
        if (this.cardList.length === 2) {
            this.nextMargin = '20rpx';
        }
    },

    onLoad(option) {
        this.loadImages();
        // console.log(this.isHaveEntityCard, 'ppppppppppp', option.index);
        this.swiperIndex = option.index ? option.index : 0;
        this.cardListApi();
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/dianzikabg.png', 'base64');
        // this.naviBgshitika = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/shitikabg.png', 'base64');
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {},
    methods: {
        async loadImages() {
            this.naviBg = await this.fetchAndConvertToBase64(this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/dianzikabg.png');
            this.naviBgshitika = await this.fetchAndConvertToBase64(
                this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/shitikabg.png',
            );
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        // 获取卡列表
        getCardList() {
            this.$store.dispatch('card/getAllCardList');
        },
        // current 改变时会触发
        async handleChangeSwiperItem(e) {
            const { current, source } = e.detail;
            if (source == 'touch') {
                //用户触摸引起
                this.swiperIndex = current;
                if (current == this.cardList.length - 1) {
                    this.nextMargin = 0;
                } else {
                    this.nextMargin = '20rpx';
                }
                if (current == 0) {
                    this.previousMargin = 0;
                } else {
                    this.previousMargin = '30rpx';
                }
            }
        },

        async cardListApi() {
            if (this.cardList.length <= 1) {
                this.nextMargin = 0;
            } else {
                this.nextMargin = '20rpx';
            }
            // if (oilCardRes.data.length > 0) {
            //   for (let i = 0; i < oilCardRes.data.length; i++) {
            //     let oilCard = await getCardDetailApi({
            //       cardNo: oilCardRes.data[i].cardNo
            //     })
            //     oilCardRes.data[i] = oilCard.data
            //     oilCardRes.data[i].afterNum = oilCard.data.cardNo.substring(oilCard.data.cardNo.length - 4, oilCard.data.cardNo.length);
            //     oilCardRes.data[i].cardType = oilCardResTmp.data[i].cardType
            //     this.isDefaultCard = oilCardRes.data[i].isDefaultCard
            //   }
            //   this.cardList = oilCardRes.data
            //   console.log('this.cardList',this.cardList)
            // }
        },
        async hangleCard(type) {
            console.log(type);
            let cardNo = this.cardList[this.swiperIndex].cardNo;
            if (type === 'oilCard') {
                let res = await getMsgTemplates();
                if (res.status === 0) {
                    uni.navigateTo({
                        url: '/packages/oil-card/pages/home/<USER>' + this.swiperIndex,
                    });
                }
            } else if (type === 'cz-records') {
                uni.navigateTo({
                    url: `/packages/oil-card/pages/cz-records/main?cardNo=${cardNo}`,
                });
            } else if (type === 'xf-records') {
                uni.navigateTo({
                    url: `/packages/oil-card/pages/xf-records/main?cardNo=${cardNo}`,
                });
            } else if (type === 'qc-records') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/load-records/main?cardNo=' + cardNo,
                });
                // 优惠合同
            } else if (type === 'yh-records') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/yh-records/main?cardNo=' + cardNo,
                });
            } else if (type === 'fuelJam') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/oilCardSkinChange/main',
                });
            } else {
                return;
            }
        },
        async setDefaultCard() {
            let isTop = this.cardList[this.swiperIndex].isDefaultCard; // 是否需要切换swiperIndex
            setTimeout(() => {
                this.swiperIndex = isTop ? 0 : this.swiperIndex;
            }, 50); // 为了达到更好的效果
            await this.$store.dispatch('card/setDefaultCard', this.swiperIndex);
        },
        // 是否隐藏点击事件
        async eyebtn(e, index) {
            this.$store.dispatch('card/setCardActiveed', index);
        },
        addbtn() {
            uni.navigateTo({
                url: `/packages/oil-card/pages/add-oilcard/main?addCardFlag=addCard`,
            });
        },
        unoilcardtap(e) {
            // console.log(this.previousMargin, this.nextMargin, 'ooooooooooooooo');
            // return
            let item = this.cardList[this.swiperIndex];
            // this.$store.commit('card/setCardList', [])
            console.log(item);
            uni.showModal({
                title: '提示',
                content: `确认解除${item.cardNoShow}的绑定`,
                confirmText: '确认',
                confirmColor: '#FF8200',
                cancelText: '取消',
                success: async res => {
                    if (res.confirm) {
                        await this.$store.dispatch('card/unBindCard', this.swiperIndex);
                        this.previousMargin = 0;
                        this.swiperIndex = 0;
                        that.cardListApi();
                    } else if (res.cancel) {
                    }
                },
            });
        },
        //解绑油卡  暂时没用
        async getunBinding(item) {
            let params = {
                idName: item.name,
                cardNo: item.cardNo,
            };
            let res = await unBinding(params);
            if (res.status == 0) {
                // this.nextMargin = '0'
                // this.previousMargin = '0'
                // this.$store.dispatch("card/getAllCardList");
                uni.showToast({
                    title: '解绑成功',
                    icon: 'none',
                });
            }
        },
    },
    computed: {
        ...mapGetters(['cardList', 'isHaveEntityCard', 'cardTopinfo']),
        cardList() {
            return this.$store.getters['card/cardList'];
        },
        swiperMargin() {
            if (this.cardList.length === 2) {
                return {
                    marginRight: '20rpx',
                };
            } else {
                return {
                    marginRight: 0,
                };
            }
        },
    },
};
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */
.manage-page {
    width: 100%;
    // height: 100%;
    height: 100vh;
    background: #f6f6f6;

    .manage-content {
        padding: 12px;

        .swiper {
            width: 100%;
            height: 315rpx;

            .swiper-box {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 315rpx;
            }
        }

        .manage-card {
            width: 702rpx;
            height: 100%;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin: 0rpx 15rpx 0 0;
            z-index: 1;

            .header-div {
                width: 100%;
                padding: 27rpx 24rpx 24rpx;

                .info-div {
                    display: flex;
                    align-items: center;

                    .logo-div {
                        img {
                            width: 70rpx;
                            height: 70rpx;
                        }
                    }

                    .right-div {
                        margin-left: 12px;
                        .top-view {
                            display: flex;
                            align-items: center;
                            .top-div {
                                font-size: 14px;
                                color: #fff;
                                vertical-align: center;
                            }

                            .top-icon {
                                background: rgba(0, 0, 0, 0.17);
                                height: 28rpx;
                                line-height: 28rpx;
                                color: #fff;
                                font-size: 9px;
                                text-align: center;
                                vertical-align: center;
                                border-radius: 2px;
                                margin-left: 4px;
                                padding: 0 7rpx;
                            }
                        }

                        .name-div {
                            color: #fff;
                            font-size: 22rpx;
                            line-height: 20px;
                            opacity: 0.8;
                            display: flex;
                            align-items: center;
                            .name {
                                position: relative;
                                margin-right: 16rpx;

                                &.name:before {
                                    content: '';
                                    position: absolute;
                                    top: 0;
                                    right: -16rpx;
                                    bottom: 0;
                                    width: 1px;
                                    border-right: 1px solid #ebedf0;
                                    -webkit-transform-origin: 0 0;
                                    transform-origin: 0 0;
                                    -webkit-transform: scaleX(0.5);
                                    transform: scaleX(0.5);
                                    // z-index: 9;
                                }
                            }

                            .card-number {
                                margin-left: 16rpx;
                                display: inline-block;
                                vertical-align: baseline;
                            }
                            .eye-block-iocn1 {
                                width: 17px;
                                height: auto;
                                padding: 0 5px;
                                display: inline-block;
                                vertical-align: baseline;
                            }
                        }
                        .address-text {
                            font-size: 12px;
                            color: #fff;
                        }
                    }
                }
            }

            .bottom-div {
                margin-top: 20rpx;
                .flex-row {
                    display: flex;
                    align-items: center;
                    .flex-item {
                        width: 50%;
                    }
                }

                .row-div {
                    padding: 0 0 8rpx;
                    display: flex;
                    align-items: center;
                    height: 25px;
                    overflow: hidden;
                    .row-div:last-child {
                        padding: 0;
                    }
                    .row-left {
                        font-size: 22rpx;
                        opacity: 0.8;
                        color: #fff;
                        margin-right: 8rpx;
                        display: inline-block;
                        vertical-align: baseline;
                    }

                    .row-price {
                        color: #fff;
                        font-size: 24rpx;
                        margin-right: 8rpx;
                        display: inline-block;
                        vertical-align: baseline;
                    }

                    .row-text {
                        font-size: 36rpx;
                        color: #fff;
                        display: inline-block;
                        vertical-align: baseline;
                    }
                    .row-text-center {
                        padding-top: 9px;
                    }
                }
            }
        }

        .section-list {
            margin: 0 auto;
            z-index: 1000;
            border-radius: 0 0 5px 5px;

            .table-view-list {
                background: #fff;
                position: relative;
                overflow: hidden;

                &.table-view-list:before {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    border-bottom: 1px solid #ebedf0;
                    -webkit-transform-origin: 0 0;
                    transform-origin: 0 0;
                    -webkit-transform: scaleY(0.5);
                    transform: scaleY(0.5);
                    // z-index: 9;
                }

                .table-view-list-text {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    padding: 16px 0px;
                    margin-left: 15px;
                    margin-right: 15px;
                    text-align: left;

                    .left {
                        .left-icon {
                            width: 40rpx;
                            height: 40rpx;
                        }
                    }

                    .center {
                        padding: 0 8px;
                        flex: 1;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .center-txt {
                            font-size: 16px;
                            color: #333;
                            //padding-bottom: 3px;
                        }

                        .center-carNo {
                            font-size: 13px;
                            color: #999999;
                        }
                    }

                    .table-view-list-arrow {
                    }

                    .table-view-list-arrow:after {
                        content: ' ';
                        display: inline-block;
                        height: 5px;
                        width: 5px;
                        border-width: 1px 1px 0 0;
                        border-color: #888888;
                        border-style: solid;
                        transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
                    }
                }
            }

            .table-view-list:last-child:before {
                content: none;
            }
        }

        .add-btn {
            width: 702rpx;
            height: 98rpx;
            background: #f96702;
            border-radius: 8rpx;
            line-height: 98rpx;
            font-size: 36rpx;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 60rpx;

            .add-img {
                width: 34rpx;
                height: 34rpx;
                margin-right: 5px;
            }
        }

        .clear-btn {
            width: 702rpx;
            height: 98rpx;
            background: #fff;
            border-radius: 8rpx;
            line-height: 98rpx;
            font-size: 36rpx;
            color: #333;
            margin-top: 20rpx;
            text-align: center;
        }
    }
}
</style>
