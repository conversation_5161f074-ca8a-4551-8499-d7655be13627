<template>
    <div>
        <div class="card" v-for="(item, index) in refundOrderList" :key="index">
            <div class="oreder-content" @click="toDetail(item)">
                <div class="detail-top">
                    <div class="name fl-row">
                        <img mode="scaleToFill" src="../image/chargeCheer.png" alt class="name-img" />
                        <div class="title">{{ item.stationName }}</div>
                        <div class="name-arrow-right"></div>
                    </div>
                    <div :class="{ invoice: true, gray: true }">已退款</div>
                </div>
                <!-- <div class="detail-contetn"  :key="idx"> </div> -->
                <div class="relevantInfo">
                    <div class="infoItem">
                        <div class="itemLeft">充电电量</div>
                        <div class="itemRight">{{ item.chargeQty ? item.chargeQty + '度' : '-' }}</div>
                    </div>
                    <div class="infoItem">
                        <div class="itemLeft">充电时长</div>
                        <div class="itemRight">{{ item.chargeTime && Number(item.chargeTime > 0) ? item.chargeTime + '分钟' : '-' }}</div>
                    </div>
                    <div class="infoItem">
                        <div class="itemLeft">充电开始时间</div>
                        <div class="itemRight">{{ item.chargeStartTime ? item.chargeStartTime : '-' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { chargeOrderReturnList } from '@/s-kit/js/v3-http/https3/order/index';
export default {
    name: 'refundOrderList',
    props: {},
    data() {
        return {
            // 退款订单列表
            refundOrderList: [],
            // 页数
            pageNum: 1,
            // 页码
            pageSize: 10,
        };
    },
    methods: {
        /**
         * @description  : 根据退款订单refundStatus字段，判断展示文案
         * @param         {Number/String} flag:refundStatus的值
         * @return        {String} 展示文案
         */
        getRefundStatus(flag) {
            if (flag == 2) {
                return '待退款';
            } else if (flag == 9) {
                return '退款失败';
            } else if (flag == 13) {
                return '部分退款';
            } else if (flag == 14) {
                return '全部退款';
            } else {
                return '';
            }
        },
        /**
         * @description  : 获取退款订单列表
         * @param         {Boolean} isInit: 是否初始化订单数据
         * @param         {Boolean} onlyPay: 是否只查询待支付
         * @param         {Object} listParams: 查询订单接口入参
         * @return        {*}
         */
        async getRefundOrderList({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            if (isInit) {
                this.refundOrderList = [];
                this.pageNum = 1;
            }
            // 通过自定义事件改变列表组件状态，'loading'加载中
            this.$emit('loadStatusChange', 'loading');
            let params = {
                startTime: listParams.timeObj.startTime + ' 00:00:00',
                endTime: listParams.timeObj.endTime + ' 23:59:59',
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };
            let res = await chargeOrderReturnList(params, { isload: false });
            // 通过自定义事件停止下拉刷新高度和动画
            this.$emit('stopRefresh');
            if (res.success) {
                if (res.data.pageNum == 1) {
                    this.refundOrderList = res.data.rows || [];
                } else {
                    this.refundOrderList = this.refundOrderList.concat(res.data.rows || []);
                }
                if (this.pageSize > res.data.rows.length) {
                    // 通过自定义事件改变列表组件状态，'nomore'没有更多了
                    this.$emit('loadStatusChange', 'nomore');
                } else {
                    // 通过自定义事件改变列表组件状态，'contentdown'下拉加载更多
                    this.$emit('loadStatusChange', 'contentdown');
                }
                this.pageNum++;
            }
            if (this.refundOrderList.length == 0) {
                // 通过自定义事件改变列表组件空态标识
                this.$emit('showEmptyChange', true);
            } else {
                // 通过自定义事件改变列表组件空态标识
                this.$emit('showEmptyChange', false);
            }
        },
        /**
         * @description  : 跳转详情页面
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        toDetail(item) {
            let url = '/packages/third-order/pages/charge-refund-order-detail/main';
            let params = item;
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style scoped lang="scss">
.card {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 10px;
    padding: 16rpx 30rpx 27rpx;
    box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(0, 0, 0, 0.07);
    display: flex;

    &:nth-last-of-type(1) {
        margin-bottom: 0;
    }

    .choice {
        width: 35px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .oreder-content {
        width: 100%;
        height: 100%;

        .detail-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40rpx;

            .name {
                white-space: nowrap; //禁止换行
                text-overflow: ellipsis; //...
                display: flex;
                align-items: center;

                .name-img {
                    width: 16px;
                    height: 16px;
                }

                .title {
                    max-width: 182px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000000;
                    margin-left: 10rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                }
            }

            .invoice {
                font-size: 24rpx;
                font-weight: 400;
                color: #e64f22;
            }

            .gray {
                color: #999999 !important;
            }
        }

        .detail-contetn {
            margin-top: 16rpx;

            display: flex;
            justify-content: space-between;

            .detail-left {
                display: flex;

                .detail-left-img {
                    width: 100rpx;
                    height: 100rpx;
                }

                .order-name {
                    max-width: 380rpx;
                    padding-top: 2px;
                    margin-left: 20rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .detail-price {
                text-align: right;

                .unitPrice {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                    text-align: right;
                }

                .litre {
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 40rpx;
                    display: flex;
                    text-align: right;
                }

                .total_prices {
                    margin-top: 10rpx;
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #333333;
                }
            }
        }

        .paymentAmount {
            height: 25px;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            margin-top: 13rpx;

            div {
                line-height: 25px;
                font-size: 14px;
            }

            div:nth-child(1) {
                color: #666666;
                font-family: PingFangSC-Regular, PingFang SC;
            }

            div:nth-child(2) {
                color: #e64f22;
                font-family: PingFangSC-Medium, PingFang SC;
            }

            div:nth-child(3) {
                color: #e64f22;
                font-weight: bold;
                font-family: PingFangSC-Medium, PingFang SC;
            }
        }

        .relevantInfo {
            margin-top: 28rpx;

            .infoItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                div {
                    font-size: 26rpx;
                    color: #888888;
                    line-height: 42rpx;
                }
            }
        }
    }
}
</style>
