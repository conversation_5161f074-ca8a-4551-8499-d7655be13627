<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="p-bf fl-column borderB">
            <zj-navbar :border-bottom="false" title="电子发票"></zj-navbar>
            <div class="nav-contetn nav-contetn2 borderB">
                <div v-for="(item, index) in navList" :key="index" class="nav" @click="handelnavList(item.id)">
                    <div class="item-name" :class="{ 'item-left': item.name == '可开票订单' }">
                        <div class="item" :class="{ active: navActive === item.id }">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <div class="nav" @click="toTitle()"><div class="titleBtn">发票抬头</div></div>
            </div>
            <div class="nav-contetn borderB" v-if="orderType != 'charge'">
                <div v-for="(item, index) in secondNavList" :key="index" class="nav" @click="handelSecondNavList(item.id)">
                    <div class="item-name">
                        <div class="item" :class="{ active: secondNavActive === item.id }">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <div class="nav" @click="handelScreen">
                    <span class="search-size">
                        <img src="../../image/search.png" alt class="search-img" />
                    </span>
                    <span class="search-size">筛选</span>
                </div>
            </div>
            <div class="f-1 mh-0 bg-F7F7FB" :class="{ paddingB1: navActive == 1, paddingB2: navActive == 2 }">
                <invoiceOrderList
                    v-if="navActive == 1"
                    ref="invoiceOrderList"
                    :timeObj="timeObj"
                    :secondNavActive="secondNavActive"
                    @exData="exData"
                    :orderType="orderType"
                ></invoiceOrderList>
                <invoiceList
                    v-if="navActive == 2"
                    ref="invoiceList"
                    :secondNavActive="secondNavActive"
                    :sortType="listParams.screenActive"
                    :amountRange="{
                        amountRangeActive: listParams.amountRangeActive,
                        minAmount: listParams.minAmount,
                        maxAmount: listParams.maxAmount,
                    }"
                    :refer="refer"
                    @exData="exData"
                    @changeModalMode="changeModalMode"
                ></invoiceList>
            </div>
            <div :class="{ closeAnAccount: true }">
                <div class="bill-box" v-if="navActive == 1">
                    <div class="order-operation">
                        <div @click="checkAll()">
                            <radio color="#e64f22" :checked="allCheckBox" value="isAlll" style="transform: scale(0.65)" />
                        </div>
                        <div class="all">全选</div>
                        <div class="order-operation-price">
                            <span class="heightLight">{{ orderNoList.length }}</span>
                            <span>个订单，共计</span>
                            <span class="heightLight">{{ checkAllAmount }}</span>
                            <span>元</span>
                        </div>
                    </div>
                    <div class="error bg-288" :style="{ opacity: orderNoList.length > 0 ? '1' : '0.2' }" v-if="orderNoList.length == 0"
                        >下一步
                    </div>
                    <div class="error bg-288" @click="nextStep" :style="{ opacity: orderNoList.length > 0 ? '1' : '0.2' }" v-else
                        >下一步
                    </div>
                </div>
            </div>
            <ZjPopup ref="maskPopup" :maskClick="true" @maskClick="maskClose" type="bottom">
                <div class="isScreen">
                    <div class="closeImgWrap fl-row">
                        <span>筛选</span>
                        <img @click="closePopup" src="../../image/black-close.png" alt />
                    </div>
                    <div v-if="navActive == 1">
                        <div class="screen-dete title">消费时间</div>
                        <div class="dete-content">
                            <div
                                v-for="(item, index) in timeList"
                                :key="index"
                                class="dete-item gap23"
                                :class="{ isActive: isdeteActive === item.id }"
                                @click="handelIsdeteActive(index, item.id)"
                            >
                                {{ item.name }}
                            </div>
                        </div>
                        <div class="calendar-dete">
                            <div class="picker">
                                <picker mode="date" :value="timeObj.startTime" @change="bindStartDateChange" fields="day" @cancel="delece">
                                    <div class="picker-content">
                                        {{ timeObj.startTime }}
                                    </div>
                                </picker>
                            </div>
                            <div class="flagstaff">-</div>
                            <div class="picker">
                                <picker mode="date" :value="timeObj.endTime" @change="bindEndDateChange" fields="day" @cancel="delece">
                                    <div class="picker-content">
                                        {{ timeObj.endTime }}
                                    </div>
                                </picker>
                            </div>
                        </div>
                        <!-- <div class="tips">可查询近2年内的消费订单</div> -->
                    </div>
                    <div v-if="navActive == 2">
                        <div class="screen-dete title">排序</div>
                        <div class="dete-content">
                            <div
                                v-for="(item, index) in deteList"
                                :key="index"
                                class="dete-item2"
                                :class="{ isActive: screenActive === item.id }"
                                @click="handelScreenActive(index, item.id)"
                                always-system
                                controlled
                            >
                                {{ item.name }}
                            </div>
                        </div>
                        <div class="screen-dete title">价格区间</div>
                        <div class="dete-content">
                            <div
                                v-for="(item, index) in amountRangeList"
                                :key="index"
                                class="dete-item2"
                                :class="{ isActive: amountRangeActive === item.id }"
                                @click="handelAmountRangeActive(index, item.id)"
                                always-system
                                controlled
                            >
                                {{ item.name }}
                            </div>
                        </div>
                        <div class="calendar-dete" v-if="amountRangeActive == 4">
                            <div class="picker">
                                <input
                                    v-model="minAmount"
                                    type="digit"
                                    @input="handleMinNum"
                                    @confirm="amountConfirm"
                                    inputmode="decimal"
                                    placeholder="最低价格"
                                    @focus="popupInputFocus"
                                    @blur="popupInputBlur"
                                />
                            </div>
                            <div class="flagstaff">-</div>
                            <div class="picker">
                                <input
                                    v-model="maxAmount"
                                    type="digit"
                                    @input="handleMaxNum"
                                    @confirm="amountConfirm"
                                    inputmode="decimal"
                                    placeholder="最高价格"
                                    @focus="popupInputFocus"
                                    @blur="popupInputBlur"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="btn">
                        <div class="reset" @click="dateReset">重置</div>
                        <div class="confirm" @click="dateConfirm">确认</div>
                    </div>
                </div>
            </ZjPopup>
            <zj-show-modal>
                <div v-if="modalMode == 'invoice'">
                    <div>1.多个订单合并开票，可能开具多张发票。</div>
                    <div>2.多个订单开具一张发票后，换开发票不可开具多张发票。</div>
                </div>
                <invoiceChangeTips v-if="modalMode == 'invoiceChange'"></invoiceChangeTips>
            </zj-show-modal>
        </div>
    </div>
</template>

<script>
import invoiceList from '../../components/invoice-list/main.vue';
import invoiceOrderList from '../../components/invoice-order-list/main.vue';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import invoiceChangeTips from '../../components/invoice-change-tips/main.vue';
import { beforeMakeInvoiceCheckApi, chargeBeforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-invoice',
    components: { invoiceList, ZjPopup, invoiceChangeTips, invoiceOrderList },
    data() {
        return {
            navList: [
                { name: '可开票订单', id: 1 },
                { name: '开票历史', id: 2 },
            ],
            secondNavList: [
                { name: '全部', id: '' },
                { name: '油品', id: '3100' },
                { name: '非油品', id: '3150' },
            ],
            deteList: [
                { name: '时间由近到远', id: 1 },
                { name: '时间由远到近', id: 2 },
                { name: '金额由大到小', id: 4 },
                { name: '金额由小到大', id: 3 },
            ],
            timeList: [
                { name: '近1个月', id: 1 },
                { name: '近3个月', id: 3 },
                { name: '近6个月', id: 6 },
                { name: '近1年', id: 12 },
            ],
            amountRangeList: [
                { name: '0-200', id: 1 },
                { name: '200-500', id: 2 },
                { name: '500-1000', id: 3 },
                { name: '其他', id: 4 },
            ],
            isdeteActive: null,
            screenActive: 1,
            amountRangeActive: null,
            minAmount: '',
            maxAmount: '',
            navActive: 1,
            secondNavActive: '',
            timeObj: { startTime: '', endTime: '' },
            timeRangeStart: '',
            timeRangeEnd: '',
            checkAllAmount: 0,
            orderNoList: [],
            allCheckBox: false,
            checkOrderList: [],
            // 接口入参集合
            listParams: {
                timeObj: { startTime: '', endTime: '' },
                screenActive: 1,
                amountRangeActive: null,
                minAmount: '',
                maxAmount: '',
                isdeteActive: null,
            },
            modalMode: '',
            osName: '',
            refer: '',
            orderType: '',
        };
    },
    onLoad(options) {
        // 接收跳转到该页面的传参，展示指定内容
        if (options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            this.navActive = Number(params.navActiveProps) || 1;
            this.secondNavActive = params.secondNavActive || '';
            this.orderType = params.orderType || '';
            if (params.refer) {
                this.refer = params.refer;
            }
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'invoicePage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        // #ifdef MP-MPAAS
        this.osName = uni.getSystemInfoSync().osName;
        // #endif
    },
    created() {
        // 时间筛选初始化
        this.init();
    },
    watch: {
        amountRangeActive(nVal) {
            switch (nVal) {
                case 1:
                    this.minAmount = '0';
                    this.maxAmount = '200';
                    break;
                case 2:
                    this.minAmount = '200';
                    this.maxAmount = '500';
                    break;
                case 3:
                    this.minAmount = '500';
                    this.maxAmount = '1000';
                    break;
                case 4 || null:
                    this.minAmount = '';
                    this.maxAmount = '';
                    break;
            }
        },
    },
    onShow() {
        // 获取接口数据
        this.apiPost();
    },
    methods: {
        popupInputFocus(e) {},
        popupInputBlur(e) {
            // #ifdef MP-MPAAS
            if (this.osName == 'ios') {
                setTimeout(() => {
                    uni.hideKeyboard();
                }, 100);
            }
            // #endif
        },
        amountConfirm() {
            return new Promise((resolve, reject) => {
                if (this.minAmount && this.maxAmount && Number(this.minAmount) > Number(this.maxAmount)) {
                    let min = JSON.parse(JSON.stringify(this.minAmount));
                    let max = JSON.parse(JSON.stringify(this.maxAmount));
                    this.minAmount = max;
                    this.maxAmount = min;
                    resolve();
                } else {
                    resolve();
                }
            });
        },
        //充值input校验
        handleMinNum(e) {
            let value = e.detail.value;
            var price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            this.$nextTick(() => {
                this.minAmount = typeof price === 'string' ? price : price[0];
            });
        },
        //充值input校验
        handleMaxNum(e) {
            let value = e.detail.value;
            var price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            this.$nextTick(() => {
                this.maxAmount = typeof price === 'string' ? price : price[0];
            });
        },
        changeModalMode(type) {
            this.modalMode = type;
        },
        /**
         * @description  : 时间筛选初始化
         * @return        {*}
         */
        init() {
            this.timeRangeStart = this.getDate('start');
            this.timeRangeEnd = this.getDate('end');
            this.handelIsdeteActive(null, 24); // 默认时间两年
            this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
        },
        /**
         * @description  : 第一层tab点击事件，点击后重置筛选项
         * @param         {*} id:按钮绑定id的值
         * @return        {*}
         */
        handelnavList(id) {
            this.navActive = id;
            this.secondNavActive = '';
            this.apiPost();
            this.allCheckBox = false;
            this.checkAllAmount = 0;
            this.orderNoList = [];
        },
        /**
         * @description  : 调用接口获取数据，根据tab按钮的值调用不同组件的接口
         * @return        {*}
         */
        apiPost() {
            if (this.navActive == 1) {
                if (this.secondNavActive == '') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'kinvoiceListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                if (this.secondNavActive == '3100') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'kinvoice_oilListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                if (this.secondNavActive == '3150') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'kinvoice_nonOilListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.$refs.invoiceOrderList.refreshPullDown();
                    }, 600);
                });
            }
            if (this.navActive == 2) {
                if (this.secondNavActive == '') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'invoiceListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                if (this.secondNavActive == '3100') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'invoice_oilListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                if (this.secondNavActive == '3150') {
                    this.$sKit.mpBP.tracker('电子发票', {
                        seed: 'invoiceBiz',
                        pageID: 'invoice_nonOilListPage',
                        refer: this.refer,
                        channelID: clientCode,
                    });
                }
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.$refs.invoiceList.getInvoiceListApi({ isInit: true });
                    }, 600);
                });
            }
        },
        /**
         * @description  : 第二层tab点击事件，重置筛选项并获取接口数据
         * @param         {*} id:第二层tab按钮绑定id的值
         * @return        {*}
         */
        handelSecondNavList(id) {
            this.secondNavActive = id;
            this.allCheckBox = false;
            this.checkAllAmount = 0;
            this.orderNoList = [];
            this.apiPost();
        },
        /**
         * @description  : 修改开始时间事件
         * @param         {*} e:组件实例
         * @return        {*}
         */
        bindStartDateChange(e) {
            this.timeObj.startTime = e.target.value.replace(/\//g, '-');
        },
        /**
         * @description  : 修改开始结束事件
         * @param         {*} e:组件实例
         * @return        {*}
         */
        bindEndDateChange(e) {
            this.timeObj.endTime = e.target.value.replace(/\//g, '-');
        },
        /**
         * @description  : 开启筛选弹窗
         * @return        {*}
         */
        handelScreen() {
            if (this.navActive == 1) {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'kinvoice_screenBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            if (this.navActive == 2) {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'invoice_screenBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            this.$refs.maskPopup.open();
        },
        /**
         * @description  : 关闭筛选弹窗，并恢复到开启弹窗前的数据
         * @return        {*}
         */
        closePopup() {
            this.timeObj = JSON.parse(JSON.stringify(this.listParams.timeObj));
            this.screenActive = this.listParams.screenActive;
            this.isdeteActive = this.listParams.isdeteActive;
            this.amountRangeActive = this.listParams.amountRangeActive;
            this.$refs.maskPopup.close();
        },
        /**
         * @description  : 点击遮罩关闭弹窗触发事件
         * @return        {*}
         */
        maskClose() {
            this.timeObj = JSON.parse(JSON.stringify(this.listParams.timeObj));
            this.screenActive = this.listParams.screenActive;
            this.amountRangeActive = this.listParams.amountRangeActive;
            this.isdeteActive = this.listParams.isdeteActive;
        },
        /**
         * @description  : 重置筛选
         * @return        {*}
         */
        dateReset() {
            this.screenActive = 1;
            this.amountRangeActive = null;
            if (this.navActive == 1) {
                this.handelIsdeteActive(null, 24);
            } else if (this.navActive == 2) {
                this.handelIsdeteActive(null, 3);
            }
            this.dateConfirm('res');
        },
        /**
         * @description  : 确认筛选
         * @return        {*}
         */
        async dateConfirm(type) {
            if (this.amountRangeActive == 4 && this.minAmount == '' && this.maxAmount == '') {
                uni.showToast({
                    title: '请输入最低价格',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            this.listParams.timeObj = JSON.parse(JSON.stringify(this.timeObj));
            this.listParams.screenActive = this.screenActive;
            this.listParams.amountRangeActive = this.amountRangeActive;
            this.listParams.isdeteActive = this.isdeteActive;
            await this.amountConfirm();
            this.listParams.minAmount = this.minAmount;
            this.listParams.maxAmount = this.maxAmount;
            if (type != 'res') {
                this.$refs.maskPopup.close();
                this.apiPost();
            }
        },
        /**
         * @description  : 跳转开票
         * @return        {*}
         */
        async nextStep() {
            if (this.orderNoList.length > 20) {
                uni.showToast({
                    title: '合并开票订单数不可大于20，请重新选择',
                    icon: 'none',
                });
                return;
            }
            let apiParams = {
                orderList: [],
            };
            apiParams.orderList = this.checkOrderList.map(item => {
                let newItem = {
                    stationCode: item.stationCode,
                    businessDay: item.businessDay,
                    orderNo: item.orderNo,
                };
                return newItem;
            });
            let postFun = () => {};
            if (this.orderType == 'charge') {
                postFun = chargeBeforeMakeInvoiceCheckApi;
            } else {
                postFun = beforeMakeInvoiceCheckApi;
            }
            let res = await postFun(apiParams, {
                handleErrorFn: () => {
                    this.apiPost();
                },
            });
            if (res && res.success) {
                if (res.data.flag) {
                    let url = '/packages/third-invoice/pages/invoice-form/main';
                    let params = {};
                    if (this.orderType == 'charge') {
                        params = {
                            type: 'invoice',
                            orderType: 'charge',
                            orderNoList: this.orderNoList,
                            checkAllAmount: this.checkAllAmount,
                            refer: this.refer,
                        };
                    } else {
                        params = { type: 'invoice', orderNoList: this.orderNoList, checkAllAmount: this.checkAllAmount, refer: this.refer };
                    }

                    if (this.orderNoList.length == 1) {
                        let goods = '';
                        if (this.orderType == 'charge') {
                            goods = this.checkOrderList[0].chargeOrderItemList
                                .map(goodsItem => {
                                    return goodsItem.productName;
                                })
                                .join(',');
                        } else {
                            goods = this.checkOrderList[0].orderIterms
                                .map(goodsItem => {
                                    return goodsItem.productName;
                                })
                                .join(',');
                        }
                        params.createTime = this.checkOrderList[0].createTime;
                        params.orgName = this.checkOrderList[0].stationName;
                        params.goods = goods;
                        this.$sKit.layer.useRouter(url, params);
                    } else if (this.orderNoList.length > 1) {
                        this.modalMode = 'invoice';
                        this.$store.dispatch('zjShowModal', {
                            cancelText: '取消',
                            cancelColor: '',
                            success: res => {
                                if (res.confirm) {
                                    this.$sKit.layer.useRouter(url, params);
                                }
                            },
                        });
                    }
                }
            }
        },
        /**
         * @description  : 全选按钮点击事件，并将是否全选的绑定值传入组件方法中
         * @return        {*}
         */
        checkAll() {
            this.allCheckBox = !this.allCheckBox;
            this.$refs.invoiceOrderList.allChecked(this.allCheckBox);
        },
        /**
         * @description  : 接收组件暴露出来的数据
         * @param         {*} obj:组件暴露出来的数据
         * @return        {*}
         */
        exData(obj) {
            this.allCheckBox = obj.isAll;
            this.checkAllAmount = obj.checkAllAmount;
            this.orderNoList = obj.orderNoList;
            this.checkOrderList = obj.checkOrderList;
        },
        /**
         * @description  : 获取并处理当前时间
         * @param         {*} type:时间类型 start为获取时间范围开始时间，当前时间两年前；end为获取时间范围结束时间，当前时间
         * @return        {*}
         */
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            if (type === 'start') {
                day += 1;
                year = year - 2;
            } else if (type === 'end') {
            }
            var lastDay = new Date(year, month, 0).getDate();
            if (day > lastDay) {
                day = lastDay;
                month += 1;
            }
            if (month > 12) {
                year + 1;
            }
            month = month > 9 ? month : '0' + month;
            day = day > 9 ? day : '0' + day;
            return `${year}-${month}-${day}`;
        },
        /**
         * @description  : 处理时间筛选按钮和时间范围的交互，处理时间格式
         * @param         {*} index: 按钮排序值
         * @param         {*} number: 按钮对应id，代表几个月的范围
         * @return        {*}
         */
        handelIsdeteActive(index, number) {
            this.isdeteActive = number;
            var date = new Date();
            var year = date.getFullYear(); // 年
            var month = date.getMonth() + 1; // 月
            var day = date.getDate(); // 日
            this.timeObj.endTime = this.getDate();
            month = month - (number % 12);
            year = year - parseInt(number / 12);
            day += 1;
            var lastDay = new Date(year, month, 0).getDate();
            if (day > lastDay) {
                day = 1;
                month += 1;
            }
            if (month > 12) {
                month = 12 - month;
                year += 1;
            }
            if (month <= 0) {
                month = month + 12;
                year = year - 1;
            }
            var lastDay = new Date(year, month, 0).getDate();
            if (day > lastDay) {
                day = lastDay;
            }
            if (month >= 0 && month <= 9) {
                month = '0' + month;
            }
            if (day >= 0 && day <= 9) {
                day = '0' + day;
            }
            this.timeObj.startTime = year + '-' + month + '-' + day;
        },
        handelScreenActive(index, id) {
            this.screenActive = id;
        },
        handelAmountRangeActive(index, id) {
            this.amountRangeActive = id;
        },
        toTitle() {
            let url = '/packages/third-invoice/pages/invoice-title-list/main';
            let params = {
                refer: this.refer,
            };
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style scoped lang="scss">
.nav-contetn {
    padding: 0 57rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .titleBtn {
        width: 155rpx;
        height: 54rpx;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        border-radius: 8rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #ffffff;
        line-height: 54rpx;
        text-align: center;
    }

    .nav {
        display: flex;
        align-items: center;
        justify-content: center;

        .item-name {
            height: 80rpx;
            .item {
                height: 100%;
                font-size: 16px;
                font-weight: 400;
                line-height: 80rpx;
                color: #333333;
                text-align: center;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
            }

            .active {
                height: 100%;
                font-size: 16px;
                color: #e64f22;
                line-height: 80rpx;
                text-align: center;
                border-bottom: 2px solid #e64f22;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
            }
        }
        .item-left {
            margin-left: 29rpx;
        }
    }

    .search-size {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 27rpx;
    }

    .search-img {
        width: 27rpx;
        height: 27rpx;
        margin-right: 4px;
    }
}

.nav-contetn2 {
    padding: 0 30rpx;

    .nav {
        flex: 1;
        &:nth-of-type(1) {
            justify-content: flex-start;
        }

        &:nth-of-type(2) {
            justify-content: center;
        }

        &:nth-of-type(3) {
            justify-content: flex-end;
        }
    }
}

.paddingB1 {
    padding-bottom: 152rpx;
}

.paddingB2 {
    padding-bottom: 0;
}

.isScreen {
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding: 32rpx;

    .closeImgWrap {
        width: 100%;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        div {
            font-size: 26rpx;
            font-weight: 400;
            color: #999999;
            line-height: 46rpx;
        }

        img {
            width: 25rpx;
            height: 25rpx;
        }
    }

    .title {
        height: 46rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
    }

    .screen-dete {
        margin-top: 24rpx;
    }

    .dete-content {
        margin-top: 41rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .dete-item {
            flex: 1;
            height: 80rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 80rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
        }

        .dete-item2 {
            flex: 0 45%;
            height: 80rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 80rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;

            &:nth-of-type(1) {
                margin-bottom: 24rpx;
            }

            &:nth-of-type(2n-1) {
                margin-right: 45rpx;
            }
        }

        .isActive {
            border: 1px solid #e64f22;
            background: #ffffff;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #e64f22;
        }
    }

    .calendar-dete {
        display: flex;
        align-items: center;
        margin-top: 24rpx;
        justify-content: space-between;

        .picker {
            width: 320rpx;
            height: 80rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            padding: 0 24rpx 0 34rpx;
            box-sizing: border-box;

            input {
                background: #f7f7fb;
                height: 100%;
                width: 100%;
                border: 0;
                padding: 0;
            }

            .picker-content {
                height: 80rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }

    .tips {
        margin-top: 24rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #999999;
        line-height: 37rpx;
    }

    .btn {
        margin-top: 32rpx;
        display: flex;
        justify-content: space-between;

        div {
            width: 330rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            font-size: 32rpx;
        }

        .reset {
            background: #ffffff;
            border-radius: 16rpx;
            border: 1rpx solid #e64f22;
            color: #e64f22;
            text-align: center;
        }

        .confirm {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 16rpx;
            color: #ffffff;
        }
    }
}

.closeAnAccount {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: env(safe-area-inset-bottom);
    z-index: 100;

    .to-title {
        width: 750rpx;
        height: 56rpx;
        background: #feeee5;
        padding-left: 59rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 26rpx;
            font-weight: 400;
            color: #e64f22;
            line-height: 56rpx;
            margin-right: 12rpx;
        }

        .name-arrow-right {
            box-sizing: border-box;
            width: 15rpx;
            height: 15rpx;
            margin-top: -3rpx;
            border: solid #e64f22;
            border-width: 0 3rpx 3rpx 0;
            transform: rotate(-45deg);
        }
    }

    .bill-box {
        width: 100%;
        height: 152rpx;
        background: #ffffff;
        border-radius: 4rpx 4rpx 0rpx 0rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        .order-operation {
            display: flex;
            align-items: center;

            .all {
                margin-right: 15px;
            }

            .order-operation-price {
                span {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 33rpx;
                }

                .heightLight {
                    color: #e64f22;
                }
            }
        }

        .error {
            width: 120px;
            height: 44px;
            border-radius: 8px;
            text-align: center;
            line-height: 44px;
            font-size: 14px;
            color: #ffffff;
            opacity: 0.2;
        }
    }
}

.heightBottom {
    bottom: calc(10px + env(safe-area-inset-bottom));
}

.gap45 {
    margin-right: 45rpx;

    &:nth-last-of-type(1) {
        margin-right: 0;
    }
}

.gap23 {
    margin-right: 23rpx;

    &:nth-last-of-type(1) {
        margin-right: 0;
    }
}

.borderB {
    border-bottom: 1rpx solid #efeff4;
}
</style>
