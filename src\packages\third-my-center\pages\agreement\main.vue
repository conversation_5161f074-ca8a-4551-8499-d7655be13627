<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="remaining_div fl-column bg-F7F7FB p-LR-16">
            <zj-navbar title="协议" :border-bottom="false"></zj-navbar>
            <div class="set_div card-default mart12">
                <div v-for="(item, index) in filteredAgreementList" @click="agreementFun(item, index)" :key="index">
                    <div class="fl-row fl-jus-bet fl-al-cen line_bottom set_div_item padding_13">
                        <div class="set_div_item_left fl-row fl-al-cen">
                            <div class="font-15 item_title">{{ item.title }}</div>
                            <!-- <div class="font-12 color-999">{{ JSON.stringify(item) }}</div> -->
                        </div>
                        <div class="right_btn_div">
                            <div class="right_btn"></div>
                        </div>
                    </div>
                </div>
                <zj-show-modal></zj-show-modal>
            </div>
            <div class="footer p-LR-16">
                <div class="check-line fl-row fl-al-cen" @click="isCheck = !isCheck">
                    <img :src="isCheck ? select : unSelect" alt="" />
                    <div class="font-12 color-333 weight-400">我已年满18周岁，且已充分阅读并同意以上所有协议</div>
                </div>
                <div class="btn">
                    <div class="confirm" @click="agreeAndReturn('ok')">同意并返回</div>
                    <div class="reset" @click="exitConfirmation('res')">退出登录</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { userAgreement } from '../../../../s-kit/js/v3-http/https3/user';
// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-logout.js';
// #endif
// #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
import wxMixin from './diff-environment/wx-logout.js';
import zfbMixin from './diff-environment/zfb-logout.js';
// #endif
// #ifdef MP-TOUTIAO
import ttMixin from './diff-environment/tt-logout.js';
// #endif
// #ifdef H5-CLOUD
import cloudMixin from './diff-environment/cloud-logout.js';
// #endif
import { clientCode } from '../../../../../project.config';
export default {
    components: {},
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
        wxMixin,
        zfbMixin,
        // #endif
        // #ifdef MP-TOUTIAO
        ttMixin,
        // #endif
        // #ifdef H5-CLOUD
        cloudMixin,
        // #endif
    ],
    data() {
        return {
            agreementList: [
                {
                    title: '《能源e站用户协议》',
                    type: '5',
                    name: 'App用户使用协议',
                },
                {
                    title: '《能源e站隐私政策》',
                    // #ifdef MP-MPAAS
                    type: '2',
                    name: 'App隐私协议',
                    // #endif
                    // #ifdef MP-WEIXIN
                    name: '微信小程序隐私协议',
                    type: '1',
                    // #endif
                    // #ifdef MP-ALIPAY
                    name: '支付宝隐私协议',
                    type: '1',
                    // #endif
                },
                {
                    title: '《实人认证协议》',
                    name: '人脸识别认证协议',
                    type: '1',
                },
                {
                    title: '《中国石油昆仑e享卡个人客户服务协议》',
                    name: '昆仑e享卡开通协议',
                    type: '1',
                },
                {
                    title: '《昆仑加油卡充值协议》',
                    name: 'App充值协议',
                    type: '1',
                },
            ],
            unSelect: require('../../image/empty.png'),
            select: require('../../image/successSel.png'),
            isCheck: false,
            clickFlag: '',
        };
    },
    onLoad() {
        this.$sKit.mpBP.tracker('隐私协议', {
            seed: 'privacyBiz',
            pageID: 'privacyPage', // 页面名
            channelID: clientCode,
            dateType: 'exposure',
        });
    },
    created() {
        this.$store.dispatch('getSetWalletStatus');
    },
    async mounted() {},
    computed: {
        ...mapGetters(['walletStatus']),
        ...mapState({}),
        filteredAgreementList() {
            /**
             * 直观代码
             * for (const item of this.agreementList) {
                    if (item.title === '《中国石油昆仑e享卡个人客户服务协议》') {
                        // 只在walletStatus.status为true时添加昆仑e享卡协议
                        if (this.walletStatus.status === true) {
                            filteredList.push(item);
                        }
                    } else {
                        // 其他协议始终添加
                        filteredList.push(item);
                    }
                }
             */
            return this.agreementList.filter(item => {
                // 如果是《中国石油昆仑e享卡个人客户服务协议》，则需要walletStatus.status为true才显示
                if (item.title === '《中国石油昆仑e享卡个人客户服务协议》' || item.title === '《昆仑加油卡充值协议》') {
                    return this.walletStatus.status === true; // 仅当status为true时保留
                }
                // 其他协议始终显示
                return true;
            });
        },
    },
    methods: {
        agreementFun(item, index) {
            console.log(item.type, item.name, '协议类型');
            this.getAgreeOn(item.type, item.name);
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // 打开PDF
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
        exitConfirmation() {
            this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '登出后，下次使用中石油加油服务需要再次登录',
                confirmText: '退出登录',
                cancelText: '暂不退出',
                cancelColor: '#666666',
                confirmColor: '#FF6B2C',
                success: async res => {
                    if (res.confirm) {
                        this.clickFlag = 1;
                        console.log('用户点击确定');
                        // #ifdef MP-MPAAS
                        this.$cnpcBridge.setValueToNative('Exit_login_identity', true);
                        // #endif
                        this.logOut();
                        this.$sKit.mpBP.tracker('隐私协议', {
                            seed: 'privacyBiz',
                            pageID: 'privacyPage', // 页面名
                            channelID: clientCode,
                            positionID: 'exitBtn',
                            dateType: 'click',
                        });
                    } else if (res.cancel) {
                        this.clickFlag = '';
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 同意并返回
        async agreeAndReturn() {
            if (!this.isCheck) {
                uni.showToast({
                    title: '请先勾选协议',
                    icon: 'none',
                    duration: 2000,
                    mask: true,
                });
            } else {
                this.$sKit.mpBP.tracker('隐私协议', {
                    seed: 'privacyBiz',
                    pageID: 'privacyPage', // 页面名
                    channelID: clientCode,
                    positionID: 'agreeBtn',
                    dateType: 'click',
                });
                this.clickFlag = 1;
                // #ifdef MP-MPAAS
                let userInfo = await this.$cnpcBridge.getUserTokenInfo();
                this.$cnpcBridge.setValueToNative(`Define_Agreement_Dialog_${userInfo.memberNo}`, true);
                // #endif
                // #ifndef MP-MPAAS
                const { memberNo } = uni.getStorageSync('tokenInfo');
                uni.setStorageSync(`Define_Agreement_Dialog_${memberNo}`, true);
                // #endif
                this.$sKit.layer.closeEvent();
            }
        },
    },
    destroyed() {
        if (!this.clickFlag) {
            this.logOut();
        }
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;
}

.set_div {
    .set_div_item {
        margin-left: 12px;
        margin-right: 12px;

        //  这个样式为bug表中要求的"提示信息左对齐"的效果
        .set_div_item_left {
            // width: 93%;
            flex: 1;

            .item_title {
                color: #000000;
            }
        }

        .right_btn_div {
            position: relative;
            .right_btn {
                width: 8px;
                height: 8px;
                box-sizing: border-box;
                border: solid #666666;
                border-width: 0 1px 1px 0;
                transform: rotate(-45deg);
            }
            .right_btn_div_switch {
                height: 25px;
                width: 44px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
.footer {
    left: 0;
    // margin-top: 22px;
    position: fixed;
    width: 100%;
    bottom: calc(env(safe-area-inset-bottom) + 10px);
    img {
        width: 15px;
        height: 15px;
        margin-right: 5px;
    }

    .check-line {
        line-height: 76rpx;
    }

    .btn {
        display: flex;
        justify-content: space-between;
        width: 100%;

        div {
            width: 48%;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            font-size: 32rpx;
        }

        .reset {
            background: #ffffff;
            border-radius: 16rpx;
            border: 1rpx solid #e64f22;
            color: #e64f22;
            text-align: center;
        }

        .confirm {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 16rpx;
            color: #ffffff;
        }
    }
}
.marlr16 {
    margin: 0 16px;
}
.mart12 {
    margin-top: 12px;
}

.padding_13 {
    padding-top: 13px;
    padding-bottom: 13px;
}
</style>
