<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background: #f7f7fb">
        <zj-navbar :height="44" title="昆仑e享卡"></zj-navbar>
        <div class="content">
            <div class="header">
                <img class="head-img" src="../../images/header-img.png" alt />
                <div class="header-content">
                    <div class="header-content-title"><div class="text1">昆仑e享卡</div><div class="text2">（电子卡）</div></div>
                    <div class="header-content-body">
                        <div class="body-item">
                            <div class="item-title">卡余额(元)</div>
                            <div class="font-style item-body">{{ walletInfo.walletBalance || 0 }} </div>
                        </div>
                        <div class="body-item">
                            <div class="item-title">冻结余额(元)</div>
                            <div class="font-style item-body">{{ walletInfo.freezeBalance || 0 }}</div>
                        </div>
                        <div class="body-item">
                            <div class="item-title">积分</div>
                            <div class="font-style item-body">{{ memberAccountInfo.loyaltyPtsAvailableAmount || 0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn btn1" @click="addCCBManger()">添加建行直营经理</div>
            <div class="btn btn2" v-if="showJoinBtn" @click="checkJoinGroup(false)">加入联合会员享优惠</div>
            <div class="tips" v-if="!showJoinBtn && associateLevelSign">已加入联合会员，支付享优惠</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { accountStatus, associateJoinGroup ,associateCheckJoinGroup} from '../../../../s-kit/js/v3-http/https3/wallet';
import requestRPC from '../../../../s-kit/js/v3-http/requestCCB.js';
import checkFKArgs from '../../../../s-kit/js/v3-native-jsapi/checkFKArgs.js';
import { ccbShareUrl } from '../../../../../project.config';
import { getEncryptInfo } from '../../../../s-kit/js/v3-http/https3/user.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {},
    data() {
        return {
            showJoinBtn: true,
            locationInfo : null,
            associateLevelSign:false
        };
    },
    computed: {
        ...mapGetters(['memberAccountInfo', 'walletInfo']),
    },
    async onLoad(options) {
        this.$cnpcBridge.getValueToNative('Define_Selected_Area', async result => {
            if (result) {
                this.locationInfo = JSON.parse(result || '{}');
            } else {
                this.locationInfo = await checkFKArgs.getLocationInfo();
            }
        });
        let res = await accountStatus();
        if (res.success) {
            this.$store.commit('setWalletStatus', res.data);
            let walletInfo = res.data;
            if (walletInfo.status) {
                this.$sKit.commonUtil.refreshWallet();
                this.checkJoinGroup(true);
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: '提示',
                    content: '暂未开通昆仑e享卡',
                    confirmText: '确认',
                    success: res => {
                        if (res.confirm) {
                            this.$cnpcBridge.closeMriver();
                        }
                    },
                });
            }
        }
    },
    methods: {
        async addCCBManger() {
            let appId = '0003';
            let originalId = 'gh_49eb307e3e53';
            let userInfo = await this.$cnpcBridge.getUserTokenInfo();
            console.log('建行分享userInfo', JSON.stringify(userInfo));
            // let phone = await this.encryptSM4({data: userInfo.phone});
            let res = await getEncryptInfo();
            console.log('ccbShareUrl===', ccbShareUrl);
            if (res.success && res.data && res.data.phone) {
                let phone = res.data.phone;
                let path = `pages/link/index?openurl=${ccbShareUrl}&hasUnionid=true&__params__regType=02&__params__regId=${phone}&__params__srcType=${appId}&__params__chnlId=31130000000000000000001`;
                console.log('建行分享path', path);
                this.$cnpcBridge.openModule({
                    type: 'weChatMiniprogram',
                    id: originalId,
                    path: path,
                });
            } else {
                uni.showToast({
                    title: '获取加密手机号失败',
                    icon: 'none',
                });
            }
        },
        async checkJoinGroup(isCustomErr = false){
            let res = await associateCheckJoinGroup(
                { areaCode:  this.locationInfo ? this.locationInfo.cityCode.toString().substring(0, 2) + '0000' : '' , associateType : 1},
                { isCustomErr: isCustomErr },
            );
            if(res.success){
                if(res.data && res.data.joinFlag){
                    this.showJoinBtn = false
                    if(res.data.associateLevel){
                      this.associateLevelSign = true
                    }
                }else{
                    this.addCCBMember(true)
                }
            }else{
                this.showJoinBtn = true
            }
        },
        async addCCBMember(isCustomErr = false) {
            let res = await associateJoinGroup(
                { regionCode: this.locationInfo ? this.locationInfo.cityCode.toString().substring(0, 2) + '0000' : '' },
                { isCustomErr: isCustomErr },
            );
            this.showJoinBtn = !(res && res.success);
            this.associateLevelSign = (res && res.success)
        },
        //国密加密
        async encryptSM4(params) {
            return new Promise((resolve, reject) => {
                let url = '/htmlapi/ccb/encrypt';
                requestRPC.requestRPC(url, params).then(
                    res => {
                        if (res.code == 0) {
                            resolve(res.data);
                        } else {
                            reject(res.msg ? res.msg : '加密失败');
                        }
                    },
                    error => {
                        reject('加密异常');
                    },
                );
            });
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
        position: relative;
        margin-top: 218rpx;
        width: 750rpx;
        height: 440rpx;
        background-image: linear-gradient(180deg, rgba(242, 243, 245, 0) 0%, #ffffff 100%);
        padding: 0 50rpx;
        .head-img {
            position: absolute;
            width: 686rpx;
            height: 361rpx;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .header-content {
            margin-top: 218rpx;
            .header-content-title {
                display: flex;
                align-items: baseline;
                .text1 {
                    font-weight: bold;
                    font-size: 36rpx;
                    color: #333333;
                    line-height: 50rpx;
                }
                .text2 {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    line-height: 40rpx;
                }
            }
            .header-content-body {
                display: flex;
                margin-top: 25rpx;
                .body-item {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    .item-title {
                        font-size: 30rpx;
                        color: #999999;
                        line-height: 42rpx;
                        text-align: left;
                    }
                    .item-body {
                        font-size: 40rpx;
                        color: #333333;
                        line-height: 56rpx;
                        text-align: left;
                    }
                }
            }
        }
    }
    .btn {
        width: 686rpx;
        height: 88rpx;
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        text-align: center;
        font-weight: bold;
        font-size: 36rpx;
        line-height: 88rpx;
        border-radius: 16rpx;
    }
    .btn1 {
        margin-top: 32rpx;
        color: #ffffff;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }
    .btn2 {
        margin-top: 30rpx;
        color: #e64f22;
        background: #ffffff;
        border: 2rpx solid #e64f22;
    }

    .tips {
        margin-top: 32rpx;
        font-size: 24rpx;
        color: #999999;
        line-height: 33rpx;
    }
}
</style>
