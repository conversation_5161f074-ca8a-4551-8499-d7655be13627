<template>
    <div class="oilCardSkinChange">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="油卡换肤"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="skin-content-wrap">
            <div v-for="(item, index) in skinList" :key="index">
                <img :src="item.image" alt />
                <div class="skin-content-item-wrap">
                    <div class="skin-content-title">
                        <span class="oneLevelTitle">{{ item.name1 }}</span>
                        <span class="twoLevelTitle">{{ item.name2 }}</span>
                    </div>
                    <div class="btn" @click="useKkin">使用皮肤</div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import pageConfig from '@/utils/pageConfig';
export default {
    data() {
        return {
            pageConfig,
            skinList: [
                // {
                //   image: require('./images/pingan.png'),
                //   name1: '2022新春专属皮肤',
                //   name2: '新年新气象,新皮肤新感受'
                // },
                // {
                //   image: require('./images/pingan.png'),
                //   name1: '2022新春专属皮肤',
                //   name2: '新年新气象,新皮肤新感受'
                // },
                // {
                //   image: require('./images/pingan.png'),
                //   name1: '2022新春专属皮肤',
                //   name2: '新年新气象,新皮肤新感受'
                // },
            ],
        };
    },
    created() {
        getSkinList(); // 获取皮肤列表
    },
    mounted() {},
    methods: {
        getSkinList() {},
        useKkin() {
            uni.showModal({
                title: '提示',
                content: '油卡皮肤设置成功',
                confirmColor: '#FF8200',
                showCancel: false,
                success: () => {
                    uni.navigateBack({
                        //返回
                        delta: 1,
                    });
                    return;
                    let params = {
                        token: this.$store.state.token,
                        carId: this.cardId,
                    };

                    deleteLicensePlate(params).then(res => {
                        if (res.status === 0) {
                            uni.navigateBack({
                                //返回
                                delta: 1,
                            });
                        }
                    });
                },
            });
        },
    },
    components: {},
};
</script>
<style scoped lang="scss">
.oilCardSkinChange {
    width: 100%;
    height: 100%;
    background: #f2f2f2;
    .skin-content-wrap {
        margin: 10px 15px 0 15px;
        img {
            width: 100%;
            height: 170px;
            margin-top: 10px;
        }
        .skin-content-item-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #fff;
            .skin-content-title {
                display: flex;
                flex-direction: column;
                .oneLevelTitle {
                    color: #171717;
                    font-size: 16px;
                }
                .twoLevelTitle {
                    color: #ababab;
                    font-size: 12px;
                }
            }
            .btn {
                width: 80px;
                height: 25px;
                text-align: center;
                line-height: 25px;
                border-radius: 20px;
                background: #aaaaaa;
                color: #f6f6f6;
            }
        }
    }
}
</style>
