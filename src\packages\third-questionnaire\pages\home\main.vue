<template>
    <div class="pageMpaas" style="position: relative; background: #f7f7fb; padding-bottom: 20px">
        <div class="question-form">
            <img v-if="!isSubmit" class="bg-image" src="../../images/bg-question.png" alt />
            <div class="page-wrap" v-if="!isSubmit">
                <zj-navbar
                    :border-bottom="false"
                    :background="{
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                    }"
                    back-icon-color="#ffffff"
                    title-color="#ffffff"
                    :title="questionListData.surveyName || defaultName"
                ></zj-navbar>
                <div class="wrap-question">
                    <div class="wrap-desc">{{ questionListData.describe || '1' }}</div>
                    <div class="wrap-content">
                        <div class="wrap-list" v-for="(item, commentIndex) in questionListDataRows" :key="commentIndex">
                            <div class="desc">{{ '问题' + item.sortNum + questionType(item.type) + '：' + item.questionTitle }}</div>
                            <div v-if="item.type == '1'">
                                <div class="content fl-row" v-for="(item2, index2) in item.surveyList" :key="index2">
                                    <img
                                        @click="changeRadio(item2, item.surveyList)"
                                        class="check-radio"
                                        :src="
                                            item2.checked
                                                ? require('../../images/circle-select.png')
                                                : require('../../images/circle-unselect.png')
                                        "
                                        alt=""
                                    />
                                    <!-- {{ item2.checked }} -->
                                    <div class="check-info">
                                        <div class="describe">{{ item2.labelSortNum + '.' + item2.describe }}</div>
                                        <img v-if="item2.imgUrl" :src="item2.imgUrl" alt="" />
                                        <div class="check-input" v-if="item2.commentFlag == 1 && item2.checked">
                                            <input
                                                maxlength="50"
                                                type="text"
                                                placeholder="请输入"
                                                v-model="item2.comment"
                                                @input="checkInput($event, commentIndex, index2)"
                                            />
                                            <div class="count">{{ item2.adviceCount > 50 ? '50' : item2.adviceCount }}/50</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="item.type == '3'">
                                <div class="content fl-row" v-for="(item2, index2) in item.surveyList" :key="index2">
                                    <img
                                        @click="changeCheck(item2, item.surveyList)"
                                        class="check-radio"
                                        :src="
                                            item2.checked
                                                ? require('../../images/square-select.png')
                                                : require('../../images/square-unselect.png')
                                        "
                                        alt=""
                                    />
                                    <div class="check-info">
                                        <div class="describe">{{ item2.labelSortNum + '.' + item2.describe }}</div>
                                        <img v-if="item2.imgUrl" :src="item2.imgUrl" alt="" />
                                        <!-- <input maxlength="50" v-if="item2.commentFlag == 1 && item2.checked" type="text" placeholder="请输入" v-model="item2.comment"> -->
                                        <div class="check-input" v-if="item2.commentFlag == 1 && item2.checked">
                                            <input
                                                maxlength="50"
                                                type="text"
                                                placeholder="请输入"
                                                v-model="item2.comment"
                                                @input="checkInput($event, commentIndex, index2)"
                                            />
                                            <div class="count">{{ item2.adviceCount > 50 ? '50' : item2.adviceCount }}/50</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <!-- <div class="font-13  color-333 fl-row fl-al-cen">您的意见或建议<div class="color-999 font-10">（非必填）</div></div> -->
                                <div class="input-div font-13" v-if="item.commentFlag == '1'">
                                    <textarea
                                        type="text"
                                        placeholder="请输入"
                                        placeholder-class="color-666 font-13"
                                        v-model="item.comment"
                                        maxlength="100"
                                        @input="adviceInput($event, commentIndex)"
                                        fixed
                                        :show-count="false"
                                    />
                                    <div class="fl-row fl-sp-end color-999 font-10"
                                        >{{ item.adviceCount > 100 ? '100' : item.adviceCount }}/100</div
                                    >
                                </div>
                                <div class="padt10" v-if="item.imgFlag == '1'">
                                    <div class="font-13 color-333 fl-row fl-al-cen">上传图片 </div>
                                    <div class="fl-row fl-al-cen mart8 fl-wrap con-list">
                                        <div class="photo-area" v-for="(img, imgIndex) in item.picList" :key="imgIndex">
                                            <img :src="img" alt="" class="area-img" @click="previewImage(imgIndex, commentIndex)" />
                                            <img
                                                src="../../images/del.png"
                                                alt=""
                                                @click.stop="delImg(imgIndex, commentIndex)"
                                                class="img-14"
                                            />
                                        </div>

                                        <div
                                            class="photo-div fl-column fl-al-jus-cen"
                                            @click="handlerUpload('add', commentIndex)"
                                            v-if="item.picList.length != 3"
                                        >
                                            <img src="../../images/photo.png" alt="" class="img-28" />
                                            <!-- <div>{{ item.picList.length < 1 ? '拍照上传' : '再加一张' }}</div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="padt10">
                                <div class="font-13 color-333 fl-row fl-al-cen">上传图片
                                </div>
                                <div class="fl-row fl-al-cen mart8 fl-wrap con-list">
                                    <div class="photo-area" v-for="(img, imgIndex) in item.picList" :key="imgIndex">
                                        <img :src="img" alt="" class="area-img" @click="previewImage(imgIndex, commentIndex)" />
                                        <img src="../../images/del.png" alt="" @click.stop="delImg(imgIndex, commentIndex)" class="img-14" />
                                    </div>

                                    <div
                                        class="photo-div fl-column fl-al-jus-cen"
                                        @click="handlerUpload('add', commentIndex)"
                                        v-if="item.picList.length != 3"
                                    >
                                        <img src="../../images/photo.png" alt="" class="img-28" />
                                    </div>
                                </div>
                            </div> -->
                        </div>
                    </div>
                    <div class="primary-btn refund-btn marlr15 mart16" @click="submitFn"> 提交 </div>
                    <div style="padding: 20rpx"></div>
                </div>
            </div>
        </div>
        <!-- <zj-navbar title="调查问卷" :border-bottom="false"></zj-navbar>
        <div class="card-default marlr15 mart12 pad-25-16">
            <div class="pab18" v-for="(item, commentIndex) in commentConfigListDataRows" :key="commentIndex">
                <div>
                    <div class="font-13  color-333 fl-row fl-al-cen">您的意见或建议<div class="color-999 font-10">（非必填）</div></div>
                    <div class="input-div font-13">
                        <textarea
                            type="text"
                            placeholder="请输入"
                            placeholder-class="color-666 font-13"
                            v-model="item.advice"
                            maxlength="100"
                            @input="adviceInput($event, commentIndex)"
                            fixed
                            :show-count="false"
                        />
                        <div class="fl-row fl-sp-end color-999 font-10">{{ item.adviceCount > 100 ? '100' : item.adviceCount }}/100</div>
                    </div>
                </div>
                <div class="padt10">
                    <div class="font-13 color-333 fl-row fl-al-cen"
                        >拍下您认为需要整改的地方<div class="color-999 font-10">（非必填）</div>
                    </div>
                    <div class="fl-row fl-al-cen mart8 fl-wrap con-list" v-if="item">
                        <div class="photo-area" v-for="(img, imgIndex) in item.picList" :key="index">
                            <img :src="img" alt="" class="area-img" @click="previewImage(imgIndex, commentIndex)" />
                            <img src="../../images/del.png" alt="" @click.stop="delImg(imgIndex, commentIndex)" class="img-14" />
                        </div>

                        <div
                            class="photo-div fl-column fl-al-jus-cen"
                            @click="handlerUpload('add', commentIndex)"
                            v-if="item.picList.length != 3"
                        >
                            <img src="../../images/photo.png" alt="" class="img-28" />
                            <div>{{ item.picList.length < 1 ? '拍照上传' : '再加一张' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="primary-btn refund-btn marlr15 mart16"> 提交 </div> -->
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import {
    getQuestionConfigList,
    surveyQuestion,
    getNegativeQuestionConfigList,
    surveyNegativeQuestion,
} from '../../../../s-kit/js/v3-http/https3/order/index.js';
import thirdConfig from '../../../../s-kit/js/third-config';
// #ifdef MP-MPAAS
import appMixin from '../../diff-environment/app-img-update.js';
// #endif
// #ifndef MP-MPAAS
import wxMixin from '../../diff-environment/wx-img-update.js';
import zfbMixin from '../../diff-environment/zfb-img-update.js';
import { splicBaseUrl } from '../../../../../project.config';
import { baseType } from '../../../../../project.config.js';

// #endif
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS
        wxMixin,
        zfbMixin,
        // #endif
    ],
    data() {
        return {
            evaluateType: 'order',
            // //评价配置评价选项列表数据
            // commentConfigListDataRows: [],
            // commentConfigListData: {},
            // commentTitle: '',
            // isPage: false,
            // testIndex: -1,
            // orderInfo: {},
            // isOil: 0,
            // imgPop: false,

            // 调查问卷数据
            defaultName: '',
            questionnaireId: '', // 调查问卷id
            questionnaireType: '', // 调查问卷类型 差评：evaluate 满意度：satisfaction
            questionnaireData: {},
            questionListData: {},
            questionListDataRows: [],
            isSubmit: false,
        };
    },
    onLoad(option) {
        console.log('option---', option);
        this.isSubmit = true;
        if (JSON.stringify(option) !== '{}') {
            let optionsData = JSON.parse(decodeURIComponent(option.data));
            console.log('optionsData---', optionsData);
            this.questionnaireData = optionsData;
            this.questionnaireId = optionsData.id;
            this.questionnaireType = optionsData.type;
        }
        // else{
        //     // this.questionnaireId = '69'
        //     // this.questionnaireType = 'evaluate'
        //     this.questionnaireId = '19'
        //     this.questionnaireType = 'satisfaction'
        //     // this.questionnaireData.orgCode = '1-A5001-C001-S001'
        //     // {id:'16',type:'satisfaction'}
        //     // {id:'22',type:'evaluate'}
        // }
        if (this.questionnaireType == 'satisfaction') {
            this.defaultName = '满意度调查问卷';
        } else {
            this.defaultName = '评价后回访调查';
        }
        // 获取调查问卷信息
        this.getQusetionList();
        this.handlerUpload = this.$sKit.commonUtil.throttleUtil(this.handlerUpload);
    },
    onShow() {},
    mounted() {},
    methods: {
        // 问题类型
        questionType(val) {
            let name = '';
            if (val == '1') {
                name = '（单选）';
            } else if (val == '3') {
                name = '（多选）';
            } else {
                name = '';
            }
            return name;
        },
        // 调查问卷信息
        async getQusetionList() {
            let params = {
                id: this.questionnaireId,
            };
            let res = '';
            if (this.questionnaireType == 'satisfaction') {
                res = await getQuestionConfigList(params, {
                    handleErrorFn: () => {
                        this.closeEvent();
                    },
                });
            } else if (this.questionnaireType == 'evaluate') {
                res = await getNegativeQuestionConfigList(params, {
                    handleErrorFn: () => {
                        this.closeEvent();
                    },
                });
            }
            console.log(res, 'getQuestionConfigList');
            if (res.success) {
                this.isSubmit = false;
                this.questionListData = res.data;
                this.questionListDataRows = res.data.questionList?.map((val, index) => {
                    val.imagesUrl = [];
                    val.images = [];
                    val.picList = [];
                    val.comment = '';
                    val.adviceCount = 0;
                    return val;
                });
                this.questionListDataRows?.forEach(item => {
                    if (item.type == '1' || item.type == '3') {
                        item.surveyList?.forEach(item1 => {
                            item1.checked = false;
                            item1.adviceCount = 0;
                        });
                    }
                });
                console.log(this.questionListDataRows, 'this.questionListDataRows');
            } else {
                this.isSubmit = true;
            }
        },
        // 单选
        changeRadio(item, list) {
            console.log(item, list);
            // 单选效果
            list.forEach(element => {
                element.checked = false;
                element.comment = '';
                element.adviceCount = 0;
            });
            item.checked = true;
            this.$forceUpdate();
        },
        // 多选
        changeCheck(item, list) {
            console.log(item, list);
            item.checked = !item.checked;
            list.forEach(e => {
                if (!e.checked && e.commentFlag == '1') {
                    e.comment = '';
                    e.adviceCount = 0;
                }
            });

            this.$forceUpdate();
        },
        // 提交调查问卷
        async submitFn() {
            let answerArr = [];
            let isAnswer = false;
            let nullText = '';
            // 将选择和问答的答案放到数组answerArr
            this.questionListDataRows?.forEach(item => {
                // item.relateArr = []
                console.log(item.images, item.picList, item.imagesUrl, 'pic');
                if (item.type == '1' || item.type == '3') {
                    // 单选、多选
                    item?.surveyList.forEach(e => {
                        if (e.checked) {
                            // item.relateArr.push(e.labelId)
                            answerArr.push({
                                sortNum: item.sortNum,
                                type: item.type,
                                relateId: e.labelId,
                                questionId: item.questionId,
                                labelId: e.labelId,
                                commentFlag: e.commentFlag,
                                comment: e.comment || '',
                                imageUrl: '',
                            });
                        }
                    });
                } else {
                    // 问答
                    // item.relateArr.push(item.questionId)
                    answerArr.push({
                        sortNum: item.sortNum,
                        type: item.type,
                        relateId: item.questionId,
                        questionId: item.questionId,
                        labelId: '',
                        comment: item.comment || '',
                        imageUrl: item.images.join(','),
                        commentFlag: item.commentFlag,
                        imgFlag: item.imgFlag,
                    });
                }
            });
            console.log(answerArr, 'answerArr');
            /**
             * 选择必填，问答非必填
             * 1、选择、问答都有 选择必填，问答非必填
             * 2、只有问答，问答必填
             */
            let isType = ''; // 问卷问题类型
            let result1 = this.questionListDataRows
                .map(item => {
                    return item.type;
                })
                .filter((item, index, arr) => arr.indexOf(item) === index); // 问卷所含的问题类型
            console.log(result1, result1.length, 'result1');
            if (result1.length == 1 && result1[0] == 2) {
                console.log('wenda');
                isType = 'wenda';
            } else {
                isType = 'qita';
                console.log('qita');
            }
            if (isType == 'wenda') {
                let wendaResult = answerArr.some(e => e.comment || e.imageUrl);
                console.log(wendaResult, 'wendaResult');
                if (!wendaResult) {
                    isAnswer = true;
                    nullText = '请填写完成！';
                } else {
                    for (let i = 0; i < answerArr.length; i++) {
                        if (
                            (!answerArr[i].comment && answerArr[i].commentFlag == '1') ||
                            (!answerArr[i].imageUrl && answerArr[i].imgFlag == '1')
                        ) {
                            nullText = `请填写问题${answerArr[i].sortNum}`;
                            isAnswer = true;
                            break;
                        }
                    }
                }
            } else {
                let result = answerArr.find(e => {
                    return e.type == '1' || e.type == '3';
                });
                console.log(result, 'result');
                if (!result) {
                    isAnswer = true;
                    nullText = '请填写完成！';
                } else {
                    for (let i = 0; i < this.questionListDataRows.length; i++) {
                        // 判断是否有未填写的问题
                        if (this.questionListDataRows[i].type == '1' || this.questionListDataRows[i].type == '3') {
                            let groupArr = []; // 当前题目选中的选项数据
                            this.questionListDataRows[i]?.surveyList.forEach(e => {
                                if (e.checked) {
                                    groupArr.push(e);
                                }
                            });
                            console.log(groupArr, 'groupArr');
                            let groupCheck = groupArr?.some(e => e.commentFlag == '1' && !e.comment); // 未填写完成题目
                            console.log(groupCheck, 'groupCheck');
                            if (groupArr.length == 0 || groupCheck) {
                                nullText = `请填写问题${this.questionListDataRows[i].sortNum}`;
                                isAnswer = true;
                                break;
                            }
                        }
                    }
                }
            }

            console.log(isAnswer, answerArr, nullText, 'answerArr');
            console.log(this.questionListDataRows, 'questionListDataRows');

            // return
            if (isAnswer) {
                this.$store.dispatch('zjShowModal', {
                    content: nullText,
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }
            let surveyArr = [];
            surveyArr = answerArr.map(item => {
                if (this.questionnaireType == 'satisfaction') {
                    // 满意度
                    return {
                        type: item.type,
                        relateId: item.relateId,
                        comment: item.comment || '',
                        imageUrl: item.imageUrl,
                    };
                } else {
                    // 差评
                    return {
                        questionId: item.questionId,
                        labelId: item.labelId || '',
                        comment: item.comment || '',
                        imageUrl: item.imageUrl,
                    };
                }
            });
            let params = {
                id: this.questionnaireId, // 问卷id
                surveyList: surveyArr, // 问卷填写数据
            };
            console.log(params, 'params');
            // return
            // 差评：evaluate 满意度：satisfaction
            let resData = '';
            if (this.questionnaireType == 'satisfaction') {
                params.stationCode = this.questionnaireData.orgCode;
                console.log(params, 'params');
                // return
                resData = await surveyQuestion(params, {
                    handleErrorFn: () => {
                        this.closeEvent();
                    },
                });
            } else if (this.questionnaireType == 'evaluate') {
                resData = await surveyNegativeQuestion(params, {
                    handleErrorFn: () => {
                        this.closeEvent();
                    },
                });
            }
            console.log(resData, 'resData11');
            if (resData.success) {
                this.$store.dispatch('zjShowModal', {
                    content: '已提交，感谢您的回答！',
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            // uni.navigateBack();
                            this.closeEvent();
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                this.isSubmit = true;
            }
        },
        // 删除图片
        delImg(imgIndex, commentIndex) {
            this.questionListDataRows[commentIndex].picList.splice(imgIndex, 1);
            this.questionListDataRows[commentIndex].images.splice(imgIndex, 1);
            this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
        },
        // 问答题字数
        adviceInput(e, commentIndex) {
            this.questionListDataRows[commentIndex].comment = e.detail.value.substring(0, 100);
            this.questionListDataRows[commentIndex].adviceCount = e.detail.value.length;
            this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
        },
        // 选择题填空字数
        checkInput(e, commentIndex, index) {
            console.log(e, commentIndex, index, 'e');
            this.questionListDataRows[commentIndex].surveyList[index].comment = e.detail.value.substring(0, 50);
            this.questionListDataRows[commentIndex].surveyList[index].adviceCount = e.detail.value.length;
            this.$set(this.questionListDataRows[commentIndex].surveyList, index, this.questionListDataRows[commentIndex].surveyList[index]);
        },
        // 拍摄图片获取size
        obtainTheSizeOfThePhotographedImage(path) {
            console.log('path---', path);
            // 写一个promise
            return new Promise((resolve, reject) => {
                uni.getFileInfo({
                    filePath: path,
                    success: function (res) {
                        console.log(res, 'res====uni.getImageInfo');
                        resolve(res.size);
                    },
                    fail: function (err) {
                        console.log(err, 'err====uni.getImageInfo');
                        reject(err);
                    },
                });
            });
        },
        // 返回上一级
        closeEvent() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        // 返回首页
        backMainPage() {
            // this.$sKit.layer.useRouter('/pages/thirdhome/main', '','switchTab');
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.backHomeFun();
            // #endif
        },
        // //关闭弹窗
        // clickBtn() {
        //     this.$refs['popDialogFlag'].close();
        // },
        // 关闭预览图片
        // closeImg(){
        //     // this.$refs.imgDialogFlag.close()
        //     this.imgPop = false
        // },
        // imgLoad(){
        //     let systemInfo = uni.getSystemInfoSync();
        //     this.imgWidth = systemInfo.windowWidth
        // },
    },
    computed: {
        // ...mapState({
        //     selectMarkerV3: state => state.locationV3_app.selectMarkerV3, //选中的油站
        // }),
        // ...mapGetters(['latV3', 'lonV3']),
    },
    components: {},
};
</script>
<style scoped lang="scss">
.question-form {
    width: 100%;
    height: 100%;
    position: relative;
    .bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        // height: 684rpx;
        height: 50%;
        display: block;
    }
    .page-wrap {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .wrap-question {
        // position: absolute;
        // left: 0;
        // top: 95px;
        // width: 100%;
        // height: calc(100% - 95px);
        overflow-y: auto;
        height: 100%;
    }
    .wrap-desc {
        // height: 11%;
        // overflow-y: auto;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 10px;
        padding: 0 34px;
        word-wrap: break-word;
        // line-break: anywhere;
        word-break: break-all;
    }
    .wrap-content {
        background: #ffffff;
        border-radius: 10px;
        padding: 20px;
        margin: 0 20px 20px;
        box-sizing: border-box;
        .wrap-list {
            margin-bottom: 20px;
            width: 100%;
            .desc {
                font-size: 13px;
                color: #333333;
                word-wrap: break-word;
                // line-break: anywhere;
                word-break: break-all;
                // margin-bottom: 10px;
            }
            .content {
                width: 100%;
                margin-top: 10px;
            }
            .check-radio {
                width: 20px;
                height: 20px;
                // margin-right: 10px;
            }
            .check-info {
                width: 90%;
                margin-left: 20rpx;
                .describe {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #333333;
                    word-wrap: break-word;
                    // line-break: anywhere;
                    word-break: break-all;
                }
                img {
                    width: 100%;
                    // width: 320rpx;
                    // height: 120rpx;
                }
                .check-input {
                    display: flex;
                    // align-items: center;
                    margin-top: 20rpx;
                    input {
                        background: #f7f7fb;
                        border-radius: 4rpx;
                        padding: 25rpx;
                        height: 44rpx;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 26rpx;
                        width: 80%;
                    }
                    .count {
                        display: flex;
                        align-items: center;
                        padding-right: 10px;
                        background: #f7f7fb;
                        color: #999999;
                    }
                }
            }
        }
    }
}
.stationinfo {
    padding-bottom: 16px;
    border-bottom: 1px solid #d3d3d3;
    box-sizing: border-box;
    margin-bottom: 24px;
}
.marlr15 {
    margin-left: 15px;
    margin-right: 15px;
}
.padt10 {
    padding-top: 10px;
}

.item-cell {
    height: 30px;
    background: #f7f7fb;
    border-radius: 6px;
    line-height: 30px;
    // max-width: 100px;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #333333;
    // padding: 0 9px;
    box-sizing: border-box;
    flex: 1;
    margin: 0 10px 10px 0; // 间隙为5px
    // background-color: #fff;
    width: calc((100% - 20px) / 2); // 这里的10px = (分布个数2-1)*间隙5px, 可以根据实际的分布个数和间隙区调整
    min-width: calc((100% - 20px) / 2); // 加入这两个后每个item的宽度就生效了
    &:nth-child(2n) {
        // 去除第2n个的margin-right
        margin-right: 0;
    }
}
.item-sel {
    border-radius: 6px;
    font-size: 13px;
    font-weight: 400;
    color: #e64f22;
    position: relative;
    &.item-cell:after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid #e64f22;
        border-radius: 6px;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
}
.mart12 {
    margin-top: 12px;
}
.mart8 {
    margin-top: 8px;
}
.marr8 {
    margin-right: 8px;
}

.mart16 {
    margin-top: 16px;
}
.oil-address {
    margin-top: 8px;
}
.img-47 {
    width: 47px;
    height: 45px;
    flex-shrink: 0;
    margin-right: 12px;
}
.img-24 {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.wid108 {
    width: 108px;
    margin-right: 11px;
}
.img-28 {
    width: 28px;
    height: 25px;
    // margin-bottom: 10px;
}
.pad-25-16 {
    padding: 10px 16px;
    box-sizing: border-box;
}

.pab18 {
    padding-bottom: 18px;
}
// .padr13 {
//     padding-right: 13px;
// }

.input-div {
    background: #f7f7fb;
    border-radius: 2px;
    padding: 5px;
    width: 100%;
    margin-top: 8px;
    // margin-bottom: 16px;
    // overflow: hidden;
    box-sizing: border-box;

    textarea {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        background: #f7f7fb;
        border: none;
        resize: none;
        height: 72px;
        font-size: 13px;
    }
}
.photo-div {
    width: 120rpx;
    height: 120rpx;
    background: #ffffff;
    border-radius: 8rpx;
    border: 1rpx dashed #ff4000;
}
.con-list {
    width: 100%;
    .photo-area {
        width: 120rpx;
        position: relative;
        margin-right: 8px;
        .area-img {
            width: 120rpx;
            height: 120rpx;
            margin: auto;
        }
        .img-14 {
            width: 14px;
            height: 14px;
            display: block;
            position: absolute;
            top: -2px;
            right: -6px;
        }
    }
}
.con-list::after {
    content: '';
    width: 30%;
}
.refund-btn {
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
    // width: 343px;
    // height: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 10px;
    // padding-bottom: calc(20px + env(safe-area-inset-bottom));
    box-sizing: border-box;
    margin-bottom: 60rpx;
}
._modal {
    flex: none;
    width: 280px;
    min-height: 104px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 19px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 34px;
            text-align: center;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}
.mask-pop {
    background: rgba(0, 0, 0, 1);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    //     z-index: 13; /* 设置遮罩层位于其他元素之上 */
    //     opacity: 1;
    .mask {
        top: 0;
        left: 0;
        opacity: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        .mask-photo {
            width: 100vw;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // margin: 0 auto;

            .preview-img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
