<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas" style="position: relative; background: #f7f7fb">
        <zj-navbar title="退款申请" :border-bottom="false"></zj-navbar>
        <div class="content">
            <div class="form-box">
                <div class="form-item col">
                    <div class="form-left">昆仑e享卡余额</div>
                    <div class="form-right">{{ walletInfo.walletBalance || 0 }}元</div>
                </div>
                <div class="line"></div>
                <div class="form-item col">
                    <div class="form-left">昆仑e享卡冻结金额</div>
                    <div class="form-right">{{ walletInfo.freezeBalance || 0 }}元</div>
                </div>
            </div>
            <div class="form-box">
                <div class="form-item col">
                    <div class="form-left">退款原因</div>
                    <div class="form-right2" @click="checkReason()"
                        >{{ submitCheck.name || '请选择' }}<img src="../../images/arrow_down.png" alt=""
                    /></div>
                </div>
                <div class="line"></div>
                <div class="form-item row">
                    <div class="form-top">退款说明</div>
                    <div class="form-bottom">
                        <textarea
                            class="textarea"
                            v-model="describe"
                            maxlength="20"
                            auto-height
                            fixed
                            placeholder-style="color:#999999"
                            placeholder="请输入"
                            @input="describeInput" 
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="btn-box">
            <div class="btn" @click="submit"> 申请退款 </div>
        </div>
        <custom-popup ref="popup" type="bottom" :animation="true" :maskClick="false">
            <div class="popup-box">
                <div class="popup-top">
                    <div class="top-left" @click="closePopup">取消</div>
                    <div class="top-center">退款原因</div>
                    <div class="top-right" @click="confirmItem">确认</div>
                </div>
                <div class="popup-item" @click="checkItem(item)" v-for="(item, index) in checkList">
                    <div class="item-left" :class="{ checked: item.id == checkedReason.id }">{{ item.name }}</div>
                    <div class="item-right"><img v-if="item.id == checkedReason.id" src="../../images/select.png" alt="" /></div>
                </div>
            </div>
        </custom-popup>

        <zj-show-modal>
            <div class="tc_div" v-if="showModalType == 1">
                <div class="title-cancel">申请退款成功！</div>
            </div>
        </zj-show-modal>
        <custom-popup ref="popupCancel" type="center" :animation="true" :maskClick="false">
            <view class="_modalBox">
                <view class="_modal">
                    <div class="font-18 color-333 weight-500 title-cancel">申请退款失败</div>
                    <div class="_modal_top">
                        <view v-if="customErr || errorCode">
                            <view class="title">{{ customErr }}</view>
                            <view class="content" v-if="errorCode">{{ errorCode }}</view>
                        </view>
                        <slot v-else></slot>
                    </div>
                    <view class="btnbox">
                        <slot name="button">
                            <view class="btn confirm" @click.stop="clickBtn('')">确定</view>
                        </slot>
                    </view>
                </view>
            </view>
        </custom-popup>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { applyRefund, applyRefundCancel } from '../../../../s-kit/js/v3-http/https3/oilCard/index.js';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            checkList: [
                { name: '充值误操作', id: 1 },
                { name: '暂时不使用', id: 2 },
                { name: '消费不方便', id: 3 },
                { name: '其他原因', id: 4 },
            ],
            checkedReason: {},
            describe: '',
            submitCheck: {},
            showModalType: 0,
            customErr: '',
            errorCode: '',
        };
    },
    methods: {
        describeInput(e) {
            this.describe = e.detail.value.substring(0,100)
        },
        // 退款原因
        checkReason() {
            this.checkedReason = this.submitCheck;
            this.$refs.popup.open();
        },
        // 取消退款
        closePopup() {
            this.$refs.popup.close();
        },
        // 选择退款原因
        checkItem(item) {
            this.checkedReason = item;
        },
        // 确认退款
        confirmItem() {
            this.submitCheck = this.checkedReason;
            this.$refs.popup.close();
        },
        // 申请退款
        async submit() {
            if (!this.submitCheck.id) {
                this.$store.dispatch('zjShowModal', {
                    title: '请选择退款原因',
                    confirmText: '确定',
                    cancelText: '',
                    success: res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return false;
            }
            if (this.describe.length < 10) {
                this.$store.dispatch('zjShowModal', {
                    title: '退款说明内容长度至少为10个字符',
                    confirmText: '确定',
                    cancelText: '',
                    success: res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return false;
            }
            let params = {
                reasonCode: this.submitCheck.id,
                describe: this.describe,
            };

            let res = await applyRefund(params, { isCustomErr: true });
            if (res.success) {
                this.showModalType = 1;
                await this.$store.dispatch('getSetWalletStatus');
                this.$store.dispatch('zjShowModal', {
                    confirmText: '确定',
                    cancelText: '',
                    success: res => {
                        if (res.confirm) {
                            // 4. 返回上一页面
                            uni.navigateBack({
                                delta: 2, // 返回的页面数
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                // 截取字符串后面的数据
                let errIndex = res.message.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.message.slice(0, errIndex);
                    customErr = res.message.slice(errIndex + 1, res.message.length);
                } else {
                    customErr = res.message;
                }
                this.$refs.popupCancel.open();
                this.customErr = customErr;
                this.errorCode = res.errorCode;
            }

            return;
        },
        // 关闭弹窗
        clickBtn() {
            this.$refs.popupCancel.close();
            // 4. 返回上一页面
            uni.navigateBack({
                delta: 2, // 返回的页面数
            });
        },
    },
    computed: {
        ...mapGetters(['walletInfo']),
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 0 30rpx;
    .form-box {
        border-radius: 16rpx;
        background: #ffffff;
        padding: 0 30rpx;
        margin-top: 24rpx;

        .form-item {
            display: flex;

            .form-left {
                line-height: 95rpx;
                font-size: 28rpx;
                color: #333333;
            }
            .form-right {
                line-height: 95rpx;
                font-size: 28rpx;
                font-weight: bold;
                color: #666666;
            }
            .form-right2 {
                line-height: 95rpx;
                font-size: 28rpx;
                color: #999999;
                display: flex;
                align-items: center;

                img {
                    margin-left: 12rpx;
                    width: 32rpx;
                    height: 32rpx;
                }
            }
            .form-top {
                padding: 29rpx 0 16rpx;
                font-size: 28rpx;
                color: #333333;
                line-height: 40rpx;
            }
            .form-bottom {
                width: 630rpx;
                min-height: 216rpx;
                background: #f7f7fb;
                border-radius: 4rpx;
                margin-bottom: 32rpx;
                padding: 21rpx 18rpx;
                .textarea {
                    background: #f7f7fb;
                    width: 100%;
                    min-height: 216rpx !important;
                }
            }
        }
        .col {
            justify-content: space-between;
            align-items: center;
        }
        .row {
            flex-direction: column;
            align-items: flex-start;
        }
        .line {
            height: 1px;
            background: #eeeeee;
        }
    }
}

.btn-box {
    position: absolute;
    bottom: env(safe-area-inset-bottom);
    left: 0;
    right: 0;
    height: 152rpx;
    padding: 32rpx;
    background: #ffffff;
    .btn {
        height: 88rpx;
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        text-align: center;
        line-height: 88rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
        border-radius: 16rpx;
    }
}

.popup-box {
    background: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    padding: 0 30rpx 6rpx;
    .popup-top {
        display: flex;
        justify-content: space-between;
        div {
            color: #333333;
            line-height: 98rpx;
            font-size: 26rpx;
            padding: 0 4rpx;

            &:nth-of-type(2) {
                font-size: 32rpx;
                font-weight: bold;
            }
        }
    }
    .popup-item {
        background: #ffffff;
        border-top: 1px solid #efeff4;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item-left {
            font-size: 28rpx;
            color: #000000;
            line-height: 88rpx;
        }
        .checked {
            font-weight: bold;
            color: #e64f22;
        }
        .item-right {
            img {
                width: 24rpx;
                height: 22rpx;
            }
        }
    }
}

.tc_div {
    .title-cancel {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        margin-top: 6px;
        text-align: center;
    }
}
._modalBox {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12000;
    display: flex;
    justify-content: center;
    align-items: center;

    ._modal {
        flex: none;
        width: 560rpx;
        min-height: 207rpx;
        background: #ffffff;
        overflow: hidden;
        border-radius: 20rpx;
        .title-cancel {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: 46rpx;
            padding: 0 30px;
            margin-top: 20px;
            text-align: center;
        }

        ._modal_top {
            padding: 40rpx;
            min-height: 114rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .title {
                text-align: center;
                font-size: 28rpx;
                font-weight: bold;
                color: #333333;
                line-height: 46rpx;
            }

            .content {
                padding: 0 20rpx;
                margin-bottom: 34rpx;
                margin-top: 8rpx;
                width: 100%;
                min-height: 68rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #000000;
                line-height: 36rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                background-image: linear-gradient(270deg, rgba(245, 245, 245, 0) 0%, #f7f7f7 50%, rgba(245, 245, 245, 0) 100%);
            }
        }

        .btnbox {
            border-top: 2rpx solid #efeff4;
            display: flex;
            width: 100%;
            height: 90rpx;
            flex-direction: row;
            justify-content: space-between;

            .btn {
                flex: 1;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 28rpx;
                font-weight: 400;
                line-height: 90rpx;

                &:active {
                    background-color: #f7f7f7;
                }
            }

            .confirm {
                font-weight: bold;
            }
        }
    }
}
</style>
