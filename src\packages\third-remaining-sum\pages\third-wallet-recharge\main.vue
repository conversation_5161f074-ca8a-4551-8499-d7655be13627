<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="wallet_recharge p-bf bg-F7F7FB">
            <zj-navbar title="充值" :border-bottom="false"></zj-navbar>
            <div class="wallet_recharge_content">
                <div class="wallet_recharge_content_top">
                    <div class="recharge_div">
                        <div class="div_title">选择充值金额</div>
                        <div class="select_div" v-if="getList.length > 1">
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == index + 1 ? 'select_class' : ''"
                                @click="changeSelect(index + 1, item.amount)"
                                v-for="(item, index) in getList"
                                :key="index"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">{{ item.amount }}</div>
                                    <div class="unit_div">元</div>
                                </div>
                                <div class="active_div font-12 color-E64F22">{{ item.describe }}</div>
                            </div>
                        </div>

                        <div class="select_div" v-else>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 1 ? 'select_class' : ''"
                                @click="changeSelect(1, 200)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">200</div>
                                    <div class="unit_div">元</div>
                                </div>
                            </div>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 2 ? 'select_class' : ''"
                                @click="changeSelect(2, 300)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">300</div>
                                    <div class="unit_div">元</div>
                                </div>
                            </div>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 3 ? 'select_class' : ''"
                                @click="changeSelect(3, 500)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">500</div>
                                    <div class="unit_div">元</div>
                                </div>
                                <!-- <div class="active_div font-12 color-E64F22">送20元油品券</div> -->
                            </div>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 4 ? 'select_class' : ''"
                                @click="changeSelect(4, 800)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">800</div>
                                    <div class="unit_div">元</div>
                                </div>
                                <!-- <div class="active_div font-12 color-E64F22">送20元油品券</div> -->
                            </div>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 5 ? 'select_class' : ''"
                                @click="changeSelect(5, 1000)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">1000</div>
                                    <div class="unit_div">元</div>
                                </div>
                                <!-- <div class="active_div font-12 color-E64F22">送20元油品券</div> -->
                            </div>
                            <div
                                class="select_item fl-column fl-al-jus-cen"
                                :class="selectId == 6 ? 'select_class' : ''"
                                @click="changeSelect(6, 2000)"
                            >
                                <div class="fl-row fl-al-jus-cen">
                                    <div class="num_div">2000</div>
                                    <div class="unit_div">元</div>
                                </div>
                                <!-- <div class="active_div font-12 color-E64F22">送20元油品券</div> -->
                            </div>
                        </div>
                    </div>
                    <div class="other_div">
                        <div class="div_title">其他金额</div>
                        <div>
                            <input
                                type="digit"
                                @input="handleNum"
                                placeholder="￥请输入充值金额"
                                placeholder-class="wallet_charge_input"
                                v-model="otherAmount"
                                @focus="changeAmount()"
                            />
                        </div>
                    </div>
                    <div class="tips_div">
                        <div class="title"> <img src="../../images/tips.png" alt />温馨提示： </div>
                        <div class="tips">
                            <div>
                                <div class="dots_div"></div>昆仑e享卡每天充值次数最多为<div class="color-ff6b2c">5次</div
                                >，仅限使用本人付款账户充值。
                            </div>
                        </div>
                        <div class="tips"> <div class="dots_div"></div>昆仑e享卡充值时不提供发票，消费后方可开具电子发票。 </div>
                        <div class="tips">
                            <div class="dots_div"></div>退款规则：仅支持全额退款，每日限1次，每月限2次，每年限5次（自然年）。
                        </div>
                        <!-- #ifndef MP-MPAAS-->
                        <!-- <div class="tips"
                            ><div class="dots_div"></div
                            >即日起能源e站将不再支持充值卡充值，存量充值卡可下载能源e站App兑换为等额礼品卡，在加油和购买商品时直接抵扣，如有疑问可咨询加油站工作人员。</div
                        > -->
                        <!-- #endif -->
                    </div>
                </div>
                <!-- #ifndef H5-CLOUD -->
                <div class="btn_div fl-jus-bet">
                    <!-- #ifdef MP-MPAAS-->
                    <div
                        v-if="!isHarmony && isOpenSwitchGiftCard"
                        class="finish_verification btn-plain color-E64F22 font-16"
                        @click="rechargeCardClick()"
                        >充值卡绑定</div
                    >
                    <!-- #endif -->
                    <div
                        :class="[
                            payingFlag ? 'bg-opacity-288' : 'btnColor',
                            androidIosApp && isOpenSwitchGiftCard ? 'finish_verification' : 'finish_verification2',
                        ]"
                        class="color-fff font-16"
                        @click="rechargeClick()"
                        >立即充值</div
                    >
                </div>
                <!-- #endif -->
                <!-- #ifdef H5-CLOUD -->
                <div class="btn_div fl-jus-bet">
                    <div
                        class="finish_verification2 color-fff font-16"
                        :class="payingFlag ? 'bg-opacity-288' : 'btnColor'"
                        @click="rechargeClick()"
                        >立即充值</div
                    >
                </div>
                <!-- #endif -->
                <!-- #ifndef H5-CLOUD -->
                <!-- TODO @version? -->
                <!-- <div class="card_recharge">
                    <div @click="toCardRecharge()">实体卡充值</div>
                </div> -->
                <!-- #endif -->
            </div>
            <ZjPopup ref="listmaskPopup" :maskClick="false" type="bottom">
                <div class="zj_popup bg-fff">
                    <div class="zj_nav">
                        <div class="font-16 color-333 weight-bold">选择支付方式</div>
                        <img src="../../images/close_btn.png" alt @click="closePopup()" />
                    </div>
                    <div
                        class="pay_list line_bottom fl-row fl-jus-bet fl-al-cen"
                        v-for="(item, index) in list"
                        :key="index"
                        @click="selectedPay(item)"
                    >
                        <div class="list_left fl-row fl-al-cen">
                            <div class="pay_left fl-row fl-al-cen">
                                <img :src="item.img" alt />
                                <div class="font-14 color-000 pay_name">{{ item.payTypeName }}</div>
                            </div>
                            <div v-if="item.channelDescription" class="marketing_copy marketing_copy_width">{{
                                item.channelDescription
                            }}</div>
                        </div>
                        <img class="select_img" v-if="payType == item.payType" src="../../images/select_btn.png" alt />
                    </div>
                    <div class="confirm_btn primary-btn color-fff font-18 weight-bold" @click="confirmPayment()">确认支付</div>
                </div>
            </ZjPopup>
            <!-- <uni-popup ref="tipsPopup" type="center" :animation="false">
                <div
                    >尊敬的中国石油昆仑e享卡客户：您好，昆仑e享卡仅限使用本人付款账户进行充值，若付款账户与昆仑e享卡账户的实名信息不一致，将无法进行充值。
                </div>
            </uni-popup> -->

            <uniPopup v-if="!isOpenSwitchGiftCard" ref="inputDialog" type="dialog">
                <UniPopupDialog
                    ref="inputClose"
                    content="温馨提示：充值卡充值不参加充值赠券活动"
                    mode="input"
                    placeholder="请输入19位充值卡密码"
                    title="充值卡充值"
                    @close="dialogInputClose"
                    @confirm="dialogInputConfirm"
                ></UniPopupDialog>
            </uniPopup>
        </div>
        <zj-old-account v-if="isTransfer" :activate="activate"></zj-old-account>
        <zj-show-modal>
            <!-- <div class="tc_div">
                    <div class="tc_title">充值卡充值</div>
                    <input type="number" class="recharge_input" v-model="rechargeableCard" placeholder="请输入19位充值卡密码" />
                </div> -->
            <div class="tc_div" v-if="showModalPop">
                <div class="title">温馨提示</div>
                <div class="text">
                    尊敬的中国石油昆仑e享卡客户：您好，昆仑e享卡仅限使用本人付款账户进行充值，若付款账户与昆仑e享卡账户的实名信息不一致，将无法进行充值。
                </div>
            </div>
            <rechargeCardDeactivated v-if="rechargeCardDeactivatedFlag"></rechargeCardDeactivated>
        </zj-show-modal>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import { preOrderRecharge, balance, rechargeByCard, queryRechargeActivityInfo } from '../../../../s-kit/js/v3-http/https3/wallet';
import { cardList } from '../../../../s-kit/js/v3-http/https3/oilCard/index.js';
import uniPopup from '../../../../s-kit/components/layout/custom-popup/uni-popup/uni-popup.vue';
import UniPopupDialog from '../../../../s-kit/components/layout/custom-popup/uni-popup-dialog/uni-popup-dialog.vue';
// #ifdef MP-MPAAS
import appWalletRecharge from './diff-environment/app-wallet-recharge.js';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD
import wxWalletRecharge from './diff-environment/wx-wallet-recharge.js';
import zfbWalletRecharge from './diff-environment/zfb-wallet-recharge.js';
// #endif
// #ifdef H5-CLOUD
import cloundWalletRecharge from './diff-environment/clound-wallet-recharge.js';
// #endif
import { mixRecharge, clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import rechargeCardDeactivated from '@/components/rechargeCardDeactivated/main.vue';
export default {
    name: 'third-wallet-recharge',
    components: {
        rechargeCardDeactivated,
        ZjPopup,
        UniPopupDialog,
        uniPopup,
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appWalletRecharge,
        // #endif
        // #ifndef MP-MPAAS || H5 || H5-CLOUD
        wxWalletRecharge,
        zfbWalletRecharge,
        // #endif
        // #ifdef H5-CLOUD
        cloundWalletRecharge,
        // #endif
    ],
    data() {
        return {
            rechargeableCard: '',
            show: false,
            payShow: false,
            selectId: 0,
            rechargeAmt: '',
            otherAmount: '',
            list: [
                // {"channelNo": "2", "channelSerialNumber": "100", "payType": "支付方式", "payTypeName": "支付宝"},
            ],
            orderId: '',
            payType: '',
            payList: [],
            paySelectId: 0,
            thirdCardList: '',
            // 区分功能限制电子卡迁移成功后返回的到哪个页面
            activate: '',
            payingFlag: false,
            qKey: '',
            getList: [],
            showModalPop: false,
            cardPassword: '',
            refer: '',
            addressName: '',
            // 判断是否需要去获取支付方式的标识
            isObtain: false,
            // 旧值存储，用来对比输入或者选择的金额是否发生了变化，如果发生了变化再去获取支付方式拿营销文案
            oldPayAmountOfMoney: '',
            // 充值卡充值停用弹窗标识
            rechargeCardDeactivatedFlag: false,
            // 充值卡兑换礼品卡开关
            isOpenSwitchGiftCard: false,
            androidIosApp: false,
        };
    },
    async onLoad(options) {
        // #ifdef MP-ALIPAY
        this.payPlugin = await this.$sKit.aliPayPlugin?.init();
        // #endif
        // #ifdef MP-WEIXIN
        await this.$sKit.wxPayPlugin?.initPayPlugin();
        // #endif
        // #ifdef MP-MPAAS

        await this.openSwitchGiftCard();
        if (this.isHarmony) {
            this.payPlugin = await this.$sKit.mpaasPayPlugin?.init();
            await this.$cnpcBridge.isCutScreen(true);
        } else {
            this.androidIosApp = true;
        }
        // #endif
        // #ifdef H5-CLOUD
        await this.$sKit.cloudPayPlugin.initPayPlugin();
        // #endif
        const popFlag = uni.getStorageSync('recharge_popUp_prompt');
        console.log(popFlag, 'recharge_popUp_prompt=======');
        if (!popFlag) {
            this.showModal();
        }
        let params = {};
        if (options) {
            if (Object.keys(options).length != 0) {
                params = JSON.parse(decodeURIComponent(options.data)) || {};
                this.refer = params?.refer || '';
                console.log(params?.cardPassword, 'params?.cardPassword======');
                if (params?.cardPassword) {
                    this.cardPassword = params?.cardPassword;
                    console.log(this.cardPassword, 'this.cardPassword ====');
                }
            }
        }
        this.confirmPayment = this.$sKit.commonUtil.throttleUtil(this.confirmPayment);
        // 获取钱包基本信息
        await this.getWalletInfo(true);
        //查询卡列表
        this.getThirdCardList();
    },
    methods: {
        openSwitchGiftCard() {
            this.$cnpcBridge.getSwitch('RechargeCardToGiftCard', res => {
                console.log(res, '获取开关');
                if (res == 'yes') {
                    this.isOpenSwitchGiftCard = true;
                }
            });
        },
        showModal() {
            this.showModalPop = true;
            setTimeout(() => {
                // this.$refs.tipsPopup.open();
                this.$store.dispatch('zjShowModal', {
                    confirmText: '我知道了',
                    confirmColor: '#E64F22',
                    success: res => {
                        if (res.confirm) {
                            uni.setStorageSync('recharge_popUp_prompt', true);
                            this.showModalPop = false;
                        }
                    },
                });
            }, 300);
        },
        /*
        获取营销文案及金额
        */
        async getQueryRechargeActivityInfo() {
            let params = {
                organizeCode: this.walletInfo.addressNo || '',
                // organizeCode: '1-A5001-C001',
            };
            let res = await queryRechargeActivityInfo(params);
            // let res = this.res;
            if (res.success) {
                this.getList = res.data;
            }
            console.log(res, '获取营销文案=======');
        },
        /**
         * @description  : 跳转实体卡充值方法
         * @param         {String} officialAccountParams -是否是公众号跳转过来点击实体卡充值
         * @param         {Boolean} backWalletRechange -公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
         * @param         {Function} eWalletNormal -功能限制
         * @param         {String} activate -公众号跳转到小程序钱包充值页面，在迁移成功时返回钱包充值页面标识
         * @return        {*}
         */
        toCardRecharge() {
            // #ifdef MP-WEIXIN || MP-ALIPAY
            // 是否是公众号或车生活跳转过来点击实体卡充值
            if (this.officialAccountParams == 'cz' || this.qKey == 'walletRecharge') {
                // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
                this.$store.commit('mSetBackWalletRechange', true);
                // 功能限制
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        let url = '/packages/third-oil-card/pages/my-card/main';
                        let params = { refer: 'r46' };
                        let type = 'navigateTo';
                        this.$sKit.layer.useRouter(url, params, type);
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        // 公众号跳转到小程序钱包充值页面，在迁移成功时返回钱包充值页面标识
                        this.activate = 'FHCZ';
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    freeze: 0,
                    walletAddParams: {
                        refer: 'r18',
                    },
                });
                return;
            }
            // #endif
            if (this.thirdCardList.length == 1) {
                let url = '/packages/third-oil-card/pages/oil-card-recharge/main';
                let params = { ...this.thirdCardList[0], refer: 'r22' };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            } else {
                let url = '/packages/third-oil-card/pages/my-card/main';
                let params = { refer: 'r46' };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        // 获取油卡列表长度
        getThirdCardList({ isInit = false } = {}) {
            cardList().then((res, err) => {
                if (res && res.success) {
                    this.thirdCardList = res.data.rows;
                }
            });
        },
        //钱包基本信息
        async getWalletInfo(init = false, callback) {
            console.log(init, '是什么值');
            await this.$store.dispatch('getAccountBalanceAction');
            if (this.walletInfo.accountStatus === 1) {
                await this.getQueryRechargeActivityInfo();
            }
            if (init) {
                this.$sKit.mpBP.tracker('充值', {
                    seed: 'rechargeBiz',
                    pageID: 'ecardRecharge', // 页面名
                    refer: this.refer || '', // 来源
                    channelID: clientCode, // C10/C12/C13
                    address: this.walletInfo.addressName || '',
                });
            }
        },
        //获取支付方式
        getPaymentMethod() {
            return new Promise(async resolve => {
                let params = {};
                console.log(this.walletInfo, 'this.walletInfo====');
                // #ifdef MP-MPAAS
                if (this.isHarmony) {
                    params.areaCode = this.walletInfo.addressNo;
                    params.amount = this.rechargeAmt || this.otherAmount;
                } else {
                    params.paramsJsonStr = encodeURIComponent(
                        JSON.stringify({
                            areaCode: this.walletInfo.addressNo,
                            amount: this.rechargeAmt || this.otherAmount,
                        }),
                    );
                }
                // #endif
                // #ifndef MP-MPAAS
                params.areaCode = this.walletInfo.addressNo;
                params.amount = this.rechargeAmt || this.otherAmount;
                // #endif
                try {
                    resolve(await this.getRechargeMethod(params));
                } catch (error) {
                    // #ifdef MP-WEIXIN
                    await this.$sKit.wxPayPlugin?.initPayPlugin();
                    // #endif
                    // #ifdef MP-ALIPAY
                    this.payPlugin = await this.$sKit.aliPayPlugin?.init();
                    // #endif
                    // #ifdef MP-MPAAS
                    if (this.isHarmony) {
                        this.payPlugin = await this.$sKit.mpaasPayPlugin?.init();
                        await this.$cnpcBridge.isCutScreen(true);
                    }
                    // #endif
                    resolve(await this.getRechargeMethod(params));
                    throw new Error(error);
                }
            });
        },
        //关闭选择支付方式弹窗
        closePopup() {
            this.$refs.listmaskPopup.close();
            // this.payType = this.list[0].payType;
        },
        //打开选择支付方式弹窗
        getCharge() {
            this.$refs.listmaskPopup.open();
        },

        //充值卡充值弹窗
        rechargeCardClick() {
            const params = { refer: 'r56' };
            const url = '/packages/third-gift-card/pages/binding/main';
            this.$sKit.layer.useRouter(url, params, 'redirectTo');
            // this.rechargeCardDeactivatedFlag = true;
            // const modalConfig = {
            //     confirmText: '去绑定',
            //     cancelText: '取消',
            //     cancelColor: '#666666',
            //     success: res => {
            //         if (res.confirm) {
            //             this.rechargeCardDeactivatedFlag = false;
            //             const params = { refer: 'r56' };
            //             const url = '/packages/third-gift-card/pages/binding/main';
            //             this.$sKit.layer.useRouter(url, params, 'redirectTo');
            //         } else if (res.cancel) {
            //             this.rechargeCardDeactivatedFlag = false;
            //             console.log('用户点击取消');
            //         }
            //     },
            // };
            // this.$store.dispatch('zjShowModal', modalConfig);
        },
        dialogInputConfirm(e) {
            this.rechargeableCard = e;
            this.rechargeCard();
        },
        //充值卡input校验
        handleNum(e) {
            let value = e.detail.value;
            var price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            this.$nextTick(() => {
                this.otherAmount = typeof price === 'string' ? price : price[0];
            });
            // this.otherAmount = this.otherAmount.replace(
            //   /^(-)*(\d+)\.(\d\d).*$/,
            //   "$1$2.$3"
            // );
        },
        /**
         * @description  : 立即充值
         * @param         {String} officialAccountParams -是否是公众号跳转过来点击实体卡充值
         * @param         {Boolean} backWalletRechange -公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
         * @param         {Function} eWalletNormal -功能限制
         * @param         {String} activate -公众号跳转到小程序钱包充值页面，在迁移成功时返回钱包充值页面标识
         * @param         {String} rechargeAmt -选择的充值金额
         * @param         {String} otherAmount -自定义输入的充值金额
         * @return        {*}
         */
        async rechargeClick() {
            if (this.payingFlag) return;
            // #ifdef MP-WEIXIN || MP-ALIPAY || H5-CLOUD
            // 是否是公众号或车生活跳转过来点击立即充值
            if (this.officialAccountParams == 'cz' || this.qKey == 'walletRecharge') {
                console.log(this.officialAccountParams, '在外部跳转到3.0充值页面');
                // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
                this.$store.commit('mSetBackWalletRechange', true);
                // 功能限制
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: async () => {
                        if (!this.rechargeAmt && !this.otherAmount) {
                            uni.showToast({
                                title: '请输入充值金额',
                                icon: 'none',
                                duration: 2000,
                            });
                            return;
                        }
                        if (this.list.length == 0) {
                            // 没有获取到支付方式就停止向下执行
                            if (!(await this.getPaymentMethod())) return;
                        }
                        // #ifdef MP-WEIXIN || MP-ALIPAY || H5-CLOUD
                        if (this.list.length === 1) {
                            this.selectedPay(this.list[0]);
                            await this.confirmPayment();
                        } else {
                            this.getCharge();
                        }
                        // #endif
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.activate = 'FHCZ'; //返回充值
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    freeze: 0,
                    walletAddParams: {
                        refer: 'r18',
                    },
                });
                return;
            }
            // #endif
            if (!this.rechargeAmt && !this.otherAmount) {
                uni.showToast({
                    title: '请输入充值金额',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 新值旧值进行判断 如果相同不进行获取支付方式拿营销文案
            const checkIsObtain = (oldValue, newValue) => oldValue && oldValue !== newValue;
            // 将其他金额和选择金额赋值到用一个变量上
            const finAmount = this.otherAmount || this.rechargeAmt;
            // 使用函数进行判断并赋值
            this.isObtain = checkIsObtain(this.oldPayAmountOfMoney, finAmount);
            this.oldPayAmountOfMoney = finAmount;
            // 为了避免每次点击充值按钮都去执行获取充值支付方式，加个标识，在支付方式数据不为空且金额有变化的时候在执行
            if (this.list.length > 0 && !this.isObtain) {
                // #ifdef MP-WEIXIN || MP-ALIPAY || H5-CLOUD
                if (this.list.length === 1) {
                    this.selectedPay(this.list[0]);
                    await this.confirmPayment();
                } else {
                    this.getCharge();
                }
                // #endif
                // #ifdef MP-MPAAS
                this.selectedPay(this.list[0]);
                this.getCharge();
                // #endif
            } else {
                // 没有获取到支付方式就停止向下执行
                if (!(await this.getPaymentMethod())) return;
                // #ifdef MP-WEIXIN || MP-ALIPAY || H5-CLOUD
                if (this.list.length === 1) {
                    this.selectedPay(this.list[0]);
                    await this.confirmPayment();
                } else {
                    this.getCharge();
                }
                // #endif
                // #ifdef MP-MPAAS
                this.getCharge();
                // #endif
            }
        },
        // 获取完支付方式后处理支付方式数组
        setIconAndRechar(res) {
            return new Promise((resolve, reject) => {
                uni.hideLoading();
                console.log(res, '获取的支付方式');
                if (res.code === 'PAY_SUCCESS' && res.data && res.data.length > 0) {
                    this.isObtain = false;
                    console.log(res.data, 'res.data====APP获取支付方式');
                    let rechargeList = this.isHarmony ? res.data.filter(item => item.payType === 2 || item.payType === 1) : res.data;
                    rechargeList.map(item => {
                        let img = '';
                        switch (Number(item.payType)) {
                            case 1:
                                img = require('../../images/wx.png');
                                break;
                            case 2:
                                img = require('../../images/zfb.png');
                                break;
                            case 8:
                                img = require('../../images/yun_unionpay.png');
                                break;
                            case 4:
                                img = require('../../images/kunpeng.png');
                                break;
                            case 23:
                                img = require('../../images/icbc.png');
                                break;
                            default:
                                break;
                        }
                        item.img = img || '';
                    });
                    this.list = rechargeList;
                    this.selectedPay(this.list[0]);
                    // 更新payingFlag状态
                    this.payingFlag = false;
                    resolve(true);
                } else {
                    resolve(false);
                    // #ifdef MP-MPAAS
                    if (this.isHarmony) {
                        this.errModules(res);
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                    // #endif
                    // #ifndef MP-MPAAS
                    this.errModules(res);
                    // #endif
                }
            });
        },
        // 获取支付方式报错提示
        errModules(res) {
            // 截取字符串后面的数据
            let errIndex = res.msg.indexOf(':');
            let errorCode = '';
            let customErr = '';
            if (errIndex !== -1) {
                errorCode = res.msg.slice(0, errIndex);
                customErr = res.msg.slice(errIndex + 1, res.msg.length);
            } else {
                customErr = res.msg;
            }
            this.$store.dispatch('zjShowModal', {
                title: customErr,
                content: `${errorCode}`,
                confirmText: '确定',
                success(res) {
                    if (res.confirm) {
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 选择金额
        changeSelect(num, amount) {
            this.selectId = num;
            this.rechargeAmt = amount;
            this.otherAmount = '';
        },
        // 获取支付方式
        selectedPay(item) {
            this.payType = item.payType;
        },
        // 自定义金额
        changeAmount() {
            this.selectId = 0;
            this.rechargeAmt = '';
        },

        /**
         * @description  : 充值卡充值方法
         * @param         {String} cardPwd -充值卡密码
         * @param         {String} accessToken -accessToken
         * @return        {*}
         */
        async rechargeCard() {
            let tokenInfo = await uni.getStorageSync('tokenInfo');
            console.log(this.rechargeableCard, 'rechargeableCard');
            if (!(this.rechargeableCard && this.rechargeableCard.length === 19)) {
                // this.$toast("请输入正确的充值卡密码");
                uni.showToast({
                    title: '请输入正确的充值卡密码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let res = null;

            // #ifdef MP-MPAAS
            let userInfo = await this.$cnpcBridge.getUserTokenInfo();
            res = await rechargeByCard({
                cardPwd: this.rechargeableCard,
                accessToken: userInfo.token3,
                path: 'PUBLIC_CLOUD',
            });
            // #endif
            // #ifdef MP-WEIXIN
            res = await rechargeByCard({
                cardPwd: this.rechargeableCard,
                gsmsToken: tokenInfo.accessToken,
                channel: '05',
            });
            // #endif
            // #ifdef MP-ALIPAY
            res = await rechargeByCard({
                cardPwd: this.rechargeableCard,
                tokenForAli: tokenInfo.accessToken,
                channel: '05',
            });
            res.result = res.InfoCode == 1 ? 'success' : 'fail';
            res.status = res.InfoCode == 1 ? 0 : 1;
            res.data = res.Data;
            // #endif

            let data = res.data;
            if (res.status == 0) {
                // this.$toast('充值成功');
                if (data.hasOwnProperty('amount')) {
                    // this.$dialog.alert({ message: })//返回单位是分，处理一下
                    this.$store.dispatch('zjShowModal', {
                        content: `恭喜您，成功充值${data.amount / 100}元`,
                        success: res => {
                            if (res.confirm) {
                                // 刷新钱包余额
                                this.$store.dispatch('getAccountBalanceAction');
                                uni.setStorageSync('refreshWalletBlanaceFlag', true);
                            } else if (res.cancel) {
                                this.rechargeableCard = '';
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
        // 支付方式（1:微信支付 2:支付宝支付 3:加油卡账户 4:电子卡账户 29:现金 11:优惠券 12:礼品卡 13:油币 14:能源币  扫码传99）
        async confirmPayment() {
            if (!this.payType) {
                uni.showToast({
                    icon: 'none',
                    title: '请选择充值支付方式',
                    duration: 2000,
                });
                return;
            }
            //关闭选择框
            this.closePopup();
            if (this.otherAmount !== '' && this.otherAmount < (mixRecharge || 1)) {
                uni.showToast({
                    icon: 'none',
                    title: '充值金额不能小于一元',
                    duration: 2000,
                });
                return;
            }
            this.payingFlag = true;
            let params = { rechargeAmt: this.rechargeAmt || this.otherAmount, payWay: this.payType };
            console.log(params, 'params============');
            uni.showLoading({
                title: '加载中',
            });
            let resMP = await preOrderRecharge(params);
            if (resMP && resMP.success) {
                this.callUpPayment(resMP);
                uni.hideLoading();
            } else {
                this.payingFlag = false;
                uni.hideLoading();
            }
        },
        dialogInputClose() {
            this.cardPassword = '';
            this.$refs.inputClose.close();
        },
    },
    computed: {
        ...mapState({
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 存储外部跳转进来携带的参数
            // #endif
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            walletInfo: state => state.wallet.walletInfo, //是否显示弹窗
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    watch: {
        cardPassword: {
            handler(val, oldVal) {
                if (val) {
                    setTimeout(() => {
                        if (this.isOpenSwitchGiftCard) return;
                        this.$refs.inputDialog.open();
                        this.$refs.inputClose.val = val.toString().trim();
                    }, 500);
                }
            },
            immediate: true,
            deep: true,
        },
        rechargeAmt: {
            handler(val, oldVal) {
                if (val !== '' && val !== oldVal) {
                    console.log('选择的值', val, oldVal);
                    this.isObtain = true;
                }
            },
        },
        otherAmount: {
            handler(val, oldVal) {
                if (val !== '' && val !== oldVal) {
                    console.log('输入的值', val, oldVal);
                    this.isObtain = true;
                }
            },
        },
    },
    async beforeDestroy() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
};
</script>

<style lang="scss" scoped>
.btnColor {
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
}

.grayColor {
    background-color: lightgray;
}

.wallet_recharge {
    .wallet_recharge_content {
        padding: 0 15px;
        box-sizing: border-box;

        .wallet_recharge_content_top {
            background: #fff;
            border-radius: 8px;
            margin-top: 10px;
            padding: 15px;
        }

        .wallet_recharge_content_top {
            .recharge_div {
                .select_div {
                    display: flex;
                    align-items: center;
                    text-align: center;
                    flex-direction: row;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    margin-top: 13px;

                    .select_item {
                        width: 30%;
                        height: 60px;
                        margin-bottom: 15px;
                        padding: 9px 0;
                        border-radius: 4px;
                        border: 1px solid #e8e8e8;

                        .num_div {
                            font-size: 16px;
                            font-weight: bold;
                            color: #666666;
                            line-height: 23px;
                        }

                        .unit_div {
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 23px;
                        }

                        .active_div {
                            line-height: 17px;
                        }
                    }

                    .select_class {
                        background: rgba(230, 79, 34, 0.1);
                        border-radius: 2px;
                        border: 1px solid #e64f22;
                        border-radius: 4px;

                        div {
                            color: #e64a1d;
                        }
                    }
                }
            }

            .other_div {
                div {
                    input {
                        width: 100%;
                        height: 40px;
                        border-radius: 2px;
                        border: 1px solid #dfdfed;
                        margin-top: 15px;
                        font-size: 16px;
                        font-weight: 400;
                        color: #333;
                        padding-left: 9px;
                        box-sizing: border-box;
                    }
                }
            }
        }
    }

    @mixin flex {
        /* #ifndef APP-NVUE */
        display: flex;
        /* #endif */
        flex-direction: row;
    }

    @mixin height {
        /* #ifndef APP-NVUE */
        height: 100%;
        /* #endif */
        /* #ifdef APP-NVUE */
        flex: 1;
        /* #endif */
    }

    .box {
        @include flex;
    }

    .button {
        @include flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 35px;
        margin: 0 5px;
        border-radius: 5px;
    }

    .example-body {
        background-color: #fff;
        padding: 10px 0;
    }

    .button-text {
        color: #fff;
        font-size: 12px;
    }

    .popup-content {
        @include flex;
        align-items: center;
        justify-content: center;
        padding: 15px;
        height: 50px;
        background-color: #fff;
    }

    .popup-height {
        @include height;
        width: 200px;
    }

    .text {
        font-size: 12px;
        color: #333;
    }

    .popup-success {
        color: #fff;
        background-color: #e1f3d8;
    }

    .popup-warn {
        color: #fff;
        background-color: #faecd8;
    }

    .popup-error {
        color: #fff;
        background-color: #fde2e2;
    }

    .popup-info {
        color: #fff;
        background-color: #f2f6fc;
    }

    .success-text {
        color: #09bb07;
    }

    .warn-text {
        color: #e6a23c;
    }

    .error-text {
        color: #f56c6c;
    }

    .info-text {
        color: #909399;
    }

    .dialog,
    .share {
        /* #ifndef APP-NVUE */
        display: flex;
        /* #endif */
        flex-direction: column;
    }

    .dialog-box {
        padding: 10px;
    }

    .dialog .button,
    .share .button {
        /* #ifndef APP-NVUE */
        width: 100%;
        /* #endif */
        margin: 0;
        margin-top: 10px;
        padding: 3px 0;
        flex: 1;
    }

    .dialog-text {
        font-size: 14px;
        color: #333;
    }
}

.wallet_recharge_content_bottom {
    background: #fff;
    padding: 0 15px 26px 15px;

    .payment_div {
        padding: 0 15px;

        .payment_item {
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            flex-direction: row;
            justify-content: space-between;

            .left {
                width: 50%;
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 14px 0;

                img {
                    width: 20px;
                    height: 20px;
                }

                div {
                    font-size: 14px;
                    color: #000;
                    margin-left: 11px;
                }
            }

            .right_select {
                width: 6%;

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }
}

.recharge_div,
.other_div,
.wallet_recharge_content_bottom {
    .div_title {
        font-size: 16px;
        font-weight: bold;
        color: #000000;
        line-height: 23px;
    }
}

.van-action-sheet__header {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
}

.van-action-sheet__close {
    font-size: 13px;
    color: #999;
}

.dialog {
    height: 105px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 30px 0px 40px 0px;

    .input-class {
        width: 90%;
        text-indent: 13px;
        height: 40px;
        border-radius: 4px;
        border: 1px solid #dfdfed;
        font-size: 14px;
        font-weight: 400;
        color: #333;
    }
}

.tips_div {
    margin-top: 16px;

    .title {
        display: flex;
        align-items: center;
        flex-direction: row;
        font-size: 12px;
        color: #333333;
        line-height: 18px;
    }

    img {
        width: 14px;
        height: 14px;
        margin-right: 5px;
    }

    .color-ff6b2c {
        color: #ff6b2c;
        display: inline-block;
        text-indent: 0;
    }

    p {
        text-indent: 20px;
        font-size: 12px;
        line-height: 18px;
    }

    .tips {
        font-size: 12px;
        line-height: 18px;
    }
}

.btn_div {
    margin-top: 20px;
    font-size: 15px;
    display: flex;
    flex-direction: row;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}

.zj_popup {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 15px 15px;

    .zj_nav {
        position: relative;
        height: 20px;

        div {
            line-height: 16px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        img {
            width: 13px;
            height: 13px;
            position: absolute;
            right: 3px;
        }
    }

    // .pay_list_div {
    .pay_list {
        padding: 16px 13px;

        &:nth-last-child(1) {
            border: none;
        }

        .list_left {
            width: 90%;

            .pay_left {
                width: 40%;

                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                }
            }

            .marketing_copy_width {
                width: 60%;
            }
        }

        .select_img {
            width: 16px;
            height: 16px;
        }
    }

    .confirm_btn {
        height: 44px;
        line-height: 44px;
        border-radius: 8px;
    }

    // }
}

.tc_div {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;

    .tc_title {
        font-size: 16px;
        color: #000;
        margin-bottom: 40rpx;
    }

    input {
        font-size: 24rpx;
        width: 100%;
        height: 60rpx;
        border-radius: 8rpx;
        padding-left: 30rpx;
        border: 2rpx solid #dedddd;
    }
}

.card_recharge {
    width: 100%;
    margin-top: 24px;

    div {
        height: 22px;
        font-size: 14px;
        font-weight: 500;
        color: #ff6b2c;
        line-height: 22px;
        float: right;
        flex-wrap: nowrap;
        text-decoration: underline;
    }
}

.dots_div {
    width: 5px;
    height: 5px;
    background: #e6e6e6;
    border-radius: 50%;
    margin-top: 5px;
    float: left;
    margin: 6px 7px 0px 20px;
}

.marketing_copy {
    color: #dc3e40;
    font-size: 12px;
    margin-left: 10px;
    margin-top: 1.5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.tc_div {
    .title {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        text-align: center;
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-top: 10px;
        text-indent: 2em;
    }
}
</style>

<style lang="scss" scoped>
.wallet_charge_input {
    color: #999;
    font-size: 14px;
    line-height: 20px;
}
</style>
