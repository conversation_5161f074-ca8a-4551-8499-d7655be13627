<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            back-text="重置密码"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="cell">
            <div class="cell-title">原密码</div>
            <input
                class="cell-input"
                :class="{ 'input-focus-cursor': clickPasswordIndex === 3 }"
                type="text"
                @click="clickPassword(3)"
                :password="isShowPrimaryPassword"
                v-model="primaryPassword"
                placeholder="请输入密码"
                disabled
            />
            <img class="cell-icon" @click="clickEye(3)" src="/static/eye-none.png" v-if="isShowPrimaryPassword" mode="widthFix" />
            <img class="cell-icon" @click="clickEye(3)" src="/static/eye-block.png" v-else mode="widthFix" />
        </div>
        <div class="cell">
            <div class="cell-title">新密码</div>
            <input
                class="cell-input"
                :class="{ 'input-focus-cursor': clickPasswordIndex === 1 }"
                type="text"
                @click="clickPassword(1)"
                :password="isShowPassword"
                v-model="password"
                placeholder="请输入密码"
                disabled
            />
            <img class="cell-icon" @click="clickEye(1)" src="/static/eye-none.png" v-if="isShowPassword" mode="widthFix" />
            <img class="cell-icon" @click="clickEye(1)" src="/static/eye-block.png" v-else mode="widthFix" />
        </div>
        <div class="cell">
            <div class="cell-title">确认密码</div>
            <input
                class="cell-input"
                :class="{ 'input-focus-cursor': clickPasswordIndex === 2 }"
                type="text"
                @click="clickPassword(2)"
                :password="isShowDefinePassword"
                v-model="definePassword"
                placeholder="请输入密码"
                disabled
            />
            <img class="cell-icon" @click="clickEye(2)" src="/static/eye-none.png" v-if="isShowDefinePassword" mode="widthFix" />
            <img class="cell-icon" @click="clickEye(2)" src="/static/eye-block.png" v-else mode="widthFix" />
        </div>
        <div class="forget-view">
            <div class="forget-text" @click="clickForget">忘记密码？</div>
        </div>
        <div class="venicle-btn">
            <div class="venicle-btn-view">
                <div class="venicle-btn-text" @click="clickDefine">确定</div>
            </div>
            <div class="btn-text-sp"></div>
        </div>
        <u-keyboard
            ref="uKeyboard"
            :zIndex="10"
            tips="安全键盘"
            mode="number"
            @change="keyboardChange"
            @backspace="keyboardBackspace"
            :dot-enabled="false"
            :mask="false"
            :random="showKeyboard"
            v-model="showKeyboard"
            safe-area-inset-bottom
        ></u-keyboard>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import pageConfig from '@/utils/pageConfig.js';
import { resetPayPass } from '@/api/home.js';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            password: '',
            isShowPassword: true,
            definePassword: '',
            isShowDefinePassword: true,
            primaryPassword: '',
            isShowPrimaryPassword: true,
            clickPasswordIndex: 0, // 1 为第一次密码，2 为确认密码
            showKeyboard: false,
        };
    },
    computed: {},
    onUnload() {},
    methods: {
        // 小眼睛点击事件
        clickEye(index) {
            if (index == 1) {
                this.isShowPassword = !this.isShowPassword;
            } else if (index == 2) {
                this.isShowDefinePassword = !this.isShowDefinePassword;
            } else if (index == 3) {
                this.isShowPrimaryPassword = !this.isShowPrimaryPassword;
            }
        },
        // 点击忘记密码
        clickForget() {
            uni.navigateTo({
                url: '/packages/password/pages/edit-password/main?isfor=1',
            });
        },
        // 确认按钮点击事件
        async clickDefine() {
            console.log(this.$util.payPassEncryption(this.password));
            if (!/^\d{6}$/.test(this.primaryPassword)) return this.$util.noneToast('请输入6位原密码');
            if (!/^\d{6}$/.test(this.password)) return this.$util.noneToast('请输入6位新密码');
            if (this.primaryPassword == this.password) return this.$util.noneToast('原密码和新密码一致');
            if (this.password != this.definePassword) return this.$util.noneToast('两次输入的密码不匹配');
            let param = {
                password: this.$util.payPassEncryption(this.primaryPassword),
                passwordNew: this.$util.payPassEncryption(this.password),
                passwordConfirm: this.$util.payPassEncryption(this.definePassword),
            };
            let res = await resetPayPass(param);
            if (res.status == -1) {
                await this.$util.showModal(res.info, true);
            } else {
                await this.$util.showModal('设置成功', true);
                uni.navigateBack();
            }
        },
        // 键盘输入事件
        keyboardChange(text) {
            if (this.clickPasswordIndex == 1) {
                this.password = this.password.length >= 6 ? this.password : this.password + text;
            } else if (this.clickPasswordIndex == 2) {
                this.definePassword = this.definePassword.length >= 6 ? this.definePassword : this.definePassword + text;
            } else if (this.clickPasswordIndex == 3) {
                this.primaryPassword = this.primaryPassword.length >= 6 ? this.primaryPassword : this.primaryPassword + text;
            }
        },
        // 安全键盘唤起事件
        clickPassword(index) {
            this.clickPasswordIndex = index;
            this.showKeyboard = true;
        },
        // 键盘退格事件
        keyboardBackspace() {
            if (this.clickPasswordIndex == 1) {
                this.password = this.password.substring(0, this.password.length - 1);
            } else if (this.clickPasswordIndex == 2) {
                this.definePassword = this.definePassword.substring(0, this.definePassword.length - 1);
            } else if (this.clickPasswordIndex == 3) {
                this.primaryPassword = this.primaryPassword.substring(0, this.primaryPassword.length - 1);
            }
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    .forget-view {
        display: flex;
        justify-content: flex-end;
        margin-top: 5px;
        .forget-text {
            padding-right: 15px;
            font-size: 13px;
            line-height: 50px;
            color: #333333;
        }
    }
    .cell {
        position: relative;
        z-index: 100000;
        display: flex;
        align-items: center;
        margin-left: 15px;
        width: 345px;
        padding-left: 10px;
        padding-right: 10px;
        background-color: #ffffff;
        border-radius: 5px;
        height: 50px;
        margin-top: 10px;
        .cell-title {
            color: #333333;
            font-size: 15px;
            font-weight: 700;
        }
        .cell-input {
            position: relative;
            text-align: right;
            flex: 1;
            line-height: 50px;
            color: #333333;
        }
        .cell-icon {
            padding-left: 20px;
            width: 16px;
            max-height: 16px;
            padding-top: 10px;
            padding-bottom: 10px;
        }
        .cell-code {
            margin-left: 10px;
            color: $btn-color;
            font-size: 15px;
            width: 78px;
            line-height: 50px;
            text-align: center;
        }
    }
}
.venicle-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    .venicle-btn-view {
        height: 64px;
        width: 100vw;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .venicle-btn-text {
            line-height: 44px;
            width: 345px;
            background-color: $btn-color;
            border-radius: 5px;
            color: #ffffff;
            text-align: center;
            font-weight: 700;
        }
    }
    .btn-text-sp {
        width: 100vw;
        background-color: #ffffff;
        height: env(safe-area-inset-bottom);
    }
}
</style>
