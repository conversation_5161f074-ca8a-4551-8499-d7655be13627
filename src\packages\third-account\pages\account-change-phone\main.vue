<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 更换手机号页面 -->
        <div class="bg-FFFFFF" style="height: 100%">
            <zj-navbar :height="44" title="更换绑定手机号"></zj-navbar>
            <div class="content">
                <div class="line">
                    <input
                        type="tel"
                        maxlength="11"
                        class="input_div"
                        placeholder-class="phone_input"
                        placeholder="请输入新的手机号"
                        v-model="phone"
                    />
                </div>
                <div class="line">
                    <input
                        type="tel"
                        class="input_div"
                        placeholder-class="phone_input"
                        v-model="verificationCode"
                        placeholder="请输入短信验证码"
                    />
                    <div v-if="show" @click="getuserNewPhoneVerifyCodeNend">获取验证码</div>
                    <div v-else class="marginRight">{{ count }}秒后重新获取</div>
                </div>
                <div class="btn" @click="getuserBasicInfoModify">确认更换绑定手机</div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { userNewPhoneVerifyCodeNend, userBasicInfoModify } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { mapGetters } from 'vuex';
import Vue from 'vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            verificationCode: '',
            phone: '',
            type: 0,
            show: true,
            count: 60,
            timer: null,
            imageCheckShow: false,
            authInfo: null,
            identityAuthStatus: '', //实人认证状态
            userInfo: {},
            messageType: false,
        };
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
    },
    onLoad(options) {
        let dataParams = JSON.parse(decodeURIComponent(options.data));
        this.type = dataParams.type;
        this.authInfo = dataParams.authInfo ? dataParams.authInfo : null;
    },
    async mounted() {
        this.getuserBasicInfoQuery(); //查询用户详细信息--获取用户认证状态

        // #ifdef MP-MPAAS
        let userInfo = await this.$cnpcBridge.getUserTokenInfo();
        this.userInfo = userInfo;
        // #endif
    },
    watch: {},
    methods: {
        //获取验证码
        async getuserNewPhoneVerifyCodeNend() {
            let params = {
                type: this.type,
                newPhone: this.phone,
                authInfo: this.authInfo,
            };
            let res = await userNewPhoneVerifyCodeNend(params);
            if (res && res.success) {
                if (res.data.existsFlag) {
                    //判断手机号是否已注册
                    uni.showToast({
                        title: '手机号已注册',
                        icon: 'none',
                        duration: 2000,
                    });
                } else {
                    if (res.data.sendFlag) {
                        //判断验证码是否发送成功
                        uni.showToast({
                            title: '验证码已发送',
                            icon: 'none',
                            duration: 2000,
                        });
                        this.messageType = true;
                        this.countFn();
                    } else {
                        uni.showToast({
                            title: '验证码发送失败,请重新认证',
                            icon: 'none',
                            duration: 2000,
                        });
                        if (this.identityAuthStatus == 15) {
                            url = '/packages/third-my-center/pages/real-person/main'; //去实人认证
                        } else {
                            url = '/packages/third-account/pages/account-phone-verify/main'; //去短信验证码验证
                        }
                    }
                }
            } else {
                if (this.identityAuthStatus == 15) {
                    url = '/packages/third-my-center/pages/real-person/main'; //去实人认证
                } else {
                    url = '/packages/third-account/pages/account-phone-verify/main'; //去短信验证码验证
                }
            }
        },
        //获取新手机号验证码按钮倒计时
        countFn() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        //点击确认更换手机号
        async getuserBasicInfoModify() {
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let params = {
                type: 3,
                mobile: this.phone,
                messageCode: this.verificationCode,
            };
            let res = await userBasicInfoModify(params);
            if (res && res.success) {
                this.userInfo.phone = this.phone;
                // #ifdef MP-MPAAS
                // 原生存储数据
                this.$cnpcBridge.setValueToNative('UserTokenInfo', encodeURIComponent(JSON.stringify(this.userInfo)), () => {
                    this.$sKit.layer.useRouter('/packages/third-account/pages/phone-result/main', {}, 'navigateTo');
                });
                // #endif
            }
        },
        //查询用户详细信息--获取用户认证状态
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.identityAuthStatus = res.data.identityAuthStatus;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 16px;

    .line {
        height: 44px;
        background: #f7f7fb;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        border-radius: 4px;
        overflow: hidden;

        .input_div {
            flex: 1;
            height: 100%;
            padding-left: 12px;
            background: #f7f7fb;
        }

        div {
            height: 30px;
            line-height: 30px;
            width: 104px;
            text-align: center;
            color: #e64f23;
            border-left: 1px solid #d3d3d3;
        }
    }

    .btn {
        height: 44px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        line-height: 44px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
    }
}
</style>
