// #ifdef MP-WEIXIN
import { mapState, mapGetters } from 'vuex';
export default {
    computed: {
        ...mapState({
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    onLoad(options) {},
    methods: {
        bindGiftCardFun() {
            return new Promise((resolve, reject) => {
                uni.showLoading();
                this.accountDataPlugin
                    .bindGiftCardByCipher(this.cardKey)
                    .then(res => {
                        uni.hideLoading();
                        if (res.code == 0) {
                            resolve();
                        } else {
                            reject(res.msg);
                        }
                    })
                    .catch(res => {
                        reject(res.msg);
                    });
            });
        },
    },
};
// #endif
