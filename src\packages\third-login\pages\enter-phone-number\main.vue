<template>
    <div class="one_lick_login">
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>
        <div class="padding_input">
            <div class="main_heading marign_bottom">请输入手机号</div>
            <input type="text" maxlength="11" class="input_div" placeholder-class="phone_input" placeholder="请输入手机号" />

            <div class="agreement_div">
                <img v-if="!select" src="../../images/not_selected.png" @click="changeSelect(true)" alt />
                <img v-else src="../../images/selected.png" @click="changeSelect(false)" alt />
                我已阅读并同意能源e站
                <div class="argreement_rules">《用户协议》</div>和
                <div class="argreement_rules">《隐私政策》</div>
            </div>
            <div class="primary-btn2" @click="getCode()">获取验证码</div>
            <div class="help_div help_div_text_bt">
                <div class="help_text" @click="toLogin()">使用已有账号登录</div>
                <div class="help_text" @click="forgetPw()">遇到问题？</div>
                <div class="help_text" @click="changePhNum()">换绑手机号</div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'rgister-an-account',
    data() {
        return {
            select: false,
        };
    },
    methods: {
        toLogin() {
            this.$sKit.layer.useRouter('/packages/third-login/pages/normal-login/main', {}, 'navigateTo');
        },
        forgetPw() {
            this.$sKit.layer.useRouter('/packages/third-login/pages/enter-phone-number/main', {}, 'navigateTo');
        },
        changeSelect(val) {
            this.select = val;
        },
        changePhNum() {
            this.$sKit.layer.useRouter('/packages/third-login/pages/change-phone-number/main', {}, 'navigateTo');
        },
        getCode() {
            this.$sKit.layer.useRouter(
                '/packages/third-login/pages/enter-verification-code/main',
                { type1: true, type2: false },
                'navigateTo',
            );
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../style/style.scss';
.padding_input {
    padding: 16px;
}
.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
}
.operator {
    margin-bottom: 16px;
    font-size: 12px;
    color: #999;
}
input {
    height: 51px;
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    font-size: 21px;
    font-weight: 600;
    color: #333333;
    line-height: 30px;
    margin-bottom: 12px;
    padding-left: 20px;
    box-sizing: border-box;
}
.agreement_div {
    color: #999999;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.argreement_rules {
    color: #e64f22;
}
</style>

<style lang="scss">
.phone_input {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
}
</style>
