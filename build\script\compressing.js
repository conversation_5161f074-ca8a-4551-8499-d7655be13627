/**
 * 代码解释：
模块引入：
compressing：用于进行文件压缩操作。
path：用于处理和转换文件路径。
fs：用于进行文件系统操作，如检查目录是否存在、创建目录等。
路径定义：
projectRoot：获取项目的根目录。
sourceDir：指定要压缩的源文件所在的目录，这里是 dist/build/h5。
staticDir 和 indexHtmlPath：分别指定要压缩的 static 目录和 index.html 文件的路径。
outputDir：指定压缩包要输出到的目录，即 dist/build。
outputPath：指定压缩包的完整输出路径，也就是 dist/build/dist.zip。
确保输出目录存在：
使用 fs.existsSync 检查 dist/build 目录是否存在，如果不存在则使用 fs.mkdirSync 并设置 recursive: true 递归创建该目录。
创建压缩流并添加内容：
创建一个 compressing.zip.Stream() 实例 zipStream 来表示一个 ZIP 压缩流。
检查 static 目录和 index.html 文件是否存在，如果存在则使用 addEntry 方法将它们添加到压缩流中。
创建可写流并进行管道传输：
创建一个可写流 destStream 用于将压缩后的数据写入到 dist.zip 文件中。
使用 pipe 方法将 zipStream 中的数据传输到 destStream 中。
监听事件：
finish 事件：当压缩完成且数据成功写入到文件后触发，输出压缩完成的提示信息。
error 事件：当压缩过程中出现错误时触发，输出错误信息。
 */

const compressing = require('compressing');
const path = require('path');
const fs = require('fs');

// 获取项目根目录
const projectRoot = __dirname;

// 定义要压缩的源目录和文件
const sourceDir = path.join(projectRoot, '../../dist/build/h5');
const staticDir = path.join(sourceDir, 'static');
const indexHtmlPath = path.join(sourceDir, 'index.html');

// 定义压缩包的输出目录
const outputDir = path.join(projectRoot, '../../dist/build');
// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// 定义压缩包的完整输出路径
const outputPath = path.join(outputDir, 'dist.zip');

// 创建 zip 流
const zipStream = new compressing.zip.Stream();

// 检查 static 目录是否存在，如果存在则添加到压缩流
if (fs.existsSync(staticDir)) {
    zipStream.addEntry(staticDir);
}

// 检查 index.html 文件是否存在，如果存在则添加到压缩流
if (fs.existsSync(indexHtmlPath)) {
    zipStream.addEntry(indexHtmlPath);
}

// 创建可写流用于写入压缩文件
const destStream = fs.createWriteStream(outputPath);

// 将 zip 流通过管道传输到可写流
zipStream.pipe(destStream);

// 监听可写流的完成事件
destStream.on('finish', () => {
    console.log(`压缩完成，压缩包已保存到: ${outputPath}`);
});

// 监听可写流的错误事件
destStream.on('error', (err) => {
    console.error('压缩过程中出现错误:', err);
});