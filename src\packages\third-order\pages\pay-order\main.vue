<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column" style="height: 100%">
            <zj-navbar :border-bottom="false" title="支付订单"></zj-navbar>
            <div class="viewBox f-1 mh-0">
                <div class="money">&yen;{{ orderInfo.rechargeAmount }}</div>
                <div class="btn" :class="isCanPay ? 'payColor' : 'bg-opacity-288'" @click="toSuccess">确认支付</div>
                <zj-show-modal></zj-show-modal>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import checkFKArgs from '../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import {
    bindCardInfo,
    orderQuery,
    getKLOrderQuery,
    queryUnionPayOrder,
    queryKunPengPay,
    queryCCBPay,
    rechargeByCardPost,
    getKLPayOrderInfo,
    getUnionPayOrder,
    getKunPengPay,
    getCCBPay,
} from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { oilCardRecharge, cardRecharge } from '../../../../s-kit/js/v3-http/https3/user';
import { preOrderRecharge, balance, rechargeByCard } from '../../../../s-kit/js/v3-http/https3/wallet';
import appPayOrder from './diff-environment/app-pay-order';
import wxPayOrder from './diff-environment/wx-pay-order';
import zfbPayOrder from './diff-environment/zfb-pay-order';
import cloundPayOrder from './diff-environment/cloud-pay-order';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    name: 'pay-order',
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appPayOrder,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxPayOrder,
        zfbPayOrder,
        // #endif
        // #ifdef H5-CLOUD
        cloundPayOrder,
        // #endif
    ],
    data() {
        return {
            orderInfo: {},
            orderId: '',
            payList: [],
            payType: '',
            dowmTime: '',
            walletInfo: {},
            isCanPay: false,
        };
    },
    onLoad(options) {
        // 获取昆仑e享卡信息
        this.getWalletInfo();
        this.orderInfo = JSON.parse(decodeURIComponent(options.data));
        this.toSuccess = this.$sKit.commonUtil.throttleUtil(this.toSuccess);
    },
    mounted() {},
    onShow() {},
    methods: {
        // 获取昆仑e享卡信息
        async getWalletInfo() {
            let res = await balance();
            if (res.success) {
                this.walletInfo = res.data;
                this.$store.dispatch('setWalletInfoAct', res.data);
                this.isCanPay = true;
            }
        },
        // 确认支付
        async toSuccess() {
            if (!this.isCanPay) return;
            this.isCanPay = false;
            this.orderId = this.orderInfo.orderNo;
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: this.orderId,
                rcvAmt: this.orderInfo.rechargeAmount + '',
                realAmt: this.orderInfo.rechargeAmount + '',
                payType: this.orderInfo.paymentType + '',
                // #ifdef MP-MPAAS
                extendFiled: JSON.stringify(await checkFKArgs.getFKArgs('sdk')),
                // #endif
            };
            this.callPlugIn(params);
        },
    },
};
</script>

<style lang="scss" scoped>
.viewBox {
    padding: 35rpx;
    align-items: center;
    background-color: #f7f7fb;
    text-align: center;

    .money {
        margin-top: 20rpx;
        color: #e64f22;
        font-size: 60rpx;
        line-height: 60rpx;
        font-weight: bold;
    }

    .time {
        color: #333;
        font-size: 36rpx;
        line-height: 36rpx;
        margin-top: 40rpx;
    }

    .pay_box {
        margin-top: 40rpx;
        background-color: white;
        border-radius: 16rpx;

        .pay_list {
            padding: 16px 13px;

            .list_left {
                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                }
            }

            .select_img {
                width: 12px;
                height: 11px;
            }
        }
    }

    .btn {
        width: 100%;
        margin-top: 100rpx;
        height: 44px;
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        line-height: 44px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
    }
    .payColor {
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }
    .grayColor {
        background-color: lightgray;
    }
}
</style>
