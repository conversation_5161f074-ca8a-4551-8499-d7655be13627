<template>
    <div class="view">
        <!--自定义地址选择器-->
        <div class="cc_area_mask" v-show="show == true"></div>
        <div :class="{ cc_area_view: true, show: show, hide: !show }">
            <div class="cc_area_view_btns">
                <text class="cc_area_view_btn_cancle" @tap="handleNYZAreaCancle">取消</text>
                <text class="cc_area_view_btn_sure" @tap="handleNYZAreaSelect" :data-province="province" :data-city="city" :data-area="area"
                    >确定</text
                >
            </div>
            <picker-view class="cc_area_pick_view" indicator-style="height: 35px;" @change="handleNYZAreaChange" :value="value">
                <picker-view-column>
                    <view v-for="(item, index) in provinces" :key="index" class="cc_area_colum_view">{{ item }}</view>
                </picker-view-column>
                <picker-view-column>
                    <view v-for="(item, index) in citys" :key="index" class="cc_area_colum_view">{{ item }}</view>
                </picker-view-column>
                <picker-view-column>
                    <view v-for="(item, index) in areas" :key="index" class="cc_area_colum_view">{{ item }}</view>
                </picker-view-column>
            </picker-view>
        </div>
    </div>
</template>

<script>
// import { getProvinces, AreaJsonFun, getMyCity, getAreas } from './area.js';

let index = [0, 0, 0];

export default {
    mixins: [
        {
            methods: {
                setData: function (obj, callback) {
                    let that = this;
                    const handleData = (tepData, tepKey, afterKey) => {
                        tepKey = tepKey.split('.');
                        tepKey.forEach(item => {
                            if (tepData[item] === null || tepData[item] === undefined) {
                                let reg = /^[0-9]+$/;
                                tepData[item] = reg.test(afterKey) ? [] : {};
                                tepData = tepData[item];
                            } else {
                                tepData = tepData[item];
                            }
                        });
                        return tepData;
                    };
                    const isFn = function (value) {
                        return typeof value == 'function' || false;
                    };
                    Object.keys(obj).forEach(function (key) {
                        let val = obj[key];
                        key = key.replace(/\]/g, '').replace(/\[/g, '.');
                        let front, after;
                        let index_after = key.lastIndexOf('.');
                        if (index_after != -1) {
                            after = key.slice(index_after + 1);
                            front = handleData(that, key.slice(0, index_after), after);
                        } else {
                            after = key;
                            front = that;
                        }
                        if (front.$data && front.$data[after] === undefined) {
                            Object.defineProperty(front, after, {
                                get() {
                                    return front.$data[after];
                                },
                                set(newValue) {
                                    front.$data[after] = newValue;
                                    that.$forceUpdate();
                                },
                                enumerable: true,
                                configurable: true,
                            });

                            // #ifndef VUE3
                            that.$set(front, after, val);
                            // #endif

                            // #ifdef VUE3
                            Reflect.set(front, after, val);
                            // #endif
                        } else {
                            // #ifndef VUE3
                            that.$set(front, after, val);
                            // #endif

                            // #ifdef VUE3
                            Reflect.set(front, after, val);
                            // #endif
                        }
                    });
                    isFn(callback) && this.$nextTick(callback);
                },
            },
        },
    ],
    data() {
        return {
            provinces: [],
            citys: [],
            value: [0, 0, 0],
            areas: [],
            provincesCodeList: [],
            citysCodeList: [],
            slidingOrNotFlag: false,
        };
    },
    watch:{

    },
    components: {},
    props: {
        // 省
        province: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },
        // 市
        city: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },
        // 区
        area: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },

        show: {
            //控制area_select显示隐藏
            type: Boolean,
            default: false,
        },
        // 省市数组
        provinceCityArray: {
            type: Array,
            default: [],
        },
        maskShow: {
            //是否显示蒙层
            type: Boolean,
            default: true,
        },
    },
    mounted() {
        this.AreaJson = this.provinceCityArray;
        console.log(this.AreaJson, 'this.AreaJson');
        this.getProvinces();
        // this.getMyCity()
        this.getMyCity(index[0], index[1]);
        this.getAreas(index[0], index[1], index[2]);
    },
    methods: {
        getProvinces() {
            this.provinces = [];
            for (var i = 0; i < this.AreaJson.length; i++) {
                this.provinces.push(this.AreaJson[i].name);
                this.provincesCodeList.push(this.AreaJson[i].name);
            }
            console.log(this.provinces, 'this.provinces');
            return this.provinces;
        },
        /**
         * 获取省对应的所有城市
         */

        getMyCity(provinceIndex) {
            var citys = [];
            for (var i = 0; i < AreaJson[provinceIndex].city.length; i++) {
                citys.push(AreaJson[provinceIndex].city[i]);
            }

            return citys;
        },
        /**
         * 获取省市对应的所有地区
         */

        getAreas(provinceIndex, cityIndex) {
            var areas = [];
            areas = AreaJson[provinceIndex].areas[cityIndex];
            return areas;
        },
        /**
         * 获取省对应的所有城市
         */
        getMyCity(provinceIndex) {
            this.citys = [];
            this.citysCodeList = [];
            for (var i = 0; i < this.AreaJson[provinceIndex].city.length; i++) {
                if (['北京市', '上海市', '重庆市', '天津市'].includes(this.AreaJson[provinceIndex].city[i])) {
                    console.log(...this.AreaJson[provinceIndex].city[i], '...this.AreaJson[provinceIndex].city[i].area');
                    this.citys.push(this.AreaJson[provinceIndex].city[i]);
                } else {
                    this.citys.push(this.AreaJson[provinceIndex].city[i]);
                    this.citysCodeList.push(this.AreaJson[provinceIndex].city[i]);
                }
            }
            return this.citys;
        },
        getAreas(provinceIndex, cityIndex) {
            this.areas = [];
            console.log(this.AreaJson[provinceIndex].city[cityIndex], 'this.AreaJson[provinceIndex].city[cityIndex]');
            for (var i = 0; i < this.AreaJson[provinceIndex].city.length; i++) {
                // console.log(this.AreaJson[provinceIndex].city[cityIndex], 'this.AreaJson[provinceIndex].city[i]');
                if (['北京市', '上海市', '重庆市', '天津市'].includes(this.AreaJson[provinceIndex].city[cityIndex])) {
                    this.areas = this.AreaJson[provinceIndex].city[i];
                    console.log(this.areas, 'areas===');
                } else {
                    this.areas = this.AreaJson[provinceIndex].areas[cityIndex];
                }
            }
            console.log(this.areas, 'this.areas');
            return this.areas;
        },
        handleNYZAreaChange: function (e) {
            this.slidingOrNotFlag = true;
            var that = this;
            console.log('e:' + JSON.stringify(e));
            var value = e.detail.value;
            /**
             * 滚动的是省
             * 省改变 市、区都不变
             */

            if (index[0] != value[0]) {
                index = [value[0], 0, 0];
                let selectCitys = this.getMyCity(index[0]);
                let selectAreas = this.getAreas(index[0], 0);
                that.setData({
                    province: this.provinces[index[0]],
                    citys: selectCitys,
                    areas: selectAreas,
                    value: [index[0], 0, 0],
                    city: selectCitys[0],
                    area: selectAreas[0],
                });
                console.log(this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]], '省改变 市、区都不变');
                that.$emit('changeClick', this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]]);
            } else if (index[1] != value[1]) {
                /**
                 * 市改变了 省不变 区变
                 */
                index = [value[0], value[1], 0];
                let selectCitys = this.getMyCity(index[0]);
                let selectAreas = this.getAreas(index[0], value[1]);
                that.setData({
                    province: this.provinces[index[0]],
                    citys: selectCitys,
                    areas: selectAreas,
                    value: [index[0], index[1], 0],
                    city: selectCitys[index[1]],
                    area: selectAreas[0],
                });
                console.log(this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]], '市改变了 省不变 区变');
                that.$emit('changeClick', this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]]);
            } else if (index[2] != value[2]) {
                /**
                 * 区改变了
                 */
                index = [value[0], value[1], value[2]];
                let selectCitys = this.getMyCity(index[0]);
                let selectAreas = this.getAreas(index[0], value[1]);
                that.setData({
                    province: this.provinces[index[0]],
                    citys: selectCitys,
                    areas: selectAreas,
                    value: [index[0], index[1], index[2]],
                    area: selectAreas[index[2]],
                    city: selectCitys[index[1]],
                });
                console.log(this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]], '区改变了');
                that.$emit('changeClick', this.provinces[index[0]], selectCitys[index[1]], selectAreas[index[2]]);
            }
        },

        /**
         * 确定按钮的点击事件
         */
        handleNYZAreaSelect: function (e) {
            let detail = {};

            if (this.slidingOrNotFlag) {
                detail = e.target.dataset;
                console.log(detail, 'detail');
            } else {
                detail = { province: this.provinces[0], city: this.citys[0], area: this.areas[0] };
            }
            const provinceIndex = this.provinces.indexOf(detail.province);
            const cityIndex = this.citys.indexOf(detail.city);
            const areaIndex = this.areas.indexOf(detail.area);

            let province = this.provinces[provinceIndex] || '';
            let city = this.citys[cityIndex] || '';
            let area = this.areas[areaIndex] || '';

            // detail.str = `${province}/${city}/${area}`;

            this.$emit('selectBrandModelYear', { detail: detail });
            console.log(detail, 'detail');
            index = [0, 0, 0];
            this.value = [0, 0, 0];
            this.getMyCity(index[0], index[1]);
        },

        /**
         * 取消按钮的点击事件
         */
        handleNYZAreaCancle: function (e) {
            var that = this;
            console.log('e:' + JSON.stringify(e));
            this.$emit('hideShow', {
                detail: false,
            });
            // 复原初始状态
            index = [0, 0, 0];
        },
    },
};
</script>
<style scoped lang="scss">
.view {
    width: 100%;
}

.cc_area_view {
    width: 100%;
    position: fixed;
    bottom: -1000px;
    left: 0px;
    background-color: #fff;
    z-index: 9999;
    transition: all 0.3s;
}

.cc_area_pick_view {
    height: 200px;
    width: 100%;
}

.cc_area_colum_view {
    line-height: 35px;
    text-align: center;
    font-size: 28rpx;
}

.hide {
    bottom: -1000rpx;
    transition: all 0.3s;
}

.show {
    bottom: 0rpx;
    transition: all 0.3s;
}

.cc_area_view_btns {
    background-color: #fff;
    border-bottom: 1px solid #eeeeee;
    font-size: 30rpx;
    padding: 18rpx 0rpx;
}

.cc_area_view_btns > text {
    display: inline-block;
    word-spacing: 4rpx;
    letter-spacing: 4rpx;
}

.cc_area_view_btn_cancle {
    color: #939393;
    padding-right: 20rpx;
    padding-left: 25rpx;
}

.cc_area_view_btn_sure {
    float: right;
    padding-left: 20rpx;
    padding-right: 25rpx;
}

.cc_area_mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(28, 28, 28, 0.6);
    position: absolute;
    top: 0;
    left: 0rpx;
    z-index: 9998;
}
</style>
