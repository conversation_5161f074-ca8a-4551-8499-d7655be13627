{"version": 3, "sources": ["webpack://vue-cup-ui/webpack/universalModuleDefinition", "webpack://vue-cup-ui/webpack/bootstrap", "webpack://vue-cup-ui/./node_modules/@soda/get-current-script/index.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?e5a4", "webpack://vue-cup-ui/./node_modules/@vue/cli-service/lib/commands/build/setPublicPath.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?a681", "webpack://vue-cup-ui/./packages/common/hybrid.js", "webpack://vue-cup-ui/packages/button/src/button.vue", "webpack://vue-cup-ui/./packages/button/src/button.vue?e932", "webpack://vue-cup-ui/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://vue-cup-ui/./packages/button/src/button.vue?ab32", "webpack://vue-cup-ui/./packages/button/index.js", "webpack://vue-cup-ui/./src/index.js", "webpack://vue-cup-ui/./node_modules/@vue/cli-service/lib/commands/build/entry-lib.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "getCurrentScript", "descriptor", "getOwnPropertyDescriptor", "document", "currentScript", "Error", "err", "pageSource", "inlineScriptSourceRegExp", "inlineScriptSource", "ieStackRegExp", "ffStackRegExp", "stackDetails", "exec", "stack", "scriptLocation", "line", "currentLocation", "location", "href", "replace", "hash", "scripts", "getElementsByTagName", "documentElement", "outerHTML", "RegExp", "trim", "length", "readyState", "src", "innerHTML", "window", "match", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "on", "handleClick", "_t", "staticRenderFns", "_agent", "navigator", "userAgent", "toLowerCase", "isFunction", "toString", "truncatedRespForJSBridge", "data", "resultString", "resultParams", "isJSBridgeEnv", "test", "isCordovaEnv", "callNative", "success", "fail", "plugin", "action", "paramArray", "iCanCall", "<PERSON><PERSON>", "WebViewJavascriptBridge", "call<PERSON><PERSON><PERSON>", "normalizeComponent", "scriptExports", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "hook", "options", "_compiled", "functional", "_scopeId", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "h", "existing", "beforeCreate", "concat", "component", "install", "<PERSON><PERSON>", "components", "opts", "for<PERSON>ach", "installed", "version", "UPButton"], "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,kBAAZC,SAA0C,kBAAXC,OACxCA,OAAOD,QAAUD,IACQ,oBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,kBAAZC,QACdA,QAAQ,cAAgBD,IAExBD,EAAK,cAAgBC,KARvB,CASoB,qBAATK,KAAuBA,KAAOC,MAAO,WAChD,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUR,QAGnC,IAAIC,EAASK,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHV,QAAS,IAUV,OANAW,EAAQH,GAAUI,KAAKX,EAAOD,QAASC,EAAQA,EAAOD,QAASO,GAG/DN,EAAOS,GAAI,EAGJT,EAAOD,QA0Df,OArDAO,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASf,EAASgB,EAAMC,GAC3CV,EAAoBW,EAAElB,EAASgB,IAClCG,OAAOC,eAAepB,EAASgB,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAASvB,GACX,qBAAXwB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAepB,EAASwB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAepB,EAAS,aAAc,CAAE0B,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAASjC,GAChC,IAAIgB,EAAShB,GAAUA,EAAO4B,WAC7B,WAAwB,OAAO5B,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAM,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,Q,uEClFrD,WAMC,SAAU1C,EAAMC,GAEb,EAAO,GAAI,EAAF,EAAS,kEAFtB,CAQkB,qBAATK,MAAuBA,MAAa,WAC3C,SAASqC,IACP,IAAIC,EAAavB,OAAOwB,yBAAyBC,SAAU,iBAE3D,IAAKF,GAAc,kBAAmBE,UAAYA,SAASC,cACzD,OAAOD,SAASC,cAIlB,GAAIH,GAAcA,EAAWpB,MAAQmB,GAAoBG,SAASC,cAChE,OAAOD,SAASC,cAKlB,IACE,MAAM,IAAIC,MAEZ,MAAOC,GAEL,IAMEC,EACAC,EACAC,EAREC,EAAgB,kCAClBC,EAAgB,6BAChBC,EAAeF,EAAcG,KAAKP,EAAIQ,QAAUH,EAAcE,KAAKP,EAAIQ,OACvEC,EAAkBH,GAAgBA,EAAa,KAAO,EACtDI,EAAQJ,GAAgBA,EAAa,KAAO,EAC5CK,EAAkBd,SAASe,SAASC,KAAKC,QAAQjB,SAASe,SAASG,KAAM,IAIzEC,EAAUnB,SAASoB,qBAAqB,UAEtCR,IAAmBE,IACrBV,EAAaJ,SAASqB,gBAAgBC,UACtCjB,EAA2B,IAAIkB,OAAO,sBAAwBV,EAAO,GAAK,iDAAkD,KAC5HP,EAAqBF,EAAWa,QAAQZ,EAA0B,MAAMmB,QAG1E,IAAK,IAAI3D,EAAI,EAAGA,EAAIsD,EAAQM,OAAQ5D,IAAK,CAEvC,GAA8B,gBAA1BsD,EAAQtD,GAAG6D,WACb,OAAOP,EAAQtD,GAIjB,GAAIsD,EAAQtD,GAAG8D,MAAQf,EACrB,OAAOO,EAAQtD,GAIjB,GACE+C,IAAmBE,GACnBK,EAAQtD,GAAG+D,WACXT,EAAQtD,GAAG+D,UAAUJ,SAAWlB,EAEhC,OAAOa,EAAQtD,GAKnB,OAAO,MAIX,OAAOgC,M,kCC7ET,yBAAuf,EAAG,G,kCCE1f,G,OAAsB,qBAAXgC,OAAwB,CACjC,IAAI5B,EAAgB4B,OAAO7B,SAASC,cAE9BJ,EAAmB,EAAQ,QAC/BI,EAAgBJ,IAGV,kBAAmBG,UACvBzB,OAAOC,eAAewB,SAAU,gBAAiB,CAAEtB,IAAKmB,IAI5D,IAAI8B,EAAM1B,GAAiBA,EAAc0B,IAAIG,MAAM,2BAC/CH,IACF,IAA0BA,EAAI,I,UAKnB,ICrBXI,EAAS,WAAa,IAAIC,EAAIvE,KAASwE,EAAGD,EAAIE,eAAmBC,EAAGH,EAAII,MAAMD,IAAIF,EAAG,OAAOE,EAAG,SAAS,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQN,EAAIO,cAAc,CAACP,EAAIQ,GAAG,YAAY,IACnLC,EAAkB,GCGtB,MAAMC,EAASC,UAAUC,UAAUC,cAEnC,SAASC,EAAWhE,GAChB,MAAiD,sBAA1CP,OAAOkB,UAAUsD,SAAS/E,KAAKc,GAG1C,SAASkE,EAAyBC,GAChC,OAAKA,EAEMA,EAAKC,aACPD,EAAKC,aACHD,EAAKE,aACPF,EAAKE,kBADP,EAHEF,EAQJ,MAAMG,EAAgB,mCAAmCC,KAAKX,GACxDY,EAAe,0BAA0BD,KAAKX,GAEpD,SAASa,EAAWC,EAASC,EAAMC,EAAQC,EAAQC,GACtD,IAAIC,GAAW,EAYf,OAXIP,GAAgBzB,OAAOiC,SACvBD,GAAW,EACXhC,OAAOiC,QAAQpD,KAAK8C,EAASC,EAAMC,EAAQC,EAAQC,IAC5CR,GAAiBvB,OAAOkC,0BAC/BF,GAAW,EACXhC,OAAOkC,wBAAwBC,YAAYN,EAAQC,EAAQC,GAAY,SAAUX,GAC7EH,EAAWU,IAAYA,EAAQR,EAAyBC,OACzD,SAAU9C,GACT2C,EAAWW,IAASA,EAAKT,EAAyB7C,QAGnD0D,EC1BX,OACE,KAAF,WACE,MAAF,CACI,MAAJ,OACI,SAAJ,OACI,QAAJ,QAEE,QAAF,CACI,mBAAJ,KACM,IAAN,iBACA,uBACU,EAAV,MAEA,YACQ,GAAR,uBACU,IAAV,EACU,GAAV,eACY,OAAZ,+BACc,KAAd,6CACgB,EAAhB,CACkB,QAAlB,MACkB,OAAlB,wCAEgB,MAEF,KAAd,wDACgB,EAAhB,CACkB,QAAlB,MACkB,OAAlB,uCAEgB,MAEF,KAAd,uDACgB,EAAhB,CACkB,QAAlB,MACkB,OAAlB,uCAEgB,MACF,QACE,MAIJ,EADZ,GAGA,MAGA,iFACA,GACQ,WAAR,KACU,KAAV,SACA,8BACY,QAAZ,uCACY,EAAZ,CACc,QAAd,eACc,OAAd,kCAGY,KAAZ,yBAEA,KAGI,YAAJ,GACA,kBACA,4CACQ,KAAR,iBACU,QAAV,eACU,OAAV,mBAIM,KAAN,SACM,KAAN,2BACM,KAAN,cACM,KAAN,uBACQ,KAAR,cACQ,KAAR,yBACA,IACQ,KAAR,cACQ,KAAR,0BC3FqM,I,UCMtL,SAASI,EACtBC,EACAnC,EACAU,EACA0B,EACAC,EACAC,EACAC,EACAC,GAGA,IAqBIC,EArBAC,EAAmC,oBAAlBP,EACjBA,EAAcO,QACdP,EAsDJ,GAnDInC,IACF0C,EAAQ1C,OAASA,EACjB0C,EAAQhC,gBAAkBA,EAC1BgC,EAAQC,WAAY,GAIlBP,IACFM,EAAQE,YAAa,GAInBN,IACFI,EAAQG,SAAW,UAAYP,GAI7BC,GACFE,EAAO,SAAUK,GAEfA,EACEA,GACCpH,KAAKqH,QAAUrH,KAAKqH,OAAOC,YAC3BtH,KAAKuH,QAAUvH,KAAKuH,OAAOF,QAAUrH,KAAKuH,OAAOF,OAAOC,WAEtDF,GAA0C,qBAAxBI,sBACrBJ,EAAUI,qBAGRb,GACFA,EAAapG,KAAKP,KAAMoH,GAGtBA,GAAWA,EAAQK,uBACrBL,EAAQK,sBAAsBC,IAAIb,IAKtCG,EAAQW,aAAeZ,GACdJ,IACTI,EAAOD,EACH,WACAH,EAAapG,KACXP,MACCgH,EAAQE,WAAalH,KAAKuH,OAASvH,MAAM4H,MAAMC,SAASC,aAG3DnB,GAGFI,EACF,GAAIC,EAAQE,WAAY,CAGtBF,EAAQe,cAAgBhB,EAExB,IAAIiB,EAAiBhB,EAAQ1C,OAC7B0C,EAAQ1C,OAAS,SAAmC2D,EAAGb,GAErD,OADAL,EAAKxG,KAAK6G,GACHY,EAAeC,EAAGb,QAEtB,CAEL,IAAIc,EAAWlB,EAAQmB,aACvBnB,EAAQmB,aAAeD,EACnB,GAAGE,OAAOF,EAAUnB,GACpB,CAACA,GAIT,MAAO,CACLpH,QAAS8G,EACTO,QAASA,GCvFb,IAAIqB,EAAY,EACd,EACA/D,EACAU,GACA,EACA,KACA,WACA,MAIa,EAAAqD,E,QCdf,EAASC,QAAU,SAASC,GAC1BA,EAAIF,UAAU,EAAS1H,KAAM,IAGhB,QCFf,MAAM6H,EAAa,CACjB,GAGIF,EAAU,SAAUC,EAAKE,EAAO,IACpCD,EAAWE,QAAQL,IACbC,EAAQK,WACZJ,EAAIF,UAAUA,EAAU1H,KAAM0H,MAIZ,qBAAXjE,QAA0BA,OAAOmE,KAC1CD,EAAQlE,OAAOmE,KAGF,OACbK,QAAS,QACTN,UACAO,SAAA,GCvBa", "file": "vue-cup-ui.umd.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"vue-cup-ui\"] = factory();\n\telse\n\t\troot[\"vue-cup-ui\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"fb15\");\n", "// addapted from the document.currentScript polyfill by <PERSON>\n// MIT license\n// source: https://github.com/amiller-gh/currentScript-polyfill\n\n// added support for Firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1620505\n\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    define([], factory);\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = factory();\n  } else {\n    root.getCurrentScript = factory();\n  }\n}(typeof self !== 'undefined' ? self : this, function () {\n  function getCurrentScript () {\n    var descriptor = Object.getOwnPropertyDescriptor(document, 'currentScript')\n    // for chrome\n    if (!descriptor && 'currentScript' in document && document.currentScript) {\n      return document.currentScript\n    }\n\n    // for other browsers with native support for currentScript\n    if (descriptor && descriptor.get !== getCurrentScript && document.currentScript) {\n      return document.currentScript\n    }\n  \n    // IE 8-10 support script readyState\n    // IE 11+ & Firefox support stack trace\n    try {\n      throw new Error();\n    }\n    catch (err) {\n      // Find the second match for the \"at\" string to get file src url from stack.\n      var ieStackRegExp = /.*at [^(]*\\((.*):(.+):(.+)\\)$/ig,\n        ffStackRegExp = /@([^@]*):(\\d+):(\\d+)\\s*$/ig,\n        stackDetails = ieStackRegExp.exec(err.stack) || ffStackRegExp.exec(err.stack),\n        scriptLocation = (stackDetails && stackDetails[1]) || false,\n        line = (stackDetails && stackDetails[2]) || false,\n        currentLocation = document.location.href.replace(document.location.hash, ''),\n        pageSource,\n        inlineScriptSourceRegExp,\n        inlineScriptSource,\n        scripts = document.getElementsByTagName('script'); // Live NodeList collection\n  \n      if (scriptLocation === currentLocation) {\n        pageSource = document.documentElement.outerHTML;\n        inlineScriptSourceRegExp = new RegExp('(?:[^\\\\n]+?\\\\n){0,' + (line - 2) + '}[^<]*<script>([\\\\d\\\\D]*?)<\\\\/script>[\\\\d\\\\D]*', 'i');\n        inlineScriptSource = pageSource.replace(inlineScriptSourceRegExp, '$1').trim();\n      }\n  \n      for (var i = 0; i < scripts.length; i++) {\n        // If ready state is interactive, return the script tag\n        if (scripts[i].readyState === 'interactive') {\n          return scripts[i];\n        }\n  \n        // If src matches, return the script tag\n        if (scripts[i].src === scriptLocation) {\n          return scripts[i];\n        }\n  \n        // If inline source matches, return the script tag\n        if (\n          scriptLocation === currentLocation &&\n          scripts[i].innerHTML &&\n          scripts[i].innerHTML.trim() === inlineScriptSource\n        ) {\n          return scripts[i];\n        }\n      }\n  \n      // If no match, return null\n      return null;\n    }\n  };\n\n  return getCurrentScript\n}));\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"", "// This file is imported into lib/wc client bundles.\n\nif (typeof window !== 'undefined') {\n  var currentScript = window.document.currentScript\n  if (process.env.NEED_CURRENTSCRIPT_POLYFILL) {\n    var getCurrentScript = require('@soda/get-current-script')\n    currentScript = getCurrentScript()\n\n    // for backward compatibility, because previously we directly included the polyfill\n    if (!('currentScript' in document)) {\n      Object.defineProperty(document, 'currentScript', { get: getCurrentScript })\n    }\n  }\n\n  var src = currentScript && currentScript.src.match(/(.+\\/)[^/]+\\.js(\\?.*)?$/)\n  if (src) {\n    __webpack_public_path__ = src[1] // eslint-disable-line\n  }\n}\n\n// Indicate to webpack that this file can be concatenated\nexport default null\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('button',{staticClass:\"up-button\",on:{\"click\":_vm.handleClick}},[_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/**\n * Created on 2020/9/3.\n */\n\nconst _agent = navigator.userAgent.toLowerCase();\n\nfunction isFunction(value) {\n    return Object.prototype.toString.call(value) === '[object Function]'\n}\n\nfunction truncatedRespForJSBridge(data) {\n  if (!data) {\n    return data;\n  } else if (data.resultString) {\n    return data.resultString;\n  } else if (data.resultParams) {\n    return data.resultParams;\n  }\n}\n\nexport const isJSBridgeEnv = /\\(securitywebcache\\s([\\d\\.]+)\\)/g.test(_agent);\nexport const isCordovaEnv = /\\(cordova\\s([\\d\\.]+)\\)/g.test(_agent);\n\nexport function callNative(success, fail, plugin, action, paramArray) {\n    let iCanCall = false;\n    if (isCordovaEnv && window.cordova) {\n        iCanCall = true;\n        window.cordova.exec(success, fail, plugin, action, paramArray);\n    } else if (isJSBridgeEnv && window.WebViewJavascriptBridge) {\n        iCanCall = true;\n        window.WebViewJavascriptBridge.callHandler(plugin, action, paramArray, function (data) {\n            isFunction(success) && success(truncatedRespForJSBridge(data))\n        }, function (err) {\n            isFunction(fail) && fail(truncatedRespForJSBridge(err))\n        })\n    }\n    return iCanCall;\n}\n", "<template>\n  <button class=\"up-button\"\n          @click=\"handleClick\">\n    <slot></slot>\n  </button>\n</template>\n\n<script>\n  import {callNative} from \"../../common/hybrid\";\n\n  export default {\n    name: \"UPButton\",\n    props: {\n      scope: String,\n      cardRule:String,\n      timeout: Number\n    },\n    methods: {\n      appletExplicitAuth(success, fail) {\n        let iCanCall = callNative(function (data) {\n          if (typeof success === 'function') {\n            success(data);\n          }\n        }, function (err) {\n          if (typeof fail === 'function') {\n            let cordovaError;\n            if (window.cordova) {\n              switch (window.cordova.errorRetStatus) {\n                case window.cordova.callbackStatus.INVALID_ACTION:\n                  cordovaError = {\n                    errcode: 'c03',\n                    errmsg: 'INVALID_ACTION_EXCEPTION: 插件里面没有此方法！'\n                  }\n                  break;\n\n                case window.cordova.callbackStatus.CLASS_NOT_FOUND_EXCEPTION:\n                  cordovaError = {\n                    errcode: 'c04',\n                    errmsg: 'CLASS_NOT_FOUND_EXCEPTION: 此插件没有实现！'\n                  }\n                  break;\n\n                case window.cordova.callbackStatus.ILLEGAL_ACCESS_EXCEPTION:\n                  cordovaError = {\n                    errcode: 'c02',\n                    errmsg: 'ILLEGAL_ACCESS_EXCEPTION: 无权限访问此插件！'\n                  }\n                  break;\n                default:\n                  break;\n              }\n            }\n            if (cordovaError) {\n              fail(cordovaError)\n            } else {\n              fail(err);\n            }\n          }\n        }, 'UPWebSdk', 'appletExplicitAuth', [{scope: this.scope,cardRule: this.cardRule || ''}]);\n        if (!iCanCall) {\n          setTimeout(() => {\n            this._count++;\n            if (this._count > (this._timeout / 20)) {\n              console.warn('请确定是否运行在云闪付APP中,且成功加载了upsdk.js');\n              fail({\n                errcode: '__ENV__10001',\n                errmsg: '检测到未在云闪付APP中运行或未成功加载upsdk.js'\n              })\n            } else {\n              this.appletExplicitAuth(success, fail)\n            }\n          }, 20)\n        }\n      },\n      handleClick(event) {\n        if (this.btnDisable) return;\n        if (this.timeout && isNaN(parseInt(this.timeout))) {\n          this.$emit('click', event, {\n            errcode: '__ENV__10002',\n            errmsg: '检测到timeout值非法'\n          });\n          return;\n        }\n        this._count = 0;\n        this._timeout = this.timeout || 2000; // 默认2s超时\n        this.btnDisable = true; // 防止多次点击，直到回调中才释放\n        this.appletExplicitAuth(data => {\n          this.btnDisable = false;\n          this.$emit('click', event, null, data);\n        }, err => {\n          this.btnDisable = false;\n          this.$emit('click', event, err);\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .up-button {\n    background: #5A98D2;\n    border-radius: 9px;\n    width: 62px;\n    height: 24px;\n    border: none;\n    font-size: 12px;\n    color: #FFFFFF;\n    letter-spacing: 1px;\n    text-align: center;\n    line-height: 24px;\n    font-weight: bold;\n    font-family: PingFangSC-Semibold;\n  }\n\n  .up-button:focus {\n    outline: none;\n  }\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./button.vue?vue&type=script&lang=js&\"", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n        injectStyles.call(\n          this,\n          (options.functional ? this.parent : this).$root.$options.shadowRoot\n        )\n      }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "import { render, staticRenderFns } from \"./button.vue?vue&type=template&id=5cf67f49&scoped=true&\"\nimport script from \"./button.vue?vue&type=script&lang=js&\"\nexport * from \"./button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./button.vue?vue&type=style&index=0&id=5cf67f49&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5cf67f49\",\n  null\n  \n)\n\nexport default component.exports", "/**\n * Created on 2020/6/22.\n */\nimport UPButton from './src/button';\n\nUPButton.install = function(Vue) {\n  Vue.component(UPButton.name, UPButton);\n};\n\nexport default UPButton;\n", "/**\n * Created on 2020/6/22.\n */\n// import './common/resize.js';\nimport './common/common.less';\nimport UPButton from '../packages/button/index.js';\n\nconst components = [\n  UPButton\n]\n\nconst install = function (Vue, opts = {}) {\n  components.forEach(component => {\n    if (install.installed) return\n    Vue.component(component.name, component)\n  })\n}\n\nif (typeof window !== 'undefined' && window.Vue) {\n  install(window.Vue);\n}\n\nexport default {\n  version: '0.1.0',\n  install,\n  UPButton\n}\n", "import './setPublicPath'\nimport mod from '~entry'\nexport default mod\nexport * from '~entry'\n"], "sourceRoot": ""}