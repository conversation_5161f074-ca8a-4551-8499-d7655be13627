import { plateList } from '../../v3-http/https3/vehicle/index';

export default {
    state: {
        // 车辆列表信息 (选中在前)
        carListV3: [],
        // 车辆列表信息 (未作处理)
        defaultCarListV3: [],
    },
    mutations: {
        // 设置车辆列表信息 (选中在前)
        setCarListV3(state, list) {
            state.carListV3 = list;
        },
        // 设置车辆列表信息 (未作处理)
        setDefaultCarListV3(state, list) {
            state.defaultCarListV3 = list;
        },
    },
    actions: {
        // 更新车辆列表
        async uploadCarListV3({ state, commit, dispatch }) {
            let res = await plateList();
            let vehicleList = res.data.vehicleList || [];
            // 设置车辆列表信息
            commit('setDefaultCarListV3', vehicleList);
            if (res.success && vehicleList.length > 0) {
                // 遍历车辆如果存在默认车辆就取默认车辆 如果不存在取车辆列表的第一项
                vehicleList.map((item, index) => {
                    if (item.defaultVehicle) {
                        commit('setCarListV3', [item]);
                    } else if (index === 0) {
                        commit('setCarListV3', [vehicleList[0]]);
                    }
                });
            } else {
                commit('setCarListV3', []);
            }
        },
    },
};
