export default {
  mounted() {
  },
  methods: {
      /**
       * @description : 唤起密码键盘支付
       * paras :  bizOrderNo 是 业务订单编号
       * paras :  stationCode 是 站编码
       * paras :  amount 是 需要冻结的金额
       * paras :  extendFiled 是 风控字段（json字符串透传，不校验里面内容）
       * paras :  AccountPreType 是 账户预支付场景：1.扫码支付；2.室外支付；3.支付预授权   0319 约定改为string类型
       * paras :  payType 支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；6：加油卡；7：信用账户；8：云闪付；9：和包支付；10：翼支付；11：优惠券；12：礼品卡；13：油币；14：能源币；15：积分；29：现金；)
       * @return  {*}
       */
      async preLicensingPlaceAnOrder(dataInfo, isAuth = false) {
          let paras = {
              areaCode: this.walletInfo.addressNo,
              bizOrderNo: dataInfo.preAuthzOrderNo,
              stationCode: this.selectMarkerV3.orgCode,
              extendFiled: JSON.stringify({
                  dfp: '',
                  gps:
                      this.riskManagementLonV3 && this.riskManagementLatV3
                          ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                          : '',
                  gpsProvince: '',
                  gpsCity: '',
                  gpsArea: '',
              }),
              accountPreType: '3',
              amount: this.preaAuthNumber,
              accountNo: this.walletInfo.ewalletNo,
              lockAll:0
          };
          this.dataInfo = dataInfo;
          // let obj = await this.$sKit.commonUtil.biometricPay(dataInfo.preAuthzOrderNo, dataInfo.preAuthzAmount, !isAuth);
          // this.$refs.loadingFlag.close()
          uni.showLoading();
          // Object.assign(paras);
          console.log('成功', paras);

          this.$cnpcBridge.setValueToNative('Define_PreAuth_STATUES',this.isPreaAuthing);
          
          let accountDataPlugin = this.accountDataPlugin
          console.log('accountDataPlugin',accountDataPlugin)
          await this.$sKit.mpaasPayPlugin.QryPreOrder(paras, this.accountDataPlugin).then(res => {
              console.log(res, '插件加油预下单');
              setTimeout(() => {
                  this.isPreaAuthing = false;
                  this.$cnpcBridge.setValueToNative('Define_PreAuth_STATUES',this.isPreaAuthing);
              }, 500);
              // PAY_ERROR_004 新增错误码，代表不是正在意义上的失败，可能是超时导致的，这种情况处理逻辑跟成功一样，跳转页面查询授权码，查不到再取消订单
              if (res.code === 'PAY_SUCCESS' || res.code === 'PAY_ERROR_004') {
                  // 获取电子钱包金额
                  this.$store.dispatch('getAccountBalanceAction');
                  uni.showLoading({
                      title: '加载中',
                      mask: true,
                  });
                  //成功要跳转
                  let URL = `/packages/third-oil-charge-payment/pages/authorization-code/main`;
                  let type = 'navigateTo';
                  this.$sKit.layer.useRouter(URL, dataInfo, type);
                  this.$cnpcBridge.setValueToNative('Define_PreAuth_Price', this.preaAuthNumber);
                  this.$cnpcBridge.setValueToNative('Define_PreAuth_Fuel', encodeURIComponent(JSON.stringify(this.seletFuel)));
                  uni.hideLoading();
              }else if (res.code === 'PAY_ERROR_003') {
                  //需要实人认证
                  this.$sKit.commonUtil
                      .oilTriggerRisk()
                      .then(
                          res => {
                              // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                              if (res == 13) {
                                  // 打开实人认证的表单弹窗
                                  this.realNameDialogFlag = true;
                              }
                          },
                          () => {
                              this.cancelAction(dataInfo);
                          },
                      )
                      .catch(err => { });
              }else {
                  this.$refs['popDialogFlag'].open();
                  this.oilDialogFlagType = 'cipherDialogFlag';
                  this.confirmText = '确认';
                  this.cancelText = '';
                  let errIndex = res.msg.indexOf(':');
                  let errorCode = '';
                  let customErr = '';
                  if (errIndex !== -1) {
                      this.oilDialogCode = res.msg.slice(0, errIndex);
                      this.oilDialogtitle = res.msg.slice(errIndex + 1, res.msg.length);
                  } else {
                      this.oilDialogtitle = res.msg;
                      this.oilDialogCode = res.code;
                  }
                  this.cancelAction(dataInfo);
              }
          });
          
      },
      // 关闭人脸认证协议弹窗
      enterNavEvent() {
          // 关闭人脸认证协议弹窗
          this.$store.dispatch('changeFacePop', false);
          this.$sKit.commonUtil.nextOilTriggerRisk().then(
              res => {
                  this.preLicensingPlaceAnOrder(this.preOrderData, true);
              },
              () => {
                  this.cancelAction(this.dataInfo);
              },
          );
      },
      cancelClick() {
          this.cancelAction(this.dataInfo);
      },
  },
};
