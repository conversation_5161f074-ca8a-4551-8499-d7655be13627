module.exports = {
    _: '', // * 别名
    name: '', // * 包名
    v2sign: '', // * 微信/支付宝必填 2.0加签key
    v3sign: '', // * 微信/支付宝必填 3.0加签key
    appId: '', // * APP微信必填 APPID
    // * 埋点
    maa: {
        id: '',
        reportURL: 'https://hkyzmsnp.kunlunjyk.com:10443/mdap/loggw/webLog.do',
    },
    subPath: '', // * 分包路径，支付宝打分包必须
    cubeMiniAppId: '3815480475716653', // 小程序跳转小程序缺省用
    mpaasAppId: 'PRI5A96DC5141050', // mPaas appid
    baseUrl: 'https://miniprotest.95504.net',
    baseImgUrl: 'https://oss-dev-app-soti.kunlunjyk.com',
    baseMallUrl: 'https://temall.95504.net/',
    rpcUrl: 'https://hkyznp.kunlunjyk.com:10443/gsms/mgw.htm',
    secretKey: "7f78842bc10f3b03ea1942a3dafc382e",
    baseUrl2App: 'http://jlapp.95504test.com',
    baseUrl2Al: 'https://miniappkfnp.kunlunjyk.com',
    splicBaseUrl: 'https://jlapp.95504test.com:9080',
    fpUrl: 'https://sitrms.95504.net', // 设备指纹服务器地址 生产
    mainPackages: [],
    subPackages: [],
    baseType: 'sit',
    workspaceid: 'sit',
    api: '/E/', // 支付宝2.0 测试 /E/ 生产 /E/ 灰度 /gray/
    apiGsms: 'gsms', // 支付宝2.0并行 测试 gsms/ 生产 gsms-a/ gsms-b/
    mixRecharge: 0.01, // 充值最低限制金额（单位元），不配置默认为 1 元
    maxMinute: 10000, // 后支付最大查询订单时间（单位分钟），不配置默认为 10 分钟
    maxDistance: 0.5, // 最大加油距离限制（单位千米），不配置默认为 0.5 千米
    minVersion: '3.6.1', // 权限版本号
    ccbShareUrl: 'https://www.ccbjrxd.cn/plx/page/internet/cncrmpcst/newCstWxWork.html', //建行添加直营经理分享链接
    encryptionKey3DES: '001gao2yang3jie4xun5ke6ji7yang81', // 3DESkey
    harmonyOSRiskControlPublicKey:
        '04f5a10593348801f8aad282c8e1a2c5b9923a63f78c312716730ee4e0313c39206a07b6194bf9bfdc17199d99376ecaea8dcc49bacb59e527b0eaed664ddf4db8', // 鸿蒙风控公钥
    secretKey: '7f78842bc10f3b03ea1942a3dafc382e',
    app: {
        globalStyle: {
            navigationStyle: 'custom',
            navigationBarTextStyle: 'white',
            backgroundTextStyle: 'dark',
            navigationBarBackgroundColor: '#fff',
            backgroundColor: '#F7F7FB',
            pullRefresh: false,
            defaultTitle: '',
            allowsBounceVertical: 'NO',
            transparentTitle: 'always',
            titlePenetrate: 'YES',
        },
        easycom: {
            'c-checkbox': '@/s-kit/components/layout/c-checkbox/c-checkbox.vue',
            'zj-old-account': '@/s-kit/components/layout/zj-old-account/index.vue',
            'zj-navbar': '@/s-kit/components/layout/zj-navbar/zj-navbar.vue',
            'zj-pull-down-refresh': '@/s-kit/components/layout/zj-pull-down-refresh/zj-pull-down-refresh.vue',
            'zj-data-list': '@/s-kit/components/layout/zj-data-list/zj-data-list.vue',
            'zj-no-data': '@/s-kit/components/layout/zj-no-data/index.vue',
            'zj-show-modal': '@/s-kit/components/layout/zj-show-modal/index.vue',
            'zj-unrealized-authentication': '@/s-kit/components/layout/zj-unrealized-authentication/index.vue',
            'zj-agreement': '@/s-kit/components/layout/zj-agreement/zj-agreement.vue',
            'zj-marketing-coupon': '@/s-kit/components/layout/zj-marketing-coupon/zj-marketing-coupon.vue',
            'zj-popup': '@/s-kit/components/layout/uni-popup/uni-popup.vue',
            'custom-popup': '@/s-kit/components/layout/zj-show-modal/custom-popup.vue',
            '^zj-(.*)': '@/s-kit/components/layout/zj-$1/zj-$1.vue',
            '^u-(.*)': 'uview-ui/components/u-$1/u-$1.vue',
        },
        permission: {
            'scope.userLocation': {
                desc: '您的位置信息将用于获取附近加油站',
            },
        },
        wxplugins: {
            'security-utils-plugin': {
                version: '5.2.1',
                provider: 'wx64166bb823f78696',
            },
            'pay-plugin': {
                version: '5.2.0',
                provider: 'wx228ef1dc0a12d490',
            },
        },
        zfbplugins: {
            securityPlugin: {
                version: '*',
                provider: '2021003176627545',
            },
            payPlugin: {
                version: '*',
                provider: '2021003173659887',
            },
            ecardPlugin: {
                version: '*',
                provider: '2021002177693516',
            },
        },
        wxSubPlugins: {
            sendCoupon: {
                sendCoupon: {
                    version: 'latest',
                    provider: 'wxf3f436ba9bd4be7b',
                },
            },
            wxInvoice: {
                'wx-invoice': {
                    version: 'latest',
                    provider: 'wx9db2c16d0633c2e7',
                },
            },
        },
    },
};
