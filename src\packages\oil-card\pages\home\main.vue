<template>
    <div class="oil-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :custom-back="clickCustomBackBtn"
            back-text="油卡充值"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="oil-top">
            <div class="top-div">
                <!-- <div class="card-top">
                    <div class="card-tip">我的加油卡 共{{ oilcardList.length }}张</div>
                    <div class="card-tip" v-if="oilCardInfo">
                        <u-icon name="arrow-left" size="24"></u-icon>
                        左滑查看其他
                    </div>
        </div>-->
                <div class="top-section">
                    <template>
                        <swiper
                            class="swiper"
                            :indicator-dots="indicatorDots"
                            :autoplay="autoplay"
                            :interval="interval"
                            :duration="300"
                            :current="swiperIndex"
                            @change="handleChangeSwiperItem"
                        >
                            <swiper-item v-for="(item, index) in oilcardList" :key="index">
                                <div
                                    class="item"
                                    :style="{
                                        backgroundImage:
                                            item.cardTypeImg && item.cardType == 1
                                                ? `url(${item.cardTypeImg})`
                                                : 'url(' + (item.cardType == 0 ? naviBgshitika : naviBg) + ')',
                                        borderRadius: item.cardTypeImg && item.cardType == 1 ? '8px' : '',
                                    }"
                                >
                                    <div class="header-div">
                                        <div class="name-div" @click="eyebtn($event, index)">
                                            <span class="name" v-if="!item.activeed">
                                                {{ item.userNameShow }}
                                            </span>
                                            <span v-else class="name">{{ item.userName }}</span>
                                            <span class="card-number" v-if="!item.activeed">
                                                {{ item.cardNoShow }}
                                            </span>
                                            <span v-else class="card-number">{{ item.cardNo }}</span>
                                            <!-- <img
                        :src="
                          !item.activeed
                            ? '/static/white-eye-close.png'
                            : '/static/white-eye-open.png'
                        "
                        alt
                        class="eye-block-iocn1"
                        mode="widthFix"
                        :data-item="item"
                      />-->
                                            <img
                                                src="@/static/white-eye-open.png"
                                                v-if="item.activeed"
                                                alt
                                                class="eye-block-iocn1"
                                                mode="widthFix"
                                                :data-item="item"
                                            />
                                            <img
                                                src="@/static/white-eye-close.png"
                                                v-else
                                                alt
                                                class="eye-block-iocn1"
                                                mode="widthFix"
                                                :data-item="item"
                                            />
                                        </div>
                                        <div class="address-text">{{ item.allAddress }}</div>
                                        <div class="bottom-div">
                                            <div class="flex-row">
                                                <div class="flex-item" v-if="item.cardType == 0">
                                                    <div class="row-div">
                                                        <span class="row-left">圈存金</span>
                                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                        <template v-else>
                                                            <span class="row-price" v-if="item.activeed">¥</span>
                                                            <span class="row-text">
                                                                {{ item.cardBalance }}
                                                            </span>
                                                        </template>
                                                    </div>
                                                    <div class="row-div">
                                                        <span class="row-left">积分</span>
                                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                        <span class="row-text" style="padding-left: 8px" v-else>{{
                                                            item.cardLoyaltyBalance
                                                        }}</span>
                                                    </div>
                                                </div>
                                                <div class="flex-item">
                                                    <div class="row-div">
                                                        <span class="row-left">备用金</span>
                                                        <span class="row-price" v-if="item.activeed">¥</span>
                                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                        <span class="row-text" v-else>
                                                            {{ item.balance }}
                                                        </span>
                                                    </div>
                                                    <div class="row-div">
                                                        <span class="row-left">积分</span>
                                                        <span class="row-text row-text-center" v-if="!item.activeed">***</span>
                                                        <span class="row-text" style="padding-left: 8px" v-else>{{
                                                            item.loyaltyBalance
                                                        }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row-div">
                                                <span class="row-left" v-if="item.cardExpiredTime"
                                                    >有效期至：{{ item.cardExpiredTime }}</span
                                                >
                                            </div>
                                        </div>
                                    </div>

                                    <!-- <div class="header-info"> -->
                                    <!-- <div class="info-left"> -->
                                    <!-- <img src="../../static/cnpc-logo.png" alt="" mode="widthFix"> -->
                                    <!-- <span v-if="!item.activeed">{{ item.cardNoShow }}</span>
                  <span v-else>{{ item.cardNo }}</span>-->
                                    <!-- <div class="info-left-btn">设为常用</div> -->
                                    <!-- <span class="cardType">{{item.cardType == 0 ? '实体卡' : '电子卡'}}</span> -->
                                    <!-- <img :src="!item.activeed ?  '../../static/eye-none.png':'../../static/eye-block.png'"
                                    alt="" class="eye-block-iocn1" mode="widthFix" :data-item="item"
                  @click="eyebtn($event,index)" />-->
                                    <!-- </div> -->
                                    <!-- <div class="info-right">{{ item.detailObj.proName }}</div> -->
                                    <!-- </div> -->
                                    <!-- <div class="center">
                                        <img src=""
                                            alt="" class="eye-block-iocn" mode="widthFix" />
                                        <div class="money-div">
                                            <div>备用金</div>
                                            <div class="money-text" v-if="!item.activeed">***</div>
                                            <div class="money-text" v-else><span
                                                    class="money-icon">¥</span>{{ item.detailObj.balance }}</div>
                                        </div>
                                        <div class="money-div">
                                            <div>卡余额</div>
                                            <div class="money-text" v-if="!item.activeed">***</div>
                                            <div class="money-text" v-else><span
                                                    class="money-icon">¥</span>{{ item.detailObj.cardBalance ? item.detailObj.cardBalance : '0.00' }}
                                            </div>
                                        </div>
                                        <div style='flex: 0.2;'></div>
                                        <div v-show='item.activeed'>
                                            <div class='money-upload' @click='clickUploadCard(index)'>
                                                {{!item.isStartDown ? '刷新' : ''}}
                                                <u-count-down ref='upLoadDown' @end='downEnd(index)' :autoplay='false'
                                                    v-show='item.isStartDown' bg-color='#FFF6EC' color='#FF8200'
                                                    :timestamp="15" :show-days="false" :show-hours="false"
                                                    :show-minutes='false'></u-count-down>
                                                {{item.isStartDown ? 's' : ''}}
                                            </div>
                                        </div>
                  </div>-->
                                    <!-- <div class="footer">
                                        <div class="info-btn" :class="{'active-btn':oilCardInfo}"
                                            @click="oilcardtap($event,0)" :data-item="item">充值明细
                                        </div>
                                        <div class="info-btn" :class="{'active-btn':oilCardInfo}"
                                            @click="oilcardtap($event,1)" :data-item="item">消费明细
                                        </div>
                                        <div class="info-btn" :class="{'error-btn':oilCardInfo}"
                                            @click="unoilcardtap($event,index)" :data-item="item">解除绑定
                                        </div>
                  </div>-->
                                </div>
                            </swiper-item>
                            <!-- <swiper-item> -->
                            <!-- <div class="item"> -->
                            <!-- <div class="header" v-if="isHaveSTcard">只能添加一张实体卡哦</div>
                            <div class="header" v-else @click="addCardtap">
                                <span class="add-icon"><img src="../../static/add-icon.png" alt=""
                                        mode="widthFix"></span>
                                <span class="add-txt">添加油卡</span>
              </div>-->
                            <!-- <div class="center">
            <img src="../../static/eye-none.png" alt="" class="eye-block-iocn" mode="widthFix">
            <div class="money-div">
              <div>备用金</div>
              <div class="money-text" v-if="eyeBlock">***</div>
            </div>
            <div class="money-div">
              <div>卡余额</div>
              <div class="money-text" v-if="eyeBlock">***</div>
            </div>
              </div>-->
                            <!-- <div class="footer">
            <div class="info-btn">充值明细</div>
            <div class="info-btn">消费明细</div>
            <div class="info-btn">充值发票</div>
            <div class="info-btn">解除绑定</div>
              </div>-->
                            <!-- </div>
              </swiper-item>-->
                        </swiper>
                    </template>
                </div>
            </div>
            <!-- <div class="opacity-div"></div> -->
        </div>

        <div class="oil-section">
            <div class="oil-wrap">
                <div class="pay-title-view">
                    <div class="pay-title">充值金额</div>
                    <!-- <div class="pay-pro" @click='clickCJWT'>常见问题</div> -->
                </div>
                <div class="pay-input">
                    <div class="pay-icon">¥</div>
                    <input
                        @click="selectpay($event, oilMoneyList[5], true)"
                        id="payInput"
                        :focus="isInputFoucs"
                        type="number"
                        @blur="blur"
                        placeholder="请输入不小于1的金额"
                        class="div-box-input"
                        v-model="oilMoneyInfo"
                        maxlength="4"
                    />
                </div>

                <div class="section-div">
                    <div
                        class="div-box"
                        v-for="(item, index) in oilMoneyList"
                        :key="index"
                        :class="{ 'div-select-box': item.isChecked }"
                        @click="selectpay($event, item)"
                        :data-item="item"
                        >¥{{ item.oilMoney }}</div
                    >
                    <!-- <div v-if="oilCardInfo">
                        <input @click="selectpay($event, 5)" :data-item="{oilMoney: 0}"
            focus="auto" bindinput='bindInputMsg' bindblur="onHideInput" confirm-type="send" bindconfirm="sendTextMsg"
                            :class="{'div-select-box': 5 == checkedIndex}" type="number" placeholder="其他金额" class="div-box"
                              readonly  @input="otherInput" maxlength="12">
                    </div>
          <div class="div-box" v-else>其他金额</div>-->
                </div>
                <div class="pay-tip" v-if="false">
                    <!-- <img src="" alt=""> -->
                    <u-icon name="info-circle"></u-icon>
                    <span>充值后可前往充值记录开具电子发票</span>
                </div>
            </div>
        </div>
        <!-- <div class="oil-kefu1">
      3个月内的线上充值可在线上开具发票,超过3个月的可到线下网点开票
    </div>-->
        <div>
            <div class="horn-text-wrap">
                <img class="horn-img" src="@/static/horn.png" alt />
                <div class="horn-text">温馨提示：</div>
            </div>

            <div class="oil-prompt">
                <span>· 以“70”开头的卡，充值不提供发票，消费后开具电子发票；</span>
                <br />
                <span>· 以“90”开头及其他卡，您可按办理加油卡时的开票类型开具电子发票；</span>
            </div>
        </div>
        <div class="oil-kefu">客服热线 956100</div>
        <div class="oil-footer">
            <div class="agreement">
                <div class="checkbox">
                    <!-- <u-checkbox v-model="agreementchecked" shape="circle" active-color="#FF8200">
                        <div class="agreement-txt">我已看过并接受 <span @click.stop='clickXieyi'
                                class="oil-money-info">《中国石油昆仑加油卡客户网上充值协议》</span>
                        </div>
          </u-checkbox>-->
                    <u-checkbox v-model="agreementchecked" shape="circle" active-color="#FF8200">
                        <div class="agreement-txt">
                            同意
                            <span @click.stop="clickXieyi" class="oil-money-info">《中国石油服务协议》</span>
                        </div>
                    </u-checkbox>
                </div>
                <!--        <div class="agreement-txt">我已看过并接受 <span :class="{'oil-money-info':oilCardInfo}">《中国石油昆仑加油卡客户网上充值协议》</span>-->
                <!--        </div>-->
            </div>
            <div class="oil-btn">
                <!-- <div class="oil-money" :class="{'oil-money-info':oilCardInfo && oilMoneyInfo}">¥<span
                        class="oil-money-bold">{{ oilMoneyInfo }}</span><span v-if='isInteger(oilMoneyInfo)'>.00</span>
        </div>-->
                <!-- <div class="oli-paybtn-gray" @click='clickPay' :class="{'oli-pay-info':oilCardInfo && oilMoneyInfo}">
        立即充值</div>-->
                <div class="oli-paybtn-gray" @click="isUpgrade" :class="{ 'oli-pay-info': oilCardInfo }">确认充值</div>
            </div>
        </div>
        <!-- 重置成功 -->
        <uni-pop ref="sucpopup" type="center" :maskClick="false">
            <div class="suc-pop-view">
                <div class="suc-pop-content">
                    <img class="suc-pop-icon" src="@/static/homeIcon/success-pay.png" />
                    <div class="suc-pop-title">充值成功, 请刷新查看</div>
                </div>
                <u-icon
                    @click="clickCloseSucPop"
                    name="close-circle-fill"
                    :custom-style="{
                        marginTop: '40rpx',
                    }"
                    color="#ffffff"
                    size="92"
                ></u-icon>
            </div>
        </uni-pop>
        <ad-view v-if="isShowContract" @closeEvent="closeEvent" @enterNavEvent="enterNavEvent"></ad-view>
        <float-img-btn v-if="showFloatImg" @onFloatBtnClicked="onFloatBtnClicked"></float-img-btn>

        <div class="popupMask" v-show="popupShow"></div>
        <div class="popup" v-show="popupShow">
            <p>订单查询中</p>
            <div class="countDown">
                <span>{{ nums }}</span
                >s
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
        <ZjNewStation
            class="new_station"
            @close="closeNewStationDialog"
            @submit="submitUpgrade"
            v-if="newStationFlag"
            :isUpgradeFlag="isUpgradeFlag"
        ></ZjNewStation>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import ZjNewStation from '@/components/zj-new-station/index.vue';
import { mapGetters, mapState } from 'vuex';
import {
    paycard,
    // contract, // 优惠合同
    // getCardDetails
    getCardDetailApi,
    unBinding,
    cardListPost,
    getContractDisInfo,
    getContractRuleInfo,
} from '@/api/home.js';
import uniPop from '@/components/uni-popup/uni-popup.vue';
import adView from './../../component/ad-view/ad-view.vue';
import FloatImgBtn from './../../component/float-img/FloatImgBtn.vue';
import projectConfig, { maxDistance } from '../../../../../project.config';
let timer = -1;
export default {
    name: 'main',
    components: {
        uniPop,
        adView,
        FloatImgBtn,
        ZjNewStation,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            projectConfig,
            agreementchecked: false,
            oilcardList: [], //油卡数组信息
            oilCardInfo: false, // 是否添加油卡
            isHaveSTcard: false, //判断油卡列表中是否有实体卡，默认没有
            oilMoneyList: [
                {
                    isChecked: false,
                    oilMoney: 200,
                },
                {
                    isChecked: false,
                    oilMoney: 500,
                },
                {
                    isChecked: false,
                    oilMoney: 1000,
                },
                {
                    isChecked: false,
                    oilMoney: 1500,
                },
                {
                    isChecked: false,
                    oilMoney: 2000,
                },
                {
                    isChecked: true,
                    oilMoney: '其他金额',
                },
            ],
            checkedIndex: 0,
            otherMoney: '',
            eyeBlock: false,
            indicatorDots: false,
            autoplay: false,
            detailObj: {},
            oilMoneyInfo: '',
            swiperIndex: 0,
            naviBg: '',
            naviBgshitika: '',
            preferentialContract: false, // 是否有优惠合同
            showBtn: false,
            isShowContract: false,
            showFloatImg: false,
            // naviBgshitika:'',
            orderNo: '',
            tradeId: '',
            businessDate: '',
            amount: '',
            htInfo: null,
            isShow: true,
            showFloatImg: false,
            // naviBgshitika:'',
            isInputFoucs: false,
            popupShow: false,
            nums: '',
            isUpgradeFlag: 'ykcz',
        };
    },
    onLoad(options) {
        this.submitUpgrade = this.$util.throttleUtil(this.submitUpgrade);
        this.loadImages();
        this.isUpgrade = this.$util.throttleUtil(this.isUpgrade);
        // this.eyebtn = this.$util.throttleUtil(this.eyebtn);
        // this.oilMoneyInfo = this.oilMoneyList[this.checkedIndex].oilMoney
        this.agreementchecked = this.$Storage.isAgreePay.value;
        let index = options.swiperIndex ? options.swiperIndex : 0;
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/dianzikabg.png', 'base64');
        // this.naviBgshitika = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/shitikabg.png', "base64")
        // this.getCardList()
        this.oilcardList = [this.cardList[index]];
        // console.log(this.cardList, this.oilcardList, '油卡数据')
        // this.swiperIndex = 0;
        this.swiperIndex = index; // 获取上一级页面携带的index
        this.oilCardInfo = this.oilcardList.length > 0;

        // uni.showModal({
        // 	content: '最新的优惠合同已到达，请您前往“合同变更”进行办理',
        // 	confirmColor: '#FF8200',
        // 	// showCancel: false,
        // 	cancelText:"取消",
        // 	confirmText: "立即办理",
        // 	success: () => {
        // 		uni.navigateTo({
        // 			url: '/packages/contract/pages/bind-contract/main'
        // 		})
        // 	}
        // })

        // this.getRechargeDiscountInformation({
        // 	cardUserId:this.oilcardList[this.swiperIndex].cardUserId,
        // 	orderNo:'1200022865419' // 动态
        // })
    },
    onUnload() {
        clearInterval(timer);
    },
    watch: {
        agreementchecked: function (newVal, oldVal) {
            this.$Storage.isAgreePay.update(newVal);
        },
    },
    methods: {
        async loadImages() {
            this.naviBg = await this.fetchAndConvertToBase64(this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/dianzikabg.png');
            this.naviBgshitika = await this.fetchAndConvertToBase64(
                this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/shitikabg.png',
            );
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        blur() {
            //收起键盘 input失去焦点
            let select = this.oilMoneyList.find(item => {
                return item.oilMoney == this.oilMoneyInfo;
            });
            if (select) {
                this.oilMoneyList[5].isChecked = false;
                select.isChecked = true;
            }
        },
        oilMoneyListInit() {
            this.oilMoneyList.forEach(info => {
                info.isChecked = false;
            });
        },
        closeEvent() {
            this.isShowContract = false;
            this.showFloatImg = true;
        },
        enterNavEvent() {
            this.isShowContract = false;
            this.showFloatImg = false;
            // let cardUserId = this.oilcardList[this.swiperIndex].cardUserId;
            let cardUserId = this.cardList[this.swiperIndex].cardUserId;
            let obj = encodeURIComponent(JSON.stringify(this.htInfo));
            uni.navigateTo({
                url: `/packages/contract/pages/bind-contract/main?cardUserId=${cardUserId}&tradeId=${this.tradeId}&amount=${this.amount}&businessDate=${this.businessDate}&htInfo=${obj}`,
            });
        },
        onFloatBtnClicked() {
            this.enterNavEvent();
        },
        // current 改变时会触发
        handleChangeSwiperItem(e) {
            const { current, source } = e.detail;
            if (source == 'touch') {
                //用户触摸引起
                this.swiperIndex = current;
            }
        },
        // 点击常见问题
        clickCJWT() {
            uni.navigateTo({
                url: '/packages/setting/pages/agreement/main?value=71',
            });
        },
        // 点击充值协议按钮
        clickXieyi() {
            uni.navigateTo({
                url: '/packages/setting/pages/agreement/main?value=64',
            });
        },
        // 点击开票成功关闭弹窗
        async clickCloseSucPop() {
            this.$store.dispatch('card/uploadCardDetail', this.swiperIndex);
        },
        isInteger(obj) {
            return Number.isInteger(obj);
        },
        // 是否隐藏点击事件
        eyebtn(e, index) {
            console.log(index, 'index');
            this.cardList.findIndex(item => {
                // item.cardNo == this.oilcardList[0].cardNo;
                item.cardNo == this.cardList[this.swiperIndex].cardNo;
            });
            this.$store.dispatch('card/setCardActiveed', this.swiperIndex);
            // this.$store.dispatch("card/setCardActiveed", index);
        },
        selectpay(e, item, isClickInput = false) {
            if (this.oilCardInfo) {
                // if (item.isChecked)
                //     return
                // else {
                this.oilMoneyListInit();
                item.isChecked = true;
                if (item.oilMoney == '其他金额') {
                    if (!isClickInput) {
                        //点击input，金额不做处理，点击其他金额按钮，金额置空
                        if (!this.oilMoneyInfo == '') this.oilMoneyInfo = '';
                        this.isInputFoucs = !this.isInputFoucs;
                    }
                } else this.oilMoneyInfo = item.oilMoney;
            }
        },
        // 其他金额
        otherInput() {
            // let otherMoney = this.otherMoney
            // this.oilMoneyInfo = otherMoney || 0
        },
        // 判断是否升级
        isUpgrade() {
            if (this.showMarkerArr.length > 0) {
                this.$store.dispatch('getNewOilStation', {
                callBack: () => {
                    this.clickPay();
                },
                params: this.markerArr,
            });
            } else {
                this.clickPay();
            }
        },
        // 确认充值
        async clickPay() {
            // this.$store.dispatch("card/setCardActiveed", this.swiperIndex);
            // return
            // if (this.oilcardList.length == 0) {
            //   uni.showToast({
            //     icon: "none",
            //     title: "请先添加油卡",
            //   });
            //   return;
            // }
            if (this.cardList.length == 0) {
                uni.showToast({
                    icon: 'none',
                    title: '请先添加油卡',
                });
                return;
            }
            if (Number(this.oilMoneyInfo) < 1) {
                uni.showToast({
                    icon: 'none',
                    title: '请输入不小于1的金额',
                });
                return;
            }
            if (!this.agreementchecked) {
                uni.showToast({
                    icon: 'none',
                    title: '请先阅读充值协议',
                });
                return;
            }
            const deviceInfo = wx.getSystemInfoSync();
            const deviceInformation = {
                ver: deviceInfo.version,
                phone: this.registerLoginInformation.phone,
                deviceinfo: deviceInfo.system.includes('iOS') ? 'apple' : 'android',
            };
            let res = await paycard({
                // cardNo: this.oilcardList[this.swiperIndex].cardNo,
                cardNo: this.cardList[this.swiperIndex].cardNo,
                amount: this.oilMoneyInfo,
                // cardType: this.oilcardList[this.swiperIndex].cardType,
                cardType: this.cardList[this.swiperIndex].cardType,
                attach: JSON.stringify(deviceInformation),
                // cardType:
            });
            uni.requestPayment({
                provider: 'wxpay',
                timeStamp: res.data.timeStamp,
                nonceStr: res.data.nonceStr,
                package: res.data.prepay,
                signType: res.data.signType,
                paySign: res.data.paySign,
                success: () => {
                    this.nums = 6;
                    timer = setInterval(() => {
                        this.nums--;
                        console.log(this.nums);
                        this.popupShow = true;
                        if (this.nums == 0) {
                            this.popupShow = false;
                            clearInterval(timer);
                            uni.showModal({
                                title: '',
                                content: '充值成功',
                                confirmColor: '#FF8200',
                                showCancel: false,
                                success: () => {
                                    this.clickCloseSucPop();
                                    this.$store.dispatch('card/refresh', this.swiperIndex);
                                    this.getRechargeDiscountInformation({
                                        // cardUserId: this.oilcardList[this.swiperIndex].cardUserId,
                                        cardUserId: this.cardList[this.swiperIndex].cardUserId,
                                        orderNo: res.data.orderNo,
                                    });
                                },
                            });
                        }
                    }, 1000);
                    this.clickCloseSucPop();
                },
                fail: function (err) {},
            });
        },
        async FloatImgBtn() {
            let params = {
                cardNo: this.infoObj.cardNo,
                pageSize: this.pageSize,
                contractTypeId: this.data.contractTypeId,
            };
            let res = await FloatImgBtn(params);
            if (res.status == 0) {
                console.log(res);
            }
        },

        // 获取充值和折扣信息
        async getRechargeDiscountInformation(params) {
            let disInfo = await getContractDisInfo(params);
            this.tradeId = disInfo.data.tradeId; //交易订单号
            this.businessDate = disInfo.data.businessDate; // 业务日期
            this.amount = disInfo.data.amount; // 充值金额
            if (disInfo.status == 0) {
                let ruleInfo = await getContractRuleInfo({
                    // cardUserId: this.oilcardList[this.swiperIndex].cardUserId,
                    cardUserId: this.cardList[this.swiperIndex].cardUserId,
                    newCtNo: disInfo.data.contractTypeId,
                    expireDate: disInfo.data.expiredDate,
                });
                if (ruleInfo.status == 0) {
                    this.htInfo = ruleInfo.data;
                    if (
                        this.htInfo.newCtNo &&
                        this.htInfo.oldCtNo &&
                        this.htInfo.newList &&
                        this.htInfo.newList.length &&
                        (this.htInfo.newCtNo != this.htInfo.oldCtNo ||
                            (this.htInfo.newCtNo == this.htInfo.oldCtNo &&
                                this.htInfo.newCtExpiredTime.replace(/\-/g, '') > this.htInfo.oldCtExpiredTime.replace(/\-/g, '')))
                    ) {
                        this.isShowContract = true;
                    }
                }
            }
        },
        // 关闭升级新站弹窗
        closeNewStationDialog() {
            this.$store.commit('setCloseDialog', false);
            // this.getCouponQrcodeFun()
        },
        // 立即升级
        async submitUpgrade() {
            this.$store.dispatch('getToken3', 'upgrade');
        },
    },
    onShow() {
        // if (this.oilcardList.length > 0) {
        //   this.oilCardInfo = true
        //   this.checkedIndex = "-1"
        // }
    },
    computed: {
        ...mapGetters(['cardList', 'isHaveEntityCard', 'cardTopinfo', 'registerLoginInformation']),
        ...mapState({
            newStationFlag: state => state.location.newStationFlag, // 打开升级弹窗
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            markerArr: state => state.location.markerArr, // marker数据
            showMarkerArr: state => state.location.showMarkerArr, // 展示在地图上的油站列表数组
        }),
    },
    beforeDestroy() {
        this.$store.commit('setCloseDialog', false);
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}

$font14: 14px;
$font15: 15px;

.suc-pop-view {
    display: flex;
    flex-direction: column;
    align-items: center;

    .suc-pop-content {
        border-radius: 10px;
        width: 345px;
        height: 235px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;

        .suc-pop-icon {
            width: 90px;
            height: 90px;
        }

        .suc-pop-title {
            margin-top: 15px;
            line-height: 36px;
            font-size: 24px;
            color: #141414;
            font-weight: 700;
        }

        .suc-pop-detail {
            line-height: 36px;
            font-size: 12px;
            color: #141414;
            font-weight: 700;
        }
    }
}

.oil-center {
    width: 100%;
    height: 100%;
    background: #f6f6f6;
    position: relative;

    .oil-top {
        width: 100%;
        // background: #333333;

        .top-div {
            padding: 12px;
            // padding: 20px 15px 0;

            .card-top {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .card-tip {
                    font-size: $font14;
                    font-weight: 400;
                    color: #ffffff;
                }
            }

            .top-section {
                // background: #FFFFFF;
                // border-top-left-radius: 5px;
                // border-top-right-radius: 5px;
                // margin-top: 10px;

                overflow: hidden;
                width: 100%;

                .swiper {
                    width: 100%;
                    height: 315rpx;
                    overflow: hidden;
                    margin-bottom: 20px;
                }

                .header {
                    text-align: center;
                    font-size: 15px;
                    font-weight: bold;
                    color: $btn-color;
                    height: 170px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .add-icon {
                        margin-right: 10px;

                        img {
                            width: 30px;
                            height: 30px;
                        }
                    }

                    .add-txt {
                        font-size: 25px;
                        font-weight: 700;
                    }
                }
                .item {
                    width: 100%;
                    height: 100%;
                    background-size: 702rpx 100%;
                    background-repeat: no-repeat;

                    .header-div {
                        width: 100%;
                        padding: 20rpx 20rpx 24rpx;
                        color: #fff;
                        font-size: 22rpx;
                        // opacity: 0.8;
                        .name-div {
                            color: #fff;
                            font-size: 22rpx;
                            line-height: 20px;
                            opacity: 0.8;
                            display: flex;
                            align-items: center;
                            .name {
                                position: relative;
                                margin-right: 16rpx;

                                &.name:before {
                                    content: '';
                                    position: absolute;
                                    top: 0;
                                    right: -16rpx;
                                    bottom: 0;
                                    width: 1px;
                                    border-right: 1px solid #ebedf0;
                                    -webkit-transform-origin: 0 0;
                                    transform-origin: 0 0;
                                    -webkit-transform: scaleX(0.5);
                                    transform: scaleX(0.5);
                                    z-index: 9;
                                }
                            }

                            .card-number {
                                margin-left: 16rpx;
                                display: inline-block;
                                vertical-align: baseline;
                            }
                            .eye-block-iocn1 {
                                width: 17px;
                                height: auto;
                                padding: 0 5px;
                                display: inline-block;
                                vertical-align: baseline;
                            }
                        }
                        .address-text {
                            font-size: 11px;
                            color: #fff;
                        }
                        .bottom-div {
                            margin-top: 20rpx;
                            .flex-row {
                                display: flex;
                                align-items: center;
                                .flex-item {
                                    width: 50%;
                                }
                            }

                            .row-div {
                                padding: 0 0 8rpx;
                                display: flex;
                                align-items: center;
                                height: 25px;
                                overflow: hidden;
                                .row-div:last-child {
                                    padding: 0;
                                }
                                .row-left {
                                    font-size: 22rpx;
                                    opacity: 0.8;
                                    color: #fff;
                                    margin-right: 8rpx;
                                    display: inline-block;
                                    vertical-align: baseline;
                                }

                                .row-price {
                                    color: #fff;
                                    font-size: 24rpx;
                                    margin-right: 8rpx;
                                    display: inline-block;
                                    vertical-align: baseline;
                                }

                                .row-text {
                                    font-size: 36rpx;
                                    color: #fff;
                                    display: inline-block;
                                    vertical-align: baseline;
                                }
                                .row-text-center {
                                    padding-top: 9px;
                                }
                            }
                        }
                    }
                }
                .header-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    color: #222222;
                    padding: 15px 15px;
                    margin: 0 auto;

                    .info-left {
                        img {
                            width: 20px;
                            height: 20px;
                            display: inline-block;
                            vertical-align: middle;
                            margin-right: 5px;
                        }

                        span {
                            font-size: 15px;
                            vertical-align: middle;
                        }
                        .info-left-btn {
                            margin-left: 20rpx;
                            display: inline-block;
                            font-size: 24rpx;
                            line-height: 40rpx;
                            border-radius: 5rpx;
                            color: $btn-color;
                            padding: 0 10rpx;
                            border: 2rpx solid $btn-color;
                        }
                        .cardType {
                            margin: 0 10rpx;
                            background-color: #ff8200;
                            font-size: 24rpx;
                            line-height: 40rpx;
                            border-radius: 10rpx;
                            color: #fff;
                            font-weight: 500;
                            padding: 2rpx 4rpx;
                            border: 2rpx solid $btn-color;
                        }
                    }

                    .info-right {
                        font-size: $font14;
                    }
                }

                .center {
                    //width: 91%;
                    display: flex;
                    justify-content: flex-start;
                    align-items: flex-start;
                    position: relative;
                    margin: 0 auto;
                    padding: 10px 15px;
                    box-sizing: border-box;

                    .eye-block-iocn {
                        width: 17px;
                        height: auto;
                        padding: 5px 24px 0 0;
                    }
                    .eye-block-iocn1 {
                        width: 17px;
                        height: auto;
                        padding: 5px 24px 0 5px;
                    }

                    .money-upload {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 50px;
                        height: 25px;
                        text-align: center;
                        font-size: 12px;
                        color: $btn-color;
                        background: $btn-mantle-color;
                        border: 0.6px solid $btn-color;
                        border-radius: 2px;
                    }

                    .money-div {
                        font-size: $font14;
                        flex: 0.4;
                        min-height: 60px;

                        .money-text {
                            font-size: 20px;
                            color: $btn-color;
                            padding-top: 8px;

                            .money-icon {
                                font-size: $font14;
                                color: $btn-color;
                            }
                        }
                    }
                }

                .center:before {
                    content: ' ';
                    position: absolute;
                    left: 11px;
                    top: 0;
                    right: 11px;
                    border-bottom: 1px solid #f3f3f3;
                }

                .footer {
                    width: 100%;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 0 15px;
                    box-sizing: border-box;
                    padding-bottom: 14px;
                }

                .info-btn {
                    padding: 6px 12px;
                    box-sizing: border-box;
                    flex: 1;
                    font-size: 12px;
                    text-align: center;
                    color: #909090;
                    border-radius: 6px;
                    border: 1px solid #ededed;
                    margin-left: 4px;
                }

                .active-btn {
                    padding: 6px 12px;
                    box-sizing: border-box;
                    flex: 1;
                    font-size: 12px;
                    background: $btn-mantle-color;
                    border-radius: 6px;
                    border: 0.5px solid $btn-color;
                    color: $btn-color;
                    margin-left: 4px;
                }

                .error-btn {
                    padding: 6px 12px;
                    box-sizing: border-box;
                    flex: 1;
                    font-size: 12px;
                    background: #fe2a2a;
                    border-radius: 6px;
                    border: 1px solid #fe2a2a;
                    color: #ffffff;
                    margin-left: 4px;
                }

                .info-btn:first-child {
                    margin-left: 0;
                }
            }
        }

        .opacity-div {
            width: 100%;
            height: 14px;
            background: linear-gradient(180deg, rgba(78, 78, 78, 0.2) 0%, #787878 100%);
            opacity: 0.2;
            margin-top: -14px;
        }
    }

    .oil-section {
        background: #ffffff;
        border-radius: 8px;
        margin: 0 12px;
        box-sizing: border-box;
        margin-top: -28px;
        position: relative;
        z-index: 100;

        .oil-wrap {
            // padding: 10px 15px 0;
            padding: 10px 10px;
        }
        .pay-title-view {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .pay-title {
                font-size: 15px;
                font-weight: bold;
                color: #333333;
            }
            .pay-pro {
                padding-top: 10px;
                padding-bottom: 10px;
                font-size: 12px;
                color: #333333;
            }
        }

        .pay-input {
            display: flex;
            align-items: center;
            background: #ffffff;
            border-radius: 5px;
            border: 1px solid #dcdcdc;
            height: 44px;
            line-height: 44px;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .div-box-input {
            width: 350rpx;
            height: 100%;
        }
        .pay-icon {
            font-size: 18px;
            color: #333333;
            padding: 0 20px;
        }
        .section-div {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;

            .div-box {
                margin-top: 7px;
                // width: 110px;
                width: 100px;
                height: 44px;
                line-height: 44px;
                text-align: center;
                // font-size: 15px;
                font-size: 13px;
                font-weight: 400;
                // color: #909090;
                color: #333333;
                background: #ffffff;
                border-radius: 5px;
                border: 1px solid #dcdcdc;
            }

            .div-select-box {
                margin-top: 7px;
                // width: 110px;
                width: 100px;
                height: 44px;
                line-height: 44px;
                text-align: center;
                // font-size: 15px;
                font-size: 13px;
                font-weight: 500;
                color: $btn-color;
                background: $btn-mantle-color;
                border-radius: 5px;
                border: 1px solid $btn-color;
            }
        }
        .pay-tip {
            color: #909090;
            font-size: 12px;
            padding: 20px 0 0;
            position: relative;
            &.pay-tip:before {
                content: '';
                position: absolute;
                top: 9px;
                left: 0;
                right: 0;
                width: 100%;
                height: 1px;
                border-top: 1px solid #909090;
                -webkit-transform-origin: 0 0;
                transform-origin: 0 0;
                -webkit-transform: scaleY(0.5);
                transform: scaleY(0.5);
                z-index: 9;
            }
        }

        .bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            margin-top: 20px;
            .pay-btn {
                width: 80px;
                text-align: center;
                font-size: 15px;
                color: #c8c8c7;
                background: #c8c8c7;
                border-radius: 5px;
                border: 1px solid #c8c8c7;
            }
            .pay-btn2 {
                width: 80px;
                color: #c8c8c7;
                font-size: 15px;
                margin-left: 20px;
            }
        }
    }
    .horn-text-wrap {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
        margin-top: 10px;
        .horn-text {
            font-size: 28rpx;
            color: #ef8934;
        }
        .horn-img {
            width: 18px;
            height: 16px;
            margin-right: 5px;
        }
    }

    .oil-prompt {
        font-size: 24rpx;
        color: #999999;
        padding-left: 15px;
        padding-right: 15px;
        margin-top: 5px;
    }
    .oil-kefu {
        font-size: 12px;
        color: #999999;
        text-align: center;
        position: relative;
        margin-top: 20px;
        &.oil-kefu:before {
            content: ' ';
            position: absolute;
            left: 25%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }

        &.oil-kefu:after {
            content: ' ';
            position: absolute;
            right: 25%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }
    }
    .oil-kefu1 {
        font-size: 11px;
        color: #999999;
        text-align: center;
        margin-top: 5px;
    }
    .oil-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;

        .agreement {
            font-size: 12px;
            font-weight: 500;
            color: #909090;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            position: relative;

            .checkbox {
                position: absolute;
                left: 15px;
                padding-bottom: 5px;
            }

            .agreement-txt {
                font-weight: bold;
                // font-size: 12px;
                font-size: 14px;
            }
        }

        .oil-btn {
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px env(safe-area-inset-bottom);
            box-sizing: border-box;

            .oil-money {
                font-size: 15px;
                font-weight: 500;
                color: #909090;
                width: 210px;
                text-align: left;

                .oil-money-bold {
                    font-size: 24px;
                }
            }

            .oil-money-info {
                color: $btn-color;
                width: 210px;
                text-align: left;
                overflow: hidden;
            }

            .oli-paybtn-gray {
                // width: 120px;
                width: 700px;
                height: 44px;
                margin-top: 10px;
                margin-bottom: 10px;
                background: #dcdcdc;
                border-radius: 5px;
                font-size: 15px;
                font-weight: 500;
                color: #ffffff;
                line-height: 44px;
                text-align: center;
            }

            .oli-pay-info {
                background: $btn-color;
            }
        }
    }
}

.oil-money-info {
    color: $btn-color;
}

.oil-kefu {
    font-size: 12px;
    color: #999999;
    text-align: center;
    position: relative;
    margin-top: 20px;
    &.oil-kefu:before {
        content: ' ';
        position: absolute;
        left: 25%;
        top: 50%;
        width: 25px;
        height: 1px;
        border-top: 1px solid #999;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }

    &.oil-kefu:after {
        content: ' ';
        position: absolute;
        right: 25%;
        top: 50%;
        width: 25px;
        height: 1px;
        border-top: 1px solid #999;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}
.popup {
    width: 300px;
    height: 150px;
    background-color: #fff;
    border-radius: 10px;
    z-index: 10000;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    p {
        font-size: 18px;
        padding-top: 50px;
    }
    .countDown {
        font-size: 16px;
        margin-top: 10px;
        span {
            color: #ff8200;
        }
    }
}
.popupMask {
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    background: #333;
    opacity: 0.5;
}
</style>
