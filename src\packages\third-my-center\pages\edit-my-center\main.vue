<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw" :class="pageType == 'address' ? 'bg-F7F7FB' : 'bg-fff'">
            <zj-navbar :border-bottom="false" :title="titleNav" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div v-if="pageType == 'address'">
                        <div class="card-default mar16">
                            <div class="fl-row fl-al-cen padd13-15">
                                <div class="f-1 font-14 weight-400 color-000">常用省市</div>
                                <div class="font-14 weight-400 color-000">{{ memberData.address }}</div>
                            </div>
                        </div>
                        <div class="card-default mar16 btn">
                            <div class="font-18 color-fff weight-500 te-center" @click="sureEdit()">确认修改</div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="mar16">
                            <div class="fl-row fl-al-base paddlr12 bg-F7F7FB border-rad-8">
                                <input
                                    type="text"
                                    class="f-1 input-style bg-F7F7FB"
                                    v-model="updateUserInfo"
                                    :placeholder="placeholderText"
                                    placeholder-style="font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;"
                                />
                                <img
                                    src="../../image/icon-sc.png"
                                    alt=""
                                    class="icon-15"
                                    v-if="pageType == 'nikeName'"
                                    @click.stop="clearInfo"
                                />
                            </div>
                        </div>
                        <div class="card-default mar16 btn">
                            <div
                                class="font-18 color-fff weight-500 te-center"
                                v-if="pageType == 'mail' || pageType == 'nikeName' || pageType == 'addressInfo'"
                                @click="submitData"
                                >保存</div
                            >
                            <div class="font-18 color-fff weight-500 te-center" v-else @click="submitData">完成</div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>

            <zj-show-modal>
                <div class="tc_div" v-if="showModalType">
                    <div class="title">常用地由“{{ walletInfo.addressName }}”变更为 “{{ choseCity }}”</div>
                    <div class="number_of_times fl-row">
                        每年可更换5次，还剩
                        <div>{{ walletInfo.leftChangeCount }}</div
                        >余次
                    </div>
                    <div class="text">您当前电子券只能在当前常用地使用，不会随着常用地变更而改变;</div>
                    <div class="text">若您的昆仑e享卡存在消费折扣，变更后当前消费折扣将失效。</div>
                </div>
            </zj-show-modal>
        </div>
    </div>
</template>

<script>
import { modifyUserInfoApi } from '../../../../s-kit/js/v3-http/https3/user.js';
import { basicInfoQuery } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            pageType: 'address',
            updateUserInfo: '',
            memberData: '',
            provincesAndCitiesList: '',
            showModalType: false,
            showPopup: false,
        };
    },
    onLoad(options) {
        let data = JSON.parse(decodeURIComponent(options.data));
        console.log(data);
        this.pageType = data.pageType;
        // 获取省市地区
        // this.getProvincesCities();
    },
    onShow() {
        this.getBasicInfoQuery();
        // this.initDate();
    },
    methods: {
        /**
         * @description  : 获取钱包数据
         * @return        {*}
         */
        async initDate() {
            // 没有引用接口，放开调用的时候注意！！！！！！！！！！！！！！！！！
            let res = await balance();
            if (res && res.success) {
                this.walletInfo = res.data;
                console.log(res, 'res============balance');
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            this.getBasicInfoQuery();
            this.$refs.pullDownRefreshRef.stopRefresh();
        },
        /**
         * @description : 查询用户基本信息
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery();
            if (res.success) {
                // for (let key in res.data) {
                // this.$set(this.memberData, key, res.data[key])
                this.memberData = res.data;
                if (this.pageType == 'mail') {
                    this.updateUserInfo = this.memberData.email;
                }
                if (this.pageType == 'nikeName') {
                    this.updateUserInfo = this.memberData.alias;
                }
                // }
            }
        },
        /**
         * @description : 清除姓名
         * @return        {*}
         */
        clearInfo() {
            console.log(this.updateUserInfo);
            this.updateUserInfo = '';
        },
        /**
         * @description : 修改用户基本信息
         * @return        {*}
         */
        async modifyUserInfoPost() {
            let params = {};
            // 邮箱
            if (this.pageType == 'mail') {
                params.email = this.updateUserInfo;
                params.type = '2';
            }
            // 昵称
            if (this.pageType == 'nikeName') {
                if (this.updateUserInfo.length > 10) {
                    uni.showToast({
                        title: '输入的昵称长度不能超过10个字',
                        icon: 'none',
                    });
                    return;
                } else {
                    params.alias = this.updateUserInfo;
                    params.type = '4';
                }
            }
            //详细地址
            if (this.pageType == 'addressInfo') {
                // params.alias = this.updateUserInfo
                // params.type = '4'
                return;
            }
            let res = await modifyUserInfoApi(params);
            console.log('res---', res);
            if (res.success) {
                if (this.pageType == 'nikeName') {
                    uni.showToast({
                        title: '修改姓名成功',
                        duration: 2000,
                    });
                } else if (this.pageType == 'mail') {
                    uni.showToast({
                        title: '修改邮箱成功',
                        duration: 2000,
                    });
                } else if (this.pageType == 'addressInfo') {
                    uni.showToast({
                        title: '修改详细地址成功',
                        duration: 2000,
                    });
                }
                uni.navigateBack();
            } else {
                if (this.pageType == 'nikeName') {
                    uni.showToast({
                        title: '修改姓名失败',
                        duration: 2000,
                    });
                } else if (this.pageType == 'mail') {
                    uni.showToast({
                        title: '修改邮箱失败',
                        duration: 2000,
                    });
                } else if (this.pageType == 'addressInfo') {
                    uni.showToast({
                        title: '修改详细地址失败',
                        duration: 2000,
                    });
                }
            }
        },
        /**
         * @description : 校验姓名格式
         * @return        {*}
         */
        checkNormal(str) {
            let reg = /^[a-zA-Z0-9\u4E00-\u9FA5\(\)\(\)]*$/g;
            // let reg = /^[a-zA-Z0-9\u4E00-\u9FA5]*$/g
            return reg.test(str);
        },
        /**
         * @description : 校验邮箱格式
         * @return        {*}
         */
        checkMail: function (str) {
            var reg = new RegExp('^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$');
            if (reg.test(str)) {
                return true;
            }
            return false;
        },
        /**
         * @description : 保存修改数据
         * @return        {*}
         */
        submitData() {
            if (this.pageType == 'nikeName') {
                if (this.updateUserInfo == '') {
                    uni.showToast({
                        title: '请输入姓名',
                        duration: 2000,
                    });
                    return;
                }
                if (!this.checkNormal(this.updateUserInfo)) {
                    uni.showToast({
                        title: '姓名格式错误',
                        duration: 2000,
                    });
                    return;
                }
            } else if (this.pageType == 'mail') {
                if (this.updateUserInfo == '' || !this.checkMail(this.updateUserInfo)) {
                    uni.showToast({
                        title: '请输入正确的邮箱',
                        duration: 2000,
                    });
                    return;
                }
            } else if (this.pageType == 'addressInfo') {
                if (this.updateUserInfo == '') {
                    uni.showToast({
                        title: '请输入详细地址',
                        duration: 2000,
                    });
                    return;
                }
            }
            this.modifyUserInfoPost();
        },

        /**
         * @description : 获取城市列表
         * @return        {*}
         */
        async getProvincesCities() {
            // 没有引用接口，放开调用的时候注意！！！！！！！！！！！！！！！！！
            let res = await provinceAndCityList();
            //
            //
            if (res.success) {
                let arr2 = res.data.map(item => {
                    let newItem = {
                        name: item.parentAreaName,
                        code: item.parentAreaCode,
                        city: item.areaList,
                    };
                    return newItem;
                });
                this.provincesAndCitiesList = arr2;
            }
        },
        /**
         * @description : 修改常用省市
         * @return        {*}
         */
        sureEdit() {
            let URL = '/packages/third-electronic-wallet/pages/wallet-select-area/main';
            let params = {};
            this.$sKit.layer.useRouter(URL, params, 'navigateTo');
        },
    },
    computed: {
        ...mapGetters(['walletInfo', 'walletStatus']),
        // 页面title显示
        titleNav() {
            if (this.pageType == 'address') {
                return '常用地';
            } else if (this.pageType == 'mail') {
                return '邮箱';
            } else {
                return '昵称';
            }
        },
        // placeholder文本
        placeholderText() {
            if (this.pageType == 'mail') {
                return '请输入常用邮箱号码';
            } else if (this.pageType == '') {
                return '';
            } else {
                return '请输入您的昵称';
            }
        },
    },
    components: {},
};
</script>
<style scoped lang="scss">
.padd13-15 {
    padding: 13px 15px;
}

.paddlr12 {
    padding: 0 12px;
    display: flex;
    align-items: center;
}

.input-style {
    height: 44px;
    // border-radius: 8px;
    margin-right: 10px;
}

.mar16 {
    margin: 16px 16px 0;
}

.btn {
    height: 44px;
    line-height: 44px;
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
}

.icon-15 {
    width: 15px;
    height: 15px;
    display: block;
}
</style>
