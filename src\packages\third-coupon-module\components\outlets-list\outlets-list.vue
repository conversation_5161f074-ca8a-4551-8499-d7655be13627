<template>
    <div class="model-div fl-column bg-F7F7FB">
        <div class="outlets-title bg-fff">
            <div class="outlets-title-text">附近可用门店</div>
            <div class="color-999 font-12">您附近有以下网点可使用当前优惠券</div>
            <div
                class="reminder fl-row fl-al-cen bg-FFF7DC font-12 weight-400 color-333"
                :style="{ marginBottom: showEmpty ? '0' : '20rpx' }"
            >
                温馨提示：为避免给您的出行带来不便，请在使用洗车/98#汽油券前与加油站电话核实当前时段是否支持核销。</div
            >
        </div>
        <div class="outlets-content">
            <zj-data-list
                background="#F7F7FB"
                ref="list"
                :emptyImage="require('../../images/kt6wd.png')"
                :emptyText="productList.length > 0 ? '' : '附近暂无可用网点'"
                :showEmpty="showEmpty"
                @scrolltolower="scrolltolower"
                @refreshPullDown="refreshPullDown"
            >
                <div class="cells-content">
                    <searchCell
                        v-for="(item, index) in productList"
                        :item="item"
                        :curIndex="index"
                        :key="index"
                        @cellClick="cellClick(item)"
                    ></searchCell>
                </div>
            </zj-data-list>
        </div>
    </div>
</template>
<script>
const PAGE_SIZE = 10; // 每页多少个
const THROTTLE_TIME = 500;
import { mapGetters, mapState } from 'vuex';
import { availableStationApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import searchCell from '../../../../s-kit/components/layout/search-module/oil-station-search-cell.vue';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
export default {
    props: {},
    data() {
        return {
            productList: [], // 附近可用网点数据列表
            // 是否展示附近可用网点空态标识
            showEmpty: false,
            // 页码
            page: 1,
            first: true,
            couponTemplateNo: '', // 电子券模板编码
        };
    },
    computed: {
        ...mapGetters(['showMarkerArrV3_app', 'latV3', 'lonV3', 'walletInfo']),
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
        }),
    },
    onLoad(option) {},
    mounted() {},
    methods: {
        /**
         * @description  : 获取油站列表
         * @param         {*} item:stationService油站类型
         * @return        {*}
         */
        selectAction(item = [], isSameArr = false, couponTemplateNo) {
            this.couponTemplateNo = couponTemplateNo;
            if (this.first) {
                this.init(isSameArr);
                this.first = false;
            } else {
                this.getGoodsListFun({ isInit: true, isSameArr: isSameArr });
            }
        },
        /**
         * @description  : 初始化位置信息，获取油站列表
         * @return        {*}
         */
        init(isSameArr) {
            this.$store.dispatch('initLocationV3_app', {
                callback: res => {
                    this.getGoodsListFun({ isInit: true }, res);
                },
                type: 'onlyLocation',
            });
        },
        /**
         * @description  : 获取油站列表
         * @param         {*} isInit: 是否重置油站列表
         * @param         {*} item: 油站类型入参
         * @return        {*}
         */
        async getGoodsListFun({ isInit = false, isSameArr = false } = {}, longitudeAndLatitude) {
            if (isInit) {
                Object.assign(this, {
                    productList: isSameArr ? (this.productList.length > 0 ? this.productList : []) : [],
                    page: 1,
                });
            }
            let { page, productList } = this;
            let params = {
                pageNo: page,
                pageSize: PAGE_SIZE,
                couponTemplateNo: this.couponTemplateNo, // 券模板编码
                latitude: this.latV3 || longitudeAndLatitude?.latitude,
                longitude: this.lonV3 || longitudeAndLatitude?.longitude,
                mapType: '1', //地图坐标系标识（0高德，1百度，2腾讯）
                // orgCode: this.walletInfo.addressNo || '',
            };
            this.$refs.list.loadStatus = 'loading';
            let { data } = await availableStationApi(params, { isload: false });
            let arr = this.$sKit.layer.filterLocations(data.rows);
            let dataRows = arr.map(item => {
                item.businessHoursList = [];
                item.tagList = item.tagList ? item.tagList : [];
                let strArr = item.businessHoursList.map(time => {
                    return time.startTime.substring(0, 5) + '-' + time.endTime.substring(0, 5);
                });
                item.businessHoursStr = strArr.join(' ');
                return item;
            });
            this.$refs.list.pullDownHeight = 0;
            this.$refs.list.pullingDown = false;
            // 判断选择框是否打开
            if (isSameArr && productList.length > 0) {
                const isSame = this.areArraysEqual(this.showMarkerArrV3_app, dataRows);
                console.log(isSame, 'result一样吗?');
                if (isSame) {
                    console.log('相同走这里');
                } else {
                    console.log('不相同走这里');
                    productList = dataRows || [];
                }
            } else {
                productList = productList.concat(dataRows || []);
            }

            if (data && page >= data.pageNo) {
                this.$refs.list.loadStatus = 'nomore';
            } else {
                this.$refs.list.loadStatus = 'contentdown';
            }
            Object.assign(this, {
                productList,
                page: Number(page) + 1,
            });
            this.showEmpty = this.productList.length <= 0 ? true : false;
        },
        /**
         *  JavaScript 中判断两个数组的数据是否相同
         * */
        areArraysEqual(arr1, arr2) {
            if (arr1.length > 0 && arr2.length > 0) {
                return arr1[0].orgCode === arr2[0].orgCode && arr1[0].distance === arr2[0].distance;
            }
            return false;
        },
        /**
         * @description  : 上拉加载附近可用网点
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.list.loadStatus !== 'nomore') {
                this.$refs.list.loadStatus = 'loading';
                this.page + 1;
                this.getGoodsListFun();
            }
        },
        /**
         * @description  : 下拉刷新附近可用网点
         * @return        {*}
         */
        refreshPullDown() {
            this.page = 1;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  :附近网点选择油站
         * @param         {*} item:油站数据
         * @return        {*}
         */
        async cellClick(item) {
            let URL = '/packages/third-scan-code-payment/pages/home-code/main';
            let params = {
                noInitLocation: true,
                tabType: 'charge',
            };
            // let type = 'reLaunch';
            let centerParam = {
                marker: item,
                upLocation: true,
                oilChangeStation: true,
            };
            this.$store.dispatch('setSelectMarkerToMapCenterV3', centerParam);
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$sKit.layer.useRouter(URL, params, 'navigateTo');
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, 'navigateTo');
            // #endif
        },
    },
    components: {
        searchCell,
        ZjPopup,
    },
};
</script>
<style scoped lang="scss">
.model-div {
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 20px;
    overflow: auto;

    .outlets-title {
        width: 100%;
        // height: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;

        .outlets-title-text {
            font-size: 17px;
            font-weight: bold;
            margin-bottom: 6px;
            margin-top: 6px;
        }

        .reminder {
            width: 100%;
            // height: 40px;
            word-wrap: break-word;
            word-break: normal;
            margin-top: 15px;
            padding: 9px 16px;
            box-sizing: border-box;
        }
    }

    .outlets-content {
        padding: 0px 16px;
        height: 350px;
    }
}

.model-popup {
    background: #ffffff;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 20px;
    overflow: auto;

    .marlr24 {
        margin: 0 16px 0 16px;
    }

    .padtb16 {
        padding-top: 16px;
        // height: 330px;
        overflow: hidden;
        overflow-x: hidden;
        overflow-y: scroll;
    }

    .popup-label-title {
        margin-left: 18px;
    }

    .popup-center {
        display: flex;
        flex-wrap: wrap;
        padding: 10px 12px;

        .popup-label {
            width: 77px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-size: 12px;
            padding: 0px 6px;
            margin: 0 5px 0 5px;
            text-align: center;
            margin-bottom: 10px;
        }

        .isSelect {
            background: #ffe9e3;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }

    .submit-btn {
        font-size: 15px;
        padding: 10px 65px;
        border-radius: 6px;
    }

    .marright {
        margin-right: 18px;
    }

    .close {
        padding: 20px 3px;

        img {
            width: 13px;
            height: 13px;
        }
    }
}
</style>
