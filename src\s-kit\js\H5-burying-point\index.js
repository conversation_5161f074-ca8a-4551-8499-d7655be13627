import { mpaasAppId, workspaceid, maa,cloudappId } from '../../../../project.config';
import { singleton } from '../singleton';
class MPassBuryingPoint {
  constructor() {
      console.log('MpassBuryingPoint 埋点');
      this.init();
  }

  init() {
    let memberNo = ''
    memberNo = uni.getStorageSync('tokenInfo')?.memberNo || 'null';
    console.log('memberNo',memberNo)
    window._to = {
          server: maa.reportURL,
          appId: mpaasAppId,
          workspaceId: workspaceid,
          h5version: '1.0.0.0',
          bizScenario: '',
          userId: memberNo,
          mtrDebug: true
      };
      console.log(window,window.Tracker)
  }

  tracker(type, extParam) {
      console.log('埋点：', type, extParam);
      console.log(window,window.Tracker)
      const Tracker = window?.Tracker
      Tracker?.click(type, {
            // bizType: 'webTracker',
            ext: {...extParam,}
        });
  }
}

export default singleton(MPassBuryingPoint);