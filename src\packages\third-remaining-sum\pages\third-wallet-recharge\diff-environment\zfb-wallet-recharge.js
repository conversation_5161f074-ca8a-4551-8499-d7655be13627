import { mapGetters, mapState } from 'vuex';
// #ifdef MP-ALIPAY || H5
export default {
    data() {
        return {
            payPlugin: {},
        };
    },
    async onLoad(options) {
        my.hideBackHome();
        if (options.data) {
            let personalObj = JSON.parse(decodeURIComponent(options.data));
            if (personalObj.qKey) {
                this.qKey = personalObj.qKey;
            }
        }
    },
    methods: {
        async getRechargeMethod(params) {
            return new Promise(async (resolve, reject) => {
                try {
                    const res = await this.payPlugin.GetRechargeTypeList(params);
                    console.log(res, '获取支付方式===支付宝');
                    resolve(this.setIconAndRechar(res));
                } catch (error) {
                    reject(error);
                }
            });
        },
        /**
         * 发起充值支付
         * @param areaCode 地区编码
         * @param bizOrderNo 业务订单编号
         * @param rcvAmt 应收总金额
         * @param realAmt 支付金额
         */
        async callUpPayment(resMP) {
            console.log('🚀 ~ file: zfb-wallet-recharge.js:80 ~ callUpPayment ~ resMP:', resMP);
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: resMP.data.prePayId,
                rcvAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                realAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                // 3.0.4风控字段
                extendFiled: JSON.stringify({
                    dfp: '',
                    gps:
                        this.riskManagementLonV3 && this.riskManagementLatV3
                            ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                            : '',
                    gpsProvince: '',
                    gpsCity: '',
                    gpsArea: '',
                }),
                openId: this.openId,
                payType: resMP.data.paymentType + '',
            };
            const res = await this.payPlugin.RechargePay(params);
            console.log(res, 'res---RechargePay');
            this.payingFlag = false;
            if (res.code === 'PAY_SUCCESS') {
                this.otherAmount = '';
                this.$sKit.layer.useRouter(
                    '/packages/third-remaining-sum/pages/third-charge-result/main',
                    { orderId: resMP.data.prePayId, refer: this.refer, addressName: this.addressName },
                    'navigateTo',
                );
            } else {
                this.closePopup();
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
    },
};
// #endif
