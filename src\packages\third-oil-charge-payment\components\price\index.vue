<template>
    <div class="price-com">
        <div :style="{ color }" v-if="!isDet">
            <span class="int" :style="{ fontSize: intFont }">{{ price | priceIntFilter }}</span>
            <span class="float" :style="{ fontSize: floatFont }">.{{ price | priceFloatFilter }}</span>
        </div>
        <div class="price" :style="{ color, marginLeft: delLeft }" v-else>
            <span class="int" :style="{ fontSize: intFont }">&yen;{{ price | priceFilter }}</span>
        </div>
    </div>
</template>

<script>
/**
 * 价格组件   价格整数和小数分离组件
 */
export default {
    props: {
        //价格
        price: [Number, String],
        color: {
            type: String,
            default: '#FF4000',
        },
        //整数字体大小
        intFont: {
            type: String,
            default: '32rpx',
        },
        //小数字体大小
        floatFont: {
            type: String,
            default: '24rpx',
        },
        // 是否被划掉
        isDet: {
            type: Boolean,
            default: false,
        },
        // 划线价左侧距离
        delLeft: {
            type: String,
            default: '5px',
        },
    },
    filters: {
        // 金额整数部分过滤器
        priceIntFilter(price) {
            return Number(price).toFixed(2).split('.')[0];
        },
        // 金额小数不分过滤器
        priceFloatFilter(price) {
            return Number(price).toFixed(2).split('.')[1];
        },
        // 金额全部
        priceFilter(price) {
            return Number(price).toFixed(2).split('.')[0] + '.' + Number(price).toFixed(2).split('.')[1];
        },
    },
};
</script>

<style lang="scss" scoped>
.price {
    margin-left: 5px;
    text-decoration: line-through;
    display: flex;
    align-items: center;
}
</style>
