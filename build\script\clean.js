/**
 * 代码解释
引入必要的模块：引入 fs 模块用于文件系统操作，引入 path 模块用于处理文件路径。
定义要清理的目录：使用 path.resolve 方法获取 dist/build/h5/static 目录的绝对路径。
递归删除函数 removeNonExcludedFiles：
检查目录是否存在。
读取目录下的所有文件和文件夹。
对于文件夹，如果是 js 文件夹则跳过；否则递归调用该函数删除子目录，最后删除空目录。
对于文件，如果不是 .css 文件则删除。
执行清理操作：调用 removeNonExcludedFiles 函数并传入要清理的目录路径，最后输出清理完成的信息。
在构建后执行脚本
你可以在 package.json 的 scripts 中配置在构建后执行该脚本。
 */
const fs = require('fs');
const path = require('path');

// 定义要清理的目录
const staticDir = path.resolve(__dirname, '../../dist/build/h5/static');

// 递归删除文件和文件夹的函数
function removeNonExcludedFiles(dir) {
    if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        files.forEach((file) => {
            const curPath = path.join(dir, file);
            const stat = fs.lstatSync(curPath);
            if (stat.isDirectory()) {
                if (file === 'js') {
                    // 跳过 js 文件夹
                    return;
                }
                // 递归删除子目录
                removeNonExcludedFiles(curPath);
                // 删除空目录
                fs.rmdirSync(curPath);
            } else {
                if (path.extname(file) !== '.css') {
                    // 删除非 .css 文件
                    fs.unlinkSync(curPath);
                }
            }
        });
    }
}

// 执行清理操作
removeNonExcludedFiles(staticDir);
console.log('清理完成');