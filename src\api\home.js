import { POST } from './httpKit.js';
import utils from '../utils/index.js';
import config from '../utils/config.js';

/***** 地图相关接口 *****/
// 获取定位附近油站接口 (停用)
export const getNearStation = (params, config) => {
    return POST('/app/json/station/getNear', params, config);
};
//获取已上线的省份位置信息
// export const getOnlineProvince = (params, config) => {
//   return POST('/app/json/area/online', params, config)
// }
export const getOnlineProvince = (params, config) => {
    return POST('/app/json/area/online', params, config);
};
//首页获取默认卡余额/电子券数量/年度优惠
export const getDefaultCardStatistics = (params, config) => {
    return POST('/app/json/cnpc_card/getDefaultCardStatistics', params, config);
};
// 获取定位附近油站列表接口
export const getNearStationList = (params, config) => {
    return POST('/app/json/station/getNearMore', params, config);
};

/***** 常用车辆相关接口 *****/
// 获取常用车牌列表
export const getCarList = (params, config) => {
    return POST('/app/json/car/getList', params, config, false);
};
// 编辑/新增 常用车辆
export const editCarInfo = (params, config) => {
    return POST('/app/json/car/save', params, config, true, false);
};
// 设置默认常用车辆
export const setDefaultCar = (params, config) => {
    return POST('/app/json/car/setDefault', params, config);
};
// 删除常用车辆
export const deteleDefdaultCar = (params, config) => {
    return POST('/app/json/car/delete', params, config);
};

/***** 消息相关接口 *****/
// 消息类型列表接口
export const getUnreadMsgTypeCnt = (params, config) => {
    return POST('/app/json/message/getUnreadMsgTypeCnt', params, config);
};
// 消息列表接口
export const getUnreadMsgModel = (params, config) => {
    return POST('/app/json/message/getUnreadMsgModel', params, config);
};
// 消息未读数
export const getUnreadMsgCnt = (params, config) => {
    return POST('/app/json/message/getUnreadMsgCnt', params, config);
};

/***** 油卡相关接口 *****/
// 油卡充值接口
export const paycard = (params, config) => {
    params = {
        ...params,
        nonce: utils.generateUUID(),
        timestamp: new Date().getTime(),
    };
    return POST('/app/json/cnpc_card/preOrder', params, config);
};
// 油卡明细接口
export const getCardDetails = (params, config) => {
    return POST('/app/json/cnpc_card/getCardDetail', params, config);
};
// 获取加油卡详情
export const getCardDetailApi = (params, config) => {
    return POST('/app/json/cnpc_card/getCardDetail', params, config);
};
// 设置默认油卡
export const setDefaultCardApi = (params, config) => {
    return POST('/app/json/cnpc_card/setDefaultCard', params, config);
};
// 解绑油卡接口
export const unBinding = (params, config) => {
    return POST('/app/json/cnpc_card/unBinding', params, config);
};
// 获取油卡接口列表
export const cardListPost = (params, config) => {
    return POST('/app/json/cnpc_card/getCardList', params, config);
};
// 充值订单记录接口
export const getRechargeList = (params, config) => {
    return POST('/app/json/order2/getRechargeList', params, config);
};
// 获取加油订单列表接口
export const getFuelListApi = (params, config) => {
    return POST('/app/json/order2/getFuelList', params, config);
};
// 获取加油卡圈存记录
export const getCircleDepositListApi = (params, config) => {
    return POST('/app/json/cnpc_card/getCircleDepositList', params, config, config.isload, config.isCustomErr);
};

// 充值订单详情接口
export const getRechargeDetailTwo = (params, config) => {
    return POST('/app/json/order2/getRechargeDetail', params, config);
};
/***** 订单相关接口 ******/
// 锁订单
export const lockOrder = (params, config) => {
    return POST('/app/json/fuel/lockOrder', params, config, false, false);
};

// 解锁订单
export const cancleOrder = (params, config) => {
    return POST('/app/json/fuel/cancelOrder', params, config, false);
};
//  退券
export const refund = (params, config) => {
    return POST('/app/json/coupon/cancelCoupon', params, config, false);
};

// 全部订单接口
export const getFuelListPost = (params, config) => {
    return POST('/app/json/order/getFuelList', params, config);
};
// 订单详情接口 (加油订单)
export const orderDetail = (params, config) => {
    return POST('/app/json/order/getFuelDetail', params, config);
};
//加油订单详情（加油订单）
export const orderDetail2 = (params, config) => {
    return POST('/app/json/order2/getFuelDetail', params, config, true, true);
};
// 订单详情接口 (充值订单)
export const getRechargeDetail = (params, config) => {
    return POST('/app/json/order/getRechargeDetail', params, config);
};
// 获取油站油枪油品接口
export const getOilGunsByStationCode = (params, config) => {
    return POST('/app/json/fuel/getOilGunsByStationCode', params, config, false);
};
// 获取最佳优惠券
export const getBestCoupon = (params, config) => {
    return POST('/app/json/coupon/getBestCoupon', params, config);
};
// 下单页面数据
export const getTradeList = (params, config) => {
    return POST('/app/json/fuel/getTradeList', params, config, false);
};
// 油品预下单接口
export const cardPreOrder = (params, config) => {
    params = {
        ...params,
        nonce: utils.generateUUID(),
        timestamp: new Date().getTime(),
    };
    return POST('/app/json/card_fuel/preOrder', params, config, true, false);
};
// 微信预下单
export const wxPreOrder = (params, config) => {
    params = {
        ...params,
        nonce: utils.generateUUID(),
        timestamp: new Date().getTime(),
    };
    return POST('/app/json/wx_fuel/preOrder', params, config, true, false);
};
// 删除加油订单
export const deleteOrder = (params, config) => {
    return POST('/app/json/order/deleteOrder', params, config, true, true);
};

// 查询待支付订单接口
export const waitPayOrder = (params, config) => {
    return POST('/app/json/fuel/waitPayOrder', params, config, true, true);
};
// 待支付订单支付
export const waitOrderPay = (params, config) => {
    return POST('/app/json/fuel/orderPay', params, config, true, true);
};
//可开票订单
export const mayBillingOrder = (params, config) => {
    return POST('/app/json/order/mayBillingOrder', params, config, true, true);
};

/***** 发票相关接口 *****/
// 添加（编辑）公司发票抬头
export const saveCompany = (params, config) => {
    return POST('/app/json/invoice/saveCompany', params, config);
};
// 添加（编辑）个人发票抬头
export const savePersonal = (params, config) => {
    return POST('/app/json/invoice/savePersonal', params, config);
};
// 开票接口
export const billingInvoice = (params, config) => {
    return POST('/app/json/invoice/billing', params, config);
};
// 重新开票接口
export const billingAgain = (params, config) => {
    return POST('/app/json/invoice/billingAgain', params, config);
};

/***** 用户协议相关接口 *****/
// 用户协议接口
export const getNewsList = (params, config) => {
    return POST('/app/json/news/getNewsList', params, config);
};

/***** 退出登录接口 *****/
// 退出登录接口
export const unBindThirdUser = (params, config) => {
    return POST('/app/json/login2/unbind', params, config);
};

/***** 优惠券相关接口 *****/
// 获取未使用的电子券
export const getUnusedList = (params, config) => {
    return POST('/app/json/coupon/getUnusedList', params, config);
};
// 获取使用的电子券
export const getUsedList = (params, config) => {
    return POST('/app/json/coupon/getUsedList', params, config);
};
// 获取已过期的电子券
export const getOverdueList = (params, config) => {
    return POST('/app/json/coupon/getOverdueList', params, config);
};
// 获取可使用的电子券
export const getUseableList = (params, config) => {
    return POST('/app/json/coupon/getUseableList', params, config);
};
// 获取未使用券详情
export const getUsedDetail = (params, config) => {
    return POST('/app/json/coupon/getCouponDetail', params, config);
};
// 获取已使用券详情
export const getUseableDetail = (params, config) => {
    return POST('/app/json/coupon/getUsedCouponDetail', params, config);
};
//  获取已使用券详情
export const electronicTicketDetails = (params, config) => {
    return POST('/app/json/coupon/getUnusedList', params, config);
};
// 获取动态核销码
export const getCouponQrcode = (params, isload = true) => {
    return POST('/app/json/coupon/getCheckCode', params, {}, isload);
};

// 获取消费统计
export const getMonthConsume = (params, config) => {
    return POST('/app/json/fuel/getMonthConsume', params, config);
};

/***** 密码相关接口 *****/
// 是否设置密码接口
export const setedPayPass = (params, config) => {
    return POST('/app/json/member/setedPayPass', params, config);
};
// 设置密码接口
export const setPayPass = (params, config) => {
    return POST('/app/json/member/setPayPass', params, config, true, false);
};
// 修改密码接口
export const resetPayPass = (params, config) => {
    return POST('/app/json/member/resetPayPass', params, config, true, false);
};
// 忘记密码接口
export const forgetPayPass = (params, config) => {
    return POST('/app/json/member/forgetPayPass', params, config, true, false);
};
// 获取验证码接口
export const memberSendVerifyCode = (params, config) => {
    return POST('/app/json/member/sendLoginVerifyCode', params, config);
};
// 校验手机号验证码接口
export const memberCheckVerifyCode = (params, config) => {
    return POST('/app/json/member/loginCheckVerifyCode', params, config);
};
// 获取验证码接口
export const sendVerifyCode = (params, config) => {
    return POST('/app/json/cnpc_card/sendVerifyCode', params, config);
};
// 重置密码接口
export const setPayPassword = (params, config) => {
    return POST('/app/json/cnpc_card/setPayPassword', params, config);
};
// 支付身份验证
export const payAuthentication = (params, config) => {
    return POST('/app/json/member/payAuthentication', params, config, true, false);
};

/***** 油卡相关接口 *****/
// 消费明细
export const getConsumeDetails = (params, config) => {
    return POST('/app/json/cnpc_card/getConsumeDetails', params, config, true, false);
};
// 油卡支付
// export const cardPay = (params, config, handleErrorFn) => {
//   return POST('/app/json/cnpc_card/cardPay', params, config, true, false, handleErrorFn)
// }
export const cardPay = (params, config, handleErrorFn) => {
    return POST('/app/json/card_fuel/cardPay', params, config, true, false, handleErrorFn);
};

/***** 版本相关接口 *****/
// 获取版本记录
export const getVersionList = (params, config) => {
    return POST('/app/json/version/versionList', params, config);
};

/**
 * 扫码记录接口
 */
// 记录商品扫码
export const material = (params, config) => {
    return POST('/app/json/statistics/material', params, config);
};

/**
 * 消息模板相关接口
 */
// 获取所有消息模板
export const getMsgTemplates = (params, config) => {
    return POST('/app/json/mini_message/getMsgTemplates', params, config);
};

/**
 * 全局配置接口
 */
// 获取配置
export const openCloseConfig = (params, config) => {
    return POST('/app/json/version/openCloseConfig', params, config);
};
// 获取首页轮播图接口

export const getBannerList = (params, config) => {
    return POST('/app/json/ad/getIndexAdData', params, config);
};

// 优惠合同接口
export const getContractDisInfo = (params, config) => {
    return POST('/app/json/contract/getContractDisInfo', params, config);
};
// 获取充值和折扣信息
export const getContractRuleInfo = (params, config) => {
    return POST('/app/json/contract/getContractRuleInfo', params, config);
};
// 提交折扣客户申请
export const commitDisContract = (params, config) => {
    return POST('/app/json/contract/commitDisContract', params, config);
};
// 油卡获取优惠合同信息
export const getContractInfo = (params, config) => {
    return POST('/app/json/contract/getContractInfo', params, config);
};
// 首页广告
export const getPopadvertisData = params => POST('/app/json/ad/getPopAdData', params);
// h5入口手机号加密接口
export const mobilePhoneNumberEncryption = (params, config) => {
    return POST('/app/json/ad/getEntrance', params, config);
};
export const getPayActivityInfo = (params, config) => {
    return POST('/app/json/fuel/getPayActivityInfo', params, config);
};
// 获取首页菜单
export const getMenuList = (params, config) => {
    return POST('/app/json/ad/getMenuList', params, config);
};
// 是否是新油站
export const isStationNew = (params, config) => {
    return POST('/app/json/third/isStationNew', params, config);
};
// 新用户迁移
export const userMigration = (params, config) => {
    return POST('/app/json/third/userMigration', params, config);
};
// 查询迁移结果
export const getUserMigrateFlag = (params, config) => {
    return POST('/app/json/third/getUserMigrateFlag', params, config);
};
// 获取3.0token
export const getThirdToken = (params, config) => {
    return POST('/app/json/third/getThirdToken', params, config);
};
// 获取用户协议和隐私协议 URI-->/third/getUserAgreement
export const getUserAgreementApi = (params, config) => {
    return POST('/app/json/third/getUserAgreement', params, config);
};

// 查询皮肤的详细信息   /third/skinDetail
export const skinDetailApi = (params, config) => {
    return POST('/app/json/third/skinDetail', params, config);
};
// 查询免费皮肤   /third/skinQueryFree
export const queryFreeApi = (params, config) => {
    return POST('/app/json/third/skinQueryFree', params, config);
};
//根据证件号查询电子卡信息
export const queryECardInfoByIdNo = (params, config) => {
    return POST('/app/json/third/queryECardInfo', params, config);
};

//获取图片验证码
export const getVerifyImgApi = (params, config) => {
    return POST('/app/json/member/getVerifyImg', params, config);
};
//  校验图片验证码
export const checkVerifyImgApi = (params, config) => {
    return POST('/app/json/member/checkVerifyImg', params, config);
};
// 用户openid加密接口()
export const openidEncryption = (params, config) => {
    return POST('/app/json/ad/encryptOpenid', params, config);
};
