import store from '../../../../store/index';
import { stationListApi } from '../../v3-http/https3/oilStationService/index';
import { getFuelGunByOrgCodeApi } from '../../v3-http/https3/oilStationService/index';
import { queryStoreInfoListByCodeList } from '../../v3-http/https3/o2o/index';
import Vue from 'vue';
import { handleTxMapTransBMap, bd09_To_Gcj02 } from '../../map.js';
import commonUtil from '../../commonUtil.js';
import cnpcBridge from '../../v3-native-jsapi/cnpcBridge';
import sKit from '../../index';
const { baseImgUrl } = require('../../../../../project.config');
const { osName, system } = uni.getSystemInfoSync();
import { clientCode, name } from '../../../../../project.config';
console.log(osName, '当前设备类型');
// #ifdef MP-WEIXIN
import bMap from '../../bmap-wx.js';
// #endif
let page = 1; // 页数
let pageSize = 10; // 每页多少个
export default {
    state: {
        // 下拉选选中的油站放置到当前数组中，在散装油去加油地图页面展示使用
        selectBulkOilStationList: [],
        // 下拉选选中的油站
        selectBulkOilStationObject: {},
        // 油站列表区分是否是散装油标识 0: 否； 1：是
        isBulkOilStationFlag: 0,
        // 填写预约申请单，选择油站使用的油站数组
        pullDownToSelectAnArray: [],
        // =================================================
        locationInfoObj: {
            latV3: '',
            lonV3: '',
            cityCode: '',
            cityName: '',
            provinceV3: '',
            cityV3: '',
            district: '',
        },
        //预约加油（1代表可以进行预约加油的油站；0代表不可以，不传代表搜索全部油站）
        bookingRefueling: '',
        refreshBulkOilListFlag: false,
    },
    mutations: {
        // 地图组件使用
        setSelectBulkOilStationList(state, info) {
            state.selectBulkOilStationList = info || [];
        },
        // 散装油提交订单组件组件使用
        setSelectBulkOilStationObject(state, info) {
            state.selectBulkOilStationObject = info || {};
        },
        // 油站列表区分是否是散装油标识
        setIsBulkOilStationFlag(state, Num) {
            state.isBulkOilStationFlag = Num;
            console.log(state.isBulkOilStationFlag, '散装油标识');
        },
        setPullDownToSelectAnArray(state, Arr) {
            state.pullDownToSelectAnArray = Arr;
        },
        // 取消散装油预约单返回到预约列表页面刷新列表
        setRefreshBulkOilList(state, Bool) {
            console.log('Bool', Bool);
            state.refreshBulkOilListFlag = Bool;
        },
        // ==========================================
        setLocationInformation(state, Obj) {
            state.locationInfoObj = Obj;
        },
    },
    actions: {
        async initLocationV3({ state, commit, dispatch }, suc) {
            // #ifdef MP-MPAAS
            handleMPAASLocation(state, commit, suc, dispatch);
            // #endif
            // #ifdef MP-WEIXIN
            await handleWeiXinLocation(state, commit, suc, dispatch);
            // #endif
            // #ifdef MP-ALIPAY
            handleAlipayLocation(state, commit, suc, dispatch);
            // #endif
            // #ifdef MP-TOUTIAO
            handleTtLocation(state, commit, suc, dispatch);
            // #endif
        },
        async getOnlineDistrictV3({ state, commit, dispatch }, suc) {
            // 从 state.locationInfoObj 中获取经纬度、城市信息等
            const { latV3, lonV3, cityCode, cityName, provinceV3, cityV3, district } = state.locationInfoObj;
            const params = { latV3, lonV3, cityCode, cityName, provinceV3, cityV3, district };
            // 保存位置信息
            commit('setLocationInformation', params);
            // 向缓存中存储位置信息
            uni.setStorageSync('locationInfo', { cityCode: cityCode, cityName: cityName });
            sKit.mpBP.setUserInfo();
            dispatch('getListOfGasStations', suc);
        },
        async getListOfGasStations({ state, commit, dispatch }, suc) {
            try {
                // 构建请求参数
                const params = buildStationListRequestParams(state);
                // 发起请求获取油站列表
                const locationRes = await stationListApi(params);
                console.log(locationRes, 'locationRes====');
                // 处理油站列表数据
                let stationList = [];
                if (locationRes.data.rows.length > 0) {
                    let arr = this.$sKit.layer.filterLocations(locationRes.data.rows);
                    stationList = processStationList(arr);
                }
                // 判断油站列表是否为空
                if (JSON.stringify(stationList) !== '[]') {
                    return;
                    // 重新组装油站列表的数据
                    const markerArr = await getAvailableMarketV3(stationList, [], state);
                    if (markerArr.length < pageSize) {
                        // commit('setLoadMortState', 'nomore');
                    } else {
                        // commit('setLoadMortState', 'loadmore');
                    }
                    return markerArr;
                } else {
                    // commit('setLoadMortState', 'nomore');
                    return [];
                }
            } catch (error) {
                console.error('获取油站列表失败:', error);
                return [];
            }
        },
    },
    getters: {},
};

// 处理APP和鸿蒙定位信息
function handleMPAASLocation(state, commit, suc, dispatch, handleTxMapTransBMap, system) {
    const isHarmony = isHarmonySystem(system);
    cnpcBridge.getLocation(async locationinfo => {
        state.locationState = locationinfo.state === 1;
        const mapdw = convertCoordinates(isHarmony, locationinfo.longitude, locationinfo.latitude, handleTxMapTransBMap);
        if (suc?.type === 'onlyLocation') {
            await setLocation(commit, locationinfo, mapdw);
            if (suc.callback && typeof suc.callback === 'function') {
                suc.callback({ latitude: mapdw.latitude, longitude: mapdw.longitude });
            }
            return;
        }
        await store.dispatch('getOnlineDistrictV3', suc);
    });
}
// 判断是否为 Harmony 系统
function isHarmonySystem() {
    return system.includes('Harmony');
}
// 进行经纬度转换
function convertCoordinates(isHarmony, longitude, latitude, handleTxMapTransBMap) {
    if (isHarmony) {
        const { longitude: newLongitude, latitude: newLatitude } = handleTxMapTransBMap(longitude, latitude);
        return { longitude: newLongitude, latitude: newLatitude };
    }
    return { longitude, latitude };
}
// 设置位置信息到state
async function setLocation(locationInfo, mapDw) {
    const cityName = locationInfo.province + locationInfo.city;
    let params = {
        latitude: mapDw.latitude,
        longitude: mapDw.longitude,
        cityCode: locationInfo.cityCode,
        cityName,
        provinceV3: locationInfo.province,
        cityV3: locationInfo.city,
        district: locationInfo.district || '',
    };
    // 保存位置信息
    store.commit('setLocationInformation', params);
}
// 处理微信小程序定位信息
async function handleWeiXinLocation(state, commit, suc, dispatch) {
    uni.getLocation({
        type: 'gcj02', // 火星坐标系
        isHighAccuracy: true,
        success: async res => {
            let wxLocationRes = {
                ...res,
                cityCode: state.cityCode,
                cityName: state.cityName,
                provinceV3: state.provinceV3,
                cityV3: state.cityV3,
            };
            if (state.cityCode === '' || state.cityName === '') {
                let BMap = new bMap.BMapWX({
                    ak: 'xOPSojXDUr6UHY6PZeRbTQK3CygUI2cl',
                });
                BMap.regeocoding({
                    fail: function () {
                        handleSuccess(state, commit, suc, dispatch, wxLocationRes);
                    },
                    success: async res => {
                        let resData = res.originalData;
                        if (resData.status === 0) {
                            let isDirectlyCity = ['北京市', '天津市', '上海市', '重庆市'].includes(resData.result.addressComponent.city);
                            let cityCode = isDirectlyCity
                                ? resData.result.addressComponent.adcode
                                : resData.result.addressComponent.adcode.substring(0, 4) + '00';
                            let cityName = resData.result.addressComponent.province + resData.result.addressComponent.city;
                            wxLocationRes.cityCode = cityCode;
                            wxLocationRes.cityName = cityName;
                            wxLocationRes.provinceV3 = resData.result.addressComponent.province;
                            wxLocationRes.cityV3 = resData.result.addressComponent.city;
                            wxLocationRes.district = resData.result.addressComponent.district;
                        }
                        await handleSuccess(state, commit, suc, dispatch, wxLocationRes);
                    },
                });
            } else {
                await handleSuccess(state, commit, suc, dispatch, wxLocationRes);
            }
        },
        fail: async err => {
            await handleFailure(state, commit, suc, dispatch, err);
        },
    });
}
// 处理支付宝小程序定位信息
function handleAlipayLocation(state, commit, suc, dispatch) {
    my.getLocation({
        type: 1,
        success: async res => {
            res.cityCode = ['北京市', '天津市', '上海市', '重庆市'].includes(res.city) ? res.districtAdcode : res.cityAdcode;
            res.cityName = res.province + res.city;
            res.provinceV3 = res.province;
            res.cityV3 = res.city;
            await handleSuccess(state, commit, suc, dispatch, res);
        },
        fail: async err => {
            await handleFailure(state, commit, suc, dispatch, err);
        },
    });
}
// 处理头条小程序定位信息
function handleTtLocation(state, commit, suc, dispatch) {
    tt.getLocation({
        type: 'gcj02', // 火星坐标系
        success: async res => {
            await handleSuccess(state, commit, suc, dispatch, res);
        },
        fail: async err => {
            handleFailure(state, commit, suc, dispatch, err);
        },
    });
}
// 处理成功回调
async function handleSuccess(state, commit, suc, dispatch, locationInfo) {
    state.locationState = true;
    const { longitude, latitude } = convertCoordinates(
        isHarmonySystem(system),
        locationInfo.longitude,
        locationInfo.latitude,
        handleTxMapTransBMap,
    );
    const mapDw = { longitude, latitude };
    if (suc?.type === 'onlyLocation') {
        await setLocation(commit, locationInfo, mapDw);
        if (suc.callback && typeof suc.callback === 'function') {
            suc.callback({ latitude, longitude });
        }
        return;
    }
    await store.dispatch('getOnlineDistrictV3', suc);
}
// 处理失败回调
async function handleFailure(state, commit, suc, dispatch, err) {
    state.locationState = false;
    if (err?.errMsg?.includes('频繁调用')) {
        return;
    }
    store.dispatch('zjShowModal', {
        title: '提示',
        content: '暂时无法获取定位，已为您选择默认城市',
        success: res => {
            if (res.confirm) {
                // 可添加确认后的逻辑
            } else if (res.cancel) {
                // 可添加取消后的逻辑
            }
        },
    });
    if (suc?.type === 'onlyLocation') {
        const defaultLocation = {
            latitude: '39.914579',
            longitude: '116.404421',
            cityCode: '110101',
            cityName: '北京市北京市',
            provinceV3: '北京市',
            cityV3: '北京市',
            district: '昌平区',
        };
        await setLocation(commit, defaultLocation, defaultLocation);
        if (suc.callback && typeof suc.callback === 'function') {
            suc.callback({ latitude: defaultLocation.latitude, longitude: defaultLocation.longitude });
        }
        return;
    }
    await store.dispatch('getOnlineDistrictV3', suc);
}
// 构建请求油站列表的参数
const buildStationListRequestParams = state => {
    console.log(store.state, 'store.state.locationInfoObj;');
    const { latV3, lonV3, cityCode, cityName, provinceV3, cityV3, district } = state.locationInfoObj;
    const { bookingRefueling } = store.state;
    const params = {
        longitude: lonV3 || '',
        latitude: latV3 || '',
        pageNum: page || '',
        pageSize: pageSize || '',
        distance: '10',
        bookingRefueling: bookingRefueling || '',
        mapType: '1',
        orgCode: store.state.wallet.walletInfo.addressNo || '',
    };
    if (store.state.bulkOil.isBulkOilStationFlag === 1) {
        params.bulkOilFlag = store.state.bulkOil.isBulkOilStationFlag;
    } else if (params.bulkOilFlag) {
        delete params.bulkOilFlag;
    }
    return params;
};
// 处理单个油站数据的逻辑
const processStationItem = (item, index) => {
    item.id = Number(item.stationId);
    item.markerId = Number(item.stationId);
    item.time =
        item.businessStartTime && item.businessEndTime
            ? `${commonUtil.timeSplit(item.businessStartTime)}-${commonUtil.timeSplit(item.businessEndTime)}`
            : '';
    item.isFirst = index === 0;
    // #ifndef MP-MPAAS
    // 转换经纬度为高德地图经纬度
    const mapArr = bd09_To_Gcj02(item.longitude, item.latitude);
    item.longitude = mapArr.longitude;
    item.latitude = mapArr.latitude;
    // #endif
    return item;
};
// 处理油站列表数据的逻辑
const processStationList = stationList => {
    return stationList.map((item, index) => processStationItem(item, index));
};
