<template>
    <div class="add-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="填写发票"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div class="add-section">
            <div class="main-contet">
                <div class="reasons" @click="isShowNum = true">
                    <div class="left label-txt">发票数量</div>
                    <div class="center label-txt title-bold"
                        >共{{ invoiceList.length }}张，共计
                        <span class="color-font">¥{{ priceSum + (isInteger(priceSum) ? '.00' : '') }}</span></div
                    >
                    <div class="right">
                        <img src="@/static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix" />
                    </div>
                </div>
                <div class="flow-container">
                    <div class="section">
                        <p class="add-title">发票详情</p>
                        <div class="table-view card form">
                            <div class="table-view-cell">
                                <div class="table-view-cell-text">
                                    <div class="left info-left">抬头类型</div>
                                    <div class="center center-flex">
                                        <div
                                            class="center-info"
                                            @click="seletedindex(index)"
                                            v-for="(item, index) in titleList"
                                            :key="index"
                                        >
                                            <img
                                                :src="index == activeindex ? '/static/select-icon.png' : '/static/selected-icon.png'"
                                                alt=""
                                                mode="widthFix"
                                            />
                                            <div class="title">{{ item.name }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <template v-if="activeindex == 0">
                                <div class="table-view-cell">
                                    <div class="table-view-cell-text">
                                        <div class="left info-left">发票抬头</div>
                                        <input class="center info-right" placeholder="请输入发票抬头" v-model="invoiceInfo.title" />
                                        <div class="right">
                                            <img
                                                src="@/static/list-icon.png"
                                                alt=""
                                                class="list-img"
                                                mode="widthFix"
                                                @click="ShowtitleTap"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div class="table-view-cell">
                                    <div class="table-view-cell-text">
                                        <div class="left info-left">企业税号</div>
                                        <input class="center info-right" placeholder="填写纳税人识别号" v-model="invoiceInfo.titlenumber" />
                                    </div>
                                </div>

                                <template v-if="moreShow">
                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left">企业地址</div>
                                            <input class="center info-right" placeholder="填写公司地址" v-model="invoiceInfo.workaddr" />
                                        </div>
                                    </div>

                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left-mar">企业电话</div>
                                            <input class="center info-right" placeholder="填写公司电话" v-model="invoiceInfo.phonenumber" />
                                        </div>
                                    </div>

                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left-mar">开户银行</div>
                                            <input class="center info-right" placeholder="填写开户银行" v-model="invoiceInfo.bank" />
                                        </div>
                                    </div>

                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left-mar">开户账号</div>
                                            <input class="center info-right" placeholder="填写银行账号" v-model="invoiceInfo.banknumber" />
                                        </div>
                                    </div>
                                </template>
                                <div class="table-view-cell">
                                    <div class="table-view-cell-text">
                                        <div class="center flex-center" @click="updownbtn" v-if="downshow">
                                            <span class="margin-10">收起</span>
                                            <u-icon name="arrow-up" size="16" color="#909090"></u-icon>
                                        </div>
                                        <div class="center flex-center" @click="updownbtn" v-if="!downshow">
                                            <span class="margin-10">更多（开户行等）</span>
                                            <u-icon name="arrow-down" size="16" color="#909090"></u-icon>
                                        </div>
                                    </div>
                                </div>
                            </template>

                            <template v-if="activeindex == 1">
                                <div class="table-view-cell">
                                    <div class="table-view-cell-text">
                                        <div class="left info-left">个人姓名</div>
                                        <input class="center info-right" placeholder="填写开票人姓名" v-model="invoiceInfo.username" />
                                        <div class="right">
                                            <img
                                                src="@/static/list-icon.png"
                                                alt=""
                                                class="list-img"
                                                mode="widthFix"
                                                @click="ShowtitleTap"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <template v-if="moreShowone">
                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left">联系电话</div>
                                            <input class="center info-right" placeholder="填写开票人电话" v-model="invoiceInfo.userphone" />
                                        </div>
                                    </div>
                                    <div class="table-view-cell">
                                        <div class="table-view-cell-text">
                                            <div class="left info-left">家庭地址</div>
                                            <input class="center info-right" placeholder="填写开票人住址" v-model="invoiceInfo.homeaddr" />
                                        </div>
                                    </div>
                                </template>
                                <div class="table-view-cell">
                                    <div class="table-view-cell-text">
                                        <div class="center flex-center" @click="updownbtnone" v-if="downshowone">
                                            <span class="margin-10">收起</span>
                                            <u-icon name="arrow-up" size="16" color="#909090"></u-icon>
                                        </div>
                                        <div class="center flex-center" @click="updownbtnone" v-if="!downshowone">
                                            <span class="margin-10">更多（地址电话）</span>
                                            <u-icon name="arrow-down" size="16" color="#909090"></u-icon>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <!-- 后台未实现 后期放开就行 搜索email 放开所有注释功能就恢复了 -->
                    <!-- <div class="section">
						<p class="add-title">接收方式</p>
						<div class="table-view form card">
							<div class="table-view-cell">
								<div class="table-view-cell-text">
									<div class="left info-left">电子邮箱</div>
									<input class="center info-right" v-model="invoiceInfo.email" placeholder="填写接收发票的邮箱地址" />
								</div>
							</div>
						</div>
					</div> -->
                </div>
            </div>
        </div>
        <div class="add-footer">
            <div class="footer-btn" @click="submit(activeindex)">提交</div>
        </div>

        <!-- 发票数量弹窗 -->
        <u-popup v-model="isShowNum" mode="bottom" border-radius="20" height="680rpx">
            <div class="invoice-mask">
                <div class="mask-title">
                    <div class="title-left">发票数量共{{ invoiceList.length }}张</div>
                    <u-icon size="22" name="close" @click="isShowNum = false" color="#979797"></u-icon>
                </div>
                <scroll-view class="mask-scroll-view" scroll-y>
                    <div class="mask-view" style="padding-bottom: env(safe-area-inset-bottom)">
                        <div class="mask-list" style="margin-bottom: 10px" v-for="(item, index) in invoiceList" :key="index">
                            <div class="reasons-cell">
                                <div class="label-txt">发票内容</div>
                                <div style="flex: 1"></div>
                                <div class="label-text title-bold">*汽油*车用汽油</div>
                            </div>
                            <div class="reasons-cell">
                                <div class="label-txt">开票方</div>
                                <div style="flex: 1"></div>
                                <div class="label-text title-bold"> 中国石油天然气集团有限公司 </div>
                            </div>

                            <div class="reasons-cell">
                                <div class="label-txt">发票金额</div>
                                <div style="flex: 1"></div>
                                <div class="label-txt title-bold"> ¥{{ item.realAmt + (isInteger(item.realAmt) ? '.00' : '') }} </div>
                            </div>
                        </div>
                    </div>
                </scroll-view>
            </div>
        </u-popup>
        <!--    企业发票抬头弹窗-->
        <u-popup v-model="isShowtitle" mode="bottom" border-radius="20" height="680rpx">
            <div class="invoice-mask">
                <div class="mask-title">
                    <div class="title-left">发票抬头</div>
                    <u-icon size="22" name="close" @click="isShowtitle = false" color="#979797"></u-icon>
                </div>
                <div class="mask-view flex-list" style="padding: 0">
                    <scroll-view class="main-scroll" scroll-y="true" style="margin-left: 15px">
                        <div class="mask-main" v-if="activeindex == 0">
                            <div
                                class="mask-list mask-bg mask-flex"
                                v-for="(item, index) in companyTitleList"
                                :key="item.id"
                                :data-item="item"
                                @click.stop="comptap(item, activeindex, index)"
                                :style="{
                                    border: selectCOInvoiceIndex == index ? '0.5px solid #FF8200' : '0.5px solid #DCDCDC',
                                    backgroundColor: selectCOInvoiceIndex == index ? '#FFF6EC' : '#ffffff',
                                }"
                            >
                                <div class="mask-cell">
                                    <div class="label-txt font15 title-bold"
                                        >{{ item.invoicetitle }}
                                        <span v-if="codefIndex == item.id" class="mask-default">默认</span>
                                    </div>
                                    <div class="label-txt">税号：{{ item.taxcode }}</div>
                                    <!--                <div class="label-txt">常用邮箱：<EMAIL></div>-->
                                </div>
                            </div>
                        </div>
                        <div class="mask-main" v-if="activeindex == 1">
                            <div
                                class="mask-list mask-flex"
                                v-for="(item, index) in personalTitleList"
                                :key="item.id"
                                :data-item="item"
                                @click.stop="comptap(item, activeindex, index)"
                                :style="{
                                    border: selectPEInvoiceIndex == index ? '0.5px solid #FF8200' : '0.5px solid #DCDCDC',
                                    backgroundColor: selectPEInvoiceIndex == index ? '#FFF6EC' : '#ffffff',
                                }"
                            >
                                <div class="mask-cell">
                                    <div class="label-txt font15 title-bold"
                                        >{{ item.invoicetitle }}
                                        <span v-if="pedefIndex == item.id" class="mask-default">默认</span>
                                    </div>
                                    <!--                <div class="label-txt">常用邮箱：<EMAIL></div>-->
                                </div>
                            </div>
                        </div>
                    </scroll-view>
                    <div class="add-titlebtn-view">
                        <div class="add-titlebtn" @click="addTitletap($event, index)" :data-activeindex="activeindex">添加常用发票抬头</div>
                    </div>
                </div>
            </div>
        </u-popup>
        <!-- 开票成功弹窗 -->
        <uni-pop ref="sucpopup" type="center" :maskClick="false">
            <div class="suc-pop-view">
                <div class="suc-pop-content">
                    <img class="suc-pop-icon" src="@/static/homeIcon/success-pay.png" />
                    <div class="suc-pop-title">开票完成</div>
                    <div class="suc-pop-detail">您的开票申请已提交，可在开票历史中查看历史开票</div>
                </div>
                <u-icon
                    @click="clickCloseSucPop"
                    name="close-circle-fill"
                    :custom-style="{
                        marginTop: '40rpx',
                    }"
                    color="#ffffff"
                    size="92"
                ></u-icon>
            </div>
        </uni-pop>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import uniPop from '@/components/uni-popup/uni-popup.vue';
import pageConfig from '@/utils/pageConfig';
import { getCompanyTitleListPost, getPersonalTitleListPost, getUserEmail } from '@/api/my-center.js';
import { billingInvoice, billingAgain } from '@/api/home.js';
export default {
    name: 'main',
    components: { uniPop },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            invoiceInfo: {
                id: '',
                title: '',
                titlenumber: '',
                workaddr: '',
                phonenumber: '',
                bank: '',
                banknumber: '',
                email: '',
                username: '',
                userphone: '',
                homeaddr: '',
            }, // 页面输入数据
            titleList: [
                {
                    name: '企业单位',
                },
                {
                    name: '个人/非企业单位',
                },
            ],
            activeindex: 0,
            downshow: false,
            moreShow: false,
            downshowone: false,
            moreShowone: false,
            isShowNum: false,
            isShowtitle: false,
            companyTitleList: [], // 公司抬头数组
            personalTitleList: [], // 个人抬头数组
            invoiceList: [], // 要开票的订单
            selectPEInvoiceIndex: -1, // 选中的默认抬头（个人）
            selectCOInvoiceIndex: -1, // 选中的默认抬头（公司）
            pedefIndex: -1, // 默认（个人）
            codefIndex: -1, // 默认（公司）
            priceSum: 0, // 发票总价
            orderType: 2, // 开票订单类型 1为充值 2为加油
            isReopen: false, //是否为重开
            /////////////////////////////\
            rechargeOrder: '', //充值订单
            invoiceDetail: {}, //充值订单的传过来的发票抬头详情
            invoiceTitleId: '',
        };
    },
    onLoad(options) {
        if (options) {
            this.invoiceList = JSON.parse(decodeURIComponent(options.list));
            //重开发票 reopen
            console.log('订单信息', this.invoiceList);
            if (options.rechargeOrder == 1) {
                console.log('发票抬头信息', JSON.parse(options.invoiceDetail));
                this.invoiceDetail = JSON.parse(options.invoiceDetail);
                this.orderType = options.rechargeOrder == 1 ? 1 : 2;
                this.priceSum = this.invoiceList[0].amount;
                this.activeindex = this.invoiceDetail.islogo == '1' ? 0 : 1;
                if (this.invoiceDetail.islogo == '1') {
                    this.invoiceInfo.title = this.invoiceDetail.invoicetitle;
                    this.invoiceInfo.titlenumber = this.invoiceDetail.taxcode;
                    this.invoiceInfo.workaddr = this.invoiceDetail.addresstax;
                    this.invoiceInfo.bank = this.invoiceDetail.openingbank;
                    this.invoiceInfo.banknumber = this.invoiceDetail.bankaccount;
                    this.invoiceInfo.phonenumber = this.invoiceDetail.telephone;
                    this.invoiceInfo.id = this.invoiceList[0].orderId;
                    this.invoiceTitleId = this.invoiceDetail.id;
                } else {
                    this.invoiceInfo.username = this.invoiceDetail.invoicetitle;
                    this.invoiceInfo.userphone = this.invoiceDetail.telephone;
                    this.invoiceInfo.homeaddr = this.invoiceDetail.addresstax;
                    this.invoiceInfo.id = this.invoiceList[0].orderId;
                    this.invoiceTitleId = this.invoiceDetail.id;
                }
                return;
            }
            this.isReopen = options.reopen ? true : false;
            if (this.isReopen) {
                this.priceSum = this.invoiceList[0].amount;
            } else {
                let sum = 0;
                for (let i = 0; i < this.invoiceList.length; i++) {
                    sum = sum + this.invoiceList[i].realAmt;
                }
                this.orderType = options.ordertype == 'recharge' ? 1 : 2;
                this.priceSum = sum.toFixed(2);
            }
        } else {
        }
    },
    async onShow() {
        await this.getPersonalTitleList();
        await this.getCompanyTitleList();
        let pedefIndex = this.$Storage.defaultPersonalInvoice.value; // 默认的抬头id（个人）
        let codefIndex = this.$Storage.defaultEnterpriseInvoice.value; // 默认的抬头id（公司）

        // 如果没有默认抬头则获取上次填写的抬头编号
        pedefIndex = pedefIndex == -1 ? this.$Storage.prevPersonalInvoice.value : pedefIndex;
        codefIndex = codefIndex == -1 ? this.$Storage.prevEnterpriseInvoice.value : codefIndex;

        this.pedefIndex = pedefIndex;
        this.codefIndex = codefIndex;
        let selectPEInvoiceIndex = pedefIndex != -1 ? this.personalTitleList.findIndex(item => item.id == pedefIndex) : pedefIndex;
        let selectCOInvoiceIndex = codefIndex != -1 ? this.companyTitleList.findIndex(item => item.id == codefIndex) : codefIndex;
        this.comptap(this.companyTitleList[selectCOInvoiceIndex], 0, selectCOInvoiceIndex);
        this.comptap(this.personalTitleList[selectPEInvoiceIndex], 1, selectPEInvoiceIndex);
        // let res = await getUserEmail()
        // this.invoiceInfo.email = res.data
    },
    onReady() {},
    methods: {
        // 判断是否是整数
        isInteger(obj) {
            return Number.isInteger(obj);
        },
        updownbtn() {
            this.moreShow = !this.moreShow;
            this.downshow = !this.downshow;
        },
        updownbtnone() {
            this.moreShowone = !this.moreShowone;
            this.downshowone = !this.downshowone;
        },
        seletedindex(index) {
            this.activeindex = index;
        },
        ShowtitleTap() {
            this.isShowtitle = true;
        },
        //填写发票信息
        async comptap(item, index, itemIndex) {
            let iteminfo = item;
            if (iteminfo) {
                if (index == 0) {
                    const that = this;
                    this.selectCOInvoiceIndex = itemIndex;
                    that.invoiceInfo.title = iteminfo.invoicetitle;
                    that.invoiceInfo.titlenumber = iteminfo.taxcode;
                    that.invoiceInfo.workaddr = iteminfo.addresstax;
                    that.invoiceInfo.bank = iteminfo.openingbank;
                    that.invoiceInfo.banknumber = iteminfo.bankaccount;
                    that.invoiceInfo.phonenumber = iteminfo.telephone;
                    // that.invoiceInfo.id = iteminfo.id
                    that.invoiceTitleId = iteminfo.id;
                } else {
                    const that = this;
                    this.selectPEInvoiceIndex = itemIndex;
                    that.invoiceInfo.username = iteminfo.invoicetitle;
                    that.invoiceInfo.userphone = iteminfo.telephone;
                    that.invoiceInfo.homeaddr = iteminfo.addresstax;
                    // that.invoiceInfo.id = iteminfo.id
                    that.invoiceTitleId = iteminfo.id;
                }
                this.isShowtitle = false;
            }
        },

        //跳转发票抬头
        addTitletap(e) {
            let item = e.target.dataset.activeindex;
            console.log(item);
            uni.navigateTo({
                url: '/packages/invoice-center/pages/add-invoice-title/main?itemdata=' + item + '&type=add',
            });
        },
        // 点击开票成功关闭弹窗
        clickCloseSucPop() {
            this.$refs.sucpopup.close();
            uni.$emit('uploadPage', {});
            uni.navigateBack();
        },

        async submit(active) {
            console.log(active, this.invoiceInfo);
            // 发票是否是企业单位
            const invoiceIsEnterprise = active == 0;
            if (invoiceIsEnterprise) {
                if (!this.invoiceInfo.title) {
                    uni.showToast({
                        title: '请输入发票抬头',
                        icon: 'none',
                    });
                    return;
                }
                if (!this.invoiceInfo.titlenumber && !/^[A-Z0-9]{10,18}$/.test(this.invoiceInfo.titlenumber)) {
                    uni.showToast({
                        title: '请输入正确税号',
                        icon: 'none',
                    });
                    return;
                }
            } else {
                if (!/^[\u4E00-\u9FA5]{2,4}$/.test(this.invoiceInfo.username)) {
                    return uni.showToast({
                        title: '请输入正确的姓名',
                        icon: 'none',
                    });
                }
            }

            // if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.invoiceInfo.email)) {
            // 	return uni.showToast({
            // 		title: "请输入正确的邮箱号",
            // 		icon: "none",
            // 	});
            // }

            let invoiceInfo = this.invoiceInfo;

            // 修改上一次的发票抬头编号
            this.$Storage[invoiceIsEnterprise ? 'prevEnterpriseInvoice' : 'prevPersonalInvoice'].update(invoiceInfo.id);
            let params = {
                type: this.orderType,
                // invoiceTitleId: invoiceInfo.id == undefined ? '' : invoiceInfo.id,
                invoiceTitleId: this.invoiceTitleId,
                islogo: this.activeindex ? 2 : 1,
                id: this.invoiceList[0].id ? this.invoiceList.map(item => item.id).join(',') : invoiceInfo.id,
                invoicetitle: this.activeindex ? invoiceInfo.username : invoiceInfo.title,
                taxcode: this.activeindex ? '' : invoiceInfo.titlenumber,
                addresstax: this.activeindex ? invoiceInfo.homeaddr : invoiceInfo.workaddr,
                telephone: this.activeindex ? invoiceInfo.userphone : invoiceInfo.phonenumber,
                openingbank: this.activeindex ? '' : invoiceInfo.bank,
                bankaccount: this.activeindex ? '' : invoiceInfo.banknumber,
                businebsscard: '',
                // email: invoiceInfo.email
            };
            if (this.isReopen && this.invoiceList[0].state == 1) {
                params.invoiceHisId = this.invoiceList[0].id;
                params.id = this.invoiceList[0].orderId;
                uni.showModal({
                    title: '',
                    content: '您提交的申请将由人工审核，每张发票所包含的订单只能完成一次重开。重开后原发票将被作废，无法进行报销',
                    success: function (res) {
                        if (res.confirm) {
                            billingAgain(params).then(result => {
                                if (result.status == 0) {
                                    this.$refs.sucpopup.open();
                                } else {
                                    uni.showToast({
                                        title: result.info,
                                        icon: 'none',
                                    });
                                }
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                let res = await billingInvoice(params);
                if (res.status == 0) {
                    this.$refs.sucpopup.open();
                } else {
                    uni.showToast({
                        title: res.info,
                        icon: 'none',
                    });
                }
            }
        },
        //  获取【企业】发票抬头列表
        async getCompanyTitleList() {
            let res = await getCompanyTitleListPost();
            this.companyTitleList = res.data;
        },
        //获取【个人】发票抬头列表
        async getPersonalTitleList() {
            let res = await getPersonalTitleListPost();
            this.personalTitleList = res.data;
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 15px;
$colorgray: #909090;

.suc-pop-view {
    display: flex;
    flex-direction: column;
    align-items: center;
    .suc-pop-content {
        border-radius: 10px;
        width: 345px;
        height: 235px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;
        .suc-pop-icon {
            width: 90px;
            height: 90px;
        }
        .suc-pop-title {
            margin-top: 15px;
            line-height: 36px;
            font-size: 24px;
            color: #141414;
            font-weight: 700;
        }
        .suc-pop-detail {
            line-height: 36px;
            font-size: 12px;
            color: #141414;
            font-weight: 700;
        }
    }
}

.mask-scroll-view {
    height: 289.5px;
}

.add-center {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    display: flex;
    flex-direction: column;

    .add-section {
        font-size: $font14;
        color: $colorgray;
        flex: 1;
        position: relative;
        padding-bottom: calc(64px + env(safe-area-inset-bottom));
        .main-scroll {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
        }

        .main-contet {
            padding: 10px 15px;
        }

        .reasons {
            display: flex;
            background: #ffffff;
            border-radius: 5px;
            padding: 13px 10px;

            .left {
                margin-right: 30px;
            }

            .center {
                flex: 1;
            }
        }

        .section {
            padding: 10px 0;

            .add-title {
                font-size: 15px;
                font-weight: bold;
                color: #909090;
                padding-bottom: 10px;
            }

            .table-view-cell {
                padding: 13px 10px;
            }
        }

        .center {
            margin-left: 15px;
        }

        .center-flex {
            display: flex;

            .center-info {
                overflow: hidden;

                img {
                    width: 20px;
                    height: 20px;
                    display: inline-block;
                    margin-right: 5px;
                }

                .title {
                    font-size: $font14;
                    display: inline-block;
                    vertical-align: middle;
                }
            }

            .center-info:nth-child(2) {
                margin-left: 15px;
            }
        }
    }

    .add-footer {
        width: 100%;
        background: #ffffff;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 1;
        padding-bottom: env(safe-area-inset-bottom);

        .footer-btn {
            margin: 10px 15px;
            width: 345px;
            height: 44px;
            background: $btn-color;
            border-radius: 5px;
            font-size: 15px;
            text-align: center;
            font-weight: bold;
            color: #ffffff;
            line-height: 44px;
            margin: 12px auto 9px;
        }
    }

    .invoice-mask {
        .mask-title {
            display: flex;
            padding: 15px 21px 15px 15px;
            align-items: center;
            border-bottom: 0.5px solid #dcdddd;

            .title-left {
                color: #222222;
                font-size: 15px;
                font-weight: 700;
                flex: 1;
            }
        }
    }

    .mask-view {
        padding: 10px 15px;

        .mask-list {
            padding: 9px 10px 12px;
            background: #ffffff;
            border-radius: 5px;
            border: 0.5px solid #dcdcdc;

            .reasons-cell {
                display: flex;
            }

            .mask-default {
                vertical-align: middle;
                width: 30px;
                height: 16px;
                background: #edffef;
                border-radius: 3px;
                border: 0.5px solid $btn-color;
                line-height: 15px;
                text-align: center;
                font-size: 10px;
                color: $btn-color;
                display: inline-block;
                margin-left: 8px;
            }
        }

        .mask-bg {
            border-radius: 5px;
            border: 0.5px solid $btn-color;
        }

        .mask-flex {
            display: flex;
            width: 345px;
            margin-bottom: 10px;
        }
    }

    .flex-list {
        display: flex;
        flex-direction: column;

        .main-scroll {
            height: calc(289.5px - env(safe-area-inset-bottom) - 64px);
            width: 345px;
            .mask-main {
                padding-top: 10px;
                padding-bottom: 5px;
            }
        }
        .add-titlebtn-view {
            position: absolute;
            bottom: 0;
            padding-bottom: env(safe-area-inset-bottom);
            background-color: #ffffff;
            .add-titlebtn {
                width: 345px;
                background: $btn-color;
                margin: 10px 15px;
                border-radius: 5px;
                font-size: 15px;
                text-align: center;
                font-weight: bold;
                color: #ffffff;
                line-height: 44px;
            }
        }
    }

    .label-txt {
        font-size: $font14;
        padding-bottom: 4px;
    }

    .font15 {
        font-size: $font15;
    }

    .title-bold {
        font-weight: bold;
        vertical-align: middle;
    }

    .color-font {
        color: $btn-color;
    }

    .arrow-img {
        width: 5px;
    }

    .list-img {
        padding-left: 10px;
        width: 13px;
        height: 12px;
    }

    .margin-10 {
        margin-right: 10px;
    }

    .flex-center {
        display: inline-block;
        text-align: center;
    }

    .card {
        box-shadow: none;
    }

    ::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
    }
}
</style>
