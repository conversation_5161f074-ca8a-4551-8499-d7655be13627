<template>
    <div class="container">
        <div class="mask">
            <div class="protocol-wrap">
                <div class="protocol-container" :style="{ backgroundImage: `url(${popupBg})` }">
                    <div class="protocol-title">能源e站隐私政策</div>
                    <div class="protocol-characters">
                        感谢您对能源e站一直以来的信任！为更好地保护您的个人信息安全，请您仔细阅读并理解我们最新更新的
                        <span @click="openProtocol" class="underline">《能源e站隐私政策》。</span>
                        若您点击“同意”，即表示您已阅读并同意上述条款，能源e站将严格按照
                        <span @click="openProtocol" class="underline">《能源e站隐私政策》</span>
                        的各项条款使用和保护您的个人信息。若您点击“不同意”，可以选择浏览模式或退出。
                    </div>
                    <div class="btn-wrap">
                        <button
                            class="popup-content-btn"
                            open-type="agreePrivacyAuthorization"
                            @agreeprivacyauthorization="handleAgreePrivacyAuthorization('confirm')"
                            >同意</button
                        >
                        <button @click="cancel('cancel')" class="popup-content-btn-next">不同意，仅浏览</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    data() {
        return {
            popupBg: require('./image/privacy.png'),
        };
    },
    computed: {
        ...mapState({
            officialAccountParams: state => state.location.officialAccountParams, //公众号跳转参数
        }),
    },
    methods: {
        handleAgreePrivacyAuthorization(res) {
            this.$store.commit('hidePrivacy');
            this.$store.commit('setGetPhoneNumber', res);
        },
        // 打开协议
        openProtocol() {
            wx.openPrivacyContract({
                success: () => {}, // 打开成功
                fail: () => {}, // 打开失败
            });
        },
        cancel(res) {
            this.$store.commit('hidePrivacy');
            this.$store.commit('setGetPhoneNumber', res);
        },
    },
};
</script>

<style scoped lang="scss">
.container {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 999; /* 设置遮罩层位于其他元素之上 */
    opacity: 1;
    /* background: red; */
}
.mask {
    top: 0;
    left: 0;
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    .protocol-wrap {
        width: 532rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: #ffffff;
        border-radius: 8px;
        .protocol-container {
            width: 100%;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            .protocol-title {
                display: flex;
                justify-content: center;
                font-size: 16px;
                color: #333;
                font-weight: bold;
                margin-top: 20px;
                margin-bottom: 20px;
            }
            .protocol-characters {
                color: #999999;
                padding: 0 30px;
                .underline {
                    color: #e64f22;
                }
            }
            .btn-wrap {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 100%;
                margin-top: 15px;
                margin-bottom: 15px;
                .popup-content-btn {
                    width: 85%;
                    height: 38px;
                    line-height: 38px;
                    background: #e64f22;
                    font-size: 30rpx;
                    font-weight: 500;
                    color: #ffffff;
                }

                .popup-content-btn-next {
                    margin-top: 28rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 42rpx;
                }
            }
            .protocol-characters::before {
                content: '\00a0\00a0\00a0\00a0\00a0\00a0\00a0\00a0';
            }
        }
    }
}
</style>
