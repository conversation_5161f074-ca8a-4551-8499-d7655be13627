<template>
    <div class="view targetElement">
        <div class="popup-box padding-16">
            <div class="topTitle weight-bold font-18 color-333 te-center">安全验证</div>
            <div class="explain fl-column fl-al-jus-cen color-E64F22 font-12 weight-400 te-center">
                <div> 为了保护您的账号安全 </div>
                <div>请本人进行实名刷脸验证</div>
            </div>
            <div>
                <div class="section-box">
                    <div class="left">姓名</div>
                    <div class="right">
                        <input type="text" class="textarea_style" v-model="params.name" placeholder="请输入真实姓名" />
                    </div>
                </div>
                <div class="section-box">
                    <div class="left">身份证号</div>
                    <div class="right">
                        <input type="text" class="textarea_style" v-model="params.idNumber" maxlength="18"
                            placeholder="请输入真实身份证号" />
                    </div>
                </div>
            </div>
            <div class="agreement_div">
                <div class="fl-row fl-al-cen" @click="changeSelectShow">
                    <img v-if="selectShow" src="../../../image/empty.png" alt="" />
                    <img v-else src="../../../image/successSel.png" alt="" />
                    <div class="box_first font-12 color-999">我已阅读并同意</div>
                </div>
                <div class="font-12 weight-400;">
                    <div class="box2 font-12 color-E64F22" @click="checkAgr()">《认证协议》</div>
                </div>
            </div>
            <div class="btnWrap fl-column fl-al-jus-cen">
                <div class="primary-btn color-fff weight-bold font-15 te-center border-rad-8" @click="dateConfirm()">确认
                </div>
                <div class="btn-plain-tran color-999 weight-400 font-15 te-center border-rad-8" @click="dateCancle()">取消
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { userAgreement } from '../../../js/v3-http/https3/oilCard/index';
import { mapState, mapGetters } from 'vuex';
export default {
    // name: "dialog",
    name: 'input-Popup',
    data() {
        return {
            selectShow: true,
            webUrl: 'https://render.alipay.com/p/yuyan/180020010001196791/preview.html?agreementId=AG01000130',
            params: {
                name: '',
                idNumber: '',
            },
        };
    },
    props: {},
    methods: {
        changeSelectShow(val) {
            this.selectShow = !this.selectShow;
        },
        dateConfirm() {
            if (!this.params.name) {
                // this.$Toast('请输入真实名称');
                uni.showToast({ title: '请输入真实姓名', icon: 'none' });
                return;
            } else if (this.params.name !== '' && !this.$test.checkName(this.params.name)) {
                uni.showToast({
                    title: '姓名格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.params.idNumber) {
                // this.$Toast("证件号不能为空");
                uni.showToast({ title: '身份证号不能为空', icon: 'none' });
                return;
            }
            if (!this.$test.newCheckIdNumber(this.params.idNumber)) {
                // this.$Toast("身份证号格式错误");
                uni.showToast({ title: '身份证号格式错误', icon: 'none' });
                return;
            }
            if (this.selectShow) {
                uni.showToast({
                    title: '请勾选协议',
                    icon: 'none',
                });
                return;
            }
            this.$emit('realNameInfo', this.params);
        },
        dateCancle() {
            this.$emit('realNameDialogClose');
        },
        // #ifdef MP-MPAAS
        checkAgrUrl() {
            this.$cnpcBridge.openModule({
                type: 'web',
                url: this.webUrl,
            });
        },
        checkAgr() {
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: '1',
                    cityName: res.cityCode,
                    name: '人脸识别认证协议',
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    } else {
                        uni.showToast({ title: '未找到该协议' });
                    }
                } else {
                    uni.showToast({ title: userAgreementRes.message });
                }
            });
        },
        // #endif
        // #ifndef MP-MPAAS
        async checkAgr() {
            let params = {
                type: '1',
                cityName: '全国',
                name: '人脸识别认证协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // uni.navigateTo({
                    //     url: '/packages/web-view/pages/home/<USER>' + encodeURIComponent(userAgreementRes.data.fileUrl),
                    // });
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            } else {
                uni.showToast({ title: userAgreementRes.message });
            }
        },
        // #endif
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    // #ifdef H5-CLOUD
    z-index: 100;
    // #endif
    // #ifndef H5-CLOUD
    z-index: 9999;
    // #endif

    .popup-box {
        position: relative;
        width: 570rpx;
        height: 750rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, calc(-50% - 73rpx));
        background: #fff;
        border-radius: 16rpx 16rpx;

        .topTitle {
            height: 46rpx;
            line-height: 46rpx;
            margin-top: 39rpx;
        }

        .explain {
            height: 64prx;
            line-height: 32rpx;
            margin-top: 23rpx;
        }

        .info-class {
            color: #e64f22;
            font-size: 12px;
            padding: 10px 0px;
        }

        .section-box {
            // background: #f7f7fb;
            border-radius: 16rpx;
            height: 94rpx;
            line-height: 94rpx;
            display: flex;
            align-items: center;
            padding-left: 15px;
            margin-top: 10px;
            border-radius: 4px;
            background-color: #f7f7f7;

            .left {
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: #333;
                min-width: 140rpx;

                .required {
                    color: #e64f22;
                    font-size: 14px;
                }
            }

            .right {
                flex: 1;
                height: 100%;
                display: flex;
                justify-content: center;
                align-content: center;

                input {
                    background-color: #f7f7f7;
                    line-height: 44px;
                    height: 100%;
                }

                .textarea_style {
                    width: 100%;
                    // text-align: right;
                    // min-height: 40rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 88rpx;
                    padding: 0;
                }
            }

            .type {
                display: flex;
                justify-content: flex-end;

                div:first-child {
                    margin-right: 56rpx;
                }
            }
        }

        .agreement_div {
            display: flex;
            width: 100%;
            margin-top: 59rpx;

            img {
                width: 13px;
                height: 13px;
            }

            .agreement {
                width: 200px;
                font-size: 12px;
                font-weight: 400;
                color: #999999;
                line-height: 17px;
                display: flex;

                a {
                    color: #e64f22;
                }
            }

            .box {
                display: inline-block;
                width: 100%;
                white-space: pre-wrap;
                margin-top: -2px;
                margin-left: 3px;
                word-break: break-all;
                overflow-wrap: break-word;
            }

            .box_first {
                float: left;
                line-height: 17px;
                margin-left: 5px;
            }

            .box2 {
                display: inline;
                line-height: 17px;
            }
        }

        .btnWrap {
            width: 100%;
            margin-top: 28rpx;

            div {
                width: 100%;
                height: 88rpx;
                line-height: 88rpx;
            }

            &:nth-child(1) {
                box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            }
        }
    }
}</style>
