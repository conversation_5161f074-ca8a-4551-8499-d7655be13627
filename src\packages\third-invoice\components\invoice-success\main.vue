<template>
    <div class="content">
        <img class="success" src="../../image/success.png" alt="" />
        <div class="text1">提交成功</div>
        <div class="text2"> 您的发票正在开出，开具成功后 您可以在“开票历史“中查看和下载 </div>
        <div class="btns">
            <div class="btn1" @click="goInvoiceHis">开票历史</div>
            <div class="btn2" @click="goInvoiceHome">发票首页</div>
        </div>
    </div>
</template>

<script>
import { clientCode } from '../../../../../project.config';
export default {
    name: 'invoice-success',
    props: {
        // 开票类型
        type: {
            type: String,
            default: '',
        },
        refer: {
            type: String,
            default: '',
        },
        orderType: {
            type: String,
            default: '',
        },
    },
    data() {
        return {};
    },
    methods: {
        /**
         * @description  : 跳转发票首页，传参第一层tab默认为2，打开发票历史tab页
         * @return        {*}
         */
        goInvoiceHis() {
            if (this.type == 'change') {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'hk-invoiceHistoryBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'invoiceHistoryBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            let url = '/packages/third-invoice/pages/home/<USER>';
            let params = {
                navActiveProps: 2,
                refer: 'r35',
                orderType: this.orderType,
            };
            if (this.type == 'invoice2') {
                params.secondNavActive = '3150';
            }
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 跳转发票首页
         * @return        {*}
         */
        goInvoiceHome() {
            if (this.type == 'change') {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'hk-invoiceHomeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'invoiceHomeBut',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            let url = '/packages/third-invoice/pages/home/<USER>';
            let params = { refer: 'r35', orderType: this.orderType };
            this.$sKit.layer.useRouter(url, params);
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    background: #f7f7fb;
    padding: 135rpx 32rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .success {
        width: 128rpx;
        height: 128rpx;
    }

    .text1 {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
        margin-top: 58rpx;
    }

    .text2 {
        width: 370rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #999999;
        line-height: 46rpx;
        margin-top: 16rpx;
        text-align: center;
    }

    .btns {
        margin-top: 32rpx;
        display: flex;
        justify-content: space-between;
        width: 100%;

        div {
            width: 331rpx;
            height: 88rpx;
            border-radius: 16rpx;
            font-size: 32rpx;
            font-weight: 400;
            line-height: 88rpx;
            text-align: center;
        }

        .btn1 {
            background: #ffffff;
            border: 1rpx solid #e64f22;
            color: #e64f22;
        }

        .btn2 {
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            color: #ffffff;
        }
    }
}
</style>
