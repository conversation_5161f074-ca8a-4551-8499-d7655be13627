<template>
    <div class="print-auto">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="自助打印"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="canvas-box" @click="previewImg">
            <canvas class="qrcode" canvas-id="qrcode"></canvas>
        </div>
        <p class="tips-txt">请在自助开票设备上，扫描二维码打印电子发票</p>
        <p class="tips-txt">二维码点击可放大</p>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getInvoicePrintQrCodeApi } from '@/api/my-center';
import wxbarcode from 'wxbarcode';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
        };
    },
    // 自动打印
    onLoad(options) {
        if (options.id) {
            let id = encodeURIComponent(options.id);
            this.loadPrintQrcode(id);
        }
        // this.loadPrintQrcode('123123')
    },
    methods: {
        async loadPrintQrcode(id) {
            let { data } = await getInvoicePrintQrCodeApi({ id });
            wxbarcode.qrcode('qrcode', data, 400, 400);
        },
        async previewImg() {
            let imgUrl = await this.getImgUrl();
            uni.previewImage({
                urls: [imgUrl],
                success(res) {
                    console.log(res);
                },
                fail(err) {
                    console.log('err', err);
                },
            });
        },
        getImgUrl() {
            let that = this;
            return new Promise(resolve => {
                uni.canvasToTempFilePath(
                    {
                        canvasId: 'qrcode',
                        fileType: 'png',
                        success(res) {
                            resolve(res.tempFilePath);
                        },
                    },
                    that,
                );
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.canvas-box {
    padding-top: 100rpx;
    text-align: center;
    .qrcode {
        display: inline-block;
        width: 400rpx;
        height: 400rpx;
    }
}
.tips-txt {
    text-align: center;
    &:first-child {
        margin-bottom: 6rpx;
    }
}
</style>
