import { mapState } from 'vuex';
export default {
    data() {
        return {
            oldWidth: '',
            rootFontSize: '',
        };
    },
    onLoad() {
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        this.hideShareMenu();
        uni.getSystemInfo({
            success: res => {
                if (res.windowWidth > 430) {
                    // this.$store.commit('setRootFontSize', '50px');
                    this.rootFontSize = '50px';
                }
            },
        });
    },
    // computed: {
    //     ...mapState({
    //         rootFontSize: state => state.thirdIndex.rootFontSize,
    //     }),
    // },
    methods: {
        pageMetaResize(e, callback = () => {}) {
            console.log(e, '触发onResize');
            let changed = false;
            if (this.oldWidth !== e.detail.size.windowWidth) {
                this.oldWidth = e.detail.size.windowWidth;
                changed = true;
                this.$store.commit('setZjNavBarShow', false);
                if (e.detail.size.windowWidth <= 430) {
                    // #ifndef MP-ALIPAY
                    // this.$store.commit('setRootFontSize', '');
                    this.rootFontSize = '';
                    // #endif
                    // #ifdef MP-ALIPAY
                    // this.$store.commit('setRootFontSize', '50px');
                    this.rootFontSize = '50px';
                    // #endif
                } else if (e.detail.size.windowWidth > 430) {
                    // this.$store.commit('setRootFontSize', '50px');
                    this.rootFontSize = '50px';
                }
                this.$forceUpdate();
                this.$store.commit('setZjNavBarShow', true);
                callback(e, changed);
            }
        },
        hideShareMenu() {
            // #ifdef MP-ALIPAY
            const pages = getCurrentPages();
            const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0];
            if (pageUrl !== '/pages/thirdHome/main') {
                my.hideShareMenu();
            }
            // #endif
        },
    },
};
