// #ifdef MP-WEIXIN  || H5
import { mapGetters, mapState } from 'vuex';
const Plugin = requirePlugin('pay-plugin');
export default {
    async onLoad(options) {},
    methods: {
        async getRechargeMethod(params) {
            return new Promise(async (resolve, reject) => {
                try {
                    const res = await Plugin.GetRechargeTypeList(params);
                    console.log(res, '获取支付方式===微信');
                    resolve(await this.setIconAndRechar(res));
                } catch (error) {
                    reject(error);
                }
            });
        },
        /**
         * 发起充值支付
         * @param areaCode 地区编码
         * @param bizOrderNo 业务订单编号
         * @param rcvAmt 应收总金额
         * @param realAmt 支付金额
         */
        async callUpPayment(resMP) {
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: resMP.data.prePayId,
                rcvAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                realAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                // 3.0.4风控字段
                extendFiled: JSON.stringify({
                    dfp: '',
                    gps:
                        this.riskManagementLonV3 && this.riskManagementLatV3
                            ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                            : '',
                    gpsProvince: '',
                    gpsCity: '',
                    gpsArea: '',
                }),
                openId: this.openId,
                payType: resMP.data.paymentType + '',
            };
            const res = await Plugin.RechargePay(params);
            if (res.code === 'PAY_SUCCESS') {
                this.callWechatCashier(res.data, params.bizOrderNo);
            } else {
                this.payingFlag = false;
                this.closePopup();
                // 截取字符串后面的数据
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: `${errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        // 拉起微信收银台
        callWechatCashier(data, bizOrderNo) {
            console.log(data, bizOrderNo, 'data, bizOrderNo');
            // 调用微信支付
            wx.requestPayment({
                appId: data.appId,
                provider: 'wxpay',
                timeStamp: data.timestamp,
                nonceStr: data.nonceStr,
                package: 'prepay_id=' + data.prepayId,
                signType: 'RSA',
                paySign: data.sign,
                success: () => {
                    // 新增触发插件方法
                    Plugin.QueryOrder({
                        bizOrderNo: bizOrderNo,
                        payOrderNo: data.payOrderNo,
                        bizModel: '8',
                    });
                    console.log('充值加快查询结果插件参数', data.payOrderNo, bizOrderNo);
                    this.otherAmount = '';
                    this.payingFlag = false;
                    this.$sKit.layer.useRouter(
                        '/packages/third-remaining-sum/pages/third-charge-result/main',
                        { orderId: bizOrderNo, refer: this.refer, addressName: this.addressName },
                        'navigateTo',
                    );
                },
                fail: err => {
                    this.payingFlag = false;
                    console.log(err, 'errerrerr');
                },
            });
        },
    },
    computed: {
        // #ifdef MP-WEIXIN
        ...mapGetters(['openId']),
        // #endif
        ...mapState({
            // 获取该网点的油品编码集合
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
        }),
    },
};
// #endif
