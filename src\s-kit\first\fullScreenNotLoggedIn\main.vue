<template>
    <div class="pageMpaas">
        <div class="p-bf bg-fff fl-column">
            <zj-navbar :isBack="false" :background="{}" :height="44" :title="title"></zj-navbar>
            <div class="loginBlock p-LR-16 fl-column f-1 fl-al-jus-cen">
                <img class="avtar" src="../notLoggedIn/image/defaultAvatar.png" alt="" />
                <!-- #ifdef MP-WEIXIN -->
                <div class="text weght-400 font-14 color-333">登录后，{{ textContent }} </div>
                <!-- @click="verificationCodeOrOneClickLogin"-->
                <getPhoneNumber :curTabIndex="curTabIndex" @loginOver='loginOver'>
                    <div
                        class="loginBtn color-fff font-18 btn-44 primary-btn2"
                        :class="{ 'bg-opacity-288': !loginButtonGrayedOut && !token && !token3 }"
                    >
                        立即登录
                    </div>
                </getPhoneNumber>
                <!-- #endif -->
                <!-- #ifdef MP-ALIPAY || MP-TOUTIAO  -->
                <div class="text weght-400 font-14 color-333">登录后，{{ textContent }}</div>
                <zfb-login-v3 :layer="true" :silence="false" @success="onLoginAfterEvent">
                    <div class="loginBtn color-fff font-18 btn-44 primary-btn2">立即登录</div>
                </zfb-login-v3>
                <!-- #endif -->
              
            </div>
        </div>
    </div>
</template>
<script>
import platform from '@/s-kit/js/platform';
// #ifdef MP-ALIPAY
import zfbLoginV3 from '@/components/zfb-login-v3/zfb-login-v3.vue';
// #endif
// #ifdef MP-WEIXIN
import getPhoneNumber from '../../../components/loginV3/getPhoneNumber.vue';
// #endif
import { mapState } from 'vuex';
export default {
    name: 'fullScreenNotLoggedIn',
    components: {
        // #ifdef MP-ALIPAY
        zfbLoginV3,
        // #endif
        // #ifdef MP-WEIXIN
        getPhoneNumber,
        // #endif
    },
    props: {
        curTabIndex: {
            default: '0',
            type: [String, Number],
        },
    },
    computed: {
        ...mapState({
            // 登录标识
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
        }),
        title() {
            return this.curTabIndex == 1 ? '钱包' : this.curTabIndex == 2 ? '订单' : this.curTabIndex == 3 ? '我的' : '';
        },
        textContent() {
            return this.curTabIndex == 1
                ? '即可使用钱包功能'
                : this.curTabIndex == 2
                ? '即可查看订单详情'
                : this.curTabIndex == 3
                ? '即可使用"我的"专区'
                : '';
        },
        paddingTop() {
            console.log(this.systemBar, 'this.systemBar');
            return `padding-top: ${10 + Number(this.systemBar)}px`;
        },
    },
    data() {
        return {
            //系统导航栏高度
            systemBar: '',
        };
    },

    created() {},
    mounted() {
        // 获取系统导航栏
        this.getTheSystemNavigationBar();
    },
    methods: {
        loginOver(res){
            this.$emit('loginOver', res)
        },
        /**
         * @description  : 获取系统导航栏
         * @return        {*}
         */
        getTheSystemNavigationBar() {
            let systemInfo = uni.getSystemInfoSync();
            if (platform.isAlipay || platform.isTouTiao) {
                console.log('systemInfo.statusBarHeight----', systemInfo.statusBarHeight);
                this.systemBar = systemInfo.statusBarHeight + Number(this.navH);
            } else {
                this.systemBar = systemInfo.statusBarHeight;
            }
        },
        async verificationCodeOrOneClickLogin() {
            // if (!this.loginButtonGrayedOut) return;
            // this.$store.commit('setLoginButtonGrayedOut', false);
            // if (await this.$store.dispatch('callAutomaticLoginAgain')) return;
            // // 不能一键登录执行验证码登录
            // let res = await this.$store.dispatch('init', 'login');
            // if (res.result == 'success') {
            //     // 不能一键登录执行验证码登录
            //     let url = '/packages/third-new-third-login/pages/login/main';
            //     // let url = '/packages/third-invoice/pages/invoice-title-form/main';
            //     let params = {};
            //     this.$store.dispatch(
            //         'setOfficialAccountParams',
            //         this.curTabIndex == 1 ? 'qb' : this.curTabIndex == 2 ? 'dd' : this.curTabIndex == 3 ? 'wd' : '',
            //     );
            //     this.$sKit.layer.useRouter(url, params);
            //     setTimeout(() => {
            //         this.$store.commit('setLoginButtonGrayedOut', true);
            //     }, 1000);
            // }
        },
        /**
         * @description  : 支付宝登录成功后回调参数存储
         * @return        {*}
         */
        onLoginAfterEvent() {
            this.$store.commit(
                'setReturnToTheSpecifiedPage',
                this.curTabIndex == 1 ? 'qb' : this.curTabIndex == 2 ? 'dd' : this.curTabIndex == 3 ? 'wd' : '',
            );
            this.$sKit.layer.backHomeFun();
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.loginBlock {
    width: 100%;
    height: 201px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .avtar {
        width: 206rpx;
        height: 206rpx;
    }

    .text {
        height: 50rpx;
        line-height: 50rpx;
        font-style: normal;
        margin-top: 5px;
    }

    .loginBtn {
        width: 360rpx;
        margin-top: 32rpx;
    }
}
</style>
