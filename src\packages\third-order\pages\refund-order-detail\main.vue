<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 订单详情页面 -->
        <div class="select-region-class">
            <zj-navbar title="订单详情"></zj-navbar>
            <div class="system-time">
                <div class="countdown-bg1 font-style">{{ hour }}</div
                >: <div class="countdown-bg2 font-style">{{ minutes }} </div>: <div class="countdown-bg3 font-style">{{ seconds }}</div
                >:
                <div class="countdown-bg4 font-style">{{ count }}</div>
            </div>
            <div class="content-view mh-0">
                <div class="commodity-order-box">
                    <div class="gas-station">
                        <img class="gas-station-img" src="../../image/cheer.png" alt />
                        <div class="gas-station-text">
                            {{ pageParams.stationName || '' }}
                        </div>
                    </div>
                    <div class="commodity-order" v-for="(item, index) in productList" :key="index">
                        <div class="commodity">
                            <img class="detail-left-img" v-if="item.productType == 1" src="../../image/order_oils.png" alt />
                            <img class="commodity-img" v-else :src="item.imgUrl" alt />
                            <div class="commodity-title">{{ item.productName || '' }}</div>
                            <div class="commodity-price">
                                <div class="unit-price">&yen;{{ item.unitPrice || '' }}</div>
                                <div class="num"> x {{ (item.productQty || '') + (item.productUnit || '') }} </div>
                            </div>
                        </div>
                        <div class="information">
                            <div class="information-data">
                                <div class="information-data-left">商品编码</div>
                                <div class="information-data-right">
                                    {{ item.productNo }}
                                    <div class="copy" @click="copyProductNo(item.productNo)">复制</div>
                                </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">原销售数量</div>
                                <div class="information-data-right">{{ item.productQty }}</div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">退货数量</div>
                                <div class="information-data-right">
                                    {{ item.returnQty }}
                                </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">实退金额</div>
                                <div class="information-data-right" style="color: #e64f22"> ￥{{ item.actualRefundAmount }} </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="details-price">
                    <div class="details-price-item">
                        <div class="item-left">退款时间</div>
                        <div class="item-right text">
                            {{ pageParams.createTime }}
                        </div>
                    </div>
                    <div class="details-price-item">
                        <div class="item-left">订单编号</div>
                        <div class="item-right">
                            <div class="item-right-text">{{ pageParams.orderNo || '' }}</div>
                            <div class="copy" @click="copyProductNo(pageParams.orderNo)">复制</div>
                        </div>
                    </div>
                    <div class="details-price-item">
                        <div class="item-left">退款总金额</div>
                        <div class="item-right" style="color: #e64f22"> ¥{{ pageParams.actualPayTotalAmount || 0 }} </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { refundOrderDetailApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'refundOrderDetail',
    data() {
        return {
            // 顶部倒计时计时器
            timer: null,
            // 倒计时展示绑定值
            hour: '00',
            minutes: '00',
            seconds: '00',
            count: '00',
            // 订单信息是否展示更多按钮绑定值，通过该字段判断
            returnOrderDetailStatus: true,
            //  商品列表
            productList: [],
            //支付时间
            payConfirmationTime: '',
            //支付渠道
            payChannel: '',
            //支付折扣
            payDiscountTotal: 0,
            // 支付明细
            payItems: [],
            // 页面展示需要的数据
            pageParams: {},
        };
    },
    onLoad(option) {
        // 进入页面先确保定时器清除
        clearInterval(this.timer);
        // 接收上个页面传来的参数
        this.pageParams = JSON.parse(decodeURIComponent(option.data));
        // 获取退款订单详情数据
        this.orderDetailPost();
    },
    mounted() {
        // 开启定时器
        this.goto();
    },
    methods: {
        /**
         * @description  : 获取退款订单详情数据
         * @return        {*}
         */
        async orderDetailPost() {
            let params = {
                returnOrderNo: this.pageParams.orderNo,
                returnOrderDetailStatus: this.returnOrderDetailStatus,
            };
            let res = await refundOrderDetailApi(params);
            if (res && res.success) {
                this.productList = res.data.orderItems || [];
            }
        },
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:要被复制的内容
         * @return        {*}
         */
        copyProductNo(value) {
            uni.setClipboardData({
                data: value,
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        /**
         * @description  : 开启定时器
         * @return        {*}
         */
        goto() {
            this.timer = setInterval(() => {
                this.count = Number(this.count);
                if (this.count < 99) {
                    this.count = this.count + 1;
                } else {
                    this.count = 0;
                }
                if (this.count < 10) {
                    this.count = '0' + this.count;
                }
                var date = new Date();
                this.hour = date.getHours(); // 时
                if (this.hour >= 0 && this.hour <= 9) {
                    this.hour = '0' + this.hour;
                }
                this.minutes = date.getMinutes(); // 分
                if (this.minutes >= 0 && this.minutes <= 9) {
                    this.minutes = '0' + this.minutes;
                }
                this.seconds = date.getSeconds(); //秒
                if (this.seconds >= 0 && this.seconds <= 9) {
                    this.seconds = '0' + this.seconds;
                }
            }, 10);
        },
    },
    /**
     * @description  : 销毁页面时清除定时器
     * @return        {*}
     */
    beforeDestroy() {
        clearInterval(this.timer);
    },
};
</script>

<style lang="scss" scoped>
.select-region-class {
    background: #f7f7fb;
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    // overflow-y: scroll;

    .system-time {
        width: 100%;
        height: 80px;
        background: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #e64f22;
        font-size: 16px;
        font-weight: bold;
        padding: 0 70px;

        div {
            width: 44px;
            height: 44px;
            color: #ffffff;
            border-radius: 10px;
            text-align: center;
            line-height: 44px;
            font-size: 18px;
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        }
    }

    .content-view {
        width: 100%;
        flex: 1;
        padding: 32rpx;
        overflow-y: auto;
        overflow-x: hidden;
        .wxCode-box {
            margin-bottom: 20rpx;
        }

        .gas-station {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            .gas-station-img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
            }

            .gas-station-text {
                width: 200px;
                font-size: 14px;
                font-weight: bold;
                color: #000;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .oil-order {
            width: 100%;
            margin: 0 auto;
            // height: 257px;
            background: #ffffff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .commodity-order-box {
            width: 100%;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;

            .commodity-order {
                margin-top: 15px;

                .item-bottom {
                    height: 35px;
                    line-height: 35px;
                    text-align: center;
                    color: #666;
                }
            }
        }

        .details-price {
            width: 100%;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            padding: 23rpx 28rpx 29rpx;

            .details-price-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .item-left {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 67rpx;
                }

                .item-right {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 67rpx;

                    .item-right-text {
                        word-break: break-all;
                        max-width: 420rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 28rpx;
                    }

                    .copy {
                        width: 29px;
                        height: 16px;
                        background: #ffffff;
                        border-radius: 2px;
                        border: 1px solid #999999;
                        text-align: center;
                        line-height: 16px;
                        color: #666;
                        font-size: 10px;
                        margin-left: 5px;
                    }
                }

                .gray {
                    color: #666666;
                }

                .item-right-button {
                    padding: 0 20rpx;
                    height: 48rpx;
                    border-radius: 4rpx;
                    border: 1rpx solid #333333;
                    line-height: 48rpx;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #333333;
                    text-align: center;
                }

                .item-right-text {
                    line-height: 50rpx;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #333333;
                }

                .color {
                    color: #e64f22;
                    border: 1rpx solid #e64f22;
                }
            }
        }

        .commodity {
            display: flex;

            .commodity-img {
                width: 50px;
                height: 50px;
                border-radius: 5px;
                overflow: hidden;
                margin-right: 10px;
            }

            .detail-left-number {
                width: 100rpx;
                height: 100rpx;
                border-radius: 5px;
                margin-right: 10px;
                background-color: #f5c41b;
                line-height: 100rpx;
                text-align: center;
                font-weight: bold;
                font-size: 40rpx;

                span {
                    vertical-align: text-top;
                    font-size: 24rpx;
                    line-height: 20rpx;
                }
            }

            .detail-left-img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 5px;
                margin-right: 10px;
            }

            .commodity-title {
                flex: 1;
                color: #333;
                font-size: 14px;
            }

            .commodity-price {
                text-align: right;

                .unit-price {
                    font-size: 14px;
                    color: #333;
                    line-height: 20px;
                }

                .num {
                    font-size: 10px;
                    color: #666;
                    line-height: 20px;
                }

                .total-price {
                    font-size: 14px;
                    color: #333;
                    font-weight: bold;
                }
            }
        }

        .pay-list {
            padding-left: 30rpx;
        }

        .information {
            margin-top: 30rpx;

            .information-data {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 15rpx;
                flex-wrap: wrap;

                &:nth-of-type(1) {
                    margin-top: 0;
                }

                .information-data-left {
                    font-size: 14px;
                    font-weight: 400;
                    color: #666666;
                    line-height: 48rpx;
                }

                .information-data-right {
                    text-align: right;
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                    display: flex;
                    align-items: center;
                    flex: 1;
                    justify-content: flex-end;
                    line-height: 48rpx;

                    .copy {
                        width: 29px;
                        height: 16px;
                        background: #ffffff;
                        border-radius: 2px;
                        border: 1px solid #999999;
                        text-align: center;
                        line-height: 16px;
                        color: #666;
                        font-size: 10px;
                        margin-left: 5px;
                    }
                }

                .text {
                    color: #e64f22;
                }
            }
        }
    }
}
</style>
