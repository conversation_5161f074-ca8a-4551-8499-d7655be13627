import { mapState, mapGetters } from 'vuex';
export default {
  // #ifdef H5-CLOUD
  onShow() {
    this.isH5CLOUD = true
    this.$nextTick(() => {
      this.getPlugin()
    });
  },
  mounted() {

  },
  methods: {
     getPlugin() {
      this.$nextTick(async () => {
        // 获取键盘的实例
        let result = await this.$refs.cloudKeyboardRef.initRef();
        console.log('result', result)
        this.$store.commit('setAccountDataPlugin', result);
        this.passwordKeyboardRef = result;
      });

    }
  },
  // #endif
};