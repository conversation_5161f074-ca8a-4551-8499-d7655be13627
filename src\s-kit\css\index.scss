.pageMpaas {
    width: 100%;
    height: 100vh;
    background-color: #fff;
    color: #333;
    overflow-x: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    // 解决页面与tabbar页面之间的黑线
    // 产生原因是由于iOS系统默认的底部导航栏样式所导致的，
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // #ifdef MP-TOUTIAO
    overflow-y: hidden;
    // #endif
}

div{
    box-sizing: border-box;
}

/* flex 布局 */
.content-box {
    box-sizing: content-box;
}

.border-box {
    box-sizing: border-box;
}

.fl-row {
    display: flex;
    flex-direction: row;
}

.fl-column {
    display: flex;
    flex-direction: column;
}

.fl-wrap {
    flex-wrap: wrap;
}

.fl-al-jus-cen {
    align-items: center;
    justify-content: center;
}

.fl-al-cen {
    align-items: center;
}

.fl-al-sta {
    align-items: flex-start;
}

.fl-al-end {
    align-items: flex-end;
}

.fl-al-base {
    align-items: baseline;
}

.fl-jus-cen {
    justify-content: center;
}

.fl-jus-sta {
    justify-content: flex-start;
}

.fl-sp-end {
    justify-content: flex-end;
}

.fl-jus-bet {
    justify-content: space-between;
}

.fl-jus-aro {
    justify-content: space-around;
}

.fl-1-auto {
    flex: 1 1 auto;
}

.fl-bas {
    flex-basis: 30%;
}

.f-1 {
    flex: 1;
}

.fl-sh-0 {
    flex-shrink: 0;
}

.fl-b-0 {
    flex-basis: 0;
}

.fl-g-1 {
    flex-grow: 1;
}

.inline {
    display: inline-block;
}

.wh-s {
    white-space: nowrap;
}

.te-center {
    text-align: center;
}

.te-left {
    text-align: left;
}

.te-right {
    text-align: right;
}

/* page相关 */

.solid {
    border: 1px solid red;
}

.p-hw {
    height: 100vh;
    width: 100vw;
}

.p-bf {
    height: 100%;
    width: 100%;
}

.width100 {
    width: 100%;
}
.mh-0 {
    min-height: 0;
}

.dis-block {
    display: block;
}

.card-shadow {
    background: #ffffff;
    box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.07);
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}

.card-default {
    background: #ffffff;
    border-radius: 8px;
}
.card-linear {
    border-radius: 8px 8px 0 0;
    // margin: 0 16px;
}
.card-input-border {
    background: #f7f7fb;
    border-radius: 8px;
    border: 1px solid #ff0a0a;
    height: 46px;
}

input {
    height: 100%;
    min-height: unset;
}
/* 背景色 */
.bg-none {
    background: none;
}
.bg-F7F7FB {
    background: #f7f7fb;
}

.bg-fff {
    background: #ffffff;
}

.bg-e64f22 {
    background: #e64f22;
}

.bg-ADA1A1 {
    background: rgba(173, 161, 161, 0.2);
}

.bg-EFEDED {
    background: #efeded;
}

.bg-EDEDF5 {
    background: #ededf5;
}

.bg-FFF7DC {
    background: #fff7dc;
}

.bg-F2F3F5 {
    background: #f2f3f5;
}
.bg-F7F6FB {
    background: #F7F6FB;
}

.bg-ff6133 {
    background: #ff6133;
}
.bg-288-FF3E00{
    background: linear-gradient( 183deg, #FFFFFF 0%, #FFECE2 100%);
    box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0,0,0,0.07);
}
.bg-linear {
    background-image: linear-gradient(180deg, #ffffff 0%, #f7f7fb 100%);
}
.bg-90-FED {
    background-image: linear-gradient( 90deg, #FFEEDD 0%, rgba(255,244,233,0.4) 100%);
}
.bg-288-E64F22 {
    background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
}

.bg-90 {
    background-image: linear-gradient(90deg, #e64f22 0%, #ffa66c 100%);
}

.bg-FFEAE0 {
    background-image: linear-gradient(90deg, #ffeae0 0%, #fcf0de 100%);
}
.bg-288 {
    background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
}
.bg-purple {
    background-image: linear-gradient(180deg, #da4af6 0%, #7a42f3 100%);
}
.bg-F2F3F5 {
    background: #f2f3f5;
}

.bg-F5F5F5 {
    background: #f5f5f5;
}

.bg-FFEBE3 {
    background: #ffebe3;
}

.bg-F3F3F6 {
    background: #f3f3f6;
}
.bg-FF6B2C {
    background: rgba(255,107,44,0.08);
}

.bg-999 {
    background: #999999;
}

.bg-D0D0D0 {
    background: #d0d0d0;
}

.bg-transparent {
    background: transparent;
}

/* 圆角相关 */
.border-rad-2 {
    border-radius: 2px;
}
.border-rad-TR-8{
    border-radius: 8px 8px 0 0;
}
.border-rad-BL-8{
    border-radius: 0 0 8px 8px ;
}

.border-rad-4 {
    border-radius: 4px;
}

.border-rad-8 {
    border-radius: 8px;
}

.border-rad-10 {
    border-radius: 10px;
}

/* 文字相关 */
.font-9 {
    font-size: 9px;
}

.font-10 {
    font-size: 10px;
}

.font-11 {
    font-size: 11px;
}

.font-12 {
    font-size: 12px;
}

.font-13 {
    font-size: 13px;
}

.font-14 {
    font-size: 14px;
}

.font-15 {
    font-size: 15px;
}

.font-16 {
    font-size: 16px;
}
.font-17 {
    font-size: 17px;
}

.font-18 {
    font-size: 18px;
}

.font-19 {
    font-size: 19px;
}

.font-20 {
    font-size: 20px;
}

.font-22 {
    font-size: 22px;
}

.font-24 {
    font-size: 24px;
}

.font-27 {
    font-size: 27px;
}

.font-28 {
    font-size: 28px;
}

.font-36 {
    font-size: 36px;
}

.font-40 {
    font-size: 40px;
}

.font-50 {
    font-size: 50px;
}

.weight-500 {
    font-weight: 500;
}

.weight-600 {
    font-weight: 600;
}
.weight-700 {
    font-weight: 700;
}

.weight-bold {
    font-weight: bold;
}

.weight-400 {
    font-weight: 400;
}

.color-E64F22 {
    color: #e64f22;
}
.color-FF3E00 {
    color: #FF3E00;
}
.color-F7B500 {
    color: #F7B500;
}
.color-118920{
    color: #118920;
}
.color-FF6B2C {
    color: #ff6b2c;
}
.color-5D2D14 {
    color: #5D2D14;
}
.color-FF4000 {
    color: #FF4000;
}

.color-E65023 {
    color: #e65023;
}
.color-FF0A0A {
    color: #ff0a0a;
}

.color-6A6A6A {
    color: #6a6a6a;
}
.color-686868 {
    color: #686868;
}

.color-1EE68C {
    color: #1ee68c;
}

.color-fff {
    color: #ffffff;
}

.color-000 {
    color: #000000;
}

.color-222 {
    color: #222222;
}

.color-333 {
    color: #333333;
}

.color-666 {
    color: #666666;
}

.color-999 {
    color: #999999;
}

.color-AD {
    color: #adadad;
}

.color-F2F3F5 {
    color: #f2f3f5;
}

.color-80 {
    color: #808080;
}

.color-F93F00 {
    color: #f93f00;
}

.color-EB5130 {
    color: #eb5130;
}

.color-1E1E1E {
    color: #1e1e1e;
}

.color-818183 {
    color: #818183;
}

.color-FA6400 {
    color: #fa6400;
}
.color-118920 {
    color: #118920;
}
.color-E02020 {
    color: #e02020;
}
.color-rgba5 {
    color: rgba(0, 0, 0, 0.5);
}

/**
*内边距相关
*padding 两个参数的时候 第一个参数为上下 第二个参数为左右 
*padding 四个参数的时候 顺序为上右下左 
*/
.padding-13 {
    padding: 13px 13px;
}

.p-LR-16 {
    padding: 0 16px;
}
.p-LR-12 {
    padding: 0 12px;
}
.p-TB-16 {
    padding: 16px 0;
}

.padding-16 {
    padding: 16px;
}
.padding12{
    padding: 12px
}
.paddingB24{
    padding-bottom: 24px;
}
.paddingB12{
    padding-bottom: 12px;
}

/**
*外边距相关
*margin 两个参数的时候 第一个参数为上下 第二个参数为左右 
*margin 四个参数的时候 顺序为上右下左 
*/
.mar-bom-12 {
    margin-bottom: 12px;
}

.mar-left-4 {
    margin-left: 4px;
}

.mar-6-l {
    margin-left: 6px;
}

.mar-top-8 {
    margin-top: 8px;
}
.mar-top-12 {
    margin-top: 12px;
}
.mar-top-15 {
    margin-top: 15px;
}

.mar-left-15 {
    margin-left: 15px;
}

.mar-right-20 {
    margin-right: 20px;
}
.marl12 {
    margin-left: 12px;
}

.mat5 {
    margin-top: 5px;
}

.marb17 {
    margin-bottom: 17px;
}

.mart14 {
    margin-top: 14px;
}

.marb10 {
    margin-bottom: 10px;
}

.marl5 {
    margin-left: 5px;
}


.marr3 {
    margin-right: 3px;
}
.marginB5{
    margin-bottom: 5px;
}
.marginB3{
    margin-bottom: 3px;
}
.marginB17{
    margin-bottom: 17px;
}
.marginB10{
    margin-bottom: 10px;
}
.mart10 {
    margin-top: 10px;
}
.mart20 {
    margin-top: 20px;
}


/* 高度相关 */
.height-40 {
    height: 40px;
}

.height-30 {
    height: 30px;
}

/* 宽度相关 */
.width-160 {
    width: 160px;
}

.width-77 {
    width: 77px;
}

/* 定位相关 */

/* 边线相关 */
.border-333 {
    border: 1px solid #333333;
}

.border-dashed {
    border: 0.5px dashed #979797;
}

.border-FF6B2C {
    border: 1px solid #ff6b2c;
}

.border-FF431E {
    border: 1px solid #ff431e;
}
.border-bottom{
    border-bottom: 1rpx solid #EEEEEE;
}
.border-opcity {
    border: 1px solid #00000000;
}

.border-fa6400 {
    border: 1px solid #fa6400;
}

.border-999 {
    border: 1px solid #999999;
}

.line_bottom {
    position: relative;

    &.line_bottom:before {
        content: ' ';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        border-top: 1px solid #efeff4;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}

.line_bottom_dashed {
    position: relative;

    &.line_bottom_dashed:before {
        content: ' ';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        border-top: 1px dashed #dddddd;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}

/* 箭头相关 */

.arroe-right-small {
    box-sizing: border-box;
    width: 8px;
    height: 8px;
    border: solid #999;
    border-width: 0 2px 2px 0;
    transform: rotate(-45deg);
}

.arroe-right-small-1 {
    box-sizing: border-box;
    width: 8px;
    height: 8px;
    border: solid #999;
    border-width: 0 1.5px 1.5px 0;
    transform: rotate(-45deg);
}

.arroe-right-333 {
    box-sizing: border-box;
    width: 6px;
    height: 6px;
    border: solid #333;
    border-width: 0 1px 1px 0;
    transform: rotate(-45deg);
}

.arroe-right-E64F22 {
    box-sizing: border-box;
    width: 6px;
    height: 6px;
    border: solid #e64f22;
    border-width: 0 1px 1px 0;
    transform: rotate(-45deg);
}
.arrow-right-FF0A0A {
    box-sizing: border-box;
    width: 6px;
    height: 6px;
    border: solid #ff0a0a;
    border-width: 0 1px 1px 0;
    transform: rotate(-45deg);
}

.arroe-right-666 {
    box-sizing: border-box;
    width: 5px;
    height: 5px;
    border: solid #666;
    border-width: 0 1px 1px 0;
    transform: rotate(-45deg);
}

.arroe-right-E64F22 {
    box-sizing: border-box;
    width: 7px;
    height: 7px;
    border: solid #e64f22;
    border-width: 0 2px 2px 0;
    transform: rotate(-45deg);
}

.arr-right-E64F22-shi {
    width: 0;
    height: 0;
    border-width: 4px;
    border-style: solid;
    border-color: transparent #e64f22 transparent transparent;
    transform: rotate(180deg);
}

.arr-right-000-shi {
    width: 0;
    height: 0;
    border-width: 7px;
    border-style: solid;
    border-color: transparent #000000 transparent transparent;
    transform: rotate(180deg);
}
.arr-right-FA6400 {
    box-sizing: border-box;
    width: 6px;
    height: 6px;
    border: solid #FA6400;
    border-width: 0 2px 2px 0;
    transform: rotate(-45deg);
}

.arrow-down {
    border: solid #999999;
    border-width: 0px 0 1px 1px;
    box-sizing: border-box;
    width: 7px;
    height: 7px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
}

.arrow-down-333 {
    box-sizing: border-box;
    width: 7px;
    height: 7px;
    border: solid #333333;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
}

.arrow-down-10 {
    border: solid #999999;
    border-width: 0px 0 1px 1px;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
}
.arrow-down-oil {
    width: 0;
    height: 0;
    border-width: 0 10px 10px;
    border-style: solid;
    border-color: transparent transparent #ffffff;
    transform: rotate(180deg);
}
/* 对号 */
.check {
    position: relative;
    display: inline-block;
    width: 11px;
    height: 8px;
    // margin: 0 12px 0 0;
}
.check::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 50%;
    border: 2px solid #ff6b2c;
    border-radius: 1px;
    border-top: none;
    border-right: none;
    background: transparent;
    transform: rotate(-45deg);
}
/* 超出隐藏 */
.ov-hid {
    overflow: hidden;
}
.hieght200 {
    height: 200px;
}

.ov-y-s {
    overflow-y: auto;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.ellipsis-2 {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 阴影 */
.shad-ef {
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
}

/* 按钮 */

// 功能按钮
.primary-btn {
    background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
    text-align: center;
}

// 默认尺寸-镂空
.btn-plain {
    background: #ffffff;
    border: 1px solid #e64f22;
    text-align: center;
}
.btn-already {
    background: #ededf5;
    text-align: center;
}
// 默认尺寸-透明
.btn-plain-tran {
    background: transparent;
    text-align: center;
}

.bg-opacity-288 {
    background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
    border-radius: 8px;
    opacity: 0.3;
    text-align: center;
}

// #ifdef MP-ALIPAY
.pageMpaas {
    // #endif

    button {
        position: static;
        background: transparent;
        padding: 0;
        line-height: 1.4285;
        font-size: 14px;
    }

    button:after {
        content: none;
    }

    button[disabled]:not([type]) {
        opacity: 0.6;
    }

    // #ifdef MP-ALIPAY
}
// #endif

// 下拉刷新
.pullDownRefreshView {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.text-indent {
    text-indent: 2em;
}

.primary-btn2 {
    height: 44px;
    line-height: 44px;
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
    text-align: center;
    border-radius: 8px;
    color: #fff;
    margin-bottom: 12px;
    font-size: 18px;
}

.other_way_btn {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 44px;
    height: 44px;
    border-radius: 8px;
    text-align: center;

    border: 1px solid #666666;
}

.other_way_btn2 {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 44px;
    height: 44px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 12px;
    background: rgba(188, 188, 188, 0.1);
}
.opacity{
    opacity: 0.5
}
.login_header {
    height: 244px;
    width: 100%;
}

.countdown-bg1 {
    background-image: linear-gradient(288deg, #f9773c 0%, #ff7c34 100%);
}
.countdown-bg2 {
    background-image: linear-gradient(288deg, #ff6728 0%, #ff702d 100%);
}
.countdown-bg3 {
    background-image: linear-gradient(288deg, #ff5c22 0%, #ff6426 100%);
}
.countdown-bg4 {
    background-image: linear-gradient(288deg, #ff501c 0%, #ff5921 100%);
}
.let-spacing {
    letter-spacing: 1px;
}
.line14 {
    line-height: 1.5;
}
.bg-118920 {
    color: #118920;
    background: rgba(17, 137, 32, 0.05);
}
.bg-E02020 {
    color: #e02020;
    background: rgba(224, 32, 32, 0.05);
}
.marker-118920 {
    //width: 50px;
    padding: 4px 5px;
    //height: 21px;
    background: rgba(17, 137, 32, 0.05);
    border-radius: 4px;
    // line-height: 21px;
    text-align: center;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.marker-E02020 {
    padding: 4px 5px;
    background: rgba(224, 32, 32, 0.05);
    border-radius: 4px;
    // line-height: 21px;
    text-align: center;
    margin: 0 4px;
}

.scrolling-text {
    display: inline-block;
    animation: marquee 10s linear infinite both;
    -webkit-animation: marquee 10s linear infinite both;
    text-align: right;
    ////这一句很重要，为了能让滚动左右连接起来padding-left: 100%;
    //padding-left: 100%;
    // #ifdef MP-TOUTIAO
    white-space: nowrap;
    // #endif
}

@keyframes marquee {
    0% {
        transform: translateX(10%);
        -webkit-transform: translateX(10%);
        -moz-transform: translateX(10%);
        -ms-transform: translateX(10%);
        -o-transform: translateX(10%);
    }
    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
        -moz-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
        -o-transform: translateX(-100%);
    }
}

.marginRight {
    margin-right: 8px;
}
.e-go{
    // width: 80px;
    padding: 0 6px 0 5px;
    height: 21px;
    line-height: 21px;
    background: rgba(250,100,0,0.05);
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    box-sizing: border-box;
}
.content-enter-active,
.content-leave-active {
   transition: opacity 0.3s ease;
 }
.content-enter,
.content-leave-to {
   opacity: 0;
 }