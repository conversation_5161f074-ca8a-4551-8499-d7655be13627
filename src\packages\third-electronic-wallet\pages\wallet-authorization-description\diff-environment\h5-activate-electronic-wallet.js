import { realNameAuth, initRealPersonIdentify, realPersonIdentify } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { openAccount } from '../../../../../s-kit/js/v3-http/https3/wallet';
import projectConfig from '../../../../../../project.config';
//  import { setCloud } from '../../../../../s-kit/js/setCloud'
export default {
    // #ifdef H5-CLOUD
    data() {
        return {
            isAuth: false,
            faceValueResponse: null
        }
    },
    onLoad(options) {
        console.log('options---', options)

    },
    onShow() {
        if (this.isAuth) {
            this.isAuth = false
            if (this.faceValueResponse) {
                this.realNameAuthentication(this.faceValueResponse)
            }
            return
        }
    },
    mounted() {
        //  setTimeout(() => {
        //      let that = this
        //      setCloud("black","0xFF000000","0","0",function (data) {
        //          // 这里的data就是接收到的dataSystemInfo
        //          console.log('在外部接收到的系统信息:', data,data.statusBarHeight);
        //          // 可以在这里进一步处理dataSystemInfo
        //          that.systemBar = data ? Number(data.statusBarHeight) : '';
        //          // 可在此处处理data
        //          that.$store.commit('mSetUpaskSystem',data)
        //      });
        //  }, 1000);
    },
    methods: {
        /**
    * @description  :  实人认证
    * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
    * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
    * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
    * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
    * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
    * @param         {string} idNo: 用户身份证号
    * @return        {*}
    */
        realNameAuthentication(faceValue) {
            return new Promise(async (resolve, reject) => {
                let params = {
                    // 认证校验码，初始化实人认证接口返回的数据。
                    authInfo: faceValue.authInfo,
                    // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡；10-资金转出；
                    type: faceValue.type || 1,
                    // 认证接入方式：1—APP接入；2—PC或H5接入；
                    verifyMode: '2',
                    // 当type=1时该字段为必填项。
                    idNo: faceValue.idNo,
                    // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
                    verifyUnique: faceValue.verifyUnique,
                    // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
                    certifyId: faceValue.certifyId,
                    gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
                };
                console.log(params, 'H5传入实人认证接口的参数');
                let res = await realPersonIdentify(params);
                if (res.success && res.data.authInfo) {
                    console.log(res, '实人认证方法接口返回结果');
                    // uni.clearStorageSync()
                    this.openEwallet(res.data.authInfo);
                    // try {
                    //     uni.removeStorageSync('real_name_auth_value');
                    // } catch (e) {
                    //     // error
                    //     uni.removeStorageSync('real_name_auth_value');
                    // }
                    resolve(res);
                } else {
                    // uni.removeStorageSync('real_name_auth_value');
                }
            });
        },
        async agreeActivate() {
            this.$sKit.h5RealPersonAuthentication.startVerification(this.personInfo).then(response => {
                // console.log('链路通过，调用卡通电子账户接口', response);
                if (response) {
                    this.faceValueResponse = response
                    this.isAuth = true
                    upsdk.pluginReady(function () {
                        upsdk.createWebView({
                            url: response.certifyUrl,
                            isFinish: '0' //是否关闭当前的窗口，1':关闭，'0':不关闭
                        });
                    })

                } else {
                }
            });
        },

        /**
         * @description  :  开通电子钱包
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @return        {*}
         */
        async openEwallet(authInfo) {
            console.log(authInfo, '链路通过，调用卡通电子账户接口')
            // let person_info = uni.getStorageSync('person_info')
            // let personInfoVal = JSON.parse(decodeURIComponent(person_info));
            // this.personInfo = personInfoVal
            // console.log('personInfoVal',personInfoVal)
            // let authInfo = response.data.authInfo
            try {
                uni.showLoading({
                    title: '加载中',
                });
                let params = {
                    ...this.personInfo, // 开通钱包流程前面传下来的参数
                    // name: this.beforeParamsForm.name, // 会员姓名
                    // idNo: this.beforeParamsForm.idNo, //  身份证号码（18位证件号）
                    // local: this.beforeParamsForm.local, //  归属地（传国标地市编号，形如：110101）
                    // inviteCode: this.beforeParamsForm.inviteCode, //  邀请码
                    // payPassword: this.beforeParamsForm.payPassword, //  支付密码
                    // confirmPassword: this.beforeParamsForm.confirmPassword, //   确认支付密码
                    // passwordFree: "0", //  免密标志(1—开通免密；0—关闭免密)
                    // freeAmount: this.beforeParamsForm.freeAmount, //  免密金额（元）；passwordFree=1时，该字段为必输项。
                    authInfo: authInfo, //  人脸识别校验码（实人认证接口返回该值）
                };
                let res = await openAccount(params);
                console.log('接口res----开通电子钱包', res.data);
                // uni.removeStorageSync('person_info')
                if (res.success) {
                    await this.$sKit.commonUtil.eWalletNormal({
                        nextFun: () => {
                            this.$sKit.mpBP.tracker('e享卡开通', {
                                seed: 'eCardActiveBiz',
                                pageID: 'sucessPage', // 页面名
                                refer: this.personInfo.refer || '', // 来源
                                channelID: projectConfig.clientCode, // C10/C12/C13
                                address: this.personInfo.address,
                            });
                            let url = '/packages/third-electronic-wallet/pages/wallet-success/main';
                            let type = 'redirectTo'; // 默认  uni.navigateTo({})
                            this.$sKit.layer.useRouter(url, {}, type);
                        },
                        freezeReasonArr: [9, 10],
                        cancelCallback: () => {
                            // 获取用户非脱敏身份信息和获取电子卡迁移数据
                            this.activate = 'ktqb';
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                        walletAddParams: {
                            refer: 'r14',
                        },
                    });
                    return;
                }
            } catch (error) {
                uni.hideLoading();
            }
        },
    },
    // #endif
};
