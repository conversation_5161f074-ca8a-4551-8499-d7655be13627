<template>
    <div class="container">
        <oil-station-info :order="order" />

        <div class="content">
            <p class="content-pending">
                <img src="/static/icon_pending.png" alt="" />
                <span>等待买家付款</span>
            </p>
            <p class="content-time">剩余支付时间： 00:13:45</p>
            <button class="content-btn">继续付款</button>
        </div>
    </div>
</template>

<script>
import OilStationInfo from './oil-station-info.vue';

export default {
    components: { OilStationInfo },
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    .content {
        text-align: center;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        &-pending {
            color: #333333;
            font-weight: 500;
            font-size: 16px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            img {
                width: 12px;
                height: 15px;
                margin-right: 5px;
            }
        }
        &-time {
            color: #333333;
            font-size: 12px;
        }

        &-btn {
            margin: 0 auto;

            width: 150px;
            height: 44px;
            line-height: 44px;
            border-radius: 8px;
            color: #fff;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        }
    }
}
</style>
