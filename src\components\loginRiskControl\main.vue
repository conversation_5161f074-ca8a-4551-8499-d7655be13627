<template>
    <div class="pageMpaas p-LR-16">
        <zj-unrealized-authentication v-if="realNameDialogFlag" @realNameDialogClose="realNameDialogClose" @realNameInfo="realNameInfo">
        </zj-unrealized-authentication>
        <zj-agreement @enterNavEvent="enterNavEvent" v-if="facePop" @cancelClick="cancelClick"></zj-agreement>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { mapState } from 'vuex';
import { realNameAuth, realPersonIdentify, identityAuthInfo } from '../../s-kit/js/v3-http/https3/oilCard/index';
import { logoutApi } from '../../s-kit/js/v3-http/https3/user.js';
export default {
    name: 'loginRiskControl',
    components: {},
    props: {},
    data() {
        return {
            riskType: '',
        };
    },
    computed: {
        ...mapState({
            // 2.0token
            token: state => state.token,
            // 3.0token
            token3: state => state.token3,
            // 风控输入表单是否显示
            realNameDialogFlag: state => state.thirdLogin.realNameDialogFlag,
            // 3.0toke
            accessToken: state => state.thirdLogin.accessToken,
            // 风控验证人脸
            facePop: state => state.thirdIndex.facePop,
        }),
    },
    mounted() {},

    methods: {
        /**
         * @description  : 实人认证弹窗的确认事件
         * @return        {*}
         */
        async realNameInfo(val) {
            let params = {
                idNo: val.idNumber,
                name: val.name,
                type: '11',
            };
            // 关闭实人认证的表单弹窗
            this.$store.commit('setRealNameDialogFlag', false);
            try {
                let resRealName = await this.realNameAuthFun(params);
                let resWxFace = await this.wxFace(resRealName).catch(error => {
                    throw error;
                });
                let resRealPerson = await this.realNameAuthentication(resWxFace).catch(error => {
                    throw error;
                });

                if (resRealPerson.success) {
                    this.$emit('verificationPassed');
                } else {
                    throw new Error('Verification failed.');
                }
            } catch (error) {
                console.error(error); // 打印完整的错误对象
                console.log('进行验证的时候报错');
                this.verifFail('13');
            }
        },
        /**
         * @description  : 验证失败或出现异常执行的方法
         * @return        {*}
         */
        verifFail(val) {
            // 按钮变亮
            this.$store.commit('setLoginButtonGrayedOut', true);
            if (val == 13) {
                // 打开实人认证的表单弹窗
                this.$store.commit('setRealNameDialogFlag', true);
            } else if (val?.includes('14', '15')) {
                // 打开人脸验证弹窗
                this.$store.dispatch('changeFacePop', true);
            }
        },
        /**
         * @description  : 关闭实人认证弹窗
         * @return        {*}
         */
        async realNameDialogClose() {
            // 关闭实人认证表单弹窗
            this.$store.commit('setRealNameDialogFlag', false);
            // 退出登录  不知道为什么这么写暂时先去掉
            // this.logOutAndLogIn();
            // 按钮变亮
            this.$store.commit('setLoginButtonGrayedOut', true);
        },
        /**
         * @description  : 点击关闭按钮
         * @return        {*}
         */
        cancelClick() {
            // this.logOutAndLogIn();

            this.$store.commit('setFacePop', false);
            this.$store.commit('setLoginButtonGrayedOut', true);
        },
        async logOutAndLogIn() {
            let res = await logoutApi();
            uni.clearStorageSync();
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
        // 同意并开始验证
        async enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            try {
                // 获取用户信息
                let resIdentityInfo = await identityAuthInfo({}, { accessToken: this.accessToken });
                resIdentityInfo.data.name = resIdentityInfo.data.realName;
                resIdentityInfo.data.idNo = resIdentityInfo.data.identityNo;
                let resRealName = await this.realNameAuthFun(resIdentityInfo.data);
                let resWxFace = await this.wxFace(resRealName).catch(error => {
                    throw error;
                });
                console.log(resWxFace, '===resWxFace===');
                let resRealPerson = await this.realNameAuthentication(resWxFace).catch(error => {
                    throw error;
                });

                if (resRealPerson.success) {
                    console.log('实人认证resolve了吗');
                    this.$emit('verificationPassed');
                }
            } catch (error) {
                console.log(error, '登录风控===14 || ===15 进行验证的报错');
                this.verifFail([14, 15]);
            }
        },
        /**
         * @description  : 实名认证  (同意开通开通昆仑e享卡)
         * @param         {String} realName -用户在上个页面输入的姓名
         * @param         {String} idNo -用户在上个页面输入的身份证号
         * @param         {String} idType -身份证件类型 1:身份证 2:军人身份证件 3:台胞证 4:港澳通行证 5:外国人永久居留身份证 6:护照
         * @param         {String} type - 7-绑卡 1-电子账户注册 2-忘记密码 11-风控校验
         * @param         {String} authInfo - Type=7必传，传入验证码接口传过来的字串
         * @return        {*}
         */
        async realNameAuthFun(personInfo) {
            return new Promise(async (resolve, reject) => {
                try {
                    let params = {
                        realName: personInfo.name,
                        idType: '1',
                        idNo: personInfo.idNo,
                        authInfo: personInfo.authInfo || '',
                        type: personInfo.type || '1',
                    };
                    let res = await realNameAuth(params, { accessToken: this.accessToken });

                    if (res.data.authInfo && res.success) {
                        console.log('初始化实人认证接口返回', res.data);
                        let params = Object.assign(personInfo, res.data);
                        console.log(params, '初始化实人认证合并参数');
                        resolve(params);
                    } else {
                        reject(res);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        },
        /**
         * @description  :  微信人脸验证
         * @param         {String} name -实名认证的返回的用户的姓名
         * @param         {String} idCardNumber -实名认证的返回的用户的身份证号
         * @return        {*}
         */
        async wxFace(realNameAuthValue) {
            return new Promise((resolve, reject) => {
                console.log(realNameAuthValue, 'wx人脸验证接收参数');
                try {
                    // 刷人脸
                    wx.startFacialRecognitionVerify({
                        //姓名
                        name: realNameAuthValue.name,
                        //身份证号
                        idCardNumber: realNameAuthValue.idNo,
                        success: res => {
                            // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                            setTimeout(() => {
                                realNameAuthValue.verifyResult = res.verifyResult;
                                resolve(realNameAuthValue);
                            }, 500);
                        },
                        fail: err => {
                            // 验证失败时触发
                            // err 包含错误码，错误信息，弹窗提示错误
                            setTimeout(() => {
                                console.log(err);
                                wx.showModal({
                                    title: '提示',
                                    content: err.ErrorMsg,
                                    showCancel: false,
                                });
                            }, 500);
                            reject(err);
                        },
                    });
                } catch (error) {
                    reject(error);
                }
            });
        },
        /**
         * @description  :  实人认证
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
         * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
         * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
         * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
         * @param         {string} idNo: 用户身份证号
         * @return        {*}
         */
        async realNameAuthentication(wxOrZfbFaceValue) {
            return new Promise(async (resolve, reject) => {
                try {
                    // #ifdef MP-WEIXIN
                    let params = {
                        // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡； 10-资金转出；
                        type: wxOrZfbFaceValue.type || 1,
                        // 认证校验码，实名认证接口返回的数据。
                        authInfo: wxOrZfbFaceValue.authInfo,
                        //微信jsapi返回的加密key；若为微信小程序渠道，该字段为必填项。
                        wxVerifyResult: wxOrZfbFaceValue.verifyResult || '',
                        // 认证接入方式：1—APP接入；2—PC或H5接入；
                        verifyMode: '2',
                        // 当type=1时该字段为必填项。
                        idNo: wxOrZfbFaceValue.idNo,
                        gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
                    };
                    // #endif
                    console.log(params, '微信传入实人认证接口的参数');
                    let res = await realPersonIdentify(params, { accessToken: this.accessToken });
                    if (res.success && res.data.authInfo) {
                        console.log(params, '实人认证方法接口返回结果');
                        resolve(res);
                    } else {
                        reject(res);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped></style>
