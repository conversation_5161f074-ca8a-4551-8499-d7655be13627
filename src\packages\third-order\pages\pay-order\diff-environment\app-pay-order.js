import { mapGetters, mapState } from 'vuex';
export default {
    computed: {
        ...mapState({
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    mounted() {
        if (this.isHarmony) {
            this.$sKit.mpaasPayPlugin?.init();
        }
    },
    methods: {
        // 确认支付
        async callPlugIn(paramsData) {
            if (!this.isHarmony) {
                this.$paymentCenter.rechargePay(
                    {
                        paramsJsonStr: encodeURIComponent(JSON.stringify(paramsData)),
                    },
                    payRes => {
                        if (this.$paymentCenter.resStatus(payRes)) {
                            this.$sKit.layer.useRouter(
                                '/packages/third-remaining-sum/pages/third-charge-result/main',
                                { orderId: this.orderId, payType: 'orderPay', refer: 'r26', addressName: this.walletInfo.addressName },
                                'redirectTo',
                            );
                        } else {
                            this.isCanPay = true;
                            uni.showToast({
                                title: payRes.msg || '支付失败，请重试',
                                icon: 'none',
                                duration: 2000,
                            });
                        }
                    },
                );
            } else {
                console.log('🚀 ~ file: zfb-wallet-recharge.js:80 ~ callUpPayment ~ resMP:', paramsData);
                let params = {
                    areaCode: paramsData.areaCode,
                    bizOrderNo: paramsData.bizOrderNo,
                    rcvAmt: Number(paramsData.rcvAmt),
                    realAmt: Number(paramsData.realAmt),
                    // 3.0.4风控字段
                    extendFiled: JSON.stringify({
                        dfp: '',
                        gps:
                            this.riskManagementLonV3 && this.riskManagementLatV3
                                ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                                : '',
                        gpsProvince: '',
                        gpsCity: '',
                        gpsArea: '',
                    }),
                    payType: paramsData.payType,
                };
                const res = await this.$sKit.mpaasPayPlugin.RechargePay(params);
                this.payingFlag = false;
                if (res.code === 'PAY_SUCCESS') {
                    this.otherAmount = '';
                    this.$sKit.layer.useRouter(
                        '/packages/third-remaining-sum/pages/third-charge-result/main',
                        { orderId: paramsData.bizOrderNo, payType: 'orderPay' },
                        'redirectTo',
                    );
                } else {
                    this.isCanPay = true;
                    // 截取字符串后面的数据
                    let errIndex = res.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = res.msg.slice(0, errIndex);
                        customErr = res.msg.slice(errIndex + 1, res.msg.length);
                    } else {
                        customErr = res.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
