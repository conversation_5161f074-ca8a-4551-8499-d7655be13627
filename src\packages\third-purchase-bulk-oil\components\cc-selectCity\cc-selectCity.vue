<template>
	<view>
		<!-- 自定义地址选择器 -->
		<view v-show="show" class="cc_area_mask"></view>
		<view :class="['cc_area_view', show ? 'show' : 'hide']">
			<view class="cc_area_view_btns">
				<text class="cc_area_view_btn_cancle" @click="handleCancle">取消</text>
				<text class="cc_area_view_btn_title">地区选择</text>
				<text class="cc_area_view_btn_sure" @click="handleSelect">确定</text>
			</view>
			<picker-view :value="value" class="cc_area_pick_view" indicator-style="height: 35px;" @change="handleChange">
				<picker-view-column>
					<view v-for="(item, index) in provinces" :key="index" class="cc_area_colum_view">{{ item }}</view>
				</picker-view-column>
				<picker-view-column>
					<view v-for="(item, index) in citys" :key="index" class="cc_area_colum_view">{{ item }}</view>
				</picker-view-column>
				<picker-view-column>
					<view v-for="(item, index) in areas" :key="index" class="cc_area_colum_view">{{ item }}</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script>
import { getProvinces,
	getMyCity,
	getAreas,
	getAreasCode,
	getProvinceId,
	getCityId } from "./area.js";

export default {
	data() {
		return {
			provinces: [], // 省份列表
			citys: [], // 城市列表
			areas: [], // 区域列表
			value: [0, 0, 0], // 当前选中的索引
			index: [0, 0, 0] // 当前选中的索引（备份）
		};
	},
	props: {
		province: {
			type: String,
			default: ''
		},
		city: {
			type: String,
			default: ''
		},
		area: {
			type: String,
			default: ''
		},
		show: {
			type: Boolean,
			default: false
		},
		maskShow: {
			type: Boolean,
			default: true
		}
	},
	watch: {
		province: 'init',
		city: 'init',
		area: 'init'
	},
	mounted() {
		this.initData();
	},
	methods: {
		// 初始化数据
		initData() {
			this.provinces = getProvinces().map(item => item.name); // 只获取省份名称
			this.citys = getMyCity(0).map(item => item.name); // 只获取城市名称
			this.areas = getAreas(0, 0).map(item => item.name); // 只获取区域名称
			this.init();
		},
		// 初始化选中值
		init() {
			const provinceIndex = this.provinces.indexOf(this.province);
			const cityIndex = this.citys.indexOf(this.city);
			const areaIndex = this.areas.indexOf(this.area);
			
			if (provinceIndex !== -1 && cityIndex !== -1 && areaIndex !== -1) {
				this.value = [provinceIndex, cityIndex, areaIndex];
				this.index = [provinceIndex, cityIndex, areaIndex];
			} else {
				this.value = [0, 0, 0];
				this.index = [0, 0, 0];
			}
		},
		// 处理选择器变化
		handleChange(e) {
			const value = e.detail.value;
			this.index = value;
			
			// 更新城市和区域数据
			this.citys = getMyCity(value[0]).map(item => item.name);
			this.areas = getAreas(value[0], value[1]).map(item => item.name);
			
			// 更新选中值
			this.value = value;
			
			// 触发 change 事件
			const areaCode = getAreasCode(value[0], value[1], value[2]);
			this.$emit("changeClick", this.provinces[value[0]], this.citys[value[1]], this.areas[value[2]], areaCode);
		},
		// 处理确定按钮点击
		handleSelect() {
			const provinceName = this.provinces[this.index[0]];
			const cityName = this.citys[this.index[1]];
			const areaName = this.areas[this.index[2]];
			
			// 获取省、市、区的 ID
			const provinceId = getProvinceId(this.index[0]);
			const cityId = getCityId(this.index[0], this.index[1]);
			const areaId = getAreasCode(this.index[0], this.index[1], this.index[2]);
			
			// 返回数据
			this.$emit('sureSelectArea', {
				detail: {
					province: provinceName,
					city: cityName,
					area: areaName,
					provinceId: provinceId,
					cityId: cityId,
					areaId: areaId
				}
			});
			
			// 重置状态
			this.resetState();
		},
		// 处理取消按钮点击
		handleCancle() {
			this.$emit('hideShow', { detail: false });
			this.resetState();
		},
		// 重置状态
		resetState() {
			this.index = [0, 0, 0];
			this.value = [0, 0, 0];
			this.updateCityAndArea(0, 0);
		},
		// 更新城市和区域数据
		updateCityAndArea(provinceIndex, cityIndex) {
			this.cityData = getMyCity(provinceIndex); // 获取城市完整数据
			this.citys = this.cityData.map(item => item.name); // 只提取城市名称
			
			this.areaData = getAreas(provinceIndex, cityIndex); // 获取区域完整数据
			this.areas = this.areaData.map(item => item.name); // 只提取区域名称
		},
	}
};
</script>

<style lang='scss' scoped>
.cc_area_view {
	width: 100%;
	position: fixed;
	bottom: -1000px;
	left: 0;
	background-color: #fff;
	z-index: 21;
	transition: all 0.3s;
}

.cc_area_pick_view {
	height: 400px;
	width: 100%;
}

.cc_area_colum_view {
	line-height: 35px;
	text-align: center;
	font-size: 28upx;
}

.hide {
	bottom: -1000upx;
	transition: all 0.3s;
}

.show {
	bottom: 0;
	transition: all 0.3s;
}

.cc_area_view_btns {
	background-color: #fff;
	border-bottom: 1px solid #eee;
	font-size: 30upx;
	padding: 18upx 0;
	display: flex;
	justify-content: space-between;
}

.cc_area_view_btns > text {
	display: inline-block;
	word-spacing: 4upx;
	letter-spacing: 4upx;
}

.cc_area_view_btn_cancle {
	color: #939393;
	padding: 0 20upx 0 25upx;
}

.cc_area_view_btn_sure {
	color: #4284e5;
	padding: 0 25upx 0 20upx;
}

.cc_area_mask {
	width: 100%;
	height: 100vh;
	background-color: rgba(28, 28, 28, 0.6);
	position: absolute;
	top: 0;
	left: 0;
	z-index: 20;
}
</style>