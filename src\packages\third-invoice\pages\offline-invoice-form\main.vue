<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <!-- 添加发票抬头页面 -->
    <div class="pageMpaas">
        <div class="p-bf fl-column add-center">
            <zj-navbar :border-bottom="false" :title="title"></zj-navbar>
            <div class="content">
                <div class="order-info">
                    <div class="order-title">订单号</div>
                    <div class="order-amount">{{ orderInfo.orderNo }}</div>
                </div>
                <div class="open-ticket-box">
                    <div class="section-box section-box2 line_bottom">
                        <div class="left"> <div class="required" style="display: inline-block">*</div>抬头类型 </div>
                        <div class="right type">
                            <div class="right-item" v-for="(item, index) in typeList" :key="index" @click="changeType(index)">
                                <img v-if="item.tag == invoiceInfo.buyerNature" src="../../image/type-checked.png" alt />
                                <img v-else src="../../image/type-unchecked.png" alt />
                                <div class="right-text">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left"> <span class="required">*</span>抬头名称 </div>
                        <div class="right">
                            <!-- #ifdef MP-ALIPAY -->
                            <input
                                v-model="invoiceInfo.buyerName"
                                class="textarea_style"
                                style="width: 100% !important; text-align: right"
                                placeholder="请输入抬头名称"
                                disable-default-padding
                                @focus="subscribe"
                            />
                            <!-- #endif -->
                            <!-- #ifdef MP-WEIXIN -->
                            <input
                                v-model="invoiceInfo.buyerName"
                                class="textarea_style"
                                style="width: 100% !important; text-align: right"
                                placeholder="请输入抬头名称"
                                disable-default-padding
                                @click="subscribe"
                            />
                            <!-- #endif -->
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">税号</div>
                        <div class="right">
                            <input type="text" class="textarea_style" v-model="invoiceInfo.buyerTaxId" placeholder="请输入税号" />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">地址</div>
                        <div class="right">
                            <input
                                class="textarea_style"
                                type="text"
                                v-model="invoiceInfo.buyerAddr"
                                placeholder="请输入地址"
                                disable-default-padding
                            />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">电话</div>
                        <div class="right">
                            <input
                                type="text"
                                class="textarea_style"
                                @input="buyerTelInput"
                                v-model="invoiceInfo.buyerTel"
                                placeholder="请输入联系方式"
                            />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">开户银行</div>
                        <div class="right">
                            <input
                                class="textarea_style"
                                type="text"
                                v-model="invoiceInfo.buyerFinancial"
                                placeholder="请输入开户银行"
                                disable-default-padding
                            />
                        </div>
                    </div>
                    <div class="section-box">
                        <div class="left">银行账号</div>
                        <div class="right">
                            <input type="text" class="textarea_style" v-model="invoiceInfo.buyerAccount" placeholder="请输入银行账号" />
                        </div>
                    </div>
                    <div class="section-box">
                        <div class="left"><div class="required" style="display: inline-block" v-if="!allowMessagePush">*</div>发票预览</div>
                        <div class="right">
                            <input type="number" class="textarea_style" v-model="invoiceInfo.phone" placeholder="请输入您的手机号" />
                        </div>
                    </div>
                </div>
                <div class="add-footer" @click="submit">去开票</div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { getAlipayUserOpenId, getWechatUserOpenId } from '../../../../s-kit/js/v3-http/https3/user';
import { messageTmpIds } from '../../../../../project.config';
import { createInvoiceApi } from '../../../../s-kit/js/v3-http/https3/invoice';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'offlineInvoiceForm',
    data() {
        return {
            // 导航文字
            title: '开发票',
            //抬头类型
            typeList: [
                {
                    name: '非自然人',
                    tag: '3',
                },
                {
                    name: '自然人',
                    tag: '4',
                },
            ],
            // 表单绑定值
            invoiceInfo: {
                // 抬头类型
                buyerNature: 3,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
                phone: '',
            },
            orderInfo: {},
            openId: '',
            allowMessagePush: false,
            isFirst: true,
        };
    },
    onLoad(options) {
        console.log(options, '接收到的参数');
        this.getOpenId();
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        console.log(options.data, '接收到的参数1');
        this.orderInfo = JSON.parse(decodeURIComponent(options.data));
        // #endif
        // #ifdef MP-WEIXIN
        if (options) {
            this.orderInfo = options;
        } else {
            this.orderInfo = getApp().globalData.pageParams;
        }
        // #endif
        console.log(JSON.stringify(this.orderInfo), '接收到的参数2');
        this.submit = this.$sKit.commonUtil.throttleUtil(this.submit);
    },
    methods: {
        getOpenId() {
            // #ifdef MP-ALIPAY
            my.getAuthCode({
                success: async res => {
                    console.log(res, 'code');
                    let httpRes = await getAlipayUserOpenId({ authToken: res.authCode });
                    if (httpRes && httpRes.success) {
                        this.openId = httpRes.data.openId;
                    }
                },
            });
            // #endif
            // #ifdef MP-WEIXIN
            wx.login({
                success: async res => {
                    console.log(res, 'code');
                    let httpRes = await getWechatUserOpenId({ jsCode: res.code });
                    if (httpRes && httpRes.success) {
                        this.openId = httpRes.data.openId;
                    }
                },
            });
            // #endif
        },
        subscribe() {
            if (!this.allowMessagePush && this.isFirst) {
                const projectTmpId = messageTmpIds.split(',')[0];
                this.$sKit.layer.requestSubscribeMessage({
                    tmplIds: [projectTmpId],
                    success: res => {
                        console.log(res, '订阅');
                        this.isFirst = false;
                        // #ifdef MP-WEIXIN
                        if (res[projectTmpId] == 'accept') {
                            this.allowMessagePush = true;
                        }
                        // #endif
                        // #ifdef MP-ALIPAY
                        if (res.behavior == 'subscribe') {
                            this.allowMessagePush = true;
                        }
                        // #endif
                    },
                    fail: res => {},
                    complete: res => {},
                });
            }
        },
        buyerTelInput(e) {
            let str = e.detail.value.toString().match(/\S/g).join('');
            setTimeout(_ => {
                this.invoiceInfo.buyerTel = str;
            }, 10);
        },
        /**
         * @description  : 抬头类型change事件，修改默认抬头类型绑定值
         * @param         {*} index:序号
         * @return        {*}
         */
        changeType(index) {
            let newInvoiceInfo = {
                // 抬头类型
                buyerNature: this.typeList[index].tag,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
            };
            if (this.invoiceInfo.headerId) {
                newInvoiceInfo.headerId = this.invoiceInfo.headerId;
            }
            this.invoiceInfo = Object.assign(this.invoiceInfo, newInvoiceInfo);
        },

        /**
        * @description  :    提交点击事件
                             如果是个人的话，抬头名称验证下中文和中间点，企业的话，不验证抬头输入的格式
                             发票类型为个人税号为非必填不校验，企业必填校验
                             当前字符串中不能有空格
        * @return        {*}
        */
        async submit() {
            if (this.invoiceInfo.buyerName == '') {
                this.$sKit.layer.showToast({ title: '抬头名称不能为空' });
                return;
            }
            if (this.invoiceInfo.buyerNature == 4) {
                if (!this.$sKit.test.checkSAndE(this.invoiceInfo.buyerName)) {
                    this.$sKit.layer.showToast({ title: '请输入正确的抬头名称' });
                    return;
                }
            } else {
                if (this.invoiceInfo.buyerName.length > 50 || !this.$sKit.test.enterpriseTitleCheck(this.invoiceInfo.buyerName)) {
                    this.$sKit.layer.showToast({ title: '请输入正确的抬头名称' });
                    return;
                }
            }
            //税号只校验格式不校验必填
            if (!this.$sKit.test.checkShuiHao_individual_third(this.invoiceInfo.buyerTaxId)) {
                return;
            }
            if (this.invoiceInfo.buyerTel !== '' && !this.$sKit.test.checkTelPhone(this.invoiceInfo.buyerTel)) {
                this.$sKit.layer.showToast({ title: '请输入正确的电话号码' });
                return;
            }
            if (this.invoiceInfo.buyerAddr !== '' && this.invoiceInfo.buyerAddr?.length > 40) {
                this.$sKit.layer.showToast({ title: '请输入正确的地址' });
                return;
            }
            if (this.invoiceInfo.buyerFinancial !== '' && !this.$sKit.test.checkBankName(this.invoiceInfo.buyerFinancial)) {
                this.$sKit.layer.showToast({ title: '请输入正确的开户银行' });
                return;
            }
            if (this.invoiceInfo.buyerAccount !== '' && !this.$sKit.test.newCheckBankCount(this.invoiceInfo.buyerAccount)) {
                this.$sKit.layer.showToast({ title: '请输入正确的银行账号' });
                return;
            }
            if (!this.allowMessagePush) {
                if (this.invoiceInfo.phone == '' || !this.$sKit.test.checkTelPhone(this.invoiceInfo.phone)) {
                    this.$sKit.layer.showToast({ title: '请输入正确的发票预览手机号码' });
                    return;
                }
            }
            this.invoiceInfo.phone = this.invoiceInfo.phone.replace(/\s/g, '');
            let params = {
                openId: this.openId,
                orderList: [this.orderInfo],
                stationCode: this.orderInfo.stationCode,
                qrCode: this.orderInfo.qrCode,
                scanChannel: this.orderInfo.scanChannel,
                subscribeFlag: this.allowMessagePush,
                ...this.invoiceInfo,
                InvoiceOrderInfo: {
                    orderNo: this.orderInfo.orderNo,
                    businessDay: this.orderInfo.businessDay,
                    stationCode: this.orderInfo.stationCode,
                },
            };
            let res = await createInvoiceApi(params);
            if (res && res.success) {
                this.$store.dispatch('zjShowModal', {
                    content: '开票成功',
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                        }
                    },
                });
            }
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 15px;
$colorgray: #909090;

.add-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .content {
        width: 100%;
        flex: 1;
        background: #f7f7fb;
        padding: 36rpx 32rpx;

        .order-info {
            width: 100%;
            height: 165px;
            background: #ffffff;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 36px 0;

            .order-title {
                font-size: 15px;
                color: #333333;
                line-height: 21px;
                text-align: center;
            }

            .order-amount {
                font-weight: bold;
                color: #333333;
                font-size: 20px;
                line-height: 20px;
                text-align: center;
                display: flex;
                align-items: flex-end;

                .order-icon {
                    font-size: 36px;
                    line-height: 39px;
                    font-weight: bold;
                    color: #333333;
                    text-align: center;
                    margin-right: 4px;
                }
            }
        }

        .open-ticket-box {
            width: 100%;
            border-radius: 24rpx;
            background: #fff;
            margin-top: 14px;

            .section-box {
                padding: 24rpx 28rpx;
                // border-top: solid 2rpx #eee;
                display: flex;
                align-items: flex-start;
                min-height: 44px;

                .left {
                    height: 20px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: #333;
                    min-width: 170rpx;

                    .required {
                        color: #e64f22;
                        font-size: 14px;
                    }
                }

                .right {
                    flex: 1;

                    .textarea_style {
                        width: 100%;
                        text-align: right;
                        // min-height: 40rpx;
                        height: 40rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 40rpx;
                        padding: 0;
                    }

                    .right-item {
                        display: flex;
                        align-items: center;

                        &:first-child {
                            margin-right: 56rpx;
                        }

                        img {
                            width: 40rpx;
                            height: 40rpx;
                            margin-right: 8rpx;
                        }

                        .right-text {
                            line-height: 40rpx;
                        }
                    }
                }

                .type {
                    display: flex;
                    justify-content: flex-end;
                }
            }

            .section-box2 {
                padding: 24rpx 28rpx;
            }
        }

        .add-footer {
            width: 100%;
            height: 88rpx;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            font-size: 36rpx;
            font-weight: bold;
            color: #ffffff;
            line-height: 88rpx;
            text-align: center;
            margin-top: 32rpx;
            border-radius: 16rpx;
        }
    }
}

.bold {
    font-weight: bold;
}

.no-bold {
    font-weight: 400;
}
</style>
