<template>
    <div>
        <TransparentMask v-show="maskDialogFlagMy" class="mask-dialog"></TransparentMask>
        <div class="setting-center">
            <u-navbar
                :back-icon-size="40"
                :height="44"
                :background="pageConfig.bgColor"
                :back-icon-color="pageConfig.titleColor.backIconColor"
                :title-color="pageConfig.titleColor.color"
                back-text="设置"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>
            <div class="section">
                <!-- <div class="reasons">
        <div class="left label-txt">姓名</div>
        <div style="flex:1"></div>
        <div class="right">
          <img src="@/static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
        </div>
                </div>-->
                <!-- <div class="reasons">
        <div class="left label-txt">证件号码</div>
        <div style="flex:1"></div>
        <div class="right">
          <img src="@/static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
        </div>
                </div>-->
                <div class="reasons">
                    <div class="left label-txt">注册手机号</div>
                    <div style="flex: 1"></div>
                    <div class="right">{{ registerLoginInformation.phone | phoneFilter }}</div>
                </div>

                <!-- <navigator url="/pages/my-center/invoice-center" hover-class="none" class="reasons">
        <div class="left label-txt title-bold">发票设置</div>
        <div style="flex:1"></div>
        <div class="right">
          <img src="../../static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
        </div>
                </navigator>-->
                <!-- <navigator url="/pages/my-center/set-mobile-payment" hover-class="none" class="reasons">
			  <div class="left label-txt title-bold">移动支付设置</div>
			  <div style="flex:1"></div>
			  <div class="right">
			    <img src="../../static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
			  </div>
                </navigator>-->
                <!-- <navigator :url="'/packages/password/pages/' + (!isPassword ? 'edit-password/main?isfor=0' : 'home/main')" hover-class="none" class="reasons">
			  <div class="left label-txt">{{isPassword ? '重置密码' : '设置密码'}}</div>
			  <div style="flex:1"></div>
			  <div class="right">
			    <img src="@/static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
			  </div>
                </navigator>-->
                <!-- 与个人中心里的“车牌管理”一样 -->
                <!-- <navigator url="/pages/home/<USER>/venicleManage" hover-class="none" class="reasons">
        <div class="left label-txt title-bold">车牌设置</div>
        <div style="flex:1"></div>
        <div class="right">
          <img src="../../static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
        </div>
                </navigator>-->

                <!-- <navigator url="/pages/my-center/version/version-list" hover-class="none" class="reasons">
        <div class="left label-txt title-bold">功能介绍</div>
        <div style="flex:1"></div>
        <div class="right">
          <img src="../../static/homeIcon/rightjt.png" alt="" class="arrow-img" mode="widthFix">
        </div>
                </navigator>-->
                <navigator v-if="!token3" url="/packages/password/pages/card-list/main" hover-class="none" class="reasons">
                    <div class="left label-txt">重置支付密码</div>
                    <div style="flex: 1"></div>
                    <div class="right">
                        <img src="@/static/homeIcon/rightjt.png" alt class="arrow-img" mode="widthFix" />
                    </div>
                </navigator>

                <navigator url="/packages/setting/pages/agreement/main?value=17" hover-class="none" class="reasons">
                    <div class="left label-txt">用户协议</div>
                    <div style="flex: 1"></div>
                    <div class="right">
                        <img src="@/static/homeIcon/rightjt.png" alt class="arrow-img" mode="widthFix" />
                    </div>
                </navigator>

                <navigator url="/packages/setting/pages/agreement/main?value=63" hover-class="none" class="reasons">
                    <div class="left label-txt">隐私政策</div>
                    <div style="flex: 1"></div>
                    <div class="right">
                        <img src="@/static/homeIcon/rightjt.png" alt class="arrow-img" mode="widthFix" />
                    </div>
                </navigator>

                <div class="reasons-bg" @click="clickEscLogin">
                    <div class="logout">退出登录</div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { logoutApi } from '../../../../s-kit/js/v3-http/https3/user.js';
import { unBindThirdUser } from '@/api/home.js';
import TransparentMask from '@/components/transparentMask/main.vue';
import { mapGetters, mapState } from 'vuex';
export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            maskDialogFlagMy: false,
        };
    },
    computed: {
        ...mapGetters(['isPassword', 'registerLoginInformation']),
        ...mapState({
            moduleBannerList: state => state.location.moduleBannerList, // 已定义菜单数组
            bannerListCity: state => state.location.bannerListCity, // 城市轮播图
            bannerListCountry: state => state.location.bannerListCountry, // 省份轮播图
            token3: state => state.token3,
        }),
    },
    filters: {
        phoneFilter(val) {
            let reg = /^(.{3}).*(.{4})$/;
            if (val) {
                return val.replace(reg, '$1****$2');
            } else {
                return '';
            }
        },
    },
    onLoad() {},
    methods: {
        async clickEscLogin() {
            this.$store.commit('setAdvertisFlag', false);
            let that = this;
            uni.showModal({
                title: '提示',
                content: '登出后，下次使用中石油加油服务需要再次登录',
                cancelText: '退出登录',
                cancelColor: '#1677FF',
                confirmText: '暂不退出',
                confirmColor: '#1677FF',
                success: res => {
                    if (!res.confirm) {
                        this.maskDialogFlagMy = true;
                        that.logout();
                    }
                },
            });
        },
        async logout() {
            let res = await unBindThirdUser({ regChannel: 6 });
            let res2 = await logoutApi()
            if (res2.success) {
                this.$store.commit('mpUserInfo', null);
                this.$store.commit('setCarListV3', []);
                this.$store.state.token3 = '';
                this.$store.state.token = '';
                this.$store.dispatch('setOfficialAccountParams', '');
                this.$store.commit('setLoginThirdFlag', false);
                this.$store.commit('setVenicleList', []);
                this.$store.commit('card/setCardList', []);
                this.$store.commit('setBannerListCity', []);
                this.$store.commit('setBannerListCountry', []);
                this.$store.state.location.moduleBannerListShow = false;
                this.$store.state.location.moduleBannerList = [];
                this.$store.commit('card/setIsHaveECard', false);
                this.$store.commit('card/setIsHaveEntityCard', false);
                this.$store.state.location.advertisementFlag = 0;
                this.$store.state.location.myPageAdvertisementFlag = 0;
                this.$store.commit('setSource', '');
                this.$store.commit('setLoginStatus', false);
                this.$store.commit('mSetPersonalInformation3', {});
                this.$store.commit('setUserInfo', {});
                this.$store.commit('setMpUserInfo', {});
                this.$store.commit('setLongTimeNotLogin', null);
                uni.setStorageSync('tokenInfo', '');
                wx.clearStorageSync();
                uni.reLaunch({
                    url: '/pages/thirdHome/main',
                });
            }
            if (res.status == 0) {

            }
        },
    },
    components: {
        TransparentMask,
    },
    destroyed() {
    },
};
</script>

<style lang='scss' scoped>
$font14: 14px;
$font15: 15px;
$colorgray: #333333;
.setting-center {
    width: 100%;
    height: 100vh;
    background: #f6f6f6;

    /* .section {
    padding: 10px 15px;

    .reasons {
      display: flex;
      background: #ffffff;
      border-radius: 5px;
      padding: 15px 10px;
      margin-bottom: 10px;
    }
  }

  .reasons-bg {
    width: 100%;
    text-align: center;
    background: $btn-mantle-color;
    border-radius: 5px;
    border: 1px solid $btn-color;
    font-size: $font15;
    font-weight: 500;
    color: $btn-color;
    line-height: 40px;
  }

  .label-txt {
    color: $colorgray;
    font-size: $font15;
  }

  .title-bold {
    font-weight: bold;
  }

  .arrow-img {
    width: 5px;
  } */

    .section {
        padding: 10px 0;

        .reasons {
            display: flex;
            background: #ffffff;
            border-radius: 5px;
            padding: 15px 10px;
        }
    }

    .reasons-bg {
        width: 100%;

        .logout {
            text-align: center;
            background: #fff;
            border-radius: 5px;
            border: 1px solid #eee;
            font-size: $font15;
            color: #333;
            line-height: 46px;
            margin: 30px 24rpx 0;
        }
    }

    .label-txt {
        color: $colorgray;
        font-size: $font15;
    }

    .title-bold {
        font-weight: bold;
    }

    .arrow-img {
        width: 5px;
    }
}
</style>
