<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="更换手机号"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <div class="line_bottom"></div>
                <div class="reminder p-LR-16 bg-FFF7DC font-12 weight-400 color-333"
                    >您以下电子券与当前手机号绑定，暂不支持变更到新的手机号上</div
                >
                <div class="coupon-wrap f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="dataList"
                        emptyText="暂无电子券"
                        :showEmpty="showEmpty"
                        :emptyImage="noPicture"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                    >
                        <div class="padding-16">
                            <div v-for="(item, index) in couponArray" :key="index">
                                <div
                                    class="border-rad-8"
                                    :class="{
                                        'bg-coupon': item.bgColor,
                                        toTakeEffect: !item.bgColor,
                                    }"
                                >
                                    <div
                                        class="upperLeft font-10 weight-500 color-fff te-center bg-ff6133"
                                        :class="{
                                            'bg-ff6133': item.bgColor,
                                            'bg-999': !item.bgColor,
                                        }"
                                        >{{ handleCoupons(item) }}</div
                                    >
                                    <div class="content-wrap fl-row">
                                        <div class="left-wrap fl-row">
                                            <div class="content-left fl-column fl-al-jus-cen">
                                                <div
                                                    class="price fl-row fl-al-base"
                                                    :class="{
                                                        'color-E64F22': item.bgColor,
                                                        'color-666': !item.bgColor,
                                                    }"
                                                >
                                                    <div
                                                        class="symbol font-14 weight-400"
                                                        v-if="item.couCategory && item.couCategory != '40'"
                                                        >&yen;</div
                                                    >
                                                    <div v-if="item.couCategory && item.couCategory != '40'" class="font-28 weight-600">{{
                                                        item.facevalue
                                                    }}</div>

                                                    <div
                                                        class="symbol font-14 weight-400"
                                                        v-if="item.couCategory && item.couCategory == '40'"
                                                    >
                                                        <div class="font-28 weight-600">{{ item.discountStr }}</div
                                                        >折
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="content-sx"></div>
                                        </div>
                                        <div class="right-wrap fl-column fl-jus-bet">
                                            <div class="title font-14 color-1E1E1E weight-600" style="margin-top: 3px">{{
                                                item.typetitle
                                            }}</div>
                                            <div class="fl-row fl-jus-bet fl-al-end">
                                                <div class="fl-column fl-jus-cen">
                                                    <div
                                                        class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400"
                                                        :class="{
                                                            'color-FA6400 border-fa6400': item.bgColor,
                                                            'color-666 border-999': !item.bgColor,
                                                        }"
                                                        >{{ handleCoupons(item) }}</div
                                                    >
                                                    <div class="time font-12 color-999 weight-400">有效期至：{{ item.couenddate }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
const PAGE_SIZE = 10;
import { appJsonCouponUnusedcoupons } from '../../../../s-kit/js/v3-http/https3/wallet';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            couponArray: [],
            page: 1,
            topState: 0,
            categoryType: '',
            noPicture: '',
            token: '',
        };
    },
    async mounted() {
        let userInfo;
        // #ifdef MP-MPAAS
        userInfo = await this.$cnpcBridge.getUserTokenInfo();
        this.token = userInfo.token;
        // #endif

        // 获取电子券列表
        this.getCouponList();
    },
    methods: {
        // 前往电子券详情页
        enterTicketDetail(item) {
            if (item.bgColor) {
                this.$sKit.layer.useRouter(
                    '/packages/third-coupon-module/pages/conopn-unuse-detail/main',
                    { couponInfo: item },
                    'navigateTo',
                );
            }
        },
        // 电子券券面按钮文字
        buttonText(item) {
            return item.bgColor ? '立即使用' : '未生效';
        },
        // 券类型
        handleCoupons(val) {
            let bizTypeText = '';
            switch (val.biztype) {
                case '9':
                    // 商城劵
                    bizTypeText = '商城券';
                    break;
                case '8':
                    //非油券
                    bizTypeText = '非油券';
                    break;
                case '':
                    //异业券
                    bizTypeText = '异业券';
                    break;
                case '5':
                    bizTypeText = '油品券';
                    break;
                case '6':
                    bizTypeText = '汽油券';
                    break;
                case '7':
                    bizTypeText = '柴油券';
                    break;
            }
            return bizTypeText;
        },
        // 上拉加载
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getCouponList();
            }
        },
        // 下拉刷新
        refreshPullDown(e) {
            // 重置数据获取电子券列表
            this.getCouponList({ isInit: true });
        },
        //获取电子券列表
        async getCouponList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    couponArray: [],
                    page: 1,
                    topState: 0,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, couponArray, totalPage } = this;
            let params = {
                pageNum: this.page,
                pageSize: PAGE_SIZE,
                categoryAlias: '',
                token: this.token,
                sortName: '',
                sortType: '',
            };
            let res = await appJsonCouponUnusedcoupons(params);
            if (res.data.length) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data;
                const currentTimestamp = new Date().getTime();
                list = list.map(item => {
                    var handelTime = new Date(item.coustartdate.replace(/-/g, '/'));
                    if (currentTimestamp > Date.parse(handelTime)) {
                        // 到使用日期
                        item.bgColor = true;
                    } else {
                        // 未到使用日期
                        item.bgColor = false;
                    }
                    return item;
                });
                couponArray = couponArray.concat(list);
                Object.assign(this, {
                    couponArray,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                // totalPage = res.data.pageSum
                if (res.data.length < PAGE_SIZE) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = couponArray.length <= 0 ? true : false;
            }
        },
        // 处理时间(10.08与产品确定列表只展示年月日，详情展示时分秒)
        screen(item) {
            let text = '';
            if (this.selectid == 20) {
                if (item.bgColor) {
                    text = '有效期至：' + item.endTime.slice(0, 10);
                } else {
                    text = '开始日期：' + item.startTime.slice(0, 10);
                }
            } else {
                text = '有效期至：' + item.endTime.slice(0, 10);
            }
            return text;
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;

        .tab_style {
            margin-right: 22px;
            line-height: 20px;
        }

        .selected {
            color: #e64f22;
        }
    }

    .type-sort {
        width: 100%;
        height: 44px;

        .sort-item {
            height: 30px;
            line-height: 30px;
            margin-right: 12px;
            padding: 0 6.5px; // 上右下左
        }

        .selectSorted {
            color: #e64f22;
        }
    }

    .reminder {
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
    }

    .coupon-wrap {
        .bg-coupon {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    width: 30%;

                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }

                    .content-sx {
                        height: 136rpx;
                        opacity: 0.5;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }

                .right-wrap {
                    width: 70%;
                    height: 100%;
                    padding: 12px 0;

                    .title {
                        margin-right: 22px;
                        overflow: hidden;
                        // max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        font-family: PingFangSC-Medium, PingFang SC;
                    }

                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }

                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }

                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }

        // 券未生效
        .toTakeEffect {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    width: 30%;

                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }

                    .content-sx {
                        height: 136rpx;
                        opacity: 0.3;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }

                .right-wrap {
                    width: 70%;
                    height: 100%;
                    padding: 12px 0;

                    .title {
                        margin-right: 22px;
                        overflow: hidden;
                        max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        font-family: PingFangSC-Medium, PingFang SC;
                    }

                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }

                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }

                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }
    }

    .btnWrap {
        width: 100%;
        height: 44px;
        line-height: 44px;
        // padding-bottom: 15px;
        margin: 0 auto 10px;
    }
}
</style>
