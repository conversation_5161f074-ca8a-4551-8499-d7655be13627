import checkFKArgs from './checkFKArgs';
import MPassBuryingPoint from '@/s-kit/js/mpass-burying-point/index.ts';
const mpBP = new MPassBuryingPoint();
/**
 *  小程序与app通信桥梁
 * @param callback
 */
const accountCenter = {
    async handleCenter(jsonData, callBack) {
        if (jsonData.method !== 'openEwallet')
            //开通电子钱包单独调用了设置风控字段，因为要多传个idno
            await accountCenter.updateExtendFiled();
        jsonData.bizType = 'accountCenter';
        my.call('mriverToNative', jsonData, data => {
            console.log(jsonData.method + ':' + JSON.stringify(data));
            if (callBack) callBack(data);
            if (!data.isSuccessed) {
                let bizMsg = data.desString.replace(/,/g, ' ');
                mpBP.tracker('业务异常埋点', {
                    seed: 'biz_exp',
                    clientSdkError: 'client_001', // 返回sdk标识
                    clientSdkApi: jsonData.method,
                    clientSdkCode: '',
                    clientSdkMsg: bizMsg,
                });
            }
        });
    },
    /**
     * 初始化sdk
     * @param  = {memberType = 37;memberNo = 37;clientToken = "rgjhv";};
     * @param callBack
     */
    initAccountSDK(gsmsToken, callBack) {
        let jsonData = {
            method: 'initPay',
            data: {
                accountCenterParams: {
                    token: gsmsToken,
                },
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * changePW修改密码
     * @param params
     * @param callBack
     */
    changePW(callBack) {
        let jsonData = {
            method: 'changePW',
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * forgotPW 忘记密码
     * @param params={verifyCode = 37234;//验证码};
     * @param callBack
     */
    forgotPW(params, callBack) {
        let jsonData = {
            method: 'forgotPW',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
   * capitalOutflow资金转出
   * @param params= {
cardNo = "37234";//卡号
amount = "2345";//金额
};
   * @param callBack
   */
    capitalOutflow(params, callBack) {
        let jsonData = {
            method: 'capitalOutflow',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
   * openEwallet开通电子钱包
   * @param params {
usedPlace = "XXX";// 常用地
idNumber = "2345";//身份证号
};
     * @param callBack
     */
    openEwallet(params, callBack) {
        let jsonData = {
            method: 'openEwallet',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * cancelEwallet注销电子钱包
     * @param params
     * @param callBack
     */
    cancelEwallet(params, callBack) {
        let jsonData = {
            method: 'cancelEwallet',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * bindCard绑定加油卡
     * @param params
     * @param callBack
     */
    bindCard(params, callBack) {
        let jsonData = {
            method: 'bindCard',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * getVerifyCode获取验证码
     * @param params   type = 1;//验证码类型：1 重置支付密码；2 注销电子钱包
     * @param callBack
     */
    getVerifyCode(params, callBack) {
        let jsonData = {
            method: 'getVerifyCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * getPayCode生成支付码
     * @param params   cardIdx
     * @param callBack
     */
    getPayCode(params, callBack) {
        let jsonData = {
            method: 'getPayCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * deleteMember清除某个会员数据
     * @param params    type = 1;//会员类型  no = 34677;//会员编号
     * @param callBack
     */
    deleteMember(params, callBack) {
        let jsonData = {
            method: 'deleteMember',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * deleteAllMember清除所有会员数据
     * @param callBack
     */
    deleteAllMember(callBack) {
        let jsonData = {
            method: 'deleteAllMember',
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * openOrUpdateBiometricCode  开通或者更新生物识别
     * @param params
     * @param callBack
     */
    openOrUpdateBiometricCode(params, callBack) {
        let jsonData = {
            method: 'openOrUpdateBiometricCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * closeBiometricCode  关闭生物识别
     * @param params
     * @param callBack
     */
    closeBiometricCode(params, callBack) {
        let jsonData = {
            method: 'closeBiometricCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * hasBiometricCode  生物识别开通状态
     * @param callBack
     */
    hasBiometricCode(callBack) {
        let jsonData = {
            method: 'hasBiometricCode',
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * hasBiometricTypes  生物识别开通类型
     * @param callBack
     */
    hasBiometricTypes(callBack) {
        let jsonData = {
            method: 'hasBiometricTypes',
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * updateExtendFiled  设置风控字段
     */
    updateExtendFiled(idNo, callBack) {
        return new Promise(async (resolve, reject) => {
            let fkInfo = await checkFKArgs.getFKArgs('sdk');
            //开通电子钱包的时候，extendFiled需增加证件号
            if (idNo) {
                fkInfo.identityNo = idNo;
            }

            let jsonData = {
                bizType: 'accountCenter',
                method: 'updateExtendFiled',
                data: {
                    accountCenterParams: { extendFiled: encodeURIComponent(JSON.stringify(fkInfo)) },
                },
            };
            console.log('账户SDK设置风控参数：', jsonData);

            my.call('mriverToNative', jsonData, data => {
                console.log(jsonData.method + ':' + JSON.stringify(data));

                if (callBack) callBack(data);
            });
            resolve();
        });
    },
    /**
     * 刷新token
     * @param callBack
     */
    refreshToken(gsmsToken, callBack) {
        let jsonData = {
            method: 'refreshToken',
            data: {
                accountCenterParams: { token: gsmsToken },
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * bindGiftCard绑定礼品卡
     * @param params
     * @param callBack
     * cardCode // 绑卡参数
     */
    bindGiftCard(params, callBack) {
        let jsonData = {
            method: 'giftCardBindCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
    /**
     * queryGiftCard礼品卡-查卡密
     * @param params
     * @param callBack
     * cardNo // 查卡密参数
     */
    giftCardQueryCode(params, callBack) {
        let jsonData = {
            method: 'giftCardQueryCode',
            data: {
                accountCenterParams: params,
            },
        };
        accountCenter.handleCenter(jsonData, callBack);
    },
};
export default accountCenter;
