<template>
    <div class="container">
        <div class="container-title">
            <img src="../../image/logo.png" alt="" />
            <p class="single-line">{{ order.orgCode || '' }}</p>
        </div>
        <div class="container-time">
            <span v-if="order.stationStatus == '20'"> 正常营业 </span>
            <span v-if="order.stationStatus == '30' || order.stationStatus == '50'"> 暂停营业 </span>
            <span v-if="order.o2oBusinessStartTime && order.o2oBusinessEndTime">
                |&nbsp;&nbsp;{{ order.o2oBusinessStartTime }}-{{ order.o2oBusinessEndTime }}
            </span>

            <img src="../../image/icon_nav.png" alt="" @click="handleNaviStateion(order)" />
        </div>
    </div>
</template>

<script>
export default {
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
        };
    },
    created() {},
    mounted() {},
    methods: {
        // 点击打开导航
        handleNaviStateion(orderInfo) {
            // const orderInfo = {
            //     latitude: '40.17292048795497',
            //     longitude: '116.24386362184396',
            //     name: 'testName',
            //     address: 'testAddress',
            // };
            // #ifdef MP-MPAAS
            this.$cnpcBridge.openLocation({
                latitude: orderInfo.latitude,
                longitude: orderInfo.longitude,
                name: '',
                address: '',
            });
            // #endif
            // #ifndef MP-MPAAS
            uni.openLocation({
                latitude: Number(orderInfo.latitude),
                longitude: Number(orderInfo.longitude),
                name: '',
                address: '',
            });
            // #endif
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    &-title {
        display: flex;
        flex-direction: row;
        align-items: center;

        img {
            width: 24px;
            height: 24px;
        }
        p {
            font-weight: 500;
            margin-left: 6px;
        }
    }
    &-time {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-left: 30px;

        span {
            color: #666666;
            font-size: 13px;
            margin-right: 10px;
        }
        img {
            width: 15px;
            height: 15px;
        }
    }
}
</style>
