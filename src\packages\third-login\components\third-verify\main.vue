<template>
    <div>
        <div class="code-input-main">
            <div class="code-input-main-item" v-for="(item, index) in codeList" :class="{ on: seat == index }" :key="index">{{
                code[index] || ''
            }}</div>
            <input class="code-input-input" ref="input" type="number" v-model="code" :maxlength="codeLength" :focus="true" />
        </div>
    </div>
</template>

<script>
export default {
    name: 'VueVercode',
    props: {
        codeLength: {
            default: 6,
            type: Number,
        },
    },
    data() {
        return {
            codeList: [],
            code: '',
            seat: 0,
        };
    },
    mounted() {
        // 定义一个数组用于循环
        this.codeList = new Array(this.codeLength).fill('');
    },
    watch: {
        // 截取字符长度
        code() {
            this.seat = this.code.length;
            // if (this.code.length > this.codeLength) {
            //     this.code = this.code.substring(0, this.codeLength)
            // }
            if (this.code.length == this.codeLength) {
                this.$emit('returnresult', this.code);
            }
        },
    },
    methods: {},
};
</script>
<style scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
}

.code-input-main {
    width: 100%;
    height: 54px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
    /* padding: 20px 0 0; */
    margin-top: 20px;
    overflow: hidden;
}

.code-input-input {
    height: 100%;
    width: 120%;
    position: absolute;
    border: none;
    top: 0;
    left: -20%;
    outline: none;
    color: transparent;
    background-color: transparent;
    text-shadow: 0 0 0 transparent;
    caret-color: transparent;
    opacity: 0;
}

.code-input-main-item {
    width: 49px;
    height: 48px;
    border-radius: 4px;
    background: #ffffff;
    line-height: 48px;
    margin-right: 5px;
    padding: 0;
    /* border: solid #eee 1px; */
    text-align: center;
    font-size: 18px;
    color: #333;
}

.on {
    /* border: 1px solid #E64F22; */
}
</style>
