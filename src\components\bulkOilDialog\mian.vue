<template>
    <div class="padding-16">
        <div class="title weight-500 color-333 font-16 te-center">散装油注意事项</div>
        <div class="content1 mar-bom-12 weight-400 font-12 color-FF4000 te-center">
            预约信息申请通过后请在24小时内前往加油站加油，否则预约信息将自动失效。
        </div>
        <div class="content1 weight-400 font-13 color-333 te-left">
            1.散装汽油属于易燃、易爆物品，购买后请妥善保管，尽快使用。
            <br />
            2.储存少量汽油应该使用金属或制式油桶灌装，请勿使用塑料桶等容器存放。
            <br />
            3.存储油桶需在阴凉避光的地方，切勿放在厨房、楼梯口、床下、儿童活动区域等， 不要靠近任何火源。
        </div>
    </div>
</template>
<script>
export default {
    name: 'bulkOilDialog',
    components: {},
    props: {},
    data() {
        return {};
    },
    computed: {},
    // 初始化完成时触发（全局只触发一次）
    onLaunch() {},
    // 当 uni-app 启动，或从后台进入前台显示
    onShow() {},
    onLoad() {},
    // 当 uni-app 从前台进入后台
    onHide() {},
    // 当 uni-app 报错时触发
    onError() {},
    // 对 nvue 页面发送的数据进行监听
    onUniNViewMessage() {},
    // 对未处理的 Promise 拒绝事件监听函数
    onUnhandledRejection() {},
    // 页面不存在监听函数
    onPageNotFound() {},
    // 监听系统主题变化
    onThemeChange() {},
    // 最后一个页面按下Android back键
    onLastPageBackPress() {},
    // 监听应用退出
    onExit() {},
    methods: {},
    filter: {},
    watch: {},
};
</script>
<style lang="scss" scoped>
.title {
    line-height: 45rpx;
    margin-bottom: 28rpx;
}
.content1 {
    line-height: 36rpx;
}
</style>
