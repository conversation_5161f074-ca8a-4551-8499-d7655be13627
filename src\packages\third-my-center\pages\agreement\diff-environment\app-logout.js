import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
     // #ifdef MP-MPAAS
    mounted() {},
    methods: {
        /**
         * @description     : 退出登录
         * @return        {*}
         */
        async logOut() {
            let res = await logoutApi({}, { isCustomErr: true });
            this.$sKit.commonUtil.logoutFun();
        },
    },
    // #endif
};
