import Maa from '@alipay-inc/mpaas-miniapp-analytics';
import Vue from 'vue';
import { mpaasAppId, workspaceid, maa, clientCode } from '../../../../project.config';
import { singleton } from '../singleton';
// #ifdef MP-MPAAS
import CnpcBridge from '../v3-native-jsapi/cnpcBridge';
// #endif
import store from '../../../store/index'

class MPassBuryingPoint {
    platformObj: {};

    constructor() {
        this.platformObj = {
            'C10': '能源e站',
            'C14': '抖音',
            'C21': '银联云闪付',
            'C2948': '平安壹钱包',
            'C2930': '平安口袋银行',
            'C2951': '平安好车主',
            'C2952': '汽车之家',
            'C2950': '云梦泽',
            'C2956': '昆仑银行',
            'C29201': '高德',
            'C2958': '和包支付',
            'C2957': '中国移动',
        };
        this.init();
    }

    init(): void {
        // #ifdef H5-CLOUD
        let memberNo = ''
        memberNo = uni.getStorageSync('tokenInfo')?.memberNo || 'null';
        let deviceId = uni.getSystemInfoSync()?.deviceId || '';
        let walletAddress = store?.state.wallet.walletInfo.addressNo || '';
        let provinceV3 = store?.state.locationV3_app.provinceV3 || '';
        let cityV3 = store?.state.locationV3_app.cityV3 || '';
        console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
        window._to = {
            server: maa.reportURL,
            appId: mpaasAppId,
            workspaceId: workspaceid,
            h5version: '*******',
            bizScenario: '',
            userId: `${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`, // 用户ID,
            mtrDebug: true,
            extendParams: { platform: this.platformObj[clientCode] }
        };
        console.log(window, window.Tracker)
        // #endif
        // #ifdef MP-MPAAS
        Maa.init({
            appId: mpaasAppId,
            workspaceId: workspaceid,
            // id: '',
            // reportURL: '',
            ...maa,
            platform: this.platformObj[clientCode],
            // debug: false,
            getUserInfo: async () => {
                let memberNo = ''
                let deviceId = ''
                let walletAddress = ''
                let provinceV3 = ''
                let cityV3 = ''
                let [userTokenInfo, commonArgs] = await Promise.all([
                    CnpcBridge.getUserTokenInfo(),
                    CnpcBridge.getCommonArgs(),
                ]);
                memberNo = userTokenInfo?.memberNo || '';
                deviceId = commonArgs?.deviceId || ''
                walletAddress = store.state.wallet.walletInfo.addressNo || '';
                provinceV3 = store.state.locationV3_app.provinceV3 || '';
                cityV3 = store.state.locationV3_app.cityV3 || '';
                console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
                return {
                    user_id: `${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`, // 用户ID
                    operators_var: '', // 赋值运营商名称（移动、联通、电信）。
                    prov_var: '', // 赋值省份名称。
                    city_var: '', // 赋值地市名称。
                };
            },
        });
        // #endif
        // #ifdef MP-WEIXIN
        Maa.init({
            appId: mpaasAppId,
            workspaceId: workspaceid,
            // id: '',
            // reportURL: '',
            ...maa,
            // debug: false,
            getUserInfo: async () => {
                let memberNo = ''
                let deviceId = ''
                let walletAddress = ''
                let provinceV3 = ''
                let cityV3 = ''
                memberNo = await uni.getStorageSync('tokenInfo')?.memberNo || 'null';
                deviceId = uni.getSystemInfoSync()?.deviceId || '';
                walletAddress = store.state.wallet.walletInfo.addressNo || '';
                provinceV3 = store.state.locationV3_app.provinceV3 || '';
                cityV3 = store.state.locationV3_app.cityV3 || '';
                console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
                return {
                    user_id: `${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`, // 用户ID
                    operators_var: '', // 赋值运营商名称（移动、联通、电信）。
                    prov_var: '', // 赋值省份名称。
                    city_var: '', // 赋值地市名称。
                };
            },
        });
        // #endif
        // #ifdef MP-ALIPAY
        Maa.init({
            appId: mpaasAppId,
            workspaceId: workspaceid,
            // id: '',
            // reportURL: '',
            ...maa,
            // debug: false,
            getUserInfo: async () => {
                let memberNo = ''
                let deviceId = ''
                let walletAddress = ''
                let provinceV3 = ''
                let cityV3 = ''
                memberNo = await uni.getStorageSync('tokenInfo')?.memberNo || 'null';
                deviceId = uni.getSystemInfoSync()?.deviceId || '';
                walletAddress = store.state.wallet.walletInfo.addressNo || '';
                provinceV3 = store.state.locationV3_app.provinceV3 || '';
                cityV3 = store.state.locationV3_app.cityV3 || '';
                console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
                return {
                    user_id: `${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`, // 用户ID
                    operators_var: '', // 赋值运营商名称（移动、联通、电信）。
                    prov_var: '', // 赋值省份名称。
                    city_var: '', // 赋值地市名称。
                };
            },
        });
        // #endif
        // #ifndef H5-CLOUD
        Vue.use(Maa);
        // #endif
    }

    tracker(type: string, extParam = {}) {
        console.log('埋点：', type, extParam);
        // #ifdef H5-CLOUD
        console.log(window, window.Tracker)
        const Tracker = window?.Tracker
        Tracker?.click(type, {
            // bizType: 'webTracker',
            ext: { ...extParam, }
        });
        // #endif
        // #ifndef H5-CLOUD
        Maa.tracker({
            eventName: type, // 事件名
            eventData: {
                seed: '', // 事件ID
                // home02_click_sitID: '', // 自定义键值
                // pageName_var: '', // 事件发生时所在页面。
                // flowArea_var: '', // 事件发生时所在区域。
                // position_var: '', // 坑位、运营位位置。 属性ID
                // position_var: '', // 坑位、运营位位置。 属性ID
                // flowName_var: '', // 事件发生时当前流量位内容或按钮名称等。
                // flow_goodsId_var: '', // 事件发生时当前流量位的商品ID等。（物品模型）
                // flow_contentId_var: '', // 事件发生时当前流量位的内容ID等。（物品模型）
                // iop_tacticsId_var: '', // 页面访问时当前页面对应的IOP策略ID号。（物品模型）
                // flowId_var: '', // 页面访问时当前页面运营位位置ID。（物品模型）
                ...extParam,
            }, // 事件数据
        });
        // #endif
    }

    async setUserInfo(userInfo = {}) {
        let memberNo = ''
        let deviceId = ''
        let walletAddress = ''
        let provinceV3 = ''
        let cityV3 = ''
        // #ifdef H5-CLOUD
        memberNo = uni.getStorageSync('tokenInfo')?.memberNo || 'null';
        deviceId = uni.getSystemInfoSync()?.deviceId || '';
        walletAddress = store?.state.wallet.walletInfo.addressNo || '';
        provinceV3 = store?.state.locationV3_app.provinceV3 || '';
        cityV3 = store?.state.locationV3_app.cityV3 || '';
        console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
        window.Tracker.setUserId(`${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`)
        return
        // #endif
        // #ifdef MP-MPAAS
        let [userTokenInfo, commonArgs] = await Promise.all([
            CnpcBridge.getUserTokenInfo(),
            CnpcBridge.getCommonArgs(),
        ]);
        memberNo = userTokenInfo?.memberNo || '';
        deviceId = commonArgs?.deviceId || ''
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        memberNo = await uni.getStorageSync('tokenInfo')?.memberNo || 'null';
        deviceId = uni.getSystemInfoSync()?.deviceId || '';
        // #endif
        walletAddress = store.state.wallet.walletInfo.addressNo || '';
        provinceV3 = store.state.locationV3_app.provinceV3 || '';
        cityV3 = store.state.locationV3_app.cityV3 || '';
        console.log(memberNo, deviceId, walletAddress, provinceV3, cityV3, '埋点setUserInfo');
        Maa.setUserInfo({
            user_id: `${memberNo}_${deviceId}_${walletAddress}_${provinceV3}_${cityV3}`, // 用户ID
            operators_var: '', // 赋值运营商名称（移动、联通、电信）。
            prov_var: '', // 赋值省份名称。
            city_var: '', // 赋值地市名称。
            ...userInfo,
        });
    }
}

export default singleton(MPassBuryingPoint);
