<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas bg-F7F7FB">
        <div class="bg-wrap">
            <img src="../../images/bg.png" alt />
        </div>
        <zj-navbar
            :background="{ background: 'rgba(255, 255, 255, 0)' }"
            :border-bottom="false"
            back-icon-color="#333"
            title="积分明细"
            titleColor="#333"
        >
        </zj-navbar>
        <div class="fl-column content-wrap p-LR-16">
            <div class="fl-row fl-jus-bet content">
                <div class="textPoints">
                    <div class="points">{{ loyaltyPtsAvailableAmount || 0 }}</div>
                    <div class="mar-left-4 gradient-text">积分</div>
                </div>
                <div class="pointsCoin">
                    <img src="../../images/pointsCoin.png" alt="" />
                </div>
            </div>

            <zjMarket marketType="redeem" spaceType="redeem_page"></zjMarket>
            <!-- #ifdef MP-MPAAS -->
            <!--                        <div class="bg-fff middle-content-wrap border-rad-4">-->
            <!--                            <div class="fl-row fl-al-cen middle-content fl-jus-bet">-->
            <!--                                <div class="fl-row fl-al-cen fl-jus-bet">-->
            <!--                                    <img class="img" src="../../images/virtualGift.png" alt="" />-->
            <!--                                    <div class="fl-column text">-->
            <!--                                        <div class="weight-600 font-18 color-333 text1">积分兑换好礼</div>-->
            <!--                                        <div class="weight-400 font-14 color-999">多种礼品免费兑换</div>-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                                <div class="redeemNow te-center color-fff font-13 weight-400" @click="redeemNowFun">立即兑换</div>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!-- #endif -->
        </div>
        <div :style="isMpaas" class="p-LR-16 f-1 mh-0">
            <div class="box">
                <div class="data-title">
                    <div class="tabs">
                        <div class="left_tab" :class="{ active: tabNum == 1 }" @click="tabNum = 1">收入明细</div>
                        <div class="right_tab" :class="{ active: tabNum == 2 }" @click="tabNum = 2">消费明细</div>
                    </div>

                    <div class="data-list-title" v-if="tabNum == 1">
                        <div class="data-list-title-left">
                            <div class="dateTime" @click="dateShow1 = true">{{ incomeDate }}</div>
                            <div class="arrow-down-333" @click="dateShow1 = true" style="margin-left: 5px; margin-top: -2px"></div>
                        </div>
                        <div class="data-list-title-right">
                            <span>本月获取：</span>
                            <span>
                                {{ incomeList ? (incomeList.monthTotalAmount ? incomeList.monthTotalAmount : 0) : 0 }}
                            </span>
                        </div>
                    </div>
                    <div class="data-list-title" v-else>
                        <div class="data-list-title-left">
                            <div class="dateTime" @click="dateShow2 = true">{{ consumedate }}</div>
                            <div class="arrow-down-333" @click="dateShow2 = true" style="margin-left: 5px; margin-top: -2px"></div>
                        </div>
                        <div class="data-list-title-right">
                            <span>本月消费：</span>
                            <span>
                                {{ consumeList ? (consumeList.monthTotalAmount ? consumeList.monthTotalAmount : 0) : 0 }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="data" ref="data">
                    <!-- 收入明细 -->
                    <div class="data-list" v-if="tabNum == 1">
                        <div class="data-list-list" v-if="incomeList && incomeList.rows.length > 0">
                            <div class="data-list-item" v-for="(i, idx) in incomeList.rows" :key="idx">
                                <div class="data-list-item-text">
                                    <div class="title">{{ i.activityName }}</div>
                                    <div class="date">{{ i.transactionTime }}</div>
                                </div>
                                <div class="data-list-item-num font-style">+{{ i.transactionAmount }}</div>
                            </div>
                        </div>
                        <div class="data-list-list" v-if="LoadingIncome && incomeList && incomeList.rows.length == 0">
                            <zj-no-data :emptyImage="require('../../images/kt8jf.png')" emptyText="暂未查询到积分收入明细"></zj-no-data>
                        </div>
                    </div>
                    <!-- 消费明细 -->
                    <div class="data-list" v-else>
                        <div class="data-list-list" v-if="consumeList && consumeList.rows.length > 0">
                            <div class="data-list-item" v-for="(i, idx) in consumeList.rows" :key="idx">
                                <div class="data-list-item-text">
                                    <div class="title">{{ i.productName }}</div>
                                    <div class="date">{{ i.transactionTime }}</div>
                                </div>
                                <div class="data-list-item-num font-style">{{ i.transactionAmount }}</div>
                            </div>
                        </div>
                        <div class="data-list-list" v-if="LoadingConsume && consumeList && consumeList.rows.length == 0">
                            <zj-no-data :emptyImage="require('../../images/kt8jf.png')" emptyText="暂未查询到积分消费明细"></zj-no-data>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <SelectDate v-if="dateShow1" @hideShow="hideShow" @sureSelectDateTime="incomebindDateChange"></SelectDate>
        <SelectDate v-if="dateShow2" @hideShow="hideShow" @sureSelectDateTime="consumebindDateChange"></SelectDate>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { userPointIncomeList, userPointConsumeList } from '../../../../s-kit/js/v3-http/https3/integral/index';
import { mapGetters } from 'vuex';
import SelectDate from '../../../../s-kit/components/layout/zj-selectDate/zj-selectDate.vue';
import { clientCode, baseType } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import zjMarket from '../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
export default {
    mixins: [publicMixinsApi],
    components: { SelectDate, zjMarket },
    name: 'index',
    data() {
        const incomecurrentDate = this.getDate({
            format: true,
        });
        const consumecurrentDate = this.getDate({
            format: true,
        });
        return {
            active: 0,
            //loyaltyPtsAvailableAmount积分余额
            loyaltyPtsAvailableAmount: 0,

            //incomeList收入
            incomeList: null,
            //consumeList 消费
            consumeList: null,
            //incomeData收入明细传参
            incomeData: {
                pageSize: 999,
                pageNum: 1,
                startDateTime: '',
                endDateTime: '',
            },
            //consumeData消费明细传参
            consumeData: {
                pageSize: 999,
                pageNum: 1,
                startDateTime: '',
                endDateTime: '',
            },
            sortHeight: '',
            incomeDate: incomecurrentDate,
            consumedate: consumecurrentDate,
            tabNum: 1,
            dateShow1: false,
            dateShow2: false,
            LoadingIncome: false,
            LoadingConsume: false,
            marTop: '',
        };
    },
    computed: {
        //初始化开始时间
        startDate() {
            return this.getDate('start');
        },
        // 初始化结束时间
        endDate() {
            return this.getDate('end');
        },
        // 初始化结束时间
        isMpaas() {
            let isMpaasStyle = this.marTop ? 'margin-top: -40px; z-index: 10' : '';
            console.log(isMpaasStyle, 'isMpaasStyle====111');
            return isMpaasStyle;
        },
        ...mapGetters(['memberAccountInfo']),
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'jifenPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
    },
    async mounted() {
        // 获取能源币，油卡，电子券，积分和余额，七日内余额
        await this.$store.dispatch('basicCouponAction');
        this.loyaltyPtsAvailableAmount = this.memberAccountInfo.loyaltyPtsAvailableAmount;
        // console.log(this.$store.state.member.memberAccountInfo.loyaltyPtsAvailableAmount);
        var date = new Date(); //获取当前时间
        var year = date.getFullYear(); //获取当前年份
        var month = date.getMonth() + 1; //获取当前月份
        var day = date.getDate(); //获取当前日期
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        //设置收入明细接口传参开始时间与结束时间
        this.incomeData.endDateTime = year + '-' + month + '-' + day;
        this.incomeData.startDateTime = year + '-' + month + '-' + '01';
        //设置消费明细接口传参开始时间与结束时间
        this.consumeData.endDateTime = year + '-' + month + '-' + day;
        this.consumeData.startDateTime = year + '-' + month + '-' + '01';
        //对接收入明细接口
        this.getuserPointIncomeList();
    },
    watch: {
        tabNum(val) {
            // 切换收入明细与消费明细tab页，消费明细无数据时请求消费明细接口
            if (this.consumeList == null) {
                // 对接消费明细接口
                this.getuserPointConsumeList();
            }
        },
    },
    methods: {
        //收入明细选择时间并请求接口
        incomebindDateChange(time) {
            console.log(time);
            this.dateShow1 = false;
            time.month = time.month > 9 ? time.month : '0' + time.month;
            this.incomeDate = time.year + '年' + time.month + '月';
            var lastDay = new Date(time.year, time.month, 0).getDate();
            this.incomeData.endDateTime = time.year + '-' + time.month + '-' + lastDay;
            this.incomeData.startDateTime = time.year + '-' + time.month + '-' + '01';
            this.getuserPointIncomeList();
        },
        //消费明细选择时间并请求接口
        consumebindDateChange(time) {
            console.log(time);
            this.dateShow2 = false;
            time.month = time.month > 9 ? time.month : '0' + time.month;
            this.consumedate = time.year + '年' + time.month + '月';
            var lastDay = new Date(time.year, time.month, 0).getDate();
            this.consumeData.endDateTime = time.year + '-' + time.month + '-' + lastDay;
            this.consumeData.startDateTime = time.year + '-' + time.month + '-' + '01';
            this.getuserPointConsumeList();
        },

        //获取时间
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            if (type === 'start') {
                month = month - 2;
                if (month <= 0) {
                    month = 12 + month;
                    year = year - 1;
                }
            }

            day = day > 9 ? day : '0' + day;
            month = month > 9 ? month : '0' + month;
            if (type.format) {
                return `${year}年${month}月`;
            } else {
                return `${year}-${month}-${day}`;
            }
        },
        //获取积分收入接口
        async getuserPointIncomeList() {
            console.log(this.incomeData);
            let res = await userPointIncomeList(this.incomeData);
            //
            if (res && res.success) {
                this.incomeList = res.data;
            }
            this.LoadingIncome = true;
        },
        //获取积分消费接口
        async getuserPointConsumeList() {
            console.log(this.consumeData);
            let res = await userPointConsumeList(this.consumeData);
            if (res && res.success) {
                this.consumeList = res.data;
            }
            this.LoadingConsume = true;
        },
        //关闭选择时间弹窗
        hideShow() {
            this.dateShow1 = false;
            this.dateShow2 = false;
        },
        // 立即兑换
        redeemNowFun() {
            // 生产环境链接
            let prdUrl =
                'https://emall.95504.net/b2c_pub/enloading?flag=6&subFlag=usmile/features%40activityId%3D3917%24activityType%3D5%24FeatureName%3D%E7%A7%AF%E5%88%86%E5%85%91%E6%8D%A2%24shopId%3Dkong%24catalogId%3Dkong%24goodsIds%3Dkong%24couponsCode%3Dkong%24catalogIds%3Dkong%24isPoints%3Dtrue&backFlag=app';
            // 测试环境链接
            let sitUrl =
                'https://temall.95504.net/b2c_pub/enloading?flag=6&subFlag=usmile/features%40activityId%3D3173%24activityType%3D5%24FeatureName%3D%E7%A7%AF%E5%88%86%E6%B4%BB%E5%8A%A82%24shopId%3Dkong%24catalogId%3Dkong%24goodsIds%3Dkong%24couponsCode%3Dkong%24catalogIds%3Dkong%24isPoints%3Dtrue%24backFlag%3Dapp';
            if (baseType === 'gray' || baseType === 'prd') {
                this.$cnpcBridge.openOldWeb({ pageUrl: prdUrl });
            } else {
                this.$cnpcBridge.openOldWeb({ pageUrl: sitUrl });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.bg-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 250px;
    img {
        width: 100%;
    }
}
.content-wrap {
    z-index: 9;
    .content {
        padding: 0 29px;
        .textPoints {
            vertical-align: bottom;
            display: inline;
            margin-top: 32rpx;

            .points {
                font-size: 72rpx;
                line-height: 102rpx;
                vertical-align: bottom;
                display: inline;
                font-weight: bold;
                /* 关键样式 */
                background: linear-gradient(90deg, #ffab02 0%, #ff4743 100%);
                -webkit-background-clip: text; /* 背景裁剪为文字 */
                background-clip: text;
                color: transparent; /* 文字透明 */

                /* 兼容支付宝小程序 */
                text-fill-color: transparent;
            }

            .gradient-text {
                font-size: 28rpx;
                line-height: 74rpx;
                font-weight: 500;
                vertical-align: bottom;
                display: inline;
                /* 关键样式 */
                background: linear-gradient(90deg, #ffab02 0%, #ff4743 100%);
                -webkit-background-clip: text; /* 背景裁剪为文字 */
                background-clip: text;
                color: transparent; /* 文字透明 */

                /* 兼容支付宝小程序 */
                text-fill-color: transparent;
            }
        }
    }
}

.pointsCoin {
    width: 113px;
    height: 125px;
    img {
        width: 100%;
        height: 100%;
    }
}
.middle-content-wrap {
    margin-top: -30px;
    z-index: 10;
}
.middle-content {
    padding: 10px 20px 10px 12px;
    .img {
        width: 60px;
        height: 60px;
    }
    .text {
        margin-left: 13px;
        .text1 {
            margin-bottom: 5px;
        }
    }
    .redeemNow {
        width: 78px;
        height: 27px;
        background: linear-gradient(90deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1 10px 0px rgba(0, 0, 0, 0.07);
        border-radius: 4px;
        line-height: 27px;
    }
}
.box {
    height: 100%;
    flex: 1;
    // height: 65%;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    overflow: hidden;

    .data-title {
        background: #fff;
        // width: 345px;
        width: 100%;
        border-radius: 8px 8px 0 0;
        margin: 0 auto;

        .tabs {
            display: flex;
            text-align: center;
            line-height: 48px;
            border-bottom: 1px solid #efeff4;
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            justify-content: space-around;
            .active {
                font-size: 16px;
                font-weight: bold;
                color: #e64f22;
                position: relative;
                border-bottom: 2px solid #e64f22;
            }
        }

        .data-list-title {
            padding: 0 15px;
            display: flex;
            background: #fff;
            justify-content: space-between;
            padding-top: 22px;
            padding-bottom: 18px;
            .data-list-title-left {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                display: flex;
                align-items: center;
                line-height: 14px;

                img {
                    margin-left: 5px;
                    width: 16px;
                    height: 16px;
                }
            }

            .data-list-title-right {
                font-size: 14px;
                font-weight: bold;

                span:first-child {
                    color: #999999;
                }

                span:last-child {
                    color: #333;
                    font-family: 'HarmonyOS_Sans_Condensed_Medium';
                }
            }
        }
    }

    .data {
        width: 345px;
        flex: 1;
        margin: 0 auto;
        border-radius: 0 0 8px 8px;
        background: #fff;
        // margin-top: 10px;
        padding-bottom: 15px;
        // margin-bottom: 26px;
        overflow: auto;

        .data-list {
            height: 100%;
            padding-bottom: 15px;

            // overflow: auto;
            .data-list-list {
                padding: 0 15px;
                width: 100%;
                padding-bottom: 15px;
                height: 100%;

                overflow: auto;

                .data-list-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 25px;

                    .pic {
                        width: 42px;
                        height: 42px;
                        border-radius: 6px;
                        margin-right: 13px;
                        overflow: hidden;
                        background: #999999;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .data-list-item-text {
                        .title {
                            font-size: 14px;
                            font-weight: 400;
                            color: #333333;
                        }

                        .date {
                            font-size: 12px;
                            font-weight: 400;
                            color: #999999;
                            margin-top: 6px;
                        }
                    }

                    .data-list-item-num {
                        font-size: 16px;
                        // font-weight: bold;
                        color: #333333;
                        flex: 1;
                        text-align: right;
                    }
                }
            }
        }
    }
}
</style>
