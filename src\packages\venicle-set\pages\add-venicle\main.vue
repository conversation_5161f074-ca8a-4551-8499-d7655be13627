<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :back-text="isEdit ? '编辑车牌' : '添加车牌'"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- 上方号码输入 -->
        <div class="plate-input-view">
            <div class="plate-input-title">
                <div class="plate-title-text">车牌号码</div>
                <div class="plate-bt-text">必填</div>
            </div>
            <div class="plate-input">
                <block v-for="(item, index) in icensePlateArr" :key="index">
                    <div
                        :class="index == selectPlateIndex ? 'plate-input-item plate-input-item-select' : 'plate-input-item'"
                        :style="{
                            backgroundColor: index == 7 ? '#f5f5f5' : null,
                            border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                            color: index == 7 ? '#969696' : null,
                            fontSize: index == 7 && item == '' ? '14px' : null,
                            fontWeight: index == 7 ? '500' : null,
                        }"
                        @click="clickPlateInput(index)"
                        >{{ index == 7 && item == '' ? '新能源' : item }}</div
                    >
                    <div class="plate-input-spot" v-if="index == 1"></div>
                </block>
            </div>
        </div>
        <!-- 信息填写 -->
        <div class="venicle-info-view">
            <div class="venicle-info-item">
                <div class="title">常用油品</div>
                <div class="bt-text">必填</div>
                <div class="yp-select-view" @click="clickSelectYP">
                    <div :class="selectYP == '请选择' ? 'yp-select-text' : 'yp-select-text yp-select-text-select'">{{ selectYP }}</div>
                    <img src="@/static/homeIcon/rightjt.png" class="yp-select-img" />
                </div>
            </div>
            <div class="venicle-info-item">
                <div class="title">单次最大加油金额</div>
                <div class="bt-text">必填</div>
                <input class="price-input" v-model="priceInput" placeholder-style="color: #909090" placeholder="请输入单次加油最大金额" />
            </div>
            <div class="venicle-info-item venicle-info-bottom">
                <div class="title">常用支付方式</div>
                <!-- <div class='bt-text'>必填</div> -->
                <div class="yp-select-view" @click="clickSelectPayType">
                    <div :class="selectPayType == '点击切换支付方式' ? 'yp-select-text' : 'yp-select-text yp-select-text-select'">{{
                        selectPayType
                    }}</div>
                    <img src="@/static/homeIcon/rightjt.png" class="yp-select-img" />
                </div>
            </div>
        </div>
        <!-- 设置默认 -->
        <div class="default-set-view">
            <div class="default-set-title">设为默认</div>
            <u-switch v-model="checked" active-color="#FF8200"></u-switch>
        </div>
        <!-- 保存按钮 -->
        <div class="btn-view">
            <div class="save-btn" @click="clickSaveBtn">保存</div>
            <div class="delete-btn" @click="clickDeleteItem" v-if="isEdit">删除</div>
        </div>
        <!-- 油品选择弹窗 -->
        <u-popup v-model="isShowSelectYP" mode="bottom" border-radius="20" safe-area-inset-bottom>
            <div class="select-yp-pop">
                <div class="pop-title-view">
                    <div class="pop-title-text">选择油品</div>
                    <u-icon size="22" name="close" @click="clickCloseYPPop" color="#979797"></u-icon>
                    <div class="pop-title-zw"></div>
                </div>
                <div class="pop-yp-list">
                    <div
                        :class="selectYPIndex == index ? 'pop-yp-item pop-yp-item-select' : 'pop-yp-item'"
                        @click="clickPopYPItem(index)"
                        v-for="(item, index) in ypArr"
                        :key="index"
                        >{{ item }}</div
                    >
                </div>
            </div>
        </u-popup>
        <!-- 支付方式选择 -->
        <u-popup v-model="isShowselectPayType" mode="bottom" border-radius="20" safe-area-inset-bottom>
            <div class="select-yp-pop">
                <div class="pop-title-view">
                    <div class="pop-title-text">选择支付方式</div>
                    <u-icon size="22" name="close" @click="clickClosePayTypePop" color="#979797"></u-icon>
                    <div class="pop-title-zw"></div>
                </div>
                <div class="pop-pay-list">
                    <div class="pop-pay-item" v-for="(item, index) in payTypeArr" :key="index" @click="clickPayType(index)">
                        <img :src="item.icon" class="pay-item-icon" />
                        <div class="pay-item-title">{{ item.title }}</div>
                        <img src="@/static/homeIcon/rightjt.png" class="item-label-arce" mode />
                    </div>
                </div>
            </div>
        </u-popup>
        <!-- 键盘 -->
        <custom-keyboard
            ref="uKeyboard"
            zIndex="10"
            @backspace="carKeyDelect"
            @change="carKeyChange"
            mode="car"
            v-model="isKeyboard"
            :mask="false"
        ></custom-keyboard>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import util from '@/utils/index.js';
import pageConfig from '@/utils/pageConfig.js';
import { editCarInfo, deteleDefdaultCar } from '@/api/home.js';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            icensePlateArr: ['', '', '', '', '', '', '', ''], // 车牌号数组
            selectPlateIndex: -1, // 正在输入的车牌号位置 -1时关闭键盘
            isKeyboard: false, // 是否显示键盘
            isFirst: false, //是否是第一次调起键盘 用于判断退格
            selectYP: '请选择', // 油品选择值
            selectPayType: '微信支付', // 切换支付方式
            priceInput: '', // 单次最大加油金额
            checked: false, // 是否默认选中
            isShowSelectYP: false, // 是否展示油品弹窗
            isShowselectPayType: false, // 是否显示选择支付类型弹窗
            ypArr: ['92#', '95#', '98#', '柴油'], // 油品类型
            payTypeArr: [
                {
                    icon: '/static/homeIcon/pay-wx.png',
                    label: ['随机立减，最高20元'],
                    title: '微信支付',
                },
                {
                    icon: '/static/pay-oilcard.png',
                    label: ['主卡 0001', '满300减50'],
                    title: '油卡支付',
                },
            ],
            selectYPIndex: -1, // 选中的常用油品index
            carId: -1, // 车辆id
            isEdit: false, // 是否是编辑
        };
    },
    watch: {
        // 监听键盘
        isKeyboard: {
            handler(newName, oldName) {
                if (newName === false) {
                    this.selectPlateIndex = -1;
                }
            },
            immediate: true,
        },
        // 监听开关按钮
        checked: {
            handler(newName, oldName) {},
            immediate: true,
        },
    },
    onLoad() {
        if (this.$mp.query.obj) {
            let option = JSON.parse(decodeURIComponent(this.$mp.query.obj));
            this.carId = option.id;
            this.selectYP = option.oilNo;
            let carNoArr = option.carNo.split('');
            this.icensePlateArr = carNoArr.length == 7 ? [...carNoArr, ''] : carNoArr;
            this.priceInput = option.refuelAmount;
            this.checked = option.isDefault;
            this.isEdit = true;
            this.selectPayType = option.payMode == 1 ? '微信支付' : '油卡支付';
        }
    },
    methods: {
        // 车牌input点击事件
        clickPlateInput(index) {
            console.log('index', index);
            this.selectPlateIndex = index;
            this.$refs.uKeyboard.changeCarInputMode(this.selectPlateIndex == 0 ? 'cn' : 'zn');
            this.isKeyboard = true;
            this.isFirst = true;
        },
        // 键盘点击事件监听
        carKeyChange(text) {
            console.log(text);

            this.isFirst = false;
            this.$set(this.icensePlateArr, this.selectPlateIndex, text);
            this.selectPlateIndex = this.selectPlateIndex + 1;
            if (this.selectPlateIndex == 1) {
                this.$refs.uKeyboard.changeCarInputMode();
            }
            if (this.selectPlateIndex == this.icensePlateArr.length) {
                this.selectPlateIndex = -1;
                this.isKeyboard = false;
            }
        },
        // 键盘退格点击事件
        carKeyDelect() {
            if (this.selectPlateIndex == 1) {
                this.$refs.uKeyboard.changeCarInputMode();
            }
            if (this.isFirst) {
                this.$set(this.icensePlateArr, this.selectPlateIndex, '');
                this.selectPlateIndex = this.selectPlateIndex - 1;
            } else {
                this.selectPlateIndex = this.selectPlateIndex - 1;
                this.$set(this.icensePlateArr, this.selectPlateIndex, '');
            }
            if (this.selectPlateIndex == -1) {
                this.selectPlateIndex = 0;
            }
        },
        // 油品选择点击事件
        clickSelectYP() {
            this.isShowSelectYP = true;
        },
        // 关闭油品弹窗事件
        clickCloseYPPop() {
            this.isShowSelectYP = false;
        },
        // 油品弹窗点击事件
        clickPopYPItem(index) {
            this.selectYPIndex = index;
            this.selectYP = this.ypArr[index];
            this.isShowSelectYP = false;
        },
        // 支付类型点击事件
        clickPayType(index) {
            this.selectPayType = this.payTypeArr[index].title;
            this.isShowselectPayType = false;
        },
        // 支付类型选择点击事件（点击输入框）
        clickSelectPayType() {
            this.isShowselectPayType = true;
        },
        // 关闭支付类型选择点击事件
        clickClosePayTypePop() {
            this.isShowselectPayType = false;
        },
        // 删除车牌点击事件
        async clickDeleteItem() {
            await util.showModal('您确定删除此常用车辆么', false);
            let res = await deteleDefdaultCar({ id: this.carId });
            this.$store.dispatch('uploadVenicleList');
            uni.navigateBack();
        },
        // 保存按钮点击事件
        async clickSaveBtn() {
            let plateStr = this.icensePlateArr.join('');
            if (plateStr.length < 7) {
                util.nomalToast('请输入完成的车牌号');
                return;
            }
            console.log(plateStr);
            if (!util.isLicenseNo(plateStr)) return util.nomalToast('请输入正确的车牌号');
            if (this.selectYP == '请选择') return util.nomalToast('请选择常用油品');
            if (Number(this.priceInput) == '') return util.nomalToast('请填写单次最大加油金额');
            if (this.selectPayType == '点击切换支付方式') return util.nomalToast('请选择支付方式');
            let params = {
                carNo: plateStr,
                oilNo: this.selectYP,
                refuelAmount: Number(this.priceInput),
                isDefault: this.checked ? 1 : 0,
                payMode: this.selectPayType == '微信支付' ? 1 : 2,
                // ,payMode: 1
            };
            if (this.carId != -1) {
                params.id = this.carId;
            }

            let plateRes = await editCarInfo(params);
            if (plateRes.status == -1) {
                await this.$util.showModal(plateRes.info, true);
            } else {
                await this.$util.showModal(this.isEdit ? '修改成功' : '添加成功', true);
                this.$store.dispatch('uploadVenicleList');
                uni.navigateBack();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
}

.plate-input-view {
    overflow: hidden;
    margin-top: 10px;
    background-color: #ffffff;
    margin-left: 15px;
    width: 345px;
    border-radius: 5px;

    .plate-input-title {
        display: flex;
        margin-left: 10px;
        margin-top: 10px;
        align-items: flex-end;

        .plate-title-text {
            font-size: 15px;
            color: #333333;
            font-weight: 700;
        }

        .plate-bt-text {
            font-size: 12px;
            color: $btn-color;
            margin-left: 5px;
        }
    }

    .plate-input {
        margin-top: 15px;
        margin-left: 5px;
        margin-right: 10px;
        margin-bottom: 14px;
        display: flex;

        .plate-input-item {
            z-index: 20;
            flex: 1;
            margin-left: 5px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-color: #F6F6F6;
            border: 0.5px solid #bcbcbc;
            color: #333333;
            border-radius: 5px;
            text-align: center;
            font-size: 15px;
            font-weight: 700;
        }

        .plate-input-item-select {
            background-color: $btn-mantle-color;
            border: 0.5px solid $btn-color;
            color: $btn-color;
        }

        .plate-input-spot {
            align-self: center;
            background-color: #dcdcdc;
            height: 10px;
            width: 10px;
            border-radius: 5px;
            margin-left: 5px;
        }
    }
}

.venicle-info-view {
    margin-left: 15px;
    margin-top: 10px;
    background-color: #ffffff;
    border-radius: 5px;
    width: 345px;

    .venicle-info-item {
        height: 49.5px;
        margin-left: 10px;
        margin-right: 10px;
        border-bottom: 0.5px solid #bcbcbc;
        display: flex;
        align-items: center;

        .title {
            color: #333333;
            font-size: 15px;
            font-weight: 700;
        }

        .bt-text {
            color: $btn-color;
            font-size: 12px;
            margin-left: 5px;
        }

        .price-title {
            color: #333333;
            font-size: 15px;
            font-weight: 700;
        }

        .price-input {
            padding-top: 10px;
            padding-bottom: 10px;
            margin-left: 10px;
            text-align: right;
            flex: 1;
            font-size: 12px;
            color: #333333;
        }

        .yp-select-view {
            flex: 1;
            display: flex;
            align-items: center;
            padding-top: 10px;
            padding-bottom: 10px;
            .yp-select-text {
                flex: 1;
                text-align: right;
                font-size: 12px;
                color: #909090;
            }

            .yp-select-text-select {
                color: #333333;
            }

            .yp-select-img {
                margin-left: 10px;
                width: 4px;
                height: 8px;
                display: block;
            }
        }
    }
    .venicle-info-bottom {
        height: 50px;
        border-bottom: none;
    }
}

.default-set-view {
    margin-left: 15px;
    margin-top: 10px;
    background-color: #ffffff;
    border-radius: 5px;
    width: 345px;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;

    .default-set-title {
        color: #333333;
        font-size: 15px;
        font-weight: 700;
    }
}

.btn-view {
    position: absolute;
    bottom: 15px;
    margin-left: 15px;
    display: flex;
    width: 345px;
    margin-bottom: env(safe-area-inset-bottom);
    .save-btn {
        text-align: center;
        background-color: $btn-color;
        line-height: 44px;
        color: #ffffff;
        font-size: 15px;
        flex: 1;
        border-radius: 5px;
    }
    .delete-btn {
        margin-left: 10px;
        text-align: center;
        background-color: $btn-mantle-color;
        line-height: 42px;
        border: 1px solid $btn-color;
        color: $btn-color;
        font-size: 15px;
        width: 100px;
        border-radius: 5px;
    }
}

.select-yp-pop {
    min-height: 160px;
    .pop-title-view {
        display: flex;
        height: 50px;
        align-items: center;
        border-bottom: 0.5px solid #dcdddd;

        .pop-title-text {
            margin-left: 15px;
            color: #222222;
            font-size: 15px;
            font-weight: 700;
            flex: 1;
        }

        .pop-title-zw {
            width: 20px;
            height: 2px;
        }
    }
    .pop-pay-list {
        .pop-pay-item {
            height: 49.5px;
            width: 345px;
            margin-left: 15px;
            border-bottom: 0.5px solid #bcbcbc;
            display: flex;
            align-items: center;
            .pay-item-icon {
                height: 14px;
                width: 16px;
            }
            .pay-item-title {
                color: #333333;
                font-size: 12px;
                margin-left: 10px;
                font-weight: 700;
                flex: 1;
            }
            .item-label-list {
                display: flex;
                .item-label-text {
                    margin-left: 5px;
                    line-height: 23px;
                    font-size: 12px;
                    color: $btn-color;
                    background-color: $btn-mantle-color;
                    border-radius: 3px;
                    border: 1px solid $btn-color;
                    padding-left: 5px;
                    padding-right: 5px;
                }
            }
            .item-label-arce {
                margin-left: 10px;
                height: 9px;
                width: 5px;
            }
        }
    }
    .pop-yp-list {
        margin-left: 10px;
        margin-right: 15px;
        margin-top: 15px;
        margin-bottom: 30px;
        display: flex;
        flex-wrap: wrap;

        .pop-yp-item {
            margin-top: 5px;
            width: 81.5px;
            margin-left: 5px;
            background-color: #f6f6f6;
            border: 0.5px solid F6F6F6;
            text-align: center;
            border-radius: 5px;
            height: 43px;
            color: #333333;
            font-size: 15px;
            line-height: 43px;
        }

        .pop-yp-item-select {
            // background-color: #EDFFEF;
            // border: 0.5px solid #118820;
            // color: #118820;
            background-color: $btn-mantle-color;
            border: 1px solid $btn-color;
            color: $btn-color;
            font-weight: 700;
        }
    }
}
</style>
