import CONFIG from './third-config';
import QRCODE from './uniapp-qrcode/index';
import platform from './platform';
import store from '../../store/index';
import cnpcBridge from './v3-native-jsapi/cnpcBridge';
import CryptoJS from 'crypto-js';
const projectConfig = require('../../../project.config');
// console.log('CONFIG-----', CONFIG)
/**
 * 页面跳转
 * @param {'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab' | 'navigateBack' | number } url  转跳路径
 * @param {String} params 跳转时携带的参数
 * @param {String} type 转跳方式
 **/

//用法：useRouter('/pages/login/index', 'reLaunch')
function useRouter(url = '', params = {}, type = 'navigateTo', prveDataObject = {}, appId = '') {
    // if(url === '') { return false}
    try {
        if (type === 'navigateBack') {
            // 1. 获取当前页面栈实例（此时最后一个元素为当前页）
            let pages = getCurrentPages();
            // 2. 上一页面实例
            // 注意是length长度，所以要想得到上一页面的实例需要 -2
            // 若要返回上上页面的实例就 -3，以此类推
            if (prveDataObject.delta) {
                let prevPage = pages[pages.length - prveDataObject.delta];
                console.log(prevPage, 'prevPage');
                // 3. 给上一页面实例绑定getValue()方法或者prveDataObject参数（注意是$vm）
                prevPage.$vm.prveDataObject = prveDataObject;
                uni[type]({ delta: prveDataObject.delta - 1 == 0 ? 1 : prveDataObject.delta - 1 });
            } else {
                uni[type]({ delta: 1 });
            }
        } else {
            if (
                JSON.stringify(CONFIG.name).includes('cnpc-my-webmini') ||
                JSON.stringify(CONFIG.name).includes('cnpc-fuel-webmini') ||
                JSON.stringify(CONFIG.name).includes('cnpc-order-webmini')
            ) {
                if (Object.keys(params).length) url = `${url}?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
                cubeMini(url, appId);
            } else {
                console.log('store---isHarmony', store.state.thirdIndex.isHarmony);
                let isHarmony = store.state.thirdIndex.isHarmony;
                if (platform.isAlipay || isHarmony || platform.isH5CLOUD) {
                    const subPath = projectConfig?.subPath || '';
                    if (Object.keys(params).length) {
                        // TODO 临时兼容解决支付宝小程序referer过长到账请求被拒的问题，最优解决方案为小程序、支付插件、账户插件对齐配置项
                        // "behavior": {
                        //     "requestReferrerStrategy": "page"
                        // }
                        if (params.zfbRefererMax) {
                            url = `${subPath}${url}`;
                            getApp().globalData.query = getApp().globalData.query || {};
                            getApp().globalData.query[url] = params;
                        } else {
                            url = `${subPath}${url}?data=${encodeURIComponent(JSON.stringify(params)).replace(/%/g, '%25')}`;
                            console.log('url----', url);
                        }
                    } else {
                        url = `${subPath}${url}`;
                    }
                    uni[type]({ url });
                } else {
                    // #ifdef MP-MPAAS
                    if (Object.keys(params).length) url = `${url}?data=${encodeURIComponent(JSON.stringify(params)).replace(/%/g, '%25')}`;
                    // #endif
                    // #ifdef MP-WEIXIN || MP-TOUTIAO || H5-CLOUD
                    if (Object.keys(params).length) url = `${url}?data=${encodeURIComponent(JSON.stringify(params))}`;
                    // #endif
                    uni[type]({ url });
                }
            }
        }
    } catch (error) {}
}

/**
 * 渲染二维码或者条形码
 * @param {String} type -qrcode 二维码 barcode 条形码
 * @param {String} id -qrcode barcode生成二维码的节点id
 * @param {String} code -生成二维码的code
 * @param {Number} width -二维码的宽度
 * @param {Number} height -二维码的高度
 * @param {Object} _this -组件页面需要传当前页面的this，否则方法内不能创建cavas的节点，不能生成二维码
 **/
function renderingQrcode(type, id, code, width, height, _this) {
    if (type == 'qrcode') {
        QRCODE.qrcode(id, code, width, height, _this);
    } else if (type == 'barcode') {
        QRCODE.barcode(id, code, width, height, _this);
    }
}

// cube页面 小程序跳转小程序
function cubeMini(url = '', appId = '') {
    // uni.showToast({
    //   title:`${JSON.stringify(CONFIG.cubeMiniAppId)} +${url}`
    // })
    // let qStr = encodeURIComponent(encodeURIComponent(JSON.stringify(query)))

    my.navigateToMiniProgram({
        appId: appId ? appId : CONFIG.cubeMiniAppId,
        path: url,
        extraData: {
            appClearTo: 'YES',
            startMultApp: 'YES',
        },
        success: res => {
            // 跳转成功
            //
            // uni.showToast({
            //   title:`${JSON.stringify(res)}`
            // })
        },
        // success:(res) {
        //     // 打开成功
        //     console.log(res,'打开成功')
        //     uni.showToast({
        //       title:`${JSON.stringify(res)}`
        //     })
        // },
        // fail(res){
        //     console.log(res,'打开失败')
        //     uni.showToast({
        //       title:`${JSON.stringify(res)} +打开失败`
        //     })
        // }
        fail: error => {
            // 跳转失败
            //
            // uni.showToast({
            //   title:`${JSON.stringify(res)} +打开失败`
            // })
        },
    });
}

// 营销助手判断
function getMarketingJudgment(stationCodeList) {
    return new Promise((resolve, reject) => {
        cnpcBridge.getSwitch('AIDiscountPrompt', res => {
            if (res !== 'no') {
                const data = JSON.parse(res).data || [];
                if (data && data.length > 0) {
                    const proCode = store.state.locationV3_app.cityCode.slice(0, 2);
                    const cityCode = store.state.locationV3_app.cityCode;
                    const matchItem = data.find(item => item.proCode == proCode);

                    if (matchItem) {
                        if (matchItem.cityCodes.length > 0) {
                            if (matchItem.cityCodes.find(item => item == cityCode)) {
                                if (matchItem.stationCodes.length > 0) {
                                    let newStationCodeList = [];
                                    newStationCodeList = stationCodeList.filter(stationItem => {
                                        return matchItem.stationCodes.some(stationItem2 => stationItem2 == stationItem);
                                    });
                                    resolve(newStationCodeList);
                                } else {
                                    resolve(stationCodeList);
                                }
                            } else {
                                resolve([]);
                            }
                        } else {
                            if (matchItem.stationCodes.length > 0) {
                                let newStationCodeList = [];
                                newStationCodeList = stationCodeList.filter(stationItem => {
                                    return matchItem.stationCodes.some(stationItem2 => stationItem2 == stationItem);
                                });
                                resolve(newStationCodeList);
                            } else {
                                resolve(stationCodeList);
                            }
                        }
                    } else {
                        resolve([]);
                    }
                } else {
                    resolve(stationCodeList);
                }
            } else {
                resolve([]);
            }
        });
    });
}

// 返回首页
async function backHomeFun() {
    // #ifdef MP-ALIPAY
    let { cppeiLoginInfo: userInfo, openId, memberNo, newMember } = (await uni.getStorageSync('tokenInfo')) || {};
    // 2.0用户
    if (newMember === false) {
        uni.reLaunch({
            url: `/pages/index/index`,
        });
        return;
    }
    // #endif

    uni.reLaunch({
        url: `${projectConfig?.subPath || ''}/pages/thirdHome/main`,
    });
}
// 关闭小程序
function closeEvent() {
    const pages = getCurrentPages();
    if (pages.length <= 1) {
        // #ifdef MP-MPAAS
        cnpcBridge.closeMriver(res => {});
        // #endif
        // #ifndef MP-MPAAS
        backHomeFun();
        // #endif
    } else {
        uni.navigateBack();
    }
}
// 处理数据缓存封装
function dataStorage(key, value, type) {
    if (type == 'setStorage') {
        uni.setStorageSync(key, value);
    } else if (type == 'getStorage') {
        return uni.getStorageSync(key);
    } else if (type == 'removeStorage') {
        return uni.removeStorageSync(key);
    }
}

// showModal封装
function showModal({
    title = '提示',
    content = '',
    confirmText = '确定',
    cancelText = '取消',
    showCancel = false,
    confirmColor = '#FF8200',
    cancelColor = '#666666',
}) {
    return new Promise(function (resolve, reject) {
        console.log(confirmColor, 'confirmColor');
        uni.showModal({
            title: title,
            content: content,
            confirmColor: confirmColor,
            cancelColor: cancelColor,
            showCancel: showCancel,
            confirmText: confirmText,
            cancelText: cancelText,
            success: res => {
                if (res.confirm) {
                    resolve(res);
                } else if (res.cancel) {
                    reject(res);
                }
            },
        });
    });
}

// 计算手机顶部状态栏和navbar的总高度
function systemTopHeight() {
    let systemInfo = uni.getSystemInfoSync();
    console.log(systemInfo.statusBarHeight + 44, '计算系统栏+navbar高度');
    return systemInfo.statusBarHeight + 44;
}
// 显示错误小黑框
function showToast({ title = '', duration = 2000, icon = 'none' }) {
    uni.showToast({
        title: title,
        duration: duration,
        icon: icon,
    });
}

// 获取随机数
function getTYUUID(str) {
    return str.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}
// 小于10的时候补0
function getzf(num) {
    if (parseInt(num) < 10) {
        num = '0' + num;
    }
    return num;
}

function returnFloat(num) {
    num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
    if (num.indexOf('.') !== -1) {
        let [integerPart, decimalPart] = num.split('.');

        if (decimalPart.length > 2) {
            decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
            decimalPart += '0';
        }

        num = `${integerPart}.${decimalPart}`;
    } else {
        num += '.00';
    }

    return num;
}
// 支付宝小程序请求随机字符串
function randomString(len) {
    len = len || 32;
    var $chars = 'ABCDEFGHIJKMNOPQRSTUVWXYZ';
    var maxPos = $chars.length;
    var pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}
// 获取当前时间
function getMyTime(str) {
    var oDate = new Date(str),
        oYear = oDate.getFullYear(),
        oMonth = oDate.getMonth() + 1,
        oDay = oDate.getDate(),
        oHour = oDate.getHours(),
        oMinute = oDate.getMinutes(),
        oSecond = oDate.getSeconds(),
        oTime = oYear + '-' + getzf(oMonth) + '-' + getzf(oDay) + ' ' + getzf(oHour) + ':' + getzf(oMinute) + ':' + getzf(oSecond); //最后拼接时间
    return oTime;
}

// 查看pdf协议
function viewPdfAgreement(url) {
    // #ifndef H5-CLOUD
    uni.downloadFile({
        url: url, //示例的url地址
        success: function (resinfo) {
            console.log('pdf协议文件已下载');
            let path = resinfo.tempFilePath;
            console.log(path, resinfo);
            uni.openDocument({
                filePath: path,
                fileType: 'pdf',
                success: function (rest) {
                    console.log('打开文件成功');
                    console.log(rest);
                },
                fail: function (error) {
                    uni.showToast({
                        icon: 'none',
                        title: '未找到该协议',
                    });
                },
            });
        },
        fail: function (err) {
            console.log('fail');
            // console.log(err);
            uni.showToast({
                icon: 'none',
                title: '未找到该协议',
            });
        },
    });
    // #endif
    // #ifdef H5-CLOUD
    var agent = navigator.userAgent.toLowerCase();
    var versionMatch = agent.match(/version (\d+)/);
    if (versionMatch && versionMatch[1] >= '1021') {
        console.log('版本是1021', versionMatch);
        upsdk.pluginReady(function () {
            upsdk.openDocument({
                filePath: url, //文件路径
                fileType: 'pdf', //文件类型，支持pdf
                success: function (data) {
                    // 插件调用成功
                },
                fail: function () {
                    // 插件调用失败
                },
            });
        });
    } else {
        console.log('版本不是1021');
        uni.showToast({
            title: '此版本不支持',
            icon: 'none',
            duration: 2000,
        });
        // upsdk.createWebView({
        //     url:url,
        //     isFinish:'0' //是否关闭当前的窗口，1':关闭，'0':不关闭
        // });
    }
    // #endif
}

// 节流
function throttleUtil(func, wait = 2000, type = 1) {
    //节流， type 1马上执行 2 隔一段时间执行
    let previous = type == 1 ? 0 : Date.now();
    return function () {
        let now = Date.now();
        let context = this;
        let args = arguments;
        if (now - previous > wait) {
            func.apply(context, args);
            previous = now;
        }
    };
}

// 检查网络同步
function getServerTimeSync() {
    return new Promise((resolve, reject) => {
        my.getServerTime({
            success: res => {
                resolve(res);
            },
            fail: err => {
                console.log(err);
                console.log('getServerTime_fail');
                reject(err);
            },
        });
    });
}

// 获取随机字符串
function getRandomString(len = 32) {
    let chars = 'ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-';
    let maxPos = chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

// 授权获取小程序静默authBaseCode
function getAuthBaseCodeSync(refresh = false) {
    const authCode = uni.getStorageSync('authBaseCode');
    if (!refresh && authCode) return authCode;
    return new Promise((resolve, reject) => {
        my.getAuthCode({
            scopes: ['auth_base'], // 静默授权
            success: res => {
                uni.setStorageSync('authBaseCode', res.authCode);
                resolve(res.authCode);
            },
            fail: err => {
                console.log(err);
                console.log('getAuthBaseCodeAsync_fail');
                reject(err);
            },
        });
    });
}

// md5加密
function md5(string) {
    // 将一个32位整数进行循环左移
    function md5_RotateLeft(lValue, iShiftBits) {
        return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }
    // 用于对两个无符号的32位整数进行加法运算
    function md5_AddUnsigned(lX, lY) {
        var lX4, lY4, lX8, lY8, lResult;
        lX8 = lX & 0x80000000;
        lY8 = lY & 0x80000000;
        lX4 = lX & 0x40000000;
        lY4 = lY & 0x40000000;
        lResult = (lX & 0x3fffffff) + (lY & 0x3fffffff);
        if (lX4 & lY4) {
            return lResult ^ 0x80000000 ^ lX8 ^ lY8;
        }
        if (lX4 | lY4) {
            if (lResult & 0x40000000) {
                return lResult ^ 0xc0000000 ^ lX8 ^ lY8;
            } else {
                return lResult ^ 0x40000000 ^ lX8 ^ lY8;
            }
        } else {
            return lResult ^ lX8 ^ lY8;
        }
    }
    // 用于计算MD5哈希算法中的F函数的函数,该函数接受三个参数，x、y和z，分别表示三个无符号的32位整数。函数的作用是根据MD5哈希算法中的F函数定义，对这三个整数进行运算，并返回结果。

    function md5_F(x, y, z) {
        return (x & y) | (~x & z);
    }
    // 用于计算MD5哈希算法中的G函数的函数,该函数接受三个参数，x、y和z，分别表示三个无符号的32位整数。函数的作用是根据MD5哈希算法中的F函数定义，对这三个整数进行运算，并返回结果。
    function md5_G(x, y, z) {
        return (x & z) | (y & ~z);
    }
    // 用于计算MD5哈希算法中的H函数的函数,该函数接受三个参数，x、y和z，分别表示三个无符号的32位整数。函数的作用是根据MD5哈希算法中的F函数定义，对这三个整数进行运算，并返回结果。
    function md5_H(x, y, z) {
        return x ^ y ^ z;
    }
    // 用于计算MD5哈希算法中的I函数的函数,该函数接受三个参数，x、y和z，分别表示三个无符号的32位整数。函数的作用是根据MD5哈希算法中的F函数定义，对这三个整数进行运算，并返回结果。
    function md5_I(x, y, z) {
        return y ^ (x | ~z);
    }
    // MD5哈希算法中的FF函数,该函数接受七个参数，分别为无符号的32位整数a、b、c、d、x、s和ac。函数的作用是根据MD5哈希算法中的FF函数定义，对这些参数进行一系列的运算，并返回结果。
    function md5_FF(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    }
    // MD5哈希算法中的GG函数,该函数接受七个参数，分别为无符号的32位整数a、b、c、d、x、s和ac。函数的作用是根据MD5哈希算法中的FF函数定义，对这些参数进行一系列的运算，并返回结果。
    function md5_GG(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    }
    // MD5哈希算法中的HH函数,该函数接受七个参数，分别为无符号的32位整数a、b、c、d、x、s和ac。函数的作用是根据MD5哈希算法中的FF函数定义，对这些参数进行一系列的运算，并返回结果。
    function md5_HH(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    }
    // MD5哈希算法中的II函数,该函数接受七个参数，分别为无符号的32位整数a、b、c、d、x、s和ac。函数的作用是根据MD5哈希算法中的FF函数定义，对这些参数进行一系列的运算，并返回结果。
    function md5_II(a, b, c, d, x, s, ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
    }
    // 将字符串转换为MD5算法中所需的字节数组
    function md5_ConvertToWordArray(string) {
        var lWordCount;
        var lMessageLength = string.length;
        var lNumberOfWords_temp1 = lMessageLength + 8;
        var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
        var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
        var lWordArray = Array(lNumberOfWords - 1);
        var lBytePosition = 0;
        var lByteCount = 0;
        while (lByteCount < lMessageLength) {
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition);
            lByteCount++;
        }
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
        lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
        lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
        return lWordArray;
    }
    // 将字转换为十六进制字符串
    function md5_WordToHex(lValue) {
        var WordToHexValue = '',
            WordToHexValue_temp = '',
            lByte,
            lCount;
        for (lCount = 0; lCount <= 3; lCount++) {
            lByte = (lValue >>> (lCount * 8)) & 255;
            WordToHexValue_temp = '0' + lByte.toString(16);
            WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
        }
        return WordToHexValue;
    }
    // 将字符串转换为UTF-8编码
    function md5_Utf8Encode(string) {
        string = string.replace(/\r\n/g, '\n');
        var utftext = '';
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if (c > 127 && c < 2048) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    }

    var x = Array();
    var k, AA, BB, CC, DD, a, b, c, d;
    var S11 = 7,
        S12 = 12,
        S13 = 17,
        S14 = 22;
    var S21 = 5,
        S22 = 9,
        S23 = 14,
        S24 = 20;
    var S31 = 4,
        S32 = 11,
        S33 = 16,
        S34 = 23;
    var S41 = 6,
        S42 = 10,
        S43 = 15,
        S44 = 21;
    // 将字符串转换为UTF-8编码
    string = md5_Utf8Encode(string);
    // 将字符串转换为MD5算法中所需的字节数组
    x = md5_ConvertToWordArray(string);
    a = 0x67452301;
    b = 0xefcdab89;
    c = 0x98badcfe;
    d = 0x10325476;
    for (k = 0; k < x.length; k += 16) {
        AA = a;
        BB = b;
        CC = c;
        DD = d;
        a = md5_FF(a, b, c, d, x[k + 0], S11, 0xd76aa478);
        d = md5_FF(d, a, b, c, x[k + 1], S12, 0xe8c7b756);
        c = md5_FF(c, d, a, b, x[k + 2], S13, 0x242070db);
        b = md5_FF(b, c, d, a, x[k + 3], S14, 0xc1bdceee);
        a = md5_FF(a, b, c, d, x[k + 4], S11, 0xf57c0faf);
        d = md5_FF(d, a, b, c, x[k + 5], S12, 0x4787c62a);
        c = md5_FF(c, d, a, b, x[k + 6], S13, 0xa8304613);
        b = md5_FF(b, c, d, a, x[k + 7], S14, 0xfd469501);
        a = md5_FF(a, b, c, d, x[k + 8], S11, 0x698098d8);
        d = md5_FF(d, a, b, c, x[k + 9], S12, 0x8b44f7af);
        c = md5_FF(c, d, a, b, x[k + 10], S13, 0xffff5bb1);
        b = md5_FF(b, c, d, a, x[k + 11], S14, 0x895cd7be);
        a = md5_FF(a, b, c, d, x[k + 12], S11, 0x6b901122);
        d = md5_FF(d, a, b, c, x[k + 13], S12, 0xfd987193);
        c = md5_FF(c, d, a, b, x[k + 14], S13, 0xa679438e);
        b = md5_FF(b, c, d, a, x[k + 15], S14, 0x49b40821);
        a = md5_GG(a, b, c, d, x[k + 1], S21, 0xf61e2562);
        d = md5_GG(d, a, b, c, x[k + 6], S22, 0xc040b340);
        c = md5_GG(c, d, a, b, x[k + 11], S23, 0x265e5a51);
        b = md5_GG(b, c, d, a, x[k + 0], S24, 0xe9b6c7aa);
        a = md5_GG(a, b, c, d, x[k + 5], S21, 0xd62f105d);
        d = md5_GG(d, a, b, c, x[k + 10], S22, 0x2441453);
        c = md5_GG(c, d, a, b, x[k + 15], S23, 0xd8a1e681);
        b = md5_GG(b, c, d, a, x[k + 4], S24, 0xe7d3fbc8);
        a = md5_GG(a, b, c, d, x[k + 9], S21, 0x21e1cde6);
        d = md5_GG(d, a, b, c, x[k + 14], S22, 0xc33707d6);
        c = md5_GG(c, d, a, b, x[k + 3], S23, 0xf4d50d87);
        b = md5_GG(b, c, d, a, x[k + 8], S24, 0x455a14ed);
        a = md5_GG(a, b, c, d, x[k + 13], S21, 0xa9e3e905);
        d = md5_GG(d, a, b, c, x[k + 2], S22, 0xfcefa3f8);
        c = md5_GG(c, d, a, b, x[k + 7], S23, 0x676f02d9);
        b = md5_GG(b, c, d, a, x[k + 12], S24, 0x8d2a4c8a);
        a = md5_HH(a, b, c, d, x[k + 5], S31, 0xfffa3942);
        d = md5_HH(d, a, b, c, x[k + 8], S32, 0x8771f681);
        c = md5_HH(c, d, a, b, x[k + 11], S33, 0x6d9d6122);
        b = md5_HH(b, c, d, a, x[k + 14], S34, 0xfde5380c);
        a = md5_HH(a, b, c, d, x[k + 1], S31, 0xa4beea44);
        d = md5_HH(d, a, b, c, x[k + 4], S32, 0x4bdecfa9);
        c = md5_HH(c, d, a, b, x[k + 7], S33, 0xf6bb4b60);
        b = md5_HH(b, c, d, a, x[k + 10], S34, 0xbebfbc70);
        a = md5_HH(a, b, c, d, x[k + 13], S31, 0x289b7ec6);
        d = md5_HH(d, a, b, c, x[k + 0], S32, 0xeaa127fa);
        c = md5_HH(c, d, a, b, x[k + 3], S33, 0xd4ef3085);
        b = md5_HH(b, c, d, a, x[k + 6], S34, 0x4881d05);
        a = md5_HH(a, b, c, d, x[k + 9], S31, 0xd9d4d039);
        d = md5_HH(d, a, b, c, x[k + 12], S32, 0xe6db99e5);
        c = md5_HH(c, d, a, b, x[k + 15], S33, 0x1fa27cf8);
        b = md5_HH(b, c, d, a, x[k + 2], S34, 0xc4ac5665);
        a = md5_II(a, b, c, d, x[k + 0], S41, 0xf4292244);
        d = md5_II(d, a, b, c, x[k + 7], S42, 0x432aff97);
        c = md5_II(c, d, a, b, x[k + 14], S43, 0xab9423a7);
        b = md5_II(b, c, d, a, x[k + 5], S44, 0xfc93a039);
        a = md5_II(a, b, c, d, x[k + 12], S41, 0x655b59c3);
        d = md5_II(d, a, b, c, x[k + 3], S42, 0x8f0ccc92);
        c = md5_II(c, d, a, b, x[k + 10], S43, 0xffeff47d);
        b = md5_II(b, c, d, a, x[k + 1], S44, 0x85845dd1);
        a = md5_II(a, b, c, d, x[k + 8], S41, 0x6fa87e4f);
        d = md5_II(d, a, b, c, x[k + 15], S42, 0xfe2ce6e0);
        c = md5_II(c, d, a, b, x[k + 6], S43, 0xa3014314);
        b = md5_II(b, c, d, a, x[k + 13], S44, 0x4e0811a1);
        a = md5_II(a, b, c, d, x[k + 4], S41, 0xf7537e82);
        d = md5_II(d, a, b, c, x[k + 11], S42, 0xbd3af235);
        c = md5_II(c, d, a, b, x[k + 2], S43, 0x2ad7d2bb);
        b = md5_II(b, c, d, a, x[k + 9], S44, 0xeb86d391);
        a = md5_AddUnsigned(a, AA);
        b = md5_AddUnsigned(b, BB);
        c = md5_AddUnsigned(c, CC);
        d = md5_AddUnsigned(d, DD);
    }
    return (md5_WordToHex(a) + md5_WordToHex(b) + md5_WordToHex(c) + md5_WordToHex(d)).toLowerCase();
}
// 非Mpaas入参
function getNoMpaasPostBody(config) {
    return new Promise(async (resolve, reject) => {
        let body = {};
        // #ifdef MP-ALIPAY
        let postData = {};
        const { cppeiLoginInfo: userInfo } = (await uni.getStorageSync('tokenInfo')) || {};
        postData.token = userInfo?.phoneToken || config.token || '';
        if (!config.body?.phoneToken) {
            delete config?.body.phoneToken;
            postData.phoneToken = userInfo?.phoneToken || config.token || '';
        }
        if (!config.body?.mobile) {
            delete config?.body.mobile;
            postData.mobile = userInfo?.phone || '';
        }
        let authBaseCode = await getAuthBaseCodeSync();
        config.url = config.url + '?authCode=' + authBaseCode;
        let serverTime = await getServerTimeSync();
        if (!serverTime || !serverTime.time) return;
        postData.timestamp = parseInt(serverTime.time / 1000);
        postData.noncestr = getRandomString(36);
        postData.wheretype = 7;
        postData.sign = '';
        postData.authcode = authBaseCode;
        postData = Object.assign({}, postData, config.body);
        const keys = Object.keys(postData).sort();
        const newObj = {};
        for (let i = 0; i < keys.length; i++) {
            newObj[keys[i]] = postData[keys[i]];
            console.log(newObj[keys[i]], postData[keys[i]], '对比');
        }
        let encryptionData = md5(`${JSON.stringify(newObj)}&zsy_key=` + projectConfig.v2sign);
        newObj.sign = encryptionData.toLocaleUpperCase();
        body = newObj;
        // #endif
        // #ifdef MP-TOUTIAO
        // const resInfo = await getCppeiLoginInfoApi();
        // if (resInfo.success) {
        //     let infoData = resInfo.data.cppeiLoginInfo;
        //     let token = '';
        //     token = JSON.parse(infoData).token;
        //     // console.log('token----toutiao',token)
        //     if (token) {
        //         body = Object.assign({}, config.body, { token: token });
        //         config.token = token;
        //     }
        // }
        // #endif
        // #ifdef MP-WEIXIN || H5-CLOUD

        let token = '';

        let tokenInfo = uni.getStorageSync('tokenInfo');
        if (store.state.token || tokenInfo.token) {
            token = store.state.token || tokenInfo.token;
        }
        if (token) {
            body = Object.assign({}, config.body, { token: token });
        }
        // #endif
        resolve(body);
    });
}

/**
 * @description: 车牌号格式处理
 * @param {*} licensePlate
 * @return {*}
 */
function formatVehicleNum(licensePlate) {
    if (typeof licensePlate !== 'string' || licensePlate.length < 2) {
        return licensePlate; // 如果输入不是字符串或长度小于2，则直接返回原始输入
    }

    const firstPart = licensePlate.slice(0, 2); // 车牌号码的前两个字符
    const secondPart = licensePlate.slice(2); // 车牌号码的第三个字符及之后的部分

    // 将车牌号码格式化为 "前两个字符 空格 第三个字符及之后的部分" 的形式
    return `${firstPart} ${secondPart}`;
}

/**
 * @description: 每隔四位分割字符串
 * @param {*} string
 * @return {*}
 */
function splitFourString(string) {
    let result = '';
    for (let i = 0; i < string.length; i++) {
        if (i % 4 == 0 && i != 0) {
            result += ' ' + string[i];
        } else {
            result += string[i];
        }
    }
    return result;
}
// js数字相加
function operation(a, b, op) {
    var o1 = toInteger(a);
    var o2 = toInteger(b);
    var n1 = o1.num;
    var n2 = o2.num;
    var t1 = o1.times;
    var t2 = o2.times;
    var max = t1 > t2 ? t1 : t2;
    var result = null;
    switch (op) {
        case 'add':
            if (t1 === t2) {
                // 两个小数位数相同
                result = n1 + n2;
            } else if (t1 > t2) {
                // o1 小数位 大于 o2
                result = n1 + n2 * (t1 / t2);
            } else {
                // o1 小数位 小于 o2
                result = n1 * (t2 / t1) + n2;
            }
            return result / max;
        case 'subtract':
            if (t1 === t2) {
                result = n1 - n2;
            } else if (t1 > t2) {
                result = n1 - n2 * (t1 / t2);
            } else {
                result = n1 * (t2 / t1) - n2;
            }
            return result / max;
        case 'multiply':
            result = (n1 * n2) / (t1 * t2);
            return result;
        case 'divide':
            result = (n1 / n2) * (t2 / t1);
            return result;
    }
}

function requestSubscribeMessage({ tmplIds = [], success = () => {}, fail = () => {}, complete = () => {}, settingFail = () => {} } = {}) {
    const projectTmpIds = projectConfig?.messageTmpIds ? projectConfig.messageTmpIds.split(',') : [];
    // #ifdef MP-WEIXIN
    wx.getSetting({
        withSubscriptions: true,
        success: res => {
            if (res.subscriptionsSetting.mainSwitch) {
                wx.requestSubscribeMessage({
                    tmplIds: tmplIds.length > 0 ? tmplIds : projectTmpIds,
                    success: res => {
                        console.log('success', res);
                        success(res);
                    },
                    fail: res => {
                        console.log('fail', res);
                        fail(res);
                    },
                    complete: res => {
                        console.log('complete', res);
                        complete(res);
                    },
                });
            }
        },
        fail: res => {
            settingFail(res);
        },
    });
    // #endif
    // #ifdef MP-ALIPAY
    my.requestSubscribeMessage({
        entityIds: tmplIds.length > 0 ? tmplIds : projectTmpIds,
        success: res => {
            console.log('success', res);
            success(res);
        },
        fail: res => {
            console.log('fail', res);
            fail(res);
        },
        complete: res => {
            console.log('complete', res);
            complete(res);
        },
    });
    // #endif
}
function toSecretKey(secretKey) {
    return CryptoJS.enc.Base64.parse(secretKey);
}
function encryptText(plaintext, secretKey) {
    try {
        const key = CryptoJS.enc.Base64.parse(secretKey);
        const encrypted = CryptoJS.TripleDES.encrypt(plaintext, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.toString();
    } catch (e) {
        console.error('Encryption failed:', e);
    }
}

function getOilNum(oilTypeStr, type = 0) {
    if (oilTypeStr && oilTypeStr.indexOf('#') !== -1) {
        let text = '';
        if (type === 0) {
            text = oilTypeStr.match(/^.*?#/)[0];
        } else if (type === 1) {
            // 找到第一个#号之前的内容，并去掉#号
            text = oilTypeStr.match(/^.*?#/)[0].replace('#', '');
        }
        return text;
    } else {
        return oilTypeStr;
    }
}

function filterLocations(data) {
    console.log(data.filter(item => item.stationStatus != 50),'油站列表过滤后');
    return data.filter(item => item.stationStatus != 50);
}

export default {
    useRouter, // 路由跳转
    showModal, // 显示模态框
    systemTopHeight, // 获取系统顶部高度
    showToast, // 显示提示框
    getTYUUID, // 获取uuid
    dataStorage, // 数据缓存
    getMyTime, // 获取当前时间
    viewPdfAgreement, // 预览PDF协议
    cubeMini, // 获取小程序码
    randomString, // 获取随机字符串
    getServerTimeSync, // 获取服务器时间
    getRandomString, // 获取随机字符串
    getAuthBaseCodeSync, // 获取授权码
    md5, // md5加密
    backHomeFun, // 返回首页
    renderingQrcode, // 生成二维码
    getNoMpaasPostBody, // 获取非MPAAS的请求体
    formatVehicleNum, //车牌号格式处理
    splitFourString,
    returnFloat, // 对数字保留两位小数时 不足两位 自动补0
    // js数字相加
    operation,
    requestSubscribeMessage,
    getMarketingJudgment, //营销助手灰度判断
    encryptText,
    getOilNum,
    filterLocations,
    closeEvent
};
