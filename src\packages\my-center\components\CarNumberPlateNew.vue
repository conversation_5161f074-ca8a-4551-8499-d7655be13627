<template>
    <div class="advertisingPopups" v-if="visible">
        <div class="full-div">
            <div class="car-number-plate" id="car-number-plate">
                <div class="car-number-plate-top">
                    <div class="car-number-plate-title">请输入车牌号</div>
                    <u-icon class="iconStyle" @click="cancle" name="close" color="#333" size="28"></u-icon>
                </div>
                <div class="car-number-plate__input" @click="show = !show">
                    <div :class="['number-items', curNumberPlate.length === 0 ? 'active' : '']">{{ curNumberPlate[0] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 1 ? 'active' : '']">{{ curNumberPlate[1] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 2 ? 'active' : '']">{{ curNumberPlate[2] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 3 ? 'active' : '']">{{ curNumberPlate[3] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 4 ? 'active' : '']">{{ curNumberPlate[4] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 5 ? 'active' : '']">{{ curNumberPlate[5] }}</div>
                    <div :class="['number-items', curNumberPlate.length === 6 ? 'active' : '']">{{ curNumberPlate[6] }}</div>
                    <div :class="['number-items', curNumberPlate.length >= 7 ? 'active' : '', 'number-items-dashed']">
                        <span v-if="!!curNumberPlate[7] || curNumberPlate[7] === 0">{{ curNumberPlate[7] }}</span>
                        <span v-else class="dsshed-new">新能源</span>
                    </div>
                </div>
                <!--  :style="{background:this.curNumberPlate.length < 6 ? '#333' : '#f96702'}" -->
                <button class="confirm" @click="submit" :style="{ background: bgColor ? '#f96702' : '#d68d59' }">确认</button>
                <div class="prompt">温馨提示:请输入正确的车牌号</div>
                <!-- keybord -->
                <u-keyboard
                    ref="uKeyboard"
                    :mask="false"
                    mode="car"
                    @change="valChange"
                    @backspace="backspace"
                    @confirm="confirm"
                    v-model="show"
                ></u-keyboard>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
// import { mobilePhoneNumberEncryption } from "@/api/home.js";
export default {
    name: 'advertisingPopups',
    components: {},
    props: {
        carNo: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            visible: true,
            show: true,
            curNumberPlate: ['', '', '', '', '', '', '', ''],
            bgColor: false,
        };
    },
    onReady() {
        // console.log('HHHHH')
        // 如果想一进入页面就打开键盘，请在此生命周期调用
        this.show = true;
    },
    onLoad() {
        this.curNumberPlate = this.carNo.split('');
    },
    computed: {
        ...mapState({
            advertisData: state => state.location.advertisData,
        }),
        ...mapGetters(['registerLoginInformation']),
    },
    watch: {
        curNumberPlate: function (val, oldVal) {
            // console.log(val, 'valvalvalvalval');
            // if (val.length >= 1) this.isShowProvice = false;
            // else this.isShowProvice = true;
            // if (this.bgColor) {
            //   this.$emit("markCar", val.toString().replace(/,/g, ""));
            // }
            // console.log(val, oldVal, 'oldValoldValoldValoldValoldVal');
        },
        carNo: function (val) {
            // console.log(val, 'valvalvalvalval');
        },
    },
    methods: {
        close() {
            this.$emit('closeEvent');
        },
        // 按键被点击(点击退格键不会触发此事件)
        valChange(val) {
            const index = this.curNumberPlate.findIndex(item => item === '');
            if (index == -1) return;
            this.curNumberPlate.splice(index, 1, val);
            this.getRepeatNum(this.curNumberPlate);
            if (this.curNumberPlate.toString().replace(/,/g, '').length >= 7) {
                const carNoValid =
                    /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{6})|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/.test(
                        this.curNumberPlate.toString().replace(/,/g, ''),
                    );
                if (!carNoValid) {
                    uni.showToast({
                        title: '车牌号格式错误',
                        icon: 'none',
                        duration: 2000,
                    });
                    return;
                }
                this.curNumberPlate.toString().replace(/,/g, '').length >= 7 ? (this.bgColor = true) : (this.bgColor = false);
            } else {
                this.bgColor = false;
            }
        },
        // 退格键被点击
        backspace() {
            if (this.curNumberPlate.length) {
                for (let index = this.curNumberPlate.length - 1; index >= 0; index--) {
                    if (this.curNumberPlate[index] !== '') {
                        // index 是表示第几个   1 是替换几个   '' 是替换成什么
                        this.curNumberPlate.splice(index, 1, '');
                        break;
                    }
                }
            }
        },
        // 点击确定将车牌号分发出去
        confirm(e) {
            // 监视了this.curNumberPlate 只要用户点击键盘就分发车牌号
        },
        submit() {
            if (this.bgColor) {
                this.$emit('markCar', this.curNumberPlate.toString().replace(/,/g, ''));
                this.$parent.clickLicensePlateNumber();
            }
        },
        cancle() {
            this.$parent.clickLicensePlateNumber();
        },
        // 用reduce时：

        getRepeatNum(arr) {},
    },
};
</script>

<style lang="scss" scoped>
.advertisingPopups {
    position: fixed;
    bottom: 0;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.6);
    .full-div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column;
        .car-number-plate {
            display: flex;
            // justify-content: center;
            flex-direction: column;
            width: 100%;
            justify-content: space-around;
            width: 90%;
            height: 30%;
            background: #fff;
            border-radius: 8px;
            position: absolute;
            top: 30%;
            .car-number-plate-top {
                display: flex;
                justify-content: space-between;
                margin: 5px 5px 15px 5px;
                .car-number-plate-title {
                    font-size: 20px;
                    color: #111;
                }
                .iconStyle {
                    margin-right: 2px;
                    width: 50px;
                    justify-content: flex-end;
                }
            }
            &__input {
                width: 100%;
                display: flex;
                justify-content: space-between;
                .number-items {
                    display: flex;
                    flex-direction: row;
                    margin-left: 5px;
                    margin-right: 5px;
                    justify-content: center;
                    flex: 1;
                    align-items: center;
                    // width: 34px;
                    // height: 49px;
                    width: 30px;
                    height: 50px;
                    background: #fff7f2;
                    border: 1px solid #f96702;
                    border-radius: 3px;
                    font-size: 18px;
                    color: #333;
                    position: relative;
                }
                .number-items-dashed {
                    text-align: center;
                    border: 1px solid #00f05f;
                    background: #83f0ae;
                    height: 50px;
                    .dsshed-new {
                        font-size: 11px;
                        padding: 0 6px;
                        line-height: 14px;
                        color: #ffffff;
                    }
                }
                // 当前号码位置
                .active {
                    // &::before {
                    //   content: "";
                    //   position: absolute;
                    //   right: 0px;
                    //   bottom: 0;
                    //   display: block;
                    //   width: 0;
                    //   height: 0;
                    //   border: 3px solid;
                    //   border-color: transparent $color-theme-o $color-theme-o transparent;
                    // }
                }
            }
            // keybord
            &__keybord {
                position: fixed;
                bottom: 0;
                left: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 220px;
                background-color: #ebebeb;
                .keybord-header {
                    display: flex;
                    justify-content: space-between;
                    padding-left: 10px;
                    height: 30px;
                    line-height: 30px;
                    text-align: right;
                    background-color: #fff;

                    & > span {
                        margin-right: 10px;
                        color: #5cacee;
                        font-size: 14px;
                    }
                }
                .keybord-body {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    background-color: #ebebeb;
                    &__row {
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        .row-item {
                            display: flex;
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;
                            width: 28px;
                            height: 35px;
                            margin: 4px;
                            font-size: 12px;
                            background-color: #fff;
                            border-radius: 4px;
                        }
                        .row-item-delete {
                            width: 46px;
                            .icon-oil {
                                font-size: 20px;
                            }
                            img {
                                width: 30px;
                                height: 20px;
                            }
                        }
                    }
                }
                // swipe
                .my-swipe {
                    width: 100%;
                    height: 100%;
                    ::v-deep .van-swipe-item {
                        width: 100%;
                    }
                    ::v-deep .van-swipe__indicators {
                        top: 60%;
                        left: 5px;
                        .van-swipe__indicator {
                            background-color: #111;
                        }
                    }
                }
            }
            .confirm {
                // background: #f96702;
                // margin: 0 auto;
                height: 40px;
                line-height: 40px;
                margin-left: 5px;
                margin-right: 5px;
                color: #fff;
                margin-top: 15px;
                border-radius: 20px;
            }
            .prompt {
                margin-top: 15px;
                font-size: 12px;
                margin-left: 5px;
                color: #ecad3a;
            }
        }
        ::v-deep .u-icon--right {
            margin-right: 2px;
            width: 50px;
            justify-content: flex-end;
        }
    }
}
</style>
