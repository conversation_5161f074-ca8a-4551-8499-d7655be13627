<template>
    <div class="content">
        <zj-navbar :height="44" title="加油"></zj-navbar>
        <!-- 地图 -->
        <map show-location style="width: 100%; height: 300px" latitude="40.16714200000000" longitude="116.23730800000000"> </map>
        <!-- <div id="allmap" class="allmap"></div> -->
    </div>
</template>

<script type="module">
// import {MP} from '../../../../s-kit/js/map'
export default {
    data() {
        return {
            bdMap: null,
        };
    },
    // onLoa
    onShow() {},
    mounted() {
        // this.$nextTick(() => {
        //   console.log('MP---',MP())
        //   MP().then(BMap => {
        //       //在此调用api
        //       this.loadBd(BMap); //加载百度地图
        //     });
        //   })
    },
    methods: {
        // loadBd: function (BMap) {
        //     this.bdMap = new BMap.Map('allmap'); //在百度地图容器中创建一个地图
        //     this.bdMap.centerAndZoom(new BMap.Point(116.246635,  40.17341), 10); // 初始化地图,设置中心点坐标和地图级别
        //     //添加地图类型控件
        //     this.bdMap.addControl(
        //       new BMap.MapTypeControl({
        //         mapTypes: [] //地图混合去掉
        //         // mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
        //       })
        //     );
        //     this.bdMap.enableScrollWheelZoom(true);
        //   },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.content {
    width: 100%;
    height: 500px;
}

.global {
    width: 100%;
    height: 100%;
}

#allmap {
    width: 100%;
    height: 100%;
}
</style>
