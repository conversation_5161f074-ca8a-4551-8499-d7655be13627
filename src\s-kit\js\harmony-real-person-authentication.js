import { realNameAuth, initRealPersonIdentify, realPersonIdentify } from './v3-http/https3/oilCard/index';
import cnpcBridge from './v3-native-jsapi/cnpcBridge';
import Store from '../../store/index';

// #ifdef MP-MPAAS
// 支付宝小程序实名--初始化实人认证--人脸验证-实名认证方法
async function startVerification(personInfo) {
    return new Promise(async (resolve, reject) => {
      console.log('personInfo',personInfo)
        // 实名认证方法
        await realNameAuthFun(personInfo)
            .then(async realNameAuthRes => {
                let params = {
                    ...personInfo,
                    ...realNameAuthRes,
                };
                console.log('harmony---params',params)
                // 人脸验证
                return await initFaceAuthentication(params);
            })
            .then(async initFaceAuthenticationRes => {
                // 实人认证方法
                return await harmonyFace(initFaceAuthenticationRes);
            })
            .then(async harmonyFaceRes => {
              console.log('harmonynFaceRes',harmonyFaceRes)
              let params1 = {
                  ...personInfo,
                  ...harmonyFaceRes,
              };
              console.log('params1',params1)
              // 实名认证
              return await realNameAuthentication(params1);
          })
          .then(async result => {
              return await resolve(result);
          })
  
            .catch(error => {
                return reject(error); // 返回失败的结果
                uni.showToast({ title: 'startVerification报错' + error });
            });
    });
  }
  
  /**
   * @description  : 实名认证  (同意开通开通昆仑e享卡)
   * @param         {String} realName -用户在上个页面输入的姓名
   * @param         {String} idNo -用户在上个页面输入的身份证号
   * @param         {String} idType -身份证件类型 1:身份证 2:军人身份证件 3:台胞证 4:港澳通行证 5:外国人永久居留身份证 6:护照
   * @param         {String} type - 7-绑卡 1-电子账户注册 2-忘记密码 11-风控校验
   * @param         {String} authInfo - Type=7必传，传入验证码接口传过来的字串
   * @return        {*}
   */
  function realNameAuthFun(personInfo) {
      return new Promise(async (resolve, reject) => {
          let params = {
              realName: personInfo.name,
              idType: '1',
              idNo: personInfo.idNo,
              authInfo: personInfo.authInfo || '',
              type: personInfo.type || '1',
              // areaCode: this.locationInfo.cityCode || this.personInfo.areaCode
              // areaCode: this.personInfo.areaCode
          };
          console.log('realNameAuthFun--params',params)
          let res = await realNameAuth(params);
          if (res.data.authInfo && res.success) {
              console.log('初始化实人认证接口返回', res);
              resolve(res.data);
          }
      });
  }
  /**
   * @description  :  人脸验证初始化
   * @param         {String} returnUrl -业务回跳地址；PC或H5接入时 必传，APP接入时为空
   * @param         {String} metaInfo -MetaInfo环境参数，需要通过JS获取或SDK获取
   * @param         {String} verifyMode -认证接入方式：1—APP接入；2—PC或H5接入；
   * @param         {Function} aliMetaInfo -阿里人脸采集MetaInfo
   * @param         {string} verifyUnique -身份认证唯一标识
   * @param         {string} certifyId -实人认证三方系统的标识
   * @param         {string} certifyUrl -第三方认证地址
   * @param         {Function} zjShowModal -全局自定义弹窗
   * @return        {*}
   */
  function initFaceAuthentication(param) {
      return new Promise(async (resolve, reject) => {
      //     let params = {
      //       returnUrl: '',
      //       metaInfo: await cnpcBridge.aliMetaInfo(),
      //       verifyMode: '2',
      //   };
      // 在调用实人认证服务端发起认证请求时需要传入该MetaInfo值
      // let metaInfo= {
      //     apdidToken: "",
      //     bioMetaInfo: "4.1.0:2916352,0",
      //     deviceType: "h5",
      //     ua: "Mozilla/5.0 (Linux; U; Android 10; zh-CN; MGA-AL40 Build/HUAWEIMGA-AL40) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/69.0.3497.100 UWS/********* Mobile Safari/537.36 UCBS/*********_231122121339 AlipayDefined AriverApp(mPaaSClient/10.2.8) MiniProgram"
      // }
      let params = {
          returnUrl: '/packages/third-electronic-wallet/pages/wallet-authorization-description',
          // MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入，目前只有app和微信小程序需要传入该值，支付宝小程序可以传空字符串。
          // metaInfo: JSON.stringify(metaInfo),
          metaInfo:await cnpcBridge.aliMetaInfo(),
          verifyMode: '1',
      };
        let res = await initRealPersonIdentify(params);
          if (res && res.success) {
            if (JSON.stringify(res.data) !== '{}') {
                console.log(res.data, '=====初始化实人认证接口返回结果');
                let params = {
                    ...param,
                    ...res.data,
                };
                resolve(params);
            }
        }
      });
  }
  /**
   * @description  :  支付宝人脸验证
   * @param         {String} certifyId -实人认证三方系统的标识
   * @param         {String} url -第三方认证地址
   */
  function harmonyFace(val) {
    console.log(val,'人脸验证val')
      return new Promise((resolve, reject) => {
          // let url = 'http://m.alyms.cn/F4.bTmpKP'
          // cnpcBridge.openModule({
          //     type: 'web',
          //     url: val.certifyUrl,
          // });
          cnpcBridge.aliFaceCollec(val.certifyId, async result => {
            console.log('result',result)
            if (result.status) {
              // let params = {
              //     ...val,
              //     ...result
              // };
                //采集成功
                // await realNameAuthentication(val);
                resolve(val);
            } else {
                Store.dispatch('zjShowModal', {
                    title: result.msg,
                    confirmText: '确认',
                    success: res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                        }
                    },
                });
            }
          });
      });
  }
  
  /**
   * @description  :  实人认证
   * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
   * @param         {String} authInfo: 认证校验码，实名认证接口返回的数据。
   * @param         {String} verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
   * @param         {string} certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
   * @param         {string} verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
   * @param         {string} idNo: 用户身份证号
   * @return        {*}
   */
  function realNameAuthentication(wxOrZfbFaceValue,) {
    console.log('wxOrZfbFaceValue',wxOrZfbFaceValue)
      return new Promise(async (resolve, reject) => {
          let params = {
            type: wxOrZfbFaceValue.type || 1,
            authInfo:wxOrZfbFaceValue.authInfo,
            verifyUnique: wxOrZfbFaceValue.verifyUnique,
            certifyId: wxOrZfbFaceValue.certifyId,
            verifyMode: '1',
            idNo: wxOrZfbFaceValue.idNo,
        };
          let res = await realPersonIdentify(params);
          console.log(res,'realNameAuthentication====')
          if (res.success) {
              console.log(params, '实人认证方法接口返回结果');
              resolve(res);
          }
      });
  }
  export default {
    startVerification,
  };
// #endif