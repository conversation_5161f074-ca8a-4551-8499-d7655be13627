<template>
    <div>
        <div class="get-phone-number" v-if="!coverMap">
            <div class="slot-container" @click="getPhoneNumber" v-if="isLogin">
                <slot></slot>
            </div>
            <button
                v-else
                open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber"
                hover-class="none"
                :phone-number-no-quota-toast="false"
            >
                <slot></slot>
            </button>
        </div>
        <cover-view v-else>
            <cover-view class="slot-container" @click="getPhoneNumber" v-if="isLogin">
                <slot></slot>
            </cover-view>
            <button
                v-else
                open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber"
                hover-class="none"
                :phone-number-no-quota-toast="false"
            >
                <slot></slot>
            </button>
        </cover-view>
        <!-- <Login v-if="loginIsShow" @change="change" :btnType="this.btnType" :params="params"></Login> -->
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { setedPayPass } from '../../api/home';
import Store from '../../store';
// import Login from '@/packages/setting/pages/login/main.vue'

export default {
    name: 'getPhoneNumber',
    props: {
        coverMap: {
            default: false,
            type: Boolean,
        },
        btnType: {
            default: 'normal',
            type: String,
        },
        curTabIndex: {
            default: '',
            type: String,
        },
        params: {
            default: {},
            type: Object,
        },
    },
    data() {
        return {
            loginIsShow: false,
            lastTapTime: 0,
        };
    },
    mounted() {
        console.log(this.isLogin, 'isLoginisLoginisLogin');
        this.getPhoneNumber = this.$util.throttleUtil(this.getPhoneNumber);
    },
    computed: {
        ...mapGetters(['isLogin']),
        ...mapState({
            officialAccountParams: state => state.location.officialAccountParams, // 存储外部跳转进来携带的参数
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            personalInformation3: state => state.personalInformation3,
        }),
    },
    methods: {
        async getPhoneNumber(e) {
            console.log(this.isLogin);
            console.log('授权登录', e);
            let currentTime = e.timeStamp; // 当前点击的时间戳
            let lastTapTime = this.lastTapTime; // 上一次点击的时间戳
            console.log(currentTime - lastTapTime, 'currentTime - lastTapTime');
            if (currentTime - lastTapTime > 500) {
                this.lastTapTime = e.timeStamp;
                console.log(e.mp.detail.errno, '执行了吗');
                if (e.mp.detail.errno == '104') {
                    try {
                        wx.getPrivacySetting({
                            success: res => {
                                let params = {
                                    params: this.params,
                                    btnType: this.btnType,
                                };
                                let _this = this;
                                console.log(res.needAuthorization, 'res.needAuthorization');
                                if (res.needAuthorization) {
                                    this.$store.dispatch('receiveButtonParameters', {
                                        privacySuccess(res) {
                                            if (res.confirm) {
                                                // _this.getPhoneNumberLogin(e);
                                            } else if (res.cancel) {
                                            }
                                        },
                                    });
                                } else {
                                }
                            },
                            fail: () => {},
                        });
                    } catch (error) {
                        this.getPhoneNumberLogin(e);
                    }
                    return;
                } else {
                    this.getPhoneNumberLogin(e);
                }
            }
        },
        async getPhoneNumberLogin(e) {
            console.log(this.isLogin);
            console.log('授权登录', e);
            // e.mp.detail.errno = "1400001";
            if (!this.isLogin && e.mp.detail.errno == '1400001') {
                let loginBackParams = {
                    params: this.params,
                    btnType: this.btnType,
                };
                let url = '/packages/third-new-third-login/pages/login/main';
                let params = loginBackParams;
                this.$sKit.layer.useRouter(url, params);
                return;
            } else {
                if (this.isLogin) {
                    // 已登录
                    this.$emit('change', {
                        isLogin: true,
                        btnType: this.btnType,
                        lastLogin: true, // 上一次登录态为 true
                        params: this.params,
                    });
                    return;
                }
                // #ifdef MP-WEIXIN
                if (e.mp.detail.errMsg === 'getPhoneNumber:fail user deny') {
                    //不同意授权
                    this.$emit('change', {
                        isLogin: false,
                        btnType: this.btnType,
                    });
                    if (this.officialAccountParams) {
                        this.$store.commit('steTransferAccountGrayBtnFlag', true);
                    }
                }
                if (e.mp.detail.errMsg === 'getPhoneNumber:ok') {
                    if (this.officialAccountParams) {
                        this.$store.commit('steTransferAccountGrayBtnFlag', false);
                    }
                    this.$store.commit('setMaskDialogFlag', true);
                    // 同意授权
                    console.log(e.mp.detail, '解密用户信息');
                    console.log('一键登录');
                    return;
                    await this.$wxLogin.registerByAuthPhoneNumber(e.mp.detail).then(
                        async res => {
                            // 是否存在公众号跳转小程序的参数
                            if (!this.officialAccountParams) {
                                this.$emit('change', {
                                    isLogin: true,
                                    btnType: this.btnType,
                                    params: this.params,
                                });
                            } else {
                                this.$emit('change');
                            }

                            // // 退出登录 => 重新登录 => 需要重新获取是否设置密码的状态
                            // let isPassword = await setedPayPass()
                            //       Store.commit('setIsSetPassword', isPassword.data)
                        },
                        err => {
                            this.$emit('change', {
                                isLogin: false,
                                btnType: this.btnType,
                            });
                            // 出现异常将中转页按钮换回官方的type类型为getPhoneNumber的按钮
                            this.$store.commit('transferAccountGrayBtnFlag', true);
                        },
                    );
                }
                // #endif
                // #ifdef QUICKAPP-WEBVIEW-HUAWEI
                if (e.mp.detail.errMsg === 'getPhoneNumber: fail, authorize fail param empty!') {
                    console.log('曹鹏威');
                    //不同意授权
                    this.$emit('change', {
                        isLogin: false,
                        btnType: this.btnType,
                    });
                }
                if (e.mp.detail.errMsg === 'getPhoneNumber:ok') {
                    this.$store.commit('setMaskDialogFlag', true);
                    //同意授权
                    this.$wxLogin.registerByAuthPhoneNumber(e.mp.detail).then(
                        async res => {
                            this.$emit('change', {
                                isLogin: true,
                                btnType: this.btnType,
                                params: this.params,
                            });

                            // // 退出登录 => 重新登录 => 需要重新获取是否设置密码的状态
                            // let isPassword = await setedPayPass()
                            //       Store.commit('setIsSetPassword', isPassword.data)
                        },
                        err => {
                            this.$emit('change', {
                                isLogin: false,
                                btnType: this.btnType,
                            });
                        },
                    );
                }
                if (e.mp.detail.errno == '1400001') {
                    let loginBackParams = {
                        params: this.params,
                        btnType: this.btnType,
                    };
                    uni.navigateTo({
                        url: `/packages/setting/pages/login/main?params=${JSON.stringify(loginBackParams)}`,
                    });
                }
                // #endif
            }
        },
        // 节流防抖函数
        debounce(fn, delay) {
            let timer = null;
            return function () {
                let context = this;
                let args = arguments;
                clearTimeout(timer);
                timer = setTimeout(() => {
                    fn.apply(context, args);
                }, delay);
            };
        },
    },
    components: {
        // Login
    },
    onUnload() {
        this.loginIsShow = false;
    },
};
</script>

<style lang="scss" scoped>
.slot-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
</style>
