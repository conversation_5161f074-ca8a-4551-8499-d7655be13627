<template>
    <div class="car-number-plate" id="car-number-plate">
        <div class="car-number-plate__input" @click="show = !show">
            <div :class="['number-items', curNumberPlate.length === 0 ? 'active' : '']">{{ curNumberPlate[0] }}</div>
            <div :class="['number-items', curNumberPlate.length === 1 ? 'active' : '']">{{ curNumberPlate[1] }}</div>
            <div :class="['number-items', curNumberPlate.length === 2 ? 'active' : '']">{{ curNumberPlate[2] }}</div>
            <div :class="['number-items', curNumberPlate.length === 3 ? 'active' : '']">{{ curNumberPlate[3] }}</div>
            <div :class="['number-items', curNumberPlate.length === 4 ? 'active' : '']">{{ curNumberPlate[4] }}</div>
            <div :class="['number-items', curNumberPlate.length === 5 ? 'active' : '']">{{ curNumberPlate[5] }}</div>
            <div :class="['number-items', curNumberPlate.length === 6 ? 'active' : '']">{{ curNumberPlate[6] }}</div>
            <div :class="['number-items', curNumberPlate.length >= 7 ? 'active' : '', 'number-items-dashed']">
                <span v-if="!!curNumberPlate[7] || curNumberPlate[7] === 0">{{ curNumberPlate[7] }}</span>
                <span v-else class="dsshed-new">新能源</span>
            </div>
        </div>
        <!-- keybord -->
        <u-keyboard
            ref="uKeyboard"
            :mask="false"
            mode="car"
            @change="valChange"
            @backspace="backspace"
            @confirm="confirm"
            v-model="show"
        ></u-keyboard>
    </div>
</template>

<script>
export default {
    name: 'CarNumberPlate',
    components: {},
    props: {
        carNo: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            show: false,
            curNumberPlate: ['', '', '', '', '', '', '', ''],
        };
    },
    watch: {
        curNumberPlate: function (val, oldVal) {
            console.log(val, 'valvalvalvalval');
            if (val.length >= 1) this.isShowProvice = false;
            else this.isShowProvice = true;
            this.$emit('markCar', val.toString().replace(/,/g, ''));
            console.log(val, oldVal, 'oldValoldValoldValoldValoldVal');
        },
        carNo: function (val) {
            console.log(val, 'valvalvalvalval');
        },
    },
    onReady() {
        // 如果想一进入页面就打开键盘，请在此生命周期调用
        this.show = true;
    },
    onLoad() {
        this.curNumberPlate = this.carNo.split('');
    },
    methods: {
        // 按键被点击(点击退格键不会触发此事件)
        valChange(val) {
            console.log(val, 'FFFFFFFFFF');
            const index = this.curNumberPlate.findIndex(item => item === '');
            if (index == -1) return;
            this.curNumberPlate.splice(index, 1, val);
            console.log(this.curNumberPlate, 'this.curNumberPlate');
        },
        // 退格键被点击
        backspace() {
            if (this.curNumberPlate.length) {
                for (let index = this.curNumberPlate.length - 1; index >= 0; index--) {
                    if (this.curNumberPlate[index] !== '') {
                        // index 是表示第几个   1 是替换几个   '' 是替换成什么
                        this.curNumberPlate.splice(index, 1, '');
                        break;
                    }
                }
            }
        },
        // 点击确定将车牌号分发出去
        confirm(e) {
            // 监视了this.curNumberPlate 只要用户点击键盘就分发车牌号
        },
    },
};
</script>

<style lang="scss" scoped>
// @import '~@/common/stylus/variable.styl'
.car-number-plate {
    display: flex;
    justify-content: center;
    width: 100%;
    &__input {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .number-items {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            width: 34px;
            height: 49px;
            background: #fff7f2;
            border: 1px solid #f96702;
            border-radius: 3px;

            font-size: 18px;
            color: #333;
            position: relative;
        }
        .number-items-dashed {
            text-align: center;
            border: 1px solid #00f05f;
            background: #83f0ae;
            .dsshed-new {
                font-size: 11px;
                padding: 0 6px;
                line-height: 14px;
                color: #ffffff;
            }
        }
        // 当前号码位置
        .active {
            // &::before {
            //   content: "";
            //   position: absolute;
            //   right: 0px;
            //   bottom: 0;
            //   display: block;
            //   width: 0;
            //   height: 0;
            //   border: 3px solid;
            //   border-color: transparent $color-theme-o $color-theme-o transparent;
            // }
        }
    }
    // keybord
    &__keybord {
        position: fixed;
        bottom: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 220px;
        background-color: #ebebeb;
        .keybord-header {
            display: flex;
            justify-content: space-between;
            padding-left: 10px;
            height: 30px;
            line-height: 30px;
            text-align: right;
            background-color: #fff;

            & > span {
                margin-right: 10px;
                color: #5cacee;
                font-size: 14px;
            }
        }
        .keybord-body {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            background-color: #ebebeb;
            &__row {
                display: flex;
                flex-direction: row;
                justify-content: center;
                .row-item {
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;
                    width: 28px;
                    height: 35px;
                    margin: 4px;
                    font-size: 12px;
                    background-color: #fff;
                    border-radius: 4px;
                }
                .row-item-delete {
                    width: 46px;
                    .icon-oil {
                        font-size: 20px;
                    }
                    img {
                        width: 30px;
                        height: 20px;
                    }
                }
            }
        }
        // swipe
        .my-swipe {
            width: 100%;
            height: 100%;
            ::v-deep .van-swipe-item {
                width: 100%;
            }
            ::v-deep .van-swipe__indicators {
                top: 60%;
                left: 5px;
                .van-swipe__indicator {
                    background-color: #111;
                }
            }
        }
    }
}
</style>
