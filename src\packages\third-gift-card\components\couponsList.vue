<template>
    <div class="view fl-column comPages">
        <div class="f-1 fl-column mh-0">
            <div class="tabs_style p-LR-16 mh-0 fl-row fl-al-cen">
                <div
                    v-for="(tab, index) in tabs"
                    :key="index"
                    :class="{ 'color-E64F22': selectId == tab.id }"
                    class="tab_style"
                    @click="selectClick(tab, index)"
                >
                    <div class="font-14 weight-500">{{ tab.title }}({{ tab.quantity || 0 }})</div>
                </div>
            </div>
            <div class="line_bottom"></div>
            <div class="coupon-wrap f-1 mh-0">
                <zj-data-list
                    ref="dataList"
                    :emptyImage="noPicture"
                    :showEmpty="showEmpty"
                    background="#F7F7FB"
                    emptyText="暂无数据"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrolltolower"
                >
                    <div class="p-TB-16">
                        <div class="list-wrap" v-for="(item, index) in couponsArray" :key="index">
                            <div class="item-module">
                                <div v-if="selectId == 1">
                                    <img v-if="item.accountStatus == 1" alt="" class="card-img" src="../images/card-6.png" />
                                    <img v-if="item.accountStatus == 2" alt="" class="card-img" src="../images/card-2.png" />
                                </div>
                                <div v-if="selectId == 2" class="img-Card">
                                    <img src="../images/card-3.png" alt="" class="card-img" />
                                </div>
                                <div v-if="selectId == 3" class="img-Card">
                                    <img src="../images/card-4.png" alt="" class="card-img" />
                                </div>

                                <div class="modole-bg fl-column">
                                    <div class="fl-row fl-al-cen fl-jus-bet card-div">
                                        <div class="item-left fl-row fl-al-cen">
                                            <img
                                                v-if="item.accountStatus == 1 && selectId == 1"
                                                alt=""
                                                class="logo-img"
                                                src="../images/cnpc-logo.png"
                                            />
                                            <img
                                                v-if="item.accountStatus == 2 && selectId != 1"
                                                alt=""
                                                class="logo-img"
                                                src="../images/logo-gray.png"
                                            />
                                            <div class="font-16 color-fff marl5 weight-500">{{ item.templetName }} </div>
                                        </div>
                                        <div class="item-right fl-row fl-al-cen">
                                            <div
                                                class="bg-item name-radius fl-row fl-al-jus-cen weight-400 font-12 color-fff"
                                                :class="item.accountStatus == 1 && selectId == 1 ? 'normal' : 'out'"
                                                @click="ruleClick(item)"
                                            >
                                                使用规则
                                            </div>
                                            <div
                                                :class="item.accountStatus == 1 && selectId == 1 ? 'normal' : 'color-686868'"
                                                class="bg-item sale-radius weight-400 font-12"
                                                @click="buyClick(item)"
                                            >
                                                消费记录</div
                                            >
                                        </div>
                                    </div>
                                    <div class="fl-row fl-jus-bet padlr15 f-1">
                                        <div class="item-left fl-row fl-al-cen">
                                            <div class="weight-400 font-14 mart32 color-fff"
                                                >余额<span class="mar-6-l font-18 weight-bold">￥</span></div
                                            >
                                            <div class="font-36 weight-bold fl-row color-fff fl-al-base mart20">{{
                                                item.availableAmount
                                            }}</div>
                                        </div>
                                        <div class="item-right fl-column fl-al-jus-cen color-fff weight-400 font-14 mart32">
                                            <div
                                                v-if="item.accountStatus == 1 && selectId == 1 && !isHarmony"
                                                class="shoppingBtn fl-row fl-al-cen font-12"
                                                @click="shoppingJump(item)"
                                                >商城<div class="arroe-right-smalls mar-left-4"></div>
                                            </div>
                                            <div class="fl-row fl-al-cen">
                                                <div>面值</div>
                                                <div class="mar-6-l">&yen;{{ item.faceValue }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div :class="selectId == 1 ? 'fl-jus-bet' : 'fl-sp-end'" class="fl-row bottom-area normal padlr15">
                                        <div
                                            v-if="selectId == 1"
                                            :class="item.accountStatus == 1 ? 'colorNormal' : 'color-rgba5'"
                                            class="font-16"
                                            >{{ item.accountStatus == 1 ? '正常' : '冻结' }}</div
                                        >
                                        <div
                                            :class="item.accountStatus == 1 && selectId == 1 ? 'colorNormal' : 'color-rgba5'"
                                            class="font-14"
                                        >
                                            {{ formatTimeFun(item.effectTime) }}-{{ formatTimeFun(item.expireTime) }}</div
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
        </div>
        <custom-popup :maskClick="false" ref="rulePopup" type="bottom">
            <div class="model-div fl-column" style="height: 337px">
                <div class="fl-row fl-jus-bet fl-al-cen marlr24">
                    <div class="font-17 weight-500 color-333">使用规则</div>
                    <div class="close" @click.stop="closeRulePop">
                        <img src="../images/close.png" alt />
                    </div>
                </div>
                <div class="f-1 mh-0">
                    <div class="card-default fl-column padtb16 marlr12" v-if="ruleDescribe.length > 0">
                        <div v-for="(item, index) in ruleDescribe" :key="index">
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
        </custom-popup>
        <custom-popup :maskClick="false" ref="cardPopup" type="bottom">
            <div class="model-div fl-column" style="height: 337px">
                <div class="fl-row fl-jus-bet fl-al-cen marlr24">
                    <div class="font-17 weight-500 color-333">消费记录</div>
                    <div class="close" @click.stop="closepop">
                        <img src="../images/close.png" alt />
                    </div>
                </div>
                <div class="f-1 mh-0">
                    <zj-data-list
                        ref="dataListConsume"
                        :showEmpty="showEmptyConsume"
                        background="#F7F7FB"
                        emptyText="暂无数据"
                        @refreshPullDown="refreshPullDownConsume"
                        @scrolltolower="scrolltolowerConsume"
                    >
                        <div v-for="(item, index) in consumeList" :key="index" class="card-default fl-column padtb16 marlr12">
                            <div class="fl-row fl-jus-bet">
                                <div class="font-14 color-333 weight-500 fl-row f-1">
                                    <div class="marr5">订单{{ item.orderNo }}</div>
                                    <div class="copy-div" @click.stop="handleCopy(item.orderNo)">复制</div>
                                </div>
                                <div class="font-14 color-E64F22 weight-500">{{ item.payAmount }}</div>
                            </div>
                            <div class="fl-row fl-jus-bet weight-400 mart5">
                                <div class="font-12 color-999">{{ item.payTime }}</div>
                                <div class="font-12 color-999">余&yen;{{ item.balance }}</div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
        </custom-popup>
        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <div class="content">
                        <div class="font-14 color-333 weight-bold">已提交申请延期，24小时内完成审核 审核成功后您可以继续使用该能源锦鲤</div>
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickBtn"
                        >确定
                    </view>
                </view>
            </div>
        </custom-popup>
    </div>
</template>
<script>
// 一次请求多少条
const PAGE_SIZE = 20;
import {
    welfareCardsGetCount,
    welfareCardsQueryList,
    giftCardConsumeList,
    buyGiftCardNumQuery,
} from '../../../s-kit/js/v3-http/https3/giftCard/index.js';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            /**
             *
             * 使用状态：
                1—未用完；
                2—已用完；
                3—已过期；
             * */
            tabs: [
                { id: 1, title: '未用完', quantity: '' },
                { id: 2, title: '已用完', quantity: '' },
                { id: 3, title: '已过期', quantity: '' },
            ],
            // 使用状态
            selectId: '1',
            // 页码
            pageNum: 1,
            // 消费页码
            page: 1,
            // 总页码
            totalPage: 0,
            // 礼品券列表
            couponsArray: [],
            // 是否展示空态标识
            showEmpty: false,
            // 暂无礼品券图片
            noPicture: require('../images/lpk-no-data.png'),
            // 消费列表为空
            showEmptyConsume: false,
            // 消费记录列表
            consumeList: [],
            // 消费总页码
            totalPageConsume: 0,
            // 使用规则
            ruleDescribe: '',
        };
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        /**
         * 页面加载
         */
        pageLoad(isInitcard) {
            this.getGiftCouponsArrayList({ isInitcard: isInitcard });
            this.queryNmuGift();
        },
        // 查询用户名下能源锦鲤数量接口
        async queryNmuGift() {
            let res = await welfareCardsGetCount({}, { isCustomErr: true });
            // availableCount	可用张数
            // usedCount	已用完张数
            // expirationCount	已过期张数
            if (res.success) {
                let quantityData = {
                    1: res.data.availableCount,
                    2: res.data.usedCount,
                    3: res.data.expirationCount,
                    // 其他需要赋值的数据
                };
                // 使用循环遍历对象，将值赋给对应的属性
                for (let key in quantityData) {
                    // 这里使用parseInt是为了解决for..in..循环中数字类型的key变为字符串的问题，这里也可以不使用严格判断来解决此处的问题
                    let tab = this.tabs.find(tab => tab.id === parseInt(key));
                    if (tab !== undefined) {
                        tab.quantity = quantityData[key];
                    }
                }
            }
        },
        /**
         * @description  : 点击tab页签，展示对应页签下的页面
         * @return        {*}
         */
        selectClick(tab, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.selectId === tab.id) return;
            // 将选中的id赋值在页面上高亮展示
            this.selectId = tab.id;
            this.getGiftCouponsArrayList({ isInitcard: true });
        },
        /**
         * @description  : 上拉加载能源锦鲤列表
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getGiftCouponsArrayList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            this.$refs.dataList.loadStatus = 'loading';
            this.pageLoad(true);
        },
        /**
         * @description  : 上拉加载能源锦鲤消费记录列表
         * @return        {*}
         */
        scrolltolowerConsume() {
            if (this.$refs.dataListConsume.loadStatus == 'contentdown') {
                this.$refs.dataListConsume.loadStatus = 'loading';
                this.giftCouponsConsumeListPost();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDownConsume(e) {
            this.$refs.dataListConsume.loadStatus = 'loading';
            this.giftCouponsConsumeListPost({ isInit: true });
        },
        // 查询用户名下能源锦鲤列表接口
        async getGiftCouponsArrayList({ isInitcard = false } = {}) {
            if (isInitcard) {
                // 重置入参页码
                Object.assign(this, {
                    couponsArray: [],
                    pageNum: 1,
                });
            }
            let { pageNum, couponsArray, totalPage, selectId } = this;

            /***
             * 使用状态：
                1—未用完(含正常和冻结)；
                2—已用完；
                3—已过期；
             * */
            let params = {
                pageNum: pageNum,
                pageSize: PAGE_SIZE,
                usedStatus: Number(selectId),
            };
            let res = await welfareCardsQueryList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.welfareCardList || [];

                // 将处理好的数组合并到定义的数组，放到页面渲染
                couponsArray = couponsArray.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    couponsArray,
                    pageNum: Number(pageNum) + 1,
                });
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && pageNum >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = couponsArray.length <= 0 ? true : false;
            }
        },
        // 查看能源锦鲤消费列表接口
        async giftCouponsConsumeListPost({ isInit = false } = {}, item) {
            if (isInit) {
                // 重置入参页码
                Object.assign(this, {
                    consumeList: [],
                    page: 1,
                });
            }
            let { page, consumeList, totalPageConsume } = this;
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
                giftCardNo: item.giftCardAsn,
            };
            let res = await giftCardConsumeList(params, { isCustomErr: true });
            if (res.success) {
                this.$refs.dataListConsume.stopRefresh();
                this.$refs.dataListConsume.pullDownHeight = 0;
                this.$refs.dataListConsume.pullingDown = false;
                let list = res.data.rows || [];
                // 将处理好的数组合并到定义的数组，放到页面渲染
                consumeList = consumeList.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    consumeList,
                    page: Number(page) + 1,
                });
                // 返回总条数
                totalPageConsume = res.data.pageSum;
                if (res.data && page >= totalPageConsume) {
                    // 没有更多了
                    this.$refs.dataListConsume.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataListConsume.loadStatus = 'contentdown';
                }
                this.showEmptyConsume = consumeList.length <= 0 ? true : false;
            }
        },
        /**
         * @description  : 复制传入的内容
         * @param         {*} value:被复制的内容
         * @return        {*}
         */
        handleCopy(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        // 处理时间只展示年月日
        formatTimeFun(time) {
            if (!time) {
                return;
            }
            let date = time.split(' ')[0];
            date = date.replace(/\-/g, '.');
            return date;
        },
        // 打开使用规则
        ruleClick(item) {
            if (typeof item.useManual == 'string') {
                this.ruleDescribe = item.useManual.split(/[(\r\n)\r\n]+/);
            }
            this.$refs.rulePopup.open();
        },
        // 关闭使用规则
        closeRulePop() {
            this.$refs.rulePopup.close();
        },
        // 打开消费记录
        buyClick(item) {
            this.$refs.cardPopup.open();
            this.giftCouponsConsumeListPost({ isInit: true }, item);
        },
        // 关闭消费记录
        closepop() {
            this.$refs.cardPopup.close();
        },
        // 申请延期
        extensionApply() {
            this.$refs.popDialogFlag.open();
        },
        //关闭弹窗
        clickBtn() {
            this.$refs.popDialogFlag.close();
        },
        // 查看待领取能源锦鲤
        unclaimedHandle() {
            let url = '/packages/third-gift-card/pages/coupons-unclaimed-list/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 商城跳转
        shoppingJump(item) {
            let params = {
                protocolCode: item.protocolNo, // 协议编号
                cityCode: item.usedPlace,
            };
            let url = `/pages/home/<USER>
            // 能源锦鲤商城小程序ID: 3534654609902342
            this.$sKit.layer.cubeMini(url, '3534654609902342');
        },
    },
};
</script>
<style scoped lang="scss">
.comPages {
    height: 100%;

    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        justify-content: space-between;

        .tab_style {
            // margin-right: 25px;
            line-height: 20px;
        }

        .selected {
            color: #e64f22;
        }

        .btn_style {
            padding: 11rpx 22rpx;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #ff6b2c;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #ff6b2c;
        }
    }

    .coupon-wrap {
        .list-wrap {
            position: relative;
            width: 345px;
            margin: 0 auto 15px;

            .item-module {
                position: relative;
                width: 100%;
                height: 175px;
                overflow: hidden;

                .img-Card {
                    position: relative;

                    .card-img {
                        width: 345px;
                        height: 175px;
                    }

                    .card-img2 {
                        position: absolute;
                        left: 15px;
                        bottom: 10px;
                        width: 74px;
                        height: 46px;
                    }
                }

                .card-img {
                    width: 345px;
                    height: 175px;
                }

                .modole-bg {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;

                    .card-div {
                        padding: 15px 15px 0;
                    }

                    .bottom-area {
                        width: 100%;
                        height: 40px;
                        line-height: 40px;

                        .colorNormal {
                            color: #b620e0;
                        }

                        .extension-btn {
                            width: 60px;
                            height: 25px;
                            line-height: 25px;
                            border-radius: 8px;
                            align-self: center;
                            margin-left: 10px;
                        }
                    }

                    .item-left {
                        flex: 1;

                        .logo-img {
                            width: 24px;
                            height: 23px;
                            flex-shrink: 0;
                            /*防止被压缩*/
                        }
                    }

                    .item-right {
                        .bg-item {
                            min-width: 70px;
                            height: 25px;
                            line-height: 25px;
                            text-align: center;
                            box-sizing: border-box;
                        }

                        .name-radius {
                            padding: 0 7px;

                            border-radius: 4px 0px 0px 4px;

                            &.normal {
                                background: #9022e6;
                            }

                            &.out {
                                background: #838282;
                            }
                        }

                        .sale-radius {
                            padding: 0 6px;
                            border-radius: 0px 4px 4px 0px;
                            background: rgba(255, 255, 255, 0.7);
                            backdrop-filter: blur(50px);

                            &.normal {
                                color: #b620e0;
                            }
                        }
                    }
                }

                .shoppingBtn {
                    padding: 5px 10px;
                    background: #ae69f6;
                    border-radius: 20px;

                    .arroe-right-smalls {
                        box-sizing: border-box;
                        width: 6px;
                        height: 6px;
                        border: solid #fff;
                        border-width: 0 1.5px 1.5px 0;
                        transform: rotate(-45deg);
                    }
                }
            }
        }
    }

    .confirmBtn {
        margin-top: 12px;
        width: 100%;
        height: 44px;
    }
    .marl5 {
        margin-left: 5px;
    }

    .marr10 {
        margin-right: 10px;
    }

    .padlr15 {
        padding: 0 15px;
    }

    .mart32 {
        margin-top: 32px;
    }

    .mart20 {
        margin-top: 20px;
    }

    .height {
        height: 57px;
        line-height: 25px;
    }
}
.model-div {
    background: #f6f6f6;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 16px;

    .marlr24 {
        margin: 0 16px 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .marr5 {
        margin-right: 5px;
    }

    .marlr12 {
        margin: 0 12px 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
        padding-left: 10px;
        padding-right: 10px;
    }

    .mart5 {
        margin-top: 5px;
    }

    .copy-div {
        width: 29px;
        padding: 3px 0;
        height: 16px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #999999;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        text-align: center;
        box-sizing: border-box;
        overflow: hidden;
        flex-shrink: 0;
        line-height: 0.8;
    }

    .close {
        padding: 20px;

        img {
            width: 13px;
            height: 13px;
        }
    }
}

._modal {
    flex: none;
    width: 280px;
    min-height: 104px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 19px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 34px;
            text-align: center;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}
</style>
