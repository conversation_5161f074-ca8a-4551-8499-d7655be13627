<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :custom-back="clickCustomBackBtn"
            back-text="订单选择"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <!-- 主页面 -->
        <scroll-view
            scroll-y
            class="content-view f1"
            refresher-enabled
            refresher-default-style="none"
            :refresher-triggered="isStartDown"
            @refresherrefresh="onRefreash"
        >
            <!-- 下拉刷新动画 -->
            <div slot="refresher" class="refresh-container">
                <image class="refresher-load" src="@/static/loading.png"></image>
                <div class="refresher-text">努力刷新中 ···</div>
            </div>
            <div class="scroll-content-view">
                <div class="title">{{ stationName }}</div>
                <div class="choice-order bg-style">
                    <div class="tag">订单选择</div>
                    <div
                        class="order-list"
                        :class="{
                            'more-list': showMore,
                            'padd-btm': orderList.length > 2,
                            'less-list': orderListLength,
                        }"
                    >
                        <div
                            class="item flex"
                            :class="{ active: index == orderIndex }"
                            v-for="(item, index) in orderList"
                            :key="index"
                            @click="choiceOrder(index)"
                        >
                            <div class="left f1">
                                <div>{{ item.quantity }}</div>
                                <div>{{ showOilType }} {{ item.oilGun }}</div>
                            </div>
                            <div class="center"><span>￥</span>{{ item.amount }}</div>
                            <div class="right">
                                <img v-if="orderIndex == index" src="@/static/images/select-ac.png" alt="" />
                                <img v-else src="@/static/images/select-no.png" alt="" />
                            </div>
                        </div>
                        <div class="show-more" v-if="orderList.length > 2" @click="toShowMore">
                            <u-icon :name="showMore ? 'arrow-up' : 'arrow-down'"></u-icon>
                        </div>
                    </div>
                </div>
                <div class="pay-ways bg-style">
                    <div class="tag">支付方式</div>
                    <div class="cel-item oil-card flex space-btn border-btm align-center" @click.self="chiosePayWay(1)">
                        <div class="left flex">
                            <img src="@/static/cnpc-logo.png" alt="" />
                            <div
                                v-if="cardList.length > 0 && cardDeatilList.length > 0 && selectMarker.cardPay"
                                class="detail"
                                @click.stop="clickChoiseOilCard(cardDeatilList[selectOilCardIndex].balance)"
                            >
                                {{ cardDeatilList[selectOilCardIndex].cardType == 0 ? '实体加油卡' : '电子加油卡' }}
                                ({{
                                    cardDeatilList[selectOilCardIndex].afterNum === undefined
                                        ? ''
                                        : cardDeatilList[selectOilCardIndex].afterNum
                                }})
                                <img src="@/static/homeIcon/rightjt.png" alt="" />
                                <div
                                    :style="{
                                        color:
                                            Number(cardDeatilList[selectOilCardIndex].balance) < Number(orderList[orderIndex].amount)
                                                ? '#e7a16f'
                                                : '#767475',
                                    }"
                                >
                                    {{ amountIs }}
                                    {{
                                        cardDeatilList[selectOilCardIndex].balance === undefined
                                            ? ''
                                            : cardDeatilList[selectOilCardIndex].balance
                                    }}
                                </div>
                            </div>
                            <div class="detail" v-else>
                                {{ selectMarker.cardPay ? '暂无油卡' : '该油站暂不支持油卡移动支付' }}
                            </div>
                        </div>
                        <div class="right" v-if="cardDeatilList.length > 0 && selectMarker.cardPay">
                            <img src="@/static/images/type-checked.png" v-if="selectPayIndex == 1" alt="" />
                            <img src="@/static/images/type-unchecked.png" v-else alt="" />
                        </div>
                    </div>
                    <div class="cel-item wechat-pay flex space-btn border-btm align-center" @click="chiosePayWay(0)">
                        <div class="left">
                            <img src="@/static/pay-wx.png" alt="" />
                            微信支付
                        </div>
                        <div class="right">
                            <img v-if="selectPayIndex == 0 || selectMarker.cardPay == 0" src="@/static/images/type-checked.png" alt="" />
                            <img src="@/static/images/type-unchecked.png" v-else alt="" />
                        </div>
                    </div>
                    <div
                        class="cel-item count-pay total-price flex space-btn align-center"
                        @click="clickOpenSelectCoupon"
                        v-if="selectCouponTip !== '暂无可用'"
                    >
                        <div class="left flex">
                            <div>券</div>
                            优惠券
                        </div>
                        <div class="right flex align-center">
                            {{ selectCouponTip }}
                            <!-- <div class="elticket_text_class">{{ selectCouponTip }}</div> -->
                            <img src="@/static/homeIcon/rightjt.png" alt="" />
                        </div>
                    </div>
                    <div v-else class="cel-item count-pay total-price flex space-btn align-center">
                        <div class="left flex">
                            <div>券</div>
                            优惠券
                        </div>
                        <div class="right flex align-center">
                            {{ selectCouponTip }}
                        </div>
                    </div>
                </div>
            </div>
        </scroll-view>
        <!-- 下方功能区 -->
        <div v-if="!isNeedSaveMoney" class="btn-view flex align-center">
            <div class="left f1">
                <div class="flex">
                    实付: <span>￥</span>
                    <div>{{ calPrice }}</div>
                </div>
                <div class="discount"> 已优惠 ￥{{ selectCoupon && selectCoupon.faceValue ? selectCoupon.faceValue : '0.00' }} </div>
            </div>
            <div class="right" v-if="!isGray" @click="clickPayBtn"> 立即支付 </div>
            <div class="buttonGrayedOut" v-else> 立即支付 </div>
        </div>
        <div v-if="isNeedSaveMoney" class="btn-view flex align-center">
            <div class="right f1" style="padding: 28rpx 0" @click="toSaveMoney"> 立即充值 </div>
        </div>
        <!-- 支付失败弹窗 -->
        <uni-pop ref="failpopup" type="center" :maskClick="false">
            <div class="suc-pop-view">
                <div class="suc-pop-content">
                    <image class="suc-pop-icon" src="@/static/homeIcon/failPay.png"></image>
                    <div class="suc-pop-title">支付失败</div>
                </div>
                <u-icon
                    @click="clickClosefailPop"
                    name="close-circle-fill"
                    :custom-style="{
                        marginTop: '40rpx',
                    }"
                    color="#ffffff"
                    size="92"
                ></u-icon>
            </div>
        </uni-pop>
        <!-- 油卡选择弹窗 -->
        <u-popup v-model="isShowOilCard" mode="bottom" border-radius="20" safe-area-inset-bottom>
            <div class="pop-view">
                <div class="pop-title-view">
                    <div class="pop-title-text">请选择油卡</div>
                    <div class="close-btn" @click="clickCloseOilCard">
                        <u-icon class="pop-close-btn" size="22" name="close" color="#979797"></u-icon>
                    </div>
                </div>
                <scroll-view class="coupon-scroll-view" :scroll-y="true" :enhanced="true" :show-scrollbar="false">
                    <oil-card-list :datalist="cardDeatilList" @click="clickCardItem" :index="selectOilCardIndex"></oil-card-list>
                </scroll-view>
            </div>
        </u-popup>
        <div class="mask" v-show="isShow"></div>
        <div class="showPassword">
            <div class="payment-box" v-show="isShow">
                <image @click="handleClosePasswordBox" class="payment-box-close" src="@/static/black-close.png" />
                <div class="payment-box-title">
                    <span class="payment-title-text">请输入支付密码</span>
                </div>
                <div class="payment-box-content">
                    <div class="payment-content-amount">
                        <span class="payment-amount-sign">¥</span>
                        <span class="payment-amount-text">{{ calPrice }}</span>
                    </div>
                </div>
                <div class="payment-box-footer">
                    <u-message-input
                        ref="uMessageInputRef"
                        :maxlength="maxlength"
                        :width="100"
                        :dot-fill="true"
                        :breathe="false"
                        :disabled-keyboard="true"
                        :value="showPassword"
                    ></u-message-input>
                </div>
            </div>
        </div>
        <safe-password id="passwordKeyboardId" title="安全键盘" v-show="isShow"></safe-password>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import uniPop from '@/components/uni-popup/uni-popup.vue';
import paymentPassword from '@/components/payment-password/payment-password.vue';
import {
    cardPreOrder,
    wxPreOrder,
    getUnusedList,
    getCardDetails,
    cardListPost,
    cancleOrder,
    refund,
    waitPayOrder,
    cardPay,
    payAuthentication,
    getUseableList,
    lockOrder,
    getTradeList,
    getBestCoupon,
} from '@/api/home.js';
import couponList from '@/components/coupon-list/coupon-list.vue';
import oilCardList from '@/components/oil-card-list/oil-card-list.vue';
import { mapState, mapGetters } from 'vuex';

const decimalSubtract = (a, b) => ((a * 1000 - b * 1000) / 1000).toFixed(2);
const minPayAmount = 0.01; // 最小支付金额
const payModeWX = 'wx';
const payModeOilCard = 'oilCard';
export default {
    components: {
        couponList,
        uniPop,
        oilCardList,
        paymentPassword,
    },
    computed: {
        ...mapGetters(['isPassword', 'cardList', 'openId', 'registerLoginInformation']),
        ...mapState({
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            source: state => state.source, // 上海小程序跳转过来的标识
            paymentFlag: state => state.paymentFlag, // 防止重复获取上海小程序标识
            lat: state => state.location.lat, // 纬度
            lon: state => state.location.lon, // 经度
            province: state => state.location.province, // 省份
            city: state => state.location.city, // 城市
        }),
        // 实付金额
        calPrice() {
            let { selectCoupon, orderIndex, orderList } = this;
            if (orderList[orderIndex] && orderList[orderIndex].amount) {
                const originAmount = orderList[orderIndex].amount;
                // const originAmount = "2"
                if (JSON.stringify(selectCoupon) != '{}') {
                    const couponAmount = selectCoupon.faceValue;
                    return Math.max(decimalSubtract(originAmount, couponAmount), minPayAmount);
                }
                // 3.999999999
                let decimalPoint = originAmount.split('.')[1];
                // var re = /([0-9]+\.[0-9]{2})[0-9]*/;
                // let decimalPoint = originAmount.replace(re,"$1")
                if (!decimalPoint) {
                    return originAmount + '.00';
                }
                return originAmount;
            } else {
                return '0';
            }
        },
        selectCouponTip() {
            let { selectCoupon } = this;
            return JSON.stringify(selectCoupon) == '{}' ? (this.notInUseCoupon ? '暂不使用' : '暂无可用') : '-￥' + selectCoupon.faceValue;
        },
        showOilType() {
            console.log(this.ypCode, 'this.ypCode');
            let ypCode = this.ypCode;
            if (ypCode.includes('92') || ypCode.includes('95') || ypCode.includes('98')) {
                this.ypName = this.ypCode + ' 汽油';
                return this.ypCode + ' 汽油';
            } else {
                this.ypName = this.ypCode + ' 柴油';
                return '柴油';
            }
        },
        // 判断当前油卡是否需要充值
        isNeedSaveMoney() {
            let { selectPayIndex, cardList, selectOilCardIndex, calPrice, cardDeatilList } = this;
            // 判断！== 0 是为了解决控制台报错
            if (cardDeatilList.length !== 0) {
                if (selectPayIndex == 1) {
                    if (Number(cardDeatilList[selectOilCardIndex].balance) >= Number(calPrice)) {
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    return false;
                }
            }
        },
        orderListLength() {
            return this.orderList.length <= 2;
        },
        amountIs() {
            if (this.cardDeatilList.length > 0 && this.cardDeatilList[this.selectOilCardIndex].balance !== undefined) {
                return Number(this.cardDeatilList[this.selectOilCardIndex].balance) < Number(this.orderList[this.orderIndex].amount)
                    ? '余额不足:'
                    : '余额:';
            } else {
                return '';
            }
        },
    },
    data() {
        return {
            maxlength: 6,
            showPassword: '',
            pageConfig: pageConfig,
            showMore: false,
            stationName: '',
            ypCode: '',
            carCode: '',
            orderList: [], // 订单列表
            orderIndex: 0,
            amount: '',
            isShowOilCard: false, // 是否展示油卡选择弹框
            couponArr: [], // 优惠券列表
            // cardList: [], // 油卡列表
            selectOilCardIndex: 0, // 选中的油卡
            selectPayIndex: 1, // 选中的支付方式 默认微信支付
            selectCoupon: {},
            isShowPasswordBox: false, // 是否显示自定义密码输入弹窗
            isShowPaymentPasswordErrorBox: false, // 是否显示支付密码输入错误弹窗
            notInUseCoupon: false, // 暂不使用优惠券
            oilCheckCode: '',
            stationInfo: {},
            isStartDown: false, // 是否开始刷新
            cardDeatilList: [],
            password: '',
            isShow: false,
            userInfo: '',
            clearOrderNo: '',
            cancleAmount: '', // 退券
            evoucherOrderNo: '', // 电子券订单号
            isGray: false, // 立即支付按钮是否置灰
            ypName: '',
            riskControlParameters: null,
            wxOrderNo: '',
        };
    },
    onLoad(option) {
        console.log(this.evoucherOrderNo, '进入页面时电子券订单号');
        this.selectMarker.cardPay ? (this.selectPayIndex = 1) : (this.selectPayIndex = 0);
        // 初始化键盘
        this.selectComponent('#passwordKeyboardId').init(this.openId, 'number');
        this.orderList = JSON.parse(option.orderdata); // 订单详情数据数组
        this.stationInfo = JSON.parse(option.stationInfo);
        if (this.orderList.length > 2) {
            this.showMore = false;
        }
        this.stationName = option.stationName;
        this.ypCode = option.ypCode;
        // this.carCode = option.carCode;
        this.loadOilCardList();
        this.getCanUseCoupon();
        uni.$on('choseUnUsedCoupon', params => {
            if (typeof params == 'boolean') {
                this.notInUseCoupon = true;
                this.selectCoupon = {};
            } else {
                // console.log("selectCoupon", params);
                this.selectCoupon = params;
            }
        });
        this.clickPayBtn = this.$util.throttleUtil(this.clickPayBtn);
    },
    async onUnload() {
        // console.log('点击左上角返回，或者是左滑右滑撤销电子券');
        if (this.clearOrderNo) {
            // 取消订单
            await cancleOrder({ orderNo: this.clearOrderNo });
            // // 退券
            // await refund({ amount:this.cancleAmount  });
        }
        uni.$off('choseUnUsedCoupon');
    },
    methods: {
        //密码键盘显示与操作
        showKeyboard() {
            this.selectComponent('#passwordKeyboardId').openKeyboard(
                'password_unique1',
                6,
                pwd => {
                    this.showPassword = pwd;
                    this.password = this.selectComponent('#passwordKeyboardId').getCipherPWD('password_unique1');
                },
                () => {
                    const cardPayInfo = (this._cardPayRes || {}).data;
                    if (this.showPassword.length === 6) {
                        this.inputComplete(cardPayInfo);
                    } else {
                        this.$util.showModal('请输入完整的密码', true);
                        this.isGray = false;
                    }
                    this.isShow = false;
                },
            );
            //  this.isShow = false;
        },
        // 立即支付 --- 输入密码后
        async inputComplete(cardPayInfo) {
            try {
                let param = {
                    idType: cardPayInfo.idType, //证件类型
                    idNum: cardPayInfo.idNum, //证件号
                    cardNo: cardPayInfo.cardNo,
                    orderNo: cardPayInfo.orderNo, //订单号
                    timestamp: Date.now(), //时间戳
                    nonce: this.$util.generateUUID(), //随机字串
                    checkCode: this.oilCheckCode,
                    payPassword: this.password,
                    shChannel: this.paymentFlag ? this.source : '',
                    jsonData: JSON.stringify(this.riskControlParameters),
                };
                console.log(param, '油卡支付风控事后参数');
                console.log(param.shChannel, '上海小程序跳转过来的标识');
                console.log(this.paymentFlag, '支付的标识--true 代表要传上海标识,false是不需要传');
                let { selectOilCardIndex, cardDeatilList } = this;
                let params = {
                    idName: cardDeatilList[selectOilCardIndex].userName,
                    cardNo: cardDeatilList[selectOilCardIndex].cardNo,
                    cardType: cardDeatilList[selectOilCardIndex].cardType,
                    idType: cardDeatilList[selectOilCardIndex].idType,
                };
                // cardPay
                let { status, info, errorCode } = await cardPay(param);
                if (status === 0) {
                    // 验证成功
                    this.handlePaySuccess(this._cardPayRes);
                } else {
                    this.handlePayFail(payModeOilCard);
                    if (errorCode == 53100025) {
                        // 未设置密码
                        wx.showModal({
                            title: '提示',
                            content: `您尚未设置油卡(${cardDeatilList[selectOilCardIndex].afterNum})的移动支付密码，请设置。`,
                            confirmText: '前往设置',
                            confirmColor: '#FF8200',
                            showCancel: false,
                            success: function (res) {
                                if (res.confirm) {
                                    uni.navigateTo({
                                        url: `/packages/password/pages/reset-password/main?params=${JSON.stringify(params)}&id=${0}`,
                                    });
                                } else if (res.cancel) {
                                    // 目前什么也不做处理
                                }
                            },
                        });
                    } else if (errorCode == 60000008) {
                        // 密码错误
                        this.isGray = false;
                        this.$util.showModal(info, true).then(() => {});
                    } else if (errorCode == 53000027) {
                        //密码错误超限
                        this.isGray = false;
                        this.$util.showModal(info, true).then(() => {});
                    } else if (errorCode == 88800101) {
                        this.isGray = false;
                        //密码连续错误，引导重置密码
                        wx.showModal({
                            title: '密码错误',
                            content: `请重新输入移动支付密码，该密码与能源e站App支付密码一致！您有5次验密机会，输错5次该油卡当天内将无法使用！是否选择重置支付密码？`,
                            confirmText: '前往重置',
                            cancelText: '继续输入',
                            confirmColor: '#FF8200',
                            showCancel: true,
                            success: function (res) {
                                if (res.confirm) {
                                    uni.navigateTo({
                                        url: `/packages/password/pages/reset-password/main?params=${JSON.stringify(params)}&id=${1}`,
                                    });
                                } else if (res.cancel) {
                                    // 目前什么也不做处理
                                }
                            },
                        });
                    } else if (errorCode == 60000041) {
                        this.isGray = false;
                        wx.showModal({
                            title: '提示',
                            content: info,
                            confirmText: '确定',
                            confirmColor: '#FF8200',
                            showCancel: true,
                            success: res => {
                                if (res.confirm) {
                                    uni.redirectTo({
                                        url: '/pages/home/<USER>',
                                    });
                                } else if (res.cancel) {
                                    // 目前什么也不做处理
                                }
                            },
                        });
                    } else {
                        this.isGray = false;
                        this.$util.showModal(info, true).then(() => {
                            uni.navigateBack({
                                delta: 1,
                            });
                        });
                    }
                }
                this.isShow = false;
                this.password = '';
                this.showPassword = '';
            } catch (err) {
                // console.warn("inputComplete => err", err);
                // this.isShow = false;
            }
        },
        async pullRefresh() {
            this.initData();
            await this.reGetOrderList();
            await this.loadOilCardList();
            await this.getCanUseCoupon();
        },
        // 下拉刷新
        async onRefreash() {
            this.isStartDown = true;
            await this.pullRefresh();
            this.isStartDown = false;
        },
        // 初始化
        initData() {
            this.orderIndex = 0;
            this.isShowOilCard = false;
            this.couponArr = [];
            this.selectOilCardIndex = 0;
            this.selectCoupon = {};
            this.isShowPasswordBox = false;
            this.isShowPaymentPasswordErrorBox = false;
            this.notInUseCoupon = false;
        },
        // 下拉刷新订单  重新获取
        async reGetOrderList() {
            try {
                let params = this.stationInfo;
                let { data } = await getTradeList(params);
                if (data.length > 0) {
                    this.orderList = data;
                    if (this.orderList.length > 2) {
                        this.showMore = false;
                    }
                } else {
                    await this.$util.showModal('未查询到订单', true);
                    uni.navigateBack();
                }
            } catch (error) {
                uni.hideLoading();
            }
        },
        // 获取可用优惠券
        async getCanUseCoupon() {
            wx.showLoading({
                title: '加载中',
                mask: true,
            });
            try {
                let res = await getBestCoupon({
                    orderNo: this.orderList[this.orderIndex].orderNo,
                    payMode: this.selectPayIndex == 1 && this.cardList.length > 0 ? 'oilCard' : 'wx',
                });
                wx.hideLoading();
                if (res.data == '') {
                    this.selectCoupon = {};

                    // uni.showModal({
                    //   title: "提示",
                    //   content: res.data.info,
                    //   showCancel: false,
                    //   success: function(res) {
                    //     if (res.confirm) {

                    //     } else if (res.cancel) {
                    //                     // 目前什么也不做处理
                    //     }
                    //   },
                    // });
                } else {
                    wx.hideLoading();
                    this.selectCoupon = res.data;
                }
                if (res.status == -1) {
                    wx.hideLoading();
                }
            } catch (error) {
                wx.hideLoading();
            }
        },
        // 查询油卡
        async loadOilCardList() {
            let oilCardRes = await cardListPost();
            for (let i = 0; i < oilCardRes.data.length; i++) {
                let oilCard = await getCardDetails({
                    cardNo: oilCardRes.data[i].cardNo,
                });
                Object.assign(oilCardRes.data[i], oilCard.data);
                // oilCardRes.data[i] = oilCard.data;
                oilCardRes.data[i].afterNum = oilCard.data.cardNo.substring(oilCard.data.cardNo.length - 4, oilCard.data.cardNo.length);
            }
            this.cardDeatilList = oilCardRes.data;
            // 如果有油卡的话 则默认支付方式为油卡支付
            if (Array.isArray(this.cardList) && this.cardList.length > 0) {
                this.selectPayIndex = 1;
                /**
                 * index -1 没有选中
                 * priority 优先级
                 *  1级 余额充足且是默认卡
                 *  2级 余额充足且是电子卡
                 *  3级 余额充足且是实体卡
                 *  4级 余额不足且是默认卡
                 *  5级 余额不足且是电子卡
                 *  6级 余额不足且是实体卡
                 */
                let priority = 7;
                let selectIndex = -1;
                for (let i = 0; i < this.cardList.length; i++) {
                    const element = this.cardList[i];
                    // 余额充足
                    // if(Number(element.balance) >= Number(this.calPrice)) {
                    if (element.isDefaultCard) {
                        selectIndex = i;
                        break;
                    } else if (element.cardType == 1) {
                        priority = 2;
                        selectIndex = i;
                    } else {
                        if (priority > 3) {
                            priority = 3;
                            selectIndex = i;
                        }
                    }
                    // }
                }
                this.selectOilCardIndex = selectIndex;
            } else {
                this.selectPayIndex = 0;
            }
        },
        toShowMore() {
            this.showMore = !this.showMore;
        },

        // 订单选择
        choiceOrder(idx) {
            this.orderIndex = idx;
            this.getCanUseCoupon();
        },

        // 点击选择油卡
        clickChoiseOilCard(item) {
            this.isShowOilCard = true;
            // this.getCanUseCoupon();
        },
        // 油卡选择点击
        clickCardItem(index) {
            if (this.selectOilCardIndex == index) return;
            this.selectOilCardIndex = index;
            this.isShowOilCard = false;
            // this.getCanUseCoupon();
        },
        // 取消油卡选择
        clickCloseOilCard() {
            this.isShowOilCard = false;
        },

        // 选择支付方式
        chiosePayWay(idx) {
            console.log(idx, this.selectPayIndex, '11111');
            // 如果当前不支持油卡支付
            if (!this.selectMarker.cardPay) return;
            if (idx == 1 && this.cardList.length == 0) return;
            if (idx == this.selectPayIndex) return;
            this.selectPayIndex = idx;
            this.getCanUseCoupon();
        },

        // 去选择优惠券
        clickOpenSelectCoupon() {
            uni.navigateTo({
                url: `/packages/place-order/pages/choice-coupon/main?orderNo=${this.orderList[this.orderIndex].orderNo}&payMode=${
                    [payModeWX, payModeOilCard][this.selectPayIndex]
                }`,
            });
        },
        // 关闭密码框
        handleClosePasswordBox() {
            this.isShow = false;
            // console.log("关闭密码框 => handleClosePasswordBox");
            this.handlePayFail(payModeOilCard);
        },
        // 重新输入支付密码
        handleReEnterPaymentPassword() {
            this.isShowPasswordBox = true;
        },
        // 忘记支付密码
        handleForgetPaymentPassword() {
            uni.navigateTo({
                url: '/packages/password/pages/edit-password/main?isfor=1',
            });
        },

        // 立即支付点击事件
        async clickPayBtn() {
            // 立即按钮是否置灰标识
            this.isGray = true;
            const oilGunOilArray = wx.getStorageSync('ypIndex');
            let { selectCoupon, orderList, orderIndex, couponArr, isPassword, selectOilCardIndex, cardList, cardDeatilList } = this;
            let islock = await lockOrder({
                orderNo: orderList[orderIndex].orderNo,
            });
            // this.isGray = islock ? true : false
            // clearOrderNo  页面卸载时触发生命周期做解锁订单使用
            this.clearOrderNo = orderList[orderIndex].orderNo;
            this.cancleAmount = orderList[orderIndex].amount;
            if (islock.status == -1) {
                this.isGray = false;
                return this.$util.showModal(islock.info, true);
            }
            const realAmt = JSON.stringify(selectCoupon) == '{}' ? orderList[orderIndex].amount : this.calPrice; // 实收金额
            console.log(cardDeatilList, 'cardDeatilList');
            if (cardDeatilList.length > 0) {
                this.riskControlParameters = {
                    // 油卡风控事前
                    cardNo: cardDeatilList[selectOilCardIndex].cardNo,
                    latitude: this.lat.toString(), // 经度
                    longitude: this.lon.toString(), // 纬度
                    fkaddress: this.province + ';' + this.city, // 交易使用地 登录城市
                    serialNo: orderList[orderIndex].orderNo,
                    userName: cardDeatilList[selectOilCardIndex].userName,
                };
            }

            const deviceInfo = wx.getSystemInfoSync();
            const deviceInformation = {
                version: deviceInfo.version,
                phone: this.registerLoginInformation.phone,
                device_type: deviceInfo.system.includes('iOS') ? 'ios' : 'android',
            };
            const params = {
                orderNo: orderList[orderIndex].orderNo,
                carNumber: this.carCode,
                payMode: [payModeWX, payModeOilCard][this.selectPayIndex], // 微信支付
                realAmt, // 实收金额 this.paymentFlag ? this.source : ''
                shChannel: this.paymentFlag ? this.source : '',
                jsonData: JSON.stringify(this.riskControlParameters),
                itemType: oilGunOilArray[0].productNoLabel, // 油品名称
                itemName: this.ypName, // 油品名称
                quantity: orderList[orderIndex].quantity, // 油量
                attach: JSON.stringify(deviceInformation),
            };
            if (params.payMode === 'wx') {
                delete params.jsonData;
                delete params.itemName;
                delete params.quantity;
                delete params.attach;
            } else {
                delete params.shChannel;
                delete params.attach;
            }
            // console.log(params,JSON.parse(params.jsonData),'params')
            if (params.payMode === 'wx') {
                console.log(params.shChannel, '上海小程序跳转过来的标识');
                console.log(this.paymentFlag, '支付的标识----当前是微信支付,--true 代表要传上海标识,false是不需要传');
            }
            // 处理电子券
            if (JSON.stringify(selectCoupon) != '{}') {
                const { voucher, faceValue } = selectCoupon;
                Object.assign(params, {
                    authCode: voucher, // 电子券单号
                    otherAmt: faceValue, // 电子券面额
                });
            }
            // console.log(selectCoupon.voucher,'aaaaaaa');
            // 处理加油卡支付
            if (params.payMode == payModeOilCard) {
                const selectedOilCard = cardList[selectOilCardIndex];
                if (selectedOilCard !== null && typeof selectedOilCard === 'object') {
                    params.cardNo = selectedOilCard.cardNo;
                } else {
                    await this.$util.showModal('您尚未绑定油卡', true);
                    return;
                }
            }
            // 微信支付
            const wxPay = res => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: res.data.timeStamp,
                    nonceStr: res.data.nonceStr,
                    package: res.data.prepay,
                    signType: res.data.signType,
                    paySign: res.data.paySign,
                    success: () => {
                        // this.isGray = false
                        this.handlePaySuccess(res);
                    },
                    fail: () => {
                        this.isGray = false;
                        // 如果是使用的微信 + 电子券支付 走失败的回调需要做撤销电子券的操作
                        if (params.payMode == payModeWX && selectCoupon.voucher) {
                            // 取消订单
                            this.handlePayFail(payModeWX);
                            // console.log('我点了关闭收银台');
                        }
                    },
                });
            };
            // cardPreOrder 油卡支付（油卡支付不用传上海标识）
            console.log(params, 'cardPreOrder接口参数是否存在风控参数');
            let res = params.payMode === 'wx' ? await wxPreOrder(params) : await cardPreOrder(params);
            if (res.status != -1) {
                if (params.payMode == payModeWX) {
                    // console.log('触发了微信支付')
                    // 微信支付evoucherOrderNo
                    wxPay(res);
                    // 电子券订单号
                    this.evoucherOrderNo = res.data.couponOrderNo;
                    this.wxOrderNo = res.data.orderNo;
                    console.log(this.evoucherOrderNo, 'this.evoucherOrderNo----预下单券订单号');
                } else {
                    // 油卡支付的情况
                    if (res.data.dangerCode == -1) {
                        // 风控阻断
                        wx.showModal({
                            title: '提示',
                            content: res.data.dangerInfo,
                            confirmText: '确定',
                            confirmColor: '#FF8200',
                            showCancel: false,
                            success: function (res) {
                                if (res.confirm) {
                                    this.isGray = false;
                                    uni.$emit('confirmBack');
                                    uni.navigateBack();
                                } else if (res.cancel) {
                                    // 目前什么也不做处理
                                }
                            },
                        });
                    } else if (res.data.dangerCode == 1) {
                        // 需要执行人脸识别
                        // 刷人脸
                        wx.startFacialRecognitionVerify({
                            name: cardDeatilList[selectOilCardIndex].userName, //姓名
                            idCardNumber: cardDeatilList[selectOilCardIndex].idNum, //身份证号
                            success: faceRes => {
                                // 验证成功后触发
                                params.verifyResult = faceRes.verifyResult;
                                params.idName = cardDeatilList[selectOilCardIndex].userName;
                                params.idNum = cardDeatilList[selectOilCardIndex].idNum;
                                params.orgName =
                                    cardDeatilList[selectOilCardIndex].provinceName + ';' + cardDeatilList[selectOilCardIndex].cityName ||
                                    ''; // 开卡机构（省市中文）
                                params.idType = cardDeatilList[selectOilCardIndex].cardType;
                                console.log('风控触发人脸后传参');
                                cardPreOrder(params).then(riskManagementRes => {
                                    if (res.status != -1) {
                                        this.oilCheckCode = riskManagementRes.data.checkCode;
                                        this._cardPayRes = riskManagementRes;
                                        this.isShow = true;
                                        if (this.isShow) {
                                            uni.hideLoading();
                                            this.showKeyboard();
                                        }
                                    } else {
                                        this.isGray = false;
                                    }
                                });
                                // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                                setTimeout(() => {
                                    // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                                    console.log(faceRes);
                                }, 500);
                            },
                            fail: err => {
                                this.isGray = false;
                                // 验证失败时触发
                                // err 包含错误码，错误信息，弹窗提示错误
                                setTimeout(() => {
                                    uni.$emit('confirmBack');
                                    uni.navigateBack();
                                    // if (err.errCode == 10004) {
                                    //   uni.showModal({
                                    //     title: "提示",
                                    //     content: res.data.dangerInfo,
                                    //     showCancel: false,
                                    //     success: function(res) {
                                    //       if (res.confirm) {
                                    //         uni.$emit("confirmBack");
                                    //         uni.navigateBack();
                                    //       } else if (res.cancel) {
                                    //         // 目前什么也不做处理
                                    //       }
                                    //     },
                                    //   });
                                    // }
                                }, 500);
                            },
                        });
                    } else if (res.data.dangerCode == 2 || res.data.dangerCode == '') {
                        // 放行
                        // this.isGray = false
                        // 油卡支付
                        // 尚未设置密码
                        // this.isShowPasswordBox = true;
                        this.oilCheckCode = res.data.checkCode;
                        this._cardPayRes = res;
                        this.isShow = true;
                        if (this.isShow) {
                            this.showKeyboard();
                        }
                    }
                }
            } else {
                console.log('----失败了---');
                let that = this;
                that.isGray = false;
                wx.showModal({
                    title: '提示',
                    content: '支付失败',
                    confirmText: '确定',
                    confirmColor: '#FF8200',
                    showCancel: true,
                    success: function (res) {
                        if (res.confirm) {
                            uni.$emit('confirmBack');
                            uni.navigateBack();
                        } else if (res.cancel) {
                            // 目前什么也不做处理
                        }
                    },
                });
            }
        },

        // 处理支付成功
        handlePaySuccess(res) {
            uni.redirectTo({
                url: '/packages/place-order/pages/pay-result/main?orderid=' + res.data.orderNo + '&id=' + this.evoucherOrderNo,
            });
            this.isGray = false;
            this.$store.commit('setPaymentFlag', false);
            console.log(this.paymentFlag, '支付成功---标识变为false,下一次加油将不再传标识');
        },
        // 处理支付失败
        async handlePayFail(payMode) {
            console.log(payMode, 'payModepayMode');
            // let res = await waitPayOrder();
            let orderNo = this.orderList[this.orderIndex].orderNo;
            let amount = this.orderList[this.orderIndex].amount;
            // 取消订单
            await cancleOrder({ orderNo });
            // 退券 （必须是微信支付的情况下）
            // console.log(this.evoucherOrderNo,payMode == 'wx','payModepayMode');
            if (this.evoucherOrderNo && payMode == 'wx') {
                // 参数是金额 和  电子券单号
                console.log(this.evoucherOrderNo, '券订单号---退券接口调用前');
                let params = {
                    stationNo: this.selectMarker.stationCode,
                    orderNo: this.wxOrderNo,
                    otherAmount: amount,
                    couponOrderNo: this.evoucherOrderNo,
                };
                // 添加参数 stationNo  orderNo
                await refund(params);
            }
            this.$util.tipsToastNoicon('支付失败');
            this.showPassword = '';
            this.password = '';
            this.isGray = false;
            this.isShow = false;
            // this.pullRefresh()
            return;
            if (res.data) {
                /* uni.redirectTo({
                      url: `/packages/place-order/pages/await-pay/main?data=${encodeURIComponent(JSON.stringify(res.data))}&payMode=${payMode}&payAmount=${this.calPrice}`,
                    }); */
                this.$util.tipsToastNoicon('支付失败');
                this.pullRefresh();
            } else {
                uni.$emit('confirmBack');
                uni.navigateBack();
            }
        },

        // 支付失败弹窗点击事件
        clickClosefailPop() {
            this.$refs.failpopup.close();
        },

        // 返回按钮点击事件
        async clickCustomBackBtn() {
            // await this.$util.showModal('您确认要放弃支付么');
            // await cancleOrder({ orderNo: this.orderData.orderNo });
            // uni.$emit('confirmBack');
            uni.navigateBack({
                delta: 1,
            });
        },

        // 立即充值
        toSaveMoney() {
            uni.navigateTo({
                url: '/packages/oil-card/pages/home/<USER>',
            });
        },
    },
};
</script>

<style scoped lang="scss">
.elticket_text_class {
    text-align: right;
    white-space: nowrap;
    max-width: 60vw;
    overflow: hidden;
    text-overflow: ellipsis;
}
// 下拉刷新
.refresh-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45px;
    width: 100vw;
    .refresher-load {
        width: 25px;
        height: 25px;
        animation: refresh 2s linear;
        animation-iteration-count: infinite;
    }
    .refresher-text {
        margin-left: 5px;
    }
}

@keyframes refresh {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.flex {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.f1 {
    flex: 1;
}

.space-btn {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.bg-style {
    background: #fff;
    border-radius: 18rpx;
    padding: 24rpx;
}

.border-btm {
    border-bottom: solid 1rpx rgb(206, 206, 206);
}

.view {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    .content-view {
        box-sizing: border-box;
        min-height: 0;
        background: #f5f5f5;
        .scroll-content-view {
            min-height: calc(100vh - 100px);
            padding: 24rpx;
            padding-bottom: 10px;
        }
        .title {
            font-size: 28rpx;
            margin-bottom: 16rpx;
            color: #999;
        }
        .choice-order {
            margin-bottom: 16rpx;
            .tag {
                font-size: 30rpx;
                margin-bottom: 20rpx;
            }
            .order-list {
                height: 380rpx;
                position: relative;
                overflow: hidden;
                &.more-list,
                &.less-list {
                    height: auto;
                }
                &.padd-btm {
                    padding-bottom: 100rpx;
                }
                .item {
                    border-radius: 16rpx;
                    padding: 24rpx;
                    border: solid 1rpx #999;
                    align-items: center;
                    margin-bottom: 24rpx;
                    &:last-child {
                        margin-bottom: 0;
                    }
                    .left {
                        div:first-child {
                            font-size: 36rpx;
                            font-weight: 600;
                            margin-bottom: 20rpx;
                        }
                        div:last-child {
                            font-size: 24rpx;
                        }
                    }
                    .center {
                        font-size: 42rpx;
                        margin-right: 20rpx;
                        span {
                            font-size: 36rpx;
                        }
                    }
                    .right img {
                        width: 40rpx;
                        height: 40rpx;
                    }

                    &.active {
                        color: #f96702;
                        border-color: #f96702;
                    }
                }
            }
            .show-more {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                padding: 10rpx;
                text-align: center;
                background: #fff;
            }
        }
        .pay-ways {
            .tag {
                font-size: 30rpx;
                margin-bottom: 20rpx;
            }
            .cel-item {
                padding: 24rpx 0;
            }
            .oil-card {
                .left {
                    img {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 10rpx;
                    }
                    .detail {
                        font-size: 30rpx;
                        img {
                            display: inline-block;
                            width: 10rpx;
                            height: 20rpx;
                            margin-left: 10rpx;
                        }
                        div {
                            margin-top: 10rpx;
                            font-size: 26rpx;
                            color: #999;
                        }
                    }
                }
                .right img {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
            .wechat-pay {
                .left {
                    img {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 10rpx;
                    }
                }
                .right img {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
            .count-pay {
                &.oil-card-count .right {
                    color: #999;
                    font-size: 28rpx;
                }
                &.total-price .right {
                    color: #f96702;
                    font-size: 28rpx;
                    img {
                        margin-left: 10rpx;
                        width: 18rpx;
                        height: 30rpx;
                    }
                }
                .left div {
                    width: 40rpx;
                    height: 40rpx;
                    border-radius: 6rpx;
                    background: red;
                    color: #fff;
                    text-align: center;
                    line-height: 40rpx;
                    margin-right: 10rpx;
                    font-size: 26rpx;
                }
            }
        }
    }
    .btn-view {
        padding: 24rpx 24rpx calc(20rpx + env(safe-area-inset-bottom));
        .left {
            text-align: right;
            .flex {
                justify-content: flex-end;
                div {
                    font-size: 38rpx;
                }
            }
            .discount {
                margin-top: 6rpx;
                font-size: 26rpx;
                color: #f96702;
            }
        }
        .right {
            padding: 20rpx 0;
            min-width: 220rpx;
            text-align: center;
            background: #f96702;
            border-radius: 16rpx;
            color: #fff;
            margin-left: 10rpx;
        }
        .buttonGrayedOut {
            padding: 20rpx 0;
            min-width: 220rpx;
            text-align: center;
            background: #c8c9cc;
            cursor: no-drop;
            border-radius: 16rpx;
            color: #fff;
            margin-left: 10rpx;
        }
    }
}

// 底部弹窗
.pop-view {
    background-color: #f6f6f6;
    min-height: 160px;
    .pop-title-view {
        display: flex;
        height: 50px;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 0.5px solid #dcdddd;
        .pop-title-text {
            margin-left: 15px;
            color: #222222;
            font-size: 16px;
            // font-weight: 700;
            flex: 1;
            text-align: center;
        }
        .close-btn {
            display: flex;
            align-items: center;
            .close-text {
                color: #909090;
                font-size: 12px;
            }
            .pop-close-btn {
                margin-left: 5px;
                padding-right: 10px;
                padding-top: 10px;
                padding-bottom: 10px;
                margin-right: 10px;
            }
        }
    }
    .coupon-scroll-view {
        height: 300px;
        background-color: #ffffff;
    }
}

// 支付失败弹窗
.suc-pop-view {
    display: flex;
    flex-direction: column;
    align-items: center;

    .suc-pop-content {
        border-radius: 10px;
        width: 345px;
        height: 235px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;

        .suc-pop-icon {
            width: 90px;
            height: 90px;
        }

        .suc-pop-title {
            margin-top: 15px;
            line-height: 36px;
            font-size: 24px;
            color: #141414;
            font-weight: 700;
        }

        .suc-pop-detail {
            line-height: 36px;
            font-size: 12px;
            color: #141414;
            font-weight: 700;
        }
    }
}

// .passwordShow{
//   width: 200px;
//   height: 100px;
//   position: fixed;
//   top: 30%;
//   left: 30%;
//   background-color:#909090;
// }

.payment-box {
    position: relative;
    width: 330px;
    height: 210px;
    overflow: hidden;
    z-index: 200;
    .payment-box-close {
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 20px;
        padding: 10px;
    }
    .payment-box-title {
        margin-top: 20px;
        text-align: center;
        .payment-title-text {
            color: #333333;
            font-size: 18px;
            font-weight: bold;
        }
    }
    .payment-box-content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-top: 15px;
        .payment-content-merchant {
            color: #909090;
            font-size: 15px;
            font-weight: 400;
        }
        .payment-content-amount {
            line-height: 42px;
            color: #333;
            font-weight: bold;
            .payment-amount-sign {
                font-size: 26px;
            }
            .payment-amount-text {
                font-size: 30px;
            }
        }
    }
    .payment-box-footer {
        height: 50px;
        margin: 20px 15px 0;
        & ::v-deep .u-char-item {
            margin: 0 -1px -1px 0; /** 避免左右边框重叠 */
            border-color: #dcdcdc !important;
            border-width: 2px;
        }
    }
}

.showPassword {
    background-color: #fff;
    position: fixed;
    top: 50%;
    left: 48%;
    transform: translate(-50%, -65%);
    z-index: 10;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
}

.mask {
    width: 100%;
    height: 100vh;
    position: fixed;
    background: rgba(110, 109, 109, 0.174);
    z-index: 5;
}
</style>
