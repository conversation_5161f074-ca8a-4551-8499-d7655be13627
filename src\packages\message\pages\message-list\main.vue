<template>
    <div class="detail">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            back-text="消息详情"
            :back-text-style="pageConfig.titleStyle"
            :background="pageConfig.bgColor"
            :border-bottom="false"
        ></u-navbar>

        <div class="message-list">
            <div class="message-item" v-for="(item, index) in messageList" :key="index" @click="messageClick(item)">
                <div class="title-time">
                    <div class="title">{{ item.sketch }}</div>
                    <div class="time">{{ item.appSendTime }}</div>
                </div>
                <div class="message-detail">{{ item.content }}</div>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getUnreadMsgModel } from '@/api/home.js';

export default {
    name: 'main',
    data() {
        return {
            // 页面配置
            pageConfig,
            // 消息列表
            messageList: [],
        };
    },
    async onLoad(option) {
        let {
            data: { msgModels },
        } = await getUnreadMsgModel({ ...option });
        this.messageList = msgModels;
    },
    methods: {
        //消息点击事件
        messageClick(item) {
            let obj = encodeURIComponent(JSON.stringify(item));
            uni.navigateTo({
                url: `/packages/message/pages/message-detail/main?obj=${obj}`,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.detail {
    width: 100vw;
    min-height: 100vh;
    background-color: #f6f6f6;

    .message-list {
        padding: 10px 15px;

        .message-item {
            padding: 10px 15px;
            background-color: #fff;
            border-radius: 5px;
            margin-bottom: 10px;
            .title-time {
                display: flex;
                align-items: center;

                .title {
                    font-size: 15px;
                    color: #333;
                    line-height: 24px;
                    flex: 1;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                }

                .time {
                    font-size: 12px;
                    line-height: 24px;
                    color: #909090;
                }
            }

            .message-detail {
                font-size: 12px;
                line-height: 24px;
                color: #909090;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }
        }
    }
}
</style>
