<template>
    <div class="container">
        <div class="content"> 订单已退货 </div>
        <oil-station-info :order="order" />
    </div>
</template>

<script>
import OilStationInfo from './oil-station-info.vue';

export default {
    components: { OilStationInfo },
    props: {
        order: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    .content {
        font-size: 20px;
        padding: 10px;
        color: #ff6b2c;
        border-bottom: 1px dashed #dddddd;
        margin-bottom: 15px;
    }
}
</style>
