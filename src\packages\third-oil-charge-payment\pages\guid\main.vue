<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="p-hw">
            <zj-navbar :height="44" title="加油流程" :border-bottom="false"></zj-navbar>
            <div class="bg-F2F3F5 p-bf pad24-14">
                <div class="process-guidelines-div">
                    <div class="process-guidelines-div-title">
                        <span class="title-bg">加油操作流程指引</span>
                    </div>
                    <div class="type-div">
                        <div class="type-div-title">
                            <img class="title-img" src="../../image/self-service.png" alt="" />
                            <span>自助加油</span>
                        </div>
                        <div class="type-div-content">
                            如您去往的加油站是自助加油站，您可以在加油机键盘中按<img
                                class="content-img"
                                src="../../image/up-content.png"
                            />键后，输入授权码，点击确认，看到加油机屏幕上显示验证通过并展示您的授权金额，即可提枪加油。挂枪后，系统会按您的实际加油金额进行结算(不会超过您的授权上限)。
                        </div>
                    </div>
                    <div class="type-div">
                        <div class="type-div-title">
                            <img class="title-img" src="../../image/non-self-service.png" alt="" />
                            <span>非自助加油</span>
                        </div>
                        <div class="type-div-content">
                            将授权码出示给加油员，加油员即可为您完成加油，挂枪后，系统会按您的实际加油金额进行结算(不会超过您的授权上限)。
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {},
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {},
    methods: {},
};
</script>
<style scoped lang="scss">
/* @import url(); 引入css类 */
.padt12 {
    padding-top: 12px;
}

.mar12 {
    margin: 0 12px;
}

.pad24-14 {
    padding: 14px 14px;
}

.title {
    padding-bottom: 24px;
}

.padt48 {
    padding-top: 36px;
}

.process-guidelines-div {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    .process-guidelines-div-title {
        text-align: center;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 23px;
        text-align: center;
        font-style: normal;
        .title-bg {
            display: inline-block;
            height: 24px;
            background: linear-gradient(to bottom, #ffffff 50%, #ffeae9 50%);
        }
    }
    .type-div {
        .type-div-title {
            display: flex;
            align-items: center;
            margin-top: 12px;
            margin-bottom: 9px;
            .title-img {
                width: 19px;
                height: 18px;
                margin-right: 5px;
            }
            span {
                font-weight: 500;
                font-size: 14px;
                color: #ff4f13;
                text-align: left;
                font-style: normal;
            }
        }
        .type-div-content {
            font-weight: 400;
            font-size: 13px;
            color: #666666;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .content-img {
                width: 10px;
                height: 15px;
                margin: 0 5px;
            }
        }
    }
}
</style>
