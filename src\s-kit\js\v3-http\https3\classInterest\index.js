import { POST, POST2 } from '../../index';
//获取会员等级列表
export const userInterestsGradeList = (params, config) => {
    return POST('user.interests.gradeList', params, config);
};

//获取会员等级所对应的权益
export const userInterests = (params, config) => {
    return POST('user.interests', params, config);
};

//获取当前客户成长值，等级到期时间
export const userBasicGetCurrentGP = async (params, config) => {
    return await POST('user.basic.getCurrentGP', params, config);
};

//会员中心基本信息
export const memberInfo = async (params, config) => {
    return await POST('user.basic.member', params, config);
};

//获取能源币，油卡，电子券，积分和余额，七日内余额
export const basicCoupon = async (params, config) => {
    return await POST('user.basic.coupon', params, config);
};

//会员体系简介
export const userMemberSystemInfo = async (params, config) => {
    return await POST('user.memberSystemInfo', params, config);
};

// 获取会员所属群组列表并展示
export const userGroupInterestsGroup = async (params, config) => {
    return await POST('user.group.interestsGroup', params, config);
};

// 查询用户详细信息
export const userBasicInfoQuery = async (params, config) => {
    return await POST('user.basicInfo.query', params, config);
};

// 查询用户修改手机号次数是否超限 /user/modifyPhoneTimes/query
export const userModifyPhoneTimesQuery = async (params, config) => {
    return await POST('user.modifyPhoneTimes.query', params, config);
};

// 获取要变更手机号验证码 /user/newPhoneVerifyCode / send
export const userNewPhoneVerifyCodeNend = async (params, config) => {
    return await POST('user.newPhoneVerifyCode.send', params, config);
};

// 修改用户基本信息/user/basicInfo/modify
export const userBasicInfoModify = async (params, config) => {
    return await POST('user.basicInfo.modify', params, config);
};

export const unBindThirdUser = (params, config) => {
    return POST2('/app/json/login2/unbind', params, config);
};
