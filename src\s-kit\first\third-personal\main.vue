<template>
    <div class="view-page">
        <div class="bg-wrap">
            <img src="./my-img/car-bg.png" alt class="bg-page" />
        </div>
        <div class="page-wrap pullDownRefreshView">
            <zj-navbar
                v-if="pageType == 'page'"
                :background="{ background: 'rgba(255, 255, 255, 0)' }"
                :border-bottom="false"
                back-icon-color="#fff"
                title="我的"
                titleColor="#fff"
            >
            </zj-navbar>
            <div class="navi-view" v-else>
                <!-- #ifdef MP-MPAAS -->
                <div class="state-view" :style="{ paddingTop: 10 + Number(statusBarHeight) + 'px' }"></div>
                <!-- #endif -->
                <!-- #ifndef MP-MPAAS  -->
                <div class="state-view" :style="{ height: Number(statusBarHeight) + Number(navH) + 'px' }"></div>
                <!-- #endif -->
            </div>

            <!-- 个人信息 -->
            <div class="f-1 mh-0" style="position: relative">
                <div class="guideBox" v-if="guideStep == 4">
                    <img class="guideImg" mode="widthFix" src="../../image/homeImg/guidePage4.png" alt="" />
                    <div class="guideBtton">
                        <div class="next" @click.stop="setGuideStep(0)">完成指引</div>
                    </div>
                </div>
                <zj-pull-down-refresh ref="pullDownRefreshRef" :refresherDisable="!isLogin" @refreshPullDown="refreshPullDown">
                    <div class="fl-row fl-jus-bet marrl-18-16">
                        <div class="header_box fl-row fl-al-cen f-1">
                            <div class="header-div">
                                <!-- <img class="vip_img" src="@/static/trirdImage/vip_img.png" alt /> -->
                                <img class="head_style" src="./my-img/header.png" alt />
                            </div>
                            <div class="fl-column">
                                <div class="fl-row align-items" v-if="isLogin">
                                    <div class="font-15 memberName color-333 weight-bold">{{ memberData.alias || '会员名称' }} </div>
                                    <div class="icon-18 font-10 wight-400" v-if="levelData.levelName">
                                        {{ levelData.levelName || '会员等级' }}
                                    </div>
                                </div>
                                <div class="fl-col align-items" @click="toLoginPage" v-else>
                                    <div class="font-15 memberName color-333 weight-bold">登录/注册</div>
                                    <div class="font-13 color-333">登录后可享受更多服务</div>
                                </div>
                                <div class="font-12 color-333 weight-bold paddt5">{{ levelData.phone }}</div>
                            </div>
                        </div>
                        <div class="fl-row">
                            <!-- #ifndef MP-MPAAS  -->
                            <div class="setting" @click="jumpRefuelingCard('mail')">
                                <img src="./my-img/msg_blick_icon.png" alt />
                                <div v-if="messageRedDot" class="right_circle">{{ messageRedDot > 99 ? '99+' : messageRedDot }}</div>
                            </div>
                            <!-- #endif -->
                            <div class="setting" @click="jumpRefuelingCard('setting')">
                                <img src="./my-img/icon-24.png" alt />
                            </div>
                        </div>
                    </div>
                    <!-- card -->
                    <div class="card-area" @click="goWallet()">
                        <img :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt />
                        <div
                            v-if="walletInfo.ecardNo && isLogin"
                            class="cardInfo fl-row fl-jus-bet fl-al-cen cardNum font-16 weight-bold color-fff"
                            >{{ $sKit.layer.splitFourString(walletInfo.ecardNo) }}</div
                        >
                        <div v-else-if="!isLogin" class="cardInfo fl-row fl-jus-bet fl-al-cen cardNum font-16 weight-bold color-fff"
                            >请先登录/注册</div
                        >
                    </div>
                    <!-- 卡余额 -->
                    <div class="centent-area">
                        <div class="fl-row fl-jus-bet">
                            <div class="fl-column f-1">
                                <!-- <div class="fl-row font-18 color-333 weight-bold paddb12"
                                    >昆仑e享卡<div class="text color-666 font-12">(电子卡)</div></div
                                > -->
                                <div class="font-15 color-999 wight-400">余额(元)</div>
                                <div class="font-20 clor-333 hei28 font-style"
                                    >{{ isLogin ? changeNum(memberAccountInfo.eWalletAvailableAmount) : '****' }}
                                </div>
                            </div>
                            <div class="fl-row fl-al-cen paddt30" @click="goWallet()">
                                <div class="font-14 weight-400 color-666">我的钱包</div>
                                <div class="arroe-right-small-1 marl3"></div>
                            </div>
                        </div>
                        <div class="cardAndVoucherWrapBox">
                            <div class="cardAndVoucherWrap">
                                <div class="cardAndVoucher-content">
                                    <!-- TODO @version? -->
                                    <div v-if="0" class="cardAndVoucher-content-item" @click="toCard()">
                                        <div class="image">
                                            <img src="./my-img/icon-oil-card.png" alt />
                                        </div>
                                        <div class="textName">实体卡</div>
                                        <div class="textNum font-style">{{ isLogin ? memberAccountInfo.fuelCardCount || 0 : '**' }}</div>
                                    </div>
                                    <div @click="jumpRefuelingCard('yhq')" class="cardAndVoucher-content-item">
                                        <div class="image">
                                            <img src="./my-img/icon-coupon.png" alt />
                                        </div>
                                        <div class="textName">优惠券</div>
                                        <div class="textNum font-style">{{ isLogin ? memberAccountInfo.couponsCount || 0 : '**' }}</div>
                                    </div>
                                    <!-- <div class="cardAndVoucher-content-item" @click="jumpRefuelingCard('hkb')">
                                        <div class="image">
                                            <img src="./my-img/icon-coin.png" alt />
                                        </div>
                                        <div class="textName">能源币</div>
                                        <div class="textNum font-style">{{ memberAccountInfo.ptsAvailableAmount || 0 }}</div>
                                    </div> -->
                                    <div v-if="0" class="cardAndVoucher-content-item" @click="jumpRefuelingCard('jf')">
                                        <div class="image">
                                            <img src="./my-img/icon-point.png" alt />
                                        </div>
                                        <div class="textName">积分</div>
                                        <div class="textNum font-style">
                                            {{ isLogin ? memberAccountInfo.loyaltyPtsAvailableAmount || 0 : '**' }}
                                        </div>
                                    </div>
                                    <!-- #ifndef MP-TOUTIAO -->
                                    <div v-if="0" class="cardAndVoucher-content-item" @click="jumpRefuelingCard('wdpf')">
                                        <div class="image">
                                            <img src="./my-img/icon-Frame.png" alt />
                                        </div>
                                        <div class="textName">我的皮肤</div>
                                        <div class="textNum font-style">
                                            {{ isLogin ? mySkin || 0 : '**' }}
                                        </div>
                                    </div>
                                    <!-- #endif -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-area-bottom">
                        <!-- 营销位 -->
                        <div class="zj-banner mar12" v-if="isLogin">
                            <zjMarket
                                :radius="0"
                                marketType="lbt"
                                :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
                                spaceType="my_page_banner_top"
                            >
                            </zjMarket>
                        </div>
                        <div class="card-default mar12" @click="jumpRefuelingCard('yhzd')">
                            <div class="fl-row fl-al-cen padd12">
                                <div class="f-1 fl-row fl-al-cen fl-al-cen">
                                    <img src="./my-img/icon-yhzd.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9">优惠账单</div>
                                </div>
                                <div class="fl-row fl-al-cen">
                                    <div class="font-12 color-999 weight-400"
                                        >累计已省<span class="font-14" style="color: #ff4000"
                                            >&yen;{{ isLogin ? totalDiscounts : '**' }}</span
                                        ></div
                                    >
                                    <div class="arroe-right-small-1 marl3"></div>
                                </div>
                            </div>
                        </div>
                        <!-- #ifndef MP-TOUTIAO || H5-CLOUD -->
                        <div class="card-default mar12" @click="jumpRefuelingCard('hym')">
                            <div class="fl-row fl-al-cen padd12">
                                <div class="f-1 fl-row fl-al-cen fl-al-cen">
                                    <img src="./my-img/icon-code.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">会员码</div>
                                </div>
                                <div class="fl-row fl-al-cen">
                                    <div class="font-12 color-999 weight-400">加油卡支付 积分消费</div>
                                    <div class="arroe-right-small-1 marl3"></div>
                                </div>
                            </div>
                        </div>
                        <!-- #endif -->

                        <!-- TODO @version? -->
                        <!--
                        <div v-if="isShowBulkOil" class="card-default mar12" @click="jumpRefuelingCard('bulkOil')">
                            <div class="fl-row fl-al-cen padd12">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img alt class="icon-24" src="./my-img/icon-invoice.png" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">散装油预约、购买</div>
                                </div>
                                <div class="fl-row fl-al-cen">
                                    <div class="font-12 color-999 weight-400">去预约/购买</div>
                                    <div class="arroe-right-small-1 marl3"></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-default mar12" @click="jumpRefuelingCard('lpk')" v-if="lpkShow">
                            <div class="fl-row fl-al-cen padd12">
                                <div class="f-1 fl-row fl-al-cen fl-al-cen">
                                    <img src="./my-img/icon-gift-card.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">礼品卡</div>
                                </div>
                                <div class="fl-row fl-al-cen">
                                    <div class="font-12 color-999 weight-400">
                                        {{ availableCount > 0 ? `${availableCount}张` : '去绑定' }}
                                    </div>
                                    <div class="arroe-right-small-1 marl3"></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-default mar12" @click="jumpRefuelingCard('fp')">
                            <div class="fl-row fl-al-cen padd12">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img src="./my-img/icon-invoice.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">电子发票</div>
                                </div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                        <div class="card-default mar12">
                            <div class="fl-row fl-al-cen padd12" @click="jumpRefuelingCard('ydqy')" v-if="isMpass">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img src="./my-img/icon-treatment.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">优待权益申请</div>
                                </div>
                                <div class="fl-row fl-al-cen">
                                    <div class="font-12 color-999 weight-400">优待证专属权益申请</div>
                                    <div class="arroe-right-small-1 marl3"></div>
                                </div>
                            </div>
                        </div>
                       -->

                        <div v-if="0" class="card-default mar12">
                            <!-- #ifndef MP-TOUTIAO || H5-CLOUD -->
                            <div v-if="isMpass" class="fl-row fl-al-cen padd12" @click="aiClientClick()">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img src="./my-img/icon-service.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">在线客服</div>
                                </div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                            <!-- #endif -->
                            <!-- <div class="fl-row fl-al-cen padd12" @click="refuelingRobot()">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img alt class="icon-24" src="./my-img/icon-robot.png" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">加油机器人</div>
                                </div>
                                <div class="arroe-right-small-1"></div>
                            </div> -->
                            <div class="fl-row fl-al-cen padd12" @click="jumpRefuelingCard('wdcl')">
                                <div class="f-1 fl-row fl-al-cen">
                                    <img src="./my-img/icon-car.png" alt class="icon-24" />
                                    <div class="font-15 weight-400 color-333 marl9 line_height">我的车辆</div>
                                </div>
                                <div class="arroe-right-small-1"></div>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
        </div>

        <div v-if="isLogin" :class="{ zjMarketBox: isPageH, noZjMarketBox: !isPageH }">
            <zjMarket
                ref="maskBanner"
                :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
                marketType="screenMask"
                spaceType="my_page"
                @isPageH="getIsPageH"
            ></zjMarket>
        </div>
        <!-- #ifndef MP-TOUTIAO || H5-CLOUD -->
        <u-modal
            v-model="show"
            :show-cancel-button="true"
            :show-title="false"
            border-radius="16"
            cancel-color="#666666"
            cancel-text="取消"
            confirm-color="#333333"
            confirm-text="去预约"
            @cancel="modalHandlerCancel"
            @confirm="modalHandlerConfirm"
        >
            <bulkOilDialog></bulkOilDialog>
        </u-modal>
        <!-- #endif -->
        <zj-old-account v-if="isTransfer"></zj-old-account>

        <zj-show-modal v-if="isHarmony"></zj-show-modal>
    </div>
</template>
<script>
import zjMarket from '../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import platform from '../../../s-kit/js/platform.js';
import { contentMessageCodeUnRead } from '../../../s-kit/js/v3-http/https3/preferentialGroup/index';
import { mapGetters, mapState } from 'vuex';
import { openAccount } from '../../../s-kit/js/v3-http/https3/wallet';
import { aiClient, getPersMiniProgramQrCode } from '../../../s-kit/js/v3-http/https3/user';
import { basicInfoQuery } from '../../../s-kit/js/v3-http/https3/oilCard/index';
import { memberInfo } from '../../../s-kit/js/v3-http/https3/classInterest/index';
import { giftCardNumQuery } from '../../../s-kit/js/v3-http/https3/giftCard/index';
import { cardList } from '../../../s-kit/js/v3-http/https3/oilCard/index.js';
import { getTotalDiscounts } from '../../../s-kit/js/v3-http/https3/order/index';
import Vue from 'vue';
import { clientCode } from '../../../../project.config';
import bulkOilDialog from '../../../components/bulkOilDialog/mian.vue';
export default {
    components: {
        bulkOilDialog,
        // ZjOldAccount
        zjMarket,
    },
    props: {
        // 页面类型 cube App一级web小程序页面   home 小程序一级页面   page二级页面
        pageType: {
            type: String,
            default: '',
        },
        isLogin: {
            type: Boolean,
            default: true,
        },
        // systemBarNum: {
        //     type: String,
        //     default: 0,
        // },
    },
    data() {
        return {
            navH: 44,
            statusBarHeight: 0,
            memberData: {},
            levelData: {},
            isload: false,
            availableCount: 0,
            clientCode: '',
            isPageH: false,
            lpkShow: true,
            isMpass: false,
            thirdCardList: [],
            // 累计已省金额
            totalDiscounts: '0',
            refer: 'r54',
            // 站内信消息红点
            messageRedDot: 0,
            show: false,
            // 散装油开关标识
            isShowBulkOil: false,
        };
    },
    onShow() {},
    async created() {
        let systemInfo;
        // #ifndef MP-MPAAS || H5-CLOUD
        systemInfo = uni.getSystemInfoSync();
        this.statusBarHeight = systemInfo.statusBarHeight;
        // #endif
        // #ifdef MP-MPAAS
        this.$cnpcBridge.getBarHeight(res => {
            this.statusBarHeight = res;
        });

        this.lpkShow = await this.$cnpcBridge.judgeProtocolCall('3.6.4');
        this.$cnpcBridge.getSwitch('isShowBulkOil', value => {
            this.isShowBulkOil = value === 'yes' || false;
        });
        // #endif
        // #ifdef H5-CLOUD
        let upaskSystem = getApp().globalData.systemBar;
        this.statusBarHeight = Number(upaskSystem);
        // #endif

        // #ifdef MP-MPAAS
        this.isMpass = true;
        // #endif

        this.$sKit.mpBP.tracker('我的页面', {
            seed: 'minePageBiz',
            pageID: 'minePage',
            refer: this.refer,
            channelID: clientCode,
        });
        this.toLoginPage = this.$sKit.commonUtil.throttleUtil(this.toLoginPage);
    },
    mounted() {
        uni.hideLoading();
        if (!this.isLogin) return;
        this.refreshPullDown();
    },
    computed: {
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            guideStep: state => state.thirdIndex.guideStep,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            isUpAdPopupMy: state => state.thirdIndex.isUpAdPopupMy,
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        ...mapGetters(['walletStatus', 'memberAccountInfo', 'walletSkin', 'mySkin', 'walletInfo']),
    },
    watch: {},
    methods: {
        openTest() {
            this.$sKit.layer.cubeMini('/pages/index/index', '****************');
        },
        toLoginPage() {
            if (!this.loginButtonGrayedOut) return;
            let url = '';
            // #ifdef MP-WEIXIN
            url = '/packages/transferAccount/pages/home/<USER>/pages/thirdHome/main&curTabIndex=3';
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/union/main?qKey=mine';
            // #endif
            // #ifdef H5-CLOUD
            url = '/packages/third-cloud-login/pages/home/<USER>';
            // #endif
            this.$sKit.layer.useRouter(url);
        },
        /**
         * @description  : 优惠账单已省总金额
         * @return        {*}
         */
        async getCumulativeAmount() {
            let res = await getTotalDiscounts({}, { isload: this.isload });
            if (res.success) {
                this.totalDiscounts = res.data;
            }
        },
        getIsPageH(res) {
            this.isPageH = res && this.isUpAdPopupMy;
            this.$store.commit('setIsUpAdPopupMy', false);
        },
        setGuideStep(step) {
            this.$store.dispatch('changeGuideStep', step);
            if (!step) {
                this.$emit('backHome');
            }
        },
        /**
         * @description  : 前往油卡页
         * @return        {*}
         */
        toCard() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.mpBP.tracker('我的页面', {
                seed: 'minePageBiz',
                pageID: 'cardBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.isload = false;
                    let url = '';
                    let params = {};
                    let type = 'navigateTo';
                    if (this.thirdCardList.length == 1) {
                        url = '/packages/third-oil-card/pages/oil-card-management/main';
                        params = this.thirdCardList[0];
                    } else {
                        url = '/packages/third-oil-card/pages/my-card/main';
                        params = {
                            refer: 'r45',
                            myRefer: this.refer,
                        };
                    }
                    this.$sKit.layer.useRouter(url, params, type);
                },
                freezeReasonArr: [],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                isEnter: 1,
                walletAddParams: {
                    refer: 'r03',
                },
            });
        },
        /**
         * @description  : 前往钱包页冻结、 注销情况可以进入
         * @return        {*}
         */
        goWallet() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.mpBP.tracker('我的页面', {
                seed: 'minePageBiz',
                pageID: 'walletBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.isload = false;
                    this.$sKit.layer.useRouter(
                        '/packages/third-my-wallet/pages/home/<USER>',
                        { refer: 'r07', myRefer: this.refer },
                        'navigateTo',
                    );
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                isEnter: 1,
                freeze: 1,
                walletAddParams: {
                    refer: 'r03',
                },
            });
        },
        /**
         * @description : 查询用户基本信息
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery({}, { isload: this.isload });
            if (res.success) {
                if (this.memberData == res.data) return;
                this.memberData = res.data;
            }
        },
        /**
         * @description : 获取站内信
         * @return        {*}
         */
        async getUserGroupNewsList() {
            let res = await contentMessageCodeUnRead({}, { isload: this.isload, isCustomErr: true });
            if (res && res.success) {
                this.messageRedDot = res.data.unReadNum;
            }
        },

        /**
         * @description : 查询会员等级信息
         * @return        {*}
         */
        async getmemberInfo() {
            let res = await memberInfo({}, { isload: this.isload });
            if (res.success) {
                if (this.levelData == res.data) return;
                this.levelData = res.data;
            }
        },
        /**
         * @description : 处理3位一个逗号
         * @return        {*}
         */
        changeNum(str) {
            return str ? str.replace(/\B(?=(\d{3})+\b)/g, ',') : '0';
        },
        /**
         * @description : 下拉刷新事件
         * @param         {string} basicCouponAction -  获取能源币，油卡，电子券，积分和余额，七日内余额
         * @param         {string} getSetWalletStatus - 获取钱包状态
         * @param         {string} getCurrentImg - 获取卡皮肤
         * @param         {string} stopRefresh - 停止刷新
         * @return        {*}
         */
        refreshPullDown() {
            this.getCumulativeAmount();
            this.getBasicInfoQuery();
            this.getmemberInfo();
            this.getUserGroupNewsList();
            if (this.lpkShow) {
                //如果app版本不支持，就不去获取礼品数据了
                this.queryNmuGift();
            }
            this.$store.dispatch('basicCouponAction');
            this.$store.dispatch('getSetWalletStatus');
            this.$store.dispatch('getMySkin');
            this.$store.dispatch('getAccountBalanceAction');
            this.$nextTick(() => {
                this.$store.dispatch('getCurrentImg', {
                    callback: () => {
                        this.$refs.pullDownRefreshRef.stopRefresh();
                    },
                    isload: this.isload,
                });

                cardList({}, { isload: this.isload, isCustomErr: true }).then((res, err) => {
                    if (res && res.success) {
                        this.thirdCardList = res.data.rows;
                    }
                });

                this.$refs.pullDownRefreshRef.stopRefresh();
            });
            console.log('下拉刷新了');
        },
        /**
         * @description : 页面跳转事件
         * @return        {*}
         */
        async jumpRefuelingCard(typeValue) {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            let url = '';
            let params = {};
            let type = 'navigateTo';

            // 定义类型到URL的映射关系
            const typeToUrlMap = {
                ye: '/packages/third-remaining-sum/pages/home/<USER>',
                qb: '/packages/third-my-wallet/pages/home/<USER>',
                jyk: '/packages/third-oil-card/pages/my-card/main',
                yhq: `/packages/third-coupon-module/pages/coupon-list/main`,
                hkb: `/packages/third-hospitality-coin/pages/home/<USER>
                jf: `/packages/third-integral/pages/home/<USER>
                hym: '/packages/third-scan-code-payment/pages/home-code/main',
                fp: `/packages/third-invoice/pages/home/<USER>
                setting: '/packages/third-my-center/pages/setting/main',
                wdqy: '/packages/third-class-interest/pages/home/<USER>',
                ydqy: '/packages/third-preferential-group/pages/home/<USER>',
                wdpf: '/packages/third-skin-replacement/pages/home/<USER>',
                zxkf: '/packages/third-my-wallet/pages/home/<USER>', // 这里没有对应的URL
                wdcl: '/packages/third-my-center/pages/my-vehicle/main',
                // denglu: '/packages/third-login/pages/rgister-an-account/main',
                ttgl: '/packages/third-invoice/pages/invoice-title-list/main',
                // gradeFn: '/packages/third-class-interest/pages/home/<USER>',
                // gradeFn: '/packages/third-oil-charge-payment/pages/oil-station-module/main'
                lpk: '/packages/third-gift-card/pages/card-list/main',
                // lpk: '/packages/third-gift-card/pages/binding/main',
                yhzd: '/packages/third-discount-bill/pages/discount-bill/main',
                dcwj: '/packages/third-questionnaire/pages/home/<USER>',
                mail: '/packages/third-message/pages/home/<USER>',
                bulkOil: '/packages/third-purchase-bulk-oil/pages/home/<USER>',
            };
            const bizPageId = {
                yhq: `couponBut`,
                jf: `jifenBut`,
                hym: 'codeBut',
                fp: `invoiceBut`,
                setting: 'settingBut',
                ydqy: 'ydEquityBut',
                wdpf: 'skinBut',
                wdcl: 'carBut',
                lpk: 'giftCardBut',
                yhzd: 'billBut',
                mail: 'mailBut',
            };
            // 根据typeValue获取对应的url和params
            if (typeValue in typeToUrlMap) {
                url = typeToUrlMap[typeValue];
            }

            if (typeValue in bizPageId) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: bizPageId[typeValue],
                    refer: this.refer,
                    channelID: clientCode,
                });
                params.myRefer = this.refer;
            }
            if (typeValue === 'hym') {
                params.tabType = 'code';
                params.refer = 'r28';
            }

            if (typeValue === 'yhq') {
                params.refer = 'r38';
            }

            if (typeValue === 'qb') {
                params.refer = 'r07';
            }

            if (typeValue === 'fp') {
                params.refer = 'r31';
            }
            if (typeValue === 'lpk') {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        this.$sKit.layer.useRouter(url, params, type);
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    walletAddParams: {
                        refer: 'r03',
                    },
                });
            } else if (typeValue === 'bulkOil') {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        this.show = true;
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                });
            } else {
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        modalHandlerConfirm() {
            // let url = '/packages/third-purchase-bulk-oil/pages/purchaseOfBulkOil/main';
            let url = '/packages/third-purchase-bulk-oil/pages/home/<USER>';
            this.$sKit.layer.useRouter(url);
        },
        modalHandlerCancel() {
            this.show = false;
        },
        /**
         * @description : 前往昆仑e享卡页面
         * @return        {*}
         */
        toRecharge() {
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/home/<USER>', {}, 'navigateTo');
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                walletAddParams: {
                    refer: 'r03',
                },
            });
        },
        /**
         * @description : 前往在线客服页面
         * @return        {*}
         */
        async aiClientClick() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.mpBP.tracker('我的页面', {
                seed: 'minePageBiz',
                pageID: 'on-lineCustomerBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let res = await aiClient();
            let data = res.data;
            if (res.status == 0) {
                // resolve(data.data)
                // #ifdef MP-MPAAS
                if (this.isHarmony) {
                    this.$cnpcBridge.openModule({
                        type: 'web',
                        url: res.data,
                    });
                } else {
                    this.$cnpcBridge.openModule({
                        type: 'oldWeb',
                        url: res.data,
                        hasNativeTop: '1',
                    });
                }
                // #endif
            } else {
                uni.showToast({
                    title: data.info,
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        refuelingRobot() {
            // #ifndef MP-MPAAS
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.$sKit.layer.useRouter('/packages/third-robotrefuel/pages/home/<USER>', {}, 'navigateTo');
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                walletAddParams: {
                    refer: 'r03',
                },
            });
            // #endif
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    let url = `pages/index/index`;
                    this.$sKit.layer.cubeMini(url, '7928435601927482');
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                isEnter: 1,
                freeze: 1,
                walletAddParams: {
                    refer: 'r03',
                },
            });
            // #endif
        },
        async queryNmuGift() {
            let res = await giftCardNumQuery({}, { isload: this.isload, isCustomErr: true });
            // availableCount	可用张数
            // usedCount	已用完张数
            // expirationCount	已过期张数
            if (res.success) {
                this.availableCount = res.data.availableCount;
            }
        },
    },

    /**
     * @description : 销毁页面上的弹窗
     * @return        {*}
     */
    // beforeDestroy() {
    //     this.$store.dispatch('zjHideModal');
    // },
};
</script>

<style scoped lang="scss">
.zjMarketBox {
    z-index: 99999999;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}

.noZjMarketBox {
    height: 0;
    width: 0;
    display: none;
}

.guideBox {
    .guideImg {
        width: 100%;
        position: absolute;
        top: 110px;
        left: 0;
        z-index: 999999;
    }

    .guideBtton {
        display: flex;
        position: absolute;
        top: 200px;
        z-index: 9999999;
        right: 25px;

        div {
            height: 51rpx;
            border-radius: 34rpx;
            border: 2rpx solid #ffffff;
            font-size: 24rpx;
            line-height: 51rpx;
            text-align: center;
            width: 83px;
            color: #333;
            background-color: #fff;
        }
    }
}

.view-page {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #f2f3f5;

    .bg-wrap {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 306px;
    }

    .bg-page {
        width: 100%;
        height: 100%;
        display: block;
        z-index: 1;
    }

    .page-wrap {
        .header_box {
            .header-div {
                margin-right: 12px;
            }

            .head_style {
                width: 36px;
                height: 36px;
                border-radius: 50% 50%;
                border: 1px solid #ffffff;
            }

            .paddt5 {
                padding-top: 5px;
            }

            .icon-18 {
                // max-width: 48px;
                color: #daf0fe;
                padding: 0 4px;
                height: 18px;
                background: linear-gradient(135deg, #414649 0%, #222425 100%);
                border-radius: 4px;
                line-height: 20px;
                margin-left: 8px;
                display: flex;
                align-items: center;
            }
        }

        .setting {
            position: relative;
            margin-right: 10px;

            img {
                width: 24px;
                height: 24px;
            }

            .right_circle {
                position: absolute;
                left: 32rpx;
                top: 0px;
                padding: 0 2px;
                min-width: 24rpx;
                height: 24rpx;
                font-weight: 400;
                font-size: 14rpx;
                color: #ffffff;
                line-height: 20rpx;
                text-align: center;
                font-style: normal;
                background: #ff0710;
                border-radius: 12rpx;
                border: 1px solid #ffffff;
            }
        }

        .card-area {
            margin-top: 17px;
            display: flex;
            justify-content: center;
            position: relative;

            img {
                width: 329px;
                height: 208px;
                border-radius: 16px;
                // border: 1px solid red;
            }

            .cardInfo {
                width: 329px;
                height: 44px;
                background: rgba(0, 0, 0, 0.2);
                border-radius: 0px 0px 16px 16px;
                backdrop-filter: blur(10px);
                position: absolute;
                bottom: 0;
                padding-left: 15px;
                line-height: 44px;
            }
        }

        .centent-area {
            background-image: linear-gradient(180deg, rgba(242, 243, 245, 0) 0%, #ffffff 100%);
            padding: 16px 25px;

            .hei28 {
                // height: 28px;
                // line-height: 28px;
                padding-top: 8px;
                // font-weight: bold;
            }

            .cardAndVoucherWrapBox {
                width: 100%;
                padding: 19px 0 0;

                .cardAndVoucherWrap {
                    .cardAndVoucher-content {
                        display: flex;
                        justify-content: space-between;

                        .cardAndVoucher-content-item {
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;

                            .image {
                                width: 34px;
                                height: 34px;
                                background: #efeff4;
                                border-radius: 50%;
                                // margin: 20px 18px 9px 18px;
                                margin: 0 auto;

                                img {
                                    width: 34px;
                                    height: 34px;
                                }
                            }

                            .textName {
                                height: 17px;
                                font-size: 15px;
                                font-weight: 400;
                                color: #666666;
                                margin: 9px 0 0;
                            }

                            .textNum {
                                height: 21px;
                                font-size: 20px;
                                // font-weight: bold;
                                color: #333333;
                                margin-top: 7.5px;
                                margin-bottom: 10px;
                            }
                        }
                    }
                }
            }
        }

        .zj-banner {
            // background-image: linear-gradient(180deg, rgba(242, 243, 245, 0) 0%, #ffffff 100%);
        }

        .card-area-bottom {
            padding-bottom: 42px;

            .zj-banner {
                // transform: translateY(5px);
            }
        }
    }

    .icon-24 {
        width: 24px;
        height: 24px;
    }

    .marrl-18-16 {
        margin: 0 18px 0 16px;
    }

    .mar12 {
        margin: 12px 12px 0;
    }

    .padd12 {
        padding: 13px;
    }

    .marl9 {
        margin-left: 9px;
    }

    .paddb12 {
        padding-bottom: 12px;
        align-items: baseline;

        .text {
            margin-left: 5px;
            // margin-bottom: 2px;
        }
    }

    .paddt30 {
        padding-top: 30px;
    }

    .marl3 {
        margin-left: 3px;
    }
}

.memberName {
    max-width: 170px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.align-items {
    align-items: center;
}

.line_height {
    line-height: 21px;
}
</style>
