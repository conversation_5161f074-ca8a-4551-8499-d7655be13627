<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <view class="page-union">
        <img v-if="numberOfPages != 1" :style="TOP" alt class="header_icon" src="../../../../static/black-close.png" @click="back()" />
        <image class="transferAccountBg_style" :src="transferAccountBg" alt=""></image>
        <div class="content_div">
            <view class="container">
                <!-- <image class="logo" :src="logo"></image>
                <view class="title">能源e站</view>
                <view class="subtitle">中国石油官方服务</view> -->
                <image class="logo_style" :src="logo"></image>
                <view class="">
                    <view class="title">能源e站</view>
                    <view class="text">e享加油生活</view>
                </view>
            </view>
            <view class="agreement_div">
                <view class="select_div">
                    <image v-if="select" :src="empty" class="" @click="select = false"></image>
                    <!-- <image class="" :src="empty" v-if="select" @click="select = false"></image> -->
                    <image class="" :src="successSel" @click="select = true" v-else></image>
                </view>
                <view class="agreement_text" @click="select = !select"
                    >我已阅读并同意能源e站<view class="font-12 color-E64F22 weight-400 agreement_name" @click.stop="clickXieyi(17)"
                        >《用户协议》</view
                    >和<view class="font-12 color-E64F22 weight-400 agreement_name" @click.stop="clickXieyi(63)">《隐私政策》</view></view
                >
            </view>
            <div v-if="!isLogin && !loginLoading">
                <!--@click="smsVerificationCodeLogin"-->
                <getPhoneNumber v-if="!select" @loginOver="loginOver">
                    <div class="primary-btn2 btnStyle">授权登录</div>
                </getPhoneNumber>
                <button v-if="select" class="primary-btn2" @click="onCheckAgree()">授权登录</button>
            </div>
            <div class="loading-box" v-if="loginLoading">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        </div>
        <zj-show-modal>
            <div style="display: inline"
                >登录能源e站，请先阅读并同意能源e站<view
                    class="font-12 color-E64F22 weight-400 agreement_name"
                    @click="clickXieyi(17)"
                    >《用户协议》</view
                ><view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view></div
            >
        </zj-show-modal>
        <Privacy v-if="privacyIsShow"></Privacy>
        <loginRiskControl v-if="realNameDialogFlag || facePop" @verificationPassed="verifSuccess"></loginRiskControl>
        <view class="footer">如需帮助，可联系：956100</view>
    </view>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import Privacy from '@/components/privacy/main.vue';
import loginRiskControl from '../../../../components/loginRiskControl/main.vue';
import logo from '@/static/img/cnpc-logo.png';
import transferAccountBg from '@/static/transferAccount-bg.png';
import empty from '@/static/empty.png';
import successSel from '@/static/successSel.png';
import { validWechatCouponPhone } from '../../../../s-kit/js/v3-http/https3/wallet';
const { baseType } = require('../../../../../project.config');
// #ifdef MP-WEIXIN
import getPhoneNumber from '../../../../components/loginV3/getPhoneNumber.vue';
import store from '../../../../store';
import Layer from '@/s-kit/js/layer';
import { userAgreement } from '../../../../s-kit/js/v3-http/https3/user';
// #endif
// 创建一个指向项目根目录附近（假设pages.json所在位置合理）的模块上下文，用于找到pages.json文件
import pagesJson from '@/pages.json';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        // #ifdef MP-WEIXIN
        loginRiskControl,
        Privacy,
        getPhoneNumber,
        // #endif
    },
    data() {
        return {
            logo,
            title: '登录', // 标题
            type: 0, // 1 登录 2 注册
            phone: '', // 一键登录手机号
            userInfo: null,
            encUserInfo: null,
            agree: false,
            isLogin: false,
            buttonGrayedOut: true,
            transferParameters: '',
            riskType: '',
            query: {},
            GZHparams: '',
            loginData: {},
            transferAccountBg,
            empty,
            successSel,
            select: true,
            systemBar: '',
            numberOfPages: -1,
            option: {},
            isSubUrl: '',
            start: '',
            end: '',
        };
    },
    computed: {
        ...mapState({
            token: state => state.token,
            token3: state => state.token3,
            privacyIsShow: state => state.privacy.privacyIsShow,
            realNameDialogFlag: state => state.thirdLogin.realNameDialogFlag,
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            officialAccountParams: state => state.location.officialAccountParams,
            facePop: state => state.thirdIndex.facePop,
            newMember: state => state.thirdLogin.newMember,
            loginLoading: state => state.thirdLogin.loginLoading,
        }),
        TOP() {
            return `margin-top: ${14 + Number(this.systemBar)}px`;
        },
    },
    onLoad() {},
    mounted() {
        // this.start = performance.now();
        this.$store.dispatch('initLocationV3_app', {
            // #ifdef MP-WEIXIN
            type: 'onlyLocation',
            // #endif
        });
        // 获取系统导航栏高度
        let systemInfo = uni.getSystemInfoSync();
        this.systemBar = systemInfo.statusBarHeight;
        if (this.token3 || this.token) {
            this.isLogin = true;
        } else {
            this.isLogin = false;
        }
        // 获取跳转过来的参数
        this.option = this.$mp.query;
        if (this.option?.refer) {
            this.$store.commit('setOutsideRefer', this.option.refer);
        }
        console.log(this.option, '===option中转页===');
        if (this.option?.router) {
            this.transferParameterProcessing(this.option);
            // 自动登录
            this.automaticLogon();
        } else {
            this.oldTransferParameterProcessing(this.option, () => {
                // 自动登录
                this.automaticLogon();
            });
        }
        // 手机号验证码登录，登陆成功后重新出发中转页方法
        this.$bus.$on('callbackLogin', res => {
            this.loginOver(JSON.parse(res));
        });
    },
    onShow() {},
    methods: {
        // 微信商户券核销插件，更换账号
        wxVerificationPlugin(res, authState = '', callBack = {}) {
            // 版本号判断，若 v1 > v2 则返回值为 1，v1 = v2 则返回值为 0
            function compareVersion(v1, v2) {
                v1 = v1.split('.');
                v2 = v2.split('.');
                var len = Math.max(v1.length, v2.length);
                while (v1.length < len) {
                    v1.push('0');
                }
                while (v2.length < len) {
                    v2.push('0');
                }
                for (var i = 0; i < len; i++) {
                    var num1 = parseInt(v1[i]);
                    var num2 = parseInt(v2[i]);
                    if (num1 > num2) {
                        return 1;
                    } else if (num1 < num2) {
                        return -1;
                    }
                }
                return 0;
            }
            const checkVersion = () => {
                const systemInfo = wx.getSystemInfoSync();
                //checkVersion版本检查：微信>=8.0.30&小程序版本库 >= 2.28.1
                let versionJudg = compareVersion(systemInfo.SDKVersion, '2.28.1') >= 0 && compareVersion(systemInfo.version, '8.0.30') >= 0;
                return versionJudg;
            };
            let callBackFun = {
                success: e => {
                    console.log(e, '核销插件返回成功');
                },
                fail: e => {
                    console.log(e, '核销插件返回失败');
                },
                complete: e => {
                    console.log(e, '核销插件返回总是');
                },
            };
            callBackFun = Object.assign(callBackFun, callBack);
            let params = {
                businessType: 'wxpayCouponUse',
                extraData: {
                    couponCode: res.couponCode,
                    stockId: res.stockId,
                    authState: authState,
                },
                success: callBackFun.success,
                fail: callBackFun.fail,
                complete: callBackFun.complete,
            };
            if (wx.openBusinessView && checkVersion()) {
                wx.openBusinessView(params);
            } else {
                store.dispatch('zjShowModal', {
                    title: '提示',
                    content: '当前微信版本过低,请升级微信到最新版本',
                    success: res => {
                        if (res.confirm) {
                            console.log('登录用户点击确定');
                        } else if (res.cancel) {
                            console.log('登录用户点击取消');
                        }
                    },
                });
            }
        },
        traverseSubPackagesRoutes(keyword) {
            return new Promise(resolve => {
                const targetDirectory = 'packages/third-helpShare'; // 指定要查找分包的目录，可根据实际情况修改
                const subPackages = pagesJson.subPackages;
                console.log(subPackages, 'subPackages');
                let fullPath;
                if (subPackages && subPackages.length > 0) {
                    subPackages.forEach(subPackage => {
                        const root = subPackage.root;
                        console.log(root, 'root===');
                        // // 判断分包根目录是否在指定目录下
                        if (root.startsWith(targetDirectory)) {
                            const packagePages = subPackage.pages;
                            console.log(packagePages, 'packagePages=====');
                            if (packagePages && packagePages.length > 0) {
                                // 使用find方法查找满足条件的页面元素
                                const foundPage = packagePages.find(page => page.path.includes(keyword));
                                if (foundPage) {
                                    // 拼接完整的路由路径，格式为：分包根目录名/页面路径
                                    fullPath = `${root}/${foundPage.path}`;
                                    console.log(fullPath, 'fullPath');
                                    // 如果需要收集所有匹配的路由路径，可以取消下面这行的注释，将路径添加到allRoutes数组
                                    // allRoutes.push(fullPath);
                                    resolve(fullPath);
                                } else {
                                    resolve(fullPath);
                                }
                            }
                        }
                    });
                }
            });
        },
        // 自动登录
        async automaticLogon() {
            let res = await this.$store.dispatch('init', { type: '4' });
            await this.loginOver(res);
        },
        // 登录结束
        async loginOver(res) {
            if (res.success) {
                this.loginData = res.data;
                console.log(res, '中转页res');
                if (this.option?.router) {
                    let reLaunch = async () => {
                        if (await this.checkWalletStatus(this.option)) return;
                        console.log(this.transferParameters, 'transferParameters');
                        uni.reLaunch({
                            url: this.transferParameters,
                        });
                    };
                    if (this.option.action == 'wxpayCouponUse' && this.option.couponCode && this.option.stockId) {
                        let time = JSON.parse(res?.data?.postLoginActionJsonStr)?.registerFlag ? 2000 : 0;
                        uni.showLoading();
                        setTimeout(async () => {
                            // 调接口判断券用户与登录用户是否一致
                            let res = await validWechatCouponPhone({ outCouponNo: this.option.couponCode }, { isCustomErr: true });
                            uni.hideLoading();
                            if (res && res.success) {
                                if (!res.data) {
                                    this.$store.dispatch('setOfficialAccountParams', '');
                                    // 账号不一致调用插件
                                    this.wxVerificationPlugin(this.option, 'invalidCredentials', {
                                        complete: e => {
                                            console.log(e, '核销插件返回总是');
                                            if (e?.extraData?.action == 'switchAccount') {
                                                uni.reLaunch({
                                                    url: '/packages/third-my-center/pages/setting/main',
                                                });
                                            } else {
                                                uni.reLaunch({
                                                    url: '/pages/thirdHome/main',
                                                });
                                            }
                                        },
                                    });
                                    return;
                                } else {
                                    reLaunch();
                                }
                            } else {
                                reLaunch();
                            }
                        }, time);
                    } else {
                        reLaunch();
                    }
                } else if (this.isSubUrl) {
                    this.oldTransferParameterProcessing(this.option);
                } else {
                    this.$store.dispatch('jumpToOfficialAccount', res.data);
                }
                this.$sKit.mpBP.tracker('中转页', {
                    seed: 'transferPageBiz',
                    pageID: 'transferPage',
                    loginInfo: encodeURIComponent(JSON.stringify(res)),
                    options: encodeURIComponent(JSON.stringify(this.option)),
                    memberNo: res.data.memberNo,
                    accessToken: res.data.accessToken,
                    loginType: res.data.loginType,
                    openId: res.data.openId,
                });
            } else {
                this.$store.commit('setLoginButtonGrayedOut', true);
                this.isLogin = false;
                this.$store.commit('setLoginLoading', false);
                if (this.option.action == 'wxpayCouponUse' && this.option.couponCode && this.option.stockId) {
                    // 账号不一致调用插件
                    this.wxVerificationPlugin(this.option, 'unauthenticated');
                }
            }
            // let userId = uni.getStorageSync('userId');
            // let tokenInfo = uni.getStorageSync('tokenInfo');
            // if (!res.success) {
            //     // obj.flag = JSON.stringify(res);
            // }
            // // console.log(obj, 'obj====');
        },
        // 新的跳转方法
        async transferParameterProcessing(option) {
            if (option && option?.router) {
                let urlStr = option?.router;
                let optionObject = this.$sKit.commonUtil.deepCopy(option);
                // 传入路由，看跳转的页面是否需要开卡后才能跳转
                let tokenInfo = uni.getStorageSync('tokenInfo');
                if (tokenInfo.accessToken) {
                    if (await this.checkWalletStatus(option)) return;
                }
                // 匹配埋点参数
                if (option?.refer) {
                    optionObject.refer = option.refer;
                } else {
                    optionObject.refer = this.splicingBuriedPointParameters(option?.router);
                }
                delete optionObject.router;
                // 拼接option的参数
                console.log(urlStr, 'urlStr==');
                this.transferParameters = urlStr + '?data=' + encodeURIComponent(JSON.stringify(optionObject));
                await this.$store.dispatch('setOfficialAccountParams', this.transferParameters);
                // await this.$store.dispatch('jumpToOfficialAccount', data);
                // uni.reLaunch({
                //     url: this.transferParameters,
                // });
                console.log(this.transferParameters, '路由参数拼接');
            }
        },
        // 老的跳转方法
        async oldTransferParameterProcessing(option, callBack) {
            if (option.q) {
                let decodedQ = decodeURIComponent(option.q);
                console.log(decodedQ, 'decodedQ===');
                this.transferParameters = decodedQ;
                if (['luckyDraw', 'couponCollection', 'fissionSharing'].includes(option.page) && decodedQ.includes('flowNo')) {
                    let flowNoStr = this.$sKit.commonUtil.parseUrl(decodedQ, 'flowNo');
                    console.log(flowNoStr, '营销参数');
                    this.transferParameters = `${option.page}#;flowNo=${flowNoStr}`;
                } else if (['hdshare', 'helpShare'].includes(option.page)) {
                    // 获取当前链接上的所有的参数，将字符串变为JSON
                    let activityJson = this.$sKit.commonUtil.extractKeyValuePairs(decodedQ);
                    console.log(activityJson, 'activityJson===');
                    // 使用 JSON 方法进行深拷贝
                    let mergeJson = Object.assign({}, JSON.parse(JSON.stringify(option)), JSON.parse(JSON.stringify(activityJson)));
                    console.log(mergeJson, '合并完mergeJson===');
                    delete mergeJson.q;
                    delete mergeJson.page;
                    console.log(mergeJson, '删除后mergeJson===');
                    // 将对象转变为字符串形式：activityClass=zllb-1021&test1=1&test2=2
                    let str = this.$sKit.commonUtil.convertToRouteString(mergeJson);
                    console.log(str, '字符串mergeJson===');
                    console.log(str, `${option.page}=====扫二维码助力裂变`);
                    this.transferParameters = option.page;
                    this.$store.commit('setActivityStr', str);
                }
            } else {
                if (option.page == 'helpShare') {
                    let activityJson = JSON.parse(JSON.stringify(option));
                    delete activityJson.page;
                    // 将对象转变为字符串形式：activityClass=zllb-1021&test1=1&test2=2
                    let str = this.$sKit.commonUtil.convertToRouteString(activityJson);
                    console.log(activityJson, `${option.page}=====助力裂变`);
                    this.$store.commit('setActivityStr', str);
                    this.transferParameters = option.page;
                } else {
                    // 登陆成功后如果 this.isSubUrl不存在，再去调用匹配方法
                    if (!this.isSubUrl) {
                        // 使用this.option.page的值去和路由路径匹配
                        this.isSubUrl = await this.traverseSubPackagesRoutes(this.option.page);
                    }
                    // 这里定义isSubUrl的值的意思是：当前pages的值是分包名字，当用户未登录的状态下，用户点击登录且成功后，作为再次调用oldTransferParameterProcessing方法的标识.isSubUrl后续页面跳转也使用
                    // 如果为false或者是空在登录成功后则不再执行当前方法，执行loginOver中的其他if和else
                    console.log(this.isSubUrl, 'this.isSubUrl====');
                    // 如果存在代表pages的value是分包名字
                    if (this.isSubUrl) {
                        // 检查用户是否是登录状态
                        const { accessToken, expiresIn } = uni.getStorageSync('tokenInfo');
                        let times = new Date().getTime();
                        console.log(expiresIn && times > Number(expiresIn), times, expiresIn, 'token过期了吗');
                        // token存在并且 token时间未过期的情况下
                        if (accessToken && expiresIn && !(times > Number(expiresIn))) {
                            console.log('token未过期，向下执行跳转');
                            await this.yunying(this.option);
                        } else {
                            console.log('token过期，向下执行callback');
                        }
                    } else {
                        // 如果没有匹配成功代表pages的值不是分包名，执行其他跳转逻辑
                        this.transferParameters = option.page;
                    }
                }
            }

            // 微信支付活动页携带的参数为false标识不需要登录
            this.$store.commit('setWxPayIsLoginFlag', option.isLoginFlag == 'false' ? false : true);
            // APP.vue中已经存储了值，不加判断，未登录情况下跳转到当前页面，会将开卡的值清掉，导致员工再使用邀请开卡的场景下只能记录注册，不能记录开卡
            if (this.officialAccountParams != 2) {
                this.$store.dispatch('setOfficialAccountParams', this.transferParameters);
            }
            if (option.zfPhone) {
                this.$store.commit('setZfPhone', option.zfPhone);
            }
            // 支付活动领完券需要重新调用3.0获取token判断用户是否变成了30用户
            if (option.logInAgain == 'true') {
                this.$store.commit('setLogInAgain', true);
                this.$store.commit('setWxPayIsLoginFlag', Boolean);
                callBack();
                return;
            }
            if (this.transferParameters && this.token && !this.newMember) {
                // 根据公众号和小程序跳转携带的参数以及是否存在3.0token跳转2.0或者3.0相应页面
                if (this.transferParameters == 'dzk' && this.cardList.length > 0) {
                    let cardRes = this.cardList.filter(item => item.cardType == 1);
                    console.log(cardRes, 'cardRes====中转页对是否有电子卡进行判断');
                    if (cardRes.length > 0 && cardRes[0].cardType == 1) {
                        this.$store.dispatch('setOfficialAccountParams', 'ykgl');
                    }
                }
                // 需要修改
                // this.$store.dispatch('jumpToOfficialAccount', this.token3);
            }
            // 优途小程序跳转存储手机号在首页提示用户手机号是否相等
            if (option.phone) {
                this.$store.commit('setYtPhone', option.phone);
            }
            // packages/transferAccount/pages/home/<USER>
            console.log(option.page, option.isLoginFlag, '中转页参数');
            // 微信支付完成后的页面配置活动页跳转到当前小程序
            if (option.page === 'zf') {
                let linkUrl = '';
                console.log(option, 'option====');
                console.log(baseType, '生产 ======== 测试');
                // // 微信支付完成后的页面配置活动页跳转到当前小程序
                // if (option.page == 'zf' && option.isLoginFlag == 'false') {
                //     let linkUrl = '';
                //     console.log(baseType, '生产 ======== 测试');
                //     if (option.activityType && option.activityClass) {
                //         let com = baseType == 'prd' || baseType == 'gray' ? `https://yyhd.kunlunjyk.com` : `https://yyhdnp.kunlunjyk.com`;
                //         linkUrl = `${com}/${option.activityType}/?activityClass=${option.activityClass}`;
                //     } else {
                //         linkUrl = decodeURIComponent(option.url);
                //         if (option.token) {
                //             // linkUrl先判断有没有问号  没有?token= 有 &token=
                //             linkUrl = `${linkUrl}${linkUrl.includes('?') ? '&' : '?'}token=${option.token}`;
                //         }
                //         if (option.sign) {
                //             // linkUrl先判断有没有问号  没有?sign= 有 &sign=
                //             linkUrl = `${linkUrl}${linkUrl.includes('?') ? '&' : '?'}sign=${option.sign}`;
                //         }
                //     }
                //     uni.reLaunch({
                //         url: `/packages/web-view/pages/home/<USER>
                //     });
                //     this.$store.dispatch('setOfficialAccountParams', '');
                //     return;
                // } else if (option.isLoginFlag == 'true') {
                //     let linkUrl = decodeURIComponent(option.url);
                //     this.$store.dispatch('setOfficialAccountParams', linkUrl);
                // }
                if (option.isLoginFlag === 'false') {
                    if (option.activityType && option.activityClass) {
                        linkUrl = this.buildLinkUrl(baseType, option);
                    } else {
                        linkUrl = decodeURIComponent(option.url);
                        if (option.token) {
                            // linkUrl先判断有没有问号  没有?token= 有 &token=
                            linkUrl = `${linkUrl}${linkUrl.includes('?') ? '&' : '?'}token=${option.token}`;
                        }
                        if (option.sign) {
                            // linkUrl先判断有没有问号  没有?sign= 有 &sign=
                            linkUrl = `${linkUrl}${linkUrl.includes('?') ? '&' : '?'}sign=${option.sign}`;
                        }
                    }
                    uni.reLaunch({
                        url: `/packages/web-view/pages/home/<USER>
                    });
                    this.$store.dispatch('setOfficialAccountParams', '');
                    return;
                } else if (option.isLoginFlag === 'true' && !option.url && option.activityClass) {
                    linkUrl = this.buildLinkUrl(baseType, option);
                    this.$store.dispatch('setOfficialAccountParams', linkUrl);
                } else if (option.isLoginFlag === 'true' && option.url) {
                    linkUrl = decodeURIComponent(option.url);
                    this.$store.dispatch('setOfficialAccountParams', linkUrl);
                }
            }
            console.log('中转页执行了吗');
            // 邀请人记录
            if (option?.flowNo && option?.memberNo && option?.shareId) {
                console.log('营销小程序裂变=====', option);
                this.$store.dispatch('setOfficialAccountParams', this.option);
            }
            callBack();
            // this.$store.dispatch('jumpToOfficialAccount', data);
        },
        yunying(option) {
            return new Promise((resolve, reject) => {
                if (option.page) {
                    let activityJson = JSON.parse(JSON.stringify(option));
                    delete activityJson.page;
                    // 将对象转变为字符串形式：activityClass=zllb-1021&test1=1&test2=2
                    let str = this.$sKit.commonUtil.convertToRouteString(activityJson);
                    console.log(activityJson, `${option.page}===微信分包跳转参数`);
                    this.$store.commit('setActivityStr', str);
                    // let url = this.traverseSubPackagesRoutes(option.page);
                    let url = this.isSubUrl;
                    if (url) {
                        // console.log(url, '测试url');
                        const { accessToken, memberNo, openId } = uni.getStorageSync('tokenInfo');
                        console.log(
                            `/${url}?${str}&gsmsToken=${accessToken}&clientCode=C12&userId=${memberNo}&openId=${openId}`,
                            '分包需要登录拼接的参数',
                        );
                        uni.reLaunch({
                            url: `/${url}?${str}&gsmsToken=${accessToken}&clientCode=C12&userId=${memberNo}&openId=${openId}`,
                        });
                        this.isSubUrl = '';
                        // this.end = performance.now();
                        // console.log(`执行时间: ${this.end - this.start} 毫秒`);
                    } else {
                        // option.page配置的参数在当前分包下不存在路由页面,没有匹配上将setActivityStr清空防止页面不跳转回首页
                        this.$store.commit('setActivityStr', '');
                        this.transferParameters = option.page;
                        resolve();
                    }
                } else {
                    resolve();
                }
            });
        },
        // 未勾选协议点击登录按钮的弹窗
        onCheckAgree() {
            this.$store.dispatch('zjShowModal', {
                confirmText: '同意',
                cancelText: '我再想想',
                cancelColor: '#666666',
                confirmColor: '#FF3E00',
                success: async res => {
                    if (res.confirm) {
                        this.select = false;
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 勾选协议
        clickXieyi(values) {
            // uni.navigateTo({
            //     url: `/packages/setting/pages/agreement/main?value=${type}`,
            // });
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('1', '微信小程序隐私协议');
            }
        },
        // 拼接埋点参数
        splicingBuriedPointParameters(url) {
            let routerList = [
                // e享卡充值页面
                { '/packages/third-remaining-sum/pages/third-wallet-recharge/main': { refer: 'r12' } },
                // 会员码页面
                { '/packages/third-scan-code-payment/pages/home-code/main': { refer: 'r30' } },
                // 发票页面
                { '/packages/third-invoice/pages/home/<USER>': { refer: 'r37' } },
                // 电子券页面
                { '/packages/third-coupon-module/pages/coupon-list/main': { refer: 'r42' } },
                // e享卡开卡页面
                { '/packages/third-electronic-wallet/pages/wallet-add-form/main': { refer: 'r04' } },
                // 钱包设置页面
                { '/packages/third-my-wallet/pages/wallet-setting/main': { refer: 'r58' } },
                // 钱包页面
                { '/packages/third-my-wallet/pages/home/<USER>': { refer: 'r03' } },
            ];
            let referValue;
            // 遍历routerList查找与url匹配的键并获取对应的refer值
            routerList.forEach(item => {
                for (let key in item) {
                    if (key === url) {
                        referValue = item[key].refer;
                    }
                }
            });
            return referValue;
        },
        // 检查是否需要检测是否开卡
        checkWalletStatus(options) {
            return new Promise(async (resolve, reject) => {
                if (options.chenkWalletStatusFlag) {
                    let res = await this.$store.dispatch('getSetWalletStatus');
                    console.log(res, '测试');
                    if (res.success && res.data) {
                        if (!res.data.status) {
                            await this.$store.dispatch('zjShowModal', {
                                title: '',
                                content: '您还没有开通昆仑e享卡，是否开通？',
                                confirmText: '去开通',
                                cancelText: '取消',
                                confirmColor: '',
                                cancelColor: '',
                                type: '',
                                success: res => {
                                    if (res.confirm) {
                                        console.log('用户点击确定');
                                        let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                                        let params = { refer: 'r08' };
                                        let type = 'reLaunch'; // 默认  uni.navigateTo({})
                                        Layer.useRouter(url, params, type);
                                    } else if (res.cancel) {
                                        console.log('用户点击取消');
                                        uni.reLaunch({
                                            url: '/pages/thirdHome/main',
                                        });
                                    }
                                },
                            });
                            resolve(true);
                        } else {
                            uni.reLaunch({
                                url: '/packages/third-my-wallet/pages/home/<USER>',
                            });
                        }
                    } else if (res.data.status) {
                        uni.reLaunch({
                            url: '/pages/thirdHome/main',
                        });
                    }
                } else {
                    resolve(false);
                }
            });
        },

        // 风控验证通过后重新调用登录接口
        verifSuccess() {
            this.$store.commit('setRealNameDialogFlag', false);
            this.automaticLogon();
        },
        // 左上角返回方法
        back() {
            this.numberOfPages = getCurrentPages().length;
            console.log(this.numberOfPages, 'this.numberOfPages');
            if (this.numberOfPages.length <= 1) {
                console.log('1111111');
                wx.exitMiniProgram({
                    success: res => {
                        console.log(res);
                    },
                });
            } else {
                console.log('2222');
                uni.navigateBack();
            }
        },
        getEnvironment(baseType) {
            return baseType === 'prd' || baseType === 'gray' ? 'https://yyhd.kunlunjyk.com' : 'https://yyhdnp.kunlunjyk.com';
        },
        buildLinkUrl(baseType, option) {
            let com = this.getEnvironment(baseType);
            let linkUrl = `${com}/${option.activityType}/?activityClass=${option.activityClass}`;

            if (option.token) {
                linkUrl += linkUrl.includes('?') ? `&token=${option.token}` : `?token=${option.token}`;
            }

            if (option.sign) {
                linkUrl += linkUrl.includes('?') ? `&sign=${option.sign}` : `?sign=${option.sign}`;
            }

            return linkUrl;
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // 打开PDF
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
        async smsVerificationCodeLogin() {
            if (!this.loginButtonGrayedOut) return;
            // 按钮置灰
            this.$store.commit('setLoginButtonGrayedOut', false);
            // 不能一键登录执行验证码登录
            let url = '/packages/third-new-third-login/pages/login/main';
            // let url = '/packages/third-invoice/pages/invoice-title-form/main';
            let params = {};
            this.$sKit.layer.useRouter(url, params);
            setTimeout(() => {
                this.$store.commit('setLoginButtonGrayedOut', true);
            }, 1000);
        },
    },
};
</script>

<style scoped lang="scss">
.page-union {
    width: 100%;
    background: #ffffff;
    overflow-x: hidden;
    // min-height: 100vh;
    height: 100%;
    position: relative;
    .header_icon {
        position: absolute;
        width: 16px;
        height: 16px;
        left: 16px;
        z-index: 1;
    }
}

.btnStyle {
    width: 100%;
}

.logo {
    width: 140rpx;
    height: 140rpx;
    margin: 200rpx auto 30rpx;
    display: block;
}

.btn-auth-login {
    color: #fff;
    background-color: rgb(51, 107, 225);
    border-radius: 60rpx;

    &:active {
        opacity: 0.7;
    }
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
}

.subtitle {
    font-size: 28rpx;
    color: #888;
    text-align: center;
    margin: 20rpx 0 80rpx;
}

.agreement-box {
    margin-top: 30rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    white-space: nowrap;
    margin-left: -10px;
    margin-bottom: -1px;

    .link {
        color: #1d77cd;
    }
}

.loading-box {
    padding-top: 80rpx;
    display: flex;
    justify-content: center;
    gap: 10rpx;

    .loading-dot {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: #888;
        animation: bolling 1s alternate infinite;
    }
}

.footer {
    position: fixed;
    bottom: 50rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    width: 100%;
}

@keyframes bolling {
    0% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(5px);
    }
}

.transferAccountBg_style {
    width: 100%;
    height: 365px;
    position: absolute;
    z-index: 0;
}
.content_div {
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 152px;
    padding: 0 63px;
}
.logo_style {
    width: 47px;
    height: 45px;
    margin-right: 14px;
}
.container {
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    margin-bottom: 43px;
}

.title {
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
}
.text {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    letter-spacing: 1px;
}
.agreement_div {
    margin-bottom: 16px;
    display: flex;
    .select_div {
        display: inline-block;
        margin-right: 5px;
        image {
            width: 13px;
            height: 13px;
        }
    }
}
.agreement_text {
    width: 94%;
}
.agreement_name {
    display: inline;
}
</style>
