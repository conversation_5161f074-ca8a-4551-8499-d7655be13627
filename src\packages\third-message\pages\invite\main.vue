<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="select-region-class bg-F7F7FB" style="height: 100%">
            <zj-navbar :height="44" title="我的邀请"></zj-navbar>
            <div class="content" v-if="inviteType">
                <div class="content-pic">
                    <img src="../../images/group_cheng.png" alt />
                </div>
                <div class="content-list">
                    <div class="content-list-item">
                        <div class="item-title">
                            <img src="../../images/group_icon_cheng.png" alt />
                            <div style="font-weight: bold">{{ groupName }}</div>
                        </div>
                        <div class="item-line"></div>
                        <div class="content-text" v-if="num == 1">您被邀请加入“能源驿站”群组，加入“能源驿站”群组即可享受如下权益：</div>
                        <div v-if="num == 1">
                            <div class="item-text" v-for="(item, index) in list" :key="index">
                                <div style="display: inline">群组权益{{ index + 1 }}:</div>{{ item.rightsName }}
                            </div>
                        </div>
                        <!-- <div v-if="num == 0" class="have-joined">
                            <img src="static/thirdImg/order/tuikuancg.png" />
                            <div class="text">已同意加入该群组</div>
                        </div>
                        <div v-if="num == 3" class="have-joined">
                            <img src="static/thirdImg/order/yaoqingshixiao.png" />
                            <div class="text">邀请已失效，请联系群组管理员</div>
                        </div> -->
                    </div>
                    <div v-if="num == 1" class="content-list-btn primary-btn" @click="agree"> 同意加入群组</div>
                    <zj-show-modal></zj-show-modal>
                </div>
            </div>
            <div v-else style="padding-top: 61px">
                <img src="../../images/agree_success.png" style="display: block; width: 128px; height: 107px; margin: 0 auto" alt="" />
            </div>
        </div>
    </div>
</template>
<script>
//   import { agreejoin, Joingroup } from './../api/groupbenefits.js'
import { userGroupJoin, userGroupIsGroupMember } from '../../../../s-kit/js/v3-http/https3/preferentialGroup/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'index',
    data() {
        //num-判断展示页面为0的时候展示已同意加入该群组位1的时候展示要加入的群组和按钮
        return {
            list: [],
            groupName: '',
            num: 1,
            showClick: false,
            groupId: '',
            inviteType: true,
        };
    },
    created() {},
    onLoad(option) {
        //接收详情页传参
        this.groupId = JSON.parse(decodeURIComponent(option.data)).id;
    },
    mounted() {
        console.log('邀请页面');
        //调用消息邀请接口
        this.getdata();
    },
    methods: {
        // 获取站内信数据
        async getdata() {
            let id = this.groupId;
            //我的邀请页面初始化根据站内信详情传的id调用接口
            let res = await userGroupIsGroupMember({ groupId: id });
            if (res && res.success) {
                if (res.data.memberStatus == 4) {
                    this.num = 1;
                } else if (res.data.memberStatus == 6) {
                    this.num = 0;
                    this.inviteType = false;
                } else {
                    this.num = 3;
                }
                this.list = res.data.rightsList;
                this.groupName = res.data.groupName;
            }
        },
        //点击同意加入群组
        agree() {
            console.log('同意');
            let that = this;
            this.$store.dispatch('zjShowModal', {
                title: '',
                content: '您被邀请加入“能源驿站”群组',
                confirmText: '同意加入',
                cancelText: '稍后再说',
                confirmColor: '#333333',
                cancelColor: '#666666',
                type: '',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        that.getUserGroupJoin();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 调用同意方法
        async getUserGroupJoin() {
            //
            console.log('同意');
            let id = this.groupId;
            let res = await userGroupJoin({ groupId: id });
            if (res && res.success) {
                this.inviteType = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.select-region-class {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.content {
    position: relative;
    padding-top: 14px;

    .content-pic {
        width: 153px;
        height: 158px;
        margin: 0 auto;
        margin-bottom: 16px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .content-list {
        padding: 0 15px;

        // zoom 1
        // overflow hidden
        // .content_list_item_show
        //   height auto !important
        //   max-height 600px !important
        //   transform-origin center top
        //   transition all 1s
        .content-list-btn {
            height: 44px;
            color: #fff;
            border-radius: 8px;
            line-height: 44px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

        .content-list-item {
            width: 345px;
            // height: 150px;
            // max-height: 150px;
            position: relative;
            // height: 137px;
            overflow: hidden;
            background: #ffffff;
            padding: 0 15px;
            padding-top: 13px;
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-sizing: border-box;

            .have-joined {
                display: flex;
                flex-direction: column;
                align-items: center;

                img {
                    margin-top: 35px;
                    width: 64px;
                    height: 64px;
                    margin-bottom: 20px;
                }

                .text {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333333;
                    margin-bottom: 45px;
                }
            }

            .item-title {
                display: flex;
                align-items: center;
                font-size: 16px;
                font-weight: bold;
                color: #333333;
                margin-bottom: 10px;

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 10px;
                }
            }

            .item-line {
                width: 100%;
                height: 1px;
                background: #eeeeee;
                margin-bottom: 12px;
            }

            .content-text {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: 17px;
                margin-bottom: 13px;
            }

            .item-text:last-child {
                margin-bottom: 0;
            }

            .item-text {
                margin-bottom: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #666666;

                div {
                    color: #333333;
                }
            }
        }
    }
}
</style>
