const fs = require('fs');
const path = require('path');

function getFullPath(_path, _root = 'project') {
    return path.resolve(__dirname, _root, _path);
}

function requireMkSync(_path, _root) {
    const fullPath = getFullPath(_path, _root);
    if (fs.existsSync(fullPath)) {
        return require(fullPath);
    } else {
        return {};
    }
}

module.exports = {
    getFullPath,
    requireMkSync,
};
