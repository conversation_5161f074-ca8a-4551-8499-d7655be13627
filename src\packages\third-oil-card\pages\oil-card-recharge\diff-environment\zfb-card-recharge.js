import { getAliHBPayList, addOrderInfo, identityAuthInfo, userAgreement } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { mixRecharge } from '../../../../../../project.config';

export default {
    // #ifdef MP-ALIPAY
    data() {
        return {
            // 是否使用花呗分期
            useHuaBei: false,
            stagingList: [], // 支持的分期数组
            stagingIndex: 0, // 选中的分期 索引
            isSupportHuaBei: 0, // 默认不支持分期 0 不支持 1 支持
        };
    },

    mounted() {
        console.log('MP-ALIPAY 2.0链路实体卡充值');
    },
    methods: {
        /**
         * @description: 选择花呗相关处理
         */
        async onUseHuaBei() {
            if (this.money < 100)
                return uni.showToast({
                    title: '花呗分期充值金额不能小于100元',
                    icon: 'none',
                });
            this.useHuaBei = !this.useHuaBei;
            this.stagingList = [];
            this.stagingIndex = 0;
            await this.getAliHBPayListFn();
        },

        /**
         * @description: 点击分期切换事件，保存当前的下标
         */
        changeStage(ind) {
            if (this.stagingList.length === 0) return;
            this.stagingIndex = ind;
        },

        // 获取地区充值方式的信息
        getAreaRecharge() {},

        /**
         * @description: 获取花呗分期数据
         */
        async getAliHBPayListFn() {
            // 金额小于100不显示分期
            if (this.money < 100) return this.resetHBData();
            // 未开启花呗分期
            if (!this.useHuaBei) return;
            const data = await getAliHBPayList({ cardno: this.thirdCardItemRecharge.cardNo, amount: this.money });
            // 未获取到数据
            if (!data.Value || Number(data.Data.Status) !== 1) {
                this.resetHBData();
                return uni.showToast({
                    title: '暂不支持分期，请选择其他方式',
                    icon: 'none',
                    duration: 2000,
                });
            }
            this.stagingList = data.Data.StagingList;
            for (let i in this.stagingList) {
                // 优先默认选中分3期的分期选项
                if (Number(this.stagingList[i].StagingNum) === 3) this.stagingIndex = Number(i);
                // 总的手续费为null，就展示每一期手续费，有总手续费就展示 总手续费
                this.stagingList[i].useTotalFee = !!this.stagingList[i].TotalInsFee;
            }
        },

        /**
         * @description: 重置花呗相关数据
         */
        resetHBData() {
            this.stagingIndex = 0;
            this.stagingList = [];
            this.useHuaBei = false;
        },

        /**
         * @description  : 确认充值
         * @param         {Boolean} agreementFlag -是否同意协议
         * @param         {String} payType -支付方式
         * @return        {*}
         */
        async confirmRecharge(e) {
            // 校验是否同意协议，是否选择支付方式，根据支付方式调用不同的支付逻辑
            if (!this.agreementFlag) {
                uni.showToast({
                    title: '请先勾选同意充值协议',
                    icon: 'none',
                });
                return;
            }
            if (this.payType == 'huabei') {
                this.onUseHuaBei();
                return;
            }
            if (this.paymentSelectIndex == '-1' && this.stagingIndex == 0) {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                });
                return;
            }
            if (this.payType == 'card') {
                // this.$refs.inputDialog.open();
                this.stopRecharge()
                return;
            }

            // if (this.stagingIndex == 0) {
            //     uni.showToast({
            //         title: '请选择支付方式',
            //         icon: 'none',
            //     });
            //     return;
            // }
            if (this.payType == 'card') {
                this.$refs.inputDialog.open();
                return;
            }
            let money = this.rechargeMoney();
            if (!money) return;
            // 聚合授权获取身份证号
            const AliUserInfo = await identityAuthInfo();
            if (!AliUserInfo.data || !AliUserInfo.data.identityNo) return;
            const { cppeiLoginInfo: userInfo,openId,memberNo } = (await uni.getStorageSync('tokenInfo')) || {};
            let params = {
                phoneToken: userInfo?.phoneToken,
                openid: openId,
                mobile: userInfo?.phone,
                UserId: memberNo,
                cardNo: this.thirdCardItemRecharge.cardNo,
                cardasn: this.thirdCardItemRecharge.cardNo,
                amount: this.money,
                invoice: '1',
                userip: '',
                name: AliUserInfo.data.realName,
                Channel: '5',
                inCommon: '1',
                FormId: e.detail.formId, // 消息模板 formId
                idNum: AliUserInfo.data.identityNo,
            };

            // 核对分期数据
            if (this.useHuaBei) {
                if (Number(this.money) < 100) return uni.showToast({ title: '花呗分期充值金额不能小于100元', icon: 'none' });
                if (this.stagingList.length === 0) return uni.showToast({ title: '分期数据异常，请稍候重试！', icon: 'none' });

                params = {
                    ...params,
                    issupportstaging: true, // 是否支持分期
                    fq_seller_percent: this.stagingList[this.stagingIndex].InterestFree ? 100 : 0,
                    stagingnum: this.stagingList[this.stagingIndex].StagingNum,
                };
            }

            try {
                const data = await addOrderInfo(params);
                if (!data.Value) return;
                // uni.showToast({ title: data.Info });
                uni.requestPayment({
                    provider: 'alipay', // 支付服务提供商，例如：'wxpay'、'alipay'
                    orderInfo: data.Data.trade_no, // 支付订单信息，具体格式根据支付服务提供商而定
                    success: res => {
                        console.log('支付成功', res);
                        switch (Number(res.resultCode)) {
                            case 9000:
                                this.$sKit.mpBP.tracker('充值', {
                                    seed: 'rechargeBiz',
                                    pageID: 'cardsucessToast', // 页面名
                                    refer: this.thirdCardItemRecharge.refer || '', // 来源
                                    channelID: 'C13', // C10/C12/C13
                                    czMoney: this.money,
                                    address: this.thirdCardItemRecharge.address, // 归属地
                                });
                                uni.showToast({ title: '支付成功' });
                                // todo 成功后流程待定
                                uni.navigateBack({ detail: 1 });
                                // 存储本地的用来刷新卡信息的标识
                                uni.setStorageSync('refreshCardManagement', true);

                                break;
                            case 8000:
                                uni.showToast({ title: '充值遇到未知异常，请检查是否到账', icon: 'none' });
                                break;
                            case 4000:
                                uni.showToast({ title: '充值遇到未知异常，请稍后再试', icon: 'none' });
                                break;
                            case 6001:
                                uni.showToast({ title: '充值取消', icon: 'none' });
                                break;
                            case 6002:
                                uni.showToast({ title: '充值遇到未知异常，请稍后再试', icon: 'none' });
                                break;
                            default:
                                uni.showToast({ title: JSON.stringify(res), icon: 'none' });
                                break;
                        }
                    },
                    fail: err => {
                        console.log('支付失败', err);
                        console.error(err);
                        uni.showToast({ title: '充值遇到未知异常，请稍后再试', icon: 'none' });
                    },
                });
            } catch (err) {}
        },
        /**
         * 匹配充值金额
         * @returns {*}
         */
        rechargeMoney() {
            let money = '';
            if (this.moneyType == '1') {
                money = '200';
            } else if (this.moneyType == '2') {
                money = '300';
            } else if (this.moneyType == '3') {
                money = '500';
            } else if (this.moneyType == '4') {
                money = '800';
            } else if (this.moneyType == '5') {
                money = '1000';
            } else if (this.moneyType == '6') {
                money = '2000';
            } else {
                money = this.money;
            }
            if (!money) {
                uni.showToast({
                    title: '请输入充值金额',
                    icon: 'none',
                });
                return;
            }
            if (money < (mixRecharge || 1)) {
                uni.showToast({
                    title: '充值金额不能小于一元',
                    icon: 'none',
                });
                return;
            }
            return money;
        },
        /**
         * @description  : 点击查看协议
         * @param         {string} type -协议类型
         * @param         {string} cityName -城市编码
         * @param         {string} name -协议名称
         * @param         {Function} checkPDF -打开协议
         * @return        {*}
         */
        async goToagreement() {
            let params = {
                type: '1',
                cityName: '全国',
                name: 'App充值协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // uni.navigateTo({
                    //     url: '/mpaas/packages/web-view/pages/home/<USER>' + encodeURIComponent(userAgreementRes.data.fileUrl),
                    // });
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            } else {
                uni.showToast({ title: userAgreementRes.message });
            }
        },
    },
    // #endif
};
