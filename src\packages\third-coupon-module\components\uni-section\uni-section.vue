<!-- 可折叠面板组件 LjCollapse -->
<template>
	<view class="LjCollapse" :style="{ backgroundColor: bgColor, width: width }">
	  <!-- 头部点击区域 -->
	  <view 
		class="LjCollapseHead" 
		:style="{ color: headColor, borderLeftColor: borderColor }"
		@click="toggleCollapse"
	  >
		<text>{{ title }}</text>
		<!-- 箭头图标 -->
		<view class="arrow-container">
		  <view 
			class="arrow-icon"
			:class="{ 'arrow-down': !isCollapsed, 'arrow-right': isCollapsed }"
		  ></view>
		</view>
	  </view>
	  
	  <!-- 内容区域使用动画 -->
	  <view 
		class="collapse-content"
		:style="contentStyle"
		@transitionend="onTransitionEnd"
	  >
		<slot v-if="!isCollapsed"></slot>
	  </view>
	</view>
  </template>
  
  <script>
  export default {
	name: "LjCollapse",
	props: {
	  title: {
		type: String,
		default: "标题"
	  },
	  headColor: {
		type: String,
		default: "orange"
	  },
	  borderColor: {
		type: String,
		default: "orange"
	  },
	  bgColor: {
		type: String,
		default: "rgba(41, 41, 41, 0.589)"
	  },
	  width: {
		type: String,
		default: "100%"
	  },
	  isCollapse: {
		type: Boolean,
		default: true
	  }
	},
	data() {
	  return {
		isCollapsed: this.isCollapse,
		contentHeight: 0,
		transition: 'none'
	  };
	},
	computed: {
	  contentStyle() {
		return {
		  height: this.isCollapsed ? '0px' : `${this.contentHeight}px`,
		  overflow: 'hidden',
		  transition: this.transition
		};
	  }
	},
	methods: {
	  toggleCollapse() {
		this.transition = 'height 0.2s ease-in-out';
		this.isCollapsed = !this.isCollapsed;
		
		// 获取内容实际高度
		if (!this.isCollapsed) {
		  this.$nextTick(() => {
			const query = uni.createSelectorQuery().in(this);
			query.select('.collapse-content').boundingClientRect(data => {
			  this.contentHeight = data.height;
			}).exec();
		  });
		}
	  },
	  onTransitionEnd() {
		this.transition = 'none';
	  }
	}
  };
  </script>
  
  <style scoped>
  .LjCollapse {
	margin: 10px 0;
	color: white;
	background-color: rgba(41, 41, 41, 0.589);
	padding: 10px;
  }
  
  .LjCollapseHead {
	display: flex;
	justify-content: space-between;
	align-items: center;
	line-height: 25px;
	border-left: 3px solid;
	padding-left: 10px;
	font-weight: bold;
	font-size: 15px;
  }
  
  .arrow-container {
	display: flex;
	align-items: center;
	height: 100%;
  }
  
  .arrow-icon {
	width: 7px;
	height: 7px;
	border-top: 2px solid #ffa500;
	border-right: 2px solid #ffa500;
	transition: transform 0.2s ease;
  }
  
  .arrow-down {
	transform: rotate(135deg);
  }
  
  .arrow-right {
	transform: rotate(45deg);
  }
  
  .collapse-content {
	overflow: hidden;
  }
  </style>