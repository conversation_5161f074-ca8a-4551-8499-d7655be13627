import { singleton } from '../singleton';
import Config from '../third-config.js';
import cnpcBridge from '../v3-native-jsapi/cnpcBridge.js';
import Store from '../../../store/index';
import { jwtTokenvalue } from '../v3-http/post.js';
let isHarmony = false;
class mpaasKeyBordPlugin {
    // #ifdef MP-MPAAS
    //   ref;
    //   sRef;
    constructor() {}
    initRef() {
        //   uni.showLoading({
        //       title: '加载中',
        //       mask: true,
        //   });
        return new Promise(async (resolve, reject) => {
            let gsmsToken;
            let systemInfo = my.getSystemInfoSync();
            console.log('system---密码盘键', systemInfo);
            if (systemInfo && (systemInfo.system.includes('Harmony') || systemInfo.platform == 'Harmony')) {
                isHarmony = true;
            }
            // 获取用户信息
            if (isHarmony) {
                let userTokenInfo = await cnpcBridge.getUserTokenInfo();
                gsmsToken = userTokenInfo.gsmsToken;
            } else {
                gsmsToken = await jwtTokenvalue();
            }
            let commonArgs = await cnpcBridge.getCommonArgs();
            // 设备信息
            my.loadPlugin({
                plugin: `${Config.app.mpassplugins.keyboardPlugin.provider}@*`,
                success: async () => {
                    /**
                 * appid 必填 对外开放平台分配的应用编码appid
                     terminalId 必填 终端唯一ID
                    token 必填 网关验证的token
                    clientCode 必填 网关对应的渠道
                * */
                    let params = {
                        appid: Config.mpaasAppId,
                        terminalId: commonArgs.deviceId,
                        token: gsmsToken,
                        clientCode: Config.clientCode,
                    };
                    // let params = {
                    //     appid:"PRI5A96DC5141050",
                    //     terminalId:'f36f2f6c39fb424ea27a7ea2ea123930',
                    //     token:'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQtY29kZSI6IkMxMCIsImdyYW50X3R5cGUiOiJkZXZpY2VfZmFjZSIsInVzZXJfaWQiOjEzNTIwMjY2NTUsInVzZXJfbmFtZSI6Im1icjE3NjExMzc2NzcwMzMzIiwicG9zdExvZ2luQWN0aW9uSnNvblN0ciI6IntcImxvbmdUaW1lTm90TG9naW5cIjpudWxsLFwicmx5ekZsYWdcIjpudWxsLFwiaXNPcGVuRmluZ2VyT3JGYWNlXCI6bnVsbCxcImJpbmRQaG9uZUZsYWdcIjp0cnVlfSIsInNjb3BlIjpbInNlcnZlciJdLCJleHAiOjE3MjQyMjI3OTIsImF1dGhvcml0aWVzIjpbIk9VVEVSIl0sImp0aSI6IjkwYjIzYmQ5LTE1MmItNDUyNC04NTgyLTVhNWZjNmE0Y2IyNCIsInJlYWxuYW1lIjoi5Y-k5bq35bq3QDMifQ.0X9p-DhW2DhkGmSCeh4rV76P3eMZ_iyrHnjdwFA3iyI',
                    //     clientCode:'C10',
                    // }
                    console.log('plugin---params', params);
                    try {
                        const keyboardPlugin = requirePlugin(`dynamic-plugin://${Config.app.mpassplugins.keyboardPlugin.provider}`);
                        const passwordInstance = keyboardPlugin.passwordInstance.createComponent();
                        const result = await passwordInstance.init(params);
                        console.log(result, 'result----keyboardPlugin');
                        // my.alert({content:JSON.stringify(result)})
                        if (Number(result.code) !== 200) {
                            my.showToast({
                                title: result.msg,
                                icon: 'none',
                                duration: 2000,
                            });
                            // uni.hideLoading();
                            return reject(result);
                        }
                        Store.commit('setAccountDataPlugin', passwordInstance);
                        Object.assign(this, keyboardPlugin);
                        // uni.hideLoading();
                        resolve(passwordInstance);
                    } catch (err) {
                        // uni.hideLoading();
                        console.log('mpaas keybord初始化失败', err);
                        reject(err);
                    }
                },
            });
        });
    }
    // #endif
}
// #ifdef MP-MPAAS
export default singleton(mpaasKeyBordPlugin);
// #endif
