import cnpcBridge from '../v3-native-jsapi/cnpcBridge.js';
const projectConfig = require('../../../../project.config');
import md5 from 'md5';
import layer from "../layer";
const requestCCB = {
    async requestRPC(url, params = {}, reqConfig = {}) {
        return new Promise((async (resolve, reject) => {
            let time = new Date().getTime();
            const uuid = 'app' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
            let sign = JSON.stringify(params) + time + uuid + 'Jx3wQMD1' + 'd68397c4fb671bc024e24e1964b067cc35388818';
            let baseUrl = projectConfig.baseUrl2App;
            my.call('request', {
                'url': baseUrl + url,
                'method': 'POST',
                'headers':{
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'referrer': 'app'
                },
                'data':`nonce=${uuid}&timestamp=${time}&jsonData=${JSON.stringify(params)}&sign=${md5(sign).toUpperCase()}&accessKey=Jx3wQMD1`,
                'requestTaskId': 1,
                'dataType': 'json',
                'timeout': 30000
            }, result => {
                console.info('resultData====='+JSON.stringify(result))
                if (result.status === 200){
                    resolve(JSON.parse(result.data))
                } else reject()
            })
        }))
    },
}

export default requestCCB;

