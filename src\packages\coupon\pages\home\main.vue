<template>
    <div class="view">
        <div>
            <FrostedGlass @change="handleReloadGetCouponList"></FrostedGlass>
        </div>
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="电子券"
            :back-text-style="pageConfig.titleStyle"
        ></u-navbar>
        <view class="scroll-view_H" :scroll-left="scrollLeft" scroll-x="true">
            <div class="slide_type_list">
                <div
                    class="slide_type_list_view"
                    v-for="(item, index) in typeList"
                    :key="index"
                    :class="{ is_selected: active == index }"
                    @click="changeType(item, index)"
                >
                    <span>{{ item.name }}</span>
                    <!-- <span v-if="index == 0 && totalRecords > 0">({{totalRecords}})</span> -->
                </div>
            </div>
            <div class="sort-div hmfilterDropdown" v-if="active == 0">
                <div class="item-sort" v-for="(idm, idx) in sortList" :key="idx" @click="chooseSort(idm, idx)">
                    <div class="sort-name">{{ idm.name }}</div>
                    <div class="sort-icon" v-if="activeIdx != idx">
                        <u-icon name="arrow-up-fill" color="#909090" size="12"></u-icon>
                        <u-icon name="arrow-down-fill" color="#909090" size="12"></u-icon>
                    </div>
                    <div class="sort-icon" v-else>
                        <template v-if="filterType == '3'">
                            <u-icon name="arrow-up-fill" :color="selectedColor" size="12"></u-icon>
                            <u-icon name="arrow-down-fill" :color="noselectedColor" size="12"></u-icon>
                        </template>
                        <template v-if="filterType == '4'">
                            <u-icon name="arrow-up-fill" :color="noselectedColor" size="12"></u-icon>
                            <u-icon name="arrow-down-fill" :color="selectedColor" size="12"></u-icon>
                        </template>
                    </div>
                </div>
                <view class="classify">
                    <HMfilterDropdown @selectHierarchyMenu="selectHierarchyMenu" :filterData="filterData"></HMfilterDropdown>
                </view>
            </div>
            <div v-if="registerLoginInformation" class="phone-tip">当前手机号：{{ registerLoginInformation.phone | phoneFilter }}</div>
        </view>
        <scroll-view
            class="coupon-scroll-view"
            :scroll-y="true"
            :enhanced="true"
            :show-scrollbar="false"
            @scrolltolower="handleGetNextPage"
        >
            <coupon-list :datalist="couponArr" :available="active == 0" :index="active" @click="couponItemClick"></coupon-list>
            <div class="loadmore">
                <u-loadmore @loadmore="clickLoadMore" :status="loadMortState" :load-text="loadText" />
            </div>
        </scroll-view>
        <Privacy v-if="privacyIsShow"></Privacy>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { electronicTicketDetails } from '@/api/home.js';
import pageConfig from '@/utils/pageConfig.js';
import couponList from '@/components/coupon-list/coupon-list.vue';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import HMfilterDropdown from '../../components/HM-filterDropdown/HM-filterDropdown.vue';
import Privacy from '@/components/privacy/main.vue';
import { getOverdueList, getUnusedList, getUsedList } from '@/api/home.js';

export default {
    components: {
        couponList,
        FrostedGlass,
        HMfilterDropdown,
        Privacy,
    },
    data() {
        return {
            couponArr: [],
            totalRecords: 0,
            /**********上方为假数据***********/
            pageConfig, // 页面配置
            active: 0, // 上方tab选中的index
            activeIdx: '0',
            selectedColor: '#fab27b',
            noselectedColor: '#FF8200',
            filterType: '3',
            sortType: 'yxq', //排序方式，默认空
            sortRule: 'D', //排序规则  A或者D
            couponAlias: '', //类型
            typeList: [
                {
                    name: '未使用',
                },
                {
                    name: '已使用',
                },
                {
                    name: '已过期',
                },
            ],
            sortList: [
                {
                    name: '有效期',
                },
                {
                    name: '金额',
                },
            ],
            filterData: [
                {
                    name: '全部类型',
                    type: 'hierarchy',
                    submenu: [
                        {
                            name: '全部类型',
                            value: '',
                        },
                        {
                            name: '油品券',
                            value: 'YP',
                        },
                        // {
                        //   name: "汽油券",
                        //   value: "QY",
                        // },
                        // {
                        //   name: "柴油券",
                        //   value: "CY",
                        // },
                        {
                            name: '非油券',
                            value: 'FY',
                        },
                        // {
                        //   name: "商城券",
                        //   value: "SQ",
                        // },
                        {
                            name: '其他',
                            value: 'XC',
                        },
                        /**
                         * YP 油品券，QY 汽油券，CY 柴油券，FY 非油券，SQ 商城券，XC 异业券
                         */
                    ],
                },
            ],
            pageNo: 1,
            pageSize: 20,
            loadText: {
                loadmore: '点击或上拉加载更多',
                loading: '努力加载中',
                nomore: '暂无更多数据',
            }, // 下拉刷新状态
            loadMortState: 'loadmore',
        };
    },

    onLoad() {
        console.log(this.registerLoginInformation, 'registerLoginInformation');
        if (this.isLogin) {
            console.log('已登录');
            this.handleReloadGetCouponList();
        }
    },
    methods: {
        // 组件分发出来的方法   传出来的参数为当前点击的是哪个类型
        selectHierarchyMenu(type) {
            this.couponAlias = type;
            this.pageNo = 1;
            this.handleReloadGetCouponList();
        },
        //点击查看更多触发事件
        clickLoadMore(e) {
            console.log('2222');
            // this.pageNo++
            // this.selectHierarchyMenu()
            // console.log('加载更多')
        },
        changeType(item, index) {
            console.log(index, 'index');
            if (index !== this.active) {
                this.couponAlias = '';
                this.pageNo = 1;
                this.active = index;
                this.couponArr = [];
                this.handleReloadGetCouponList();
            }
        },
        // 排序点击事件
        chooseSort(idm, index) {
            this.pageNo = 1;
            if (this.activeIdx != index) {
                //切换排序方式
                this.activeIdx = index;
                this.sortType = this.activeIdx == '0' ? 'yxq' : 'je';

                this.sortRule = 'D';
                this.filterType = '3';
            } else {
                if (this.filterType == '3') {
                    this.filterType = '4';
                    this.sortRule = 'A';
                } else {
                    this.filterType = '3';
                    this.sortRule = 'D';
                }
            }
            this.handleReloadGetCouponList();
        },
        //优惠券点击事件
        couponItemClick(index) {
            const item = this.couponArr[index];
            if (this.active == 2) {
                return;
            }
            let url = `/packages/coupon/pages/coupon-detail/main?voucher=${item.voucher}&status=${this.active}`;
            if (this.active == 1) {
                url += `&bizTypeText=${item.bizTypeText}`;
            }
            uni.navigateTo({
                url,
            });
        },
        /**
         * 重新加载
         * 初始化分页配置
         */
        handleReloadGetCouponList() {
            this._pageParams = {
                pageNo: this.pageNo,
                pageSize: this.pageSize,
            };
            this._pageParams.sortType = this.sortType;
            this._pageParams.sortRule = this.sortRule;
            this._pageParams.couponAlias = this.couponAlias;
            this.handleGetCouponList();
        },
        /**
         * 获取下一页
         */
        handleGetNextPage() {
            if (this.loadMortState == 'loadmore') {
                //可以加载更多
                this.pageNo++;
                this.handleReloadGetCouponList();
            }
        },
        /**
         * 获取优惠券列表
         */
        handleGetCouponList() {
            this.loadMortState = 'loading';
            const getCouponListPromise = [getUnusedList, getUsedList, getOverdueList][this.active];
            return getCouponListPromise(this._pageParams)
                .then(res => {
                    if (res.status == 0) {
                        const { data = [] } = res;
                        return {
                            data,
                        };
                    }
                })
                .then(({ data }) => {
                    console.log(data.length);
                    //目前只有未使用分页了
                    if (this.active == 0) {
                        if (data.length < this.pageSize) {
                            this.loadMortState = 'nomore';
                        } else {
                            this.loadMortState = 'loadmore';
                        }
                    } else this.loadMortState = 'nomore';
                    if (this.pageNo == 1) this.couponArr = data;
                    else this.couponArr.push(...data);
                });
        },
    },
    computed: {
        ...mapGetters(['registerLoginInformation', 'isLogin']),
        ...mapState({
            privacyIsShow: state => state.privacy.privacyIsShow,
        }),
    },
    filters: {
        phoneFilter(val) {
            let reg = /^(.{3}).*(.{4})$/;
            return val.replace(reg, '$1****$2');
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    // overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    .coupon-scroll-view {
        flex: 1;
        min-height: 0;
    }
    .loadmore {
        padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
        ::v-deep .u-load-more-wrap {
            background-color: transparent !important;
            view {
                background-color: transparent !important;
            }
        }
    }
}

// 横向滑动tab
.scroll-view_H {
    margin-bottom: 10px;
    .slide_type_list {
        display: flex;
        align-items: center;
        padding: 12.5px 0;

        .slide_type_list_view {
            margin-left: 35px;
            margin-right: 35px;
            flex: 1;
            padding-bottom: 7px;
            font-size: 15px;
            color: #333333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .is_selected {
            color: #ff8200;
            font-weight: bold;
            position: relative;
        }

        .is_selected:before {
            content: '';
            position: absolute;
            // left: 28%;
            bottom: -2px;
            // width: 24px;
            width: 100%;
            height: 3px;
            border-bottom: 2px solid #ff8200;
            // background: url('~@/static/coupon-menu-line.png') no-repeat 0 / 100%;
        }
    }
    .sort-div {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 10px;
        height: 35px;
        .item-sort {
            display: flex;
            align-items: center;
            font-size: 14px;

            .sort-icon {
                margin-left: 5px;
                display: flex;
                flex-direction: column;
            }
        }
        .down-active {
            color: #f46458;
        }
        .down-show {
            height: 88rpx;
        }
    }
    .hmfilterDropdown {
        position: relative;
        .classFly {
            position: absolute;
            top: calc(100% + 10px);
        }
    }

    .phone-tip {
        font-size: 14px;
        color: #ccc;
        text-align: center;
        position: relative;

        &.phone-tip:before {
            content: ' ';
            position: absolute;
            left: 20%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }

        &.phone-tip:after {
            content: ' ';
            position: absolute;
            right: 20%;
            top: 50%;
            width: 25px;
            height: 1px;
            border-top: 1px solid #999;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }
    }
}
</style>
