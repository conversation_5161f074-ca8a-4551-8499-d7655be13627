import { POST, POST2 } from '../index';
import { api, apiGsms } from '../../../../../project.config';

// 开通电子账户
export const openAccount = (params, config) => {
    return POST('account.openAccount', params, config);
};

// 修改支付密码
export const passwordModify = (params, config) => {
    return POST('account.password.modify', params, config);
};
// 忘记支付密码
export const passwordForget = (params, config) => {
    return POST('account.password.forget', params, config);
};

export const balance = (params, config) => {
    return POST('account.balance', params, config);
};
// 获取充值金额及营销文案
export const queryRechargeActivityInfo = (params, config) => {
    return POST('account.queryRechargeActivityInfo', params, config);
};
// export const accountBalanceList = (params, config) => {
//   return POST('account.accountBalanceList', params, config)
// }

// 查看电子账户的明细列表接口
export const detailList = (params, config) => {
    return POST('account.detailList', params, config);
};
// 查看电子账户的明细详情接口
export const detailInfo = (params, config) => {
    return POST('account.detailInfo', params, config);
};

// 查询电子账户剩余注销次数接口 /account/leftCancelTimes/query
export const accountLeftCancelTimesQuery = (params, config) => {
    return POST('account.leftCancelTimes.query', params, config);
};

// 电子账户注销接口 /account/logout  app走sdk
export const accountLogout = (params, config) => {
    return POST('account.logout', params, config);
};
// 更换开户地接口 /account/address/modify
export const addressModify = (params, config) => {
    return POST('account.address.modify', params, config);
};
// 查询电子账户开通状态接口 /account/accountStatus=
export const accountStatus = (params, config) => {
    return POST('account.accountStatus', params, config);
};
// 发送短信验证码/content/messageCode/send
export const messageCodeSend = (params, config) => {
    return POST('content.messageCode.send', params, config);
};
// 校验验证码/content/messageCode/verify
export const messageCodeVerify = (params, config) => {
    return POST('content.messageCode.verify', params, config);
};
// 充值预下单
export const preOrderRecharge = (params, config) => {
    return POST('account.preOrder.recharge', params, config);
};
// 昆仑e享卡充值记录
export const eCardRechargeRecord = (params, config) => {
    return POST('account.eCardRechargeRecord', params, config);
};

// 查询会员升级前的老电子卡消费记录接口
export const cardOldConsumeRecordList = (params, config) => {
    return POST('card.oldConsumeRecordList', params, config);
};
// 查询待办事项
export const getTodoInfo = (params, config) => {
    return POST('user.getTodoInfo', params, config);
};
// 用户与加油员绑定接口
export const staffMemberBind = (params, config) => {
    return POST('user.staffMember.bind', params, config);
};
// 支付宝uid换取员工推荐e码
export const staffInfoGet = (params, config) => {
    return POST('user.staffInfo.get', params, config);
};

// 迁移电子卡
export const migrateECard = (params, config) => {
    return POST('account.migrateECard', params, config);
};

// 充值状态查询

export const rechargeStatusQuery = (params, config) => {
    return POST('account.rechargeStatus.query', params, config);
};
// 充值卡充值
export const rechargeByCard = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/app_third/rechargeByCard', params, config);
    // #endif
    // #ifdef MP-ALIPAY
    return POST2(api + apiGsms + '/v1/recharge/cardCharge', params, config);
    // #endif
    // #ifdef MP-WEIXIN
    return POST2('/app/json/third/cardCharge', params, config);
    // #endif
};
// 根据证件号查询电子卡信息
export const queryECardInfoByIdNo = (params, config) => {
    return POST2('/app/json/app_third/queryECardInfoByIdNo', params, config);
};
// 根据证件号查询电子卡信息(微信小程序)
export const queryECardInfoByIdNoMiniProgram = (params, config) => {
    return POST2('/app/json/third/queryECardInfo', params, config);
};
// 根据证件号查询电子卡信息(支付宝小程序)
export const queryECardInfoByIdAliMiniProgram = (params, config) => {
    return POST2(api + apiGsms + '/v1/queryECardInfoByIdNo', params, config);
};
// 获取2.0电子券列表
export const appJsonCouponUnusedcoupons = (params, config) => {
    return POST2('/app/json/coupon/unusedcoupons', params, config);
};

// 获取2.0电子券列表 未使用详情
export const getUnusedDetail = (params, config) => {
    return POST2('/app/json/coupon/getUnusedDetail', params, config);
};
// 获取2.0电子券列表 已使用详情
export const getUsedDetail = (params, config) => {
    return POST2('/app/json/coupon/getUsedDetail', params, config);
};
// 开通掌纹支付
export const palmPayOpen = (params, config) => {
    return POST('account.palmPay.open', params, config);
};
// 关闭掌纹支付
export const palmPayClose = (params, config) => {
    return POST('account.palmPay.close', params, config);
};
// 查看掌纹支付开通结果
export const palmPayGetFlag = (params, config) => {
    return POST('account.palmPay.getFlag', params, config);
};

// 风控验证
export const accountRiskEngineSupport = (params, config) => {
    return POST('account.riskEngineSupport', params, config);
};

// 建行联合会员加群组 /user/associate/joinGroup
export const associateJoinGroup = (params, config) => {
    return POST('user.associate.joinGroup', params, config);
};
// 查询联合会员是否加入群组接口  /user/associate/checkJoinGroup
export const associateCheckJoinGroup = (params, config) => {
    return POST('user.associate.checkJoinGroup', params, config);
};

// 昆仑e享卡挂失解挂接口 /account/status/lockOrUnlock
export const lockOrUnlockApi = (params, config) => {
    return POST('account.status.lockOrUnlock', params, config);
};
// 校验短信 -忘记登录密码
export const noLoginVerify = (params, config) => {
    return POST('content.messageCode.noLoginVerify', params, config);
};

// 校验该商户券是否归属于当前用户
export const validWechatCouponPhone = (params, config) => {
    return POST('account.validWechatCouponPhone', params, config);
};
