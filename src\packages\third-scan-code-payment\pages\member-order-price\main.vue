<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="确认订单"></zj-navbar>
            <div class="p-bf bg-F7F7FB padding-16 f-1 box_sizing">
                <div v-if="active == 1">
                    <div class="money bg-fff border-rad-8">
                        <div class="title font-15 weight-400 color-333 te-center" style="margin-top: 50px">{{
                            payType == 15 ? '待付积分' : '待付金额'
                        }}</div>
                        <div class="fl-row num fl-al-jus-cen">
                            <div class="font-36 weight-600 color-333" v-if="payType != 15">&yen;</div>
                            <div class="font-50 weight-600 color-333">{{ payType != 15 ? actualPayTotalAmount : accumulatedPoint }}</div>
                            <div v-if="payType == 15" class="order-amount-price-tag font-28 color-333">积分</div>
                        </div>
                    </div>
                    <div class="confirmBtn primary-btn color-fff fl-row fl-al-jus-cen confirmRecharge border-rad-8" @click="limitationFn()">
                        <div>确认支付</div>
                    </div>
                </div>
                <div v-if="active == 2">
                    <div class="money-result bg-fff border-rad-8">
                        <div class="title" style="margin-top: 30px">
                            <img src="../../images/success-green.png" alt />
                        </div>
                        <div class="money-result-title color-333 font-16">支付成功</div>
                        <div class="fl-row num fl-al-jus-cen">
                            <div class="font-36 weight-600 color-333" v-if="payType != 15">&yen;</div>
                            <div class="font-50 weight-600 color-333">{{ payType != 15 ? actualPayTotalAmount : accumulatedPoint }}</div>
                            <div v-if="payType == 15" class="order-amount-price-tag font-28 color-333">积分</div>
                        </div>
                    </div>
                    <div class="confirmBtn primary-btn color-fff fl-row fl-al-jus-cen confirmRecharge border-rad-8" @click="closeEvent"
                        >完成</div
                    >
                    <zj-marketing-coupon></zj-marketing-coupon>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zj-unrealized-authentication
                v-if="realNameDialogFlag"
                @realNameDialogClose="realNameDialogClose"
                @realNameInfo="realNameInfo"
            ></zj-unrealized-authentication>
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <div class="mask" v-if="isOrderShow"></div>
            <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
            <zjMarket
                v-if="active == 2"
                marketType="screenMask"
                ref="maskBanner"
                :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
                spaceType="payment_page"
            ></zjMarket>
        </div>
        <div v-if="isHarmony">
            <account-plugin ref="accountPlugin"></account-plugin>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
// #ifdef MP-MPAAS
import appMemberOrderPrice from './diff-environment/app-member-order-price';
// #endif
// #ifndef MP-MPAAS || MP-PIANG
import wxMemberOrderPrice from './diff-environment/wx-member-order-price';
import zfbMemberOrderPrice from './diff-environment/zfb-member-order-price';
// #endif
import zjMarket from '../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import projectConfig from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    name: 'code-order-price',
    components: { zjMarket },
    props: {},
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMemberOrderPrice,
        // #endif
        // #ifndef MP-MPAAS
        wxMemberOrderPrice,
        zfbMemberOrderPrice,
        // #endif
    ],
    data() {
        return {
            // SDK查询到的订单支付信息
            payMentInfo: {},
            // 默认金额为0
            actualPayTotalAmount: 0,
            //1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；6：加油卡；7：信用账户；8：云闪付；9：和包支付；10：翼支付；11：优惠券；12：礼品卡；13：油币；14：能源币；15：积分；29：现金；
            payType: 0,
            //默认积分
            accumulatedPoint: 0,
            // 待支付和支付完成状态判断标识
            active: 1,
            //未实人认证加油时需要弹出输入身份证号和姓名的弹窗
            realNameDialogFlag: false,
            // 微信小程序和支付宝小程序账户插件实例
            passwordKeyboardRef: null,
            // 密码键盘被拉起来之前的遮罩
            isOrderShow: false,
        };
    },
    computed: {
        ...mapGetters(['walletInfo']),
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    created() {},
    onLoad(options) {
        // #ifdef MP-WEIXIN
        let result = this.selectComponent('#passwordKeyboardId');
        this.$sKit.keyBordPlugin.initRef(result);
        this.passwordKeyboardRef = result;
        // #endif
        // #ifdef MP-ALIPAY
        this.$nextTick(() => {
            let result = this.$refs['handlePasswordKeyboardRef'];
            console.log('keybord初始化', result);
            this.$sKit.keyBordPlugin.initRef(result);
            this.passwordKeyboardRef = this.$refs['handlePasswordKeyboardRef'];
        });
        // 支付宝referer过长兼容
        const nowPage = getCurrentPages().pop();
        let optionsData = getApp().globalData.query?.[nowPage.$page.fullPath];

        if (!optionsData?.zfbRefererMax) {
            this.payMentInfo = JSON.parse(decodeURIComponent(options.data));
        } else {
            this.payMentInfo = optionsData;
        }
        // #endif
        // #ifndef MP-ALIPAY
        // 查询到的订单信息
        this.payMentInfo = JSON.parse(decodeURIComponent(options.data));
        // #endif
        // 设置节流，防止用户在短时间内频繁点击
        this.limitationFn = this.$sKit.commonUtil.throttleUtil(this.limitationFn, 3000);
        console.log(this.payMentInfo, '查询到的订单信息');
        // 判断是否存在支付方式
        if (this.payMentInfo.payTypeList.length > 0) {
            // 订单信息的支付方式(判断是积分还是油卡或者昆仑e享卡)
            this.payMentInfo.payTypeList.forEach(item => {
                // 判断支付方式是油卡、昆仑e享卡还是积分   v420新增payType为12礼品卡的  v430去掉payType为12的礼品卡（只展示主支付方式的金额，不展示礼品卡的）
                if (item.payType == 5 || item.payType == 6 || item.payType == 15) {
                    // 将实付金额赋值到页面展示
                    this.actualPayTotalAmount = item.realPayMoney;
                    this.payType = item.payType;
                    if (item.payType == 15) {
                        // 待支付积分
                        this.accumulatedPoint = item.accumulatedPoint;
                    }
                }
            });
        } else {
            // 待支付金额
            this.actualPayTotalAmount = this.payMentInfo.orderAmount;
        }
        this.$sKit.mpBP.tracker('会员码', {
            seed: 'memberCodeBiz',
            pageID: 'paidOrderPage', // 页面名
            refer: this.payMentInfo.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
            address: this.cityName,
        });
    },
    methods: {
        /**
         * @description  : 密码未设置限制
         * @param         {Function} setPassword -是否设置支付密码
         * @return        {*}
         */
        limitationFn() {
            // 调用公共方法查看昆仑e享卡是否设置密码
            this.$sKit.commonUtil.setPassword(() => {
                // 确认支付
                this.checkRisk();
            });
        },
        /**
         * @description  : 确认支付之前检验是否触发风控
         * @param         {String} this.payMentInfo.faceCheckFlag -风控标识：1为需要面部识别
         * @return        {*}
         */
        checkRisk() {
            if (this.payMentInfo.faceCheckFlag && this.payMentInfo.faceCheckFlag == 1) {
                console.log('进入的风控');
                //查看用户是否进行过实人认证
                this.$sKit.commonUtil
                    .oilTriggerRisk()
                    .then(res => {
                        // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                        if (res == 13) {
                            console.log('13=====进入未认证打开弹窗');
                            // 打开实人认证的表单弹窗
                            this.realNameDialogFlag = true;
                        }
                        // else {
                        //     // 如果不是13的时候认证成功后唤起密码键盘支付
                        //     this.confirmPay(true);
                        // }
                    })
                    .catch(() => {
                        uni.hideLoading();
                    });
            } else {
                console.log('没有触发风控');
                this.confirmPay();
            }
        },
        /**
         * @description  : 实人认证弹窗的确认事件
         * @param         {String} name -姓名
         * @param         {String} idNumber -身份证号
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @param         {Object} val -打开获关闭弹窗
         * @return        {*}
         */
        realNameInfo(val) {
            this.$sKit.commonUtil
                .triggerRiskAuth(val.name, val.idNumber)
                .then(res => {
                    // 关闭实人认证的表单弹窗
                    this.realNameDialogFlag = false;
                    // 再次执行确认支付
                    this.confirmPay(true);
                })
                .catch(err => {
                    uni.showToast({ title: err });
                });
        },
        /**
         * @description  : 关闭实人认证弹窗
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameDialogClose() {
            this.realNameDialogFlag = false;
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.$sKit.commonUtil
                .nextOilTriggerRisk()
                .then(res => {
                    uni.showLoading({
                        title: '加载中',
                        mask: true,
                    });
                    // 如果不是13的时候认证成功后唤起密码键盘支付
                    this.confirmPay(true);
                })
                .catch(() => {
                    uni.hideLoading();
                });
        },
    },
    filter: {},
    watch: {},
};
</script>

<style lang="scss" scoped>
@import '../../../../s-kit/css/index.scss';
.view {
    .money {
        width: 100%;
        height: 212px;
        display: inline-block;

        .title {
            margin-top: 30px;
            line-height: 22px;
        }

        .num {
            line-height: 50px;
            margin-top: 16px;

            .order-amount-price-tag {
                margin-top: 8px;
            }
        }
    }

    .confirmBtn {
        margin-top: 12px;
        width: 100%;
        height: 44px;
    }

    .money-result {
        width: 100%;
        height: 212px;
        position: relative;
        // 加这行代码是为了解决子元素添加margin-top影响父元素；上下盒子添加外边距重叠问题
        display: inline-block;
        margin-bottom: 16px;

        .title {
            width: 64px;
            height: 64px;
            margin: 40px auto 0;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .num {
            line-height: 50px;
            margin-top: 16px;
            margin-bottom: 30px;

            .order-amount-price-tag {
                margin-top: 8px;
            }
        }

        .text {
            margin-bottom: 15px;
        }

        .text2 {
            margin-bottom: 42.5px;
        }
    }

    .money-result-title {
        margin: 15px 0 auto;
        text-align: center;
    }

    .confirmBtn {
        margin-top: 12px;
        width: 100%;
        height: 44px;
    }

    .mask {
        position: fixed;
        background: rgba(0, 0, 0, 0.1);
        z-index: 998;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }
}

.box_sizing {
    box-sizing: border-box;
}
</style>
