import Vue from 'vue';
import Vuex from 'vuex';

// #s-ifdef cnpc-wx
import location from './modules/location.js';
import venicle from './modules/venicle.js';
import card from './modules/card.js';
import privacy from './modules/privacy.js';
// #s-endif

import thirdIndex from '../s-kit/js/v3-store/modules/thirdIndex';
import thirdLogin from '../s-kit/js/v3-store/modules/thirdLogin';
import thirdInitModal from '../s-kit/js/v3-store/modules/thirdInitModal';
import wallet from '../s-kit/js/v3-store/modules/wallet';
import member from '../s-kit/js/v3-store/modules/member';
import locationV3_app from '../s-kit/js/v3-store/modules/locationV3_app.js';
import carListV3 from '../s-kit/js/v3-store/modules/carListV3.js';
import bulkOil from '../s-kit/js/v3-store/modules/bulkOil.js';

Vue.use(Vuex);
const store = new Vuex.Store({
    state: {
        token: '',
        phone: '',
        token3: '',
        //推荐注册码
        staffStationId: '',
        accessToken: '',
        openId: '',
        isLogin: false,
        userInfo: null,
        mpUserInfo: null,
        locationInfo: null,
        channel: '',
        wxCode: '',
        isSetPassword: false,
        frostedglassIsLoading: true,
        loginDate: false,
        registerLoginInformation: null,
        source: '', // 上海小程序跳转过来的标识
        sourceTimeSh: '', // 时间戳
        paymentFlag: true,
        isOfficialAccount: false, // 公众号跳转小程序中转页按钮隐藏标识
        // 个人账号信息
        personalInformation3: {},
        //3.0 个人信息
        basicInfo: null,
        //3.0请求插件
        gsmsToken: '',
        // 微信支付宝设备指纹
        fpInfo: null,
        // 邀请开卡
        invitingCardOpeningId: '',
        // 新用户标识
        newUserTag: '',
    },
    mutations: {
        setIsSetPassword: (state, isSetPassword) => {
            state.isSetPassword = isSetPassword;
        },
        setLocationInfo: (state, locationInfo) => {
            state.locationInfo = locationInfo;
        },
        setToken: (state, token) => {
            state.token = token;
        },
        setToken3: (state, token3) => {
            state.token3 = token3;
        },
        // 推荐注册
        setStaffStationId: (state, staffStationId) => {
            state.staffStationId = staffStationId;
        },
        // 邀请开卡
        setInvitingCardOpeningId: (state, info) => {
            state.invitingCardOpeningId = info;
        },
        setOpenId: (state, openId) => {
            state.openId = openId;
        },
        setLoginStatus: (state, status) => {
            state.isLogin = status;
        },
        setUserInfo: (state, userInfo) => {
            state.userInfo = userInfo;
        },
        setMpUserInfo: (state, userInfo) => {
            state.mpUserInfo = userInfo;
        },
        setChannel(state, info) {
            state.channel = info;
        },
        setWxCode(state, info) {
            state.wxCode = info;
        },
        setFrostedglassIsLoading(state, frostedglassIsLoading) {
            state.frostedglassIsLoading = frostedglassIsLoading;
        },
        setLoginDate(state, setLoginDate) {
            state.loginDate = setLoginDate;
        },
        setRegisterLoginInformation(state, info) {
            state.registerLoginInformation = info;
        },
        setSource(state, info) {
            state.source = info;
        },
        setSourceTimeSh(state, info) {
            state.sourceTimeSh = info;
        },
        setPaymentFlag(state, info) {
            state.paymentFlag = info;
        },
        // 3.0个人账号信息
        mSetPersonalInformation3: (state, info) => {
            return new Promise((resolve, reject) => {
                state.personalInformation3 = info || {};
                resolve()
            });
            // console.log(state.personalInformation3, '存在嗎')
        },
        // 3.0个人信息
        mSetbasicInfo: (state, info) => {
            state.basicInfo = info || {};
        },
        // 3.0请求插件
        mSetGsmsToken: (state, info) => {
            state.gsmsToken = info || '';
        },
        // 设置设备指纹
        setFPInfo: (state, data) => {
            state.fpInfo = data;
        },
    },
    getters: {
        token: state => state.token,
        token3: state => state.token3,
        isLogin: state => state.isLogin,
        codeLoginFlag: state => state.codeLoginFlag,
        openId: state => state.openId,
        mpUserInfo: state => state.mpUserInfo,
        registerLoginInformation: state => state.registerLoginInformation,
        channel: state => state.channel,
        wxCode: state => state.wxCode,
        isPassword: state => state.isSetPassword,
        // 油卡相关
        cardList: state => state.card.cardList,
        isHaveEntityCard: state => state.card.isHaveEntityCard,
        isHaveECard: state => state.card.isHaveECard,
        cardTopinfo: state => state.card.cardTopinfo,
        frostedglassIsLoading: state => state.frostedglassIsLoading,
        // 微信支付宝设备指纹
        fpInfo: state => state.fpInfo,
    },
    modules: {
        // #s-ifdef cnpc-wx
        location, // 位置信息
        venicle, // 车辆信息
        card, // 油卡信息
        privacy, // 隐私协议弹窗
        // #s-endif

        thirdIndex,
        thirdInitModal,
        wallet,
        member,
        locationV3_app,
        carListV3,
        thirdLogin,
        bulkOil
    },
    plugins: [],
});
export default store;
