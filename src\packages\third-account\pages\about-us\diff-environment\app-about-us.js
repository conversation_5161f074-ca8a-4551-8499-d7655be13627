import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user.js';
export default {
    methods: {
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('2', 'App隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            // 获取位置信息
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: type,
                    cityName: res.cityCode,
                    name: name,
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        // 打开pdf
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    }
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            });
        },
    },
};
