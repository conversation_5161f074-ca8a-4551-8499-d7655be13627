<template>
    <div>
        <TransparentMask v-if="maskDialogFlag" class="mask-dialog"></TransparentMask>
        <view
            class="view"
            :style="[
                { filter: grayHomePage ? 'grayscale(100%)' : 'grayscale(0%)' },
                { overflow: advertisFlag ? 'hidden' : 'scroll-y' },
                { position: 'relative' },
            ]"
        >
            <div class="navi-view">
                <div class="state-view" :style="{ height: stateH + 'px' }"></div>
                <div class="navi-bar" :style="{ height: 44 + 'px' }">
                    <div
                        class="navi-bar-content"
                        :style="{
                            height: navBarStyle.height,
                            'margin-top': navBarStyle['margin-top'],
                        }"
                    >
                        <div class="my-icon-view"></div>
                        <div class="navi-title">{{ title }}</div>
                    </div>
                </div>
            </div>
            <!-- 地图组件 215px-->
            <map
                :style="'height: calc(100vh - ' + (isIphoneX ? navH + 34 : navH) + 'px - 280px);' + 'top:' + navH + 'px'"
                :scale="scale"
                show-location
                :markers="showMarkerArr"
                :latitude="lat"
                :longitude="lon"
                @callouttap="clickCallOut"
                @markertap="clickMarker"
            >
                <cover-view slot="callout" v-if="isCustomCallout">
                    <cover-view class="marker-view" :marker-id="item.id" v-for="item in showMarkerArr" :key="item.id">
                        <cover-view class="marker-lable-view">
                            <cover-view class="marker-km">距您{{ item.distance }}km</cover-view>
                            <!-- <cover-image class="marker-navi-img" src="/static/img/navi-icon-2.png"></cover-image> -->
                            <cover-image
                                class="marker-navi-img"
                                :src="projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/navi-icon-2.png'"
                            ></cover-image>
                        </cover-view>
                    </cover-view>
                </cover-view>
            </map>
            <!-- 上方悬停按钮2022-03-03新增需求中 -->
            <div
                style="display: none"
                class="top-cover-view"
                @click="showUserCardInfo(showCardNo)"
                v-if="isLogin"
                :style="{
                    top: navH - 1 + 'px',
                }"
            >
                <div class="flex justify-between paddlr20 top-cover-content">
                    <div class="userinfo-head">
                        <img class="avatar" :src="registerLoginInformation.headImg" />
                    </div>
                    <div class="userinfo-top info-left pad-1 flex align-center flex-1">
                        <div style="margin-left: 10rpx">余额：</div>
                        <div v-if="showCardNo"> ￥{{ cardTopinfo.balance === undefined ? '0' : cardTopinfo.balance }} </div>
                        <div v-else>***</div>
                    </div>
                    <div class="userinfo-top info-center flex align-center flex-1">
                        <div>电子券：</div>
                        <div v-if="showCardNo"> {{ cardTopinfo.couponCount === undefined ? '0' : cardTopinfo.couponCount }}张 </div>
                        <div v-else>***</div>
                    </div>
                    <div class="userinfo-top info-right pad-2 flex align-center flex-1" style="justify-contnet: flex-end">
                        <div>年度优惠：</div>
                        <div v-if="showCardNo"> ￥{{ cardTopinfo.yearDiscountAmt === undefined ? '0' : cardTopinfo.yearDiscountAmt }} </div>
                        <div v-else>***</div>
                    </div>
                </div>
            </div>
            <!-- 定位悬停按钮 -->
            <!-- <cover-image class="location-icon" src="@/static/img/currentLocation-2.png" @click="clickLocation"></cover-image> -->
            <cover-image
                class="location-icon"
                :src="projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/currentLocation-2.png'"
                @click="clickLocation"
            ></cover-image>
            <view class="bottom-cover-view" :style="{ bottom: bottomCoverViewHeight.bottom }">
                <div class="oilingService">
                    <div class="oil-title flex align-center justify-between">
                        <div class="top-title">加油服务</div>
                        <get-phone-number @change="getPhoneNumber" btnType="tjyk">
                            <div class="top-addCard" v-if="!isHaveEntityCard && isLogin && cardList.length > 0 && cardList.length < 2">
                                添加油卡
                            </div>
                        </get-phone-number>
                    </div>
                    <div class="oilcaed-div" v-if="cardList.length == 0">
                        <div class="bgImg">
                            <!-- <img src="@/static/img/shitikabg.png" mode="aspectFill" class="top-bg" /> -->
                            <img
                                mode="aspectFill"
                                class="top-bg"
                                :src="projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/shitikabg.png'"
                                alt=""
                            />
                        </div>
                        <div class="oil-text flex align-center justify-between">
                            <div class="flex align-center">
                                <div v-if="selectMarker.id">
                                    <!-- <img src="@/static/img/cnpc-logo.png" alt v-if="selectMarker.openState == 1" />
                                    <img src="@/static/img/normal-cnpc-logo.png" alt v-else /> -->
                                    <img
                                        :src="projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/cnpc-logo.png'"
                                        v-if="selectMarker.openState == 1"
                                        alt=""
                                    />
                                    <img
                                        :src="projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/normal-cnpc-logo.png'"
                                        v-else
                                        alt=""
                                    />
                                </div>
                                <div class="oil-info">
                                    <div class="oilName">中国石油加油卡</div>
                                    <div class="oilDetail">添加后可以直接线上充值</div>
                                </div>
                            </div>
                            <get-phone-number @change="getPhoneNumber" btnType="tj">
                                <div class="addOilbut">添加</div>
                            </get-phone-number>
                        </div>
                    </div>
                    <div class="oilcard-div" v-else>
                        <swiper
                            class="swiper"
                            :indicator-dots="indicatorDots"
                            :autoplay="autoplay"
                            :current="swiperIndex"
                            :previous-margin="previousMargin"
                            @change="handleChangeSwiperItem"
                            :next-margin="nextMargin"
                            :duration="300"
                        >
                            <swiper-item v-for="(item, index) in cardList" :key="index" class="swiper-box">
                                <div
                                    class="oil-text flex align-center justify-between"
                                    :style="{
                                        marginRight: swiperMargin.marginRight,
                                        background: item.cardType == 1 && item.cardTypeImg ? `url(${item.cardTypeImg} )` : '#feefe4',
                                    }"
                                >
                                    <div class="flex align-center">
                                        <img class="ss-icon" v-if="selectMarker.id" src="@/static/cnpc-logo.png" alt />
                                        <!--   :src="
                        selectMarker.openState == 1
                          ? '/static/cnpc-logo.png'
                          : '/static/normal-cnpc-logo.png'
                    "-->
                                        <div class="oil-info" :data-item="item" @click="showDefaultCard($event, index)">
                                            <div class="oilName flex align-center">
                                                <div>余额:</div>
                                                <div v-if="item.activeed"> ￥{{ item.balance ? item.balance : 0 }} </div>
                                                <div v-else>****</div>
                                                <img
                                                    src="@/static/eye-block.png"
                                                    v-if="item.activeed"
                                                    alt
                                                    class="eye-block-iocn1"
                                                    mode="widthFix"
                                                />
                                                <img src="@/static/eye-none.png" v-else alt class="eye-block-iocn1" mode="widthFix" />
                                            </div>
                                            <div class="oilDetail flex align-center">
                                                <div class="card-user-name" v-if="item.activeed">
                                                    {{ item.userName }}
                                                </div>
                                                <div class="card-user-name" v-else>
                                                    {{ item.userNameShow }}
                                                </div>
                                                <div class="card-mid">|</div>
                                                <div class="card-user-idcard" v-if="item.activeed">
                                                    {{ item.cardNo }}
                                                </div>
                                                <div class="card-user-idcard" v-else>
                                                    {{ item.cardNoShow }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="rechargeBut" @click="chargeOilCard(index)"> 充值 </div>
                                </div>
                            </swiper-item>
                        </swiper>
                    </div>
                    <!-- 首页功能区  #f96702-->
                    <div class="tab-div-wrap">
                        <div class="tab-div" v-if="!moduleBannerListShow">
                            <div class="tab-item">
                                <u-badge
                                    absolute="true"
                                    v-if="!isHaveECard"
                                    size="mini"
                                    :offset="[-8, 15]"
                                    type="error"
                                    count="new"
                                ></u-badge>
                                <get-phone-number @change="getPhoneNumber" btnType="eCardApply">
                                    <img src="@/static/e-card-icon.png" alt />
                                    <div>电子卡</div>
                                </get-phone-number>
                            </div>
                            <div class="tab-item">
                                <get-phone-number @change="getPhoneNumber" btnType="oilCardQuery">
                                    <img src="@/static/my-oil.png" alt />
                                    <div>油卡查询</div>
                                </get-phone-number>
                            </div>
                            <div class="tab-item">
                                <get-phone-number @change="getPhoneNumber" btnType="coupon">
                                    <img src="@/static/my-coupon.png" alt />
                                    <div>电子券</div>
                                </get-phone-number>
                            </div>
                            <div class="tab-item tab-item-my" @click="skipMy">
                                <!-- <get-phone-number @change="getPhoneNumber" btnType="my"> -->
                                <img src="@/static/homeIcon/my.png" alt />
                                <div>我的</div>
                                <!-- </get-phone-number> -->
                            </div>
                        </div>
                        <swiper
                            v-if="moduleBannerListShow"
                            class="center-module-swiper"
                            :duration="500"
                            :indicator-dots="moduleBannerList.length === 1 ? false : true"
                            indicator-active-color="#f96702"
                        >
                            <swiper-item class="center-module-swiper-item" v-for="item in moduleBannerList" :key="item">
                                <div v-for="(ite, inx) in item.data" :key="inx" class="center-swiperItem-div">
                                    <div class="center-swiperItem-div-item">
                                        <u-badge
                                            absolute="true"
                                            v-if="!isHaveECard && ite.menuName === '电子卡'"
                                            size="mini"
                                            :offset="[-8, 15]"
                                            type="error"
                                            count="new"
                                        ></u-badge>
                                        <div @click="menuClick(ite)">
                                            <img alt :src="ite.menuImg" />
                                            <div class="center-swiperItem-name">
                                                {{ ite.menuName }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </swiper-item>
                        </swiper>
                    </div>
                </div>
                <div class="service-station-view">
                    <div class="ss-text-view flex align-center justify-between" v-if="selectMarker.id">
                        <div class="sta-left">
                            {{ selectMarker.stationName + (selectMarker.openState == 1 ? '' : '（暂未开通）') }}
                        </div>
                        <div class="sta-right" @click="clickMoreYZ">其他油站</div>
                    </div>
                    <div class="station-mid flex align-center" v-if="selectMarker.id">
                        <img src="@/static/homeIcon/location-fill.png" alt />
                        <div class="station-address">{{ selectMarker.address }}</div>
                        <div class="station-juli" @click="clickNaviStateion"> {{ selectMarker.distance }}km </div>
                        <!-- <img class="ss-navi-icon" src="@/static/navi-icon-2.png" @click="clickNaviStateion"> -->
                    </div>
                    <div class="station-bot">
                        <span v-for="(item, index) in selectMarker.services" :key="index">
                            {{ item }}
                        </span>
                    </div>
                    <div class="btn-view">
                        <get-phone-number @change="getPhoneNumber" btnType="kjjy">
                            <div class="btn-fast-fuel">加油</div>
                        </get-phone-number>
                    </div>
                </div>
                <!-- 最下方轮播图 -->
                <div :style="{ marginBottom: bannerListCity.length ? '0' : '20px' }" class="banner" v-if="bannerListCountry.length !== 0">
                    <swiper class="swiper" :autoplay="true" :interval="3000" :duration="500" :circular="true">
                        <swiper-item v-for="item in bannerListCountry" :key="item">
                            <get-phone-number @change="getPhoneNumber" btnType="lbt" :params="item">
                                <img alt class="swiper-img" :src="item.img" />
                            </get-phone-number>
                        </swiper-item>
                    </swiper>
                </div>
                <div class="banner banner-sec" v-if="bannerListCity.length !== 0">
                    <swiper class="swiper" :autoplay="true" :interval="3000" :duration="500" :circular="true">
                        <swiper-item v-for="item in bannerListCity" :key="item">
                            <get-phone-number @change="getPhoneNumber" btnType="lbt" :params="item">
                                <image alt="" class="swiper-img" :src="item.img"></image>
                            </get-phone-number>
                        </swiper-item>
                    </swiper>
                </div>
            </view>
            <AdvertisingPopups v-if="advertisFlag" @closeEvent="closeEvent"></AdvertisingPopups>
        </view>
        <Privacy v-if="privacyIsShow"></Privacy>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { getMsgTemplates, mobilePhoneNumberEncryption, openidEncryption } from '@/api/home.js';
import { getOtherUserInfoPost } from '@/api/my-center.js';
import { mapState, mapGetters } from 'vuex';
import pageConfig from '@/utils/pageConfig.js';
import GetPhoneNumber from '@/components/login/get-phone-number';
import AdvertisingPopups from '@/components/advertisingPopups/advertisingPopups.vue';
import TransparentMask from '@/components/transparentMask/main.vue';
import Privacy from '@/components/privacy/main.vue';
import CONFIG from '@/utils/config.js';
import projectConfig from '../../../project.config';
export default {
    components: {
        GetPhoneNumber,
        AdvertisingPopups,
        TransparentMask,
        Privacy,
    },
    data() {
        return {
            projectConfig,
            pageConfig: pageConfig, // 页面配置
            navH: 63, // 导航高度
            isIphoneX: false, // 是否是苹果x及以上
            selectPlateIndex: 0, // 选中的车辆index
            isShowTip: true, // 是否显示提示
            msgNum: 0, //未读消息数
            system: true, // ios是true false 是安卓
            stateH: 20, // 导航状态高度
            isShowR: true, // 是否显示圆角
            menuRect: null, // 胶囊按钮的高度
            title: '', // 导航文字
            showCardNo: false, //顶部卡信息默认显示星星
            swiperIndex: 0,
            indicatorDots: false,
            autoplay: false,
            nextMargin: '0',
            previousMargin: '0',
            flag: true,
            typeBtn: '',
            btnFlag: false,
            scrollTop: 0,
            adShowOrHide: true,
            indicatorDots2: false,
            grayHomePage: '',
            codeLoginFlag: false,
            codeLoginObj: {},
        };
    },
    onShow() {
        // if (this.isLogin && this.token3) {
        //     uni.reLaunch({
        //         url:'pages/thirdhHome/main'
        //     })
        // }
        this.advertisFlag2 = true;
        this.grayHomePage = wx.getStorageSync('grayHomePage');
        /**
         * 这里的操作是：当未登录状态下点击某个功能按钮跳转到相关页面，
         * 再次回到首页时需要获取功能菜单和展示弹窗广告
         */
        if (this.advertisementFlag && this.isLogin && this.city !== '') {
            this.$store.dispatch('getAdvertisingPopups', { city: this.city, showLimit: 0 });
            this.$store.commit('setAdvertisementFlag', 0);
        }

        if (this.codeLoginFlag) {
            this.getPhoneNumber(this.codeLoginObj);
        }
    },
    onLoad(options) {
        console.log(this.officialAccountParams, this.token, !this.token3, '2.0首页接收的掌纹支付参数');
        if (this.officialAccountParams == 'yt' && this.ytPhone && this.isLogin) {
            this.comparePhoneNumbers(this.phone, this.ytPhone);
        }
        if (this.officialAccountParams == 'brushPalm' && this.token && !this.token3) {
            // uni.showToast({
            //     mask: true,
            //     title: '暂不支持微信刷掌，请在工作人员引导下升级',
            //     icon: 'none',
            // });
            uni.showModal({
                title: '提示',
                content: '暂不支持微信刷掌，请在工作人员引导下升级',
                showCancel: false,
                confirmText: '确认',
                confirmColor: '#FF8200',
                success: res => {
                    if (res.confirm) {
                    }
                },
            });
        }
        console.log(projectConfig, '项目配置');
        let getAccountInfoSync = wx.getAccountInfoSync();
        console.log(getAccountInfoSync, '小程序版本信息');
        this.$store.dispatch('initLocation', { callback: () => {}, val: 'loactionFlag' });
        let tokenInfo = uni.getStorageSync('tokenInfo');
        if (tokenInfo.token) {
            this.$store.dispatch('card/getAllCardList');
        }
        if (JSON.stringify(this.selectMarker) !== '{}') {
            // this.$store.dispatch('getNewOilStation', this.selectMarker.stationCode)
        }
        var obj = wx.getEnterOptionsSync();
        // let a = wx.getStorageSync('sourceTimeSh')
        // console.log(a, '读取本地缓存的时间戳', obj.query.time, '跳转携带的时间戳')
        if (obj.query.source && obj.query.time !== wx.getStorageSync('sourceTimeSh')) {
            this.$store.commit('setSource', obj.query.source);
            wx.setStorageSync('sourceTimeSh', obj.query.time);
            this.$store.commit('setPaymentFlag', true);
            // console.log(obj.query.source, obj.query.time, this.paymentFlag, '获取参数标识,时间戳,支付的标识')
            // console.log(this.paymentFlag, '如果为true,代表传上海标识参数')
            // this.$store.commit('setSourceTimeSh', obj.query.time)
        } else {
            this.$store.commit('setPaymentFlag', false);
            // console.log(this.paymentFlag, '如果为false,代表不传上海标识参数')
        }

        this.title = CONFIG.appId == 'wx7cd1712834749dcb' ? '能源e站' : '昆仑加油';
        let systemInfo = uni.getSystemInfoSync();
        this.stateH = systemInfo.statusBarHeight;
        this.system = systemInfo.system.indexOf('iOS') > -1;
        this.navH = 44 + systemInfo.statusBarHeight;
        let name = 'iPhone X';
        if (systemInfo.model.indexOf(name) > -1) {
            this.isIphoneX = true;
        }
        // this.$store.dispatch("initLocation", {
        //   callback: () => {

        //   }, val: ''
        // });

        if (this.isLogin) {
            if (this.cardList.length <= 1) {
                this.nextMargin = 0;
            } else {
                this.nextMargin = '20rpx';
            }
        }

        // 获取胶囊按钮的大小
        this.menuRect = this.getMenuRect();
    },
    mounted() {
        console.log('2.0首页mounted');
    },
    onShareAppMessage() {
        return {
            title: '不下车加油、油卡充值，消费、在线开发票，加油更便捷',
            path: '/pages/thirdHome/main',
        };
    },
    onHide() {},
    computed: {
        ...mapState({
            lat: state => state.location.lat, // 纬度
            lon: state => state.location.lon, // 经度
            province: state => state.location.province, // 省份
            city: state => state.location.city, // 城市
            showMarkerArr: state => state.location.markerArr, // marker数据
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            venicleList: state => state.venicle.venicleList, // 车辆列表
            scale: state => state.location.scale, // 地图缩放比例
            isCustomCallout: state => state.location.isCustomCallout, // 版本判断
            bannerListCity: state => state.location.bannerListCity, // 城市banner
            bannerListCountry: state => state.location.bannerListCountry, // 国家banner
            authorizedLocationFlag: state => state.location.authorizedLocationFlag, // 国家banner
            advertisFlag: state => state.location.advertisFlag,
            advertisementFlag: state => state.location.advertisementFlag,
            moduleBannerListShow: state => state.location.moduleBannerListShow, // 自定义菜单展示标识
            moduleBannerList: state => state.location.moduleBannerList, // 自定义菜单数组
            source: state => state.source, // 上海小程序跳转过来的标识
            sourceTimeSh: state => state.sourceTimeSh, // 上海小程序跳转过来的时间戳
            paymentFlag: state => state.paymentFlag, // 防止重复获取上海小程序标识
            isStationNewObj: state => state.location.isStationNewObj, // 是否为新油站
            newStationFlag: state => state.location.newStationFlag, // 打开升级弹窗
            token: state => state.token,
            token3: state => state.token3,
            phone: state => state.phone,
            ytPhone: state => state.location.ytPhone,
            maskDialogFlag: state => state.location.maskDialogFlag,
            privacyIsShow: state => state.privacy.privacyIsShow,
            officialAccountParams: state => state.location.officialAccountParams, // 存储外部跳转进来携带的参数和小程序跳转过来的参数
        }),
        ...mapGetters([
            'isLogin',
            'registerLoginInformation',
            'cardList',
            'isHaveEntityCard', // 是否有实体卡
            'isHaveECard', // 是否有电子卡
            'cardTopinfo',
            'markerArr',
        ]),

        navBarStyle() {
            const menuRectHeight = (this.menuRect || { height: 32 })['height'];
            return {
                height: menuRectHeight - 2 + 'px',
                'margin-top': 22 - menuRectHeight / 2 + 'px',
            };
        },
        bottomCoverViewHeight() {
            if (this.bannerListCity.length > 0 && this.bannerListCountry.length > 0) {
                return {
                    bottom: -140 + 'px',
                };
            } else if (this.bannerListCity.length > 0 || this.bannerListCountry.length > 0) {
                return {
                    bottom: -60 + 'px',
                };
            }
        },
        swiperMargin() {
            if (this.cardList.length === 2) {
                this.nextMargin = '20rpx';
                return {
                    marginRight: '15rpx',
                };
            } else {
                this.previousMargin = 0;
                this.nextMargin = 0;
                return {
                    marginRight: 0,
                };
            }
        },
        scroll() {
            return this.newStationFlag || this.advertisFlag;
        },
    },
    watch: {
        cardList: function (val) {
            if (this.typeBtn === 'tj' && this.cardList.length === 0) {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/add-oilcard/main',
                });
            }
        },
        isLogin: {
            handler(val, oldVal) {
                if (val && this.city !== '') {
                    console.log('首页watch====');
                    this.$store.dispatch('getBanner', this.city);
                    this.$store.dispatch('getMenuData', this.city);
                }
                // if (JSON.stringify(val) !== '{}' && this.isLogin) {
                //   // 获取是否是新油站
                //   this.$store.dispatch('getNewOilStation', this.selectMarker.stationCode)
                // }
            },
            deep: true,
            immediate: true,
        },
        // selectMarker: {
        //   handler (val) {
        //     if (JSON.stringify(val) !== '{}' && this.isLogin) {
        //       // 获取是否是新油站
        //       this.$store.dispatch('getNewOilStation', this.selectMarker.stationCode)
        //     }
        //   },
        //   deep: true,
        //   immediate: true
        // }
    },
    methods: {
        // 点击省份轮播图
        async countryOrCityBannerJump(item) {
            // if (item.linkType == '1') {
            //     if (item.url !== '') {
            //         if (item.url.includes('openId')) {
            //             let openIdRes = await openidEncryption();
            //             if (openIdRes.status === 0) {
            //                 uni.navigateTo({
            //                     url: `/packages/web-view/pages/home/<USER>
            //                         item.url.indexOf('?') != -1
            //                             ? `${item.url}&miniProgramOpenId=${openIdRes.data}`
            //                             : `${item.url}?miniProgramOpenId=${openIdRes.data}`,
            //                     )}`,
            //                 });
            //             }
            //         } else {
            //             // 跳转h5 外部
            //             let res = await mobilePhoneNumberEncryption({
            //                 phone: this.registerLoginInformation.phone,
            //             });
            //             // let openidRes = await openidEncryption()
            //             // 如果返回的是成功的响应
            //             if (res.status === 0) {
            //                 // 跳转h5
            //                 uni.navigateTo({
            //                     url: `/packages/web-view/pages/home/<USER>
            //                         item.url.indexOf('?') != -1 ? `${item.url}&phone=${res.data}` : `${item.url}?phone=${res.data}`,
            //                     )}&name=${item.name}`,
            //                 });
            //             }
            //         }
            //     } else {
            //         return;
            //     }
            // } else if (item.linkType == '2') {
            //     // 跳转小程序
            //     wx.navigateToMiniProgram({
            //         appId: item.appId,
            //         path: item.path,
            //         envVersion: 'release', // 正式版
            //     });
            // } else if (item.linkType == '3') {
            //     if (item.url !== '') {
            //         //内部链接
            //         uni.navigateTo({
            //             url: item.url,
            //         });
            //     } else {
            //         return;
            //     }
            // } else {
            //     return;
            // }
            try {
                if (item.linkType == '1') {
                    if (item.url === '') {
                        return;
                    }
                    let navigateUrl = '';
                    if (item.url.includes('openId')) {
                        let openIdRes = await openidEncryption();
                        if (openIdRes.status === 0) {
                            navigateUrl = `${
                                item.url.indexOf('?') != -1
                                    ? `${item.url}&miniProgramOpenId=${openIdRes.data}`
                                    : `${item.url}?miniProgramOpenId=${openIdRes.data}`
                            }`;
                        }
                    } else {
                        let res = await mobilePhoneNumberEncryption({ phone: this.registerLoginInformation.phone });
                        if (res.status === 0) {
                            navigateUrl = `${
                                item.url.indexOf('?') != -1 ? `${item.url}&phone=${res.data}` : `${item.url}?phone=${res.data}`
                            }`;
                        }
                    }

                    if (navigateUrl !== '') {
                        uni.navigateTo({
                            url: `/packages/web-view/pages/home/<USER>
                        });
                    }
                } else if (item.linkType == '2') {
                    wx.navigateToMiniProgram({
                        appId: item.appId,
                        path: item.path,
                        envVersion: 'release',
                    });
                } else if (item.linkType == '3') {
                    if (item.url == '') {
                        return;
                    }
                    uni.navigateTo({
                        url: item.url,
                    });
                } else {
                    return;
                }
            } catch (error) {
                console.error('An error occurred:', error);
                // 可以根据具体情况进行错误处理
            }
        },
        // 优途小程序跳转过来以后小判断手机号是否相等
        comparePhoneNumbers(phone1, phone2) {
            console.log('对比手机号');
            // 提取手机号的前三位和后四位
            const phone1Prefix = phone1.substring(0, 3);
            const phone1Suffix = phone1.substring(phone1.length - 4);
            const phone2Prefix = phone2.substring(0, 3);
            const phone2Suffix = phone2.substring(phone2.length - 4);

            // 比较前三位和后四位是否相等
            if (phone1Prefix === phone2Prefix && phone1Suffix === phone2Suffix) {
                return;
            } else {
                // this.$store.dispatch('zjShowModal', {
                //     title: `当前登录手机号${phone1.substring(0, 3) + '****' + phone1.substring(7)}与优途${
                //         this.ytPhone
                //     }不一致,请您确认是否需要重新登录`,
                //     confirmText: '我知道了',
                //     cancelText: '',
                //     confirmColor: '#333',
                //     success: async res => {
                //         if (res.confirm) {
                //             this.$store.commit('setYtPhone', '');
                //             this.$store.dispatch('setOfficialAccountParams', '');
                //         } else if (res.cancel) {
                //         }
                //     },
                // });
                uni.showModal({
                    title: `当前登录手机号${phone1.substring(0, 3) + '****' + phone1.substring(7)}与优途${
                        this.ytPhone
                    }不一致,请您确认是否需要重新登录`,
                    showCancel: false,
                    confirmText: '我知道了',
                    confirmColor: '#333',
                    maskClosable: true, // 不允许点击遮罩层关闭弹窗
                    success: res => {
                        if (res.confirm) {
                            this.$store.commit('setYtPhone', '');
                            this.$store.dispatch('setOfficialAccountParams', '');
                        }
                    },
                });
            }
        },
        //油卡充值
        chargeOilCard(index) {
            uni.navigateTo({
                url: '/packages/oil-card/pages/home/<USER>' + index,
            });
        },
        handleChangeSwiperItem(e) {
            const { current, source } = e.detail;
            if (source == 'touch') {
                //用户触摸引起
                this.swiperIndex = current;
                if (this.swiperIndex == this.cardList.length - 1) {
                    this.nextMargin = 0;
                } else {
                    this.nextMargin = '20rpx';
                }
                if (current == 0) {
                    this.previousMargin = 0;
                } else {
                    this.previousMargin = '30rpx';
                }
            }
        },
        // 卡数据显示切换
        async showDefaultCard(e, index) {
            console.log('卡切换', 'ssssssssss');
            this.$store.dispatch('card/setCardActiveed', index);
        },
        //点击查看首页顶部用户卡相关信息
        showUserCardInfo(flag) {
            this.showCardNo = !flag;
            if (this.showCardNo && this.isLogin) {
                this.$store.dispatch('card/getDefaultCardInfo');
            }
        },
        // 获取胶囊按钮的相关信息
        getMenuRect() {
            try {
                let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
                if (menuButtonInfo !== null && typeof menuButtonInfo === 'object') {
                    return menuButtonInfo;
                }
            } catch (err) {
                return null;
            }
        },
        // 提示关闭点击事件
        clickCloseTip() {
            this.isShowTip = false;
        },
        // 对应气泡点击事件
        clickCallOut(e) {
            let markerId = e.markerId;
            let index = this.showMarkerArr.findIndex(item => {
                return item.id == markerId;
            });
            let item = this.showMarkerArr[index];
            uni.openLocation({
                latitude: Number(item.latitude),
                longitude: Number(item.longitude),
                name: item.stationName,
                address: item.address,
            });
        },
        // 地图marker点击事件
        clickMarker(e) {
            // #ifdef MP-WEIXIN
            let markerId = e.markerId;
            // #endif
            // #ifdef QUICKAPP-WEBVIEW-HUAWEI
            let markerId = e.detail.markerId;
            // #endif

            let index = this.showMarkerArr.findIndex(item => {
                return item.id == markerId;
            });

            this.$store.commit('setSelectMarker', this.showMarkerArr[index]);
        },
        // 视野发生变化时触发
        regionchangeAction(e) {
            // let scale = e.detail.scale
            //this.$store.commit('setScale', scale)
        },
        // 点击重新定位点击事件
        // clickLocation () {
        //   this.authorizedLocationFlag ? this.$store.dispatch("uploadLocation", () => { }) : this.$store.dispatch("initLocation", { callback: () => { }, val: 'loactionFlag' });
        // },
        // 点击重新定位点击事件
        clickLocation() {
            try {
                wx.getPrivacySetting({
                    success: res => {
                        let params = {
                            params: this.params,
                            btnType: this.btnType,
                        };
                        let _this = this;
                        console.log(res.needAuthorization, 'res.needAuthorization');
                        if (res.needAuthorization) {
                            this.$store.dispatch('receiveButtonParameters', {
                                privacySuccess(res) {
                                    if (res.confirm) {
                                        // _this.$wxLogin.init()
                                        // _this.getPhoneNumberLogin(e)
                                        _this.authorizedLocationFlag
                                            ? _this.$store.dispatch('uploadLocation', () => {})
                                            : _this.$store.dispatch('initLocation', { callback: () => {}, val: 'loactionFlag' });
                                    } else if (res.cancel) {
                                    }
                                },
                            });
                            this.$store.commit('setFunctionButtons', params);
                        } else {
                            _this.authorizedLocationFlag
                                ? _this.$store.dispatch('uploadLocation', () => {})
                                : _this.$store.dispatch('initLocation', { callback: () => {}, val: 'loactionFlag' });
                        }
                    },
                    fail: () => {},
                });
            } catch (error) {
                this.authorizedLocationFlag
                    ? this.$store.dispatch('uploadLocation', () => {})
                    : this.$store.dispatch('initLocation', { callback: () => {}, val: 'loactionFlag' });
            }
        },
        // 车辆选择点击事件
        clickVehicle(index) {
            this.selectPlateIndex = index;
        },

        // 点击更多油站
        clickMoreYZ() {
            uni.navigateTo({
                url: `/packages/location/pages/home/<USER>
            });
        },
        // 点击导航
        clickNaviStateion() {
            if (this.selectMarker.openState != 1) {
                return uni.showModal({
                    title: '提示',
                    content: `该油站暂未开通线上加油功能，是否继续前往？`,
                    confirmColor: this.grayHomePage ? '#7f7f7f' : '#FF8200',
                    cancelText: '更换油站',
                    // confirmText: "去导航",
                    success: res => {
                        if (res.confirm) {
                            uni.openLocation({
                                latitude: Number(this.selectMarker.latitude),
                                longitude: Number(this.selectMarker.longitude),
                                name: this.selectMarker.stationName,
                                address: this.selectMarker.address,
                            });
                        }
                    },
                });
            } else {
                uni.openLocation({
                    latitude: Number(this.selectMarker.latitude),
                    longitude: Number(this.selectMarker.longitude),
                    name: this.selectMarker.stationName,
                    address: this.selectMarker.address,
                });
            }
        },
        //webview跳转地址方法（ETC签约页面）
        async webViewLinkFun() {
            const { appId, webViewUrl } = this.pageConfig;
            const { data } = await getOtherUserInfoPost({
                appNo: appId,
            });
            let linkUrl = webViewUrl + '?' + this.queryStrFun(data);
            uni.navigateTo({
                url: `/packages/web-view/pages/home/<USER>/main?src=${encodeURIComponent(linkUrl)}`,
            });
        },
        //拼接查询字符串方法
        queryStrFun(obj) {
            let str = '';
            for (let key in obj) {
                str += `&${key}=${obj[key]}`;
            }
            return str ? str.substring(1) : '';
        },
        // 我的页面跳转
        skipMy() {
            uni.navigateTo({
                url: '/packages/my-center/pages/home/<USER>',
            });
        },
        // 页面跳转方法
        async loginRouter(type) {
            if (type == 'addv') {
                uni.navigateTo({
                    url: '/packages/venicle-set/pages/home/<USER>',
                });
            } else if (type == 'ykcz') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/home/<USER>',
                });
            } else if (type == 'kjjy') {
                let venInfo;
                await this.$store.dispatch('initLocation', {
                    callback: () => {
                        // // 判断当前获取油站是否为新站的接口是不是调用失败 this.isStationNewObj = {} 调用失败
                        // if (JSON.stringify(this.isStationNewObj) !== '{}') {
                        //   // // 这里调用重新获取油站是为了 如果当前用户在>500<1000米是进入查询订单页面，导致升级弹窗不能显示
                        //   // this.$store.dispatch('uploadLocation', () => {
                        //   // })
                        //   // 是新加油站并且距离《500米
                        //   if (this.isStationNewObj.onlineType === 1 && this.isStationNewObj.onlineType !== '' && JSON.stringify(this.isStationNewObj) !== '{}' && Number(this.selectMarker.distance) < 500) {
                        //     this.$store.commit('setCloseDialog', true)
                        //     return
                        //   }
                        // }
                        if (this.venicleList) {
                            venInfo = encodeURIComponent(JSON.stringify(this.venicleList[this.selectPlateIndex]));
                        }
                        // if (this.selectMarker.openState != 1) {
                        //   return uni.showModal({
                        //     title: "提示",
                        //     content: `该油站暂未开通线上加油功能，请更换其它油站或进行线下支付`,
                        //     cancelText: "我知道了",
                        //     confirmText: "去导航",
                        //     success: (res) => {
                        //       if (res.confirm) {
                        //         uni.openLocation({
                        //           latitude: Number(this.selectMarker.latitude),
                        //           longitude: Number(this.selectMarker.longitude),
                        //           name: this.selectMarker.stationName,
                        //           address: this.selectMarker.address,
                        //         });
                        //       }
                        //     },
                        //   });
                        // }

                        uni.navigateTo({
                            url: `/packages/place-order/pages/home/<USER>
                                this.selectMarker,
                            )}`,
                        });
                    },
                    val: 'loactionFlag',
                });
            } else if (type == 'pay-code') {
                // 跳转其他小程序
            } else if (type == 'ad') {
                this.webViewLinkFun();
            } else if (type == 'oilCardQuery') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/manage-oilcard/main',
                    // url: "/packages/oil-card/pages/oilCardSkinChange/main",
                });
            } else if (type == 'coupon') {
                uni.navigateTo({
                    url: '/packages/coupon/pages/home/<USER>',
                });
            } else if (type == 'my') {
                // uni.navigateTo({
                //   url: `/pages/tabar/main`,
                // });
                // return
                uni.navigateTo({
                    url: '/packages/my-center/pages/home/<USER>',
                    // url: '/packages/avatarNickname/pages/home/<USER>'
                });
            } else if (type == 'tjyk') {
                uni.navigateTo({
                    url: '/packages/oil-card/pages/add-oilcard/main',
                });
            } else if (type == 'eCardApply') {
                if (this.isHaveECard) {
                    //有电子卡的话，跳转到油卡管理
                    uni.navigateTo({
                        url: '/packages/oil-card/pages/manage-oilcard/main?index=' + this.cardList.findIndex(item => item.cardType !== 0),
                    });
                }
                //电子卡申请
                else {
                    uni.showModal({
                        content: '您名下所有的中石油加油卡（包含实体卡和电子卡）预留手机号，将变为新开卡预留的手机号',
                        confirmColor: this.grayHomePage ? '#7f7f7f' : '#FF8200',
                        showCancel: false,
                        success: res => {
                            if (res.confirm) {
                                uni.navigateTo({
                                    url: '/packages/ecard-apply/pages/apply/main',
                                });
                            } else if (res.cancel) {
                                return;
                            }
                        },
                    });
                }
            }
        },
        async getPhoneNumber(e) {
            this.typeBtn = e.btnType;
            // 消息模板相关逻辑
            if (e.btnType == 'kjjy') {
                let timestamp = Date.parse(new Date());
                let msgAuthorizeTime = this.$Storage.msgAuthorizeTime;
                let msgAuthorizeList = this.$Storage.msgAuthorizeList;
                console.log('liyangyang123', this.$Storage);
                let app = getApp();
                let msgAFun = async () => {
                    let msgTList = await getMsgTemplates();
                    // globalData.msgTemplates = msgTList.data
                    /**
                     * autList：
                     * {templateId: "rJytaukGt_rDeiTG68REL1yIvUoOFxLt-XvktOoNfqE", title: "充值成功"}
                     * {templateId: "dXXBI2koMKaCTjCtZ9uFaOcNzUy2I3ZJPNh8D3ePFh0", title: "加油成功"}
                     */
                    let autList = msgTList.data;
                    let haveAutList = [];
                    let temSum = 0;
                    if (msgAuthorizeList != -1) {
                        for (let i = 0; i < autList.length; i++) {
                            if (msgAuthorizeList[autList[i].templateId] != 'accept') {
                                haveAutList.push(autList[i].templateId);
                                temSum += 1;
                            }
                        }
                    } else {
                        haveAutList = autList;
                    }
                    app.globalData.isThisTimeTemCancel = temSum >= autList.length;
                    let tMsgRes = await this.$util.requestSubscribeMessage(haveAutList).catch();
                    console.log('liyangyang', tMsgRes);
                    if (tMsgRes.errMsg == 'requestSubscribeMessage:ok') {
                        msgAuthorizeList.update(tMsgRes);
                        msgAuthorizeTime.update(timestamp);
                    } else {
                        msgAuthorizeTime.update(-1);
                    }
                };
                console.log('----缓存时间----', msgAuthorizeTime.value);
                console.log('----缓存信息----', msgAuthorizeList.value);
                console.log('liyangyang1111111', app.globalData);
                if (!app.globalData.isThisTimeTemCancel) {
                    if (msgAuthorizeTime.value == -1) {
                        await msgAFun();
                    } else {
                        // 时间判断
                        if (timestamp - msgAuthorizeTime.value > 3600000) {
                            await msgAFun();
                        } else {
                            // 是否全部授权判断
                            let isAllAuthorize = true;
                            for (const key in msgAuthorizeList.value) {
                                console.log(msgAuthorizeList.value[key]);
                                if (msgAuthorizeList.value[key] != 'accept') {
                                    isAllAuthorize = false;
                                    break;
                                }
                            }
                            if (!isAllAuthorize) {
                                await msgAFun();
                            }
                        }
                    }
                }
            } else if (e.btnType == 'lbt') {
                if (this.isLogin) {
                    console.log('params', e.params);
                    this.countryOrCityBannerJump(e.params);
                    return;
                }
            }

            // 登录相关逻辑
            if (this.isLogin) {
                e.btnType === 'eCardApply'
                    ? await this.$store.dispatch('card/getAllCardList')
                    : this.$store.dispatch('card/getAllCardList');
                // await this.$store.dispatch("uploadVenicleList");
                this.codeLoginFlag = false;
                return this.loginRouter(e.btnType);
            }
        },
        async adGetPhoneNumber(e) {
            if (this.isLogin) {
                // 已登录
                return this.loginRouter('ad');
            }
            if (e.mp.detail.errMsg === 'getPhoneNumber:fail user deny') {
                //不同意授权
            }
            if (e.mp.detail.errMsg === 'getPhoneNumber:ok') {
                //同意授权
                this.$wxLogin.registerByAuthPhoneNumber(e.mp.detail).then(
                    async res => {
                        // await this.$store.dispatch("uploadVenicleList");
                        this.loginRouter('ad');
                    },
                    err => {},
                );
            }
        },
        // 省份选择点击事件
        clickSelectLocation() {
            // 误删可能以后还要用
            // uni.navigateTo({
            // 	url: "./select-location"
            // })
        },
        closeEvent() {
            this.$store.commit('setAdvertisFlag', false);
            this.$store.commit('setAdvertisementFlag', 0);
        },

        // 点击动态菜单跳转
        menuClick(item) {
            if (item.linkType == 1) {
                uni.navigateTo({
                    url: `/packages/web-view/pages/home/<USER>
                });
            } else if (item.linkType == 2) {
                // 跳转小程序
                wx.navigateToMiniProgram({
                    path: item.path,
                    envVersion: 'release', // 正式版  release
                    appId: item.appId,
                });
            } else if (item.linkType == 4) {
                //
                if (item.menuUrl === '/packages/ecard-apply/pages/apply/main') {
                    if (this.isHaveECard) {
                        //有电子卡的话，跳转到油卡管理
                        uni.navigateTo({
                            url:
                                '/packages/oil-card/pages/manage-oilcard/main?index=' +
                                this.cardList.findIndex(item => item.cardType !== 0),
                        });
                    }
                    //电子卡申请
                    else {
                        uni.showModal({
                            content: '您名下所有的中石油加油卡（包含实体卡和电子卡）预留手机号，将变为新开卡预留的手机号',
                            confirmColor: this.grayHomePage ? '#7f7f7f' : '#FF8200',
                            showCancel: false,
                            success: res => {
                                if (res.confirm) {
                                    uni.navigateTo({
                                        url: '/packages/ecard-apply/pages/apply/main',
                                    });
                                } else if (res.cancel) {
                                    return;
                                }
                            },
                        });
                    }
                } else {
                    //内部链接
                    uni.navigateTo({
                        url: item.menuUrl,
                    });
                }
            }
        },
        // 关闭升级新站弹窗
        closeNewStationDialog() {
            this.$store.commit('setCloseDialog', false);
        },
        // 立即升级
        async submitUpgrade() {
            this.$store.dispatch('getToken3', 'upgrade');
        },
    },
    destroyed() {},
};
</script>

<style scoped lang="scss">
.mask-dialog {
}
.view {
    background-color: #f5f5f5;
    height: 100vh;
}
.solid {
    border: 1px solid red;
}

.flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.flex-1 {
    flex: 1;
}

map {
    position: fixed;
    width: 375px;
}

.navi-view {
    position: fixed;
    z-index: 10;
    background: #f96702;

    // background-color: $bg-color;

    .state-view {
        width: 100vw;
    }

    .navi-bar {
        height: 44px;
        width: 100vw;
        background: #f96702;
        overflow: hidden;
        .navi-bar-content {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            height: 30px;
            margin-top: 6px;
            .my-icon-view {
                .navi-my-icon {
                    margin-left: 10px;
                    //   margin-top: 6px;
                    margin-right: 10px;
                    //   margin-bottom: 6px;
                    height: 28px;
                    width: 28px;
                    border-radius: 16px;
                    overflow: hidden;
                }
            }

            .navi-title {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translate(20%, -50%);
                color: #ffffff;
                font-size: 36rpx;
            }

            .pay-code-img {
                height: 26px;
                width: 26px;
                padding: 5px;
                margin-left: 5px;
                display: block;
            }
        }
    }
}

// 2022-03-03新增需求
.top-cover-view {
    position: fixed;
    left: 0;
    width: 100%;
    background-color: #fff;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: #666666;
    padding: 10rpx 0;
    overflow: hidden;
    .top-cover-content {
        width: 100%;
        box-sizing: border-box;
    }
    .paddlr20 {
        padding: 0 20rpx;
    }
    .userinfo-head {
        height: 45rpx;
        width: 45rpx;
        border-radius: 50%;
        z-index: 9999;
        .avatar {
            height: 45rpx;
            width: 45rpx;
            border-radius: 50%;
        }
    }
    .userinfo-top {
        float: left;
    }
    .pad-1 {
        padding-right: 15rpx;
    }
    .pad-2 {
        padding-left: 15rpx;
    }
}

.activity-view {
    position: fixed;
    right: 8px;
    .activity-icon {
        display: block;
        width: 55px;
        height: 50px;
    }
}

.location-icon {
    display: block;
    position: fixed;
    right: 0;
    width: 40px;
    height: 40px;
    z-index: 1;
    /*  #ifdef  MP-WEIXIN	 */
    padding: 15px 10px;

    bottom: calc(740rpx + env(safe-area-inset-bottom));
    /*  #endif  */
    /*  #ifdef  QUICKAPP-WEBVIEW-HUAWEI	 */
    right: 16px;
    bottom: calc(785rpx + env(safe-area-inset-bottom));
    /*  #endif  */
}

// 2022-03-03新增需求
.bottom-cover-view {
    bottom: 0rpx;
    position: absolute;
    left: 0;
    width: 100vw;
    border-radius: 20rpx;
    margin-bottom: 5px;
    // height: 300px;
    // transform: translateY(391px);
    .oilingService {
        background-color: #ffffff;
        border-radius: 20rpx;
        margin-bottom: 5px;
        margin-left: 15px;
        margin-top: 10px;
        width: 345px;
        // height:600rpx;
        // box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        padding: 20rpx 0;
        .oil-title {
            padding: 10rpx 24rpx 0;
            .top-title {
                color: #333;
                font-size: 34rpx;
                font-weight: 700;
            }
            .top-addCard {
                font-size: 28rpx;
                color: #999;
                font-family: PingFangSC-Regular;
            }
        }
        .oilcaed-div {
            position: relative;
            padding: 0 20rpx;
            border-radius: 20rpx;
            margin-top: 20rpx;
            .bgImg {
                height: 125rpx;
                border-radius: 20rpx;
                .top-bg {
                    border-radius: 20rpx;
                    display: block;
                    height: 125rpx;
                }
            }
            .oil-text {
                position: absolute;
                left: 0;
                right: 0;
                top: 0rpx;
                bottom: 0;
                padding: 0 40rpx;
                .ss-icon {
                    width: 70rpx;
                    height: 68rpx;
                    display: block;
                    border: 6rpx solid #fff;
                    border-radius: 50%;
                    background-color: #fff;
                    border-radius: 50%;
                }
                .oil-info {
                    margin-left: 20rpx;
                    .oilName {
                        font-family: PingFangSC-Medium;
                        font-size: 28rpx;
                        color: #ffffff;
                    }
                    .oilDetail {
                        opacity: 0.8;
                        font-family: PingFangSC-Regular;
                        font-size: 20rpx;
                        color: #ffffff;
                    }
                }
                .addOilbut {
                    color: #ffffff;
                    border: 1px solid #fff;
                    border-radius: 10rpx;
                    padding: 6rpx 20rpx;
                    font-size: 26rpx;
                }
            }
        }
        .oilcard-div {
            position: relative;
            height: 125rpx;
            padding: 0 20rpx;
            margin-top: 20rpx;
            border-radius: 20rpx;
            .swiper {
                height: 125rpx;
            }
            .swiper-box {
                height: 125rpx;
            }
            .oil-text {
                margin-right: 15rpx;
                background-color: #feefe4;
                padding: 0 28rpx;
                height: 125rpx;
                border-radius: 20rpx;
                .ss-icon {
                    width: 70rpx;
                    height: 68rpx;
                    display: block;
                    border: 6rpx solid #fff;
                    border-radius: 50%;
                    background-color: #fff;
                    border-radius: 50%;
                }
                .oil-info {
                    margin-left: 20rpx;
                    .oilName {
                        // font-family: PingFangSC-Medium;
                        font-size: 28rpx;
                        color: #333333;
                        .eye-block-iocn1 {
                            width: 17px;
                            height: auto;
                        }
                        div {
                            margin-right: 10rpx;
                        }
                    }
                    .oilDetail {
                        font-family: PingFangSC-Regular;
                        font-size: 24rpx;
                        color: #666666;
                        margin-top: 2rpx;
                        .card-user-name {
                            padding-right: 10rpx;
                        }
                        .card-mid {
                            color: #666666;
                            height: 35rpx;
                            margin-right: 10rpx;
                        }
                    }
                }
                .rechargeBut {
                    color: #f96702;
                    border: 1px solid #f96702;
                    border-radius: 10rpx;
                    padding: 6rpx 20rpx;
                    font-size: 26rpx;
                }
            }
        }
        .tab-div-wrap {
            width: 100%;
            height: 70px;
            display: flex;
            padding: 30rpx 0;
            .center-module-swiper {
                width: 100%;
                height: 70px;
                // margin-top: 10px;
                .center-module-swiper-item {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    padding-top: 5px;
                    .center-swiperItem-div {
                        // flex: 1 1 auto;
                        width: 25%;
                        height: 100%;
                        .center-swiperItem-div-item {
                            position: relative;
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: flex-start;
                            .center-swiperItem-name {
                                margin-top: 10rpx;
                                text-align: center;
                                color: #333;
                                font-family: PingFangSC-Regular;
                                font-size: 24rpx;
                            }
                            image {
                                width: 50rpx;
                                height: 50rpx;
                                margin-left: 50%;
                                transform: translateX(-50%);
                            }
                        }
                    }
                }
            }
            .tab-div {
                width: 100%;
                height: 70px;
                display: flex;
                // padding: 30rpx 0;
                position: relative;
                .tab-item {
                    position: relative;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    img {
                        width: 50rpx;
                        height: 50rpx;
                        // margin-left: 50%;
                        // transform: translateX(-50%);
                    }
                    div {
                        margin-top: 10rpx;
                        text-align: center;
                        color: #333;
                        // font-family: PingFangSC-Regular;
                        font-size: 24rpx;
                    }
                }
            }
        }
    }

    .service-station-view {
        background-color: #ffffff;
        border-radius: 20rpx;
        margin-bottom: 10rpx;
        margin-left: 15px;
        margin-top: 10px;
        width: 345px;
        // box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
        font-size: 28rpx;
        color: #999;
        font-family: PingFangSC-Regular;
        .ss-text-view {
            padding: 20rpx 24rpx 0;
            .sta-left {
                color: #333;
                font-size: 32rpx;
                font-weight: 700;
            }
            .sta-right {
                min-width: 60px;
            }
        }
        .station-mid {
            padding: 12rpx 24rpx 0 12rpx;
            margin-top: 6rpx;
            img {
                width: 30rpx;
                height: 30rpx;
            }
            .station-juli {
                margin-left: 10rpx;
            }
            .ss-navi-icon {
                width: 32rpx;
                height: 33rpx;
                margin-left: 10rpx;
            }
            .station-address {
                margin-left: 10rpx;
                max-width: 470rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .station-bot {
            padding: 12rpx 24rpx;
            font-size: 20rpx;
            span {
                border: 1px solid #999;
                border-radius: 10rpx;
                padding: 0 5rpx;
                margin-right: 10rpx;
            }
        }
        .btn-view {
            padding: 20rpx 24rpx;
            .btn-fast-fuel {
                width: 100%;
                background-color: #f96702;
                color: #ffffff;
                border-radius: 5px;
                height: 45px;
                line-height: 45px;
                text-align: center;
                font-family: PingFangSC-Regular;
                font-size: 36rpx;
            }
        }
    }
    .banner {
        // padding: 10px 15px 10px 15px;
        padding: 10rpx 30rpx 0rpx 30rpx;
        // background: #fff;
        // border-radius: 20rpx;
        // margin-bottom: 5px;
        // margin-left: 15px;
        // margin-top: 10px;
        // overflow: hidden;
        // padding: 20rpx 0;
        .swiper {
            width: 100%;
            height: 80px;
        }
        .swiper-img {
            width: 100%;
            height: 80px;
            border-radius: 5px;
        }
    }
    .banner-sec {
        margin-bottom: 20px;
    }
}

.marker-view {
    position: relative;
    background: #fff;
    width: 200rpx;
    height: 70rpx;
    border-radius: 20px;
    font-size: 24rpx;
    color: #333;
    margin-bottom: 10rpx;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);

    .marker-lable-view {
        height: 70rpx;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        .marker-navi-img {
            height: 15px;
            width: 15px;
            margin-left: 10rpx;
        }
    }
}
::v-deep .center-module-swiper .wx-swiper-dot {
    width: 10rpx; /*点的长*/
    height: 10rpx; /*点的高*/
    display: inline-flex;
    // margin-left: 5rpx; /* 修改点之间的间距 ，我这里挨的近为负数，分开点数值加大*/
    justify-content: space-between;
}
</style>
