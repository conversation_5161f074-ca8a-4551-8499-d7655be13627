<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="position: relative; background: #f7f7fb; padding-bottom: 20px">
        <zj-navbar title="评价详情" :border-bottom="false"></zj-navbar>
        <div class="card-default marlr15 mart12 pad-25-16" v-if="isPage">
            <div class="stationinfo fl-row fl-jus-bet" v-if="commentConfigListData.stationName">
                <img src="../../images/cnpc-logo.png" alt="" class="img-47" />
                <div class="detail f-1">
                    <div class="name-area fl-row fl-al-cen">
                        <p class="name font-16 weight-bold color-000">{{ commentConfigListData.stationName || '暂未查询到网点' }}</p>
                    </div>
                    <div class="oil-address font-12 weight-400 color-333">{{ commentConfigListData.address || '' }} </div>
                </div>
                <div class="color-999 font-12">距离您{{ commentConfigListData.distance || '0.00' }}KM</div>
            </div>
            <div class="pab18" v-for="(item, commentIndex) in commentConfigListDataRows" :key="commentIndex">
                <div class="fl-column">
                    <!-- <div class="font-15 weight-500 te-center color-333">{{ commentIndex + 1 || '' }}.{{ item.title || '' }}</div> -->
                    <div class="font-15 weight-500 te-center color-333">{{ item.title || '' }}</div>
                    <div class="padt10" v-if="item.showStarFlag">
                        <!-- 使用三目运算符来动态变化显示的是哪张图片,activeIndex是js中的分数,index是scoreArray的下标 -->
                        <div class="fl-row fl-al-cen fl-jus-cen" v-if="evaluateType != 'order' && evaluateType != 'o2oOrder'">
                            <div v-for="(itemSource, indexScore) in scoreArray" :key="indexScore">
                                <div
                                    @click="changeScore(indexScore + 1, commentIndex)"
                                    v-if="commentConfigListData.starImage.choosedImage && commentConfigListData.starImage.unChooseImage"
                                >
                                    <img
                                        :src="
                                            item.starLevel > indexScore
                                                ? commentConfigListData.starImage.choosedImage
                                                : commentConfigListData.starImage.unChooseImage
                                        "
                                        alt=""
                                        class="img-24"
                                    />
                                </div>
                                <div @click="changeScore(indexScore + 1, commentIndex)" v-else>
                                    <img :src="item.starLevel > indexScore ? fullStarUrl : nullStarUrl" alt="" class="img-24" />
                                </div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen fl-jus-cen" v-else>
                            <div v-for="(itemSource, indexScore) in scoreArray" :key="indexScore">
                                <div
                                    @click="changeScore(indexScore + 1, commentIndex)"
                                    v-if="item.starImage.choosedImage && item.starImage.unChooseImage"
                                >
                                    <img
                                        :src="item.starLevel > indexScore ? item.starImage.choosedImage : item.starImage.unChooseImage"
                                        alt=""
                                        class="img-24"
                                    />
                                </div>
                                <div @click="changeScore(indexScore + 1, commentIndex)" v-else>
                                    <img :src="item.starLevel > indexScore ? fullStarUrl : nullStarUrl" alt="" class="img-24" />
                                </div>
                            </div>
                        </div>
                        <div class="color-E64F22 font-13 te-center padt10" v-if="evaluateType != 'order' && evaluateType != 'o2oOrder'">
                            <div>{{ commentConfigListData.tipList[item.starLevel - 1].tip }}</div>
                            <!-- <div v-if="item.starLevel == 4">满意</div>
                            <div v-if="item.starLevel == 3">一般</div>
                            <div v-if="item.starLevel == 2">不满意</div>
                            <div v-if="item.starLevel == 1">非常不满意</div>  -->
                        </div>
                        <div class="color-E64F22 font-13 te-center padt10" v-else>
                            <div>{{ item.tipList[item.starLevel - 1].tip }}</div>
                            <!-- <div v-if="item.starLevel == 4">满意</div>
                            <div v-if="item.starLevel == 3">一般</div>
                            <div v-if="item.starLevel == 2">不满意</div>
                            <div v-if="item.starLevel == 1">非常不满意</div>  -->
                        </div>
                    </div>
                </div>
                <div class="padt10" v-if="item.showLabelChooseFlag && evaluateType != 'code'">
                    <div class="font-13 color-333 fl-row fl-al-cen"
                        >请选择评价标签
                        <div class="color-999 font-10" v-if="item.chooseTagType != 1">（非必填）</div>
                        <div class="color-999 font-10" v-if="item.chooseTagType == 1">（必填）</div>
                    </div>
                    <div
                        v-for="(acItem, indexItem) in item.items"
                        :key="indexItem"
                        class="fl-row fl-al-cen fl-wrap fl-jus-sta con-list"
                        v-if="item.starLevel == acItem.labelType"
                    >
                        <div
                            @click="experienceHandle(label, commentIndex)"
                            class="item-cell mart8"
                            :class="item.choosedLabels.indexOf(label) != '-1' ? 'item-sel' : ''"
                            v-for="(label, isx) in acItem.labels"
                            :key="isx"
                        >
                            {{ label }}
                        </div>
                    </div>
                </div>
                <div class="padt10 fl-row fl-al-cen fl-wrap" v-else>
                    <div
                        v-for="(acItem, indexItem) in item.items"
                        :key="indexItem"
                        class="fl-row fl-al-cen fl-wrap"
                        v-if="item.starLevel == acItem.labelType"
                    >
                        <div
                            @click="experienceHandleCode(acItem, commentIndex)"
                            class="item-cell mart8 marr8"
                            :class="item.choosedLabels.findIndex(lab => lab.labelId == acItem.labelId) > -1 ? 'item-sel' : ''"
                        >
                            {{ acItem.describe }}
                        </div>
                    </div>
                </div>
                <div v-if="item.chooseFlag">
                    <div v-for="(questionItem, questionIndex) in item.chooseList" :key="questionIndex" class="ques-content fl-row">
                        <img
                            @click="chooseHandle(questionItem, questionIndex, commentIndex, item.chooseList)"
                            class="check-radio"
                            :src="
                                questionItem.checked
                                    ? require('../../images/circle-select.png')
                                    : require('../../images/circle-unselect.png')
                            "
                            alt=""
                        />
                        <!-- {{ item2.checked }} -->
                        <div class="check-info">
                            <div class="describe">{{ questionItem.sortNum + '.' + questionItem.chooseTitle }}</div>
                            <img v-if="questionItem.imgUrl" :src="questionItem.imgUrl" alt="" />
                            <div class="check-input" v-if="questionItem.commentFlag && questionItem.checked">
                                <input
                                    cursor-spacing="15"
                                    maxlength="50"
                                    type="text"
                                    placeholder="请输入"
                                    v-model="questionItem.comment"
                                    @input="checkInput($event, commentIndex, questionIndex)"
                                />
                                <!-- <textarea class="textarea" rows="1" wrap="off" maxlength="50" :show-count="false" fixed type="text" placeholder="请输入" v-model="questionItem.comment" @input="checkInput($event,commentIndex,questionIndex)" /> -->

                                <div class="count">{{ questionItem.adviceCount > 50 ? '50' : questionItem.adviceCount }}/50</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="item.showCommentFlag">
                    <div class="font-13 color-333 fl-row fl-al-cen">您的意见或建议<div class="color-999 font-10">（非必填）</div></div>
                    <div class="input-div font-13">
                        <textarea
                            type="text"
                            placeholder="请输入"
                            placeholder-class="color-666 font-13"
                            v-model="item.advice"
                            maxlength="100"
                            @input="adviceInput($event, commentIndex)"
                            fixed
                            :show-count="false"
                        />
                        <div class="fl-row fl-sp-end color-999 font-10">{{ item.adviceCount > 100 ? '100' : item.adviceCount }}/100</div>
                    </div>
                </div>
                <div class="padt10" v-if="item.showPictureFlag">
                    <div class="font-13 color-333 fl-row fl-al-cen"
                        >拍下您认为需要整改的地方<div class="color-999 font-10">（非必填）</div>
                    </div>
                    <div class="fl-row fl-al-cen mart8 fl-wrap con-list" v-if="item">
                        <div class="photo-area" v-for="(img, imgIndex) in item.picList" :key="index">
                            <img :src="img" alt="" class="area-img" @click="previewImage(imgIndex, commentIndex)" />
                            <img src="../../images/del.png" alt="" @click.stop="delImg(imgIndex, commentIndex)" class="img-14" />
                        </div>

                        <div
                            class="photo-div fl-column fl-al-jus-cen"
                            @click="handlerUpload('add', commentIndex)"
                            v-if="item.picList.length != 3"
                        >
                            <img src="../../images/photo.png" alt="" class="img-28" />
                            <div>{{ item.picList.length < 1 ? '拍照上传' : '再加一张' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="btn-box">
            <div v-if="isPage" class="primary-btn refund-btn mart16" @click="evaluateApplication"> 提交 </div>
        </div>

        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <div class="content">
                        <div class="font-14 color-333 weight-bold"
                            ><span class="weight-400 font-14 color-333">请评分“</span> {{ commentTitle }}”</div
                        >
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view
                        class="btn confirm"
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        @click.stop="clickBtn"
                        >确定
                    </view>
                </view>
            </div>
        </custom-popup>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import {
    QRCodeCommentConfigList,
    orderCommentConfigList,
    orderUserComment,
    orderQRCodeComment,
} from '../../../../s-kit/js/v3-http/https3/order/index.js';
import { baseType } from '../../../../../project.config.js';

// #ifdef MP-MPAAS
import appMixin from '../../diff-environment/app-img-update.js';
// #endif
// #ifndef MP-MPAAS
import wxMixin from '../../diff-environment/wx-img-update.js';
import zfbMixin from '../../diff-environment/zfb-img-update.js';
// #endif
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS
        wxMixin,
        zfbMixin,
        // #endif
    ],
    data() {
        return {
            fullStarUrl: require('../../images/star.png'),
            nullStarUrl: require('../../images/no-star.png'),
            minCount: 0,
            scoreArray: [1, 2, 3, 4, 5], //分为1-5个评分层次

            // advice: '', //建议
            evaluateShow: false,
            evaluateType: 'order',
            //评价配置评价选项列表数据
            commentConfigListDataRows: [],
            commentConfigListData: {},
            commentTitle: '',
            isPage: false,
            testIndex: -1,
            orderInfo: {},
            isOil: 0,
            imgPop: false,
            codeQusetion: [],
            evaluateTitle: '',
        };
    },
    onLoad(option) {
        console.log('option---', option);
        let optionsData = JSON.parse(decodeURIComponent(option.data));
        console.log('optionsData---', optionsData);
        this.$sKit.mpBP.tracker('我的消费订单', {
            seed: 'xfOrderBiz',
            pageID: 'evaluatePage',
            refer: optionsData.refer,
            channelID: clientCode,
        });
        this.evaluateType = optionsData.evaluateType;
        if (this.evaluateType != 'order' && this.evaluateType != 'o2oOrder') {
            // 二维码里有一个orgCode和placeName
            this.placeName = optionsData.placeName;
            this.orgCode = optionsData.orgCode;
            this.init();
        } else {
            // this.orderSubType = optionsData.orderSubType;
            // this.orderType = optionsData.orderType;
            this.isPage = true;
            this.isOil = optionsData.isOil;
            this.orderInfo = optionsData;
            console.log('this.orderInfo---', this.orderInfo);
            this.getOrderCommentConfigList();
        }
        this.handlerUpload = this.$sKit.commonUtil.throttleUtil(this.handlerUpload);

        this.handlerUpload = this.$sKit.commonUtil.throttleUtil(this.handlerUpload);
    },

    onShow() {
        // 条件限制：需要判断油站距离客户的距离，超出500米不允许客户打开该评价页面。
    },
    methods: {
        init() {
            // 获取位置信息，判断当前位置距离油站是否>500米
            this.$store.dispatch('initLocationV3_app', {
                callback: () => {
                    if (Number(this.selectMarkerV3.distance) < 0.5) {
                        this.getQRCodeCommentConfigList();
                    } else {
                        this.isPage = false;
                        this.$store.dispatch('zjShowModal', {
                            content: '您距离加油站超过500米，请到加油站内进行评价。',
                            confirmText: '确定',
                            success: res => {
                                if (res.confirm) {
                                    console.log('用户点击确定');
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                        return false;
                    }
                },
            });
            // this.getQRCodeCommentConfigList();
        },

        // 根据油站机构编号、当前定位经纬度等信息查询扫码评价配置的评价选项信息列表。
        async getQRCodeCommentConfigList() {
            let params = {
                stationCode: this.orgCode,
                posx: this.lonV3,
                posy: this.latV3,
                placeName: this.placeName,
            };
            let res = await QRCodeCommentConfigList(params, {
                handleErrorFn: () => {
                    this.backMainPage();
                },
            });
            if (res.success) {
                this.isPage = true;
                this.commentConfigListData = res.data;
                this.commentConfigListDataRows = res.data.rows.map((val, index) => {
                    val.starLevel = 5;
                    val.choosedLabels = [];
                    val.images = [];
                    val.picList = [];
                    val.advice = '';
                    val.adviceCount = 0;
                    val.choosedQuestion = [];
                    val.chooseList?.forEach(e => {
                        e.checked = false;
                        e.adviceCount = 0;
                    });
                    return val;
                });
                console.log(this.commentConfigListDataRows, 'this.commentConfigListDataRows');
                // this.commentConfigListDataRows = dataList;
            }
        },
        // 根据油站机构编号查询订单评价配置的评价选项信息列表。
        async getOrderCommentConfigList() {
            let params = {
                // stationCode:'1-A4301-C001-S006',
                // orderType: 2,
                // orderSubType: '13',
                stationCode: this.orderInfo.stationCode,
                orderType: this.orderInfo.orderType ? this.orderInfo.orderType : this.orderInfo.orderSubType,
                orderSubType: this.orderInfo.orderSubType ? this.orderInfo.orderSubType : this.orderInfo.orderType,
            };
            let res = await orderCommentConfigList(params, {
                handleErrorFn: () => {
                    this.backMainPage();
                },
            });
            if (res.success) {
                this.isPage = true;
                // console.log('res.data--', res.data.rows);
                this.commentConfigListDataRows = res.data.rows.map((val, index) => {
                    val.choosedLabels = [];
                    val.images = [];
                    val.picList = [];
                    val.advice = '';
                    val.adviceCount = 0;
                    val.starLevel = 5;
                    val.choosedQuestion = [];
                    val.chooseList?.forEach(e => {
                        e.checked = false;
                        e.adviceCount = 0;
                    });
                    return val;
                });
                console.log(this.commentConfigListDataRows, 'this.commentConfigListDataRows');
            }
        },
        //设置评分
        changeScore(indexScore, commentIndex) {
            // 对第一个星星特殊处理，只有一个的时候，点击可以取消，否则无法作0星评价
            let activeIndex;
            if (indexScore == 1) {
                if (activeIndex == 1) {
                    activeIndex = 0;
                } else {
                    activeIndex = 1;
                }
            } else {
                activeIndex = indexScore;
            }
            // 对最少颗星星的限制
            if (activeIndex < this.minCount) activeIndex = this.minCount;
            if (this.evaluateType == 'order' || this.evaluateType == 'o2oOrder') {
                this.commentConfigListDataRows[commentIndex].starLevel = activeIndex;
                this.commentConfigListDataRows[commentIndex].choosedLabels = [];
                this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
            } else {
                let newObj = this.commentConfigListData;
                newObj.rows[commentIndex].starLevel = activeIndex;
                newObj.rows[commentIndex].choosedLabels = [];
                this.commentConfigListDataRows = newObj.rows;

                // 要更改的数据源（可以是一个对象或者数组） / 要更改的具体数据 （索引） / 重新赋的值(any)
                this.$set(this.commentConfigListDataRows, commentIndex, newObj.rows[commentIndex]);
                // console.log(newObj.rows,'ooooo---')
            }
        },
        // 选择标签
        experienceHandle(label, commentIndex) {
            console.log(label, commentIndex);
            let isExitLabelIndex = this.commentConfigListDataRows[commentIndex].choosedLabels.indexOf(label);
            if (isExitLabelIndex > -1) {
                this.commentConfigListDataRows[commentIndex].choosedLabels.splice(isExitLabelIndex, 1);
            } else {
                this.commentConfigListDataRows[commentIndex].choosedLabels.push(label);
            }
            this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
            console.log(this.commentConfigListDataRows);
        },
        // 扫码评价选择标签
        experienceHandleCode(acItem, commentIndex) {
            console.log(acItem, commentIndex);
            let newObj = this.commentConfigListData;
            let isExitLabelIndex = this.commentConfigListDataRows[commentIndex].choosedLabels.findIndex(
                item => item.labelId == acItem.labelId,
            );
            if (isExitLabelIndex > -1) {
                newObj.rows[commentIndex].choosedLabels.splice(isExitLabelIndex, 1);
            } else {
                newObj.rows[commentIndex].choosedLabels.push(acItem);
            }
            this.commentConfigListDataRows = newObj.rows;
            // 要更改的数据源（可以是一个对象或者数组） / 要更改的具体数据 （索引） / 重新赋的值(any)
            this.$set(this.commentConfigListDataRows, commentIndex, newObj.rows[commentIndex]);
            console.log(this.commentConfigListDataRows);
        },
        // 选择题选择选项
        chooseHandle(item, index, commentIndex, list) {
            console.log(item, index, 'choose');
            // 单选
            list.forEach((item, itemIndex) => {
                if (index == itemIndex) {
                    list[itemIndex].checked = !list[itemIndex].checked;
                    list[itemIndex].comment = '';
                    list[itemIndex].adviceCount = 0;
                    return;
                }
                list[itemIndex].checked = false;
            });
            // 扫码评价选择题
            this.commentConfigListDataRows[commentIndex].choosedQuestion = [];
            let arr = [];
            if (item.checked) {
                arr.push(item);
            }
            this.commentConfigListDataRows[commentIndex].choosedQuestion = arr.map(e => {
                return {
                    chooseId: e.chooseId,
                    comment: e.comment,
                    sortNum: e.sortNum,
                };
            });
            this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
            console.log(this.commentConfigListDataRows);
        },
        // 选择题填空字数
        checkInput(e, commentIndex, index) {
            this.commentConfigListDataRows[commentIndex].chooseList[index].comment = e.detail.value.substring(0, 50);
            this.commentConfigListDataRows[commentIndex].chooseList[index].adviceCount = e.detail.value.length;
            this.commentConfigListDataRows[commentIndex].choosedQuestion.forEach(i => {
                if (i.chooseId == this.commentConfigListDataRows[commentIndex].chooseList[index].chooseId) {
                    i.comment = e.detail.value.substring(0, 50); // 选择评论
                }
            });
            this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
        },
        //删除图片
        delImg(imgIndex, commentIndex) {
            if (this.evaluateType == 'order' || this.evaluateType == 'o2oOrder') {
                this.commentConfigListDataRows[commentIndex].picList.splice(imgIndex, 1);
                this.commentConfigListDataRows[commentIndex].images.splice(imgIndex, 1);
                this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
            } else {
                let newObj = this.commentConfigListData;
                newObj.rows[commentIndex].picList.splice(imgIndex, 1);
                newObj.rows[commentIndex].images.splice(imgIndex, 1);
                this.commentConfigListDataRows = newObj.rows;
                this.$set(this.commentConfigListDataRows, commentIndex, newObj.rows[commentIndex]);
            }
        },
        adviceInput(e, commentIndex) {
            if (this.evaluateType == 'order' || this.evaluateType == 'o2oOrder') {
                this.commentConfigListDataRows[commentIndex].advice = e.detail.value.substring(0, 100);
                this.commentConfigListDataRows[commentIndex].adviceCount = e.detail.value.length;
                this.$set(this.commentConfigListDataRows, commentIndex, this.commentConfigListDataRows[commentIndex]);
            } else {
                let newObj = this.commentConfigListData;
                newObj.rows[commentIndex].advice = e.detail.value.substring(0, 100);
                newObj.rows[commentIndex].adviceCount = e.detail.value.length;
                this.commentConfigListDataRows = newObj.rows;
                this.$set(this.commentConfigListDataRows, commentIndex, newObj.rows[commentIndex]);
            }
        },
        // 拍摄图片获取size
        obtainTheSizeOfThePhotographedImage(path) {
            console.log('path---', path);
            // 写一个promise
            return new Promise((resolve, reject) => {
                uni.getFileInfo({
                    filePath: path,
                    success: function (res) {
                        console.log(res, 'res====uni.getImageInfo');
                        resolve(res.size);
                    },
                    fail: function (err) {
                        console.log(err, 'err====uni.getImageInfo');
                        reject(err);
                    },
                });
            });
        },

        // 返回首页
        backMainPage() {
            // this.$sKit.layer.useRouter('/pages/thirdhome/main', '','switchTab');
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.backHomeFun();
            // #endif
        },
        /*
            提交评价
            订单子类型：orderSubType
            11—预约加油；
            13—线上后支付加油(e站加油订单)；
            23—O2O订单；
            26—异业订单(能源商城订单)；
            订单编号:orderNo
            油站机构编号:stationCode
            评价选项列表:commentItems
        */
        async evaluateApplication() {
            let dataList = this.commentConfigListDataRows || [];
            console.log('dataList--', dataList);
            // 订单评价选择题chooseType 1 为选择题
            let orderArr = [];
            let allCheck = false; // 选择题是否填完
            let isTagCheck = false;
            dataList.forEach(orderItem => {
                orderArr = orderArr.concat(orderItem.choosedQuestion); // 扫码评价选择题
                if (orderItem.chooseType == 1) {
                    let selectArr = []; // 选中的选项
                    orderItem.chooseList.forEach(e => {
                        if (e.checked) selectArr.push(e);
                    });
                    console.log(selectArr, 'selectArr');
                    let isCheck = selectArr.some(i => i.commentFlag && !i.comment); // 带填空的未填写完成的
                    if (selectArr.length == 0 || isCheck) {
                        allCheck = true;
                        return;
                    }
                }
                // 评价标签为必选项
                if (orderItem.chooseTagType == 1 && orderItem.items != null) {
                    if (orderItem.choosedLabels.length == 0) {
                        isTagCheck = true;
                    }
                }
            });
            if (allCheck) {
                this.$store.dispatch('zjShowModal', {
                    content: '请填写完成',
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }

            if (isTagCheck) {
                let tagItem = dataList.find(i => i.choosedLabels.length == 0);
                console.log(tagItem, 'tagItem');
                this.evaluateTitle = tagItem.title;
                this.$store.dispatch('zjShowModal', {
                    title: '必选项',
                    content: `请选择${this.evaluateTitle}的标签`,
                    confirmText: '确定',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }
            this.codeQusetion = orderArr;
            console.log(this.codeQusetion, 'orderArr');
            let commentItem = dataList.find(item => item.starLevel == '0');
            // let commentItemStar = dataList.find(item => Number(item.starLevel)<=3);
            // console.log(commentItemStar, commentItemStar.starLevel,'commentItemStar.starLevel---')
            // return;
            // 请评分弹窗
            if (commentItem && commentItem.showStarFlag) {
                this.commentTitle = commentItem.title;
                this.$refs.popDialogFlag.open();
                return false;
            }
            // if(this.evaluateType == 'order' && commentItemStar && Number(commentItemStar.starLevel)<=3 && commentItemStar.choosedLabels.length<=0){
            //     uni.showToast({
            //         title: '请至少选择一个标签哦',
            //         icon: 'none',
            //         duration: 2000,
            //     });
            //     return;
            // }
            else {
                if (this.evaluateType == 'order' || this.evaluateType == 'o2oOrder') {
                    let params = {
                        orderNo: this.orderInfo.orderNo,
                        orderType: this.orderInfo.orderType ? this.orderInfo.orderType : this.orderInfo.orderSubType,
                        orderSubType: this.orderInfo.orderSubType ? this.orderInfo.orderSubType : this.orderInfo.orderType,
                        stationCode: this.orderInfo.stationCode,
                    };
                    let commentItemsNew = this.commentConfigListDataRows.map(data => {
                        return {
                            id: data.id, //订单评价维度ID
                            title: data.title, //选项标题
                            starLevel: data.chooseType == '1' ? '' : data.starLevel, //星级，选择题传空
                            images: data.images, //图片url列表
                            advice: data.advice, //意见或者建议
                            dimension: data.dimension, //维度名称
                            choosedLabels: data.choosedLabels, //已选择标签列表
                            // choosedList: data.choosedQuestion, // 选择题已选择选项
                        };
                    });
                    params.commentItems = commentItemsNew;
                    params.choosedList = this.codeQusetion;
                    console.log(params, 'commentItemsNew');
                    let resData = await orderUserComment(params, {
                        handleErrorFn: () => {
                            if (this.isOil == '1') {
                                this.backMainPage();
                            } else {
                                uni.navigateBack();
                            }
                        },
                    });
                    if (resData.success) {
                        this.$store.dispatch('zjShowModal', {
                            content: '已提交，感谢您的评价！',
                            confirmText: '确定',
                            success: res => {
                                if (res.confirm) {
                                    console.log('用户点击确定');
                                    if (this.isOil == '1') {
                                        this.backMainPage();
                                    } else {
                                        uni.navigateBack();
                                    }
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                    }
                } else {
                    let params = {
                        stationCode: this.commentConfigListData.stationCode,
                        placeId: this.commentConfigListData.placeId,
                        placeName: this.placeName,
                    };
                    let commentItemsNew = this.commentConfigListDataRows.map(data => {
                        return {
                            title: data.title,
                            id: data.id,
                            dimension: data.dimension,
                            starLevel: data.chooseType == '1' ? '' : data.starLevel, //星级 starLevel传null渠道层报错暂定换为空字符串
                            images: data.images,
                            advice: data.advice,
                            choosedItems: data.choosedLabels,
                            choosedList: data.choosedQuestion, // 选择题已选择选项
                        };
                    });
                    params.commentItems = commentItemsNew;
                    // params.choosedList = this.codeQusetion;
                    console.log(commentItemsNew, 'commentItemsNew');
                    let resData = await orderQRCodeComment(params, {
                        handleErrorFn: () => {
                            // if (this.isOil == '1') {
                            this.backMainPage();
                            // } else {
                            //     uni.navigateBack();
                            // }
                        },
                    });
                    if (resData.success) {
                        this.$store.dispatch('zjShowModal', {
                            content: '已提交，感谢您的评价！',
                            confirmText: '确定',
                            success: res => {
                                if (res.confirm) {
                                    // if (this.isOil == '1') {
                                    this.backMainPage();
                                    // } else {
                                    //     uni.navigateBack();
                                    // }
                                    console.log('用户点击确定');
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                    }
                }
            }
        },
        //关闭弹窗
        clickBtn() {
            this.$refs.popDialogFlag.close();
        },
        // 关闭预览图片
        // closeImg(){
        //     // this.$refs.imgDialogFlag.close()
        //     this.imgPop = false
        // },
        // imgLoad(){
        //     let systemInfo = uni.getSystemInfoSync();
        //     this.imgWidth = systemInfo.windowWidth
        // },
    },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3, //选中的油站
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        ...mapGetters(['latV3', 'lonV3']),
    },
    components: {},
};
</script>
<style scoped lang="scss">
.pageMpaas {
    height: calc(100vh - 1px);
}
.stationinfo {
    padding-bottom: 16px;
    border-bottom: 1px solid #d3d3d3;
    box-sizing: border-box;
    margin-bottom: 24px;
}
.marlr15 {
    margin-left: 15px;
    margin-right: 15px;
}
.padt10 {
    padding-top: 10px;
}

.item-cell {
    height: 30px;
    background: #f7f7fb;
    border-radius: 6px;
    line-height: 30px;
    // max-width: 100px;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #333333;
    // padding: 0 9px;
    box-sizing: border-box;
    flex: 1;
    margin: 0 10px 10px 0; // 间隙为5px
    // background-color: #fff;
    width: calc((100% - 20px) / 2); // 这里的10px = (分布个数2-1)*间隙5px, 可以根据实际的分布个数和间隙区调整
    min-width: calc((100% - 20px) / 2); // 加入这两个后每个item的宽度就生效了
    &:nth-child(2n) {
        // 去除第2n个的margin-right
        margin-right: 0;
    }
}
.item-sel {
    border-radius: 6px;
    font-size: 13px;
    font-weight: 400;
    color: #e64f22;
    position: relative;
    &.item-cell:after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid #e64f22;
        border-radius: 6px;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
}
.mart12 {
    margin-top: 12px;
}
.mart8 {
    margin-top: 8px;
}
.marr8 {
    margin-right: 8px;
}

.mart16 {
    margin-top: 16px;
}
.oil-address {
    margin-top: 8px;
}
.img-47 {
    width: 47px;
    height: 45px;
    flex-shrink: 0;
    margin-right: 12px;
}
.img-24 {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.wid108 {
    width: 108px;
    margin-right: 11px;
}
.img-28 {
    width: 28px;
    height: 25px;
    margin-bottom: 10px;
}
.pad-25-16 {
    padding: 10px 16px;
    box-sizing: border-box;
}

.pab18 {
    padding-bottom: 18px;
}
// .padr13 {
//     padding-right: 13px;
// }
.input-div {
    background: #f7f7fb;
    border-radius: 2px;
    padding: 5px;
    width: 100%;
    margin-top: 8px;
    margin-bottom: 16px;
    // overflow: hidden;
    box-sizing: border-box;

    textarea {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        background: #f7f7fb;
        border: none;
        resize: none;
        height: 72px;
        font-size: 13px;
    }
}
.photo-div {
    width: 74px;
    height: 74px;
    background: #f7f7fb;
    border-radius: 2px;
    color: #333;
    font-size: 11px;
    font-weight: 400;
}
.con-list {
    width: 100%;
    .photo-area {
        width: 74px;
        position: relative;
        margin-right: 8px;
        .area-img {
            width: 74px;
            height: 74px;
            margin: auto;
        }
        .img-14 {
            width: 14px;
            height: 14px;
            display: block;
            position: absolute;
            top: -2px;
            right: -6px;
        }
    }
}
.con-list::after {
    content: '';
    width: 30%;
}
.btn-box {
    padding: 0 15px;
    .refund-btn {
        font-size: 18px;
        font-weight: 500;
        color: #ffffff;
        width: 100%;
        height: 44px;
        line-height: 44px;
        text-align: center;
        border-radius: 10px;
        // padding-bottom: calc(20px + env(safe-area-inset-bottom));
        box-sizing: border-box;
    }
}
._modal {
    flex: none;
    width: 280px;
    min-height: 104px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 19px 0 0;

        .content {
            padding: 0 20px;
            margin-bottom: 17px;
            width: 100%;
            min-height: 34px;
            text-align: center;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 45px;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 1px solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}
.mask-pop {
    background: rgba(0, 0, 0, 1);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    //     z-index: 13; /* 设置遮罩层位于其他元素之上 */
    //     opacity: 1;
    .mask {
        top: 0;
        left: 0;
        opacity: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        .mask-photo {
            width: 100vw;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // margin: 0 auto;

            .preview-img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
.ques-content {
    width: 100%;
    margin-top: 10px;
}
.check-radio {
    width: 20px;
    height: 20px;
    // margin-right: 10px;
}
.check-info {
    width: 90%;
    margin-left: 20rpx;
    .describe {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        word-wrap: break-word;
        // line-break: anywhere;
        word-break: break-all;
    }
    img {
        width: 100%;
        // width: 320rpx;
        // height: 120rpx;
    }
    .check-input {
        display: flex;
        // align-items: center;
        margin-top: 20rpx;
        padding-bottom: 20rpx;
        input {
            background: #f7f7fb;
            border-radius: 4rpx;
            padding: 25rpx;
            height: 44rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 26rpx;
            width: 80%;
        }
        // .textarea{
        //     background: #F7F7FB;
        //     border-radius: 4rpx;
        //     padding: 25rpx;
        //     height: 30rpx;
        //     font-family: PingFangSC, PingFang SC;
        //     font-weight: 400;
        //     font-size: 26rpx;
        //     width: 80%;
        //     white-space: nowrap;
        //     overflow: auto;
        //     resize: none;
        //     word-wrap: break-word;
        // }
        .count {
            display: flex;
            align-items: center;
            padding-right: 10px;
            background: #f7f7fb;
            color: #999999;
        }
    }
}
</style>
