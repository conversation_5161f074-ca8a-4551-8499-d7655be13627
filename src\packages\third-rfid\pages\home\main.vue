<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas" style="background: #f7f7fb">
        <div class="fl-column p-hw unfomrs">
            <img class="bg-image" src="../../image/bg-car.png" alt />
            <div class="page-wrap">
                <zj-navbar
                    :border-bottom="false"
                    :background="{
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                    }"
                    back-icon-color="#ffffff"
                    title-color="#ffffff"
                    :title="saveType == 1 ? 'RFID无感支付' : '车辆签约'"
                ></zj-navbar>
                <div class="wrap-car">
                    <div class="bg-car-content">
                        <div class="bg-car-title">Hello,</div>
                        <div class="bg-car-text">添加车辆，加油更便捷</div>
                    </div>
                    <div class="unfomrs-content" v-if="saveType == 1">
                        <div class="car-number" v-if="rfidDataList.length == 0">
                            <div class="license-box">
                                <div class="license-box-title">
                                    <div class="license-box-title status">状态-{{ '正常' }}</div>
                                    <div class="license-box-title btn">启动</div>
                                </div>
                                <!-- 选择车牌 -->
                                <!-- 车辆类型 (0：普通车 1：新能源车) -->
                                <div class="plate-input" v-if="saveType == 1">
                                    <block v-for="(item, index) in licensePlateArr" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateIndex ? 'plate-input-item plate-input-item-select' : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',
                                                fontSize: index == 7 && item == '' ? '20rpx' : null,
                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <span v-if="index == 7 && item == ''" :style="{ writingMode: 'tb-rl' }">新能源</span>
                                            <span>{{ item }}</span>
                                        </div>
                                    </block>
                                </div>
                                <div class="plate-input" v-else>
                                    <block v-for="(item, index) in licensePlateArrEdit" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateEditIndex
                                                    ? 'plate-input-item plate-input-item-select'
                                                    : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',
                                                fontSize: index == 7 && item == '' ? '20rpx' : null,
                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <span v-if="index == 7 && item == ''" :style="{ writingMode: 'tb-rl' }">新能源</span>
                                            <span>{{ item }}</span>
                                        </div>
                                    </block>
                                </div>
                            </div>
                            <div class="license-btn">新增车环</div>
                        </div>
                        <div class="null-content" v-else>
                            <img src="../../image/null-bg.png" alt="">
                            <div class="null-text">未查询到您的签约信息，请新增车环</div>
                            <div class="null-btn">
                                <div>新增车环</div>
                            </div>
                        </div>
                    </div>
                    <div class="unfomrs-content" v-else>
                        <div class="car-number">
                            <div class="license-box">
                                <div class="license-title">
                                    <div class="license-title red">*</div>
                                    <div class="license-title name">车牌号码</div>
                                </div>
                                <!-- 选择车牌 -->
                                <!-- 车辆类型 (0：普通车 1：新能源车) -->
                                <div class="plate-input" v-if="saveType == 1">
                                    <block v-for="(item, index) in licensePlateArr" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateIndex ? 'plate-input-item plate-input-item-select' : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',
                                                fontSize: index == 7 && item == '' ? '20rpx' : null,
                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <span v-if="index == 7 && item == ''" :style="{ writingMode: 'tb-rl' }">新能源</span>
                                            <span>{{ item }}</span>
                                        </div>
                                    </block>
                                </div>
                                <div class="plate-input" v-else>
                                    <block v-for="(item, index) in licensePlateArrEdit" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateEditIndex
                                                    ? 'plate-input-item plate-input-item-select'
                                                    : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',
                                                fontSize: index == 7 && item == '' ? '20rpx' : null,
                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <span v-if="index == 7 && item == ''" :style="{ writingMode: 'tb-rl' }">新能源</span>
                                            <span>{{ item }}</span>
                                        </div>
                                    </block>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="infos">
                                    <div>手机号码</div>
                                    <input type="text" v-model="formData.phone" placeholder="请输入手机号码" />
                                </div>
                                <div class="lines"></div>
                                <div class="infos">
                                    <div>车环编号</div>
                                    <input type="text" v-model="formData.carNo" placeholder="请输入车环编号" />
                                </div>
                            </div>
                            <div class="pay-info">
                                <div class="title">扣款方式</div>
                                <div class="pay-list" v-for="(item,index) in payList" :key="index">
                                    <div>{{ item.name }}</div>
                                    <radio color="#e64f22" :checked="formData.payType == item.value" @change="changePayType(item.value)" />
                                </div>
                            </div>
                            <div class="auth-info">
                                <div class="infos">
                                    <div>预授权金额</div>
                                    <input type="text" v-model="formData.authPayNum" placeholder="最大5000元" />
                                </div>
                                <div class="lines"></div>
                                <div class="message-info">
                                    <div class="info1">短信验证码</div>
                                    <div class="info2">
                                        <input type="text" v-model="formData.message" placeholder="请输入验证码">
                                        <div>获取验证码</div>
                                    </div>
                                </div>
                            </div>
                            <div class="protocol">
                                <div class="protocol-info">
                                    <radio color="#e64f22" :checked="checkedProtocol" value="checked" style="transform: scale(0.65)" />
                                    <div class="content">
                                        <span>我已阅读并同意能源e站</span>
                                        <span>《RFID加油客户服务协议》</span>
                                    </div>
                                    <!-- <img src="" alt=""> -->
                                </div>
                            </div>
                            <div class="sign-btn">确认签约</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 键盘 -->
            <customKeyboard
                ref="uKeyboard"
                zIndex="10"
                @backspace="carKeyDelect"
                @change="carKeyChange"
                mode="car"
                v-model="isKeyboard"
                :mask="false"
            ></customKeyboard>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import customKeyboard from '../../components/custom-keyboard/custom-keyboard.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { customKeyboard },
    name: 'myVehicleDetail',
    data() {
        return {
            // 新增后车牌号数组
            licensePlateArr: ['', '', '', '', '', '', '', ''],
            // 编辑后的车牌号数组
            licensePlateArrEdit: ['', '', '', '', '', '', '', ''],
            // 正在输入新增后的车牌号位置 -1时关闭键盘
            selectPlateIndex: -1,
            // 正在输入编辑后的车牌号位置 -1时关闭键盘
            selectPlateEditIndex: -1,
            // 是否显示键盘
            isKeyboard: false,
            //是否是第一次调起键盘 用于判断退格
            isFirst: false,
            //车辆类型 (0：普通车 1：新能源车)
            vehicleType: 0,
            //车牌颜色数据
            licenseArr: [],
            // 车辆类型数据
            vehicleTypeArr: [],
            //选择新增的车牌颜色数据
            licensePlateVal: '-1',
            //选择编辑后的车牌颜色数据
            licensePlateValEdit: '-1',
            // licensePlateValEditIndex:'-1',
            // 用来区分添加还是编辑
            saveType: 2,
            //接口请求完页面数据加载
            isInit: false,

            // 签约信息
            rfidDataList: [],
            // 车辆签约信息
            formData: {},
            // 扣款方式列表
            payList: [
                {
                    name: '昆仑e享卡',
                    value: 1,
                },
                {
                    name: '银联',
                    value: 2,
                },
            ],
            // 协议勾选
            checkedProtocol: false,
        };
    },
    computed: {},
    watch: {
        // 监听键盘
        isKeyboard: {
            handler(newName, oldName) {
                if (newName === false) {
                    if (this.saveType == 1) {
                        this.selectPlateIndex = -1;
                    } else {
                        this.selectPlateEditIndex = -1;
                    }
                }
            },
            immediate: true,
        },
    },
    async onLoad(options) {
    },
    mounted() {},
    methods: {
        
        // 键盘点击事件监听
        carKeyChange(text) {
            this.isFirst = false;
            let indexCar = this.saveType == 1 ? this.selectPlateIndex : this.selectPlateEditIndex;
            let icenseArr = this.saveType == 1 ? this.licensePlateArr : this.licensePlateArrEdit;
            console.log('indexCar--', indexCar);
            console.log('icenseArr--', icenseArr);
            this.$set(icenseArr, indexCar, text);
            this.$nextTick(() => {
                if (this.saveType == 1) {
                    this.selectPlateIndex = indexCar + 1;
                    if (this.selectPlateIndex == 1) {
                        this.$refs.uKeyboard.changeCarInputMode();
                    }
                    if (this.selectPlateIndex == this.licensePlateArr.length) {
                        this.selectPlateIndex = -1;
                        this.isKeyboard = false;
                    }
                } else {
                    this.selectPlateEditIndex = indexCar + 1;
                    if (this.selectPlateEditIndex == 1) {
                        this.$refs.uKeyboard.changeCarInputMode();
                    }
                    if (this.selectPlateEditIndex == this.licensePlateArrEdit.length) {
                        this.selectPlateEditIndex = -1;
                        this.isKeyboard = false;
                    }
                }
            });
        },
        // 键盘退格点击事件
        carKeyDelect() {
            let indexCarDelect = this.saveType == 1 ? this.selectPlateIndex : this.selectPlateEditIndex;
            let licensePlateArrDelect = this.saveType == 1 ? this.licensePlateArr : this.licensePlateArrEdit;
            if (indexCarDelect == 1) {
                this.$nextTick(() => {
                    this.$refs.uKeyboard.changeCarInputMode();
                });
            }
            if (this.isFirst) {
                this.$set(licensePlateArrDelect, indexCarDelect, '');
                indexCarDelect = indexCarDelect - 1;
            } else {
                indexCarDelect = indexCarDelect - 1;
                this.$set(licensePlateArrDelect, indexCarDelect, '');
            }
            if (indexCarDelect == -1) {
                indexCarDelect = 0;
            }
            if (this.saveType == 1) {
                this.selectPlateIndex = indexCarDelect;
            } else {
                this.selectPlateEditIndex = indexCarDelect;
            }
        },
        // 车牌input点击事件
        clickPlateInput(index) {
            this.$nextTick(() => {
                if (this.saveType == 1) {
                    this.selectPlateIndex = index;
                    this.$refs.uKeyboard.changeCarInputMode(this.selectPlateIndex == 0 ? 'cn' : 'zn');
                } else {
                    this.selectPlateEditIndex = index;
                    this.$refs.uKeyboard.changeCarInputMode(this.selectPlateEditIndex == 0 ? 'cn' : 'zn');
                }
            });
            this.isKeyboard = true;
            this.isFirst = true;
        },
        // 返回上一页面
        backpage() {
            if (this.pageType == 'o2o') {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
            } else {
                let url = '';
                let params = {};
                let type = 'navigateBack';
                let prevParams = {
                    // 得到上一页面的实例需要传2 若要返回上上页面的实例就传3，以此类推
                    delta: 1,
                    // 返回指定页面的data中的标识，用于在调用接口成功后返回该页面时候，调用指定的函数或更改要返回页面中定义的属性值
                    refreshListFlag: true,
                };
                this.$sKit.layer.useRouter(url, params, type, prevParams);
            }
        },
    },
};
</script>
<style scoped lang="scss">
.flex {
    display: flex;
}

.unfomrs {
    position: relative;
    background-color: #f5f5f5;

    .bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 484rpx;
        display: block;
    }

    .page-wrap {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .wrap-car {
        position: absolute;
        left: 0;
        top: 95px;
        width: 100%;
    }

    .bg-car-content {
        padding-left: 23px;
        width: 100%;
        box-sizing: border-box;

        .bg-car-title {
            font-size: 52rpx;
            color: #ffffff;
            line-height: 62rpx;
        }

        .bg-car-text {
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 42rpx;
        }
    }

    .unfomrs-content {
        margin-top: 35px;
        width: 100%;
        box-sizing: border-box;
        .null-content{
            margin: 0rpx 30rpx 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient( 180deg, #FFFFFF 0%, #F7F7FB 100%);
            border-radius: 16rpx;
            padding: 81rpx 95rpx 0 95rpx;
            .null-text {
                margin-top: 20px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #999999;
            }
            .null-btn {
                margin-top: 20px;
                margin-bottom: 60px;
                div {
                    width: 165px;
                    height: 44px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: bold;
                    text-align: center;
                    line-height: 44px;
                    color: #ffffff;
                    background: linear-gradient( 288deg, #FF3E00 0%, #FF7B33 100%);
                    box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0,0,0,0.07);
                }
            }
        }
        .car-number {
            margin: 0rpx 30rpx 0;
            .license-btn{
                margin-top: 20px;
                margin-bottom: 60px;
                border-radius: 8px;
                font-family: PingFangSC, PingFang SC;
                font-weight: bold;
                font-size: 36rpx;
                color: #FFFFFF;
                background: linear-gradient( 288deg, #FF3E00 0%, #FF7B33 100%);
                box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0,0,0,0.07);
                text-align: center;
                padding: 19rpx 0;
            }
            .car-info{
                margin-top: 20rpx;
                border-radius: 15rpx;
                padding: 26rpx 30rpx;
                background: #ffffff;
                .lines{
                    height: 1rpx;
                    background: #EEEEEE;
                    margin: 29rpx 0;
                }
                .infos{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    div{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }
                    input{
                        border: none;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                        text-align: right;
                    }
                }
            }
            .pay-info{
                margin-top: 20rpx;
                border-radius: 15rpx;
                padding: 26rpx 30rpx;
                background: #ffffff;
                .title{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: bold;
                    font-size: 32rpx;
                    color: #000000;
                }
                .pay-list{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 20rpx;
                    div{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }
                }
            }
            .auth-info{
                margin-top: 20rpx;
                border-radius: 15rpx;
                padding: 26rpx 30rpx;
                background: #ffffff;
                .lines{
                    height: 1rpx;
                    background: #EEEEEE;
                    margin: 29rpx 0;
                }
                .infos{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    div{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }
                    input{
                        border: none;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                        text-align: right;
                    }
                }
                .message-info{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .info1{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }
                    .info2{
                        display: flex;
                        align-items: center;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #E65A31;
                        div{
                            margin-left: 30rpx;
                        }
                    }
                    input{
                        border: none;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                        text-align: right;
                    }
                }
            }
            .protocol{
                margin-top: 20rpx;
                display: flex;
                align-items: center;
                .protocol-info{
                    display: flex;
                    align-items: center;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #999999;
                    .content{
                        // display: flex;
                        span{
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #999999;
                            &:nth-child(2) {
                                color: #E65A31;
                                // margin-left: 5rpx;
                            }
                        }
                    }
                    // img{
                    //     width: 20rpx;
                    //     height: 20rpx;
                    // }
                }
            }
            .sign-btn{
                margin-top: 20rpx;
                margin-bottom: 60px;
                border-radius: 8px;
                font-family: PingFangSC, PingFang SC;
                font-weight: bold;
                font-size: 36rpx;
                color: #FFFFFF;
                background: linear-gradient( 288deg, #FF3E00 0%, #FF7B33 100%);
                box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0,0,0,0.07);
                text-align: center;
                padding: 19rpx 0;
            }
            .license-box {
                position: relative;
                border-radius: 15rpx;
                padding: 26rpx 30rpx;
                background: #ffffff;
                z-index: 999;
                .license-box-title{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    &.status{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: bold;
                        font-size: 28rpx;
                        color: #333333;
                    }
                    &.btn{
                        padding: 14rpx 41rpx;
                        background: linear-gradient( 135deg, #FF4000 0%, #FF6A00 100%);
                        border-radius: 100rpx;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: bold;
                        font-size: 24rpx;
                        color: #FFFFFF;
                    }
                }

                .license-title {
                    display: flex;

                    &.red {
                        color: #ea6a44;
                    }

                    &.name {
                        font-size: 28rpx;
                        color: #999999;
                        line-height: 40rpx;
                    }
                }

                .plate-input {
                    margin-top: 15px;
                    display: flex;

                    .plate-input-item {
                        z-index: 20;
                        flex: 1;
                        margin-left: 5px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #f7f7fb;
                        color: #333333;
                        border-radius: 5px;
                        text-align: center;
                        font-size: 15px;
                        font-weight: 700;
                        box-sizing: border-box;
                    }

                    .plate-input-item-select {
                        background-color: $btn-mantle-color;
                        color: $btn-color;
                        box-sizing: border-box;
                        position: relative;
                    }

                    .plate-input-spot {
                        align-self: center;
                        background-color: #dcdcdc;
                        height: 10px;
                        width: 10px;
                        border-radius: 5px;
                        margin-left: 5px;
                    }
                }
            }

            .substance-box {
                border-radius: 15rpx;
                background-color: #ffffff;
                padding: 0 30rpx;
                margin-top: 20rpx;

                .basic-box {
                    //  padding-bottom:29rpx;
                    height: 95rpx;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex: 1;
                    border-bottom: 2rpx solid #eeeeee;
                    overflow: hidden;

                    &:last-child {
                        border-bottom: none;
                    }

                    .substance-tip {
                        display: flex;

                        &.red {
                            color: #e64a1d;
                        }

                        &.number {
                            font-size: 28rpx;
                            font-weight: 400;
                            line-height: 40rpx;
                        }
                    }

                    .substanceWidth {
                        width: 120px;
                    }

                    .substance-tip-right {
                        display: flex;
                        align-items: center;

                        .placeholder {
                            font-size: 26rpx;
                            color: #999;
                            line-height: 40rpx;
                        }

                        .substance-right {
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #999999;
                            line-height: 40rpx;

                            &.input {
                                text-align: right;
                                margin-right: 5px;
                            }
                        }

                        u-icon {
                            color: #999;
                        }

                        .car-arrowhead {
                            width: 32rpx;
                            height: 32rpx;
                        }
                    }
                }

                &.spe {
                    margin-top: 20rpx;
                }
            }

            .basic-box-label {
                padding-top: 20px;
                color: #e64a1d;
                font-size: 12px;
                font-weight: 400;
                line-height: 40rpx;
            }

            .addCar {
                height: 44px;
                margin-bottom: 12px;
                line-height: 44px;
                width: 100%;
            }

            .btnWrap {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
                margin-bottom: 15px;

                div {
                    width: 165px;
                    height: 44px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 44px;

                    &:nth-of-type(1) {
                        border: 0.5px solid #ff6b2c;
                        color: #ff6b2c;
                        background: #ffffff;
                    }

                    &:nth-of-type(2) {
                        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                        box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.07);
                        color: #ffffff;
                    }
                }
            }
        }
    }
}

.img_style {
    width: 42px;
    height: 23px;
}
.dete-content-wrap {
    // margin-bottom: 20px;
    padding: 12px;
    .dete-content {
        margin-top: 41rpx;
        flex: 0 0 25%; /* 将每个元素的flex属性设置为1，使它们均匀分布 */
        .dete-item {
            width: 155rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
        }
        .isActive {
            border: 1px solid #e64f22;
            background: #ffffff;
            font-weight: 400;
            color: #e64f22;
        }
    }
    .angleStrInput {
        width: 155rpx;
        height: 60rpx;
        background: #f7f7fb;
        border-radius: 4rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;
        padding: 0;
    }
    // .gap23 {
    //     // margin-right: 23rpx;
    //     &:nth-last-of-type(1) {
    //         margin-right: 0;
    //     }
    // }
}
.pop_div {
    background: #fff;
    padding: 10px 15px;
}
</style>
