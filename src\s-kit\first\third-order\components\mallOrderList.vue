<template>
    <div>
        <div class="list-card" v-for="(item, index) in mallOrderList" :key="index">
            <!-- <div> -->
            <!-- <div v-if="val.orderGoodsInfoList.length > 1"> -->
            <div class="list_card_div" v-for="(item2, index2) in item.mySOrderInfoList" :key="index2">
                <div class="oreder-content">
                    <div class="detail-top">
                        <div class="name fl-row">
                            <img mode="scaleToFill" src="../image/shopImg.png" alt class="name-img" />
                            <div class="title">{{ item2.shopInfo.shopName }}</div>
                            <!-- #ifdef MP-MPAAS -->
                            <div class="name-arrow-right"></div>
                            <!-- #endif -->
                        </div>
                        <div :class="{ invoice: true }">{{ orderShowStatus(item.orderStatus, item2.orderStatus) }}</div>
                    </div>
                    <!-- 将商品分为油品和非油品 -->
                    <div
                        class="detail-contetn"
                        v-for="(goodItem, idx) in item2.orderGoodsInfoList"
                        :key="idx"
                        @click="toDetail(item2, item, item.orderStatus)"
                    >
                        <div class="detail-left">
                            <img class="detail-left-img" :src="splicingImg(goodItem.goodsMainImg)" alt />
                            <div class="order-name">{{ goodItem.goodsName }}</div>
                        </div>
                        <div class="detail-price">
                            <div class="unitPrice">&yen;{{ goodItem.listPrice }}</div>
                            <!-- <div class="litre" v-if="productType == 1">
              {{ goodItem.productQty }}{{ goodItem.productUnit }}
              </div>-->
                            <div class="litre">x {{ goodItem.goodsCount }}</div>
                            <div class="refundStatus">{{
                                afterSaleShowStatus(goodItem.afterSaleType, goodItem.refundStatus, goodItem.returnStatus)
                            }}</div>
                        </div>
                    </div>
                    <div class="paymentAmount">
                        <div class="totalPayment-hint"> {{ item2.payPoint ? '实付积分：' : '实付总额：' }}</div>
                        <div class="totalPayment-sum font-style" v-if="!item2.payPoint">&yen;</div>
                        <div class="chars font-style">{{ item2.payPoint || item2.payAmount || 0 }}</div>
                    </div>
                    <!-- <div class="detail-bottom">仅退款 退款中 极速退款成功</div> -->
                </div>
            </div>
            <!-- </div> -->
            <!-- </div> -->
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { queryOrderList } from '@/s-kit/js/v3-http/https3/order/index';
import platform from '@/s-kit/js/platform';
import appMallOrderList from './diff-environment/app-mall-order-list.js';
import wxMallOrderList from './diff-environment/wx-mall-order-list.js';
import zfbMallOrderList from './diff-environment/zfb-mall-order-list.js';
import { baseMallUrl } from '../../../../../project.config';
export default {
    name: 'mallOrderList',
    props: {
        selectStatus: {
            default: false,
            type: Boolean,
        },
    },
    mixins: [
        // #ifdef MP-MPAAS
        appMallOrderList,
        // #endif
        // #ifdef MP-WEIXIN
        wxMallOrderList,
        // #endif
        // #ifdef MP-ALIPAY
        zfbMallOrderList,
        // #endif
    ],

    data() {
        return {
            list: [],
            mallOrderList: [],
            pageNum: 1,
            pageSize: 10,
        };
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
    },
    mounted() {
        this.$store.dispatch('memberBaseInfoAction');
    },
    methods: {
        /*订单状态映射关系 */
        /**
         * afterSaleType: {
          REFUND: 'REFUND', // 仅退款
          INITATIVE_REFUND: 'INITATIVEREFUND', // 主动退款
          GROUP_PURCHASE_REFUND: 'GROUPPURCHASEREFUND', // 团购退款
          RETURN_REFUND: 'RETURNREFUND', // 退货退款
          EXCHANGE: 'EXCHANGE' // 换货
        }
        returnStatus: { // 退货/换货状态 包含 EXCHANGE 的是换货的状态，不包含的是退货状态
          AUDIT: 'AUDIT', // 待审核
          SENTBANK: 'SENTBANK', // 待发回
          PENDING: 'PENDING', // 检测不通过待终审
          QUALIFIED: 'QUALIFIED', // 检测通过待终审
          COMPLETED: 'COMPLETED', // 已完成
          CLOSED: 'CLOSED', // 关闭
          SIGNIN: 'SIGNIN', // 待签收 退换货共用
          BETESTED: 'BETESTED', // 待检验 退换货共用
          REVOKE: 'REVOKE', // 撤销 退换货共用
          EXCHANGEAUDIT: 'EXCHANGEAUDIT', // 换货待审核
          PENDINGEXCHANGE: 'PENDINGEXCHANGE', // 换货检测不通过待终审
          QUALIFIEDCOMPLETED: 'QUALIFIEDCOMPLETED', // 换货检测合格待发新货
          PENDINGEXCHANGENEW: 'PENDINGEXCHANGENEW', // 换货终审通过待发新货
          EXCHANGESENTBANK: 'EXCHANGESENTBANK', // 换货待发回
          EXCHANGECLOSED: 'EXCHANGECLOSED', // 换货关闭
          QUALIFIEDCOMPLETEDSEND: 'QUALIFIEDCOMPLETEDSEND' // 换货完成已发货
        },

        refundStatus: { // 退款状态
          PENDING: 'PENDING', // 待确认
          REFUNDPENDING: 'REFUNDPENDING', // 待审核
          ABNORMAL: 'ABNORMAL', // 受理异常
          REAFFIRM: 'REAFFIRM', // 已处理
          REFUNDSUCCESS: 'REFUNDSUCCESS' // 受理成功
        },
         */
        /*mOrderStatus：最外层订单状态
        sOrderStatus：子订单状态
        */
        /**
         * @description  :
         * @param         {*} mOrderStatus:item.orderStatus：最外层订单状态
         * @param         {*} sOrderStatus:item2.orderStatus：子订单状态
         * @param         {*} sOrderType:sOrderType 子订单类型
         * @return        {*}
         */
        orderShowStatus: (mOrderStatus, sOrderStatus, sOrderType) => {
            if (mOrderStatus === 'SUBMITED') {
                return '待付款';
            }
            if (mOrderStatus === 'PAID') {
                if (sOrderStatus === 'PENDINGSHIP') {
                    return '待发货';
                }
                // if (sOrderStatus === 'PENDINGSHIP' && sOrderType === '03') {
                //   return '团购中'
                // }
                if (sOrderStatus === 'PENDINGRECEIVE') {
                    return '卖家已发货';
                }
                if (sOrderStatus === 'PENDINGEVALUATION' || sOrderStatus === 'COMPLETED') {
                    return '交易成功';
                }
                if (sOrderStatus === 'CLOSED') {
                    return '交易关闭';
                }
            }
            if (mOrderStatus === 'COMPLETED') {
                if (sOrderStatus === 'COMPLETED') {
                    return '交易成功';
                }
                if (sOrderStatus === 'CLOSED') {
                    return '交易关闭';
                }
            }
            if (mOrderStatus === 'CANCELED') {
                return '交易关闭';
            }
            return '';
        },
        //this.afterSaleShowStatus(goods.afterSaleType, goods.refundStatus, goods.returnStatus)
        /**
         * @description  : 订单商城前端处理的商品状态的映射参数
         * @param         {*} afterSaleType:goods.afterSaleType  退款状态
         * @param         {*} refundStatus:goods.refundStatus  退款状态
         * @param         {*} returnStatus: goods.returnStatus  退款状态
         * @return        {*}
         */
        afterSaleShowStatus: (afterSaleType, refundStatus, returnStatus) => {
            if (afterSaleType === 'REFUND' || afterSaleType === 'INITATIVEREFUND' || afterSaleType === 'GROUPPURCHASEREFUND') {
                // 退款
                if (refundStatus === 'PENDING' || refundStatus === 'REFUNDPENDING' || refundStatus === 'ABNORMAL') {
                    return '退款中';
                }
                if (refundStatus === 'REFUNDSUCCESS' || refundStatus === 'REAFFIRM') {
                    return '退款成功';
                }
            }
            if (afterSaleType === 'RETURNREFUND') {
                // 退货退款
                switch (returnStatus) {
                    case 'AUDIT':
                    case 'SIGNIN':
                    case 'BETESTED':
                    case 'QUALIFIED':
                    case 'PENDING':
                        return '退款中';
                    case 'COMPLETED':
                        if (refundStatus === 'PENDING' || refundStatus === 'REFUNDPENDING' || refundStatus === 'ABNORMAL') {
                            return '退款中';
                        }
                        if (refundStatus === 'REAFFIRM' || refundStatus === 'REFUNDSUCCESS') {
                            return '退款成功';
                        }
                        break;
                    case 'REVOKE':
                    case 'CLOSED':
                        return '退款关闭';
                    case 'SENTBANK':
                        return '待寄回商品';
                }
            }
            if (afterSaleType === 'EXCHANGE') {
                // 换货
                switch (returnStatus) {
                    case 'EXCHANGEAUDIT':
                    case 'SIGNIN':
                    case 'BETESTED':
                    case 'PENDINGEXCHANGE':
                    case 'QUALIFIEDCOMPLETED':
                    case 'PENDINGEXCHANGENEW':
                        return '换货中';
                    case 'REVOKE':
                    case 'EXCHANGECLOSED':
                        return '换货关闭';
                    case 'EXCHANGESENTBANK':
                        return '待寄回商品';
                    case 'QUALIFIEDCOMPLETEDSEND':
                        return '换货完成';
                }
            }
            return '';
        },
        /**
         * @description  : 图片地址拼接
         * @param         {*} url:图片地址
         * @return        {*}
         */
        splicingImg(url) {
            // https://temall.95504.net/ 测试
            // https://emall.95504.net/ 生产
            // return 'https://temall.95504.net/' + url
            if (url.startsWith('http')) {
                return url;
            } else {
                return baseMallUrl + url;
            }
        },
        /**
         * @description  : 请求商城订单列表
         * @param         {*} isInit:是否充值商城订单列表
         * @param         {*} listParams:入参信息
         * @return        {*}
         */
        async getMallOrderList({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            if (isInit) {
                this.mallOrderList = [];
                this.pageNum = 1;
            }
            this.$emit('loadStatusChange', 'loading');
            let params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };
            if (listParams.secondNavActive != '') {
                params.orderStatus = listParams.secondNavActive;
            }
            // #ifdef MP-WEIXIN
            params.memberId = this.memberBaseInfo.memberNo;
            // #endif
            let res = await queryOrderList(params, { isload: false });
            this.$emit('stopRefresh');
            if (res.status == 0) {
                let arr = [];
                // #ifndef MP-MPAAS
                if (platform.isAlipay) {
                    arr = res.Data.dataList || [];
                }
                // #endif
                // #ifdef MP-MPAAS
                arr = res.data.dataList || [];
                // #endif
                // #ifdef MP-WEIXIN
                arr = res.data || [];
                // #endif
                if (this.pageNum == 1) {
                    this.mallOrderList = arr;
                } else {
                    this.mallOrderList = this.mallOrderList.concat(arr);
                }
                if (arr.length < this.pageSize) {
                    this.$emit('loadStatusChange', 'nomore');
                } else {
                    this.$emit('loadStatusChange', 'contentdown');
                }
                this.pageNum++;
            }
            if (this.mallOrderList.length == 0) {
                this.$emit('showEmptyChange', true);
            } else {
                this.$emit('showEmptyChange', false);
            }
        },
    },
};
</script>

<style scoped lang="scss">
.list-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .choice {
        width: 73rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .oreder-content {
        background: #ffffff;
        padding: 16rpx 30rpx 27rpx;
        box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(0, 0, 0, 0.07);
        border-radius: 16rpx;
        width: 100%;
        height: 100%;

        .detail-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40rpx;

            .name {
                white-space: nowrap; //禁止换行
                text-overflow: ellipsis; //...
                display: flex;
                align-items: center;

                .name-img {
                    width: 16px;
                    height: 16px;
                }

                .title {
                    max-width: 182px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000000;
                    margin-left: 10rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                }
            }

            .invoice {
                font-size: 24rpx;
                font-weight: 400;
                color: #f93f00;
            }

            .gray {
                color: #999999 !important;
            }
        }

        .detail-contetn {
            margin-top: 24rpx;

            display: flex;
            justify-content: space-between;

            .detail-left {
                display: flex;

                .detail-left-img {
                    width: 100rpx;
                    height: 100rpx;
                }

                .order-name {
                    max-width: 380rpx;
                    padding-top: 2px;
                    margin-left: 20rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .detail-price {
                text-align: right;

                .unitPrice {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }

                .litre {
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 40rpx;
                }
            }
        }

        .paymentAmount {
            height: 25px;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            margin-top: 2px;

            div {
                line-height: 25px;
            }

            div:nth-child(1) {
                font-size: 14px;
                color: #666666;
            }

            div:nth-child(2) {
                font-size: 14px;
                color: #e64f22;
            }

            div:nth-child(3) {
                font-size: 14px;
                color: #e64f22;
                font-weight: bold;
            }
        }

        .detail-bottom {
            margin-top: 10rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .countdown {
                display: flex;
                align-items: center;

                div {
                    &:nth-of-type(1) {
                        font-size: 36rpx;
                        font-weight: 600;
                        color: #e64f22;
                        line-height: 80rpx;
                    }

                    &:nth-of-type(2) {
                        margin-left: 10rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #888888;
                        line-height: 80rpx;
                    }
                }
            }

            .bottom-button-pay {
                text-align: center;
                width: 209rpx;
                height: 80rpx;
                background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
                border-radius: 16rpx;
                font-size: 30rpx;
                font-weight: 400;
                color: #ffffff;
                line-height: 80rpx;
            }

            .bottom-button-open {
                text-align: center;
                width: 209rpx;
                height: 80rpx;
                border-radius: 16rpx;
                border: 1rpx solid #e64f22;
                font-size: 30rpx;
                font-weight: 400;
                color: #e64f22;
                line-height: 80rpx;
            }

            .bottom-button-see {
                text-align: center;
                font-size: 30rpx;
                font-weight: 400;
                color: #333333;
                line-height: 80rpx;
            }
        }
    }
}

.refundStatus {
    color: #f0bd00;
    margin-top: 5px;
    font-size: 24rpx;
}

.list_card_div {
    margin-bottom: 10px;
}
.list_card_div:nth-child(0) {
    margin-top: 0;
}
</style>
