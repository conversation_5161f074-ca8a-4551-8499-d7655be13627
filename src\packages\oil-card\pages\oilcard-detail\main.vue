<template>
    <div class="oil-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            :background="pageConfig.bgColor"
            :custom-back="clickCustomBackBtn"
            back-text="油卡记录"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="oil-top">
            <div class="top-section">
                <div class="top-flex">
                    <div class="top-view">
                        <div class="top-one padding20">
                            <img src="@/static/cnpc-logo.png" alt mode="widthFix" />
                            <span class="title-color">中国石油加油卡</span>
                        </div>
                        <span class="title-color">{{ detailObj.cardNo }}</span>
                    </div>
                    <div class="top-view top-line margin-50">
                        <p class="label-txt title-color padding20">
                            备用金
                            <span class="title-bold color-font margin-10">
                                <i class="icon-txt">¥</i>
                                {{ detailObj.balance }}
                            </span>
                        </p>
                        <p class="label-txt title-color">
                            卡余额
                            <span class="title-bold color-font margin-10">
                                <i class="icon-txt">¥</i>
                                {{ detailObj.cardBalance }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="opacity-div"></div>
        </div>
        <scroll-view class="scroll-view_H" :scroll-left="scrollLeft" scroll-x="true">
            <div class="slide_type_list">
                <div
                    class="slide_type_list_view"
                    v-for="(item, index) in typeList"
                    :key="index"
                    :class="{ is_selected: active == index }"
                    @click="changeType(item, index)"
                >
                    <div>{{ item.name }}</div>
                </div>
            </div>
        </scroll-view>
        <scroll-view
            :style="{
                height: 'calc(100vh - 330rpx - ' + navh + 'px)',
                marginTop: '20rpx',
            }"
            scroll-y="true"
            @scrolltolower="actionBottom"
        >
            <div class="content" v-if="active == 0">
                <template v-if="oilcardListRecharge.length < 0">
                    <u-empty text="暂无充值明细信息" mode="list" :margin-top="80"></u-empty>
                </template>
                <template v-else>
                    <div class="action-list" bg-color="#f6f6f6" v-for="(item, index) in oilcardListRecharge" :key="index">
                        <!-- <u-swipe-action :show="item.show" :index="index" @click="deleteOrder" @open="open" :options="options"> -->
                        <!-- <div class='flex-list'>
							<div class="flex-left">
								<div class="cell">
									<div class="cell-title">充值时间</div>
									<div class="cell-detail">{{
									      item.orderTime != '' ? item.orderTime.replace(/\-/g, ".") : ''
									    }}</div>
								</div>
								<div class="cell">
									<div class="cell-title">订单号</div>
									<div class="cell-detail">{{ item.orderNo }}</div>
								</div>
								<div class="cell">
									<div class="cell-title">充值金额</div>
									<div class="cell-theme"> +{{ item.amount }}</div>
								</div>
            </div>-->
                        <div class="flex-list1" @click="goRechargeDetail(item)">
                            <div class="flex-left1">
                                <div class="cell1">
                                    <div class="title1">{{ item.orgName }}</div>
                                    <div
                                        v-if="item.invoiceStatus == 0 || item.invoiceStatus == 3 || item.invoiceStatus == 4"
                                        class="cell-btn1 disable-cell-btn1"
                                        >不可开票</div
                                    >
                                    <div v-if="item.invoiceStatus == 1" class="cell-btn1">未开票</div>
                                    <div v-if="item.invoiceStatus == 2" class="cell-btn1 disable-cell-btn1">已开票</div>
                                </div>
                                <div class="celltime1">
                                    <div class="cell-title1">创建时间：</div>
                                    <div class="cell-detail1">
                                        {{ item.tradeTime != '' ? item.tradeTime.replace(/\-/g, '.') : '' }}
                                    </div>
                                </div>
                                <div class="order-money1">
                                    <div>￥</div>
                                    <div>
                                        <my-price color="#FF8200" intFont="18px" floatFont="12px" :price="item.amount"></my-price>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 开发票按钮 功能有了开发即可 -->
                        <!-- <div v-if='item.state == 2' :class="'cell-btn' + (item.isInvoice == 0 ? '' : ' disable-cell-btn')">{{item.isInvoice == 0 ? '开发票' : '已开票'}}</div> -->
                    </div>
                    <!-- </u-swipe-action> -->
                    <!-- </div> -->
                    <div v-if="nextFlag" class="textcenter">暂无更多数据</div>
                </template>
            </div>

            <div class="content" v-if="active == 1">
                <template v-if="oilcardListtConsume.length < 0">
                    <u-empty text="暂无消费明细信息" mode="list" :margin-top="80"></u-empty>
                </template>
                <template v-else>
                    <div class="flex-list" v-for="(item, index) in oilcardListtConsume" :key="index">
                        <div class="flex-left">
                            <div class="cell">
                                <div class="cell-title">消费账户</div>
                                <div class="cell-detail">{{ item.accountType }}</div>
                            </div>
                            <div class="cell">
                                <div class="cell-title">消费时间</div>
                                <div class="cell-detail">{{ item.occurTime.replace(/\-/g, '.') }}</div>
                            </div>
                            <div class="cell" style="padding-bottom: 0">
                                <div class="cell-title">订单号</div>
                                <div class="cell-detail">{{ item.orderId }}</div>
                            </div>
                            <div class="cell">
                                <div class="cell-title">消费金额</div>
                                <div class="cell-theme">{{ item.amount }}</div>
                            </div>
                        </div>
                        <!-- <div class="flex-right color-font title-bold">开发票</div> -->
                    </div>
                    <div v-if="nextFlagoil" class="textcenter xfjl-more">暂无更多数据</div>
                    <div class="xfjl-view">
                        <div class="color-font xfjl-tip">*当前只显示3个月内消费记录</div>
                        <div class="color-font xfjl-tip">如需更多请登录https://www.kunlunjyk.com/查询</div>
                    </div>
                </template>
            </div>
        </scroll-view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getConsumeDetails } from '@/api/home.js';
import myPrice from '@/components/price/price.vue';
import { deleteOrder } from '@/api/home.js';
export default {
    name: 'main',
    components: {
        myPrice, //价格
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            active: 0,
            typeList: [
                {
                    name: '充值明细',
                },
                {
                    name: '消费明细',
                },
            ],
            pageSize: 8,
            pageNo: 1,
            page: 1,
            infoObj: {},
            navh: 64, // 导航高度
            detailObj: {},
            oilcardListRecharge: [],
            oilcardListtConsume: [],
            nextFlag: false, // 下一页
            nextFlagoil: false, // 下一页
            options: [
                {
                    text: '删除',
                    style: {
                        backgroundColor: '#dd524d',
                        borderTopRightRadius: '5px',
                        borderBottomRightRadius: '5px',
                    },
                },
            ],
        };
    },
    onLoad(options) {
        console.log(options);
        this.navh = this.$GlobalData.systemInfo.statusBarHeight + 40;
        let goodsInfoObj = decodeURIComponent(options.itemdata);
        let goodsInfo = JSON.parse(goodsInfoObj);
        this.infoObj = goodsInfo;
        this.active = options.index;
        if (this.active == 1) {
            this.getConsumeDetails();
        } else {
            this.getRechargeDetails();
        }
        this.getCardDetails();
    },
    onShow() {},
    methods: {
        //点击进入充值订单详情
        goRechargeDetail(item) {
            console.log(item);
            uni.navigateTo({
                url: `/packages/order/pages/recharge-order-detail/main?id=${item.orderId}&invoiceStatus=${item.invoiceStatus}`,
            });
        },
        // 触底监听
        actionBottom() {
            console.log('触底刷新!!');
            if (this.active == '0') {
                // 获取更多数据,
                console.log(this.oilcardListRecharge.length);
                if (!this.nextFlag) {
                    this.pageNo++;
                    this.getRechargeDetails();
                }
            } else {
                if (!this.nextFlagoil) {
                    this.page++;
                    this.getConsumeDetails();
                }
            }
        },
        // 删除订单点击事件
        async deleteOrder(index) {
            await this.$util.showModal('订单删除后将无法恢复，确定删除？');
            await deleteOrder({
                orderType: this.active ? 2 : 1,
                orderId: this.oilcardListRecharge[index].id,
            });
            this.oilcardListRecharge.splice(index, 1);
        },
        open(index) {
            // 先将正在被操作的swipeAction标记为打开状态，否则由于props的特性限制，
            // 原本为'false'，再次设置为'false'会无效
            this.oilcardListRecharge[index].show = true;
            this.oilcardListRecharge.map((val, idx) => {
                if (index != idx) this.oilcardListRecharge[idx].show = false;
            });
        },
        changeType(item, index) {
            this.active = index;

            if (index == 1) {
                this.nextFlagoil = false;
                this.page = 1;
                this.getConsumeDetails();
            } else {
                this.nextFlag = false;
                this.pageNo = 1;
                this.getRechargeDetails();
            }
        },
        //获取加油卡明细
        getCardDetails() {
            let url = '/app/json/cnpc_card/getCardDetails';
            let params = {
                cardNo: this.infoObj.cardNo,
            };
            this.$http.post(url, params).then(
                res => {
                    if (res.status == 0) {
                        console.log(res);
                        this.detailObj = res.data;
                    } else {
                        uni.showToast({
                            title: '获取数据失败',
                            icon: 'none',
                        });
                    }
                },
                err => {
                    uni.showToast({
                        title: '系统运行过程中遇到问题，请稍候再试',
                        icon: 'none',
                    });
                },
            );
        },
        //充值明细
        async getRechargeDetails() {
            // 之前充值明细的接口请求
            // let url = '/app/json/order/getRechargeList'
            // let params = {
            // 	cardNo: this.infoObj.cardNo,
            // 	pageSize: this.pageSize,
            // 	pageNo: this.pageNo
            // }
            // await this.$http.post(url, params).then(res => {
            // 		if (res.status == 0) {
            // 			console.log(res)
            // 			for (let i = 0; i < res.data.length; i++) {
            // 				res.data[i].show = false
            // 			}
            // 			this.oilcardListRecharge = this.pageNo == 1 ? res.data : [...this.oilcardListRecharge,
            // 				...res.data
            // 			]
            // 			if (res.data.length < this.pageSize) {
            // 				this.nextFlag = true
            // 			}
            // 		} else {
            // 			uni.showToast({
            // 				title: "获取数据失败",
            // 				icon: "none",
            // 			});
            // 		}
            // 	},
            // 	err => {
            // 		uni.showToast({
            // 			title: "系统运行过程中遇到问题，请稍候再试",
            // 			icon: "none",
            // 		});
            // 	}
            // )
            let url = '/app/json/order2/getRechargeList';
            let params = {
                cardNo: this.infoObj.cardNo,
                pageSize: this.pageSize,
                pageNo: this.pageNo,
            };
            await this.$http.post(url, params).then(
                res => {
                    if (res.status == 0) {
                        for (let i = 0; i < res.data.length; i++) {
                            res.data[i].show = false;
                        }
                        this.oilcardListRecharge = this.pageNo == 1 ? res.data : [...this.oilcardListRecharge, ...res.data];
                        if (res.data.length < this.pageSize) {
                            this.nextFlag = true;
                        }
                    } else {
                        uni.showToast({
                            title: '获取数据失败',
                            icon: 'none',
                        });
                    }
                },
                err => {
                    uni.showToast({
                        title: '系统运行过程中遇到问题，请稍候再试',
                        icon: 'none',
                    });
                },
            );
        },
        // 消费明细
        async getConsumeDetails() {
            let params = {
                cardNo: this.infoObj.cardNo,
                pageSize: this.pageSize,
                pageNo: this.page,
            };
            let res = await getConsumeDetails(params);
            if (res.status == 0) {
                console.log(res);
                this.oilcardListtConsume = this.page == 1 ? res.data : [...this.oilcardListtConsume, ...res.data];
                if (res.data.length < this.pageSize) {
                    this.nextFlagoil = true;
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
$font14: 14px;
$font15: 15px;
$font12: 12px;
$colorgray: #909090;
$colortitle: #222222;
$colorview: #333333;

.oil-center {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    position: relative;

    .oil-top {
        width: 100%;
        background: #333333;
        padding: 20px 15px 0;

        .top-section {
            background: #ffffff;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            padding: 15px;

            .top-flex {
                display: flex;
                align-items: center;
                text-align: left;

                .top-view {
                    flex: 1;

                    .top-one {
                        img {
                            width: 21px;
                            height: 21px;
                            margin-right: 4px;
                        }
                    }
                }
            }
        }

        .opacity-div {
            width: 100%;
            height: 14px;
            background: linear-gradient(180deg, rgba(78, 78, 78, 0.2) 0%, #787878 100%);
            margin-top: -14px;
            opacity: 0.2;
        }
    }

    // 横向滑动tab
    .scroll-view_H {
        background-color: #f6f6f6;

        .slide_type_list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            height: 50px;
            .slide_type_list_view {
                padding-bottom: 5px;
                font-size: 15px;
                color: #333333;
            }

            .is_selected {
                color: $btn-color;
                font-weight: bold;
                position: relative;
            }

            .is_selected:before {
                content: '';
                position: absolute;
                width: 24px;
                height: 3px;
                background: linear-gradient(270deg, rgba(255, 130, 0, 0) 0%, $btn-color 100%);
                left: 28%;
                bottom: -2px;
            }
        }
    }

    .content {
        padding: 0px 15px 10px 15px;
        overflow: hidden;

        .action-list {
            margin-top: 10px;
            overflow: hidden;
            border-radius: 5px;
            .flex-list1 {
                background: #ffffff;
                display: flex;
                align-items: center;
                padding: 10px 15px 10px 15px;
                overflow: hidden;
                border-radius: 5px;
                .flex-left1 {
                    flex: 1;
                    .cell1 {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        color: #333333;
                        font-size: 15px;
                        font-weight: 700;
                        .title1 {
                            width: 250px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                    .celltime1 {
                        display: flex;
                        align-items: center;
                        color: #666666;
                        font-size: 12px;
                        margin-top: 3px;
                    }
                    .order-money1 {
                        margin-top: 10px;
                        font-weight: 500;
                        display: flex;
                        align-items: flex-end;
                        color: #ff8200;
                    }
                }
                .cell-btn1 {
                    background-color: $btn-mantle-color;
                    line-height: 22px;
                    font-size: 12px;
                    padding-left: 7px;
                    padding-right: 7px;
                    border: 1px solid $btn-color;
                    color: $btn-color;
                    border-radius: 15px;
                }
                .disable-cell-btn1 {
                    border: 1px solid #dcdcdc;
                    color: #909090;
                    background-color: #f6f6f6;
                }
            }
        }

        .flex-list {
            background: #ffffff;
            display: flex;
            align-items: center;
            padding: 6px 10px 6px 10px;
            overflow: hidden;
            border-radius: 5px;

            .flex-left {
                flex: 1;

                .cell {
                    display: flex;
                    align-items: center;

                    .cell-title {
                        line-height: 24px;
                        font-size: 12px;
                        font-weight: 700;
                        width: 60px;
                    }

                    .cell-detail {
                        font-size: 12px;
                        color: #909090;
                    }

                    .cell-theme {
                        color: $btn-color;
                        font-size: 15px;
                        font-weight: 700;
                        line-height: 24px;
                    }
                }
            }

            .cell-btn {
                background-color: $btn-mantle-color;
                line-height: 24px;
                font-size: 12px;
                padding-left: 7px;
                padding-right: 7px;
                border: 1px solid $btn-color;
                color: $btn-color;
                border-radius: 3px;
            }

            .disable-cell-btn {
                border: 1px solid #dcdcdc;
                color: #909090;
                background-color: #f6f6f6;
            }

            .flex-right {
                text-align: center;
            }
        }
    }

    .top-line {
        position: relative;
    }

    .top-line:after {
        content: '';
        position: absolute;
        left: -20px;
        bottom: 0;
        right: 0;
        height: 100%;
        border-left: 1px solid #f3f3f3;
    }

    .color-font {
        color: $btn-color;
    }

    .title-color {
        color: $colortitle;
    }

    .view-color {
        color: $colorview;
    }

    .padding20 {
        padding-bottom: 12px;
    }

    .margin-50 {
        margin-left: 25px;
    }

    .margin-10 {
        margin-left: 5px;
    }

    .label-txt {
        font-size: $font14;
        padding-bottom: 4px;
    }

    .gray-color {
        color: $colorgray;
    }

    .title-bold {
        font-weight: bold;
    }

    .icon-txt {
        font-size: $font12;
        display: inline-block;
    }

    .text-10 {
        transform: scale(0.83);
        text-align: left;
    }

    .textcenter {
        text-align: center;
        padding-bottom: calc(env(safe-area-inset-bottom) + 44px);
        color: #909090;
        margin-top: 10px;
    }

    .xfjl-more {
    }
    .xfjl-view {
        position: fixed;
        bottom: 0;
        padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 345px;
        background-color: #f6f6f6;
    }
    .xfjl-tip {
        text-align: center;
        font-size: 10px;
    }
}
</style>
