import test from './test';
import layer from './layer';
import platform from './platform';
import mixApi from './mix-api';
import commonUtil from './commonUtil';
import aliKeyBordPlugin from './v3-plugin/aliKeyBordPlugin.js';
import wxKeyBordPlugin from './v3-plugin/wxKeyBordPlugin.js';
import aliPayPlugin from './v3-plugin/aliPayPlugin.js';
import wxPayPlugin from './v3-plugin/wxPayPlugin.js';
import cloudPayPlugin from './v3-plugin/cloudPayPlugin.js';

// #ifdef MP-MPAAS
import mpaasKeyBordPlugin from './v3-plugin/mpaasKeyBordPlugin.js';
import mpaasPayPlugin from './v3-plugin/mpaasPayPlugin.js';
import mpaasAccountPlugin from './v3-plugin/mpaasAccountPlugin.js';
import harmonyRealPersonAuthentication from './harmony-real-person-authentication.js';
// #endif
// #ifdef MP-WEIXIN
// 微信实人认证处理
import wxRealPersonAuthentication from './wx-real-person-authentication';
// #endif
// #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
// 支付宝实人认证处理
import zfbRealPersonAuthentication from './zfb-real-person-authentication';
// #endif
// h5实人认证处理
// #ifdef H5-CLOUD
import h5RealPersonAuthentication from './h5-real-person-authentication';
// #endif
import MPassBuryingPoint from '@/s-kit/js/mpass-burying-point/index.ts';

export default {
    test,
    layer,
    mixApi,
    commonUtil,
    mpBP: new MPassBuryingPoint(),
    // #ifdef MP-MPAAS
    mpaasPayPlugin: new mpaasPayPlugin(),
    keyBordPlugin: new mpaasKeyBordPlugin(),
    accountPlugin: new mpaasAccountPlugin(),
    // #endif
    // #ifdef H5-CLOUD
    cloudPayPlugin: cloudPayPlugin,
    h5RealPersonAuthentication,
    // 使用示例
    // const buryingPoint = new MPassBuryingPoint();
    // buryingPoint.tracker();
    // #endif
    // #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
    aliPayPlugin: new aliPayPlugin(),
    keyBordPlugin: new aliKeyBordPlugin(),
    // #endif
    // #ifdef MP-WEIXIN
    wxPayPlugin,
    keyBordPlugin: wxKeyBordPlugin,
    // #endif
    // #ifdef MP-MPAAS
    harmonyRealPersonAuthentication,
    // #endif
    // #ifdef MP-WEIXIN
    wxRealPersonAuthentication,
    // #endif
    // #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
    zfbRealPersonAuthentication,
    // #endif
    ...platform,
};
// #ifdef H5-CLOUD
import './mtracker.min.js';
// #endif
