<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <zj-navbar :border-bottom="false"></zj-navbar>
        <invoice-batch-auth :authKey="authKey" @success="wxInvoiceSuccess" @fail="wxInvoiceFail"></invoice-batch-auth>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    name: 'invoiceWxCardpack',
    mixins: [publicMixinsApi],
    data() {
        return {
            authKey: '',
        };
    },
    async onLoad(option) {
        // 接收上个页面的传参
        let params = JSON.parse(decodeURIComponent(option.data));
        this.authKey = params?.authKey || '';
    },
    onShow() {},

    methods: {
        wxInvoiceSuccess(e) {
            console.log('wxInvoiceSuccess', e.detail);
            if (e.detail.ret == 0) {
                uni.navigateBack();
            }
        },
        wxInvoiceFail(e) {
            console.log('wxInvoiceFail', e.detail);
        },
    },
};
</script>

<style lang="scss" scoped></style>
