import store from '../../../store/index';
import cnpcBridge from '../v3-native-jsapi/cnpcBridge';
import wxLogin from '../../../utils/login';
import commonUtil from '../commonUtil';
import layer from '../layer';
import sKit from '../index';
import Vue from 'vue';
const paymentCenter = {
    // APP原生交互(支付)
    handleCenter(jsonData, callBack) {
        jsonData.bizType = 'paymentCenter';
        console.log(
            '支付SDK入参：',
            jsonData,
            jsonData.data && jsonData.data.paramsJsonStr ? decodeURIComponent(jsonData.data.paramsJsonStr) : '',
        );
        my.call('mriverToNative', jsonData, data => {
            console.log(jsonData.method + ':' + JSON.stringify(data));
            if (!paymentCenter.resStatus(data) && data.msg !== 'B_B06_000001:无此交易') {
                let bizMsg = data.msg.replace(/,/g, ' ');
                sKit.mpBP.tracker('业务异常埋点', {
                    seed: 'biz_exp',
                    clientSdkError: 'client_002', // 返回sdk标识
                    clientSdkApi: jsonData.method,
                    clientSdkCode: data.code,
                    clientSdkMsg: bizMsg,
                });
            }
            if (data.code == '401') {
                //token过期
                store.dispatch('tokenExpired', {
                    success: async () => {
                        // 313.2版本优化
                        let { gsmsToken } = await cnpcBridge.getUserTokenInfo();
                        //需要刷新token后再调用
                        paymentCenter.initPay(gsmsToken, paymentCenter.handleCenter(jsonData, callBack));
                    },
                    fail: res => {
                        // 重新登录
                        // #ifdef MP-MPAAS
                        // 重新登录
                        cnpcBridge.businessTypeAlertDialog('登录信息已过期,请重新登录', 'LoginExpired', res => {
                            //退出登录
                            commonUtil.logoutFun();
                        });
                        // #endif
                        // #ifdef MP-ALIPAY
                        my.clearStorageSync();
                        layer.backHomeFun();
                        // #endif
                        // #ifdef MP-WEIXIN
                        wxLogin.redirectToRegister();
                        // #endif
                    },
                });
            } else {
                if (callBack) callBack(data);
            }
        });
    },
    /**
     * 初始化sdk
     * @param params
     * @param callBack
     */
    initPay(gsmsToken, callBack) {
        let jsonData = {
            method: 'initPay',
            data: {
                payCenterParams: {
                    token: gsmsToken,
                },
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 退出sdk
     * @param data
     * @param callBack
     */
    exitPay(params, callBack) {
        let jsonData = {
            method: 'exitPay',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 获取充值的支付方式列表
     * @param params
     * @param callBack
     */
    getRechargePayTypeList(params, callBack) {
        let jsonData = {
            method: 'rechargePayTypeList',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 充值发起支付
     * @param params
     * @param callBack
     */
    rechargePay(params, callBack) {
        let jsonData = {
            method: 'rechargePay',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 消费的支付方式列表
     * @param params
     * @param callBack
     */
    getBuyPayTypeList(params, callBack) {
        let jsonData = {
            method: 'buyPayTypeList',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 消费发起支付
     * @param params
     * @param callBack
     */
    buyPay(params, callBack) {
        let jsonData = {
            method: 'buyPay',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 预授权下单
     * @param params
     * @param callBack
     */
    qryPreOrder(params, callBack) {
        let jsonData = {
            method: 'qryPreOrder',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 查询pos待支付订单
     * @param params
     * @param callBack
     */
    getRposUnPayList(callBack) {
        let jsonData = {
            method: 'rposUnPayList',
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 发起pos支付
     * @param params
     * @param callBack
     */
    rposPay(params, callBack) {
        let jsonData = {
            method: 'rposPay',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    /**
     * 发起pos付款码支付
     * @param params
     * @param callBack
     */
    rposCodePay(params, callBack) {
        let jsonData = {
            method: 'rposCodePay',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
    resStatus(res) {
        return res.code === 'PAY_SUCCESS' ? true : false;
    },
    resAuthStatus(res) {
        return res.code === 'PAY_ERROR_003' ? true : false;
    },
    /**
     * 查询订单里已核销电子券（二次支付的时候，查的第一次支付的时候核销的券信息）
     * @param params //String json = "{\"bizModel\":\"3\",\"bizOrderNo\":\"23432434\",\"payOrderNo\":\"122344\",\"provinceCode\":\"122344\"}";
     * bizOrderNo业务订单号，payOrderNo支付订单号，mainOrderNo主支付订单不能同时为空 ，provinceCode  油站编码
     * @param callBack
     */
    queryOrderVerifiedCoupon(params, callBack) {
        let jsonData = {
            method: 'queryOrderVerifiedCoupon',
            data: {
                payCenterParams: params,
            },
        };
        paymentCenter.handleCenter(jsonData, callBack);
    },
};
export default paymentCenter;
