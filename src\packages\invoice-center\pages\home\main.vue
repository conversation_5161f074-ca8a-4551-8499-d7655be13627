<template>
    <div>
        <div>
            <FrostedGlass @change="getInvoiceTitleList"></FrostedGlass>
        </div>
        <div class="detail-center">
            <u-navbar
                :background="pageConfig.bgColor"
                :back-icon-size="40"
                :back-icon-color="pageConfig.titleColor.backIconColor"
                :height="44"
                :title-color="pageConfig.titleColor.color"
                back-text="我的发票"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>

            <!-- <div class="content">
      <div class="order">
        <template v-if="billingList.length<0">
          <u-empty text="暂无开票历史信息" mode="list" :margin-top="80"></u-empty>
        </template>
        <template v-else>
					<div class="content-view">
						<div class='action-list' bg-color='#f6f6f6' v-for="(item, index) in billingList" :key="item.id">
							<u-swipe-action :show="item.show" :index="index" @open="orderOpen" :options="options" @click='clickDelect'>
								开票成功标记 :class='item.state == 1 ? "order-bg" : ""'
								<div class="order-list"  @click="nexttodetail(item)">
									<div class="order-left">
										<div class="order-item-info">
											<div class="order-item-title">{{ item.xsfmc }}</div>
											<div class="order-item-date">
												<text>{{item.kprq}}</text>
												<text>{{item.modifyDateArr[0]}}</text>
												<text class="order-date-week">({{item.modifyDateArr[2]}})</text>
												<text>{{item.modifyDateArr[1]}}</text>
											</div>
										</div>
										<div class="order-item-amount">￥{{item.hjje}}</div>
									</div>
									<img v-if="item.state == 2" class="invalid-icon" src="../../static/invalid.png" alt="">	
								</div>
							</u-swipe-action>
						</div>
					</div>
          <div class='loadmore' :style='{"padding-bottom": "10px", "padding-top": billingList.length? "0": "10px"}'>
            <u-loadmore @loadmore='clickLoadMore' :status="loadMortState" :load-text='loadText' />
          </div>
        </template>
      </div>
      </div>-->

            <!-- 改版  发票历史优化，合并抬头与列表，列表新增时间筛选 -->
            <div class="header-tabs flex-center-around">
                <div
                    class="tabs-item"
                    @click="changeTab(index)"
                    :class="{ active: tabIndex == index }"
                    v-for="(item, index) in tabs"
                    :key="index"
                    >{{ item.name }}</div
                >
            </div>
            <div v-if="tabIndex != 0" class="tabs-menu flex-center-around">
                <div class="menu-item" @click="filterFunc(1)">
                    时间
                    <div class="filter-btn">
                        <div class="top-btn">
                            <img v-if="invoiceFilterIndex == 0" src="@/static/images/filter-active.png" alt />
                            <img v-else src="@/static/images/filter.png" alt />
                        </div>
                        <div class="top-bottom">
                            <img v-if="invoiceFilterIndex == 1" src="@/static/images/filter-active.png" alt />
                            <img v-else src="@/static/images/filter.png" alt />
                        </div>
                    </div>
                </div>
                <div class="menu-item" @click="filterFunc(2)">
                    金额
                    <div class="filter-btn">
                        <div class="top-btn">
                            <img v-if="invoiceFilterIndex == 2" src="@/static/images/filter-active.png" alt />
                            <img v-else src="@/static/images/filter.png" alt />
                        </div>
                        <div class="top-bottom">
                            <img v-if="invoiceFilterIndex == 3" src="@/static/images/filter-active.png" alt />
                            <img v-else src="@/static/images/filter.png" alt />
                        </div>
                    </div>
                </div>
            </div>

            <div class="scroll-view-wrap">
                <!-- 列表 -->
                <scroll-view scroll-y class="content-list" @scrolltolower="reachBottom">
                    <div class="content-view" v-if="billingList.length > 0">
                        <div class="action-list" bg-color="#f6f6f6" v-for="item in billingList" :key="item.id">
                            <!-- <u-swipe-action :show="item.show" :heaindex="index" @open="orderOpen" :options="options" @click="clickDelect"> -->
                            <div class="tax-list" v-if="tabIndex == 0" @click="nexttodetail(item, 0)">
                                <div class="left">
                                    <div class="title">{{ item.invoicetitle }}</div>
                                    <p v-if="item.taxcode">税号：{{ item.taxcode }}</p>
                                </div>
                                <div class="right">
                                    <u-icon name="arrow-right"></u-icon>
                                </div>
                            </div>
                            <div class="invoice-list" v-else @click="nexttodetail(item, 1)">
                                <div class="title">{{ item.xsfmc }}</div>
                                <div class="content">
                                    <div class="left">
                                        <p>购买方：{{ item.gmfmc }}</p>
                                        <p>开票日期：{{ item.kprq }}</p>
                                        <p>开票金额：{{ item.hjje }}元</p>
                                    </div>
                                    <div class="right">
                                        <u-icon name="arrow-right"></u-icon>
                                    </div>
                                </div>
                            </div>
                            <!-- </u-swipe-action> -->
                        </div>
                    </div>
                    <div
                        class="loadmore"
                        v-if="noMore"
                        :style="{ 'padding-bottom': '10px', 'padding-top': billingList.length ? '0' : '10px' }"
                    >
                        <u-loadmore status="nomore" />
                    </div>
                </scroll-view>
            </div>
            <div class="add-title" v-if="tabIndex == 0">
                <div class="add-title-btn" @click="addTitle">添加抬头</div>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getInvoiceTitleList, delInvoiceTitlePost, getBillingHistoryListPost, deleteInvoice } from '@/api/my-center.js';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import { mapGetters } from 'vuex';
export default {
    name: 'invoice-history',
    components: {
        FrostedGlass,
    },
    computed: {
        ...mapGetters(['isLogin']),
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            options: [
                {
                    text: '删除',
                    style: {
                        backgroundColor: '#dd524d',
                        borderTopRightRadius: '5px',
                        borderBottomRightRadius: '5px',
                    },
                },
            ],
            // 下拉刷新状态
            loadText: {
                loadmore: '点击或上拉加载更多',
                loading: '努力加载中',
                nomore: '暂无更多数据',
            },
            // 上拉加载状态
            loadMortState: 'loading',

            billingList: [],
            //分页页数
            page: 1,
            //一页数据个数
            rows: 10,
            tabs: [
                {
                    name: '抬头管理',
                },
                {
                    name: '发票列表',
                },
            ],
            tabIndex: 0,

            invoiceFilter: [
                {
                    name: '最近时间',
                    type: 'kprq',
                    tag: 'A',
                },
                {
                    name: '最早时间',
                    type: 'kprq',
                    tag: 'D',
                },
                {
                    name: '小额优先',
                    type: 'hjje',
                    tag: 'A',
                },
                {
                    name: '大额优先',
                    type: 'hjje',
                    tag: 'D',
                },
            ],
            invoiceFilterIndex: 1,

            noMore: false,
        };
    },
    onShow() {
        if (this.isLogin) {
            this.init();
            if (this.tabIndex == 0) {
                this.getInvoiceTitleList();
            } else {
                this.getPageDataFun();
            }
        }
    },
    methods: {
        init() {
            this.noMore = false;
            this.billingList = [];
            this.page = 1;
        },
        // 分类切换
        changeTab(idx) {
            if (this.tabIndex != idx) {
                this.init();
                this.tabIndex = idx;
                if (idx == 0) {
                    this.getInvoiceTitleList();
                } else {
                    this.getPageDataFun();
                }
            }
        },

        // 过滤
        filterFunc(type) {
            if (type == 1) {
                if (this.invoiceFilterIndex == 0) {
                    this.invoiceFilterIndex = 1;
                } else {
                    this.invoiceFilterIndex = 0;
                }
            } else {
                if (this.invoiceFilterIndex == 2) {
                    this.invoiceFilterIndex = 3;
                } else {
                    this.invoiceFilterIndex = 2;
                }
            }
            this.init();
            this.getPageDataFun();
        },

        // 获取抬头列表
        async getInvoiceTitleList() {
            let { data } = await getInvoiceTitleList();
            data = data.map(item => {
                item.show = false;
                return item;
            });
            this.billingList = [...this.billingList, ...data];
            this.noMore = true;
        },
        // 添加抬头
        addTitle() {
            if (this.billingList.length < 3) {
                // 去添加抬头
                uni.navigateTo({
                    url: '/packages/invoice-center/pages/add-invoice-title/main?type=add',
                });
                return;
            }
            this.$util.tipsToastNoicon('每位用户只允许同时存在三张发票名片！');
        },

        // 获取发票列表
        // 状态 0:不可开票 1:未开 2:已开 3:申请中 4:红冲中
        async getPageDataFun() {
            let { page, invoiceFilterIndex, invoiceFilter } = this;
            let params = {
                pageNo: page,
                sortType: invoiceFilter[invoiceFilterIndex].type,
                sortRule: invoiceFilter[invoiceFilterIndex].tag,
            };
            let res = await getBillingHistoryListPost(params);
            if (this.page < Number(res.totalPages) || res.data.length == 0 || res.data.length < 10) {
                this.noMore = true;
            }
            this.billingList = this.billingList.concat(res.data);
        },

        /**
         * 处理日期
         */
        handleModifyTime(modifyTime, weekTime) {
            if (!modifyTime || !weekTime) return '';
            weekTime = weekTime.replace('星期', '周');
            const [date, time] = modifyTime.split(/\s+/);
            return [date, time, weekTime];
        },

        // 订单左滑事件
        orderOpen(index) {
            // 先将正在被操作的swipeAction标记为打开状态，否则由于props的特性限制，
            // 原本为'false'，再次设置为'false'会无效
            this.billingList[index].show = true;
            this.billingList.map((val, idx) => {
                if (index != idx) this.billingList[idx].show = false;
            });
        },
        // 删除订单
        async clickDelect(index) {
            await this.$util.showModal('订单删除后将无法恢复，确定删除？');
            await deleteInvoice({
                id: this.billingList[index].id,
            });
            this.billingList.splice(index, 1);
            await this.$util.showModal('删除成功', true);
        },
        clickLoadMore() {
            this.getPageDataFun();
        },

        //详情点击事件
        nexttodetail(item, type) {
            if (type == 0) {
                // 抬头详情
                uni.navigateTo({
                    url: '/packages/invoice-center/pages/header-detail/main?id=' + item.id,
                });
            } else {
                // 发票详情
                uni.navigateTo({
                    url: `/packages/invoice-center/pages/invoice-detail/main?id=${item.id}`,
                });
            }
        },

        // 触底加载
        reachBottom() {
            if (this.tabIndex == 1) {
                this.page++;
                this.getPageDataFun();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
$font14: 14px;
/* .detail-center {
  width: 100%;
  min-height: 100%;
  background: #F6F6F6;
  box-sizing: border-box;

  .content {
    padding: 0 15px;
		.content-view {
			overflow: hidden;
			margin-top: 10px;
			
			.action-list {
				margin-bottom: 10px;
				overflow: hidden;
				border-radius: 5px;
				.order-list {
					position: relative;
					height: 75px;
					border-radius: 5px;
					display: flex;
					align-items: center;
					padding: 0 10px 0 10px;
					
					.order-left {
						flex: 1;
						display: flex;
						justify-content: space-between;
						align-items: center;
						.order-item-info {
							.order-item-title {
								font-size: 15px;
								font-weight: bold;
								color: #333333;
								line-height: 24px;
							}
							.order-item-date {
								font-size: 12px;
								font-weight: 400;
								color: #909090;
								line-height: 24px;
								.order-date-week {
									padding: 0 10px;
								}
							}
						}
						.order-item-amount {
							font-size: $font14;
							font-weight: 700;
							color: $btn-color;
							line-height: 24px;
						}
					}
					
					.order-img {
						flex: 0.5;
					
						img {
							width: 44px;
						}
					}
					
					.order-right {
						height: 72px;
					
						.right-money {
							color: $btn-color;
							font-size: $font14;
							text-align: right;
					
							span {
								font-size: 15px;
								font-weight: 700;
								line-height: 21px;
								margin-top: 3px;
							}
					
							.arrow-right {
								margin-left: 9px;
							}
						}
					
						.right-div {
							display: flex;
							padding-top: 19px;
							justify-content: flex-end;
					
							.used-div {
								width: 50px;
								border-radius: 3px;
								color: #909090;
								background: #F6F6F6;
								border: 1px solid #DCDCDC;
								line-height: 23px;
								text-align: center;
								font-size: 12px;
							}
					
							.open-div {
								font-size: 12px;
								width: 50px;
								background: $btn-mantle-color;
								border-radius: 3px;
								border: 1px solid $btn-color;
								font-size: $font14;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: $btn-color;
								line-height: 23px;
								text-align: center;
							}
						}
					}
				}
				.invalid-icon {
					position: absolute;
					top: 0;
					right: 0;
					width: 50px;
					height: 50px;
				}
			}
			
		}
	.order-bg {
		background-image: url(../../static/kaipiao-success.png);
		background-repeat: no-repeat;
		background-position: top right;
		background-size: 55px 51px;
	}

    
  }
} */
.flex-center-around {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-around;
}
.detail-center {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding-bottom: env(safe-area-inset-bottom);
    .header-tabs {
        width: 100%;
        height: 84rpx;
        background: #fff;
        .tabs-item {
            height: 100%;
            line-height: 84rpx;
            font-size: 34rpx;
            color: #333333;
            position: relative;
            &.active {
                color: #f96702;
                &::after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 2rpx;
                    background: #f96702;
                    left: 0;
                    bottom: 0;
                }
            }
        }
    }
    .tabs-menu {
        width: 100%;
        height: 80rpx;
        background: #fff;
        .menu-item {
            font-size: 30rpx;
            color: #333333;
            height: 100%;
            line-height: 80rpx;
            position: relative;
            display: flex;
            .filter-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-left: 10rpx;
                div {
                    height: 30rpx;
                    width: 30rpx;
                    position: relative;
                    &.top-btn {
                        top: 6rpx;
                        img {
                            transform: rotate(180deg);
                        }
                    }
                    &.top-bottom {
                        top: -6rpx;
                    }
                    img {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        margin-left: 0;
                    }
                }
            }
            .filter-box {
                position: absolute;
                left: -20rpx;
                top: 100rpx;
                z-index: 10;
                padding: 20rpx;
                box-sizing: content-box;
                background: #fff;
                width: 120%;
                text-align: center;
                border-radius: 8rpx;
                &::before {
                    content: '';
                    display: inline-block;
                    width: 0;
                    height: 0;
                    border-width: 20rpx;
                    border-style: solid;
                    border-color: transparent transparent #fff transparent;
                    position: absolute;
                    left: 0;
                    top: -40rpx;
                    right: 0;
                    margin: auto;
                }
                div.active {
                    color: #f96702;
                }
                div:first-child {
                    border-bottom: solid 2rpx #fafafa;
                }
            }
            img {
                width: 19rpx;
                height: 13rpx;
                margin-left: 8rpx;
            }
            &.is-open {
                img {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .scroll-view-wrap {
        min-height: 0;
        flex: 1;
        position: relative;
        padding: 24rpx 24rpx;
        background: #f5f5f5;
        .content-list {
            height: 100%;
            flex: 1;
            .content-view .action-list {
                border-radius: 16rpx;
                margin-bottom: 24rpx;
                overflow: hidden;
                background: #fff;
                .tax-list {
                    padding: 20rpx 30rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .title {
                        font-size: 30rpx;
                        color: #333333;
                        margin-bottom: 16rpx;
                    }
                    p {
                        margin-bottom: 8rpx;
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
                .invoice-list {
                    padding: 20rpx 30rpx;
                    .title {
                        font-size: 30rpx;
                        color: #333333;
                        margin-bottom: 16rpx;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    .content {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .left {
                            font-size: 26rpx;
                            color: #999999;
                            p {
                                margin-bottom: 8rpx;
                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
            }
            .loadmore {
                ::v-deep .u-load-more-wrap {
                    background-color: transparent !important;
                    view {
                        background-color: transparent !important;
                    }
                }
            }
        }
    }
    .add-title {
        padding: 24rpx;
        &-btn {
            width: 100%;
            height: 98rpx;
            font-size: 36rpx;
            color: #fff;
            background: #f96702;
            border-radius: 8rpx;
            text-align: center;
            line-height: 98rpx;
        }
    }
}
</style>
