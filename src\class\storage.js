export class Storage {
    constructor(key, value = '') {
        this.value = uni.getStorageSync(key);
        this.key = key;
        this.defaultValue = value;
        if (!this.value) {
            this.update(value);
        }
    }
    update(value) {
        uni.setStorageSync(this.key, value);
        this.value = value;
    }
    reset() {
        uni.setStorageSync(this.key, this.defaultValue);
        this.value = this.defaultValue;
    }
}
