<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details fl-column p-bf bg-F7F7FB">
            <zj-navbar
                :titleSize="36"
                :height="44"
                :titleFontWeight="500"
                title="交易明细"
                class="zj_navbar"
                :border-bottom="false"
            ></zj-navbar>
            <div class="wallet_details_content fl-column f-1 mh-0">
                <div class="select_div" ref="select_div">
                    <div class="select_item" :class="selectId == 0 ? 'selected_class' : ''" @click="selectTypeClick(0, 'null')">
                        <div>全部</div>
                    </div>
                    <div class="select_item" :class="selectId == 1 ? 'selected_class' : ''" @click="selectTypeClick(1, 10)">
                        <div>充值</div>
                    </div>
                    <div class="select_item" :class="selectId == 2 ? 'selected_class' : ''" @click="selectTypeClick(2, 1)">
                        <div>支出</div>
                    </div>
                </div>

                <div class="f-1 mh-0 dataList_box">
                    <!-- :emptyImage="require('../../images/wallet')" -->
                    <!-- emptyText="暂未查询到昆仑e享卡明细" -->
                    <zj-data-list
                        ref="dataList"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                        :showEmpty="showEmpty"
                        :emptyImage="require('../../images/kt10qb.png')"
                        emptyText="暂未查询到昆仑e享卡明细"
                    >
                        <div class="content_div">
                            <div v-for="(item, index) in detailsList" :key="index" class="item_div" @click="toDetails(item)">
                                <div class="item_left">
                                    <div class="left_title">{{ item.flowDirection }}</div>
                                    <div class="left_time">{{ item.time }}</div>
                                </div>
                                <div class="item_right fl-row font-style" :class="item.amountChange > 0 ? 'color-E64F22' : 'reduce_class'">
                                    <div class="right_type" v-if="item.amountChange > 0">+</div>
                                    <div class="right_amount">{{ item.amountChange }}</div>
                                </div>
                            </div>
                            <div class="btn_box">
                                <div class="btn" @click="goDetailOfConsumption">电子卡消费明细</div>
                            </div>
                        </div>
                    </zj-data-list>
                    <div class="btn_box_bottom" v-if="showEmpty">
                        <div class="btn" @click="goDetailOfConsumption">电子卡消费明细</div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { detailList } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-transaction-details',
    data() {
        return {
            selectId: 0,
            diffHeight: 0,
            detailsList: [],
            pageNum: 1,
            pageSize: 10,
            bottomBounce: false,
            val: 'null',
            showEmpty: false,
        };
    },
    mounted() {
        this.selectTypeClick(0, 'null');
    },
    onLoad(option) {},
    methods: {
        //前往详情页
        toDetails(item) {
            let params = item;
            params.walletDetail = true;
            if (!params.sourceOrderNo) return;
            if (params.sourceChannel == '能源e站-能源商城') return;
            params.orderNo = params.sourceOrderNo;
            if (item.amountChange > 0) {
                this.$sKit.layer.useRouter('/packages/third-order/pages/wallet-order-detail/main', params);
            } else {
                if (params.type == 18) {
                    this.$sKit.layer.useRouter('/packages/third-order/pages/charge-order-detail/main', params);
                }else if(params.tradeMode == 10 || params.tradeMode == 3 || params.type == 10 || params.type == 11 ){
                    return
                } else {
                    this.$sKit.layer.useRouter('/packages/third-order/pages/order-detail/main', params);
                }
            }
        },
        //加载
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.selectTypeClick(this.selectId, this.val, { isInit: false });
            }
        },

        //刷新
        refreshPullDown() {
            this.selectTypeClick(this.selectId, this.val, { isInit: true });
        },
        /**
         * @description  : 筛选、初始化数据
         * @param         {String} pageSize -页大小
         * @param         {String} pageNum -当前页
         * @param         {String} filter -筛选条件（null-全部；10-充值；1-支出）
         * @return        {*}
         */
        async selectTypeClick(id, val, { isInit = false } = {}) {
            if (isInit) {
                this.pageNum = 1;
                this.detailsList = [];
            }
            if (this.selectId != id) {
                this.selectId = id;
                this.pageNum = 1;
                this.detailsList = [];
            }
            this.val = val;
            // 获取钱包明细列表
            let params = {
                filter: val, //筛选条件（null-全部；10-充值；1-支出）
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };

            let res = await detailList(params);
            this.$refs.dataList.stopRefresh();
            if (res && res.success && res.data) {
                // return
                this.detailsList = this.detailsList.concat(res.data.accountInfoList || []);
                this.pageNum++;
                if (this.pageSize > res.data.accountInfoList.length) {
                    this.$refs.dataList.loadStatus = 'nomore'; // 没有更多了
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown'; // 上拉加载更多
                }
            }
            if (this.detailsList.length <= 0) {
                this.showEmpty = true; // 显示暂无数据图片
            } else {
                this.showEmpty = false; // 显示暂无数据图片
            }
        },
        //去电子卡消费明细
        goDetailOfConsumption() {
            this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/ecard-xf-records/main', {}, 'navigateTo');
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        .dataList_box {
            max-height: calc(100vh - 65px);
            position: relative;
        }

        // background: $page-base-bg-color;
        .select_div {
            display: flex;
            width: 100%;
            height: 44px;
            background: #fff;
            flex-direction: row;
            justify-content: space-between;

            .select_item {
                width: 33%;
                text-align: center;

                div {
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 44px;
                }
            }

            .selected_class {
                div {
                    font-size: 16px;
                    font-weight: 500;
                    color: #e64f22;
                    line-height: 43px;
                    border-bottom: 2px solid #e64f22;
                    display: inline-block;
                }
            }
        }

        .content_div {
            padding: 0 15px;
            position: relative;

            .item_div {
                display: flex;
                background: #fff;
                border-radius: 8px;
                margin-top: 10px;
                padding: 15px;
                width: 100%;
                align-items: center;
                flex-direction: row;
                justify-content: space-between;

                .item_left {
                    width: 70%;
                    display: flex;
                    flex-direction: column;

                    .left_title {
                        font-size: 14px;
                        font-weight: 400;
                        color: #333333;
                        line-height: 20px;
                    }

                    .left_time {
                        font-size: 12px;
                        font-weight: 400;
                        color: #999999;
                        line-height: 17px;
                        margin-top: 9px;
                    }
                }

                .item_right {
                    justify-content: right;

                    .right_type {
                        font-size: 16px;
                    }

                    .right_amount {
                        font-size: 16px;
                    }
                }

                .reduce_class {
                    color: #333;
                }
            }
        }
    }

    .btn_box {
        position: absolute;
        bottom: -87px;
        width: 92%;
        padding-bottom: 15px;
        left: 50%;
        transform: translateX(-50%);

        .btn {
            width: 100%;
            // margin: 0 auto;
            height: 44px;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            line-height: 44px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            border-radius: 8px;
        }
    }
}

.btn_box_bottom {
    width: 92%;
    position: absolute;
    bottom: 40rpx;
    left: 50%;
    transform: translateX(-50%);
}

.btn {
    width: 100%;
    margin: 0 auto;
    height: 44px;
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
    line-height: 44px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    border-radius: 8px;
}
</style>
