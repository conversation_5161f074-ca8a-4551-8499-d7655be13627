<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <!-- 添加发票抬头页面 -->
    <div class="pageMpaas" @click="hidHeaderNameList">
        <div class="p-bf fl-column add-center">
            <zj-navbar :border-bottom="false" :title="title"></zj-navbar>
            <div class="content">
                <div class="copy-box">
                    <textarea
                        class="copy-textarea"
                        disable-default-padding
                        placeholder="粘贴到此区域，智能识别抬头名称、税号、地址、电话、开户银行、银行账号信息。"
                        placeholder-class="copy-textarea-placeholder"
                        v-model="copyText"
                        auto-height
                        fixed
                        :enableNative="false"
                        :show-count="false"
                    ></textarea>
                    <div class="copy-btn" @click="copyAndIdentify"
                        >粘贴并识别<img
                            v-if="!invoiceGuideFlag"
                            class="invoiceGuidePage"
                            @click="closeInvoiceGuide"
                            src="../../image/invoiceGuidePage.png"
                            alt=""
                    /></div>
                </div>
                <div class="open-ticket-box">
                    <div class="section-box section-box2 line_bottom">
                        <div class="left"> <div class="required" style="display: inline-block">*</div>抬头类型 </div>
                        <div class="right type">
                            <div class="right-item" v-for="(item, index) in typeList" :key="index" @click="changeType(index)">
                                <img v-if="item.tag == invoiceInfo.buyerNature" src="../../image/type-checked.png" alt />
                                <img v-else src="../../image/type-unchecked.png" alt />
                                <div class="right-text">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left"> <span class="required">*</span>抬头名称 </div>
                        <div class="right">
                            <input
                                v-model="invoiceInfo.buyerName"
                                class="textarea_style"
                                style="width: 100% !important; text-align: right"
                                @input="buyerNameInput"
                                placeholder="请输入抬头名称"
                                disable-default-padding
                            />
                            <div class="headerNameList" v-if="showHeaderNameList && headerNameList.length > 0">
                                <div
                                    class="headerNameItem line_bottom"
                                    v-for="(item, index) in headerNameList"
                                    :key="index"
                                    @click="checkHeaderName(item)"
                                >
                                    {{ item.buyerName }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">税号</div>
                        <div class="right">
                            <input type="text" class="textarea_style" v-model="invoiceInfo.buyerTaxId" placeholder="请输入税号" />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">地址</div>
                        <div class="right">
                            <input
                                class="textarea_style"
                                type="text"
                                v-model="invoiceInfo.buyerAddr"
                                placeholder="请输入地址"
                                disable-default-padding
                            />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">电话</div>
                        <div class="right">
                            <input
                                type="text"
                                class="textarea_style"
                                @input="buyerTelInput"
                                v-model="invoiceInfo.buyerTel"
                                placeholder="请输入联系方式"
                            />
                        </div>
                    </div>
                    <div class="section-box line_bottom">
                        <div class="left">开户银行</div>
                        <div class="right">
                            <input
                                class="textarea_style"
                                type="text"
                                v-model="invoiceInfo.buyerFinancial"
                                placeholder="请输入开户银行"
                                disable-default-padding
                            />
                        </div>
                    </div>
                    <div class="section-box">
                        <div class="left">银行账号</div>
                        <div class="right">
                            <input type="text" class="textarea_style" v-model="invoiceInfo.buyerAccount" placeholder="请输入银行账号" />
                        </div>
                    </div>
                </div>
                <div class="default">
                    <div class="text">设置为默认抬头</div>
                    <switch
                        :checked="invoiceInfo.defaultCount"
                        color="#e64f22"
                        style="transform: scale(0.8)"
                        @change="defaultCountChange"
                        :disabled="isDisable"
                    />
                </div>
                <div class="add-footer" @click="submit">{{ btnText }}</div>
            </div>
            <zj-show-modal></zj-show-modal>
            <div class="mask" v-if="!invoiceGuideFlag" @click="closeInvoiceGuide"></div>
        </div>
    </div>
</template>

<script>
import {
    invoiceTitleUpApi,
    invoiceRcmdListkApi,
    invoiceRcmdCardkApi,
    regexInvoiceTitle,
} from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'invoiceTitleForm',
    data() {
        return {
            // 导航文字
            title: '新增发票抬头',
            // 按钮文字
            btnText: '保存',
            // 页面类型 add 是添加发票  edit 是编辑发票
            type: 'add',
            //抬头类型
            typeList: [
                {
                    name: '非自然人',
                    tag: '3',
                },
                {
                    name: '自然人',
                    tag: '4',
                },
            ],
            // 表单绑定值
            invoiceInfo: {
                // 抬头类型
                buyerNature: 3,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
                //是否默认抬头
                defaultCount: false,
            },
            autoHeight: true,
            isDisable: false,
            autoHeight: true,
            showHeaderNameList: false,
            headerNameList: [],
            refer: '',
            copyText: '',
            invoiceGuideFlag: true,
        };
    },
    onLoad(options) {
        this.invoiceGuideFlag = uni.getStorageSync('invoiceGuideFlag');
        this.buyerNameInput = this.$sKit.commonUtil.debounce(this.buyerNameInput, 800);
        // 获得路由传参，判断添加还是编辑
        if (JSON.stringify(options) !== '{}') {
            let params = JSON.parse(decodeURIComponent(options.data || ''));
            this.type = params.type;
            if (params.refer) {
                this.refer = params.refer;
            }
            if (this.type == 'edit') {
                this.title = params.title;
                this.btnText = params.btnText;
                let invoiceInfoRes = JSON.stringify(params.detail);
                var newObj = JSON.parse(invoiceInfoRes.replace(/"undefined"|null/g, `""`));
                if (newObj.buyerNature == 1 || newObj.buyerNature == 2) {
                    newObj.buyerNature = 3;
                }
                Object.assign(this.invoiceInfo, newObj);
                this.isDisable = params.detail.defaultCount;
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'editHeaderPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            } else {
                this.$sKit.mpBP.tracker('电子发票', {
                    seed: 'invoiceBiz',
                    pageID: 'addHeaderPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
        }
    },
    methods: {
        closeInvoiceGuide() {
            this.invoiceGuideFlag = true;
            uni.setStorageSync('invoiceGuideFlag', true);
        },
        copyAndIdentify() {
            let newstr = this.copyText.replace(/\s*/g, '');
            if (newstr.length > 0) {
                this.regexInvoiceTitleFun();
            } else {
                this.$store.dispatch('zjShowModal', {
                    content: '“能源e站”申请获取你复制的内容，为您自动填充开票信息，是否允许？',
                    confirmText: '允许粘贴',
                    cancelText: '不允许粘贴',
                    confirmColor: '#FF4000',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            uni.getClipboardData({
                                success: res => {
                                    this.copyText = res.data;
                                    this.regexInvoiceTitleFun();
                                },
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        async regexInvoiceTitleFun() {
            let defaultObj = {
                // 抬头类型
                buyerNature: this.invoiceInfo.buyerNature,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
                //是否默认抬头
                defaultCount: this.invoiceInfo.defaultCount,
            };
            let omitNull = obj => {
                Object.keys(obj)
                    .filter(k => obj[k] === null)
                    .forEach(k => delete obj[k]);
                return obj;
            };
            let isAllNull = obj => {
                let bool = true;
                for (const key in obj) {
                    if (obj[key] !== null) {
                        bool = false;
                        return;
                    }
                }
                return bool;
            };
            let newstr = this.copyText.replace(/\s*/g, '');
            if (newstr.length > 0) {
                let res = await regexInvoiceTitle({ titleInfo: this.copyText });
                if (res && res.success) {
                    let resObj = res.data;
                    if (isAllNull(resObj)) {
                        uni.showToast({
                            title: '信息未识别，请手动填充',
                            icon: 'none',
                            duration: 2000,
                        });
                    } else {
                        if (resObj.buyerNameCompany) {
                            resObj.buyerNature = 3;
                            resObj.buyerName = resObj.buyerNameCompany;
                        } else if (resObj.buyerNamePerson) {
                            resObj.buyerNature = 4;
                            resObj.buyerName = resObj.buyerNamePerson;
                        }
                        this.invoiceInfo = { ...omitNull(defaultObj), ...omitNull(res.data) };
                    }
                }
            } else {
                uni.showToast({
                    title: '暂未识别到粘贴内容',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        buyerTelInput(e) {
            // this.invoiceInfo.buyerTel = e.detail.value.replace(/\s/g, '');
            let str = e.detail.value.toString().match(/\S/g).join('');
            setTimeout(_ => {
                this.invoiceInfo.buyerTel = str;
            }, 10);
        },
        /**
         * @description  : 点击页面其他地方隐藏候选弹窗
         * @return        {*}
         */
        hidHeaderNameList() {
            this.showHeaderNameList = false;
        },
        /**
         * @description  : 抬头名称input事件，触发企业抬头名称模糊匹配
         * @param         {*} e:组件实例
         * @return        {*}
         */
        buyerNameInput(e) {
            if (this.invoiceInfo.buyerNature == 3 && e.detail.value) {
                this.invoiceRcmdListkPost(e.detail.value);
            } else {
                this.showHeaderNameList = false;
            }
        },
        async invoiceRcmdListkPost(name) {
            let res = await invoiceRcmdListkApi({ buyerMatchName: name }, { isload: false, isCustomErr: true });
            if (res && res.success) {
                this.showHeaderNameList = true;
                this.headerNameList = res.data;
            }
        },
        /**
         * @description  : 选中抬头，回显抬头信息
         * @param         {*} item:抬头对象
         * @return        {*}
         */
        async checkHeaderName(item) {
            let defaultObj = {
                // 抬头类型
                buyerNature: this.invoiceInfo.buyerNature,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
                //是否默认抬头
                defaultCount: this.invoiceInfo.defaultCount,
            };
            if (this.invoiceInfo.headerId) {
                defaultObj.headerId = this.invoiceInfo.headerId;
            }
            let params = {
                buyerName: item.buyerName,
                buyerTaxId: item.buyerTaxId,
            };
            let res = await invoiceRcmdCardkApi(params);
            if (res && res.success) {
                let omitNull = obj => {
                    Object.keys(obj)
                        .filter(k => obj[k] === null)
                        .forEach(k => delete obj[k]);
                    return obj;
                };
                this.invoiceInfo = { ...omitNull(defaultObj), ...omitNull(res.data) };
                this.showHeaderNameList = false;
            }
        },
        /**
         * @description  : 默认抬头change事件，修改默认抬头开关绑定值
         * @param         {*} e:组件实例
         * @return        {*}
         */
        defaultCountChange(e) {
            this.invoiceInfo.defaultCount = e.detail.value;
        },
        /**
         * @description  : 抬头类型change事件，修改默认抬头类型绑定值
         * @param         {*} index:序号
         * @return        {*}
         */
        changeType(index) {
            let newInvoiceInfo = {
                // 抬头类型
                buyerNature: this.typeList[index].tag,
                //抬头名称
                buyerName: '',
                //税号
                buyerTaxId: '',
                // 地址
                buyerAddr: '',
                // 联系方式
                buyerTel: '',
                // 开户银行
                buyerFinancial: '',
                //银行账号
                buyerAccount: '',
                //是否默认抬头
                defaultCount: this.invoiceInfo.defaultCount,
            };
            if (this.invoiceInfo.headerId) {
                newInvoiceInfo.headerId = this.invoiceInfo.headerId;
            }
            this.invoiceInfo = Object.assign(this.invoiceInfo, newInvoiceInfo);
        },

        /**
     * @description  :    提交点击事件
                          如果是个人的话，抬头名称验证下中文和中间点，企业的话，不验证抬头输入的格式
                          发票类型为个人税号为非必填不校验，企业必填校验
                          当前字符串中不能有空格
     * @return        {*}
     */
        async submit() {
            if (this.invoiceInfo.buyerName == '') {
                this.$sKit.layer.showToast({ title: '抬头名称不能为空' });
                return;
            }

            if (this.invoiceInfo.buyerNature == 4) {
                if (!this.$sKit.test.checkSAndE(this.invoiceInfo.buyerName)) {
                    this.$sKit.layer.showToast({ title: '请输入正确的抬头名称' });
                    return;
                }
            } else {
                if (this.invoiceInfo.buyerName.length > 50 || !this.$sKit.test.enterpriseTitleCheck(this.invoiceInfo.buyerName)) {
                    this.$sKit.layer.showToast({ title: '请输入正确的抬头名称' });
                    return;
                }
            }
            //税号只校验格式不校验必填
            if (!this.$sKit.test.checkShuiHao_individual_third(this.invoiceInfo.buyerTaxId)) {
                return;
            }
            if (this.invoiceInfo.buyerTel !== '' && !this.$sKit.test.checkTelPhone(this.invoiceInfo.buyerTel)) {
                this.$sKit.layer.showToast({ title: '请输入正确的电话号码' });
                return;
            }
            if (this.invoiceInfo.buyerAddr !== '' && this.invoiceInfo.buyerAddr?.length > 40) {
                this.$sKit.layer.showToast({ title: '请输入正确的地址' });
                return;
            }
            if (this.invoiceInfo.buyerFinancial !== '' && !this.$sKit.test.checkBankName(this.invoiceInfo.buyerFinancial)) {
                this.$sKit.layer.showToast({ title: '请输入正确的开户银行' });
                return;
            }
            if (this.invoiceInfo.buyerAccount !== '' && !this.$sKit.test.newCheckBankCount(this.invoiceInfo.buyerAccount)) {
                this.$sKit.layer.showToast({ title: '请输入正确的银行账号' });
                return;
            }
            let res = await invoiceTitleUpApi({ ...this.invoiceInfo });

            if (res && res.success) {
                // 如果是添加返回一层到抬头列表，如果是编辑返回两层到抬头列表
                if (this.type == 'add') {
                    uni.navigateBack({
                        delta: 1,
                    });
                } else {
                    uni.navigateBack({
                        delta: 2,
                    });
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 15px;
$colorgray: #909090;

.add-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .content {
        width: 100%;
        flex: 1;
        background: #f7f7fb;
        padding: 36rpx 32rpx;

        .copy-box {
            width: 100%;
            min-height: 200rpx;
            background: #ffffff;
            border-radius: 16rpx;
            padding: 24rpx 32rpx;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-bottom: 28rpx;

            .copy-textarea {
                min-height: 80rpx;
                width: 100%;
            }

            .copy-btn {
                margin-top: 22rpx;
                width: 130rpx;
                height: 42rpx;
                background: #f7f7fb;
                border-radius: 8rpx;
                border: 1px solid #ff4909;
                font-size: 20rpx;
                color: #ff4909;
                line-height: 40rpx;
                text-align: center;
                position: relative;

                .invoiceGuidePage {
                    position: absolute;
                    z-index: 999999;
                    width: 750rpx;
                    height: 321rpx;
                    right: -66rpx;
                    top: -2rpx;
                }
            }
        }

        .open-ticket-box {
            width: 100%;
            border-radius: 24rpx;
            background: #fff;

            .section-box {
                padding: 24rpx 28rpx;
                // border-top: solid 2rpx #eee;
                display: flex;
                align-items: flex-start;
                min-height: 44px;

                .left {
                    height: 20px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: #333;
                    min-width: 170rpx;

                    .required {
                        color: #e64f22;
                        font-size: 14px;
                    }
                }

                .right {
                    flex: 1;

                    .textarea_style {
                        width: 100%;
                        text-align: right;
                        // min-height: 40rpx;
                        height: 40rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 40rpx;
                        padding: 0;
                    }

                    .right-item {
                        display: flex;
                        align-items: center;

                        &:first-child {
                            margin-right: 56rpx;
                        }

                        img {
                            width: 40rpx;
                            height: 40rpx;
                            margin-right: 8rpx;
                        }

                        .right-text {
                            line-height: 40rpx;
                        }
                    }
                }

                .type {
                    display: flex;
                    justify-content: flex-end;
                }
            }

            .section-box2 {
                padding: 24rpx 28rpx;
            }
        }

        .default {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 44px;
            background: #ffffff;
            margin-top: 10px;
            margin-bottom: 20px;
            border-radius: 8px;

            .text {
                height: 20px;
                font-size: 14px;

                font-weight: 400;
                color: #333333;
                line-height: 20px;
                margin-left: 10px;
            }

            .switch {
                width: 42px;
                height: 23px;
                margin-right: 10px;
            }
        }

        .add-footer {
            width: 100%;
            height: 88rpx;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            font-size: 36rpx;
            font-weight: bold;
            color: #ffffff;
            line-height: 88rpx;
            text-align: center;
            margin-top: 32rpx;
            border-radius: 16rpx;
        }
    }
}

.headerNameList {
    position: absolute;
    top: 44px;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 270px;
    background: #ffffff;
    border-radius: 0 0 10px 10px;
    z-index: 1000;
    overflow: auto;
    opacity: 0.9;

    .headerNameItem {
        padding: 10px 15px;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
    }
}

.bold {
    font-weight: bold;
}

.no-bold {
    font-weight: 400;
}

.mask {
    width: 100%;
    height: 100vh;
    position: fixed;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
</style>
<style lang="scss">
.copy-textarea-placeholder {
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
}
</style>
