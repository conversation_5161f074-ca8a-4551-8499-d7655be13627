<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <zj-navbar :border-bottom="false" title="订单明细"></zj-navbar>

        <div class="container">
            <!-- 待付款 直接跳转收银台-->
            <!-- <order-status-await v-if="queryParams.status === 1" :order="orderDetails" /> -->

            <!-- 已取消 -->
            <order-status-canceled v-if="queryParams.status == 5" :order="orderDetails" />

            <!-- 已退货 走能源订单详情-->
            <!-- <order-status-returned v-if="queryParams.status == 9" :order="orderDetails" /> -->

            <!-- 已完成-待取货\已取货  -->
            <order-status-completed
                v-if="queryParams.status == 4 || queryParams.status == 6 || queryParams.status == 8"
                :order="orderDetails"
                :order-status="queryParams.status"
            />

            <!-- 订单信息 -->
            <order-info-item :order="orderDetails" :order-status="queryParams.status" />

            <!-- 订单评价 -->
            <div class="btn">
                <button v-if="isComment" class="btn-item comment" @click="handleComment">去评价</button>
                <button v-if="orderDetails.invoiceFlag == '0' && queryParams.status == 4" class="btn-item invoice" @click="handleInvoice"
                    >去开票</button
                >
                <button v-if="orderDetails.invoiceFlag == '1'" class="btn-item invoice" @click="seeInvoice">查看发票</button>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { detail } from '../../../../s-kit/js/v3-http/https3/o2o/index';
import { orderCommentFlag } from '../../../../s-kit/js/v3-http/https3/order/index';
// import OrderStatusAwait from '../../components/order-status-await.vue';
import OrderStatusCanceled from '../../components/order-status-canceled.vue';
import OrderStatusCompleted from '../../components/order-status-completed.vue';
import OrderStatusReturned from '../../components/order-status-returned.vue';
import OrderInfoItem from '../../components/order-info-item.vue';
import { beforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { getInvoiceByOrderNoApi } from '../../../../s-kit/js/v3-http/https3/order/index';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { OrderStatusCanceled, OrderStatusCompleted, OrderStatusReturned, OrderInfoItem },
    data() {
        return {
            orderDetails: {
                pickUpCode: '',
                consignee: '',
                consigneePhone: '',
                licensePlate: '',
                expectPickUpTime: '',
                pickUpStatus: '',
                stationCode: '',
                payConfirmationTime: '',
                orderNo: '',
                payChannel: [],
                payDiscountTotal: '',
                businessDay: '',
                discountTotalAmount: '',
                orderItems: [],
                code: '',
                o2oName: '',
                longitude: '',
                latitude: '',
                o2oBusinessStartTime: '',
                o2oBusinessEndTime: '',
                payItems: [],
                receivedTotalAmount: '',
                orderTotalAmount: '',
                invoiceFlag: null,
                createTime: '',
                actualPayTotalAmount: '',
                actualTotalRefundAmount: '',
                stationStatus: '',
            },
            queryParams: {
                // orderNo: '2406071239350005175375013', // 订单编号
                // stationCode: '1-A4301-C001-S006', // 网点编码
                // status: 8, // 状态（待支付=1，已完成-已取货=4，已取消=5，外送-待收货=6，已完成-待提货=8，已退款=9）
            },
            isComment: false, // 是否能评价
        };
    },
    onLoad(options) {
        let queryInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
        this.queryParams = { ...queryInfo };
        this.getDetails(this.queryParams);
        this.handleComment = this.$sKit.commonUtil.throttleUtil(this.handleComment);
        this.handleInvoice = this.$sKit.commonUtil.throttleUtil(this.handleInvoice);
    },

    onShow() {},
    methods: {
        /**
         * @description: 去评价
         * @return {*}
         */
        handleComment() {
            let url = '/packages/third-evaluate/pages/home/<USER>';
            this.orderDetails.orderType = '2';
            let params = {
                evaluateType: 'o2oOrder',
                isOil: '1',
                ...this.orderDetails,
            };
            if (Object.keys(params).length) {
                url = `${url}?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
            }
            this.$sKit.layer.cubeMini(url);
        },

        /**
         * @description: 去开票
         * @return {*}
         */
        async handleInvoice() {
            let apiParams = {
                orderList: [
                    {
                        stationCode: this.orderDetails.stationCode,
                        businessDay: this.orderDetails.businessDay,
                        orderNo: this.orderDetails.orderNo,
                    },
                ],
            };
            let res = await beforeMakeInvoiceCheckApi(apiParams, {
                handleErrorFn: () => {
                    // this.orderDetailPost();
                    //获取订单开票状态失败，则重新获取订单信息，刷新页面
                    this.getDetails(this.queryParams);
                },
            });
            if (res && res.success) {
                if (res.data.flag) {
                    let goods = this.orderDetails.orderItems
                        .map(goodsItem => {
                            return goodsItem.productName;
                        })
                        .join(',');
                    let url = '/packages/third-invoice/pages/invoice-form/main';
                    let params = {
                        type: 'invoice',
                        orderNoList: [this.orderDetails.orderNo],
                        checkAllAmount: this.orderDetails.actualPayTotalAmount,
                        createTime: this.orderDetails.createTime,
                        orgName: this.orderDetails.o2oName,
                        goods: goods,
                        refer: 'r32',
                    };
                    this.$sKit.layer.useRouter(url, params);
                }
            }
        },

        // 查看发票
        async seeInvoice() {
            let params = {
                orderNum: this.orderDetails.orderNo,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
        /**
         * @description: 查询O2O订单详情
         * @return {*}
         */
        async getDetails(params) {
            let res = await detail(params);
            if (res && res.success) {
                // 截取时分，去掉秒
                if (res.data.o2oBusinessStartTime && res.data.o2oBusinessEndTime) {
                    res.data.o2oBusinessStartTime = res.data.o2oBusinessStartTime.slice(0, 5);
                    res.data.o2oBusinessEndTime = res.data.o2oBusinessEndTime.slice(0, 5);
                }

                // @ 解决后端老接口 orderSubType 未返回值问题
                if (!res.data.orderSubType) {
                    res.data.orderSubType = 23;
                }

                this.orderDetails = res.data;

                // @ 只有已完成订单才能评价 待备货、待提货、已取消订单不能评价
                if (this.queryParams.status && this.queryParams.status == 4) {
                    this.getOrderCommentQueryApi(res.data);
                }
            }
        },

        /**
         * @description: 查询订单评价状态
         * @param {*} data
         * @return {*}
         */
        async getOrderCommentQueryApi(data) {
            let params = {
                orderNo: this.queryParams.orderNo, // 订单编号
                stationCode: this.queryParams.stationCode, // 油站编码
                payAmount: data.actualPayTotalAmount, // 支付金额
                orderType: 2, // 订单类型
                orderSubType: data.orderSubType || 23, // 订单子类型
                createTime: data.createTime, // 订单创建时间(yyyy-MM-dd HH:mm:ss)
            };
            let res = await orderCommentFlag(params);
            if (res && res.success) {
                this.isComment = res.data.commentFlag == 1 ? true : false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.pageView {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f7fb;
}
.container {
    padding: 10px 15px;
}
.btn {
    margin: 20px 0;
    display: flex;
    justify-content: flex-end;

    &-item {
        // flex: 1;
        width: 50%;
        height: 45px;
        font-size: 16px;
        font-weight: 400;
        line-height: 45px;
        border-radius: 10px;
    }

    button {
        margin: 0;
    }

    .comment {
        color: #e64f22;
        border: 1px solid #e64f22;
        background-color: #ffffff;
    }

    .invoice {
        color: #ffffff;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }

    /* 当有两个按钮时，给第一个按钮添加右边距 */
    .btn-item:first-child:nth-last-child(2) {
        margin-right: 12px;
    }
}
</style>
