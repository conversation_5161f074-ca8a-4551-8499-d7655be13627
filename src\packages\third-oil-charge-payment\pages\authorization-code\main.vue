<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="page p-hw bg-F2F3F5 ov-y-s">
            <div class="page-wrap fl-column">
                <!-- 导航栏 -->
                <zj-navbar :height="44" title="e享加油" :border-bottom="false"></zj-navbar>
                <div class="margin-12-l-r card-default margin-top-12">
                    <!-- 授权码 -->
                    <div class="code-div">
                        <div class="fl-row fl-al-cen fl-jus-bet">
                            <div class="fl-row fl-al-cen">
                                <img src="../../image/icon-logo-24.png" alt class="icon-24" />
                                <div class="font-16 color-000 weight-bold mar-6-l">{{ details.stationName }}</div>
                            </div>
                            <div
                                class="color-FF6B2C font-12 weight-400 te-center bg-F5F5F5 wid-72"
                                @click="cancleClick"
                                v-if="preAuthOrderInfo.preOrderStatus == 1"
                                >取消订单</div
                            >
                        </div>
                        <!-- 预授权订单状态1创建 -->
                        <div v-if="preAuthOrderInfo.preOrderStatus == 1">
                            <div class="fl-row fl-al-jus-cen mart17">
                                <div class="font-16 weight-400 color-666">请在加油机上输入授权码</div>
                            </div>
                            <div class="spacing color-FF6B2C font-40 mart24 te-center font-style marl10">{{
                                refuelCode ? refuelCode : ''
                            }}</div>
                            <div class="fl-row fl-al-jus-cen mart24">
                                <div class="font-15 weight-400 color-999">油品号：{{ $sKit.layer.getOilNum(details.productName) }}</div>
                                <div class="bor-1"></div>
                                <div class="font-15 weight-400 color-999">加油金额： {{ preAuthOrderInfo.preAuthzAmount }} 元 </div>
                            </div>
                        </div>
                        <!-- 3开始加油 -->
                        <div class="fl-column fl-al-jus-cen" v-else-if="preAuthOrderInfo.preOrderStatus == 3">
                            <loading></loading>
                            <div class="frozen-div te-center">
                                <div class="font-14 color-333 weight-bold">本次加油已冻结{{ preAuthOrderInfo.preAuthzAmount }}元 </div>
                                <div class="font-12 color-999">加油完成按实际加油金额扣款加油结束后</div>
                                <div class="font-12 color-999">请核对加油信息</div>
                            </div>
                            <div class="fl-row fl-al-jus-cen mart24">
                                <div class="font-15 weight-400 color-999">油品号：{{ $sKit.layer.getOilNum(details.productName) }}</div>
                                <div class="bor-1"></div>
                                <div class="font-15 weight-400 color-999">加油金额： {{ preAuthOrderInfo.preAuthzAmount }} 元 </div>
                            </div>
                        </div>
                        <div class="process-guidelines-div">
                            <div class="process-guidelines-div-title">
                                <span class="title-bg">加油操作流程指引</span>
                            </div>
                            <div class="type-div">
                                <div class="type-div-title">
                                    <img class="title-img" src="../../image/self-service.png" alt="" />
                                    <span>自助加油</span>
                                </div>
                                <div class="type-div-content">
                                    如您去往的加油站是自助加油站，您可以在加油机键盘中按<img
                                        class="content-img"
                                        src="../../image/up-content.png"
                                    />键后，输入授权码，点击确认，看到加油机屏幕上显示验证通过并展示您的授权金额，即可提枪加油。挂枪后，系统会按您的实际加油金额进行结算(不会超过您的授权上限)。
                                </div>
                            </div>
                            <div class="type-div">
                                <div class="type-div-title">
                                    <img class="title-img" src="../../image/non-self-service.png" alt="" />
                                    <span>非自助加油</span>
                                </div>
                                <div class="type-div-content">
                                    将授权码出示给加油员，加油员即可为您完成加油，挂枪后，系统会按您的实际加油金额进行结算(不会超过您的授权上限)。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- #ifdef MP-WEIXIN || MP-MPAAS || H5-->
                <div class="margin-12-l-r margin-top-12" v-if="codeImg">
                    <addAnagerBox @getCodeImg="getCodeImg" :orgCode="details.stationCode" type="home"></addAnagerBox>
                </div>
                <!-- #endif -->
                <!-- #ifdef MP-MPAAS || H5-->
                <div v-if="intMarketText" class="margin-12-l-r margin-top-12">
                    <intMarket
                        :pageID="intMarketPageID"
                        :stationCode="details.stationCode"
                        @getIntMarketText="getIntMarketText"
                    ></intMarket>
                </div>
                <!-- #endif -->
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import loading from '../../components/loading/loading.vue';
// import myGoodsWaterfall from '../../components/goods-waterfall'
import {
    getRefuelCodeApi,
    preAuthCancel,
    preAuthOrderApi,
    getPreAuthOrder,
} from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { orderCommentFlag } from '../../../../s-kit/js/v3-http/https3/order/index.js';
import { beforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import addAnagerBox from '@/s-kit/components/layout/add-anager-box/index.vue';
import intMarket from '@/s-kit/components/layout/int-market/main.vue';
const currency = require('currency.js');

// import UniPopup from '../../../../components/uni-popup/uni-popup.vue';
// 导航高度
const NAV_HEIGHT = 44;
import { mapState } from 'vuex';
//计时器
let timer = null;
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        addAnagerBox,
        intMarket,
        loading,
    },
    data() {
        return {
            navH: NAV_HEIGHT, // 导航高度
            // 订单信息数据
            details: null,
            // e享加油码
            refuelCode: '',
            requestStatusInterval: null,
            pollTime: 10000,
            preAuthOrderInfo: {},
            requestCodeInterval: null,
            beginTime: '',
            orderCommentFlag: 0,
            // 控制预约加油重复
            evaluationControlFlag: true,
            oilServicePageFlag: true,
            codeImg: true,
            intMarketText: false,
            intMarketPageID: '',
        };
    },
    created() {},
    onLoad(options) {
        console.log('成功', options);
        if (this.requestStatusInterval) clearTimeout(this.requestCodeInterval);
        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
        this.details = JSON.parse(decodeURIComponent(options.data));
        // 查询预授权订单详情
        this.getPreAuthorizationDetails();
        this.beginTime = new Date().getTime();
        this.$sKit.mpBP.tracker('e享加油', {
            seed: 'exoilBiz',
            pageID: 'codePage',
            refer: this.details.refer,
            channelID: clientCode,
            address: this.cityName,
        });
        // #ifdef MP-MPAAS
        this.$cnpcBridge.getSwitch('NonOilActivityRecommend', res => {
            if (res == 'yes') {
                this.intMarketText = true;
            } else {
                this.intMarketText = false;
            }
        });
        // #endif
    },
    // onUnload() {
    //     clearTimeout(this.requestCodeInterval);
    //     clearTimeout(this.requestStatusInterval);
    // },
    mounted() {},
    methods: {
        // 获取订单去评价状态
        /**
         * 订单子类型：
            11—预约加油；
            13—线上后支付加油(e站加油订单)；
            23—O2O订单；
            26—异业订单(能源商城订单)；
         */
        // async getOrderCommentFlag() {
        //     let params = {
        //         orderNo: this.preAuthOrderInfo.orderNo,
        //         stationCode: this.details.stationCode,
        //         payAmount: this.preAuthOrderInfo.actualPayAmount,
        //         orderType: 2,
        //         orderSubType: '11',
        //         createTime: this.preAuthOrderInfo.createTime,
        //     };
        //     let res = await orderCommentFlag(params);
        //     if (res.success) {
        //         this.orderCommentFlag = res.data.commentFlag;
        //     }
        // },
        // 去评价
        // openComment() {
        //     let url = '/packages/third-evaluate/pages/home/<USER>';
        //     let type = 'redirectTo';
        //     this.preAuthOrderInfo.orderSubType = '11';
        //     this.preAuthOrderInfo.orderType = '2';
        //     let params = {
        //         evaluateType: 'order',
        //         ...this.preAuthOrderInfo,
        //         isOil: '1',
        //     };
        //     this.$sKit.layer.useRouter(url, params, type);
        // },
        getIntMarketText(intMarketText) {
            this.intMarketText = intMarketText;
        },
        getCodeImg(codeImg) {
            this.codeImg = codeImg;
        },
        // 获取加油预约码
        async getRefuelCode() {
            clearTimeout(this.requestCodeInterval);
            let params = {
                preAuthzOrderNo: this.details.preAuthzOrderNo,
                stationCode: this.details.stationCode,
            };
            let res = await getRefuelCodeApi(params, { isCustomErr: true });
            if (res.success) {
                uni.hideLoading();
                this.refuelCode = res.data.refuelCode;
                // 预授权订单状态1创建；5已取消；3开始加油；4加油结束；
                if (this.refuelCode) {
                    this.$store.dispatch('getAccountBalanceAction');
                    clearTimeout(this.requestCodeInterval);
                    // clearTimeout(this.requestStatusInterval);
                }
                // else {
                //   clearInterval(this.requestCodeInterval)
                //   // clearTimeout(this.requestStatusInterval);
                // }
            } else {
                // uni.hideLoading();
                // 每隔5秒查一次
                this.requestCodeInterval = setTimeout(() => {
                    this.getRefuelCode();
                }, 5000);
                if (new Date().getTime() - this.beginTime > 25 * 1000) {
                    // wx.hideLoading()
                    clearTimeout(this.requestCodeInterval);
                    clearTimeout(this.requestStatusInterval);
                    this.$store.dispatch('zjShowModal', {
                        title: '获取订单失败，请重新预约！',
                        confirmText: '确认',
                        cancelText: '',
                        confirmColor: '#333',
                        success: res => {
                            if (res.confirm) {
                                //
                                this.cancelAction();
                                console.log('用户点击确认');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                    return;
                }
            }
        },
        // 查询预授权订单详情
        getPreAuthorizationDetails() {
            let params = {
                preAuthzOrderNo: this.details.preAuthzOrderNo,
                stationCode: this.details.stationCode,
            };
            getPreAuthOrder(params, { isload: false }).then(
                async res => {
                    if (res.data) {
                        console.log('getPreAuthOrder----res.data', res.data);
                        this.preAuthOrderInfo = res.data;

                        console.log(this.evaluationControlFlag, 'this.evaluationControlFlag');
                        // 防止出现两次订单评价页面
                        if (this.evaluationControlFlag) {
                            // 预授权订单状态1创建；5已取消；3开始加油；4加油结束；
                            if (this.preAuthOrderInfo.preOrderStatus == 1 || this.preAuthOrderInfo.preOrderStatus == 3) {
                                if (this.preAuthOrderInfo.preOrderStatus == 1) {
                                    this.intMarketPageID = 'ecodePage';
                                } else if (this.preAuthOrderInfo.preOrderStatus == 3) {
                                    this.intMarketPageID = 'oilServicePage';
                                }
                                if (this.oilServicePageFlag && this.preAuthOrderInfo.preOrderStatus == 3) {
                                    this.$sKit.mpBP.tracker('e享加油', {
                                        seed: 'exoilBiz',
                                        pageID: 'oilServicePage',
                                        refer: this.details.refer,
                                        channelID: clientCode,
                                        djAmount: this.preAuthOrderInfo.preAuthzAmount,
                                        address: this.cityName,
                                    });
                                    this.oilServicePageFlag = false;
                                }
                                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                                this.requestStatusInterval = setTimeout(() => {
                                    this.getPreAuthorizationDetails();
                                }, this.pollTime);
                            } else if (this.preAuthOrderInfo.preOrderStatus == 5) {
                                this.backpage();
                            } else if (this.preAuthOrderInfo.preOrderStatus == 4) {
                                this.$store.commit('setSelectedCoupon', {});
                                // #ifdef MP-MPAAS
                                this.$cnpcBridge.removeValueToNative('Define_Selected_Coupon');
                                // #endif
                                // #ifndef MP-MPAAS
                                uni.removeStorageSync('setSelectCoupon');
                                // #endif
                                this.evaluationControlFlag = false;
                                // this.$store.commit('mSetCircularOrder',false)
                                // this.getOrderCommentFlag();
                                //加油结束，获取钱包明细
                                this.$store.dispatch('getAccountBalanceAction');
                                // 获取能源币，油卡，电子券，积分和余额，七日内余额
                                this.$store.dispatch('basicCouponAction');
                                // this.$store.commit('mSetCircularOrder', false)
                                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                                let URL = `/packages/third-oil-charge-payment/pages/oil-charge-pay-result/main`;
                                let params = { ...this.preAuthOrderInfo, refer: this.details.refer };
                                let type = 'redirectTo';
                                this.$sKit.layer.useRouter(URL, params, type);
                            }
                            if (!this.refuelCode) {
                                this.getRefuelCode();
                            }
                        }
                    }
                },
                error => {
                    if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                    this.requestStatusInterval = setTimeout(() => {
                        this.getPreAuthorizationDetails();
                    }, this.pollTime);
                },
            );
        },
        // 取消加油接口
        async cancelAction() {
            let params = {
                preAuthOrderNo: this.details.preAuthzOrderNo,
                stationCode: this.details.stationCode,
            };
            let res = await preAuthCancel(params);
            //
            if (res.success) {
                this.$store.dispatch('getAccountBalanceAction');
                // this.$store.commit('mSetInfoAuthOrder', {})
                // this.$store.commit('mSetCircularOrder', false);
                clearTimeout(this.requestStatusInterval);
                clearTimeout(this.requestCodeInterval);
                this.backpage();
                uni.hideLoading();
            }
        },
        // 取消加油操作
        cancleClick() {
            this.$store.dispatch('zjShowModal', {
                title: '您是否取消订单？',
                confirmText: '继续加油',
                cancelText: '取消订单',
                cancelColor: '#666666',
                confirmColor: '#FF6B2C',
                success: async res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        uni.hideLoading();
                    } else if (res.cancel) {
                        this.cancelAction();
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 打开引导加油流程
        guidClick() {
            uni.hideLoading();
            this.$sKit.layer.useRouter('/packages/third-oil-charge-payment/pages/guid/main', '', 'navigateTo');
        },
        // 去开票
        // async openInvoice() {
        //     let apiParams = {
        //         orderList: [
        //             {
        //                 stationCode: this.preAuthOrderInfo.stationCode,
        //                 businessDay: this.preAuthOrderInfo.businessDay,
        //                 orderNo: this.preAuthOrderInfo.orderNo,
        //             },
        //         ],
        //     };
        //     let res = await beforeMakeInvoiceCheckApi(apiParams);
        //     if (res && res.success) {
        //         if (res.data.flag) {
        //             let params = {
        //                 orderNoList: [this.preAuthOrderInfo.orderNo],
        //                 checkAllAmount: this.preAuthOrderInfo.actualPayAmount,
        //                 type: 'invoice',
        //                 createTime: this.preAuthOrderInfo.createTime,
        //                 orgName: this.preAuthOrderInfo.stationName,
        //                 goods: this.preAuthOrderInfo.productName,
        //             };
        //             uni.hideLoading();
        //             this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params);
        //         }
        //     }
        // },
        // 返回上一页
        backpage() {
            clearTimeout(this.requestStatusInterval);
            clearTimeout(this.requestCodeInterval);
            uni.hideLoading();
            // setTimeout(() => {
            const pages = getCurrentPages();
            console.log(pages.length, 'pages.length ');
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                console.log('22222');
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                console.log('11111');

                uni.navigateBack({});
            }
            uni.hideLoading();
            // }, 200);
        },
    },

    computed: {
        ...mapState({
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            cityName: state => state.locationV3_app.cityName,
        }),
    },
    beforeDestroy() {
        clearTimeout(this.requestStatusInterval);
        clearTimeout(this.requestCodeInterval);
        uni.$emit('refreshReserveOrder', { data: '' });
    },
    destroyed() {
        uni.hideLoading();
        clearTimeout(this.requestStatusInterval);
        clearTimeout(this.requestCodeInterval);
    },
};
</script>
<style scoped lang="scss">
.page {
    position: relative;

    .page-wrap {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 10;
    }
}

.margin-12-l-r {
    margin: 0 16px;
}

.margin-top-12 {
    margin-top: 12px;
}

.marb8 {
    margin-bottom: 8px;
}

.navbar-title-wrap {
    .navi-input-view {
        width: 318px;
        height: 36px;
        background: #f4f4f4;
        border-radius: 18px;
        line-height: 36px;
        padding: 0 13px;
    }

    .search-icon {
        width: 18px;
        height: 18px;
    }
}

.icon-75 {
    width: 75px;
    height: 57px;
}

.wid-72 {
    width: 72px;
    height: 25px;
    line-height: 25px;
    border-radius: 200px;
}

.icon-24 {
    width: 24px;
    height: 24px;
}

.icon-12 {
    width: 16px;
    height: 16px;
    margin-top: 4px;
}

.box-30 {
    margin-top: 31px;
    margin-bottom: 20px;
}

.box-table {
    margin-top: 16px;
    padding-top: 16px;
}

.btn {
    width: 67px;
    height: 26px;
    border-radius: 4px;
    line-height: 26px;
    text-align: center;
}

.cell-div {
    margin-top: 12px;
}

.mar-6-l {
    margin-left: 6px;
}

.mart21 {
    margin-top: 21px;
}

.mart20 {
    margin-top: 20px;
}

.mart17 {
    margin-top: 17px;
}

.mart24 {
    margin-top: 24px;
}

.marl18 {
    margin-left: 18px;
}

.marl10 {
    margin-left: 10px;
}

.bor-1 {
    position: relative;
    margin: 0 14px;

    &.bor-1::after {
        content: '';
        position: absolute;
        left: 0;
        top: -7px;
        border-right: 1px solid #999;
        width: 1px;
        height: 15px;
    }
}

.code-div {
    padding: 17px 14px 16px;
}

.spacing {
    letter-spacing: 27px;
}

.frozen-div {
    line-height: 1.8;
}

.process-guidelines-div {
    margin-top: 31px;
    .process-guidelines-div-title {
        padding-top: 15px;
        text-align: center;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 23px;
        text-align: center;
        font-style: normal;
        border-top: 1px dashed #dddddd;
        .title-bg {
            display: inline-block;
            height: 24px;
            background: linear-gradient(to bottom, #ffffff 50%, #ffeae9 50%);
        }
    }
    .type-div {
        .type-div-title {
            display: flex;
            align-items: center;
            margin-top: 12px;
            margin-bottom: 9px;
            .title-img {
                width: 19px;
                height: 18px;
                margin-right: 5px;
            }
            span {
                font-weight: 500;
                font-size: 14px;
                color: #ff4f13;
                text-align: left;
                font-style: normal;
            }
        }
        .type-div-content {
            font-weight: 400;
            font-size: 13px;
            color: #666666;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .content-img {
                width: 10px;
                height: 15px;
                margin: 0 5px;
            }
        }
    }
}
</style>
