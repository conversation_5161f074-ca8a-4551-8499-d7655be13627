<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw unfomrs">
            <img class="bg-image" src="../../image/bg-car.png" alt />
            <div class="page-wrap">
                <zj-navbar
                    :border-bottom="false"
                    :background="{
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                    }"
                    back-icon-color="#ffffff"
                    title-color="#ffffff"
                    :title="saveType == 1 ? '添加车辆' : '编辑车辆'"
                ></zj-navbar>
                <div class="wrap-car">
                    <div class="bg-car-content">
                        <div class="bg-car-title">Hello,</div>
                        <div class="bg-car-text">添加车辆，加油更便捷</div>
                    </div>
                    <div class="unfomrs-content">
                        <div class="car-number">
                            <div class="license-box">
                                <div class="license-title">
                                    <div class="license-title red">*</div>
                                    <div class="license-title name">车牌号码</div>
                                </div>
                                <!-- 选择车牌 -->
                                <!-- 车辆类型 (0：普通车 1：新能源车) -->
                                <div class="plate-input" v-if="saveType == 1">
                                    <block v-for="(item, index) in licensePlateArr" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateIndex ? 'plate-input-item plate-input-item-select' : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',
                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <div v-if="index === 7 && item === ''" :style="{ writingMode: 'tb-rl', fontSize: '20rpx' }"
                                                >新能源</div
                                            >
                                            <div v-else>{{ item }}</div>
                                        </div>
                                    </block>
                                </div>
                                <div class="plate-input" v-else>
                                    <block v-for="(item, index) in licensePlateArrEdit" :key="index">
                                        <div
                                            :class="
                                                index == selectPlateEditIndex
                                                    ? 'plate-input-item plate-input-item-select'
                                                    : 'plate-input-item'
                                            "
                                            :style="{
                                                backgroundColor: index == 7 ? '#f5f5f5' : null,
                                                border: index == 7 ? '0.5px dashed #bcbcbc' : null,
                                                color: index == 7 ? '#969696' : '#3A3A3A',

                                                dispaly: index == 7 ? 'flex' : null,
                                                fontWeight: index == 7 ? '500' : '400',
                                            }"
                                            @click="clickPlateInput(index)"
                                        >
                                            <div v-if="index === 7 && item === ''" :style="{ writingMode: 'tb-rl', fontSize: '20rpx' }"
                                                >新能源</div
                                            >
                                            <div v-else>{{ item }}</div>
                                        </div>
                                    </block>
                                </div>
                            </div>

                            <div class="substance-box">
                                <div class="basic-box">
                                    <div class="substance-tip substanceWidth">
                                        <div v-if="vehicleType != 1" class="substance-tip red">*</div>
                                        <div class="substance-tip number">油品品号</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <picker @change="bindPickerChange" :value="oilNumSelect" :range="oilNumArr" range-key="fuelName">
                                            <div class="flex">
                                                <div class="picker-content" :class="{ placeholder: oilNumSelect < 0 }">
                                                    {{ oilNumSelect >= 0 ? oilNumArr[oilNumSelect].fuelName : '请选择油品品号' }}
                                                </div>
                                                <u-icon class="uicon" :name="olisShow ? 'arrow-down' : 'arrow-right'"></u-icon>
                                            </div>
                                        </picker>
                                    </div>
                                </div>
                                <div class="basic-box">
                                    <div class="basic-box">
                                        <div class="substance-tip substanceWidth">
                                            <div v-if="vehicleType != 1" class="substance-tip red">*</div>
                                            <div class="substance-tip number">车牌颜色</div>
                                        </div>
                                        <div>
                                            <div class="substance-tip-right" v-if="saveType == 1">
                                                <picker
                                                    @change="bindPickerChangePlate"
                                                    :value="licensePlateVal"
                                                    :range="licenseArr"
                                                    range-key="describle"
                                                >
                                                    <div class="flex">
                                                        <div class="picker-content" :class="{ placeholder: licensePlateVal < 0 }">
                                                            {{
                                                                licensePlateVal >= 0
                                                                    ? licenseArr[licensePlateVal].describle
                                                                    : '请选择车牌颜色'
                                                            }}
                                                        </div>
                                                        <!-- icon图标 -->
                                                        <u-icon
                                                            class="uicon"
                                                            :name="licensePlateShow ? 'arrow-down' : 'arrow-right'"
                                                        ></u-icon>
                                                    </div>
                                                </picker>
                                            </div>
                                            <div class="substance-tip-right" v-else>
                                                <picker
                                                    @change="bindPickerChangePlate"
                                                    :value="licensePlateValEdit"
                                                    :range="licenseArr"
                                                    range-key="describle"
                                                >
                                                    <div class="flex">
                                                        <div class="picker-content" :class="{ placeholder: licensePlateValEdit < 0 }">{{
                                                            licensePlateValEdit >= 0
                                                                ? licenseArr[licensePlateValEdit].describle
                                                                : '请选择车牌颜色'
                                                        }}</div>
                                                        <u-icon
                                                            class="uicon"
                                                            :name="licensePlateShow ? 'arrow-down' : 'arrow-right'"
                                                        ></u-icon>
                                                    </div>
                                                </picker>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="basic-box">
                                    <div class="substance-tip substanceWidth">
                                        <div v-if="vehicleType != 1" class="substance-tip red">*</div>
                                        <div class="substance-tip number">单次最大金额</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <input
                                            type="digit"
                                            class="substance-right input"
                                            v-model="refuelAmountMax"
                                            placeholder="请输入(单位：元)"
                                            @input="otherAmountsInput"
                                        />
                                    </div>
                                </div>
                                <!-- <div class="basic-box">
                                    <div class="substance-tip substanceWidth">
                                        <div class="substance-tip number">车辆类型</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <picker
                                            @change="bindVehicleType"
                                            :value="vehicleType"
                                            :range="vehicleTypeArray"
                                            range-key="describle"
                                        >
                                            <div class="flex">
                                                <div class="picker-content substance-right" :class="{ placeholder: vehicleType < 0 }">
                                                    {{ vehicleType >= 0 ? vehicleTypeArray[vehicleType].describle : '车辆类型' }}
                                                </div>
                                                <u-icon class="uicon" :name="vehicleTypeIsShow ? 'arrow-down' : 'arrow-right'"></u-icon>
                                            </div>
                                        </picker>
                                    </div>
                                </div>

                                <div class="basic-box" @click="bindThreeLevelLinkageFun">
                                    <div class="substance-tip substanceWidth">
                                        <div class="substance-tip number">车辆品牌/型号/年份</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <div class="flex">
                                            <div class="picker-content substance-right" :class="{ placeholder: vehicleModelYearFlag }">
                                                {{ selectBrandModelYearStr || '请选择车辆品牌/型号/年份' }}
                                            </div>
                                            <u-icon class="uicon" :name="vehicleModelYearFlag ? 'arrow-down' : 'arrow-right'"></u-icon>
                                        </div>
                                    </div>
                                </div>
                                <div class="basic-box">
                                    <div class="substance-tip substanceWidth">
                                        <div class="substance-tip number">车辆外盖朝向</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <picker
                                            @change="bindVehicleOuterCoverOrientation"
                                            :value="vehicleOuterCoverOrientation"
                                            :range="vehicleOuterCoverOrientationArray"
                                            range-key="name"
                                        >
                                            <div class="flex">
                                                <div
                                                    class="picker-content substance-right"
                                                    :class="{ placeholder: vehicleOuterCoverOrientation < 0 }"
                                                >
                                                    {{
                                                        vehicleOuterCoverOrientation >= 0
                                                            ? vehicleOuterCoverOrientationArray[vehicleOuterCoverOrientation].name
                                                            : '车辆外盖朝向'
                                                    }}
                                                </div>
                                                <u-icon
                                                    class="uicon"
                                                    :name="vehicleOuterCoverOrientationIsShow ? 'arrow-down' : 'arrow-right'"
                                                ></u-icon>
                                            </div>
                                        </picker>
                                    </div>
                                </div>
                                <div class="basic-box">
                                    <div class="substance-tip substanceWidth">
                                        <div class="substance-tip number">油箱外盖打开方式</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <picker
                                            @change="bindOpeningMethodOfFuelTankCover"
                                            :value="fuelTankCoverSelect"
                                            :range="openingMethodOfFuelTankCover"
                                            range-key="name"
                                        >
                                            <div class="flex">
                                                <div
                                                    class="picker-content substance-right"
                                                    :class="{ placeholder: fuelTankCoverSelect < 0 }"
                                                >
                                                    {{
                                                        fuelTankCoverSelect >= 0
                                                            ? openingMethodOfFuelTankCover[fuelTankCoverSelect].name
                                                            : '油箱外盖打开方式'
                                                    }}
                                                </div>
                                                <u-icon
                                                    class="uicon"
                                                    :name="openingMethodOfFuelTankCoverIsShow ? 'arrow-down' : 'arrow-right'"
                                                ></u-icon>
                                            </div>
                                        </picker>
                                    </div>
                                </div>
                                <div class="basic-box" @click="bindOpeningAngleOfFuelTankInnerCover">
                                    <div class="substance-tip substanceWidth">
                                        <div class="substance-tip number">油箱内盖拧开角度</div>
                                    </div>

                                    <div class="substance-tip-right">
                                        <div class="substance-right">{{ angleStrSelect || angleStr }}</div>
                                        <u-icon class="uicon" :name="fuelTankOpeningAngle ? 'arrow-down' : 'arrow-right'"></u-icon>
                                        <uni-popup type="bottom" ref="popup" :mask-click="false">
                                            <div class="fl-column fl-jus-bet pop_div">
                                                <div class="fl-row fl-jus-bet padding12">
                                                    <div @click="innerCoverAngleCancle">取消</div>
                                                    <div @click="innerCoverAngleConfirm">确认</div>
                                                </div>
                                                <div class="dete-content-wrap fl-row fl-wrap">
                                                    <div
                                                        class="dete-content gap23"
                                                        v-for="(item, index) in openAngle"
                                                        :key="index"
                                                        @click="fuelTankOpeningAngleListItemFun(item, index)"
                                                    >
                                                        <div :class="{ isActive: screenActive == index }" class="dete-item"
                                                            >{{ item.name }}
                                                        </div>
                                                    </div>
                                                    <div class="dete-content gap23">
                                                        <input
                                                            @focus="handleFocus"
                                                            type="digit"
                                                            class="te-center angleStrInput"
                                                            v-model="angleStr"
                                                            :class="{ isActive: otherActive }"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </uni-popup>
                                    </div>
                                </div> -->
                            </div>
                            <div class="substance-box spe">
                                <!-- <div class="basic-box">
                <div class="substance-tip">
                  <div class="substance-tip number">开通车牌付</div>
                </div>
                <div class="substance-tip-right">
                  <switch color="#e64f22" @change="licensePlatePayment" style="transform:scale(0.8)" />
                </div>
              </div>
              <div class="basic-box">
                <div class="substance-tip">
                  <div class="substance-tip number">该车辆加油消费自动匹配优惠规则</div>
                </div>
                <div class="substance-tip-right">
                  <switch @change="preferential" color="#e64f22" style="transform:scale(0.8)"></switch>
                </div>
              </div> -->
                                <div class="basic-box">
                                    <div class="substance-tip">
                                        <div class="substance-tip number">设为默认车辆</div>
                                    </div>
                                    <div class="substance-tip-right">
                                        <img
                                            class="img_style"
                                            src="../../image/switch_on.png"
                                            v-if="defaultVehicleFlag"
                                            @click="defaultVehicle"
                                        />
                                        <img class="img_style" src="../../image/switch_off.png" v-else @click="defaultVehicle" />
                                        <!-- <switch @change="defaultVehicle" color="#e64f22" style="transform:scale(0.8)"></switch> -->
                                    </div>
                                </div>
                            </div>
                            <div class="basic-box-label">
                                <div class="substance-tip" v-if="!charge">
                                    <div class="substance-tip number">加油时系统为您自动匹配最优优惠</div>
                                </div>
                            </div>
                            <div class="addCar primary-btn border-rad-8 shad-ef color-fff" v-if="saveType === 1" @click="save">
                                <span class="save">保存</span>
                            </div>
                            <div class="btnWrap" v-else>
                                <div class="preservation" @click="deleteVehicle">删除</div>
                                <div class="delete" @click="save">保存</div>
                            </div>
                        </div>
                    </div>
                </div>
                <CITY
                    v-if="brandModelYear.length > 0"
                    :show="vehicleModelYearFlag"
                    @selectBrandModelYear="selectBrandModelYear"
                    :provinceCityArray="brandModelYear"
                    @hideShow="hideShow"
                ></CITY>
            </div>
            <!-- 键盘 -->
            <customKeyboard
                ref="uKeyboard"
                zIndex="10"
                @backspace="carKeyDelect"
                @change="carKeyChange"
                mode="car"
                v-model="isKeyboard"
                :mask="false"
            ></customKeyboard>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import customKeyboard from '../../components/custom-keyboard/custom-keyboard.vue';
import CITY from '../../components/cc-selectCity/cc-selectCity.vue';
import {
    plateEnumList,
    plateFuelInfo,
    plateDetail,
    plateDelete,
    plateModifyOrCreate,
    queryDictItemListByCode,
    queryBrands,
} from '../../../../s-kit/js/v3-http/https3/vehicle/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { customKeyboard, CITY },
    name: 'myVehicleDetail',
    data() {
        return {
            // 新增后车牌号数组
            licensePlateArr: ['', '', '', '', '', '', '', ''],
            // 编辑后的车牌号数组
            licensePlateArrEdit: ['', '', '', '', '', '', '', ''],
            // 正在输入新增后的车牌号位置 -1时关闭键盘
            selectPlateIndex: -1,
            // 正在输入编辑后的车牌号位置 -1时关闭键盘
            selectPlateEditIndex: -1,
            // 是否显示键盘
            isKeyboard: false,
            //是否是第一次调起键盘 用于判断退格
            isFirst: false,
            //是否开通车牌付
            noFeelPayFlag: false,
            //是否该车辆加油消费自动匹配优惠规则
            autoDiscountFlag: true,
            //是否单次最大金额
            refuelAmountMax: '',
            //车辆类型 (0：普通车 1：新能源车)
            vehicleType: 0,
            // 油品品号数据
            oilNumArr: [],
            // 选中油品数据
            oilNumSelect: '-1',
            //车牌颜色数据
            licenseArr: [],
            // 车辆类型数据
            vehicleTypeArr: [],
            //选择新增的车牌颜色数据
            licensePlateVal: '-1',
            //选择编辑后的车牌颜色数据
            licensePlateValEdit: '-1',
            // licensePlateValEditIndex:'-1',
            // 用来区分添加还是编辑
            saveType: 1,
            //接口请求完页面数据加载
            isInit: false,
            // 列表页面传递过来的车辆信息
            plateInfo: {},
            // 设置为默认车辆
            defaultVehicleFlag: false,
            // 判断是否从o2o小程序跳转来
            pageType: '',
            vehicleType: '-1',
            openAngle: [],
            openingMethodOfFuelTankCover: [],
            vehicleTypeArray: [],
            vehicleOuterCoverOrientationArray: [],
            // 车辆外盖朝向
            vehicleOuterCoverOrientation: '-1',
            // 选择油箱外盖打开方式
            fuelTankCoverSelect: '-1',
            // 油箱内盖拧开角度打开弹窗标识
            fuelTankOpeningAngle: false,
            // 输入的拧开角度
            angleStr: '',
            // 选择的拧开角度
            angleStrSelect: '',
            angleStrSelectCode: '',
            screenActive: -1,
            brandModelYear: [],
            // 选择其他的时候进行高亮
            otherActive: false,
            // 车辆品牌/型号/年份选择标识
            vehicleModelYearFlag: false,
            // 车辆品牌/型号/年份选择后的字符串
            selectBrandModelYearStr: '',
            selectBrandModelYearDetail: {},
            // 充电小程序标识
            charge: '',
        };
    },
    computed: {},
    watch: {
        // 监听键盘
        isKeyboard: {
            handler(newName, oldName) {
                if (newName === false) {
                    if (this.saveType == 1) {
                        this.selectPlateIndex = -1;
                    } else {
                        this.selectPlateEditIndex = -1;
                    }
                }
            },
            immediate: true,
        },
    },
    async onLoad(options) {
        // 车辆列表跳转携带过来的具体车辆数据
        let plateItem = JSON.parse(decodeURIComponent(options.data));
        this.charge = plateItem?.source;
        console.log(plateItem, plateItem.source, plateItem.pageType, '车辆信息11111');
        if (plateItem.items) {
            console.log(plateItem, '车辆信息');
            // 车辆列表跳转携带过来的具体车辆数据
            this.plateInfo = plateItem.items;
        }
        if (plateItem.pageType) {
            this.pageType = plateItem.pageType;
        }
        // type 区分添加与编辑事件
        this.saveType = plateItem.type || 1;
        console.log('区分添加还是编辑', this.saveType);
        // 油品信息和车辆颜色
        await this.obtainOtherVehicleInformation();
        await this.oilInformationAndVehicleColor();
        await this.getQueryBrands();
    },
    mounted() {},
    methods: {
        // 关闭车辆品牌型号年份
        hideShow({ detail }) {
            console.log(detail, 'detail');
            this.vehicleModelYearFlag = false;
        },
        // 车辆品牌型号年份确认
        selectBrandModelYear({ detail }) {
            console.log(detail, '车辆品牌型号年份确认');
            this.selectBrandModelYearDetail = detail;
            this.selectBrandModelYearStr = `${detail.province}/${detail.city}/${detail.area}`;
            this.vehicleModelYearFlag = false;
        },
        // 打开车辆品牌型号年份
        bindThreeLevelLinkageFun() {
            this.vehicleModelYearFlag = true;
        },
        /**
         * @description  : 获取车辆颜色
         * @param        {Number} VEHICLE_COLOUR-车辆颜色，
         * @param        {Number} OUTER_COVER_ORIENTATION-外盖朝向
         * @param        {Number} OPEN_METHOD_OF_TANK_COVER-外盖打开方式，
         * @param        {Number} ANGLE_OF_FUEL_TANK_COVER-外盖打开角度
         * @return        {*}
         */
        async obtainOtherVehicleInformation() {
            const dicts = [
                { name: 'VEHICLE_COLOUR' },
                { name: 'OUTER_COVER_ORIENTATION' },
                { name: 'OPEN_METHOD_OF_TANK_COVER' },
                { name: 'ANGLE_OF_FUEL_TANK_COVER' },
            ];

            const promises = dicts.map(async dict => {
                try {
                    let params = { dictCode: dict.name };
                    let res = await queryDictItemListByCode(params);
                    if (res.success && res.data.length > 0) {
                        switch (dict.name) {
                            case 'OUTER_COVER_ORIENTATION':
                                this.vehicleOuterCoverOrientationArray = res.data;
                                break;
                            case 'OPEN_METHOD_OF_TANK_COVER':
                                this.openingMethodOfFuelTankCover = res.data;
                                break;
                            case 'ANGLE_OF_FUEL_TANK_COVER':
                                this.openAngle = res.data;
                                break;
                        }
                    }
                    console.log(res, `获取添加车辆的${dict.name}`);
                } catch (error) {
                    console.error(`Failed to obtain ${dict.name}: ${error}`);
                }
            });

            await Promise.all(promises);
        },
        async getQueryBrands() {
            let res = await queryBrands({});
            if (res.success && res.data.length > 0) {
                this.brandModelYear = res.data.map(item => {
                    let newItem = {
                        name: item.brand,
                        city: item.modelList.map(ite => {
                            return ite.model;
                        }),
                        areas: item.modelList.map(ite => {
                            return ite.year;
                        }),
                    };
                    return newItem;
                });
            }
        },
        // 内盖顶部取消事件
        innerCoverAngleCancle() {
            this.fuelTankOpeningAngle = false;
            this.$refs.popup.close();
            this.angleStrSelect = '';
            this.angleStr = '';
        },
        // 内盖顶部确认事件
        innerCoverAngleConfirm() {
            this.fuelTankOpeningAngle = false;
            this.$refs.popup.close();
        },
        // 油箱内盖拧开角度
        bindOpeningAngleOfFuelTankInnerCover() {
            console.log('油箱内盖拧开角度');
            this.fuelTankOpeningAngle = true;
            this.$refs.popup.open('top');
        },
        fuelTankOpeningAngleListItemFun(item, index) {
            console.log(item, index, '油箱内盖拧开角度点击角度选项');
            this.angleStrSelect = item.name;
            this.screenActive = index;
            this.otherActive = false;
        },
        // 点击其他聚焦
        handleFocus() {
            this.angleStrSelect = '';
            this.otherActive = true;
            this.screenActive = -1;
        },
        // 油箱外盖打开方式
        bindOpeningMethodOfFuelTankCover({ detail }) {
            this.fuelTankCoverSelect = detail.value;
            console.log(detail, '油箱外盖打开方式');
        },
        // 车辆外盖朝向
        bindVehicleOuterCoverOrientation({ detail }) {
            this.vehicleOuterCoverOrientation = detail.value;
            console.log(detail, '车辆外盖朝向');
        },
        // 选择车辆类型
        bindVehicleType({ detail }) {
            this.vehicleType = detail.value;
            console.log(detail, '选择车辆类型');
        },
        // 油品信息和车辆颜色
        oilInformationAndVehicleColor() {
            //油品标号接口
            new Promise((resolve, reject) => {
                plateFuelInfo().then(res => {
                    if (res.success) {
                        // 油品型号数组
                        this.oilNumArr = res.data;
                        console.log('油品型号');
                    }
                    resolve('oilNumArr');
                });
            });
            // 枚举值类型(type：1—车牌颜色,2-车辆类型)
            new Promise((resolve, reject) => {
                plateEnumList({ type: 1 }).then(res => {
                    if (res.success) {
                        // 车牌颜色
                        this.licenseArr = res.data.enumList;
                        console.log('车牌颜色');
                    }
                    resolve('licenseArr');
                });
            });
            new Promise((resolve, reject) => {
                plateEnumList({ type: 2 }).then(res => {
                    if (res.success) {
                        // 车牌颜色
                        this.vehicleTypeArray = res.data.enumList;
                        console.log('车辆类型');
                    }
                    resolve('vehicleTypeArray');
                });
            });

            if (this.saveType != 1) {
                /**
                 * @description  : 获取当前车辆详情接口
                 * @param         {*}licensePlate:车牌号码
                 * @param         {*}licensePlateColor:车牌颜色
                 * @param         {*}carNoArr:车牌号数组
                 * @param         {*}fuelTypeVal:油品类型
                 * @param         {*}licensePlateVal:车牌颜色
                 * @param         {*}licensePlateArrEdit:编辑后的车牌号数组
                 * @param         {*}licensePlateValEdit:编辑后的车牌号颜色
                 * @param         {*}saveType:type 区分添加与编辑事件 0添加 1编辑
                 * @return        {*}
                 */

                let params = {
                    licensePlate: this.plateInfo.licensePlate,
                    licensePlateColor: this.plateInfo.licensePlateColor,
                    newEnergyFlag: '',
                };
                new Promise((resolve, reject) => {
                    plateDetail(params).then(res => {
                        if (res.success) {
                            let data = res.data || '';
                            let carNoArr = data.licensePlate ? data.licensePlate.split('') : '';
                            console.log(carNoArr, 'carNoArr====');
                            // 新增后车牌号数组
                            this.licensePlateArr = carNoArr.length == 7 ? [...carNoArr, ''] : JSON.parse(JSON.stringify(carNoArr));
                            // 车牌颜色
                            this.licensePlateVal = this.getIndex(this.licenseArr, data.licensePlateColor);
                            let fuelTypeVal = data.fuelType;
                            setTimeout(() => {
                                // 如果有相同的油品就展示，没有就为-1
                                this.oilNumSelect =
                                    this.oilNumArr.length > 0 ? this.oilNumArr.findIndex(item => item.fuelType == fuelTypeVal) : '-1';
                                if (this.saveType != 1) {
                                    // 编辑后的车牌号数组
                                    this.licensePlateArrEdit =
                                        carNoArr.length == 7 ? [...carNoArr, ''] : JSON.parse(JSON.stringify(carNoArr));
                                    // 编辑后的车牌号颜色
                                    this.licensePlateValEdit = this.getIndex(this.licenseArr, data.licensePlateColor);
                                }
                            }, 100);
                            //  单次最大加油金额
                            this.refuelAmountMax = data.refuelAmountMax ? Number(data.refuelAmountMax).toFixed(2) : '';
                            this.defaultVehicleFlag = data.defaultVehicle;
                            //是否该车辆加油消费自动匹配优惠规则
                            this.autoDiscountFlag = data.autoDiscountFlag;
                            //是否开通车牌付
                            this.noFeelPayFlag = data.noFeelPayFlag;
                            //车辆类型 (0：普通车 1：新能源车)
                            this.vehicleType = data.vehicleType;
                            this.selectBrandModelYearStr = data.brand ? `${data.brand}/${data.model}/${data.carYear}` : '';
                            this.selectBrandModelYearDetail.province = data.brand ? data.brand : '';
                            this.selectBrandModelYearDetail.city = data.model ? data.model : '';
                            this.selectBrandModelYearDetail.area = data.carYear ? `${data.carYear}` : '';
                            this.fuelTankCoverSelect = data.outerCoverModel != null ? (data.outerCoverModel == 1 ? 0 : 1) : '-1';
                            // 车辆外盖朝向
                            this.vehicleOuterCoverOrientation =
                                data.coverDirection != null ? (data.coverDirection == 1 ? 0 : data.coverDirection == 2 ? 1 : '') : '-1';
                            // 拧开角度
                            if (data.innerCoverAngle) {
                                this.openAngle.some((element, index) => {
                                    if (Number(element.name.replace(/°/g, '')) === data.innerCoverAngle) {
                                        this.screenActive = index;
                                        this.angleStrSelect = `${data.innerCoverAngle}°`;
                                        this.otherActive = false;
                                        return true; // 终止循环
                                    } else {
                                        this.angleStr = data.innerCoverAngle;
                                        // 选中其他，进行手动输入的时候高亮
                                        this.otherActive = true;
                                        this.screenActive = -1;
                                        return false;
                                    }
                                });
                            } else {
                                this.angleStr = '';
                            }
                        }
                    });
                });
            }
            this.isInit = true;
        },
        // 遍历回显车牌颜色和车辆信息
        getIndex(list, id) {
            var index;
            for (var i = 0; i < list.length; i++) {
                if (id == list[i].code) {
                    index = i;
                }
            }
            return index;
        },
        //选择油品事件
        bindPickerChange({ detail }) {
            this.oilNumSelect = detail.value;
        },
        //选择车牌颜色
        bindPickerChangePlate({ detail }) {
            if (this.saveType == 1) {
                this.licensePlateVal = detail.value;
                console.log(this.licensePlateVal, '选择车牌颜色');
            } else {
                this.licensePlateValEdit = detail.value;
            }
        },
        // 键盘点击事件监听
        carKeyChange(text) {
            this.isFirst = false;
            let indexCar = this.saveType == 1 ? this.selectPlateIndex : this.selectPlateEditIndex;
            let icenseArr = this.saveType == 1 ? this.licensePlateArr : this.licensePlateArrEdit;
            console.log(this.licensePlateArr, this.licensePlateArrEdit, '=======1');
            // console.log('indexCar--', indexCar);
            // console.log('icenseArr--', icenseArr);
            this.$set(icenseArr, indexCar, text);
            console.log(this.licensePlateArr, this.licensePlateArrEdit, '=======2');
            if (indexCar + 1 === 8 || (icenseArr.length === 8 && icenseArr.every(item => item !== ''))) {
                console.log(this.selectPlateIndex, this.selectPlateEditIndex, '键盘点击事件监听');
                this.vehicleType = 1;
            } else {
                this.vehicleType = 0;
            }
            this.$nextTick(() => {
                if (this.saveType == 1) {
                    this.selectPlateIndex = indexCar + 1;

                    console.log(this.selectPlateIndex, 'this.selectPlateIndex');
                    if (this.selectPlateIndex == 1) {
                        this.$refs.uKeyboard.changeCarInputMode();
                    }
                    if (this.selectPlateIndex == this.licensePlateArr.length) {
                        this.selectPlateIndex = -1;
                        this.isKeyboard = false;
                    }
                } else {
                    this.selectPlateEditIndex = indexCar + 1;
                    if (this.selectPlateEditIndex == 1) {
                        this.$refs.uKeyboard.changeCarInputMode();
                    }
                    if (this.selectPlateEditIndex == this.licensePlateArrEdit.length) {
                        this.selectPlateEditIndex = -1;
                        this.isKeyboard = false;
                    }
                }
            });
        },
        // 键盘退格点击事件
        carKeyDelect() {
            let indexCarDelect = this.saveType == 1 ? this.selectPlateIndex : this.selectPlateEditIndex;
            let licensePlateArrDelect = this.saveType == 1 ? this.licensePlateArr : this.licensePlateArrEdit;
            console.log(indexCarDelect, '键盘退格点击事件');
            if (indexCarDelect == 1) {
                this.$nextTick(() => {
                    this.$refs.uKeyboard.changeCarInputMode();
                });
            }
            if (this.isFirst) {
                console.log(this.isFirst, '键盘退格点击事件==this.isFirst====1');
                this.$set(licensePlateArrDelect, indexCarDelect, '');
                indexCarDelect = indexCarDelect - 1;
            } else {
                console.log(this.isFirst, '键盘退格点击事件==this.isFirst====2');
                indexCarDelect = indexCarDelect - 1;
                this.$set(licensePlateArrDelect, indexCarDelect, '');
            }
            if (indexCarDelect == -1) {
                indexCarDelect = 0;
            }
            if (this.saveType == 1) {
                this.selectPlateIndex = indexCarDelect;
            } else {
                this.selectPlateEditIndex = indexCarDelect;
            }
            if (indexCarDelect === 8) {
                console.log(indexCarDelect, '键盘退格点击事件===');
                this.vehicleType = 1;
            } else {
                this.vehicleType = 0;
            }
        },
        // 车牌input点击事件
        clickPlateInput(index) {
            this.$nextTick(() => {
                if (this.saveType == 1) {
                    this.selectPlateIndex = index;
                    this.$refs.uKeyboard.changeCarInputMode(this.selectPlateIndex == 0 ? 'cn' : 'zn');
                } else {
                    this.selectPlateEditIndex = index;
                    this.$refs.uKeyboard.changeCarInputMode(this.selectPlateEditIndex == 0 ? 'cn' : 'zn');
                }
            });
            this.isKeyboard = true;
            this.isFirst = true;
        },

        // 是否设置为默认车辆
        defaultVehicle() {
            // console.log(detail.value, '是否设置为默认车辆');
            this.defaultVehicleFlag = !this.defaultVehicleFlag;
        },
        // 是否在车辆加油消费自动匹配优惠规则
        preferential({ detail }) {
            console.log(detail.value, '是否在车辆加油消费自动匹配优惠规则');
            this.autoDiscountFlag = detail.value;
        },
        // 是否开通车牌付
        licensePlatePayment({ detail }) {
            console.log(detail.value, '是否开通车牌付');
            this.noFeelPayFlag = detail.value;
        },

        /**
         * @description  :    删除车牌接口
         * @param         {*} licensePlate:车牌号
         * @param         {*} licensePlateColor:车辆颜色
         * @return        {*}
         */
        async deleteVehicle() {
            let plateStr = this.licensePlateArr.join('');
            this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '是否删除此车辆信息？',
                confirmText: '确认',
                confirmColor: '#000',
                cancelText: '取消',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        let params = {
                            licensePlate: plateStr,
                            licensePlateColor: this.plateInfo.licensePlateColor,
                        };
                        plateDelete(params).then(res => {
                            if (res.success == true) {
                                this.backpage();
                            }
                        });
                    }
                },
            });
        },

        /**
         * @description  :    添加车辆与修改车辆点击事件
         * @param         {*} type:type:1新增2编辑修改
         * @param         {*} licensePlate:车牌号
         * @param         {*} refuelAmountMax:单词最大加油金额
         * @param         {*} defaultVehicle:是否是默认车辆
         * @param         {*} licensePlateColor:车牌颜色
         * @param         {*} vehicleType:车辆类型
         * @param         {*} fuelType:油品
         * @param         {*} autoDiscountFlag:是否在车辆加油消费自动匹配优惠规则
         * @param         {*} noFeelPayFlag:是否开通车牌付
         * @param         {*} licensePlateNew:编辑后的车牌号
         * @param         {*} licensePlateColorNew:编辑后的车牌颜色
         * @return        {*}
         */
        async save() {
            let plateStr = this.licensePlateArr.join('');
            console.log(plateStr, 'plateStr===');
            if (this.saveType == 1) {
                if (plateStr.length <= 0) {
                    this.$sKit.layer.showToast({ title: '车辆号码不能为空' });
                    return;
                }
                if (!this.$test.carNoV3(plateStr)) return this.$sKit.layer.showToast({ title: '车辆号码格式错误' });
            }

            //判断是 否是新能源车
            if (plateStr.length == 8) {
                this.vehicleType = 1;
            } else {
                this.vehicleType = 0;
            }
            //
            if (plateStr.length !== 8) {
                if (this.oilNumSelect == '-1') return this.$sKit.layer.showToast({ title: '油品号不能为空' });
                if (this.licensePlateVal == '-1') return this.$sKit.layer.showToast({ title: '车牌颜色不能为空' });
                if (Number(this.refuelAmountMax) == '') return this.$sKit.layer.showToast({ title: '单次最大加油金额不能为空' });
                if (Number(this.refuelAmountMax) < 10 || Number(this.refuelAmountMax) > 5000) {
                    this.$sKit.layer.showToast({ title: '单次加油金额在10-5000元' });
                    return;
                }
                if (!/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(this.refuelAmountMax))
                    return this.$sKit.layer.showToast({ title: '单次最大加油金额格式错误' });
            }
            console.log('this.licensePlateVal', this.licensePlateVal);
            // vehicleType //车辆类型 (0：普通车 1：新能源车)

            let params = {
                // type:1新增2编辑修改
                type: this.saveType,
                licensePlate: plateStr,
                refuelAmountMax: Number(this.refuelAmountMax),
                defaultVehicle: this.defaultVehicleFlag,
                vehicleType: this.vehicleType,
                fuelType: this.oilNumArr[this.oilNumSelect]?.fuelType,
                autoDiscountFlag: this.autoDiscountFlag,
                noFeelPayFlag: this.noFeelPayFlag,
                // 外盖朝向
                coverDirection: this.vehicleOuterCoverOrientationArray[this.vehicleOuterCoverOrientation]
                    ? this.vehicleOuterCoverOrientationArray[this.vehicleOuterCoverOrientation].code
                    : '',

                // 油盖打开方式
                outerCoverModel: this.openingMethodOfFuelTankCover[this.fuelTankCoverSelect]
                    ? this.openingMethodOfFuelTankCover[this.fuelTankCoverSelect].code
                    : '',
                brand: this.selectBrandModelYearDetail.province || '',
                vehicleModel: this.selectBrandModelYearDetail.city || '',
            };
            if (this.angleStrSelect || this.angleStr) {
                // 油箱内盖拧开角度
                params.innerCoverAngle = this.angleStrSelect.replace(/°/g, '') || this.angleStr.replace;
            }
            if (this.selectBrandModelYearDetail.area) {
                params.carYear = this.selectBrandModelYearDetail.area;
            }
            if (this.saveType == 1) {
                if (Number(this.licensePlateVal) >= 0) {
                    params.licensePlateColor = this.licenseArr[this.licensePlateVal]?.code;
                } else {
                    params.licensePlateColor = this.plateInfo?.licensePlateColor;
                }
            } else if (this.saveType == 2) {
                params.licensePlateColor = this.plateInfo?.licensePlateColor;
            }
            if (this.saveType != 1) {
                let editPlateStr = this.licensePlateArrEdit.join('');
                console.log('editPlateStr', editPlateStr);
                if (editPlateStr.length <= 0) return this.$sKit.layer.showToast({ title: '车辆号码不能为空' });
                if (!this.$test.carNoV3(editPlateStr)) return this.$sKit.layer.showToast({ title: '车辆号码格式错误' });
                params.licensePlateNew = editPlateStr;
                if (plateStr.length !== 8) {
                    if (this.licensePlateValEdit == '-1') return this.$sKit.layer.showToast({ title: '请选择车牌颜色' });
                    params.licensePlateColorNew = this.licenseArr[this.licensePlateValEdit].code;
                }
            }
            console.log(params, 'params');
            let plateRes = await plateModifyOrCreate(params);
            if (plateRes.success == true) {
                this.$store.dispatch('zjShowModal', {
                    title: this.saveType != 1 ? '修改车辆成功' : '添加车辆成功',
                    confirmText: '确认',
                    confirmColor: '#000',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            console.log('添加车辆或者修改车辆用户点击确定');
                            this.backpage();
                        }
                    },
                });
            }
        },
        // 返回上一页面
        backpage() {
            if (this.pageType == 'otherMiniPrograms') {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                uni.navigateBack({});
                // #endif
            } else {
                uni.navigateBack({});
            }
        },
        otherAmountsInput(e) {
            let value = e.detail.value;
            var price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            this.$nextTick(() => {
                this.refuelAmountMax = price;
            });
        },
    },
};
</script>
<style scoped lang="scss">
.flex {
    display: flex;
}

.unfomrs {
    position: relative;
    background-color: #f5f5f5;

    .bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 484rpx;
        display: block;
    }

    .page-wrap {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .wrap-car {
        position: absolute;
        left: 0;
        top: 95px;
        width: 100%;
    }

    .bg-car-content {
        padding-left: 23px;
        width: 100%;
        box-sizing: border-box;

        .bg-car-title {
            font-size: 52rpx;
            color: #ffffff;
            line-height: 62rpx;
        }

        .bg-car-text {
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 42rpx;
        }
    }

    .unfomrs-content {
        margin-top: 35px;
        width: 100%;
        box-sizing: border-box;

        .car-number {
            margin: 0rpx 30rpx 0;

            .license-box {
                position: relative;
                border-radius: 15rpx;
                padding: 26rpx 30rpx;
                background: #ffffff;
                z-index: 999;

                .license-title {
                    display: flex;

                    &.red {
                        color: #ea6a44;
                    }

                    &.name {
                        font-size: 28rpx;
                        color: #999999;
                        line-height: 40rpx;
                    }
                }

                .plate-input {
                    margin-top: 15px;
                    display: flex;

                    .plate-input-item {
                        z-index: 20;
                        flex: 1;
                        margin-left: 5px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #f7f7fb;
                        color: #333333;
                        border-radius: 5px;
                        text-align: center;
                        font-size: 15px;
                        font-weight: 700;
                        box-sizing: border-box;
                    }

                    .plate-input-item-select {
                        background-color: $btn-mantle-color;
                        color: $btn-color;
                        box-sizing: border-box;
                        position: relative;
                    }

                    .plate-input-spot {
                        align-self: center;
                        background-color: #dcdcdc;
                        height: 10px;
                        width: 10px;
                        border-radius: 5px;
                        margin-left: 5px;
                    }
                }
            }

            .substance-box {
                border-radius: 15rpx;
                background-color: #ffffff;
                padding: 0 30rpx;
                margin-top: 20rpx;

                .basic-box {
                    //  padding-bottom:29rpx;
                    height: 95rpx;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex: 1;
                    border-bottom: 2rpx solid #eeeeee;
                    overflow: hidden;

                    &:last-child {
                        border-bottom: none;
                    }

                    .substance-tip {
                        display: flex;

                        &.red {
                            color: #e64a1d;
                        }

                        &.number {
                            font-size: 28rpx;
                            font-weight: 400;
                            line-height: 40rpx;
                        }
                    }

                    .substanceWidth {
                        width: 120px;
                    }

                    .substance-tip-right {
                        display: flex;
                        align-items: center;

                        .placeholder {
                            font-size: 26rpx;
                            color: #999;
                            line-height: 40rpx;
                        }

                        .substance-right {
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #999999;
                            line-height: 40rpx;

                            &.input {
                                text-align: right;
                                margin-right: 5px;
                            }
                        }

                        u-icon {
                            color: #999;
                        }

                        .car-arrowhead {
                            width: 32rpx;
                            height: 32rpx;
                        }
                    }
                }

                &.spe {
                    margin-top: 20rpx;
                }
            }

            .basic-box-label {
                padding-top: 20px;
                color: #e64a1d;
                font-size: 12px;
                font-weight: 400;
                line-height: 40rpx;
            }

            .addCar {
                height: 44px;
                margin-bottom: 12px;
                line-height: 44px;
                width: 100%;
            }

            .btnWrap {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
                margin-bottom: 15px;

                div {
                    width: 165px;
                    height: 44px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 44px;

                    &:nth-of-type(1) {
                        border: 0.5px solid #ff6b2c;
                        color: #ff6b2c;
                        background: #ffffff;
                    }

                    &:nth-of-type(2) {
                        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                        box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.07);
                        color: #ffffff;
                    }
                }
            }
        }
    }
}

.img_style {
    width: 42px;
    height: 23px;
}
.dete-content-wrap {
    // margin-bottom: 20px;
    padding: 12px;
    .dete-content {
        margin-top: 41rpx;
        flex: 0 0 25%; /* 将每个元素的flex属性设置为1，使它们均匀分布 */
        .dete-item {
            width: 155rpx;
            height: 60rpx;
            background: #f7f7fb;
            border-radius: 4rpx;
            line-height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
        }
        .isActive {
            border: 1px solid #e64f22;
            background: #ffffff;
            font-weight: 400;
            color: #e64f22;
        }
    }
    .angleStrInput {
        width: 155rpx;
        height: 60rpx;
        background: #f7f7fb;
        border-radius: 4rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;
        padding: 0;
    }
    // .gap23 {
    //     // margin-right: 23rpx;
    //     &:nth-last-of-type(1) {
    //         margin-right: 0;
    //     }
    // }
}
.pop_div {
    background: #fff;
    padding: 10px 15px;
}
</style>
