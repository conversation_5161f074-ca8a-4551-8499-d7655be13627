Component({
    ref() {
        return {
            ...this._ref,
            props: this.props,
        };
    },
    props: {
        title: '',
        onPwdOpen: () => {},
    },
    data: {},
    onInit() {},
    async didMount() {
        this.$page.safePwd = this;
        this.initData();
    },
    methods: {
        initData() {
            console.log('keyBoard loaded');
        },
        async handleRef(ref) {
            this._ref = ref;
        },
        handPwdOpen(info) {
            this.props.onPwdOpen(info);
        },
    },
});
