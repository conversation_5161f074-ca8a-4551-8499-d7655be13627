<template>
    <div class="view">
        <!-- <u-navbar :back-icon-size='40' :height='44' :title-color="pageConfig.titleColor"
			:background="pageConfig.bgColor" title='支付结果' :border-bottom="false">
		</u-navbar>
		<div class='title-time'>{{orderDetailData.orderTime}}</div>
		<div class='header-view'>
			<div class='suc-view'>
				<img class="suc-icon" src="@/static/result-suc.png" mode="">
				<div class="suc-title">支付成功</div>
			</div>
			<div class='price-view'>
				<div class='price-icon'>￥</div>
				<div class='price-price'>{{orderDetailData.amount}}</div>
			</div>
			<div class='sf-price-text'>实付金额：{{orderDetailData.realAmt}}</div>
			<div class='current-time-text'>
				<span>{{currentTime[0]}}</span>
				<span style="margin-left: 12px;">{{currentTime[1]}}</span>
			</div>
		</div>
		<div class='order-info-view'>
			<div class='order-title-view'>
				<div class='order-title'>订单信息</div>
				<div class="vehicle-item-view" :style="{
					border: '0.5px solid #DCDCDC',
					backgroundColor: '#f6f6f6'
				}">
					<div :class="'vehicle-item-pz vehicle-item-pz-select'">{{orderDetailData.carNumber}}</div>
					<div :class="'vehicle-item-yz vehicle-item-yz-select'">{{isNaN(Number(orderDetailData.oils)) ? orderDetailData.oils : orderDetailData.oils + '#'}}</div>
				</div>
			</div>
			<div class='order-info-item' v-for='(item, index) in orderInfo' :key='index'>
				<div class='order-info-title'>{{item.title}}</div>
				<div class='order-info-detail'>{{item.detail}}</div>
			</div>
		</div>
		<div class='order-info-view'>
			<div class='order-title-view'>
				<div class='order-title'>商品信息</div>
			</div>
			<div class='order-info-item' v-for='(item, index) in commodityInfo' :key='index'>
				<div class='order-info-title'>{{item.title}}</div>
				<div class='order-info-detail'>{{item.detail}}</div>
			</div>
		</div>
		<div class='order-info-view'>
			<div class='order-title-view'>
				<div class='order-title'>优惠信息</div>
			</div>
			<div class='order-info-item' v-for='(item, index) in discountInfo' :key='index'>
				<div class='order-info-title'>{{item.title}}</div>
				<div class='order-info-detail'>{{item.detail}}</div>
			</div>
		</div>
		<div class='order-info-view' style='margin-top: 10px;'>
			<div class='order-info-item' v-for='(item, index) in orderCode' :key='index'>
				<div class='order-info-title'>{{item.title}}</div>
				<div class='order-info-detail'>{{item.detail}}</div>
			</div>
			<canvas class="order-barcode-icon" canvas-id='barcode'></canvas>
		</div>
		<div class='btns-view'>
			<div class='share-btn' @click='clickBackHomePage'>返回首页</div>
			<div class='invoice-btn' @click='clickBackByOrder'>我的订单</div>
		</div>
		<swiper class="gg-swiper" :autoplay="true" :interval="3000" :duration="1000">
			<swiper-item class="gg-swiper-item">
				<img class="swiper-item-img" src="@/static/ad-2.png">
			</swiper-item>
    </swiper>-->
        <div class="top-div">
            <div class="navTop">
                <img class="navTopbg" src="@/static/images/bg.png" alt />
            </div>
            <div class="top-img-div" :style="{ height: head_up_h }"></div>
            <div class="backImg-div" @click="go_back" :style="{ height: capsule_h, top: head_up_h }">
                <img class="backImg" src="@/static/back.png" alt />
            </div>
        </div>
        <div class="order-detail">
            <div class="top-station flex align-center" v-if="orderDetailData.stationName">
                <div class="oil-tip">—</div>
                <div class="station-name">{{ orderDetailData.stationName }}</div>
                <div class="oil-tip">—</div>
            </div>
            <!-- <u-loading mode="flower"></u-loading> -->
            <div class="tip-order">请向加油员出示账单</div>
            <div class="order-info">
                <div class="order-title">实付金额</div>
                <div v-if="loading == 0" class="order-price flex align-center">
                    <div class="getting">订单获取中</div>
                    <span class="shadow_dot"></span>
                </div>
                <!-- v-if="!orderStatus" -->
                <div class="order-price flex align-center" v-else-if="loading == 1">
                    <div class="pris">￥</div>
                    <div class="price">{{ orderDetailData.realAmount }}</div>
                </div>
                <div class="order-price align-center" v-else>
                    <div class="tips">获取订单失败</div>
                    <div class="gocheck" @click="goRecord"> 请前往“我的-消费记录”查看订单 </div>
                </div>

                <div style="margin-bottom: 20rpx">
                    <timing></timing>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">枪号</div>
                    <div class="label-text title-font" v-if="orderDetailData.oilGunNo">
                        {{ orderDetailData.oilGunNo }}
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">单价</div>
                    <div class="label-text title-font" v-if="orderDetailData.unitPrice"> ￥{{ orderDetailData.unitPrice }}/升 </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">升数</div>
                    <div class="label-text title-font" v-if="orderDetailData.quantity"> {{ orderDetailData.quantity }}升 </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">订单金额</div>
                    <div class="label-text title-font" v-if="orderDetailData.quantity">
                        ￥{{ orderDetailData.totalAmount ? orderDetailData.totalAmount : '0' }}元
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">电子券</div>
                    <div class="label-text title-font" v-if="orderDetailData.quantity">
                        ￥{{ orderDetailData.couponAmt ? orderDetailData.couponAmt : '0 ' }}元
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">折扣金额</div>
                    <div class="label-text zk-text title-font" v-if="orderDetailData.quantity">
                        ￥{{ orderDetailData.discountAmount ? orderDetailData.discountAmount : '0' }}元
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">支付方式</div>
                    <div class="label-text title-font" v-if="orderDetailData.payType">
                        {{ orderDetailData.payType }}
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">支付时间</div>
                    <div class="label-text title-font" v-if="orderDetailData.tradeTime">
                        {{ orderDetailData.tradeTime }}
                    </div>
                </div>
                <div class="reasons-cell flex align-center justify-between">
                    <div class="label-text">订单号</div>
                    <div class="label-text title-font" v-if="orderDetailData.unitPrice">
                        {{ orderDetailData.tradeNo }}
                    </div>
                </div>
                <div class="btn-view" @click="cancleOrder">
                    <div class="btn-fast-fuel">已确认</div>
                </div>
            </div>
        </div>
        <Campaign v-if="campaignIDFlag" :campaign="campaign" @closeEvent="closeEvent"></Campaign>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
// import AdvertisingPopups from '@/components/advertisingPopups/advertisingPopups.vue'
import Campaign from '../campaign/main.vue';
import wxbarcode from 'wxbarcode';
import { orderDetail2, getPayActivityInfo } from '@/api/home.js';
import timing from '@/components/timing';
let timer = -1;
export default {
    components: {
        timing,
        Campaign,
        // AdvertisingPopups
    },
    data() {
        return {
            loading: 0,
            head_up_h: uni.getMenuButtonBoundingClientRect().top + 'px',
            capsule_h: uni.getMenuButtonBoundingClientRect().height + 'px',
            oliList: [
                { id: '300668', name: '92' },
                { id: '300667', name: '95' },
                { id: '300684', name: '98' },
                { id: '300644', name: '0' },
                { id: '300568', name: '-10' },
                { id: '300567', name: '-20' },
                { id: '300575', name: '-35' },
            ],
            orderInfo: [
                {
                    title: '枪号',
                    detail: '',
                },
                {
                    title: '单价',
                    detail: '',
                },
                {
                    title: '升数',
                    detail: '',
                },
                {
                    title: '订单金额',
                    detail: '',
                },
                {
                    title: '折扣金额',
                    detail: '',
                },
                {
                    title: '支付方式',
                    detail: '',
                },
                {
                    title: '订单号',
                    detail: '',
                },
            ],
            commodityInfo: [
                {
                    title: '油枪',
                    detail: '',
                },
                {
                    title: '油品',
                    detail: '',
                },
                {
                    title: '加油量',
                    detail: '',
                },
                {
                    title: '油价',
                    detail: '',
                },
            ],
            discountInfo: [
                {
                    title: '优惠券',
                    detail: '',
                },
                {
                    title: '油卡优惠',
                    detail: '',
                },
            ],
            currentTime: '',
            pageConfig: pageConfig, // 页面配置
            orderDetailData: {
                // init
                amount: '',
                realAmt: '',
                carNumber: '',
                oils: '',
            }, // 订单详情
            orderStatus: false, //订单状态
            loadNum: 0, //加载请求次数
            campaignIDFlag: false,
            campaign: {},
        };
    },
    onLoad(options) {
        console.log(options.id, '当前支付这笔订单使用券的订单号');
        this.$Storage.msgAuthorizeTime.update(-1);
        this.$util.requestSubscribeMessage(['gm51Kdqg6VAmhJTsswka51soOKalrtOcZT1D0y1hSDY']);
        setTimeout(() => {
            this.inquireOrder();
            timer = setInterval(() => this.inquireOrder(), 4000);
        }, 3000);
        // if(typeof res.data === 'object' && res.data != null) {

        // 	let payMode
        // 	if (this.orderDetailData.payMode == 'wx') {
        // 		payMode = '微信支付'
        // 	} else if (this.orderDetailData.payMode == 'oilCard') {
        // 		payMode = `油卡（${this.orderDetailData.payCardNo}）`
        // 	}

        // 	this.orderInfo[0].detail = this.orderDetailData.oilGun + '号枪'
        // 	this.orderInfo[1].detail = '￥' + this.orderDetailData.unitPrice + '/升'
        // 	this.orderInfo[2].detail = this.orderDetailData.quantity + '升'
        // 	this.orderInfo[3].detail = '￥' + this.orderDetailData.amount + '元'
        // 	this.orderInfo[4].detail = '￥' + this.orderDetailData.discountAmt + '元'
        // 	this.orderInfo[5].detail = payMode
        // 	this.orderInfo[6].detail = this.orderDetailData.tradeTime
        // 	this.orderInfo[7].detail = this.orderDetailData.orderNo

        // this.commodityInfo[0].detail = this.orderDetailData.oilGun + '号枪'
        // this.commodityInfo[1].detail = isNaN(Number(this.orderDetailData.oils)) ? this.orderDetailData.oils : this.orderDetailData.oils + '#'
        // this.commodityInfo[2].detail = this.orderDetailData.quantity
        // this.commodityInfo[3].detail = this.orderDetailData.unitPrice + '元/升'

        // this.discountInfo[0].detail = '-￥' + this.orderDetailData.otherAmt
        // this.discountInfo[1].detail = '-￥' + this.orderDetailData.discountAmt
        // }
    },
    onShareAppMessage() {},
    // onUnload() {
    //   clearInterval(timer);
    // },
    beforeDestroy() {
        clearInterval(timer);
    },
    methods: {
        async inquireOrder() {
            if (this.loadNum < 3) {
                let res = await orderDetail2({ orderId: this.$mp.query.orderid });
                if (res.status == 0) {
                    clearInterval(timer);
                    this.orderDetailData = res.data;
                    this.getPopupAds();
                    this.orderStatus = false;
                    this.loading = 1;

                    // wx.hideLoading()
                } else if (res.status == -1) {
                    // this.orderStatus = true
                    this.loading = 2;
                }
                this.loadNum++;
            } else {
                clearInterval(timer);
                this.$util.showModal('订单不存在', true);
                // wx.hideLoading()
            }
        },
        //前往我的-消费记录页面
        goRecord() {
            uni.redirectTo({
                url: '/packages/my-center/pages/xf-records/main',
            });
        },
        clickBackHomePage() {
            uni.navigateBack({ delta: 2 });
        },
        clickBackByOrder() {
            uni.redirectTo({
                url: '/packages/order/pages/order/main',
            });
        },
        go_back() {
            uni.navigateBack({
                delta: 1,
            });
        },
        cancleOrder() {
            uni.reLaunch({
                url: '/pages/home/<USER>',
            });
        },
        // 获取支付后的弹窗
        getPopupAds() {
            var getOilGunOilArray = wx.getStorageSync('ypIndex');
            let oil = '';
            console.log(this.orderDetailData.goodsName.split('号')[0], '油品号-------油品号');
            this.oliList.forEach(item => {
                if (item.name === this.orderDetailData.goodsName.split('号')[0]) {
                    return (oil = item.id);
                }
            });
            let params = {
                orderNumber: this.orderDetailData.tradeNo,
                stationCode: getOilGunOilArray[0].stationCode,
                amount: this.orderDetailData.realAmount.toString(),
                oil: oil,
            };
            getPayActivityInfo(params).then(res => {
                if (res.status === 0 && res.data.activityImg != '') {
                    this.campaign = res.data;
                    this.campaignIDFlag = true;
                }
            });
        },
        closeEvent() {
            this.campaignIDFlag = false;
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
.flex {
    display: flex;
}
.align-center {
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.flex-1 {
    flex: 1;
}
.view {
    width: 100%;
    min-height: 100vh;
    background-color: #f5f5f5;
    .top-div {
        position: relative;
        .navTop {
            height: 500rpx;
            .navTopbg {
                display: block;
                width: 100%;
                height: 500rpx;
            }
        }
        .top-img-div {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .backImg-div {
            position: absolute;
            left: 0;
            width: 100%;
            .backImg {
                width: 40rpx;
                height: 45rpx;
                margin: 8rpx 0 0 20rpx;
            }
        }
    }
    .order-detail {
        margin: 0 30rpx;
        margin-top: -280rpx;
        position: relative;
        .top-station {
            color: #fff;
            font-size: 32rpx;
            width: 690rpx;
            justify-content: center;
            .oil-tip {
            }
            .station-name {
                margin-right: 30rpx;
                margin-left: 30rpx;
            }
        }
        .tip-order {
            color: #fff;
            text-align: center;
            margin-top: 30rpx;
            font-size: 56rpx;
            font-style: oblique;
            font-weight: 700;
            margin-right: 10rpx;
        }
        .order-info {
            text-align: center;
            background-color: #fff;
            border-radius: 20rpx;
            margin-top: 40rpx;
            padding: 20rpx 0;
            .order-title {
                color: #898989;
                font-size: 28rpx;
                margin-top: 30rpx;
            }
            .getting {
                font-size: 28px;
            }
            .order-price {
                color: #333;
                font-weight: 700;
                padding: 30rpx 0;
                align-items: flex-end;
                justify-content: center;
                .pris {
                    font-size: 40rpx;
                    padding-bottom: 2px;
                }
                .price {
                    font-size: 64rpx;
                }
                .tips {
                    font-size: 52rpx;
                    font-weight: normal;
                    color: $uni-color-error;
                }
                .gocheck {
                    font-size: 44rpx;
                    font-weight: normal;
                    color: $uni-color-warning;
                    margin-top: 5px;
                }
            }
            .reasons-cell {
                padding: 0 10px;
                .label-text {
                    color: #4e4e4e;
                    font-size: 28rpx;
                    line-height: 30px;
                }
                .zk-text {
                    color: #f96702;
                }
            }
            .btn-view {
                margin-top: 30rpx;
                padding: 10rpx 20rpx;
                .btn-fast-fuel {
                    background-color: #f96702;
                    color: #ffffff;
                    border-radius: 5px;
                    height: 45px;
                    line-height: 45px;
                    text-align: center;
                    font-family: PingFangSC-Regular;
                    font-size: 36rpx;
                }
            }
        }
    }
}

// .view {
// 	width: 100%;
// 	min-height: 100%;
// 	background-color: #F6F6F6;
// }

// .title-time {
// 	background-color: $bg-color;
// 	line-height: 50px;
// 	width: 100vw;
// 	color: #FFFFFF;
// 	font-size: 18px;
// 	text-align: center;
// }

// .header-view {
// 	display: flex;
// 	width: 100vw;
// 	flex-direction: column;
// 	justify-content: center;
// 	align-items: center;
// 	padding-top: 20px;
// 	padding-bottom: 20px;
// 	.suc-view {
// 		display: flex;
// 		.suc-icon {
// 			height: 25px;
// 			width: 25px;
// 		}
// 		.suc-title {
// 			line-height: 25px;
// 			font-size: 18px;
// 			font-weight: 700;
// 			margin-left: 5px;
// 		}
// 	}
// 	.price-view {
// 		height: 50px;
// 		display: flex;
// 		align-items: flex-end;
// 		.price-icon {
// 			font-size: 20px;
// 			padding-bottom: 5px;
// 			font-weight: 700;
// 			color: $btn-color;
// 		}
// 		.price-price {
// 			font-size: 36px;
// 			font-weight: 700;
// 			color: $btn-color;
// 		}
// 	}
// 	.sf-price-text {
// 		line-height: 21px;
// 		color: #333333;
// 		font-weight: 700;
// 	}
// 	.current-time-text {
// 		margin-top: 10px;
// 		box-sizing: border-box;
// 		width: 248px;
// 		height: 32px;
// 		line-height: 32px;
// 		border: 1px solid #DCDCDC;
// 		font-size: 15px;
// 		text-align: center;

// 		color: #fff;
// 		background: #333;
// 		font-weight: bold;
// 		border-radius: 5px;
// 	}
// }

// .order-info-view {
// 	margin-left: 15px;
// 	width: 345px;
// 	border-radius: 5px;
// 	overflow: hidden;
// 	background-color: #FFFFFF;
// 	padding-bottom: 6px;
// 	margin-bottom: 20rpx;
// 	.order-title-view {
// 		display: flex;
// 		align-items: center;
// 		height: 44px;
// 		justify-content: space-between;
// 		.order-title {
// 			margin-left: 10px;
// 			color: #333333;
// 			font-size: 15px;
// 			font-weight: 700;
// 		}
// 		.vehicle-item-view {
// 			height: 24px;
// 			margin-right: 10px;
// 			width: 104px;
// 			border-radius: 3px;
// 			position: relative;

// 			.vehicle-item-pz {
// 				font-size: 12px;
// 				line-height: 24px;
// 				margin-left: 10px;
// 				color: #909090;
// 				width: 104px;
// 			}

// 			.vehicle-item-pz-select {
// 				color: #333333;
// 			}

// 			.vehicle-item-yz {
// 				position: absolute;
// 				right: -0.5px;
// 				top: 0px;
// 				width: 32px;
// 				font-size: 12px;
// 				line-height: 24px;
// 				background-color: #909090;
// 				text-align: center;
// 				color: #FFFFFF;
// 				border-radius: 3px;
// 			}

// 			.vehicle-item-yz-select {
// 				background-color: #333333;
// 			}

// 		}
// 	}

// 	.order-info-item {
// 		margin-left: 15px;
// 		margin-right: 15px;
// 		display: flex;
// 		justify-content: space-between;

// 		.order-info-title {
// 			font-size: 12px;
// 			color: #333333;
// 			line-height: 30px;
// 		}

// 		.order-info-detail {
// 			font-size: 12px;
// 			color: #333333;
// 			line-height: 30px;
// 		}
// 	}
// 	.order-barcode-icon {
// 		margin-left: 15px;
// 		width: 315px;
// 		height: 60px;
// 		margin-top: 15px;
// 		margin-bottom: 7px;
// 	}
// }

// .btns-view {
// 	padding-top: 10px;
// 	margin-left: 15px;
// 	margin-right: 15px;
// 	display: flex;
// 	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
// 	.share-btn {
// 		box-sizing: border-box;
// 		line-height: 44px;
// 		text-align: center;
// 		border-radius: 5px;
// 		font-size: 15px;
// 		color: $btn-color;
// 		border: 1px solid $btn-color;
// 		background-color: $btn-mantle-color;
// 		flex: 1;
// 	}
// 	.invoice-btn {
// 		line-height: 44px;
// 		margin-left: 10px;
// 		background-color: $btn-color;
// 		color: #FFFFFF;
// 		font-size: 15px;
// 		border-radius: 5px;
// 		flex: 1;
// 		text-align: center;
// 	}
// }

// @keyframes sucIcon {
// 	0% {
// 		transform: scale(1, 1)
// 	}

// 	50% {
// 		transform: scale(0.8, 0.8)
// 	}

// 	100% {
// 		transform: scale(1, 1)
// 	}
// }

// .gg-swiper {
// 	height: 45px;
// 	margin-top: 10px;
// 	margin-left: 15px;
// 	width: 345px;
// 	padding-bottom: calc(env(safe-area-inset-bottom) + 15px);
// 	.gg-swiper-item {
// 		.swiper-item-img {
// 			height: 45px;
// 			width: 345px;
// 			border-radius: 5px;
// 		}
// 	}
// }
.shadow_dot {
    display: inline-block;
    min-width: 5px;
    min-height: 5px;
    border-radius: 50%;
    box-shadow: 5px 0 currentColor, 15px 0 currentColor, 25px 0 currentColor;
    -webkit-animation: shadow_dot 4s infinite step-start both;
    animation: shadow_dot 4s infinite step-start both;
}
.shadow_dot:before {
    content: '...';
} /* IE8 */
.shadow_dot::before {
    content: '';
}
:root .shadow_dot {
    margin-right: 8px;
} /* IE9+,FF,CH,OP,SF */

@-webkit-keyframes shadow_dot {
    25% {
        box-shadow: none;
    }
    50% {
        box-shadow: 5px 0 currentColor;
    }
    75% {
        box-shadow: 5px 0 currentColor, 15px 0 currentColor;
    }
}
@keyframes shadow_dot {
    25% {
        box-shadow: none;
    }
    50% {
        box-shadow: 5px 0 currentColor;
    }
    75% {
        box-shadow: 5px 0 currentColor, 15px 0 currentColor;
    }
}
</style>
