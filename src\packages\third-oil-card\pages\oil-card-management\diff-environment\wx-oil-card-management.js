import { rollout } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
export default {
    // #ifdef MP-WEIXIN
    methods: {
        fundTransferMethod() {
            this.$sKit.commonUtil.cardOperationPwd({
                nextFun: async () => {
                    this.showKeyboard();
                },
                walletAddParams: {
                    refer: 'r12',
                },
            });
        },
        //密码键盘显示与操作
        showKeyboard() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                pwd => {
                    this.passwordlength = this.passwordKeyboardRef.getLength('password_unique1');
                    this.password = this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    if (this.passwordlength == 6) {
                        this.transferOut();
                    }
                    this.payPassword = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                },
            );
        },

        async transferOut() {
            let params = {
                cardNo: this.thirdCardItem.cardNo,
                amount: this.cardInfoDetail.cash,
                password: this.password,
            };
            let res = await rollout(params);

            if (res.success) {
                let url = '/packages/third-oil-card/pages/fund-transfer-out-result/main';
                let params = {};
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            } else {
            }
        },
    },
    // #endif
};
