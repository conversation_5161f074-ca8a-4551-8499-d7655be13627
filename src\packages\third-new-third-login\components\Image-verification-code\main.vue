<template>
    <div class="content_div">
        <div class="mask_div"></div>
        <div class="slider_div">
            <div class="top_div">
                <span>请完成安全验证</span>
                <div>
                    <img @click="refreshImg()" src="../../images/refresh.png" alt />
                    <img @click="close()" class="close_img" src="../../images/back_x.png" alt />
                </div>
            </div>
            <div class="image_div content" id="content">
                <img class="bg_img" ref="bgimg" :src="imageUrl" @load="imgHandle" alt />
                <img class="slider_img" ref="frontimg" :style="frontStyleCom" :src="smallImage" alt />
            </div>
            <div class="slider_btn">
                <img class="sliderBg" src="../../images/slideBg.png" alt />
                <!-- #ifdef MP-WEIXIN -->
                <img
                    class="sliderBtn"
                    @mousedown="sliderDown"
                    @touchstart="touchStartEvent"
                    @touchmove="touchMoveEvent"
                    @touchend="touchEndEvent"
                    :style="{ left: sliderLeft }"
                    src="../../images/slideBtn.png"
                    alt
                />
                <!-- #endif -->
                <!-- #ifdef MP-TOUTIAO -->
                <img
                    class="sliderBtn"
                    @mousedown="sliderDown"
                    @touchstart="touchStartEvent"
                    @touchmove="touchMoveEvent"
                    @touchend="touchEndEvent"
                    :style="{ left: sliderLeft }"
                    src="../../images/slideBtn.png"
                    alt
                />
                <!-- #endif -->
            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { contentGetVerifySlideImage, messageCodeNoLoginSend } from '../../../../s-kit/js/v3-http/https3/thirdLogin/index';
// import { bindCardList, userLogin, getVerifySlideImage, msgNoLoginSend } from './../../../../utils/loginApi.js';
// import Toast from '../../../Vendor/toast';
var bgImgWidth = 482,
    bgImageHeight = 242,
    frontImgWidth = 80,
    frontImgHeight = 80;

export default {
    props: {
        // 手机号
        phone: {
            type: String,
            default: '',
        },
        path: {
            type: String,
            default: '',
        },
        routerInfo: {
            type: Object,
            default: () => {
                return {};
            },
        },
        isToPage: {
            type: Boolean,
            default: true,
        },
        oneKeyLogin: {
            type: Boolean,
            default: false,
        },
        idNo: {
            type: String,
            default: '',
        },
        distinguish: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            // 滑块验证背景图
            imageUrl: '',
            // 滑块验证小图
            smallImage: '',
            // 滑块验证小图大小和位置
            frontStyle: { top: 0, left: 0, width: 0, height: 0 },
            // 横坐标
            originX: undefined,
            // 纵坐标
            originY: undefined,
            // 滑块距离左侧距离
            sliderLeft: 0,
            isMouseDown: false,
            uuid: this.$util.generateUUID(),
            startX: 0,
            pathList: [],
            contentWidth: 0,
            imageCheckShow: false,
            noSliding: false,
            faceInfo: '',
            commonArgs: {},
            city: '',
            province: '',
            heightRatio: null, //3.0滑块高度
            reqId: null, //3.0请求id
            authInfo: null, //3.0加密字串,验证时原样返回后端
            sliderInfo: {},
            forget_pwd: false,
            isThirdLink: false,
        };
    },
    async created() {},

    async mounted() {
        this.refreshImg();
    },
    computed: {
        frontStyleCom() {
            return `width: ${this.frontStyle.width}px; height: ${this.frontStyle.height}px; top: ${this.frontStyle.top}px; left: ${this.frontStyle.left}px;`;
        },
        // ...mapGetters(['login']),
    },
    methods: {
        close() {
            this.$emit('changeShow');
        },
        // 获取图片验证码
        async thirdGetImgCode() {
            let params = {
                mobile: this.phone,
            };
            let res = await contentGetVerifySlideImage(params);
            console.log(res, 'getVerifySlideImage=========');
            if (res.success) {
                //       this.imageSrc = `data:image/png;base64, ${base64String}`;
                this.imageUrl = res.data.backgroundImage;
                this.smallImage = res.data.frontImage;
                console.log('获取到的需要补全的小图片', res.data.frontImage);
                this.reqId = res.data.reqId;
                this.heightRatio = res.data.heightRatio;
                this.authInfo = res.data.authInfo;
                this.dealImgs();
            }
        },
        imgHandle() {
            this.noSliding = true;
        },
        //绘制图片宽高
        dealImgs() {
            let _this = this;
            let contentWidth = 0;
            // #ifdef MP-TOUTIAO
            console.log('执行了吗===抖音');
            const query = tt.createSelectorQuery();
            query.select('.content').boundingClientRect();
            query.exec(res => {
                if (res && res[0]) {
                    contentWidth = res[0].width;
                    console.log(contentWidth, 'contentWidth');

                    // 在这里执行后续操作，确保在获取到 contentWidth 后再进行计算
                    _this.frontStyle.width = (frontImgWidth * contentWidth) / bgImgWidth;
                    _this.frontStyle.height = (frontImgHeight * contentWidth) / bgImgWidth;
                    _this.frontStyle.top = (bgImageHeight * _this.heightRatio * contentWidth) / bgImgWidth;
                }
            });
            // #endif
            // #ifdef MP-WEIXIN
            console.log('执行了吗====微信');
            uni.createSelectorQuery()
                .in(this)
                .select('.content')
                .boundingClientRect(data => {
                    contentWidth = data.width;
                    console.log(contentWidth, 'contentWidth');
                    _this.frontStyle.width = (frontImgWidth * contentWidth) / bgImgWidth;
                    _this.frontStyle.height = (frontImgHeight * contentWidth) / bgImgWidth;
                    _this.frontStyle.top = (bgImageHeight * _this.heightRatio * contentWidth) / bgImgWidth;
                })
                .exec();
            // #endif
        },
        // 这个方法通常用于处理鼠标在元素上按下的事件。当用户在元素上按下鼠标左键时，会触发这个方法。在这个方法中，通常会记录一些状态或执行一些操作，比如记录鼠标按下时的位置或改变元素的样式。
        sliderDown(event) {
            this.originX = event.clientX;
            this.originY = event.clientY;
            this.isMouseDown = true;
            if (!this.noSliding) return;
        },
        // 刷新图片
        refreshImg() {
            this.thirdGetImgCode();
        },
        // 这个方法通常用于处理触摸设备上触摸开始的事件。当用户在元素上开始触摸屏幕时，会触发这个方法。类似于sliderDown，在这个方法中通常会记录触摸开始时的位置或执行一些操作。
        touchStartEvent(e) {
            console.log(e, 'touchStartEvent====触摸开始');
            if (!this.noSliding) {
                return;
            }
            this.originX = e.changedTouches[0].pageX;
            this.originY = e.changedTouches[0].pageY;
            this.isMouseDown = true;
            let touches = e.changedTouches[0];
            // 获取拖拽按钮的默认坐标
            this.startX = touches.clientX;
            this.pathList.push({
                xd: 0,
                yd: this.heightRatio,
                time: this.getTime(),
            });
            console.log(this.originX, this.originX);
        },
        // 这个方法通常用于处理触摸设备上触摸移动的事件。当用户在元素上触摸屏幕并移动手指时，会触发这个方法。在这个方法中通常会根据手指移动的位置来调整元素的位置或执行其他相关操作。
        async touchMoveEvent(e) {
            console.log(e, 'touchMoveEvent====触摸移动');
            // 创建节点获取元素宽高是异步 可能存在获取不到值的问题
            let bgimgClientWidth = 0; // 修正为初始化为0，方便后续赋值
            let frontimgClientWidth = 0; // 修正为初始化为0，方便后续赋值
            // 获取背景图宽度信息
            // #ifdef MP-WEIXIN
            const bgImgQuery = uni.createSelectorQuery().in(this).select('.bg_img');
            const bgImgRect = await new Promise((resolve, reject) => {
                bgImgQuery
                    .boundingClientRect(data => {
                        resolve(data);
                    })
                    .exec(res => {
                        if (!res.length) {
                            reject(new Error('未获取到背景图的布局信息'));
                        }
                    });
            });
            bgimgClientWidth = bgImgRect.width;
            console.log(bgimgClientWidth, 'bgimgClientWidth===');

            // 获取前景图宽度信息
            const sliderImgQuery = uni.createSelectorQuery().in(this).select('.slider_img');
            const sliderImgRect = await new Promise((resolve, reject) => {
                sliderImgQuery
                    .boundingClientRect(data => {
                        resolve(data);
                    })
                    .exec(res => {
                        if (!res.length) {
                            reject(new Error('未获取到前景图的布局信息'));
                        }
                    });
            });
            frontimgClientWidth = sliderImgRect.width;
            console.log(frontimgClientWidth, 'frontimgClientWidth===');

            if (!this.isMouseDown) return false;
            let touches = e.changedTouches[0];
            let pageX = touches.clientX;
            console.log(pageX, 'pageX====');

            // 重新计算最大允许偏移量
            this.maxRightOffset = bgimgClientWidth - frontimgClientWidth;

            let left = 0;
            if (pageX - this.startX < 0) {
                left = 0;
                console.log(left, 'if=======');
            } else if (pageX - this.startX > this.maxRightOffset) {
                left = this.maxRightOffset;
                console.log(left, 'else if=======');
            } else {
                left = pageX - this.startX;
                console.log(left, 'else=======');
            }

            this.sliderLeft = left + 'px';
            this.frontStyle.left = left;

            // #endif
            // #ifdef MP-TOUTIAO
            Promise.all([this.getBoundingClientRect('.bg_img'), this.getBoundingClientRect('.slider_img')])
                .then(([bgimgClientWidth, frontimgClientWidth]) => {
                    console.log(bgimgClientWidth, '获取到的值=====bg_img');
                    console.log(frontimgClientWidth, '获取到的值=====slider_img');
                    if (!this.isMouseDown) return false;
                    let touches = e.changedTouches[0];
                    let pageX = touches.clientX;
                    console.log(pageX, 'pageX====');
                    let left = 0;
                    if (pageX - this.startX < 0) {
                        left = 0;
                        console.log(left, 'if=======');
                    } else if (pageX - this.startX > bgimgClientWidth - frontimgClientWidth) {
                        left = bgimgClientWidth - frontimgClientWidth;
                        console.log(left, 'else if=======');
                    } else {
                        left = pageX - this.startX;
                        console.log(left, 'else=======');
                    }
                    this.sliderLeft = left + 'px';
                    this.frontStyle.left = left;
                    // 在这里可以继续处理后续逻辑
                })
                .catch(error => {
                    console.error(error);
                });

            // #endif
        },
        getBoundingClientRect(selector) {
            return new Promise((resolve, reject) => {
                const query = tt.createSelectorQuery();
                query.select(selector).boundingClientRect();
                query.exec(res => {
                    if (res && res[0]) {
                        resolve(res[0].width);
                    } else {
                        reject(new Error('Failed to get bounding client rect'));
                    }
                });
            });
        },
        // 这个方法通常用于处理触摸设备上触摸结束的事件。当用户在元素上结束触摸时，会触发这个方法。在这个方法中通常会进行一些清理工作，比如重置状态或执行最终的操作。
        touchEndEvent(e) {
            console.log(e, 'touchEndEvent====触摸结束');
            let bgimgClientWidth = Number;
            let queryBgImg = new Promise((resolve, reject) => {
                // #ifdef MP-WEIXIN
                uni.createSelectorQuery()
                    .in(this)
                    .select('.bg_img')
                    .boundingClientRect(data => {
                        console.log(data, 'bg_img ===== data');
                        bgimgClientWidth = data.width;
                        resolve();
                    })
                    .exec();
                // #endif
                // #ifdef MP-TOUTIAO
                const query = tt.createSelectorQuery();
                query.select('.bg_img').boundingClientRect();
                query.exec(res => {
                    if (res && res[0]) {
                        console.log(res[0], 'res[0]');
                        bgimgClientWidth = res[0].width;
                        resolve();
                    }
                });
                // #endif
            });
            console.log(this.isMouseDown, 'this.isMouseDown');
            if (!this.isMouseDown) return false;
            this.isMouseDown = false;

            queryBgImg.then(width => {
                console.log(bgimgClientWidth, 'bgimgClientWidth=====touchEndEvent');
                this.pathList.push({
                    xd: (this.frontStyle.left / bgimgClientWidth).toFixed(2),
                    yd: this.heightRatio,
                    x: '',
                    y: '',
                    time: this.getTime(),
                });
                // 3.0密码登录滑块之后
                this.sliderInfo = {
                    sliderReqId: this.reqId,
                    sliderAuthInfo: this.authInfo,
                    sliderPathList: this.pathList,
                };
                //滑块验证登录
                this.thirdSendMsg();
            });
        },
        //  3.0短信发送验证码
        async thirdSendMsg() {
            let params = {
                mobile: this.phone,
                reqId: this.reqId,
                authInfo: this.authInfo || '',
                pathList: this.pathList,
            };
            let res = await messageCodeNoLoginSend(params);
            console.log(res, 'messageCodeNoLoginSend=======');
            if (res.success) {
                this.$emit('sendSuccess');
                this.close();
            } else {
                this.frontStyle.left = 0;
                this.sliderLeft = 0;
                this.pathList = [];
                // uni.showToast({
                //     title: `${res.message || '校验失败'}`,
                // });
                if (res.errorCode == 'B_C20_008026') {
                    this.close();
                    return;
                } else {
                    this.thirdGetImgCode();
                    return;
                }
            }
        },
        getTime() {
            // 获取当前时间
            let now = new Date();

            // 获取年、月、日、时、分、秒
            let year = now.getFullYear();
            let month = now.getMonth() + 1;
            let day = now.getDate();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            let seconds = now.getSeconds();

            // 格式化为指定格式
            month = month < 10 ? '0' + month : month;
            day = day < 10 ? '0' + day : day;
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            seconds = seconds < 10 ? '0' + seconds : seconds;

            let formattedDate = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            return formattedDate;
        },
    },
};
</script>

<style lang="scss" scoped>
.content_div {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
}

.slider_div {
    width: 280px;
    height: 259px;
    border-radius: 10px;
    background: #fff;
    position: relative;
    top: 50%;
    left: 50%;
    padding: 15px 16px 19px 16px;
    transform: translate(-50%, -50%);

    .image_div {
        position: relative;
        width: 100%;
        width: 248px;
        height: 125px;

        .bg_img {
            width: 100%;
            height: 100%;
        }

        .slider_img {
            position: absolute;
        }
    }

    .slider_btn {
        width: 100%;
        height: 44px;
        position: relative;
        margin-top: 20px;

        .sliderBg {
            width: 100%;
            height: 100%;
        }

        .sliderBtn {
            width: 44px;
            height: 44px;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
}

.top_div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    span {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }

    div {
        img {
            width: 15px;
            height: 15px;
        }

        .close_img {
            margin-left: 10px;
        }
    }
}

.mask_div {
    width: 100%;
    height: 100%;
    position: fixed;
    // z-index: 1;
    background: #000000;
    opacity: 0.5;
}

.close_header_div {
    width: 20px;
    height: 20px;
    position: fixed;
    top: 59px;
    left: 14px;
    z-index: 20;

    .header_icon2 {
        width: 15px;
        height: 15px;
    }
}
</style>
