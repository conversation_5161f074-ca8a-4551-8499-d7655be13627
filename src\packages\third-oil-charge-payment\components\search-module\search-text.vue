<template>
    <div class="search-area">
        <div class="input-area">
            <img class="search-icon" src="../../image/icon-serch.png" alt />
            <!-- #ifdef MP-MPAAS || H5-CLOUD -->
            <input v-model="searchText" class="input" placeholder="输入油站名称" type="text" @blur="aeraBlur" />
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <input v-model="searchText" class="input" placeholder="输入油站名称" type="text" @blur="aeraBlur" @input="aeraInput" />
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <div class="ali-search" @click="aliChooseLocation">
                <input
                    :value="searchText"
                    class="input"
                    disabled
                    placeholder="请输入地点、商区名称或者油站名称"
                    type="text"
                    @blur="aeraBlur"
                />
            </div>
            <!-- #endif -->

            <!-- #ifndef MP-ALIPAY -->
            <div class="fl-row">
                <div class="font-14 color-999 bor-seach" @click="searchClick">搜索</div>
            </div>
            <!-- #endif -->

            <!-- #ifdef MP-MPAAS || H5-CLOUD -->
            <div v-if="addressList && addressList.length > 0 && searchText && searchText.length > 0 && inputFlag" class="input-pop">
                <div v-for="item in addressList" :key="item.uid" class="address-search-item" @click="handleClickAddress(item)">
                    <span class="search-item-location">{{ item.name }}</span>
                </div>
            </div>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <div class="input-pop" v-if="inputFlag">
                <div v-for="item in addressList" :key="item.id" class="address-search-item" @click="handleClickAddress(item)">
                    <span class="search-item-location">{{ item.title }}</span>
                </div>
            </div>
            <!-- #endif -->
        </div>
    </div>
</template>

<script>
// #ifdef MP-WEIXIN
let QQMapWX = require('@/utils/qqmap-wx-jssdk.min');
let qqmapsdk = new QQMapWX({
    key: 'WQEBZ-VTP3F-BBCJR-J5GZP-XZHCQ-LRF4E', // 必填
});
const THROTTLE_TIME = 500;
// #endif
// #ifdef MP-MPAAS
let Fly = require('flyio/dist/npm/wx');
let fly = new Fly();
// #endif
import { mapGetters } from 'vuex';

export default {
    props: {
        isCustom: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            searchText: '',
            addressList: [],
            isListenSearchText: true,
            inputFlag: false, //输入框控制
        };
    },
    created() {},
    mounted() {},
    methods: {
        aeraBlur() {
            setTimeout(() => {
                this.inputFlag = false;
            }, 500);
        },
        /**
         * @description  : 根据关键词搜索
         * @param         {*} item:关键词
         * @return        {*}
         */
        // #ifdef MP-MPAAS || H5-CLOUD
        handleClickAddress(item) {
            this.isListenSearchText = false;
            this.searchText = item.name;
            this.$nextTick(() => {
                this.isListenSearchText = true;
                this.destinationLocationInfo = item;
                this.addressList = [];
                this.$emit('change', item);
                this.inputFlag = false;
            });
        },
        // #endif
        // #ifdef MP-WEIXIN
        handleClickAddress(item) {
            this.searchText = item.title;
            this.$nextTick(() => {
                this.destinationLocationInfo = item;
                this.addressList = [];
                this.$emit('change', item);
                this.inputFlag = false;
            });
        },
        /**
         * 根据关键字获取相关的地址信息
         * @param {*} keyword 关键字
         * @param {*} otherOptions 其他配置 具体参考: https://lbs.qq.com/miniProgram/jsSdk/jsSdkGuide/methodGetsuggestion
         * @returns
         */
        getSuggestion(keyword, otherOptions = {}) {
            return new Promise((resolve, reject) => {
                qqmapsdk.getSuggestion({
                    keyword,
                    ...otherOptions,
                    success: resolve,
                    fail: reject,
                });
            });
        },
        /**
         * @description  : 输入油站名称
         * @return        {*}
         */
        aeraInput() {
            // console.log('this.province', this.province)
            if (Date.now() - this.lastSearch < THROTTLE_TIME || this.isCustom) {
                return;
            }
            this.lastSearch = Date.now();
            this.timerId = setTimeout(() => {
                uni.showLoading({
                    title: '加载中',
                });
                if (!this.searchText) {
                    this.addressList = [];
                    this.inputFlag = false;
                    wx.hideLoading();
                    return;
                }

                return this.getSuggestion(this.searchText, {
                    region: this.province || '北京',
                    page_size: 20,
                })
                    .then(res => {
                        if (res.status == 0) {
                            this.inputFlag = true;
                            this.addressList = res.data;
                        }
                    })
                    .catch(e => {
                        console.warn('searchAddressListByKeyWords => get => warn', e);
                        uni.showToast({
                            title: '请求失败',
                            icon: 'none',
                        });
                        this.addressList = [];
                        this.inputFlag = false;
                    })
                    .finally(() => wx.hideLoading());
            }, THROTTLE_TIME);
        },
        // #endif
        // #ifdef MP-ALIPAY
        /**
         * @description  : 请输入地点、商区名称或者油站名称后的逻辑
         * @return        {*}
         */
        aliChooseLocation() {
            uni.chooseLocation({
                longitude: this.lonV3,
                latitude: this.latV3,
                success: res => {
                    this.searchText = res.name;
                    const location = {
                        lng: res.longitude,
                        lat: res.latitude,
                    };
                    this.$emit('change', { location });
                },
                fail: error => {
                    console.log(error);
                },
            });
        },
        // #endif
        searchClick() {
            if (this.isCustom) {
                this.$emit('searchClick', this.searchText);
            }
        },
    },
    computed: {
        // #ifdef MP-WEIXIN
        inputStyle() {
            return `box-shadow:${this.inputFlag ? '0px 2px 3px 0px rgba(192,187,187,0.5)' : ''})`;
        },
        // #endif
        ...mapGetters(['latV3', 'lonV3']),
    },
    components: {},
    watch: {
        // #ifdef MP-MPAAS
        searchText(curVal, oldVal) {
            if (!this.isListenSearchText) {
                return;
            }
            this.destinationLocationInfo = {};
            if (curVal) {
                let URL = `http://api.map.baidu.com/place/v2/search?query='${this.searchText}'&region=131&output=json&ak=T0VPdIGBUv1w1YgAODQIciaP8CssB588`;
                fly.get(URL)
                    .then(res => {
                        //
                        this.addressList = res.data.results;
                        this.inputFlag = true;
                    })
                    .catch(err => {
                        this.addressList = [];
                    });
            } else {
                this.addressList = [];
                this.$emit('change', {});
            }
        },
        // #endif
        // #ifdef H5-CLOUD
        searchText(curVal, oldVal) {
            if (!this.isListenSearchText) {
                return;
            }
            this.destinationLocationInfo = {};
            if (curVal) {
                const ak = 'T0VPdIGBUv1w1YgAODQIciaP8CssB588';
                // let URL = `http://api.map.baidu.com/place/v2/search?query='${this.searchText}'&region=131&output=json&ak=T0VPdIGBUv1w1YgAODQIciaP8CssB588`;
                const method = 'GET';
                const url = `https://api.map.baidu.com/place/v2/search?query=${encodeURIComponent(
                    this.searchText,
                )}&region=131&output=json&ak=${ak}&s=1`;
                const data = null;
                const headers = { 'Content-Type': 'text/javascript' };
                const timeOut = 2000;
                const dataType = 'json';
                upsdk.pluginReady(() => {
                    upsdk.sendRequest({
                        method: method,
                        url: url,
                        data: data,
                        headers: headers,
                        timeOut: timeOut,
                        dataType: dataType,
                        success: response => {
                            // 插件调用成功
                            console.log(response, '地址搜索');
                            let dataInfo = response.message;
                            console.log('typeof dataInfo.data', typeof dataInfo);
                            let res = {};
                            if (typeof dataInfo === 'string' && dataInfo) {
                                res = JSON.parse(dataInfo);
                            } else {
                                res = dataInfo;
                            }
                            console.log('res---结果', res);
                            this.addressList = res.results;
                            this.inputFlag = true;
                        },
                        fail: error => {
                            // 插件调用失败
                            this.addressList = [];
                        },
                    });
                });
            } else {
                this.addressList = [];
                this.$emit('change', {});
            }
        },
        // #endif
        // #ifdef MP-WEIXIN
        searchText(val, oldVal) {
            if (val) {
                // this.aeraInput();
            } else {
                this.addressList = [];
                this.$emit('change', {});
            }
        },
        // #endif
    },
};
</script>
<style scoped lang="scss">
.search-area {
    display: flex;
    align-items: center;
    padding: 16px 0 6px 0;

    .input-area {
        flex: 1;
        height: 40px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e7e7e7;
        display: flex;
        align-items: center;
        padding: 0px 14px;
        position: relative;

        .search-icon {
            width: 17px;
            height: 17px;
        }

        .input {
            margin-left: 9px;
            font-size: 14px;
            caret-color: #e64a1d;
            flex: 1;
            height: 40px;
            background: none;
        }

        .bor-seach {
            // width: 40px;
            height: 16px;
            border-left: 1px solid #999;
            padding-left: 9px;
            line-height: 16px;
        }

        .input-pop {
            position: absolute;
            top: 40px;
            left: 0;
            width: 100%;
            height: 270px;
            background: #f7f7fb;
            box-shadow: 0px 2px 3px 0px rgba(192, 187, 187, 0.5);
            border-radius: 0 0 10px 10px;
            z-index: 1000;
            overflow: auto;

            .address-search-item {
                padding: 10px 40px;
                box-sizing: border-box;

                .search-item-location {
                    box-sizing: border-box;
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                }
            }
        }
    }

    .ali-search {
        width: 100%;
        height: 100%;

        .input {
            padding: 0;
            width: 100%;
        }
    }
}
</style>
