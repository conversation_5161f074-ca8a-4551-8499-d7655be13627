<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas bg-F7F7FB">
        <zj-navbar :height="44" title="资金转出"></zj-navbar>
        <div class="view-phone fl-column fl-al-jus-cen">
            <div class="weight-bold font-20">{{phone}}</div>
            <div class="font-12 color-686868">我们将发送验证码到您的手机</div>            
        </div>
        <div class="p-LR-16">
            <div class="bg-fff line">
                <input type="text" class="input_div" v-model="phoneCode" placeholder-class="phone_input" placeholder="请输入短信验证码"/>
                <div class="div" @click="getcashsweepCode" v-if="show">获取验证码</div>
                <div class="right-code marginRight" v-else>{{ count }}秒后再次获取</div>
            </div>
            <div class="addCard primary-btn fl-row color-fff fl-al-jus-cen border-rad-8" @click="confirmClick()">
                <div>确认提交</div>
            </div>
        </div>        
        <zj-show-modal></zj-show-modal>
	</div>
</template>
<script>
import { messageSend, unBindCardFundTransfer } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            phoneCode: '',
            show: true, // 验证码
            count: 60,
            timerSendCode: null,
            timer: null,
            messageType: false,
            cardNo: '',
            phone: ''
        }
    },
    mounted() {
    },
    onLoad(options) {
        let data = JSON.parse(decodeURIComponent(options.data))
        this.cardNo = data.cardNo
        this.phone = data.phone
    },
    methods: {
        /**
         * 发送验证码
         */
        async getcashsweepCode() {
            if (!this.show) return;
            if (!this.timerSendCode) {
                console.log( '发送验证码===========');
                let res = await messageSend({cardNo: this.cardNo})
                if (res.success) {
                    this.getCode();
                    this.show = false;
                    uni.showToast({
                        title: '验证码发送成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.messageType = true;
                }
                this.timerSendCode = setTimeout(() => {
                    this.timerSendCode = null;
                }, 3000);
            }

        },
        /**
         * 获取验证码倒计时
         */
         getCode() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        //确认提交事件
        async confirmClick() {
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.phoneCode) {
                uni.showToast({
                    title: '验证码不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.$sKit.test.checkValidCode(this.phoneCode)) {
                uni.showToast({
                    title: '验证码格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let res = await unBindCardFundTransfer({cardNo: this.cardNo, verifyCode: this.phoneCode})
            if (res.success) {
                this.$store.dispatch('zjShowModal', {
                    title: '已成功转出',
                    confirmText: '确认',
                    confirmColor: '#E64F22',
                    success: res => {
                        if (res.confirm) {
                            this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/home/<USER>', { refer: 'r07' }, 'redirectTo');
                        } else if (res.cancel) {
                        }
                    },
                });                
            }
        }
    }
}
</script>
<style scoped lang="scss">
.view-phone {
    height: 110px;
}
.line {
    height: 44px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;
    .input_div {
        flex: 1;
        height: 100%;
        padding-left: 12px;
    }
    .div {
        height: 30px;
        line-height: 30px;
        width: 114px;
        text-align: center;
        color: #FF6B2C;
        border-left: 1px solid #d3d3d3;
        font-size: 16px;
    }
}
.addCard {
    height: 44px;
    line-height: 44px;
    margin-bottom: 12px;
    width: 100%;
}
.marginRight {
    margin-right: 8px;
}
</style>