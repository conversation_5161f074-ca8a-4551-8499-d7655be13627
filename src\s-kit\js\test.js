import layer from './layer';

const regularType = {
    /**
     * 验证电子邮箱格式
     */
    email: /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/,
    /**
     * 验证手机格式
     */
    mobile: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,

    /**
     * 金额,只允许2位小数
     */
    amount: /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/,
    /**
     * 验证URL格式
     */
    url: /^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/,
};

/**
 *
 * @param {*} value  传入校验的值
 * @param {*} type   传入校验的类型
 */
// let method = regularType[type].test(value)
let regexMethod = (value, type = 'email') => {
    // return
    // if(type == 'idCardComplex'){
    // 	idCardComplex(value, type)
    // }else{
    return regularType[type].test(value);
    // }
};

/**
 * 只能是字母或者中文
 */
function checkSAndE(str) {
    if (!str) {
        return true;
    }
    var zg = /^[\u4e00-\u9fff\u3000-\u303fA-Za-z0-9\·]{2,20}$/;
    if (zg.test(str)) {
        return true;
    } else {
        return false;
    }
}

function enterpriseTitleCheck(str) {
    if (!str) {
        return true;
    }
    var zg = /^(?!\s*$)[A-Za-z0-9.《》<>,，、·。_\-\[\]\(\)\u4e00-\u9fff\u3000-\u303f\（）\s-]+$/;
    if (zg.test(str)) {
        return true;
    } else {
        return false;
    }
}

// 个人抬头税号3.0，不校验必填
function checkShuiHao_individual_third(taxId) {
    if (taxId == '' || !taxId) {
        return true;
    }
    //税号长度15-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{15,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        layer.showToast({ title: '纳税人识别号请输入15-20位的数字或大写字母的组合' });
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                layer.showToast({ title: '纳税人识别号前两位是数字的话，3-6位也必须为数字' });
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字' });
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字' });
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                layer.showToast({ title: '纳税人识别号前两位均为字母，则3-8位必须为数字' });
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为字母，则第2-16位必须为数字' });
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为数字，则前6位必须为数字' });
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                layer.showToast({ title: '纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个' });
                return false;
            }
        }
    }
    return true;
}

// 3.0税号
function checkShuiHao_New_third(taxId) {
    if (taxId == '') {
        layer.showToast({ title: '纳税人识别号不能为空' });
        return false;
    }
    //税号长度15-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{15,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        layer.showToast({ title: '纳税人识别号请输入15-20位的数字或大写字母的组合' });
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                layer.showToast({ title: '纳税人识别号前两位是数字的话，3-6位也必须为数字' });
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字' });
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字' });
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                layer.showToast({ title: '纳税人识别号前两位均为字母，则3-8位必须为数字' });
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为字母，则第2-16位必须为数字' });
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                layer.showToast({ title: '纳税人识别号第1位为数字，则前6位必须为数字' });
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                layer.showToast({ title: '纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个' });
                return false;
            }
        }
    }
    return true;
}

function checkValidCode(str) {
    let reg = /^\d{4,6}$/;
    return reg.test(str);
}

function checkTelPhone(str) {
    if (!str) {
        return true;
    }
    // let reg = /^[0-9\-\s]{0,20}$/g
    let reg = /^[0-9\-\s]{0,20}$/g;
    // let reg = /^((\+86)|(86))?(1)\d{10}$/.test(value) || /^0[0-9-]{10,13}$/g
    return reg.test(str);
}

function checkBankName(str) {
    let reg = /^(?!\s*$)[A-Za-z0-9.《》<>,，、·。_\-\[\]\(\)\u4e00-\u9fff\u3000-\u303f\（）\s-]{0,40}$/;
    return reg.test(str);
}

function newCheckBankCount(str) {
    if (!str) {
        return true;
    }
    let reg = /^[0-9]{0,30}$/g;
    return reg.test(str);
}

function isAndroidClient() {
    let systemInfo = uni.getSystemInfoSync();
    if (systemInfo.osName === 'ios') {
        return false;
    } else {
        return true;
    }
}

function checkPassWord(password) {
    //必须为字母加数字且长度为6-20位
    var str = password;
    if (str == null || str.length < 6 || str > 20) {
        return false;
    }
    var reg1 = new RegExp(/^[0-9A-Za-z]+$/);
    if (!reg1.test(str)) {
        return false;
    }
    var reg = new RegExp(/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/);
    if (reg.test(str)) {
        return true;
    } else {
        return false;
    }
}

export default {
    regexMethod,
    checkSAndE,
    enterpriseTitleCheck,
    checkShuiHao_individual_third,
    checkShuiHao_New_third,
    checkTelPhone,
    checkBankName,
    newCheckBankCount,
    checkValidCode,
    isAndroidClient,
    checkPassWord,
};
