<template>
    <div class="pageMpaas" v-if="pageData">
        <!-- <Navigation
        :title="getTitle"
        hasBack="1"
        @backEvent="backEvent"
      ></Navigation> -->
        <zj-navbar :title="getTitle"> </zj-navbar>
        <div class="result-content" :style="{ marginTop: topHeight + 100 + 'px' }">
            <img src="../../images/success.png" v-if="pageData.type == 0" />
            <img src="../../images/faild.png" v-if="pageData.type == 1" />
            <img mode="widthFix" v-if="pageData.type == 2" src="../../images/fastInSuccess.gif" />
            <div class="message">{{ pageData.message }}</div>
            <!-- <div class="message">{{ '基本撒数控机床备尝啊手机卡少女卡斯楠艰苦是吧插卡机啊是吧插卡机不擦拉卡成绩表阿斯卡胶擦上次看菜鸟仓' }}</div> -->
        </div>

        <div class="bottom">
            <div v-if="pageData.type == 0" class="span left-span" @click="backEvent">查看信息</div>
            <div v-if="pageData.type == 0" class="span right-span" @click="goHome">返回首页</div>
            <div v-if="pageData.type == 1" class="span complete" @click="backEvent">关闭</div>
            <div v-if="pageData.type == 2" class="span complete" @click="backEvent">确认</div>
        </div>
    </div>
    <div v-else> <zj-show-modal></zj-show-modal> </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: '',

    props: {},
    data() {
        return {
            pageData: {},
            topHeight: 0,
            getTitle: '',
        };
    },
    methods: {
        backEvent() {
            console.log('11111111');
            uni.navigateBack({ delta: 1 });
        },
        goHome() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver();
            // #endif
        },
    },
    onLoad(option) {
        // type: 0, // 0 开通成功 1 开通失败 2 进站成功
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        this.pageData = queryInfo.pageData;
        this.getTitle = this.pageData.type == 0 ? '开通成功' : this.pageData.type == 1 ? '开通结果' : '欢迎光临';
    },
    mounted() {},
    watch: {},
    computed: {},
};
</script>

<style scoped lang="scss">
.result-content {
    width: 100%;
    img {
        width: 130px;
        height: 130px;
        margin: 0 auto;
        display: block;
    }
    div {
        font-size: 16px;
        text-align: center;
    }
    .message {
        margin-top: 30px;
        padding: 0 16px;
    }
}

.bottom {
    display: flex;
    padding: 16px;
    justify-content: space-between;
    .span {
        font-size: 16px;
        font-weight: 500px;
        color: white;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        border-radius: 8px;
        // padding: 11px 40px;
        height: 44px;
        line-height: 44px;
        display: inline-block;
        width: 48%;
        text-align: center;
    }
    .left-span {
        background: #b5b5b7;
    }
    .right-span {
    }
    .complete {
        width: 100%;
    }
}
</style>
