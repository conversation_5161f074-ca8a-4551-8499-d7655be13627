
Component({
    properties: {
        authKey: {
            type: String,
            default: '',
        },
    },
    methods: {
        success(res) {
            console.log('plugin onSuccess:', res);
            this.triggerEvent('success', res.detail);
        },
        fail(err) {
            console.log('plugin onFail:', err);
            this.triggerEvent('fail', err.detail);
        },
    },
});
// interface SuccessResp {
//     ret: number;
//     errMsg: string;
//     out_user_id: string;
// }

// interface FailResp {
//     statusCode: number;
//     errMsg: string;
// }
