import { POST } from '../../index';
//查询用户名下礼品卡数量接口
export const giftCardNumQuery = (params, config) => {
    return POST('account.giftCardNum.query', params, config);
};
// 查询用户名下礼品卡列表接口
export const giftCardList = (params, config) => {
    return POST('account.giftCard.list', params, config);
};
// 查看礼品卡券消费列表接口
export const giftCardConsumeList = (params, config) => {
    return POST('account.giftCard.consumeList', params, config);
};
// 绑定礼品卡接口
export const giftCardBind = (params, config) => {
    return POST('account.giftCard.bind', params, config);
};

// 查询用户名下购卡数量接口
export const buyGiftCardNumQuery = (params, config) => {
    return POST('account.buyGiftCardNum.query', params, config);
};
// [查看用户名下购卡列表接口]
export const buyGiftCardList = (params, config) => {
    return POST('account.buyGiftCard.list', params, config);
};
// [消费时查询用户名下可用礼品卡数量接口]
export const availableConsumeNumber = (params, config) => {
    return POST('account.giftCardNum.availableConsume.query', params, config);
};
// [5.1.44[消费时查询用户名下可用礼品卡列表接口]
export const consumeAvailableList = (params, config) => {
    return POST('account.giftCard.consume.availableList', params, config);
};
// 查询用户待领取礼品卡接口
export const unclaimedList = (params, config) => {
    return POST('account.giftCard.unclaimedList', params, config);
};

// 查询能源锦鲤数量接口
export const welfareCardsGetCount = (params, config) => {
    return POST('account.welfareCards.getCount', params, config);
};
// 分页查询能源锦鲤列表接口
export const welfareCardsQueryList = (params, config) => {
    return POST('account.welfareCards.queryList', params, config);
}; // 充值卡兑换礼品卡接口 /account/prepaidCard/exchangeTo/giftCard
export const exchangeToGiftCard = (params, config) => {
    return POST('account.prepaidCard.exchangeTo.giftCard', params, config);
};
