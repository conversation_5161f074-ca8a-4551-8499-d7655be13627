<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageView">
        <zj-navbar :border-bottom="false" title="订单明细"></zj-navbar>

        <div class="container">
            <!-- 待付款 直接跳转收银台-->
            <!-- <order-status-await v-if="queryParams.status === 1" :order="orderDetails" /> -->

            <!-- 已取消 -->
            <order-status-canceled v-if="queryParams.status == 5" :order="orderDetails" />

            <!-- 已退货 走能源订单详情-->
            <!-- <order-status-returned v-if="queryParams.status == 9" :order="orderDetails" /> -->

            <!-- 自提才显示 && 已完成-待取货\已取货 -->
            <order-status-completed
                v-if="orderDetails.orderSubType == 48 && (queryParams.status == 4 || queryParams.status == 8 || queryParams.status == 10)"
                :order="orderDetails"
                :order-status="queryParams.status"
            />

            <!-- 订单信息 -->
            <order-info-item
                :order="orderDetails"
                :order-status="queryParams.status"
                :delivery="deliveryInfoList"
                :deliveryStatus="deliveryStatus"
                :deliveryStatusVal="deliveryStatusVal"
            />

            <!-- 订单评价 -->
            <div class="btn">
                <button v-if="isComment" class="btn-item comment" @click="handleComment">去评价</button>
                <button
                    v-if="orderDetails.orderSubType == 49 && deliveryStatusVal == 50"
                    class="btn-item invoice"
                    @click="confirmGoodsHandle"
                    >包裹签收</button
                >
                <button
                    v-if="orderDetails.invoiceFlag == '0' && queryParams.status == 4"
                    class="btn-item invoice"
                    @click="handleInvoice"
                >
                    去开票
                </button>
                <button v-if="orderDetails.invoiceFlag == '1'" class="btn-item invoice" @click="seeInvoice">查看发票</button>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { detail, getDeliveryInfo, confirmGoods } from '../../../../s-kit/js/v3-http/https3/o2o/index';
import { orderCommentFlag } from '../../../../s-kit/js/v3-http/https3/order/index';
import { beforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { getInvoiceByOrderNoApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import OrderStatusAwait from '../../components/flk-components/order-status-await.vue';
import OrderStatusCanceled from '../../components/flk-components/order-status-canceled.vue';
import OrderStatusCompleted from '../../components/flk-components/order-status-completed.vue';
import OrderStatusReturned from '../../components/flk-components/order-status-returned.vue';
import OrderInfoItem from '../../components/flk-components/order-info-item.vue';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { OrderStatusAwait, OrderStatusCanceled, OrderStatusCompleted, OrderStatusReturned, OrderInfoItem },
    data() {
        return {
            orderDetails1: {
                pickUpCode: '',
                consignee: '',
                consigneePhone: '',
                licensePlate: '',
                expectPickUpTime: '',
                pickUpStatus: '',
                stationCode: '',
                payConfirmationTime: '',
                pickUpTime: '',
                orderNo: '',
                payChannel: [],
                payDiscountTotal: '',
                businessDay: '',
                discountTotalAmount: '',
                orderItems: [],
                code: '',
                o2oName: '',
                longitude: '',
                latitude: '',
                o2oBusinessStartTime: '',
                o2oBusinessEndTime: '',
                payItems: [],
                discountList: [],
                actualPayTotalAmount: [],
                receivedTotalAmount: '',
                orderTotalAmount: '',
                invoiceFlag: null,
                createTime: '',
                actualTotalRefundAmount: '',
                stationStatus: '',
                deliveryMoney: '', // 配送费用
                performanceNo: '', // 履约订单号，和订单号不同
                hosCode: '', // hos编码
                actualPayTotalAmount: '', // 实际支付金额
            },
            orderDetails: {},

            // 物流信息
            deliveryInfoList: [],
            deliveryStatus: '',
            deliveryStatusVal: 0, //0:失败" 1:"待接单" 20:"已接单" 30:"已拣货" 40:"待发货" 45:"待揽收" 50:"已发货" 60:"已签收" 70:"售后中" 80:"售后完成" 90:"已取消"

            // ToDo O2O订单详情只展示（已取消、已完成、待取货|待收货）
            // 状态（待支付=1，已完成-已取货=4，已取消=5，外送-待收货=10，自提-待提货=8，已退款=9）
            // todo status-状态（待支付=1，已完成=4，已取消=5，自提-待提货=8，已退款=9,外送-待收货=10）
            // todo O2O配送订单状态100=待接单状态; 201=业务员接单状态; 202=业务员到达状态; 300=配送中状态; 401=正常签收状态; 402=退回签收状态; 500=取消状态; 600=暂未找到合适的配送;
            queryParams: {
                // orderNo: '', // 订单编号
                // stationCode: '', // 网点编码
                // o2oOrderStatus: 100, // O2O配送订单状态
                // status: 8,
            },
            isComment: false, // 是否能评价
        };
    },
    onLoad(options) {
        let queryInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
        this.queryParams = { ...queryInfo };
        console.log('this.queryParams----', this.queryParams);
        this.getDetails(this.queryParams);
    },

    onShow() {},
    methods: {
        /**
         * @description: 去评价
         * @return {*}
         */
        handleComment() {
            let url = '/packages/third-evaluate/pages/home/<USER>';
            this.orderDetails.orderType = '2';
            let params = {
                evaluateType: 'o2oOrder',
                isOil: '1',
                ...this.orderDetails,
            };
            if (Object.keys(params).length) {
                url = `${url}?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
            }
            // this.$sKit.layer.cubeMini(url, '3815480475716653');
            this.$sKit.layer.useRouter(url, params);
        },

        /**
         * @description: 去开票
         * @return {*}
         */
        async handleInvoice() {
            let apiParams = {
                orderList: [
                    {
                        stationCode: this.orderDetails.stationCode,
                        businessDay: this.orderDetails.businessDay,
                        orderNo: this.orderDetails.orderNo,
                    },
                ],
            };
            let res = await beforeMakeInvoiceCheckApi(apiParams, {
                handleErrorFn: () => {
                    // this.orderDetailPost();
                    //获取订单开票状态失败，则重新获取订单信息，刷新页面
                    this.getDetails(this.queryParams);
                },
            });

            if (res && res.success) {
                if (res.data.flag) {
                    let goods = this.orderDetails.orderItems
                        .map(goodsItem => {
                            return goodsItem.productName;
                        })
                        .join(',');

                    let url = '/packages/third-invoice/pages/invoice-form/main';
                    let params = {
                        type: 'invoice',
                        orderNoList: [this.orderDetails.orderNo],
                        checkAllAmount: this.orderDetails.actualPayTotalAmount,
                        createTime: this.orderDetails.createTime,
                        orgName: this.orderDetails.o2oName,
                        goods: goods,
                    };
                    if (Object.keys(params).length) {
                        url = `${url}?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
                    }
                    // this.$sKit.layer.cubeMini(url, '3815480475716653');
                    this.$sKit.layer.useRouter(url, params);
                }
            }
        },

        // 查看发票
        async seeInvoice() {
            let params = {
                orderNum: this.orderDetails.orderNo,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    if (Object.keys(params).length) {
                        url = `${url}?data=${encodeURIComponent(encodeURIComponent(JSON.stringify(params)))}`;
                    }
                    // this.$sKit.layer.cubeMini(url, '3815480475716653');
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                            }
                        },
                    });
                }
            }
        },
        // 包裹签收
        async confirmGoodsHandle() {
            const { stationCode, orderNo } = this.queryParams;
            let params = {
                stationCode,
                orderNo,
            };
            let res = await confirmGoods(params);
            if (res && res.success) {
                this.queryParams.status = 4;
                this.getDetails(this.queryParams);
            }
        },

        /**
         * @description: 查询O2O订单详情
         * @return {*}
         */
        async getDetails(queryParams) {
            const { orderNo, stationCode, status } = queryParams;
            let params = {
                orderNo,
                stationCode,
                status,
                isB2B2cOrder: true, //是否是B2C订单 true:是 false:不是B2C订单
            };
            let res = await detail(params);
            if (res && res.success) {
                // 状态为49，调用物流信息
                if (res.data.orderSubType == 49) {
                    this.getDeliveryInfo(this.queryParams);
                }

                // 截取时分，去掉秒
                // if (res.data.o2oBusinessStartTime && res.data.o2oBusinessEndTime) {
                //     res.data.o2oBusinessStartTime = res.data.o2oBusinessStartTime.slice(0, 5);
                //     res.data.o2oBusinessEndTime = res.data.o2oBusinessEndTime.slice(0, 5);
                // }
                if (stationCode.startsWith('1-A5401') || stationCode.startsWith('1-A6501')) {
                    res.data.o2oBusinessStartTime = '10:00';
                    res.data.o2oBusinessEndTime = '20:00';
                } else {
                    res.data.o2oBusinessStartTime = '8:00';
                    res.data.o2oBusinessEndTime = '18:00';
                }

                // @ 解决后端老接口 orderSubType 未返回值问题
                if (!res.data.orderSubType) {
                    res.data.orderSubType = 48;
                }
                this.orderDetails = res.data;

                // @ 只有已完成订单才能评价 待备货、待提货、已取消订单不能评价
                if (this.queryParams.status && this.queryParams.status == 4) {
                    this.getOrderCommentQueryApi(res.data);
                }

                // @ 构造地图参数
                let mapParams = {
                    o2oOrderStatus: this.queryParams.o2oOrderStatus, // 这个值应该是响应式的，或者从 props 中获取
                    performanceNo: res.data.performanceNo,
                    longitude: res.data.longitude,
                    latitude: res.data.latitude,
                    // longitude: 104.068284,
                    // latitude: 30.658925,
                };
                this.$store.dispatch('mapInfoAction', mapParams);
            }
        },

        /**
         * @description: 查询物流信息
         * @return {*}
         */
        async getDeliveryInfo(queryParams) {
            let params = {
                orderNo: queryParams.orderNo,
            };
            let res = await getDeliveryInfo(params);
            if (res && res.success) {
                this.deliveryInfoList = res.data.deliveryInfoList || [];
                this.deliveryStatus = res.data.deliveryStatus;
                this.deliveryStatusVal = res.data.deliveryStatusVal;
            }
        },

        /**
         * @description: 查询订单评价状态
         * @param {*} data
         * @return {*}
         */
        async getOrderCommentQueryApi(data) {
            let params = {
                orderNo: this.queryParams.orderNo, // 订单编号
                stationCode: this.queryParams.stationCode, // 油站编码
                payAmount: data.actualPayTotalAmount, // 支付金额
                orderType: 2, // 订单类型
                orderSubType: data.orderSubType || 48, // 订单子类型
                // createTime: data.pickUpTime, // 订单创建时间(yyyy-MM-dd HH:mm:ss)
                createTime: data.createTime, // 订单创建时间(yyyy-MM-dd HH:mm:ss)
            };
            let res = await orderCommentFlag(params);
            if (res && res.success) {
                this.isComment = res.data.commentFlag == 1 ? true : false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.pageView {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f7fb;
}

.container {
    padding: 10px 15px;
}

.btn {
    margin: 20px 0;
    display: flex;
    justify-content: flex-end;

    &-item {
        // flex: 1;
        width: 50%;
        height: 45px;
        font-size: 16px;
        font-weight: 400;
        line-height: 45px;
        border-radius: 10px;
    }

    .comment {
        color: #e64f22;
        border: 1px solid #e64f22;
        background-color: #ffffff;
    }

    .invoice {
        color: #ffffff;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }

    /* 当有两个按钮时，给第一个按钮添加右边距 */
    .btn-item:first-child:nth-last-child(2) {
        margin-right: 12px;
    }
}
</style>
