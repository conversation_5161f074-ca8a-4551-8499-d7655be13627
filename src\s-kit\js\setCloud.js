// 定义 setCloud 函数，用于设置云相关配置并获取系统信息
export function setCloud(
  // 可选参数，appletStyle 默认为 'black'，表示黑色主题
  appletStyle = 'white',
  // 可选参数，navBackgroundColor 默认为黑色的十六进制值
  navBackgroundColor = "0xFFFFFFFF",
  // 可选参数，appletTitleBarVisible 默认为 false，表示标题栏不可见
  appletTitleBarVisible = false,
  // 可选参数，backBtnVisible 默认为 false，表示返回按钮不可见
  backBtnVisible = false
) {
  // 返回一个 Promise 对象，用于处理异步操作
  return new Promise((resolve, reject) => {
    try {
      // 检查 upsdk 是否存在以及 pluginReady 方法是否可用
      if (!upsdk || typeof upsdk.pluginReady !== 'function') {
        // 如果不存在或不可用，抛出错误并拒绝 Promise
        reject(new Error('upsdk or upsdk.pluginReady is not available.'));
        return;
      }
      // 调用 upsdk 的 pluginReady 方法，在插件准备好后执行回调函数
      upsdk.pluginReady(() => {
        try {
          // 检查 upsdk 的 setTitleStyle 方法是否可用
          if (typeof upsdk.setTitleStyle !== 'function') {
            // 如果不可用，抛出错误并拒绝 Promise
            reject(new Error('upsdk.setTitleStyle is not available.'));
            return;
          }
          console.log('appletTitleBarVisible', Number(appletTitleBarVisible))
          console.log('backBtnVisible', Number(backBtnVisible))
          // 调用 upsdk 的 setTitleStyle 方法，设置标题栏样式
          upsdk.setTitleStyle({
            appletStyle,
            navBackgroundColor,
            appletTitleBarVisible: Number(appletTitleBarVisible) || 0,
            backBtnVisible: Number(backBtnVisible) || 0
          });
          // 检查 upsdk 的 getSystemInfo 方法是否可用
          if (typeof upsdk.getSystemInfo !== 'function') {
            // 如果不可用，抛出错误并拒绝 Promise
            reject(new Error('upsdk.getSystemInfo is not available.'));
            return;
          }
          // 调用 upsdk 的 getSystemInfo 方法，获取系统信息
          upsdk.getSystemInfo({
            success: (dataSystemInfo) => {
              console.log(dataSystemInfo, 'dataSystemInfo---');
              // 成功获取系统信息后，解析 Promise 并传递信息
              resolve(dataSystemInfo);
            },
            fail: (error) => {
              // 获取系统信息失败时，拒绝 Promise 并传递错误信息
              reject(new Error(`Failed to get system info: ${JSON.stringify(error)}`));
            }
          });
        } catch (error) {
          // 捕获并处理代码执行过程中的错误，拒绝 Promise 并传递错误信息
          reject(new Error(`Error in upsdk operations: ${error.message}`));
        }
      });
    } catch (error) {
      // 捕获并处理外层代码执行过程中的错误，拒绝 Promise 并传递错误信息
      reject(new Error(`General error: ${error.message}`));
    }
  });
}