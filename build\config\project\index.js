/**
 * 此文件用于项目的配置
 * 配置说明
 * _  String  项目描述
 * name String 项目名称
 * appId String 小程序的唯一标识
 * cubeMiniAppId cube小程序跳转所需要的appid
 * baseUrl2App String app2.0接口域名
 * baseImgUrl 图片文件路径
 * baseUrl String 网络请求域名
 * baseMallUrl 商城订单图片
 * mainPackages Array 主包项目目录
 * subPackages Array 分包项目目录
 * app Object 小程序项目配置
 * baseType 请求环境 dev/qas/sit
 * permission 微信地理位置获取
 * plugins 插件管理
 * easycom 全局组件引入
 * workspaceid 网关环境判断 sit/product
 * minVersion  原生app最小版本号，做兼容性处理
 */
const { requireMkSync } = require('../utils');

const cnpcFuelMiniDev = requireMkSync('./cnpc-fuel-mini-dev.js');
const cnpcFuelMiniSit = requireMkSync('./cnpc-fuel-mini-sit.js');
const cnpcFuelMiniGray = requireMkSync('./cnpc-fuel-mini-gray.js');
const cnpcFuelMiniPrd = requireMkSync('./cnpc-fuel-mini-prd.js');

const cnpcMyWebMiniDev = requireMkSync('./cnpc-my-webmini-dev.js');
const cnpcMyWebMiniSit = requireMkSync('./cnpc-my-webmini-sit.js');
const cnpcMyWebMiniGray = requireMkSync('./cnpc-my-webmini-gray.js');
const cnpcMyWebMiniPrd = requireMkSync('./cnpc-my-webmini-prd.js');

const cnpcFuelWebMiniDev = requireMkSync('./cnpc-fuel-webmini-dev.js');
const cnpcFuelWebMiniSit = requireMkSync('./cnpc-fuel-webmini-sit.js');
const cnpcFuelWebMiniGray = requireMkSync('./cnpc-fuel-webmini-gray.js');
const cnpcFuelWebMiniPrd = requireMkSync('./cnpc-fuel-webmini-prd.js');

const cnpcOrderWebMiniDev = requireMkSync('./cnpc-order-webmini-dev.js');
const cnpcOrderWebMiniSit = requireMkSync('./cnpc-order-webmini-sit.js');
const cnpcOrderWebMiniGray = requireMkSync('./cnpc-order-webmini-gray.js');
const cnpcOrderWebMiniPrd = requireMkSync('./cnpc-order-webmini-prd.js');

const zfbDev = requireMkSync('./zfb-dev.js');
const zfbSit = requireMkSync('./zfb-sit.js');
const zfbGray = requireMkSync('./zfb-gray.js');
const zfbPrd = requireMkSync('./zfb-prd.js');

const wxDev = requireMkSync('./wx-dev.js');
const wxSit = requireMkSync('./wx-sit.js');
const wxGray = requireMkSync('./wx-gray.js');
const wxPrd = requireMkSync('./wx-prd.js');
const subWxTest = requireMkSync('./sub-wx.js');

const ttDev = requireMkSync('./tt-dev.js');

const ttSit = requireMkSync('./tt-sit.js');
const cloudpaySit = requireMkSync('./cnpc-cloudpay-sit.js');
const cloudpayGray = requireMkSync('./cnpc-cloudpay-gray.js');
const cloudpayPrd = requireMkSync('./cnpc-cloudpay-prd.js');

const pinganYqbDev = requireMkSync('./cnpc-pingan-yqb-dev.js');
const pinganYqbSit = requireMkSync('./cnpc-pingan-yqb-sit.js');
const pinganYqbGray = requireMkSync('./cnpc-pingan-yqb-gray.js');
const pinganYqbPrd = requireMkSync('./cnpc-pingan-yqb-prd.js');

const pinganHczSit = requireMkSync('./cnpc-pingan-hcz-sit.js');

const pinganKdyhSit = requireMkSync('./cnpc-pingan-kdyh-sit.js');



// 2.0环境所需页面
const v2ProSubPack = [
    'setting',
    'avatarNickname',
    'contract',
    'coupon',
    'ecard-apply',
    'invoice-center',
    'location',
    'message',
    'my-center',
    'oil-card',
    'order',
    'password',
    'place-order',
    'register',
    'transferAccount',
    'venicle-set',
    'version',
];
// 3.0 重构环境所需页面
const v3ProSubPack = [
    'third-oil-card',
    'third-order',
    'third-skin-replacement',
    'third-remaining-sum',
    'third-invoice',
    'third-electronic-wallet',
    'third-oil-charge-payment',
    'third-my-wallet',
    'third-preferential-group',
    'third-group-master',
    'third-integral',
    'third-message',
    'third-class-interest',
    'third-scan-code-payment',
    'third-login',
    'third-my-center',
    'third-coupon-module',
    'third-hospitality-coin',
    'third-evaluate',
    'third-fastIn',
    'web-view',
    'third-gift-card',
    'third-rfid',
    'third-questionnaire',
    'third-account',
    'third-discount-bill',
    'third-robotrefuel',
    'third-purchase-bulk-oil',
];

// 微信、支付宝小程序3.0 重构环境所需页面
const miniV3ProSubPack = [
    'third-oil-card',
    'third-order',
    'third-skin-replacement',
    'third-remaining-sum',
    'third-invoice',
    'third-electronic-wallet',
    'third-oil-charge-payment',
    'third-my-wallet',
    'third-preferential-group',
    'third-group-master',
    'third-integral',
    'third-message',
    'third-class-interest',
    'third-scan-code-payment',
    'third-my-center',
    'third-coupon-module',
    'third-hospitality-coin',
    'web-view',
    'third-evaluate',
    'third-rfid',
    'third-gift-card',
    'third-account',
    'third-discount-bill',
    'third-robotrefuel',
    'thirdMarketing',
    'third-purchase-bulk-oil',
    'third-charge',
];

const onlyWxSubPack = ['union@main', 'home@main', 'third-new-third-login', 'third-helpShare'];
const onlyCloudSubPack = ['third-cloud-login'];
const openSubpack = [
    'third-oil-card',
    'third-order',
    'third-skin-replacement',
    'third-remaining-sum',
    'third-invoice',
    'third-electronic-wallet',
    'third-oil-charge-payment',
    'third-my-wallet',
    'third-preferential-group',
    'third-group-master',
    'third-integral',
    'third-message',
    'third-class-interest',
    'third-scan-code-payment',
    'third-my-center',
    'third-coupon-module',
    'third-hospitality-coin',
    'web-view',
    'third-evaluate',
    'third-rfid',
    'third-gift-card',
    'third-account',
    'third-discount-bill',
    'setting',
];

// h5打包静态资源所需的3.0页面
const staticV3ProSubPack = [
    'third-oil-card',
    'third-order',
    'third-skin-replacement',
    'third-remaining-sum',
    'third-invoice',
    'third-electronic-wallet',
    'third-oil-charge-payment',
    'third-my-wallet',
    'third-preferential-group',
    'third-group-master',
    'third-integral',
    'third-message',
    'third-class-interest',
    'third-scan-code-payment',
    'third-my-center',
    'third-coupon-module',
    'third-hospitality-coin',
    'third-fastIn',
    'web-view',
    'third-evaluate',
    'thirdHome@main',
    'third-gift-card',
    'third-rfid',
    'third-questionnaire',
    'third-new-third-login',
    'third-account',
    'third-discount-bill',
    'third-robotrefuel',
    'third-purchase-bulk-oil',
];

const subWxpage = ['third-integral'];
const ttPage = ['setting'];
// const gysy = ['ecard-apply']
// 2.0项目与3.0项目合并（需要雷总协调放静态文件图片 的服务器）
const mergeProSubPack = [...v2ProSubPack, ...miniV3ProSubPack, ...onlyWxSubPack];
const staticMergeProSubPack = [...v2ProSubPack, ...staticV3ProSubPack];
const mainPackages = ['thirdHome'];
const noV2mainPackages = ['thirdHome'];
const subPackthirdHome = [...miniV3ProSubPack, 'thirdHome@main'];
const subPackages = v3ProSubPack;
// 此处创建类是为了更好的扩展
class Project {
    constructor(data) {
        /**
         * mirror 文件镜像 会从入口读取，导出到出口位置
         * entry 入口配置
         * output 出口配置
         */
        // const mainPath = '../../../src/pages/'
        // data.mirror = {
        //   entry: {
        //     cube_my: path.resolve(__dirname, `${mainPath}thirdHome/components/third-personal/main.vue`),
        //     cube_oil: path.resolve(__dirname, `${mainPath}thirdHome/components/third-oil/main.vue`),
        //     cube_order: path.resolve(__dirname, `${mainPath}thirdHome/components/third-order/main.vue`),
        //   },
        //   output: {
        //     cube_my: path.resolve(__dirname, `${mainPath}cube-my/main.vue`),
        //     cube_oil: path.resolve(__dirname, `${mainPath}cube-oil/main.vue`),
        //     cube_order: path.resolve(__dirname, `${mainPath}cube-order/main.vue`),
        //   }
        // }
        this.config = data;
    }
}

module.exports = [
    new Project({ ...cnpcFuelMiniDev, mainPackages: noV2mainPackages, subPackages: [...subPackages] }),
    new Project({ ...cnpcFuelMiniSit, mainPackages: noV2mainPackages, subPackages: [...subPackages] }),
    new Project({ ...cnpcFuelMiniGray, mainPackages: noV2mainPackages, subPackages: [...subPackages] }),
    new Project({ ...cnpcFuelMiniPrd, mainPackages: noV2mainPackages, subPackages: [...subPackages] }),

    new Project({ ...cnpcMyWebMiniDev, mainPackages: ['cube-my'], subPackages: [] }),
    new Project({ ...cnpcMyWebMiniSit, mainPackages: ['cube-my'], subPackages: [] }),
    new Project({ ...cnpcMyWebMiniGray, mainPackages: ['cube-my'], subPackages: [] }),
    new Project({ ...cnpcMyWebMiniPrd, mainPackages: ['cube-my'], subPackages: [] }),

    new Project({ ...cnpcFuelWebMiniDev, mainPackages: ['cubeOil'], subPackages: [] }),
    new Project({ ...cnpcFuelWebMiniSit, mainPackages: ['cubeOil'], subPackages: [] }),
    new Project({ ...cnpcFuelWebMiniGray, mainPackages: ['cubeOil'], subPackages: [] }),
    new Project({ ...cnpcFuelWebMiniPrd, mainPackages: ['cubeOil'], subPackages: [] }),

    new Project({ ...cnpcOrderWebMiniDev, mainPackages: ['cube-order'], subPackages: [] }),
    new Project({ ...cnpcOrderWebMiniSit, mainPackages: ['cube-order'], subPackages: [] }),
    new Project({ ...cnpcOrderWebMiniGray, mainPackages: ['cube-order'], subPackages: [] }),
    new Project({ ...cnpcOrderWebMiniPrd, mainPackages: ['cube-order'], subPackages: [] }),

    new Project({ ...wxDev, mainPackages: ['thirdHome'], subPackages: mergeProSubPack }),
    new Project({ ...wxSit, mainPackages: ['thirdHome'], subPackages: mergeProSubPack }),
    new Project({ ...wxGray, mainPackages: ['thirdHome'], subPackages: mergeProSubPack }),
    new Project({ ...wxPrd, mainPackages: ['thirdHome'], subPackages: mergeProSubPack }),
    new Project({ ...subWxTest, mainPackages: ['thirdHome'], subPackages: subWxpage }),

    new Project({ ...zfbDev, mainPackages: ['union'], subPackages: subPackthirdHome }),
    new Project({ ...zfbSit, mainPackages: ['union'], subPackages: subPackthirdHome }),
    new Project({ ...zfbGray, mainPackages: ['union'], subPackages: subPackthirdHome }),
    new Project({ ...zfbPrd, mainPackages: ['union'], subPackages: subPackthirdHome }),

    new Project({ ...ttDev, mainPackages: noV2mainPackages, subPackages: mergeProSubPack }),
    new Project({ ...ttSit, mainPackages: noV2mainPackages, subPackages: mergeProSubPack }),
    new Project({ ...pinganYqbDev, mainPackages: noV2mainPackages, subPackages: openSubpack }),
    new Project({ ...pinganYqbSit, mainPackages: noV2mainPackages, subPackages: openSubpack }),
    new Project({ ...pinganYqbGray, mainPackages: noV2mainPackages, subPackages: openSubpack }),
    new Project({ ...pinganYqbPrd, mainPackages: noV2mainPackages, subPackages: openSubpack }),

    new Project({ ...pinganHczSit, mainPackages: noV2mainPackages, subPackages: openSubpack }),

    new Project({ ...pinganKdyhSit, mainPackages: noV2mainPackages, subPackages: openSubpack }),

    new Project({ ...cloudpaySit, mainPackages: noV2mainPackages, subPackages: [...openSubpack, ...onlyCloudSubPack] }),
    new Project({ ...cloudpayGray, mainPackages: noV2mainPackages, subPackages: [...openSubpack, ...onlyCloudSubPack] }),
    new Project({ ...cloudpayPrd, mainPackages: noV2mainPackages, subPackages: [...openSubpack, ...onlyCloudSubPack] }),

    new Project({
        _: '处理静态图片',
        name: 'cnpc-test-h5',
        appId: 'wx368676e079814986',
        baseUrl: 'https://miniprotest.95504.net',
        baseImgUrl: 'https://oss-dev-app-soti.kunlunjyk.com',
        baseMallUrl: 'https://temall.95504.net/',
        baseUrl2App: 'https://jlapp.95504test.com',
        splicBaseUrl: 'https://jlapp.95504test.com:9080',
        mainPackages: ['home', 'thirdHome'],
        subPackages: staticMergeProSubPack,
        baseType: '',
        mixRecharge: 1, // 充值最低限制金额
        app: {
            globalStyle: {
                backgroundTextStyle: 'dark',
                navigationBarBackgroundColor: '#fff',
                navigationBarTextStyle: 'black',
                backgroundColor: '#F7F7FB',
                navigationStyle: 'custom',
                pullRefresh: false,
                defaultTitle: '',
                allowsBounceVertical: 'NO',
                transparentTitle: 'always',
                titlePenetrate: 'YES',
            },
            easycom: {
                'c-checkbox': '@/s-kit/components/layout/c-checkbox/c-checkbox.vue',
                'zj-old-account': '@/s-kit/components/layout/zj-old-account/index.vue',
                'zj-navbar': '@/s-kit/components/layout/zj-navbar/zj-navbar.vue',
                'zj-pull-down-refresh': '@/s-kit/components/layout/zj-pull-down-refresh/zj-pull-down-refresh.vue',
                'zj-data-list': '@/s-kit/components/layout/zj-data-list/zj-data-list.vue',
                'zj-no-data': '@/s-kit/components/layout/zj-no-data/index.vue',
                'zj-show-modal': '@/s-kit/components/layout/zj-show-modal/index.vue',
                'zj-unrealized-authentication': '@/s-kit/components/layout/zj-unrealized-authentication/index.vue',
                'zj-agreement': '@/s-kit/components/layout/zj-agreement/zj-agreement.vue',
                'zj-popup': '@/s-kit/components/layout/uni-popup/uni-popup.vue',
                '^zj-(.*)': '@/s-kit/components/layout/zj-$1/zj-$1.vue',
                '^u-(.*)': 'uview-ui/components/u-$1/u-$1.vue',
            },
            permission: {
                'scope.userLocation': {
                    desc: '您的位置信息将用于获取附近加油站',
                },
            },
            wxplugins: {
                'security-utils-plugin': {
                    version: '1.0.0',
                    provider: 'wx696812685f4f99e5',
                },
            },
            zfbplugins: {
                securityPlugin: {
                    version: '*',
                    provider: '2021003176627545',
                },
            },
        },
    }),
];
