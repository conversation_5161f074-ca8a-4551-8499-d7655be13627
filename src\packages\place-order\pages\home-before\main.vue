<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            :background="pageConfig.bgColor"
            back-text="确认订单"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <scroll-view
            scroll-y
            :style="{
                height: 'calc(100vh - ' + (isIphoneX ? navH + 34 : navH) + 'px - 64px);',
            }"
            refresher-enabled
            refresher-default-style="none"
            :refresher-triggered="isStartDown"
            @refresherrefresh="onRefreash"
        >
            <!-- 下拉刷新动画 -->
            <div slot="refresher" class="refresh-container">
                <img class="refresher-load" src="@/static/loading.png" />
                <div class="refresher-text">努力刷新中 ···</div>
            </div>
            <div class="content-view">
                <!-- 下拉刷新提示 -->
                <!-- <div class='load-tips-view' v-if='isTips'>
					<img class="load-tips" src="@/static/refrech-tip.png" mode="">
				</div> -->

                <!-- 油站选择 -->
                <!-- <div class='select-station-view' @click='clickReplaceStation'> -->
                <div class="select-station-view">
                    <img class="select-station-icon" src="@/static/cnpc-logo.png" mode="" />
                    <div class="station-text-view">
                        <div class="station-name">{{ selectMarker.stationName }}</div>
                        <div class="station-address">{{ selectMarker.address }}</div>
                    </div>
                    <!-- <div class='more-view'>
						<div class='more-text'>更换油站</div>
						<img class='more-icon' src="@/static/homeIcon/rightjt.png" mode="">
					</div> -->
                </div>
                <!-- 油枪or油品选择 -->
                <div class="select-yp-pop">
                    <div class="yp-title">选择油品</div>
                    <div class="select-list-view">
                        <div
                            :class="selectYPIndex == index ? 'yp-item yp-item-select' : 'yp-item'"
                            @click="clickPopYPItem(index)"
                            v-for="(item, index) in ypArr"
                            :key="index"
                            >{{ item }}</div
                        >
                    </div>
                </div>
                <div class="select-yp-pop">
                    <div class="yp-title">选择油枪</div>
                    <div class="select-list-view">
                        <div
                            @click="clickSelectYqCode(index)"
                            :class="selectYqCodeIndex == index ? 'yp-item yp-item-select' : 'yp-item'"
                            v-for="(item, index) in selectYqCodeArr"
                            :key="index"
                            >{{ item | setyqItem }}</div
                        >
                        <div @click="selectQtQq" :class="otherYqCodeIndex == 1 ? 'yp-item-select-other' : 'yp-item'">
                            <input
                                class="yqinput"
                                placeholder="其他"
                                maxlength="2"
                                v-model.trim="otherYqCode"
                                @input="oilotherYqInput"
                                @blur="oilotherYqHandle"
                                :placeholder-style="
                                    otherYqCodeIndex == 1 ? 'color:#ff8200;font-size:15px;' : 'color:#333333;font-size:15px;'
                                "
                                type="number"
                            />
                        </div>
                    </div>
                </div>
                <!-- 订单选择 -->
                <div class="yq-select-view">
                    <div
                        :class="index == selectYQIndex ? 'yq-item yq-item-select' : 'yq-item'"
                        v-for="(item, index) in showYQArr"
                        :key="index"
                        @click="clickYQItem(index)"
                    >
                        <div class="yq-item-name">{{ item.oilGun }}</div>
                        <div class="yq-item-rise">{{ item.quantity }}</div>
                        <div class="yq-item-price-title">加油金额：</div>
                        <div class="yq-item-price">{{ item.amount + (isInteger(item.amount) ? '.00' : '') }}</div>
                        <img
                            class="yq-item-image"
                            :src="index == selectYQIndex ? '/static/select-icon.png' : '/static/homeIcon/normal.png'"
                            mode=""
                        />
                    </div>
                    <div class="yq-more-view" v-if="isMore && showYQArr.length">
                        <div class="yq-more-text" @click="clickLookMore">查看更多</div>
                        <img class="yq-more-icon" src="@/static/homeIcon/bottomjt-h.png" @click="clickLookMore" />
                    </div>
                    <div class="yq-more-view" v-else>
                        <div class="yq-more-text">暂无更多订单</div>
                    </div>
                </div>
            </div>
        </scroll-view>
        <!-- 下方支付 -->
        <div class="pay-view">
            <template v-if="!downNum">
                <div class="pay-price-view">
                    <div :class="selectYQIndex != -1 ? 'pay-price-icon' : 'pay-price-icon pay-normal-color'">￥</div>
                    <div :class="selectYQIndex != -1 ? 'pay-price-text' : 'pay-price-text pay-normal-color'">
                        {{ payPrice + (isInteger(payPrice) ? '.00' : '') }}</div
                    >
                </div>
                <div :class="selectYQIndex != -1 ? 'pay-price-btn' : 'pay-price-btn pay-normal-bg'" @click="clickWxPay">确认订单 </div>
            </template>
            <template v-else>
                <div class="await-tip" style="margin-left: 15px">您有未支付的订单，</div>
                <div class="await-num">{{ downNum }}</div>
                <div class="await-tip" style="flex: 1">秒后失效</div>
                <div class="await-btn" @click="clickContinuePay">继续支付</div>
            </template>
        </div>

        <!-- 加载动画 -->
        <uni-pop ref="load" type="center" :maskClick="false">
            <!-- <div class='load-img' :style='"background-image: url(" + loadImage + "); width: 100px; height: 100px;"' mode=""></div> -->
            <img src="@/static/load.gif" style="width: 300px; height: 128px" mode="" />
        </uni-pop>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
let timer = null;
import { mapState } from 'vuex';
import uniPop from '@/components/uni-popup/uni-popup.vue';
import { getTradeList, getOilGunsByStationCode, preOrder, lockOrder, waitPayOrder } from '@/api/home.js';
import pageConfig from '@/utils/pageConfig.js';
import couponList from '@/components/coupon-list/coupon-list.vue';
import Config from '@/utils/config.js';

const TEST_DATA = {
    stationCode: 'KKKK',
    province: '北京市',
};

export default {
    components: {
        couponList,
        uniPop,
    },
    data() {
        return {
            /**********上方为假数据***********/
            isStartDown: false, // 是否开始刷新
            otherYqCode: '',
            yqArr: [
                {
                    amount: 0,
                },
            ], //油枪数据
            pageConfig: pageConfig, // 页面配置
            isShowSelectYP: false, // 是否展示油品弹窗
            ypArr: [], // 油品类型
            selectYPIndex: -1, // 选中的常用油品index
            selectYQIndex: -1, // 选中的油枪index
            payPrice: 0, // 最终价格
            isMore: false, // 是否显示展示更多
            showYQArr: [], // 油枪数据
            carData: {}, // 常用车辆
            yqCodeArr: [], // 油枪（所有油品对应的油枪）
            selectYqCodeArr: [], // 要展示的油枪数组
            selectYqCodeIndex: 0, // 油枪编号选中的index
            otherYqCodeIndex: '', //其他搜索油枪的index
            navH: 64,
            isIphoneX: false,
            // loadImage: '/static/net-loading.png',
            isFirstShow: false,
            isTips: true,
            downNum: 0, // 倒计时时间
        };
    },
    onUnload() {
        uni.$off('confirmBack');
        if (timer) {
            clearInterval(timer);
        }
    },
    async onLoad() {
        let systemInfo = uni.getSystemInfoSync();
        console.log(this.system);
        this.navH = 44 + systemInfo.statusBarHeight;
        let name = 'iPhone X';
        if (systemInfo.model.indexOf(name) > -1) {
            this.isIphoneX = true;
        }
        let strData = JSON.parse(decodeURIComponent(this.$mp.query.venicledata));
        this.carData = strData;
        // let res = await waitPayOrder()
        // @TODO 上线的时候需要f放开
        let ypRes = await getOilGunsByStationCode({
            stationCode: 'KKKK', //Config.testEnvironment ? TEST_DATA.stationCode : this.selectMarker.stationCode,
            // province: Config.testEnvironment ? TEST_DATA.province : this.province
        });
        if (!ypRes) {
            return this.$refs.load.close();
        }
        let ypArr = [];
        for (let key in ypRes.data) {
            ypArr.push(isNaN(Number(key)) ? key : key + '#');
        }

        this.ypArr = ypArr;
        this.yqCodeArr = ypRes.data;
        let findIndex = this.ypArr.findIndex(item => item == this.carData.oilNo);
        this.selectYPIndex = findIndex == -1 ? 0 : findIndex;
        this.selectYqCodeArr = this.yqCodeArr[this.ypArr[this.selectYPIndex].replace('#', '')];
        // if(res.data) {
        // 	this.downNum = res.data.remainTime
        // 	timer = setInterval(async () => {
        // 		this.downNum --
        // 		if(this.downNum == 0) {
        // 			clearInterval(timer)
        // 			await this.onLoadNetwork()
        // 		}
        // 	}, 1000)
        // 	this.payMode = res.data.payMode;
        // } else {
        // 	await this.onLoadNetwork()
        // }

        // 确认订单返回监听
        uni.$on('confirmBack', async params => {
            if (params) {
                const { downNum, payMode } = params;
                this.payMode = payMode;
                if (downNum) {
                    this.downNum = downNum;
                    timer = setInterval(async () => {
                        this.downNum--;
                        if (this.downNum == 0) {
                            clearInterval(timer);
                            this.$refs.load.open();
                            await this.getyqData();
                            this.$refs.load.close();
                            this.setYQArr();
                        }
                    }, 1000);
                }
            } else {
                this.downNum = 0;
                this.$refs.load.open();
                await this.getyqData();
                this.$refs.load.close();
                this.setYQArr();
            }
        });
    },
    filters: {
        setyqItem(item) {
            return item + '号枪';
        },
    },
    computed: {
        ...mapState({
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            province: state => state.location.province, // 选中的省份
        }),
    },
    methods: {
        // onload 网络请求
        async onLoadNetwork() {
            // 获取加载动画背景图
            // let base64 = uni.getFileSystemManager().readFileSync(this.loadImage, 'base64');
            // this.loadImage = 'data:image/png;base64,' + base64
            this.$refs.load.open();
            // let ypRes = await getOilGunsByStationCode({
            // 	stationCode: this.selectMarker.stationCode,
            // 	province: this.province
            // })
            await this.getyqData();
            this.$refs.load.close();
            this.setYQArr();
        },
        // 更换油站
        clickReplaceStation() {
            uni.navigateTo({
                url: '/packages/location/pages/home/<USER>',
            });
        },
        // 倒计时结束事件
        downEnd() {
            this.isStartDown = false;
        },
        // 下拉刷新
        async onRefreash() {
            this.isStartDown = true;
            this.isTips = false;
            this.$refs.load.open();
            await this.getyqData();
            this.$refs.load.close();
            this.setYQArr();
            this.isStartDown = false;
            this.isTips = true;
        },
        // 油枪选择点击事件
        async clickSelectYqCode(index) {
            this.otherYqCode = '';
            this.otherYqCodeIndex = '';
            this.selectYqCodeIndex = index;
            this.$refs.load.open();
            await this.getyqData();
            this.$refs.load.close();
            this.setYQArr();
            this.isShowSelectYP = false;
        },
        //选择其他输入油枪号搜索订单（切换样式）
        selectQtQq() {
            // this.otherYqCode
            this.selectYqCodeIndex = -1;
            this.otherYqCodeIndex = 1;
        },
        //输入油枪号失去焦点搜索订单
        async oilotherYqHandle() {
            if (this.otherYqCode != '' && this.otherYqCode != 0) {
                this.$refs.load.open();
                await this.getyqData();
                this.$refs.load.close();
                this.setYQArr();
            } else {
                this.otherYqCode = '';
            }
        },
        //处理其他按钮输入数据的格式
        oilotherYqInput(e) {
            let val = e.detail.value;
            // this.otherYqCode = e.detail.value
            let newVal = val.substring(0, 1);
            let newValTwo = e.detail.value.substring(1, 2);
            if (val == 0 || val == '') {
                this.otherYqCode = '';
            }
            if (newVal == 0) {
                if (newValTwo != '') {
                    //有第二位
                    this.otherYqCode = newValTwo;
                } else if (newValTwo == 0) {
                    this.otherYqCode = '';
                } else {
                    this.otherYqCode = newValTwo;
                }
            } else {
                this.otherYqCode = e.detail.value;
            }
        },
        // 获取油枪数据
        async getyqData() {
            // @TODO 上线的时候需要f放开
            // let res = await getTradeList({
            // 	stationCode: this.selectMarker.stationCode,
            // 	province: this.province,
            // 	oil: this.ypArr[this.selectYPIndex].replace('#', ''),
            // 	oilGun: this.selectYqCodeArr[this.selectYqCodeIndex],
            // })
            let res = await getTradeList({
                stationCode: 'KKKK', //Config.testEnvironment ? TEST_DATA.stationCode : this.selectMarker.stationCode,
                province: Config.testEnvironment ? TEST_DATA.province : this.province,
                oil: this.ypArr[this.selectYPIndex].replace('#', ''),
                oilGun: this.otherYqCode ? this.otherYqCode : this.selectYqCodeArr[this.selectYqCodeIndex],
            });

            this.yqArr = res ? res.data : [];
            // this.otherYqCode = ''
            if (this.yqArr.length == 0) {
                this.selectYQIndex = -1;
            } else {
                this.selectYQIndex = 0;
            }
        },
        // 判断是否是整数
        isInteger(obj) {
            if (obj === undefined) {
                return;
            }
            return Number.isInteger(obj);
        },
        // 设置油枪按钮
        setYQArr() {
            if (this.yqArr == '') {
                this.yqArr = [];
                this.showYQArr = this.yqArr;
            } else {
                if (this.yqArr.length > 3) {
                    this.isMore = true;
                    this.showYQArr = [this.yqArr[0], this.yqArr[1], this.yqArr[2]];
                } else {
                    this.showYQArr = this.yqArr;
                }
            }
            if (this.selectYQIndex == -1) return;
            this.setPrice();
        },
        // 设置金额方法
        setPrice() {
            this.payPrice = this.yqArr[this.selectYQIndex].amount;
        },
        // 油品选择点击事件
        async clickPopYPItem(index) {
            this.selectYPIndex = index;
            this.otherYqCode = '';
            this.otherYqCodeIndex = '';
            this.selectYqCodeIndex = 0;
            this.selectYqCodeArr = this.yqCodeArr[this.ypArr[this.selectYPIndex].replace('#', '')];
            this.$refs.load.open();
            await this.getyqData();
            this.$refs.load.close();
            this.setYQArr();
            this.isShowSelectYP = false;
        },
        // 弹起油品弹窗
        clickSelectYP(index) {
            this.isShowSelectYP = true;
        },
        // 关闭油品弹窗事件
        clickCloseYPPop() {
            this.isShowSelectYP = false;
        },
        // 有枪选择点击事件
        clickYQItem(index) {
            this.selectYQIndex = index;
            this.setPrice();
        },
        // 显示更多点击事件
        clickLookMore() {
            this.isMore = false;
            this.showYQArr = this.yqArr;
        },
        // 继续支付
        async clickContinuePay() {
            let res = await waitPayOrder();
            if (timer) {
                clearInterval(timer);
            }
            uni.navigateTo({
                url:
                    '/packages/place-order/pages/await-pay/main?data=' +
                    encodeURIComponent(JSON.stringify(res.data)) +
                    '&payMode=' +
                    this.payMode,
            });
        },
        // 点击确认订单 跳转
        async clickWxPay() {
            if (this.selectYQIndex == -1) return;
            let islock = await lockOrder({
                orderNo: this.yqArr[this.selectYQIndex].orderNo,
            });
            if (islock.status == -1) {
                return this.$util.showModal(islock.info, true);
            }
            let yqObj = this.yqArr[this.selectYQIndex];
            let param = {
                ...yqObj,
                carCode: this.carData.carNo,
                ypCode: this.ypArr[this.selectYPIndex],
            };
            if (timer) {
                clearInterval(timer);
            }
            uni.navigateTo({
                url: '/packages/place-order/pages/confirm-order-before/main?orderdata=' + JSON.stringify(param),
            });
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
}

.coupon-scroll-view {
    height: 285px;
    width: 100vw;
}

// 下拉刷新
.refresh-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45px;
    width: 100vw;
    .refresher-load {
        width: 25px;
        height: 25px;
        animation: refresh 2s linear;
        animation-iteration-count: infinite;
    }
    .refresher-text {
        margin-left: 5px;
    }
}
@keyframes refresh {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.content-view {
    overflow: hidden;
    .load-tips-view {
        display: flex;
        justify-content: center;
        margin-top: 10px;
        .load-tips {
            width: 159px;
            height: 25px;
        }
    }

    // 油站选择
    .select-station-view {
        display: flex;
        background-color: #ffffff;
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;
        overflow: hidden;
        border-radius: 5px;

        .select-station-icon {
            width: 30px;
            height: 30px;
            margin-left: 10px;
            margin-top: 10px;
        }

        .station-text-view {
            margin-top: 10px;
            flex: 1;
            margin-left: 10px;
            margin-bottom: 10px;
            overflow: hidden;

            .station-name {
                font-size: 15px;
                font-weight: 700;
                color: #333333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1;
            }

            .station-address {
                margin-top: 5px;
                font-size: 12px;
                color: #666666;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .more-view {
            display: flex;
            align-items: center;
            height: 42px;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 5px;
            padding-right: 10px;

            .more-text {
                font-size: 12px;
                color: #333333;
            }

            .more-icon {
                margin-left: 5px;
                height: 9px;
                width: 5px;
            }
        }
    }

    // 油品油枪选择
    .select-yp-pop {
        margin-top: 5px;

        .yp-title {
            margin-left: 15px;
            font-size: 15px;
            color: #333333;
            font-weight: 700;
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .select-list-view {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;

            .yp-item {
                box-sizing: border-box;
                margin-top: 5px;
                width: calc((100vw - 45px) / 4);
                margin-left: 5px;
                background-color: #ffffff;
                border: 1px solid #ffffff;
                text-align: center;
                border-radius: 5px;
                height: 43px;
                color: #333333;
                font-size: 15px;
                line-height: 43px;
            }

            .yp-item-select {
                background-color: $btn-mantle-color;
                border: 1px solid $btn-color;
                color: $btn-color;
                font-weight: 700;
            }
            .yp-item-select-other {
                background-color: $btn-mantle-color;
                border: 1px solid $btn-color;
                color: $btn-color;
                font-weight: 700;
                box-sizing: border-box;
                margin-top: 5px;
                width: calc((100vw - 45px) / 4);
                margin-left: 5px;
                text-align: center;
                border-radius: 5px;
                font-size: 15px;
            }
            .yqinput {
                height: 43px;
                line-height: 43px;
            }
        }
    }

    .yq-select-view {
        margin-top: 15px;

        .yq-item {
            margin-left: 15px;
            width: 344px;
            height: 49px;
            display: flex;
            margin-bottom: 10px;
            align-items: center;
            border-radius: 5px;
            border: 0.5rpx solid #ffffff;
            background-color: #ffffff;

            .yq-item-name {
                margin-left: 10px;
                font-size: 12px;
                color: #333333;
                font-weight: 700;
            }

            .yq-item-rise {
                flex: 1;
                font-size: 12px;
                color: #333333;
                text-align: center;
            }

            .yq-item-price-title {
                font-size: 12px;
                color: #333333;
            }

            .yq-item-price {
                font-size: 15px;
                color: #333333;
                font-weight: 700;
            }

            .yq-item-image {
                margin-left: 18px;
                margin-right: 10px;
                width: 20px;
                height: 20px;
                display: block;
            }
        }

        .yq-item-select {
            border: 0.5rpx solid $btn-color;
            background-color: $btn-mantle-color;
        }

        .yq-more-view {
            display: flex;
            top: 30px;
            width: 375px;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;

            .yq-more-text {
                color: #333333;
                font-size: 12px;
            }

            .yq-more-icon {
                padding-top: 10px;
                padding-bottom: 10px;
                width: 8px;
                height: 4px;
                margin-left: 11.5px;
                display: block;
            }
        }
    }
}

.pay-view {
    position: fixed;
    padding-bottom: env(safe-area-inset-bottom);
    width: 375px;
    background-color: #ffffff;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    .await-tip {
        font-size: 13px;
        color: #333333;
        font-weight: 700;
    }
    .await-num {
        font-weight: 700;
        font-size: 15px;
        color: $btn-color;
        margin-right: 2px;
    }
    .await-btn {
        background-color: $btn-color;
        height: 44px;
        width: 120px;
        text-align: center;
        line-height: 44px;
        margin-top: 10px;
        margin-bottom: 10px;
        color: #ffffff;
        font-size: 15px;
        margin-right: 15px;
        border-radius: 5px;
    }
    .pay-price-view {
        display: flex;
        align-items: flex-end;
        flex: 1;
        margin-left: 14px;

        .pay-price-icon {
            font-size: 15px;
            color: $btn-color;
            font-weight: 700;
            padding-bottom: 2px;
        }

        .pay-price-text {
            font-size: 25px;
            color: $btn-color;
            font-weight: 700;
        }

        .pay-normal-color {
            color: #909090;
        }
    }

    .pay-price-btn {
        background-color: $btn-color;
        height: 44px;
        width: 120px;
        text-align: center;
        line-height: 44px;
        margin-top: 10px;
        margin-bottom: 10px;
        color: #ffffff;
        font-size: 15px;
        margin-right: 15px;
        border-radius: 5px;
    }

    .pay-normal-bg {
        background-color: #909090;
    }
}
.load-img {
    background-size: 1200px 100px;
    animation: load 0.5s steps(1, end);
    animation-iteration-count: infinite;
}
@keyframes load {
    0% {
        background-position: -1200px 0;
    }
    8.33% {
        background-position: -100px 0;
    }
    16.66% {
        background-position: -200px 0;
    }
    24.99% {
        background-position: -300px 0;
    }
    33.33% {
        background-position: -400px 0;
    }
    41.66% {
        background-position: -500px 0;
    }
    49.99% {
        background-position: -600px 0;
    }
    58.33% {
        background-position: -700px 0;
    }
    66.66% {
        background-position: -800px 0;
    }
    74.99% {
        background-position: -900px 0;
    }
    83.33% {
        background-position: -1000px 0;
    }
    91.66% {
        background-position: -1100px 0;
    }
    100% {
        background-position: -1200px 0;
    }
}
</style>
