import cnpcBridge from '../v3-native-jsapi/cnpcBridge';
import commonUtil from '../commonUtil.js';
import checkFKArgs from '../v3-native-jsapi/checkFKArgs.js';
import qs from 'qs';
import Vue from 'vue';
import { getCppeiLoginInfo, logoutApi } from './https3/user.js';
const projectConfig = require('../../../../project.config');

// #ifdef MP-WEIXIN || H5-CLOUD
let Fly = require('flyio/dist/npm/wx');
import wxLogin from '../../../utils/login';
// #endif
// #ifdef MP-TOUTIAO
let Fly = require('./tt-fly.js');
// #endif
// #ifndef MP-WEIXIN || MP-TOUTIAO || H5-CLOUD
let Fly = require('flyio/dist/npm/ap');
// #endif

import { async } from 'q';
import store from '../../../store/index';
import layer from '../layer';
import accountCenter from '../v3-native-jsapi/accountCenter';

let fly = new Fly();
let customErr = ''; // 错误信息
let errCode = ''; // 错误号
let needLoadingCount = 0; // 当等于0的时候 请求接口的loading全部取消
let envName = ''; // 当前平台
let isLoad; // 是否显示loading标识
let isCustomErr; // 是否自定义显示错误信息
// #ifdef MP-ALIPAY
envName = 'al';
// #endif
// #ifdef MP-WEIXIN
envName = 'wx';
// #endif
// #ifdef MP-TOUTIAO
envName = 'tt';
// #endif
// #ifdef H5-CLOUD
envName = 'cloud';
// #endif
// 2.0皮肤列表
let notPromptingUrl = [
    '/app/json/app_third/skinDetail', //皮肤详情
    '/app/json/app_third/skinFree', //免费皮肤
    '/app/json/third/skinQueryFree',
    '/app/json/third/skinDetail',
    projectConfig.api + projectConfig.apiGsms + '/v1/skin/list/queryFree',
    projectConfig.api + projectConfig.apiGsms + '/v1/skin/list/detail',
];
let isIncludeUrl;
// #ifdef MP-MPAAS
envName = 'app';
let urlList = [
    '/app/json/app_third/skinDetail', //皮肤详情
    '/app/json/app_third/skinFree', //免费皮肤
    '/app/json/app_third/queryECardInfoByIdNo', //根据证件号查询电子卡信息
    '/app/json/card/recharge', // 油卡充值预下单
    '/app/json/app_third/rechargeByCard', //3.0钱包充值卡充值
    '/app/json/card/getKLPayOrderInfo', //昆仑充值
    '/app/json/card/getUnionPayOrder', //银联预下单
    '/app/json/refuel/queryCSBOrderList', //商城订单列表
    '/app/json/user/modifyPwd', //修改登录密码
    '/app/json/card/getCCBPay', // 建设银行
    '/app/json/card/rechargeByCard', //加油卡充值卡充值
    '/app/json/coupon/unusedcoupons', //电子券
    '/app/json/coupon/getUnusedDetail', //未使用电子券详情
    '/app/json/coupon/getUsedDetail', //已使用电子券详情
    '/app/json/app_third/makeRechargeInvoiceAsync', //在3.0给2.0充值订单开票
];
// #endif
// let apiListWx = [
//     '/app/json/third/cardCharge',
//     '/app/json/third/queryECardInfo',
//     '/app/json/login2/unbind',
//     '/app/json/third/rechargeInvoice',
//     '/app/json/order2/getRechargeList',
//     '/app/json/order2/getRechargeDetail',
//     '/app/json/cnpc_card/preOrder',
//     '/app/json/fuel/queryOrderList',
//     '/app/json/third/skinQueryFree',
//     '/app/json/third/skinDetail',
// ];
/**
 * @description  : 初始化报错回调
 * @return        {*}
 */
let handleErrorFn;

console.log('fly---', fly);
// #ifdef MP-WEIXIN
// 配置微信请求头
fly.config.headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    referrer: 1,
};
fly.config.baseURL = projectConfig.baseUrl;
// #endif
// #ifdef MP-ALIPAY
// 配置支付宝请求头
fly.config.headers = {
    'Content-Type': 'application/json',
};
fly.config.baseURL = projectConfig.baseUrl2Al;
// #endif
// #ifdef MP-MPAAS
//  配置APP请求头
fly.config.headers = {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    referrer: 'app',
};
fly.config.baseURL = projectConfig.baseUrl2App;
// #endif
// #ifdef MP-TOUTIAO
//  配置APP请求头
fly.config.headers = {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    referrer: 'app',
};
fly.config.baseURL = projectConfig.baseUrl2App;
// #endif
// #ifdef H5-CLOUD
//  配置APP请求头
fly.config.headers = {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    referrer: 'app',
};
fly.config.baseURL = projectConfig.baseUrl;
// #endif

// 设置超时时间
fly.config.timeout = 30000;
// 配置mpaas请求头
fly.interceptors.request.use(async config => {
    config.headers = {
        ...config.headers,
        UUID: envName + layer.getTYUUID('xxxxxxxxxxxxxxxx'),
    };
    handleErrorFn = () => {
        let _fn;
        if (typeof (_fn = config.handleErrorFn) == 'function') {
            _fn();
        }
    };
    // 找到第一个问号的位置
    let questionMarkIndex = config.url.indexOf('?');
    // 如果存在问号，则截取第一个问号前的部分
    let resultUrl = questionMarkIndex !== -1 ? config.url.substring(0, questionMarkIndex) : config.url;
    console.log(resultUrl, 'resultUrl=============');
    // 请求接口是否包含2.0皮肤接口
    isIncludeUrl = notPromptingUrl.includes(resultUrl);
    console.log(isIncludeUrl, notPromptingUrl, config.url, 'isIncludeUrl=======');
    // #ifdef MP-ALIPAY
    const { cppeiLoginInfo: userInfo, openId } = (await uni.getStorageSync('tokenInfo')) || {};
    if (!userInfo?.phoneToken) {
        let params = {
            openId: openId,
        };
        await getCppeiLoginInfoFn(params);
    }
    // #endif
    // #ifdef MP-WEIXIN
    let tokenInfo = uni.getStorageSync('tokenInfo');
    if (!tokenInfo.token) {
        let params = {
            openId: tokenInfo.openId,
        };
        await getCppeiLoginInfoFn(params);
    }
    // #endif
    // #ifdef H5-CLOUD
    let tokenInfo = uni.getStorageSync('tokenInfo');
    if (!tokenInfo.token) {
       // let params = {
       //     openId: tokenInfo.openId,
       // };
       await getCppeiLoginInfoFn(params);
    }
    // #endif
    // #ifdef MP-MPAAS
    let [userTokenInfo, commonArgs, args, locationInfo] = await Promise.all([
        // 获取用户信息
        cnpcBridge.getUserTokenInfo(),
        // 获取设备公共参数
        cnpcBridge.getCommonArgs(),
        // 获取风控参数
        checkFKArgs.getFKArgs2(config.url),
        // 获取位置信息
        checkFKArgs.getLocationInfo(),
    ]);
    if (!userTokenInfo.token) {
        await getCppeiLoginInfoFn();
        userTokenInfo = await cnpcBridge.getUserTokenInfo();
    }
    console.log(userTokenInfo.token, userTokenInfo, 'userTokenInfo.token=====');
    let jsonObj = JSON.parse(JSON.stringify(config.body));
    //增加省市 已经有省市的，不需要覆盖，没有的加上
    jsonObj.province = locationInfo.province || '';
    jsonObj.city = locationInfo.city || '';
    let requestData = {
        token: userTokenInfo.token,
        sysDeviceId: commonArgs.deviceId || '',
        timestamp: new Date().getTime(),
        nonce: layer.randomString(), // 字符串加密
        province: jsonObj.province,
        city: jsonObj.city,
    };
    //增加app系统标识
    config.headers.platform = commonArgs.os || '';
    //判断当前接口是不是需要加密
    let obj = urlList.find(item => config.url.indexOf(item) != -1);

    if (!!obj) {
        // bangbangEncrypt  邦邦加密
        let encryptStr = await cnpcBridge.bangbangEncrypt(JSON.stringify(Object.assign(args, jsonObj)));
        requestData.jsonData = encryptStr.data;
    } else {
        requestData.jsonData = JSON.stringify(Object.assign(args, jsonObj));
    }
    // 加签字符串
    let signStr = await cnpcBridge.dataAddSign(JSON.stringify(requestData));
    requestData.sign = signStr;
    config.body = qs.stringify(requestData);
    let time = new Date().getTime();
    if (config.url.indexOf('?') == -1) {
        config.url = config.url + '?ver=' + time;
    } else {
        config.url = config.url + '&ver=' + time;
    }
    // #endif

    // #ifndef MP-MPAAS
    // 非mpaas入参
    config.body = await layer.getNoMpaasPostBody(config);
    // #endif
    if (config.isPut) {
        delete config.body.token;
    }

    isLoad = config.isload;
    isCustomErr = config.isCustomErr;
    if (isLoad) {
        ShowLoading();
    }
    console.log(`---------2.0请求---------${config.url}------${config.headers.UUID}`, JSON.stringify(config));

    return config;
});
// 请求响应体
fly.interceptors.response.use(
    async (res, promise) => {
        console.log(`---------2.0响应---------${res.request.url}`, res);
        if (isLoad) {
            hideLoading();
        }
        let data = res.data;
        // #ifdef MP-MPAAS
        // 遍历当前接口是否是经过加密
        if (data.errorCode == 1000) {
            // 带业务类型的提示框 alert提示框
            cnpcBridge.businessTypeAlertDialog(data.info || '登录信息已过期,请重新登录', 'LoginExpired', res => {
                //退出登录
                commonUtil.logoutFun();
            });
        } else if (res.data.status === -1 && !isCustomErr) {
            customErr = res.data.info;
            errCode = res.data.errorCode;
            showError();
        }
        let obj = urlList.find(item => res.request.url.indexOf(item) != -1);
        if (!!obj) {
            if (data && data.data != null && data.data !== '') {
                // 梆梆解密
                let res = await cnpcBridge.bangBangDecrypt(data.data);
                data.data = JSON.parse(res.data);
                return data;
            }
        }
        // #endif
        // #ifdef MP-WEIXIN || MP-TOUTIAO
        // 登录过期或者未登
        if (res.data.errorCode === 1000) {
            let tokenInfo = uni.getStorageSync('tokenInfo');
            tokenInfo.token = '';
            uni.setStorageSync('tokenInfo', tokenInfo);
            customErr = res.data.info;
            errCode = 1000;
            showError();
        } else if (res.data.status === -1 && !isCustomErr) {
            let tokenInfo = uni.getStorageSync('tokenInfo');
            tokenInfo.token = '';
            uni.setStorageSync('tokenInfo', tokenInfo);
            customErr = res.data.info;
            errCode = res.data.errorCode;
            showError();
        }
        // #endif

        // #ifdef MP-ALIPAY
        console.log('res-error', res.data);
        if (typeof res.data === 'string') {
            data = JSON.parse(res.data);
        } else {
            data = res.data;
        }
        if (!data.Value) {
            customErr = data.Info;
            errCode = data.InfoCode;
            showError();
        }
        // #endif
        return data;
    },
    // 接收接口报错抛出的错误信息，并使用弹窗展示
    async error => {
        console.log('error-0-0', error);
        if (isLoad) {
            hideLoading();
        }
        customErr = error?.response?.data?.Info || '';
        errCode = error?.response?.data?.InfoCode || '';
        showError();
        return error.response.data;
    },
);
//登录失效弹窗
function handlerShowModal() {
    store.dispatch('zjShowModal', {
        content: '您的登录状态可能已失效，请尝试重新登录后使用此功能按钮',
        confirmText: '暂不退出',
        showCancel: true,
        confirmColor: '#333333',
        success: async res => {
            if (res.confirm) {
            }
            //  else if (res.cancel) {
            //     // #ifdef MP-WEIXIN
            //     uni.clearStorageSync();
            //     let logouRes = await logoutApi();
            //     uni.reLaunch({
            //         url: '/pages/thirdHome/main',
            //     });
            //     // #endif
            //     // #ifdef MP-MPAAS
            //     commonUtil.logoutFun();
            //     // #endif
            // }
        },
    });
}
async function getCppeiLoginInfoFn(params = {}) {
    console.log(isIncludeUrl, 'isIncludeUrl========');
    return new Promise(async (resolve, reject) => {
        try {
            let res = await getCppeiLoginInfo(params, { isCustomErr: true });
            console.log(res, 'getCppeiLoginInfo====');
            if (res.success) {
                if (res.data.cppeiLoginInfo) {
                    let userInfo2 = JSON.parse(res.data.cppeiLoginInfo);
                    // #ifdef MP-WEIXIN
                    let tokenInfo = await uni.getStorageSync('tokenInfo');
                    store.state.token = userInfo2.token;
                    const mergedObj = { ...tokenInfo, token: userInfo2.token, phone: userInfo2.token };
                    store.dispatch('storeLoginInformation', mergedObj);
                    uni.setStorageSync('tokenInfo', mergedObj);
                    console.log(mergedObj, '存储成功');
                    // #endif
                    // #ifdef MP-MPAAS
                    let userTokenInfo = await cnpcBridge.getUserTokenInfo();
                    let vuexObj = await cnpcBridge.getVuex2Info();
                    userTokenInfo.token = userInfo2.token;
                    vuexObj.login.token = userInfo2.token;
                    await setToken(userTokenInfo, vuexObj);
                    console.log(userTokenInfo, vuexObj, '存储成功');
                    // #endif
                    // #ifdef MP-ALIPAY
                    let userInfo = (await uni.getStorageSync('tokenInfo')) || {};
                    userInfo.cppeiLoginInfo = userInfo2;
                    uni.setStorageSync('tokenInfo', userInfo);
                    console.log(userInfo, '存储成功');
                    // #endif
                    resolve();
                } else {
                    if (!isIncludeUrl) {
                        handlerShowModal();
                    }
                    return;
                }
            } else {
                if (!isIncludeUrl) {
                    handlerShowModal();
                }
                return;
            }
        } catch (error) {
            if (!isIncludeUrl) {
                handlerShowModal();
            }
            return;
        }
    });
}
// 显示loading弹窗
function ShowLoading() {
    // if (needLoadingCount === 0) {
    uni.showLoading({
        title: '加载中',
        mask: true,
    });
    // }
    // needLoadingCount++;
}

function setToken(userTokenInfo, vuexObj) {
    return new Promise((resolve, reject) => {
        cnpcBridge.setValueToNative('UserTokenInfo', encodeURIComponent(JSON.stringify(userTokenInfo)), () => {
            cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
                resolve();
            });
        });
    });
}

// 隐藏loading弹窗
function hideLoading() {
    if (isLoad) {
        // if (needLoadingCount <= 0) {
        //     return;
        // }
        // needLoadingCount--;
        // throttle(() => {
        //     if (needLoadingCount === 0) {
        uni.hideLoading();
        // }
        // }, 0)();
    }
}
// 按钮的节流函数 防止重复点击
function throttle(func, delay) {
    let timer;
    return function (...args) {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
    timer = setTimeout(() => {
        func.apply(this, args);
    }, delay);
}

// 统一报错弹窗
function showError() {
    if (customErr) {
        store.dispatch('zjShowModal', {
            title: customErr,
            success: res => {
                if (res.confirm) {
                    // #ifdef MP-WEIXIN
                    if (errCode == 1000) {
                        layer.backHomeFun();
                        errCode = 0;
                        return;
                    }
                    // #endif
                    // #ifdef MP-ALIPAY
                    if (errCode == 10011 || errCode == 10012 || errCode == 10013 || errCode == 10017) {
                        my.clearStorageSync();
                        layer.backHomeFun();
                        return;
                    }
                    // #endif
                    //  #ifdef MP-TOUTIAO
                    if (errCode == 1000) {
                        layer.backHomeFun();
                        errCode = 0;
                        return;
                    }
                    // #endif
                    handleErrorFn();
                } else if (res.cancel) {
                }
            },
        });
    } else {
        store.dispatch('zjShowModal', {
            title: '服务繁忙,请稍后重试...',
            success: res => {
                if (res.confirm) {
                    handleErrorFn();
                } else if (res.cancel) {
                }
            },
        });
    }
    customErr = '';
}

export default fly;
