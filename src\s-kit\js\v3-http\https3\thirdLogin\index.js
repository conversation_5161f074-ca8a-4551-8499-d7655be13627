import { POST } from '../../index';
// 获取图片验证码
export const contentGetVerifySlideImage = (params, config) => {
    return POST('content.getVerifySlideImage', params, config);
};
// 获取短信验证码
export const messageCodeNoLoginSend = (params, config) => {
    return POST('content.messageCode.noLoginSend', params, config);
};
//用户登录/注册
export const userLogin = (params, config) => {
    return POST('user.login', params, config)
  }
// 更换设备进行人脸验证(获取拉起人脸的key)
export const initWechatFaceLoginByCode = (params, config) => {
    return POST('user.initWechatFaceLoginByCode', params, config)
}