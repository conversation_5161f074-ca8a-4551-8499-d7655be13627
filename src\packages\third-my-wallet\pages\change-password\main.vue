<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar title="修改支付密码" :border-bottom="false"></zj-navbar>
            <div class="f-1 bg-F7F7FB padding-16">
                <div class="fl-column fl-al-sta">
                    <div class="current border-rad-4" @click="showKeyboard2">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            newPasswordShow || '请输入新密码'
                        }}</div>
                    </div>
                    <div class="current border-rad-4" @click="showKeyboard3">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            confirmNewPasswordShow || '请确认新密码'
                        }}</div>
                    </div>
                </div>
                <div class="footer">
                    <div class="fl-row fl-al-cen fl-jus-bet footer-text">
                        <div class="font-12 color-333 fl-row fl-al-cen">
                            <img src="../../images/tips.png" alt />
                            支付密码为6位数字
                        </div>
                    </div>
                </div>
                <div class="btn primary-btn border-rad-8 shad-ef font-18 color-fff" @click="confirmBtn()">确认修改</div>
            </div>
            <zj-show-modal></zj-show-modal>
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-MPAAS -->
            <div v-if="isHarmony">
                <keyboard-plugin></keyboard-plugin>
            </div>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <cloudKeyBordPlugin ref="cloudKeyboardRef"></cloudKeyBordPlugin>
            <!-- #endif -->
        </div>
        <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';

import { passwordForget } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import { identityAuthInfo } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
// #ifndef MP-MPAAS || H5-CLOUD
import wxForgetPassword from './diff-environment/wx-forget-password.js';
import zfbForgetPassword from './diff-environment/zfb-forget-password.js';
// #endif
// #ifdef MP-MPAAS
import harmonyMixin from './diff-environment/harmony-forget-password.js';
// #endif
import { clientCode } from '../../../../../project.config.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
// #ifdef H5-CLOUD
import cloudForgetPassword from './diff-environment/cloud-forget-password.js';
import cloudKeyBordPlugin from '../../../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue'
// #endif
export default {
    name: 'change-payment-password',
    components: {
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin
        // #endif
    },
    props: {},
    mixins: [
        publicMixinsApi,
        // #ifndef MP-MPAAS || H5-CLOUD
        wxForgetPassword,
        zfbForgetPassword,
        // #endif
        // #ifdef MP-MPAAS
        harmonyMixin,
        // #endif
        // #ifdef H5-CLOUD
        cloudForgetPassword
        // #endif
    ],
    data() {
        return {
            fromInput: 'color:#999999;font-size:14px;',
            newPassword: '',
            newPasswordLength: '',
            newPasswordShow: '',
            confirmPassword: '',
            confirmNewPasswordLength: '',
            confirmNewPasswordShow: '',
            authInfo: '',
            passwordKeyboardRef: '',
            // 个人信息
            personInfo: {
                type: 2,
                // type: 11,
            },
            passWordOpenVal: {},
        };
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    onShow() { },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) this.refer = params.refer;
        }
        this.$sKit.mpBP.tracker('忘记支付密码', {
            seed: 'forgetPsdBiz',
            pageID: 'updatePsdPage', // 页面名
            refer: this.refer, // 来源
            channelID: clientCode, // C10/C12/C13
        });
        // 获取用户非脱敏信息
        this.getIdentityAuthInfoPost();
    },
    async mounted() {
        // #ifdef MP-MPAAS
        await this.$cnpcBridge.isCutScreen(true);
        // #endif
    },
    onReady() { },
    methods: {
        /**
         * @description  : 获取用户信息（证件号姓名，证件号未脱敏）
         * @return        {*}
         */
        async getIdentityAuthInfoPost() {
            let res = await identityAuthInfo();
            if (res.success) {
                this.personInfo.name = res.data.realName;
                this.personInfo.idNo = res.data.identityNo;
                console.log(this.personInfo, 'this.personInfo===忘记密码页面获取的个人信息');
            }
        },
        //新密码
        showKeyboard2() {
            // #ifdef MP-MPAAS || H5-CLOUD 
            /**
       * keyboardType 必填  number： 数字键盘  letter：字母键盘
          passwordType 必填  payment：支付密码；setup：设置密码；modify：修改密码
          numberPassword 非必填 数字密码的长度，最小为4最大为8，默认为4
          letterPassword 非必填 字母键盘密码长度 最小为4最大为25，默认4-25
          regular 非必填 字母键盘正则校验，只能验证类型，如数字字母特殊符号
          setText 非必填，默认文字为支付
          passwordInputShow 非必填，默认显示。 有且值为0时不显示
          mongolianlayer 非必填，蒙层。 有且值为1显示
       * 
       * */
            if (this.isHarmony || this.isH5CLOUD) {
                let params = {
                    keyboardType: 'number',
                    passwordType: 'setup',
                    numberPassword: 6,
                    setText: '支付',
                    mongolianlayer: 1,
                };
                // console.log('this.passwordKeyboardRef',this.passwordKeyboardRef)
                this.passwordKeyboardRef.openKeyboard(
                    params,
                    () => {
                        this.newPasswordShow = this.passwordKeyboardRef.getFirstLength();
                        this.confirmNewPasswordShow = this.passwordKeyboardRef.getSecondLength();
                        // console.log('pwdLength--',pwdLength)
                        // this.newPasswordShow = pwdLength
                    },
                    val => {
                        console.log('密码参数：', val);
                        this.newPassword = val.cipherText;
                        this.confirmPassword = val.cipherText;
                        this.passWordOpenVal = val;
                    },
                );
            }
            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD 
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                async pwd => {
                    this.newPasswordLength = await this.passwordKeyboardRef.getLength('password_unique1');
                    this.newPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    this.newPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                },
            );
            // #endif
        },
        //确认密码
        // #ifndef MP-MPAAS || H5-CLOUD 
        showKeyboard3() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique2',
                6,
                async pwd => {
                    this.confirmNewPasswordLength = await this.passwordKeyboardRef.getLength('password_unique2');
                    this.confirmPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique2');
                    this.confirmNewPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘2的关闭函数');
                },
            );
        },
        // #endif
        // 提交修改
        async submitForgotPassword(authInfo) {
            let params = {
                newPassword: this.newPassword,
                confirmPassword: this.confirmPassword,
                authInfo: authInfo,
            };
            if (this.isHarmony || this.isH5CLOUD) {
                params.deviceId = this.passWordOpenVal.deviceId;
                params.keyboardDataCacheId = this.passWordOpenVal.keyboardDataCacheId;
                params.uniqueId = this.passWordOpenVal.uniqueId;
            }

            let res = await passwordForget(params);
            if (res.success) {
                this.$sKit.mpBP.tracker('忘记支付密码', {
                    seed: 'forgetPsdBiz',
                    pageID: 'settingPsdsuccessToast', // 页面名
                    refer: this.refer, // 来源
                    channelID: clientCode, // C10/C12/C13
                });
                uni.showToast({
                    title: '修改支付密码成功',
                    duration: 2000,
                });
                setTimeout(() => {
                    // this.$store.commit('mSetRefuelingMigrationFlag', true);
                    this.$store.dispatch('getSetWalletStatus');
                    uni.navigateBack({
                        delta: this.detail ? 2 : 1, //返回层数，2则上上页
                    });
                }, 2000);
            }
        },
    },
    filter: {},
    watch: {},
    async beforeDestroy() {
        // #ifdef MP-MPAAS
        await this.$cnpcBridge.isCutScreen(false);
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        await this.$cnpcBridge.isCutScreen(false);
        // #endif
    },
};
</script>
<style scoped lang="scss">
.view {
    .current {
        width: 100%;
        height: 44px;
        margin-bottom: 16px;
        position: relative;

        .inputBox {
            height: 44px;
            line-height: 44px;
            padding-left: 15px;
        }

        input {
            width: 100%;
            height: 100%;
            padding-left: 15px;
        }

        img {
            position: absolute;
            right: 14px;
            top: 50%;
            z-index: 2;
            transform: translateY(-50%);
        }

        .close_div {
            width: 18px;
            height: 9px;
        }

        .open_div {
            width: 22px;
            height: 15px;
        }
    }

    .footer {
        .footer-text {
            margin-bottom: 12px;

            img {
                width: 15px;
                height: 15px;
                margin-right: 5px;
            }
        }
    }

    .btn {
        height: 44px;
        line-height: 44px;
        width: 100%;
        margin-top: 16px;
        margin-bottom: 12px;
    }
}

.show_model_div {
    text-align: center;
}
</style>

<style lang="scss">
.forgot_pas_inp {
    font-size: 14px;
    color: #999999;

    line-height: 20px;
}
</style>
