<template>
    <div>
        <div class="code-input-main">
            <div class="code-input-main-item" v-for="(item, index) in 6" :key="index">{{ code[index] || '' }}</div>
            <!-- #ifdef MP-WEIXIN -->
            <input class="code-input-input" ref="input" type="tel" inputmode="decimal" v-model="code" :maxlength="6" />
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO -->
            <input class="code-input-input" ref="input" maxlength="6" type="number" v-model="code" />
            <!-- #endif -->
        </div>
    </div>
</template>

<script>
export default {
    name: 'VueVercode',
    props: {},
    data() {
        return {
            code: '',
        };
    },
    created() {
        this.returnresultFun = this.$sKit.commonUtil.throttleUtil(this.returnresultFun, 500);
    },
    mounted() {},
    watch: {
        // 截取字符长度
        code: {
            handler: function (val, oldVal) {
                if (val.length == 6) {
                    this.returnresultFun();
                }
            },
        },
    },
    methods: {
        claerCode() {
            this.code = '';
        },
        returnresultFun() {
            this.$emit('returnresult', this.code);
        },
    },
};
</script>
<style scoped lang="scss">
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
}

.code-input-main {
    width: 100%;
    height: 54px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    position: relative;
    /* padding: 20px 0 0; */
    margin-top: 20px;
    overflow: hidden;
    /* margin-left: 5px; */
    /* transform: translateZ(0); */
}

.code-input {
    transform: translateZ(0);
}

.code-input-input {
    height: 100%;
    width: 120%;
    position: absolute;
    border: none;
    top: 0;
    left: -20%;
    outline: none;
    color: transparent;
    background-color: transparent;
    text-shadow: 0 0 0 transparent;
    caret-color: transparent;
    z-index: 9999;
    /* display: block;
  visibility: visible; */
}

.code-input-main-item {
    width: 49px;
    height: 48px;
    border-radius: 4px;
    background: #ffffff;
    line-height: 48px;
    margin-right: 5px;
    padding: 0;
    /* border: solid #eee 1px; */
    text-align: center;
    font-size: 18px;
    color: #333;
}
</style>
