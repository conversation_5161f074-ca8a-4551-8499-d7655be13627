<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="确认订单"></zj-navbar>
            <div class="f-1 bg-fff padding-16 p-bf mh-0">
                <zj-data-list
                    background="#F7F7FB"
                    ref="dataList"
                    :showEmpty="showEmpty"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrolltolower"
                >
                    <div
                        class="coupon-wrap fl-row"
                        v-for="(item, index) in couponList"
                        :key="index"
                        @click="ticketCellClickAction(item, index)"
                    >
                        <div class="select-img-wrap border-rad-8">
                            <img v-if="selectedCouponInfo.couponNo == item.couponNo" src="../../images/successSel.png" alt />
                            <img v-else src="../../images/empty.png" alt />
                        </div>
                        <div class="bg-coupon bg-F7F7FB border-rad-8">
                            <div class="upperLeft font-10 weight-500 color-fff te-center bg-ff6133" v-if="item.expRem">{{
                                getCouponType(item.expRem)
                            }}</div>
                            <div class="content-wrap fl-row">
                                <div class="left-wrap fl-row">
                                    <div class="content-left fl-column fl-al-cen">
                                        <div class="price fl-row fl-al-base">
                                            <div class="symbol font-14 color-E64F22 weight-400" v-if="item.couponType != 40">&yen;</div>
                                            <div class="font-28 color-E64F22 weight-400">{{ getCouponAmountStr(item) }} </div>
                                            <div v-if="item.couponType == 40" class="symbol font-14 color-E64F22 weight-400">折</div>
                                        </div>
                                        <div class="font-13 color-EB5130 weight-400">{{ getConditionStr(item) }}</div>
                                    </div>
                                    <div class="content-sx">
                                        <img src="../../images/line_used.png" alt />
                                    </div>
                                </div>
                                <div class="right-wrap">
                                    <div class="content-right">
                                        <div class="title font-15 color-1E1E1E weight-500">{{ item.couponTemplateName }} </div>
                                        <!-- <div class="fl-column expRem_div" v-if="item.expRem">即将到期</div> -->
                                        <div class="fl-column">
                                            <div class="time font-12 color-999 weight-400">有效期至：{{ item.endDate }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <div class="primary-btn btn" @click="chooseFinishAction" v-if="couponList.length > 0">确定</div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { clientCode } from '../../../../../project.config';
import { mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'choose-coupon',
    components: {},
    props: {},
    data() {
        return {
            // codeCouponArray: [],
            couponList: [{}],
            selectedCouponInfo: '',
            /*最后操作的券*/
            lastOperationCouponInfo: {},
            showEmpty: false,
            refer: '',
            bizContent: '',
        };
    },
    computed: {
        ...mapState({
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    created() {},
    mounted() {},
    onLoad(options) {
        // #ifdef MP-MPAAS
        let couponData
        if (this.isHarmony) {
            const nowPage = getCurrentPages().pop();
            couponData = getApp().globalData.query?.[nowPage.$page.fullPath];
            if (!couponData?.zfbRefererMax) {
                couponData = JSON.parse(decodeURIComponent(options.data));
            }
        } else {
            couponData = options ? JSON.parse(decodeURIComponent(options.data)) : ''; 
        }
        if (couponData.couponList.length > 0) {
            this.couponList = couponData.couponList;
            this.setDefaultSelectedCoupon();
        }
        if (couponData?.refer) {
            this.refer = couponData.refer;
        }
        if (couponData?.bizContent) {
            this.bizContent = couponData.bizContent;
        }
        // #endif
        // #ifndef MP-MPAAS
        let couponData = options ? JSON.parse(decodeURIComponent(options.data)) : '';
        if (couponData.couponList.length > 0) {
            this.couponList = couponData.couponList;
            this.setDefaultSelectedCoupon();
        }
        if (couponData?.refer) {
            this.refer = couponData.refer;
        }
        if (couponData?.bizContent) {
            this.bizContent = couponData.bizContent;
        }
        // #endif
        this.$sKit.mpBP.tracker('后支付加油', {
            seed: 'hpayoilBiz',
            pageID: 'selectCouponPage', // 返回sdk标识
            refer: this.refer || '',
            channelID: clientCode,
            content: this.bizContent,
            dataType: 'click',
            address: this.cityName,
        });
        setTimeout(() => {
            this.$refs.dataList.loadStatus = 'nomore';
        }, 300);
        
        // this.$nextTick(() => {
        //     this.$refs.dataList.loadStatus = 'nomore';
        // });
    },
    methods: {
        /**
         * @description  : 电子券类型
         * @return        {*}
         */
        getCouponType(val) {
            console.log(val, '电子券类型');
            // return val == 10 ? '满减券' : val == 20 ? '计次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
            return val ? '快过期' : '';
        },
        /**
         * @description  : 满减券展示形式
         * @return        {*}
         */
        getConditionStr(item) {
            if (item.useThresholdAmount) {
                let conditionStr = '满' + item.useThresholdAmount + '元可用';
                return conditionStr;
            } else {
                return '无金额门槛';
            }
        },
        /**
         * @description  : 获取优惠券金额
         * @return        {*}
         */
        getCouponAmountStr(item) {
            if (item.couponType == 10) {
                return item.couponAmount;
            } else if (item.couponType == 40) {
                return item.couponAmount;
            }
            return '';
        },
        /**
         * @description  : 匹配选中的优惠券
         * @return        {*}
         */
        setDefaultSelectedCoupon() {
            for (let i = 0; i < this.couponList.length; i++) {
                let item = this.couponList[i];
                if (item.used == 1) {
                    this.selectedCouponInfo = item;
                    this.lastOperationCouponInfo = JSON.parse(JSON.stringify(item));
                    break;
                }
            }
        },
        /**
         * @description  : 选择电子券
         * @return        {*}
         */
        ticketCellClickAction(item) {
            if (this.selectedCouponInfo.couponNo == item.couponNo) {
                this.selectedCouponInfo = {};
            } else {
                this.selectedCouponInfo = item;
            }
            //
            console.log('ticketCellClickAction', item);
            this.lastOperationCouponInfo = JSON.parse(JSON.stringify(item));
            console.log('lastOperationCouponInfo', this.lastOperationCouponInfo);
        },
        /**
         * @description  : 选择优惠券去顶点击事件
         * @return        {*}
         */
        chooseFinishAction() {
            let arr = [];
            console.log('000-selectedCouponInfo', this.selectedCouponInfo, this.couponList);

            if (Object.keys(this.selectedCouponInfo).length === 0) {
                //取消了选中优惠券
                arr.push({
                    couponNo: this.lastOperationCouponInfo.couponNo,
                    couponTemplateNo: this.lastOperationCouponInfo.couponTemplateNo,
                    lastSelected: 1,
                    direction: 0,
                });
                console.log('couponNo', arr);
                // 付款码订单页面是否执行onshow标识
                // this.$store.commit('setIsToSelectTicketPage', true)
                this.$store.dispatch('setIsToSelectTicketPageAction', true);
                // this.$store.dispatch('selectTicketsAction2', arr)
                this.$store.dispatch('selectTicketsAction', arr);
            } else if (this.selectedCouponInfo.used == 0) {
                //有选中的券
                arr.push({
                    couponNo: this.selectedCouponInfo.couponNo,
                    couponTemplateNo: this.selectedCouponInfo.couponTemplateNo,
                    lastSelected: 1,
                    direction: 1,
                });
                console.log('couponNo', arr);
                // 付款码订单页面是否执行onshow标识
                // this.$store.commit('setIsToSelectTicketPage', true)
                this.$store.dispatch('setIsToSelectTicketPageAction', true);
                // this.$store.dispatch('selectTicketsAction2', arr)
                this.$store.dispatch('selectTicketsAction', arr);
            }
            uni.navigateBack({
                delta: 1, //返回的页面数
            });
        },
        // selectCoupon (item, index) {

        //   if (this.codeCouponSelectIndex == index) return
        //   console.log(item, index, '扫码支付确认订单选择电子券');
        //   this.codeCouponSelectIndex = index
        //   // 选中返回上一层携带参数
        //   uni.navigateBack({
        //     delta: 1
        //   })
        // },
        // 上拉加载
        // scrolltolower () {
        //   console.log('上拉触底事件')
        //   if (this.$refs.dataList.loadStatus == "contentdown") {
        //     this.$refs.dataList.loadStatus = "loading";
        //     this.getData()
        //   }
        // },
        // 下拉刷新
        refreshPullDown(e) {
            //  重置数据
            this.$refs.dataList.stopRefresh();
            // this.$refs.dataList.pullDownHeight = 0
            // this.$refs.dataList.pullingDown = false
        },
        // 上拉加载
        scrolltolower() {},
        // getData ({ isInit = false } = {}) {
        //   if (isInit) {
        //     // 重置入参页码
        //     this.$refs.dataList.loadStatus = "loading";
        //   }
        // },
        //获取优惠券
        // getTicket () {
        //   let params = {
        //     // orderId: this.orderInfo.orderId,
        //     // orderChannel: this.orderInfo.orderChannel,
        //     // stationCode: this.orderInfo.stationCode,
        //     // totalAmount: this.orderInfo.orderAmount,
        //     // couponList: this.selectedCouponInfo
        //     orderId: '2204121556170000000104003',
        //     orderChannel: 15,
        //     stationCode: '1-A5001-C005-5094',
        //     totalAmount: 17.90,
        //     couponList: this.selectedCouponInfo
        //   }
        //   queryUnusedCouponList(params).then(res => {
        //     console.log(res)
        //     if (res.success) {
        //       if (res.data) {
        //         this.codeCouponArray = res.data.couponList
        //       } else {
        //         this.codeCouponArray = []
        //       }
        //     } else {
        //       // this.$Toast(res.message ? res.message : '获取优惠券列表失败')
        //     }
        //     // this.getSelectedCouponName()
        //   }
        //   )
        // },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
@import '../../../../s-kit/css/index.scss';
.view {
    .coupon-wrap {
        width: 100%;
        height: 100.5px;
        line-height: 100.5px;
        margin-bottom: 11px;

        .select-img-wrap {
            width: 20px;
            height: 20px;
            margin-right: 13.5px;

            img {
                width: 20px;
                height: 20px;
            }
        }

        .item-flex {
            width: 100%;
            height: 100px;
            // display: flex;
            // flex-direction: row;
            // align-items: center;
            // margin-bottom: 12px;
            // padding-right: 15px;
            position: relative;
        }

        .bg-coupon {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    // #ifdef MP-MPAAS
                    width: 32%;
                    // #endif
                    // #ifdef MP-WEIXIN
                    width: 32%;
                    // #endif
                    // #ifdef MP-ALIPAY
                    width: 29%;
                    // #endif

                    .content-left {
                        width: 100%;
                        line-height: 20px;
                        margin-top: 34px;

                        .price {
                            line-height: 20px;
                        }
                    }

                    .content-sx {
                        width: 1px;
                        height: 68px;
                        opacity: 0.5;
                        border: 0.5px solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-top: 15px;
                        img {
                            width: 1px;
                            height: 68px;
                        }
                    }
                }

                .right-wrap {
                    width: 68%;
                    height: 100%;
                    margin-left: 8px;

                    .content-right {
                        // margin-top: -17px;
                        .title {
                            line-height: 21px;
                            margin-top: 21px;
                            margin-right: 22px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }

                        .time {
                            line-height: 16.5px;
                            margin-top: 18px;
                        }

                        .expRem_div {
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }

    .btn {
        height: 44px;
        line-height: 44px;
        color: #fff;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
        margin: 0 16px 20px;
    }
}
</style>
