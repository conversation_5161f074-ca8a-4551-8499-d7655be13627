<template>
    <div class="add-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :back-text="title"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="open-ticket-box">
            <div class="section-box title">开票信息</div>
            <div class="section-box">
                <div class="left">
                    抬头类型
                    <!-- <div>*</div> -->
                </div>
                <div class="right type" v-if="type == 'add'">
                    <div v-for="(item, index) in typeList" :key="index" @click="changeType(index)">
                        <img v-if="item.tag == invoiceInfo.islogo" src="@/static/images/type-checked.png" alt />
                        <img v-else src="@/static/images/type-unchecked.png" alt />
                        {{ item.name }}
                    </div>
                </div>
                <div class="right type" v-else>{{ invoiceInfo.islogo == '1' ? '企业' : '个人' }}</div>
            </div>
            <div class="section-box">
                <div class="left">
                    抬头名称
                    <div>*</div>
                </div>
                <div class="right">
                    <input type="text" placeholder="请输入抬头名称" v-model="invoiceInfo.invoicetitle" />
                </div>
            </div>
            <div class="section-box">
                <div class="left">
                    税号
                    <div v-if="invoiceInfo.islogo == 1">*</div>
                </div>
                <div class="right">
                    <input type="text" placeholder="请输入纳税人识别号" v-model="invoiceInfo.taxcode" />
                </div>
            </div>
            <div class="section-box">
                <div class="left">地址</div>
                <div class="right">
                    <input type="text" placeholder="请输入地址" v-model="invoiceInfo.addresstax" />
                </div>
            </div>
            <div class="section-box">
                <div class="left">电话</div>
                <div class="right">
                    <input type="text" placeholder="请输入电话号码" v-model="invoiceInfo.telephone" />
                </div>
            </div>
            <div class="section-box">
                <div class="left">开户银行</div>
                <div class="right">
                    <input type="text" placeholder="请输入开户银行" v-model="invoiceInfo.openingbank" />
                </div>
            </div>
            <div class="section-box">
                <div class="left">银行账号</div>
                <div class="right">
                    <input type="text" placeholder="请输入银行账号" v-model="invoiceInfo.bankaccount" />
                </div>
            </div>
        </div>
        <div class="add-footer">
            <div class="footer-btn" @click="submit">{{ btnText }}</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { saveCompany, savePersonal } from '@/api/home.js';
import { bankCardAttribution } from '@/utils/bankcard.js';

export default {
    name: 'add-invoice-title',
    data() {
        return {
            title: '', // 导航文字
            btnText: '保存', // 按钮文字
            type: 'none', // 页面类型 add 是添加发票  edit 是编辑发票
            // isENChecked: false, // 是否设置默认(企业)
            // isPEChecked: false, // 是否设置默认(个人)
            pageConfig: pageConfig, // 页面配置
            // active: 0, // 0 是企业类型 1是个人类型
            typeList: [
                {
                    name: '企业',
                    tag: '1',
                },
                {
                    name: '个人',
                    tag: '2',
                },
            ],
            invoiceInfo: {
                islogo: 1,
                invoicetitle: '',
                taxcode: '',
                addresstax: '',
                telephone: '',
                openingbank: '',
                bankaccount: '',
            },
            unclick: false,
        };
    },
    onLoad(options) {
        this.active = options.itemdata;
        this.type = options.type;
        this.title = options.type == 'add' ? '添加抬头' : '编辑抬头';
        if (options.detail) {
            console.log('发票详情', decodeURIComponent(options.detail));
            this.invoiceInfo = JSON.parse(decodeURIComponent(options.detail));
            console.log(this.invoiceInfo, 'this.invoiceInfo');
        }
    },
    methods: {
        changeType(index) {
            if (this.type == 'add') {
                this.invoiceInfo.islogo = this.typeList[index].tag;
            }
        },
        //提交点击事件 !checkNormal(this.invoiceInfo.invoicetitle)
        async submit() {
            if (this.invoiceInfo.invoicetitle == '') {
                uni.showToast({
                    title: '抬头名称不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.invoiceInfo.islogo == 2) {
                //如果是个人的话，验证下中文和中间点，企业的话，不验证抬头输入的格式
                if (!this.$test.checkName(this.invoiceInfo.invoicetitle)) {
                    uni.showToast({
                        title: '请输入正确的抬头名称',
                        icon: 'none',
                        duration: 2000,
                    });
                    return;
                }
            }
            // if (!this.$test.checkNormal(this.invoiceInfo.invoicetitle)) {
            //     uni.showToast({
            //         title: '请输入正确的抬头名称',
            //         icon: 'none',
            //         duration: 2000
            //     })
            //     return;
            // }
            if (this.invoiceInfo.islogo == 1) {
                if (!this.$test.checkShuiHao_New(this.invoiceInfo.taxcode)) {
                    return;
                }
            } else {
                if (this.invoiceInfo.taxcode !== '') {
                    if (!this.$test.checkShuiHao_New(this.invoiceInfo.taxcode)) {
                        return;
                    }
                }
            }
            // if (this.invoiceInfo.addresstax == '') {
            //   uni.showToast({
            //     title: '发票地址不能为空',
            //     icon: 'none',
            //     duration: 2000
            //   })
            //   return;
            // }
            // 当前字符串中不能有空格
            if (
                this.invoiceInfo.telephone !== '' &&
                !this.$test.validator(this.invoiceInfo.telephone) &&
                !this.$test.checkTelPhone(this.invoiceInfo.telephone)
            ) {
                uni.showToast({
                    title: '请输入正确的电话号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // if (this.invoiceInfo.openingbank !== '' && !this.$test.checkBankName(this.invoiceInfo.openingbank)) {
            //     uni.showToast({
            //         title: '请输入正确的开户银行',
            //         icon: 'none',
            //         duration: 2000,
            //     });
            //     return;
            // }
            // if (this.invoiceInfo.bankaccount !== '' && !this.$test.checkBankCount(this.invoiceInfo.bankaccount)) {
            //     uni.showToast({
            //         title: '请输入正确的银行账号',
            //         icon: 'none',
            //         duration: 2000,
            //     });
            //     return;
            // }

            let { invoiceInfo } = this;
            if (this.invoiceInfo.islogo == 1) {
                await saveCompany({ ...invoiceInfo });
            } else {
                await savePersonal({ ...invoiceInfo });
            }
            uni.navigateBack();
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 15px;
$colorgray: #909090;

.add-center {
    width: 100%;
    height: 100%;
    background: #f6f6f6;
    display: flex;
    flex-direction: column;
    padding: 0 24rpx;
    .open-ticket-box {
        width: 100%;
        border-radius: 24rpx;
        background: #fff;
        margin-top: 24rpx;
        .section-box {
            padding: 24rpx;
            border-top: solid 2rpx #eee;
            display: flex;
            &.title {
                font-size: 32rpx;
                border-top: none;
            }
            .left {
                font-size: 34rpx;
                width: 180rpx;
                display: flex;
                div {
                    color: #f43d45;
                }
            }
            .right {
                flex: 1;
            }
            .type {
                display: flex;
                div:first-child {
                    margin-right: 56rpx;
                }
                img {
                    display: inline-block;
                    width: 44rpx;
                    height: 44rpx;
                    margin-right: 8rpx;
                }
            }
        }
    }

    .add-footer {
        width: 100%;
        position: fixed;
        left: 0;
        bottom: 0;
        padding: 24rpx 24rpx env(safe-area-inset-bottom);
        background: #fff;
        .footer-btn {
            width: 100%;
            height: 98rpx;
            text-align: center;
            line-height: 98rpx;
            background: #f96702;
            border-radius: 8rpx;
            color: #fff;
            font-size: 34rpx;
            margin-bottom: 10px;
        }
    }
}
</style>
