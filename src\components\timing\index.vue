<template>
    <div class="countdown">
        <div class="red-div">{{ dateData.hour }}</div>
        <span>:</span>
        <div class="red-div threeWidth">{{ dateData.minute }}</div>
        <span>:</span>
        <div class="red-div threeWidth">{{ dateData.second }}</div>
        <span>:</span>
        <div class="red-div fourWidth">{{ dateData.milliseconds }}</div>
    </div>
</template>

<script>
export default {
    name: 'timing',
    data() {
        return {
            dateData: {
                hour: '00',
                minute: '00',
                second: '00',
                milliseconds: '0000',
            },
            //定时器
            timer: null,
        };
    },
    mounted() {
        this.dateTimeFun();
        this.timerFun();
    },
    beforeDestroy() {
        this.timer && clearTimeout(this.timer);
    },
    methods: {
        dateTimeFun() {
            const date = new Date();
            let dateData = {
                hour: this.addZeroFun(date.getHours()),
                minute: this.addZeroFun(date.getMinutes()),
                second: this.addZeroFun(date.getSeconds()),
                milliseconds: this.addZeroMillFun(date.getMilliseconds()),
            };
            this.dateData = { ...dateData };
        },
        //补0方法
        addZeroFun(num) {
            let str = '';
            if (num < 10) {
                str = '0' + num;
            } else {
                str = num + '';
            }
            return str;
        },
        //毫秒补0方法
        addZeroMillFun(num) {
            let str = '';
            if (num < 10) {
                str = '00' + num;
            } else if (num < 100 && num > 10) {
                str = '0' + num;
            } else {
                str = num + '';
            }
            return str;
        },
        //定时器方法
        timerFun() {
            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.dateTimeFun();
                this.timerFun();
            }, 10);
        },
    },
};
</script>

<style lang="scss" scoped>
.countdown {
    color: #f96702;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    .red-div {
        display: flex;
        padding: 30rpx 0;
        align-items: center;
        justify-content: center;
        border: 1px solid #f96702;
        margin: 0 20rpx;
        padding: 8rpx 12rpx;
        font-size: 60rpx;
        border-radius: 10rpx;
        text-align: center;
        background-color: #fff8f3;
    }
    .threeWidth {
        width: 40px;
    }
    .fourWidth {
        width: 70px;
    }
    span {
        font-size: 50rpx;
    }
}
</style>
