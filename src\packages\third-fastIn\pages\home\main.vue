<template>
    <div class="pageMpaas">
        <div class="remaining_div bg-fff">
            <zj-navbar :background="{}" :title="pageType == 1 ? '编辑信息' : '快速进站'"> </zj-navbar>

            <div class="content_div">
                <div class="item_div">
                    <span class="item_left">姓名</span>
                    <input
                        :class="pageType == 2 || pageType == 1 ? 'item_righ readonly' : 'item_righ'"
                        v-model="name"
                        placeholder="输入您的姓名"
                        :disabled="pageType == 2 || pageType == 1"
                    />
                </div>
                <div class="item_div">
                    <span class="item_left">身份证号</span>
                    <input
                        v-if="pageType == 2 || pageType == 1"
                        :class="pageType == 2 || pageType == 1 ? 'item_righ readonly' : 'item_righ'"
                        v-model="cardIdNumberSec"
                        placeholder="输入您的身份证号"
                        disabled
                    />
                    <input v-else class="item_right" placeholder="输入您的身份证号" v-model="cardIdNumber" :disabled="pageType == 2" />
                </div>
                <div class="item_div">
                    <span class="item_left">手机号</span>
                    <input
                        class="item_righ readonly"
                        placeholder="输入您的手机号"
                        :value="phone ? phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') : ''"
                        disabled
                    />
                </div>
                <div class="carList_div">
                    <div v-for="(item, index) in carList" :key="index" class="item_div">
                        <span class="item_left">{{ item.id }}</span>
                        <div class="item_right">
                            <div class="left-small" @click="pageType != 2 && clickPlateInput(index)">
                                <span :class="pageType == 2 ? 'readonly' : ''">
                                    {{ item.province }}
                                </span>
                                <img src="../../images/icon-down.png" />
                            </div>
                            <input
                                :class="pageType == 2 ? 'center-small readonly' : 'center-small'"
                                placeholder="请输入车牌信息"
                                :disabled="pageType == 2"
                                v-model="item.carId"
                            />
                            <img
                                class="right-small"
                                @click="carList.length > 1 && deleteCarItem(item, index)"
                                src="../../images/delete.png"
                                v-if="carList.length > 1 && pageType != 2"
                            />
                        </div>
                    </div>
                </div>
                <div class="bottom-msg" v-if="carList.length < 3 && pageType != 2">
                    <span class="left-span">最多添加3个车牌</span>
                    <div class="right-span btnColor" @click="addCarItem()">添加</div>
                </div>
            </div>
            <div class="station-list" v-if="pageType == 2 && currentStation">
                <img src="../../images/logo.png" class="img" />
                <div class="center-div">
                    <div class="top">您当前在</div>
                    <div class="center">{{ currentStation.orgName }}</div>
                    <div class="station-bottom">
                        <img src="../../images/location.png" />
                        <span class="message"
                            >距离您{{
                                currentStation.distance * 1 > 1 ? currentStation.distance + '公里' : currentStation.distance * 1000 + '米'
                            }}</span
                        >
                    </div>
                </div>
                <div class="rightdiv">
                    <div @click="changestation()">不是这个油站?</div>
                </div>
            </div>
            <div class="bottom">
                <p v-if="pageType == 0">申请快速进站，用户必须已在加油站通过身份证使用过加油服务；若无，请先去加油站登记身份信息。</p>
                <button v-if="pageType == 0" @click="regist(true)">提交申请</button>
                <button v-if="pageType == 1" @click="regist(false)">修改信息</button>
                <button v-if="pageType == 2" @click="fastIn">快速进站</button>
            </div>

            <customKeyboard
                ref="uKeyboard"
                zIndex="10"
                @backspace="carKeyDelect"
                @change="carKeyChange"
                mode="car"
                v-model="isKeyboard"
                :mask="false"
            ></customKeyboard>
            <zj-show-modal></zj-show-modal>
        </div>
        <div class="header-top">
            <div :style="{ height: `${systemBar}px` }"></div>
            <div @click="rightClick" class="title_text">{{ pageType == 2 ? '修改' : '' }}</div>
        </div>
    </div>
</template>

<script>
import { stationListApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { customerModify, customerRegist, customerArrived, getCustomer } from '../../../../s-kit/js/v3-http/https3/fastIn';
import { memberInfo } from '../../../../s-kit/js/v3-http/https3/classInterest/index';

import customKeyboard from '../../components/custom-keyboard/custom-keyboard.vue';
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { customKeyboard },
    name: 'third-fastIn',
    props: {},
    data() {
        return {
            name: '',
            cardIdNumberSec: '',
            cardIdNumber: '',
            phone: '',
            pageType: 0,
            carList: [
                {
                    id: '车辆信息',
                    province: '新',
                    carId: '',
                },
            ],
            carItem: {
                id: '车辆信息',
                province: '新',
                carId: '',
            },
            // 是否显示键盘
            isKeyboard: false,
            currentCarMessage: null,
            // 正在输入新增后的车牌号位置 -1时关闭键盘
            selectPlateIndex: -1,
            // 正在输入编辑后的车牌号位置 -1时关闭键盘
            selectPlateEditIndex: -1,
            clickIndex: '',
            currentStation: null,
            isModify: false,
            isChangeStation: false,
            commonArgs: null,
            userInfo: null,
            stationList: null,
            // 选中油站返回的数据
            prveDataObject: {
                refreshListFlag: false,
                stationParam: {},
            },
            systemBar: '',
        };
    },
    async onShow() {
        if (this.prveDataObject.refreshListFlag) {
            this.currentStation = JSON.parse(JSON.stringify(this.prveDataObject.stationParam));
            // this.currentStation.distance = this.prveDataObject.stationParam.distance;
            // this.currentStation.orgName = this.prveDataObject.stationParam.orgName;
            // this.currentStation.hosCode = this.prveDataObject.stationParam.hosCode;
        }
    },
    async onLoad() {
        this.commonArgs = await this.$cnpcBridge.getCommonArgs();
        let userInfoToken = await this.$cnpcBridge.getUserTokenInfo(); // 获取用户信息
        this.token = userInfoToken.token;
        this.currentCarMessage = this.carList[0];
        let userInfo = await this.requestInfo();
        this.configDatas(userInfo);
    },
    async created() {},

    async mounted() {
        this.$cnpcBridge.getBarHeight(res => {
            this.systemBar = res;
        });
    },

    computed: {
        ...mapGetters(['latV3', 'lonV3', 'walletInfo']),
    },
    methods: {
        rightClick() {
            if (this.pageType != 2) {
                return;
            }
            this.pageType = 1;
            // this.isModify = true;
        },
        async fastIn() {
            let isOpen = await this.requestFastIn();
            console.log(isOpen, 'isOpen============');
            if (isOpen) {
                this.goToResult({
                    type: 2,
                    message: '已提交进站申请，请摇下车窗，脸部对准摄像头，等待放行',
                });
            }
        },
        goToResult(val) {
            let params = {
                pageData: val,
            };
            this.$sKit.layer.useRouter('/packages/third-fastIn/pages/result/main', params, 'navigateTo');
        },
        changestation() {
            this.isChangeStation = true;
            let URL = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {
                type: 'ekl', //昆仑e享卡
            };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        requestInfo() {
            return new Promise(async (resolve, reject) => {
                let params = {
                    token: this.token,
                    imei: await this.$cnpcUtils.encryption(this.commonArgs.deviceId),
                };
                let res = await getCustomer(params);
                // let data = res.data;
                console.log(res, 'getCustomer=============');
                if (res.status == 0) {
                    resolve(res.data);
                } else {
                    resolve(null);
                    uni.showToast({
                        title: res.info,
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        },
        async requestFastIn() {
            return new Promise(async (resolve, result) => {
                let params = {
                    token: this.token,
                    imei: await this.$cnpcUtils.encryption(this.commonArgs.deviceId),
                    stationCode: this.currentStation ? this.currentStation.hosCode : '',
                };
                let res = await customerArrived(params);
                console.log(res, 'customerArrived=========');
                if (res.status == 0) {
                    if (res.data.resultcode == 200) {
                        console.log(true, 'isOpen============');
                        resolve(true);
                    } else {
                        uni.showToast({
                            title: res.data.msg || '快速进站失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '快速进站失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        },
        async getStationList() {
            await this.$store.dispatch('initLocationV3_app', {
                callback: async () => {
                    let params = {
                        bookingRefueling: '',
                        distance: '10',
                        latitude: this.latV3,
                        longitude: this.lonV3,
                        pageNum: 1,
                        pageSize: 10,
                        mapType: '1', //地图坐标系标识（0高德，1百度，2腾讯）
                        orgCode: this.walletInfo.addressNo || '',
                    };
                    // 指定页面跳转到付款码页面的判断(页面默认展示为会员码)
                    let res = await stationListApi(params);
                    console.log(res, 'stationListApi==========');
                    if (res.success) {
                        this.stationList = this.$sKit.layer.filterLocations(res.data.rows);
                        this.currentStation = this.stationList ? this.stationList[0] : null;
                    } else {
                        uni.showToast({
                            title: res.message || '获取附近加油站失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                },
            });
        },
        async configDatas(userInfo) {
            if (userInfo && userInfo.resultcode == 200) {
                let res = await memberInfo();
                console.log(userInfo, 'userInfo=============111');

                let userdata = userInfo.data;
                this.userInfo = userdata;
                // 有信息则展示信息
                this.pageType = 2;
                this.name = userdata.name;
                // this.phone = this.$store.state.userInfo.phone;
                this.phone = res.data.phone;
                this.cardIdNumber = userdata.idno;
                // this.carList = userInfo.carList;
                this.carList = [];
                if (userdata.plate1) {
                    let info = {
                        id: '车辆信息',
                        province: userdata.plate1.substring(0, 1),
                        carId: userdata.plate1.substring(1),
                    };
                    this.carList.push(info);
                }
                if (userdata.plate2) {
                    let info = {
                        id: '车辆信息',
                        province: userdata.plate2.substring(0, 1),
                        carId: userdata.plate2.substring(1),
                    };
                    this.carList.push(info);
                }
                if (userdata.plate3) {
                    let info = {
                        id: '车辆信息',
                        province: userdata.plate3.substring(0, 1),
                        carId: userdata.plate3.substring(1),
                    };
                    this.carList.push(info);
                }
                this.userInfo.carList = JSON.parse(JSON.stringify(this.carList));
                this.cardIdNumberSec = this.cardIdNumber.replace(/^(\d{3})\d{11}(\d{4})$/, '$1***********$2');
                //请求油站数据
                this.stationList = await this.getStationList();
                this.currentStation = this.stationList ? this.stationList[0] : null;
            } else {
                this.pageType = 0;
                let res = await memberInfo();
                console.log(userInfo, 'userInfo=============');
                this.name = res.data.username || '';
                this.phone = res.data.phone;
            }
        },
        async regist(isRegest) {
            if (!this.name) {
                uni.showToast({
                    title: '请填写姓名',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.cardIdNumber) {
                uni.showToast({
                    title: '请填写身份证号',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let isComplete = true;
            this.carList.forEach(item => {
                if (!item.carId) {
                    isComplete = false;
                }
            });
            if (!isComplete) {
                uni.showToast({
                    title: '请填写车牌号',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }

            let params = {
                token: this.token,
                realName: this.name,
                idno: await this.$cnpcUtils.encryption(this.cardIdNumber),
                imei: await this.$cnpcUtils.encryption(this.commonArgs.deviceId),
                plate1: this.carList[0] ? this.carList[0].province + this.carList[0].carId : '',
                plate2: this.carList[1] ? this.carList[1].province + this.carList[1].carId : '',
                plate3: this.carList[2] ? this.carList[2].province + this.carList[2].carId : '',
            };
            console.log('params', params);
            let _this = this;
            let res = isRegest ? await customerRegist(params) : await customerModify(params);
            console.log(res, '提交申请=============');
            if (res.status == 0) {
                if (res.data.resultcode == 200) {
                    if (isRegest) {
                        // _this.isModify = true;
                        _this.goToResult({
                            type: 0,
                            message: '开通成功',
                        });
                    } else {
                        uni.navigateBack({ delta: 1 });
                    }
                } else {
                    _this.goToResult({
                        type: 1,
                        message: res.data.msg || '开通失败',
                    });
                }
            } else {
                uni.showToast({
                    title: res.info || '请求错误',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        addCarItem() {
            console.log('点击添加============');
            let item = JSON.parse(JSON.stringify(this.carItem));
            console.log(item, '点击添加============');

            // item.id = '车辆信息' + (this.carList.length + 1)
            this.carList.push(item);
        },
        deleteCarItem(item, index) {
            if (item.carId && item.province) {
                let _this = this;
                this.$store.dispatch('zjShowModal', {
                    title: '提示',
                    content: '是否删除此车牌信息?',
                    confirmText: '确认',
                    confirmColor: '#000',
                    cancelText: '取消',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            this.carList.splice(index, 1);
                        }
                    },
                });
                return;
            }
            this.carList.splice(index, 1);
        },
        clickPlateInput(index, val) {
            this.currentCarMessage = this.carList[index];
            this.$nextTick(() => {
                this.$refs.uKeyboard.changeCarInputMode('cn');
            });
            this.isKeyboard = true;
            this.isFirst = true;
            this.clickIndex = index;
        },
        // 键盘点击事件监听
        carKeyChange(text) {
            console.log(text);
            this.carList[this.clickIndex].province = text;
            this.isKeyboard = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;
    .content_div {
        padding: 10px 16px;
        .item_div {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 5px 0;
            .item_left {
                width: 28%;
                text-align: justify;
                display: inline-block;
                height: 29px;
                line-height: 30px;
            }
            // .item_left:after {
            //     content: '';
            //     display: inline-block;
            //     width: 100%;
            // }
            input {
                display: inline-block;
                width: 65%;
                height: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                font-size: 13px;
            }
            .readonly {
                color: #333;
            }
        }
        .carList_div {
            .item_div {
                .item_left,
                .item_right {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                .left-small {
                    display: flex;
                    align-items: center;
                    margin-left: 10px;
                    width: 20%;
                    border-radius: 5px;
                    background: #f6f6f6;
                    padding: 5px;
                    justify-content: space-around;
                    img {
                        width: 15px;
                        height: 15px;
                    }
                }
                .center-small {
                    flex: 1;
                    margin-left: 10px;
                }
                .right-small {
                    margin-left: 10px;
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }
}

.bottom {
    position: absolute;
    bottom: 15px;
    left: 15px;
    right: 15px;
    p {
        font-size: 12px;
        color: #666;
    }
    button {
        margin-top: 10px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        color: white;
        width: 100%;
        border-radius: 10px;
        border: none;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.bottom-msg {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
    .right-span {
        color: white;
        border-radius: 5px;
        padding: 5px 8px;
        font-size: 14px;
    }
}
.btnColor {
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
}
.station-list {
    display: flex;
    align-items: center;
    border-top: 1px solid #ddd;
    padding: 20px 15px 0 15px;
    margin-top: 10px;
    .img {
        width: 50px;
        height: 50px;
    }
    .center-div {
        flex: 1;
        margin: 0 10px;
    }
    .top {
        font-size: 12px;
        color: #666;
    }
    .center {
        font-size: 17px;
        padding: 5px 0;
    }
    .station-bottom {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #666;
        img {
            width: 10px;
            height: 10px;
        }
        .message {
            margin-left: 10px;
        }
    }
    .rightdiv {
        font-size: 13px;
        color: #666;
        border: 1px solid #f5f5f5;
        border-radius: 3px;
        padding: 3px;
    }
}
.header-top {
    position: fixed;
    // width: 100%;
    right: 15px;
    z-index: 9999;
    .title_text {
        height: 44px;
        line-height: 44px;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        // margin-top: 53px;
    }
}
</style>
