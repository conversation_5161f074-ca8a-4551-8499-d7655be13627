<template>
    <div class="pageMpaas">
        <div class="view">
            <zj-navbar :height="44" title="修改支付密码"></zj-navbar>
            <div class="main">
                <div class="formBox">
                    <div class="item" @click="showKeyboard1">
                        <input placeholder="请输入原密码" v-model="originalPasswordShow" disabled />
                    </div>
                    <div class="item" @click="showKeyboard2">
                        <input placeholder="请输入新密码" v-model="newPasswordShow" disabled />
                    </div>
                    <div class="item" @click="showKeyboard3">
                        <input placeholder="请确认新密码" v-model="confirmNewPasswordShow" disabled />
                    </div>
                </div>
                <div class="formTips">
                    <div class="tipsLeft">
                        <!-- <img class="exclamationPoint" src="@/static/trirdImage/exclamationPoint.png" alt /> -->
                        <span class="formTipsText">支付密码为6位数字</span>
                    </div>
                </div>
                <!-- #ifdef MP-WEIXIN -->
                <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
                <!-- #endif -->
                <!-- #ifdef MP-ALIPAY -->
                <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
                <!-- #endif -->
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { baseType } from '../../../../../project.config';
export default {
    name: 'payNoSecret',
    data() {
        return {
            name: '',
            originalPassword: '',
            newPassword: '',
            confirmNewPassword: '',
            originalPasswordLength: '',
            newPasswordLength: '',
            confirmNewPasswordLength: '',
            originalPasswordShow: '',
            newPasswordShow: '',
            confirmNewPasswordShow: '',
            passwordKeyboardRef: '',
            gsmsToken: '',
            openId: '',
        };
    },
    onLoad(option) {
        // #ifdef MP-WEIXIN
        this.selectComponent('#passwordKeyboardId').init(this.gsmsToken, this.openId, 'number', baseType); //V3.0纯数字键盘
        this.passwordKeyboardRef = this.selectComponent('#passwordKeyboardId');
        // #endif
    },
    onReady() {},
    computed: {},
    methods: {
        //原密码
        showKeyboard1() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                pwd => {
                    this.originalPasswordLength = this.passwordKeyboardRef.getLength('password_unique1');
                    this.originalPassword = this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    this.originalPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                },
            );
        },
        //新密码
        showKeyboard2() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique2',
                6,
                pwd => {
                    this.newPasswordLength = this.passwordKeyboardRef.getLength('password_unique2');
                    this.newPassword = this.passwordKeyboardRef.getCipherPWD('password_unique2');
                    this.newPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘2的关闭函数');
                },
            );
        },
        //确认密码
        showKeyboard3() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique3',
                6,
                pwd => {
                    this.confirmNewPasswordLength = this.passwordKeyboardRef.getLength('password_unique3');
                    this.confirmNewPassword = this.passwordKeyboardRef.getCipherPWD('password_unique3');
                    this.confirmNewPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘3的关闭函数');
                },
            );
        },
    },
    components: {
        // safePasswordDev
    },
};
</script>
<style lang="scss" scoped>
.view {
    // background-color: #F7F7FB;
    background-color: #fff;
    height: 100%;
    padding: 0 30rpx env(safe-area-inset-bottom);

    .main {
        // padding: 0 15px;
        margin-top: 10px;

        .formBox {
            .item {
                height: 44px;
                background-color: #f7f7fb;
                padding: 5px 10px;
                border-radius: 10px;
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .img1 {
                    width: 22px;
                    height: 15px;
                    margin-right: 15px;
                }

                .img2 {
                    width: 18px;
                    height: 9px;
                    margin-right: 15px;
                }
            }
        }

        .formTips {
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .tipsLeft {
                .exclamationPoint {
                    width: 15px;
                    height: 15px;
                    margin-right: 5px;
                }

                .formTipsText {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 15px;
                }
            }

            .tipsRight {
                margin-right: 20px;
                color: #e64f23;
                font-size: 14px;
            }
        }

        .btnBox {
            height: 44px;
            // margin-top: 20px;
            background-color: #e64f22;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 15px;
            color: #ffffff;
        }
    }
}

// >>>.u-form-item{
//   padding: 0
// }
</style>
