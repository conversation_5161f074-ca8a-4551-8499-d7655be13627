import Vue from 'vue';
import md5 from 'md5';
import store from '../../../store/index';
import layer from '../layer';
import checkFKArgs from './../v3-native-jsapi/checkFKArgs.js';
import commonUtil from './../commonUtil.js';
import projectConfig from '../../../../project.config.js';
import sKit from '../index';
console.log('store-0-0-0-', store, projectConfig);
const method = 'post';
const mpaasAppId = projectConfig.mpaasAppId;
const workspaceid = projectConfig.workspaceid;
const miniAppId = projectConfig.appId;
// #ifndef MP-TOUTIAO
import zfbFPMixins from '../../../mixins/zfb-fp-plugin';
import wxFPMixins from '../../../mixins/wx-fp-plugin';
// #endif
import platform from '@/s-kit/js/platform';
// #ifdef MP-ALIPAY || MP-WEIXIN
let accountInfo = uni.getAccountInfoSync();
console.log('accountInfo--', accountInfo);
// #endif
let systemInfo = uni.getSystemInfoSync();
const postPlatform = systemInfo.osName || '';

// #ifdef MP-WEIXIN
import MP from '../../../utils/wasm/mgssdk.min.js';
// #endif
// #ifdef MP-ALIPAY || MP-MPAAS
import MP from './mpaas_mgs_sdk_zfb_1_0_2.js';
// #endif
// #ifdef MP-TOUTIAO
import MP from './static/mgssdk.js';
// #endif
// #ifdef H5-CLOUD
import MP from './mpaas_mgs_sdk_yl_1_0_0.js';
// #endif
let isHarmony = false;
console.log(JSON.stringify(systemInfo), 'systemInfo');
// #ifndef MP-MPAAS || H5-CLOUD
if (systemInfo?.osName === 'harmonyos') {
    isHarmony = true;
}
// #endif
// #ifdef MP-MPAAS
import cnpcBridge from '../v3-native-jsapi/cnpcBridge.js';
let commonArgs;
let miniVersion;
let userTokenInfo;
// #endif
const clientCode = projectConfig.clientCode;

let isErrorCodeFlag = true; //token刷新第一次接口触发标识
let isLoginExpired = false; // 正常返回接口置false，第一次触发004005报错置true
let ddList = []; // 等token的网路请求
let needLoadingCount = 0; //loading中接口数
// 需要风控参数的接口
const riskControlInterfaceList = [
    'order.unPaidOrder', // 生成待支付订单
    'account.password.forget', // 忘记密码
    'account.preOrder.recharge', // 昆仑e享卡充值预下单
    'account.openAccount', // 开通昆仑e享卡
    'account.password.modify', // 修改支付密码
    'card.bindCard', // 绑定加油卡
    'card.rollout', // 资金转出
    'card.fastBindCard', // 油卡快捷绑定接口
    'user.plate.modifyOrCreate', // 修改/创建车牌号接口
    'order.preAuth.generateOrder', // 预授权下单
    'user.refreshToken', // token刷新接口
    'user.basicInfo.modify', // 修改用户基本信息
    'user.cancelAccount', // 用户注销
    'invoice.make', // 3.0消费开票
    'user.login', //账号登录，目前小程序不调
    'user.cancelAccount', //账号注销，目前小程序不调
    'account.rechargeStatus.query', //昆仑e享卡充值状态查询
    'account.logout', //账户注销，目前小程序不调
    'user.initWechatFaceLoginByCode', // 微信小程序调用
    'account.riskEngineSupport', // 会员码风控
];

const needAddressList = [
    'user.memberInfo.initRealPersonIdentify',
    'user.identityAuth.wxPalmprintAuth',
    'user.memberInfo.realPersonIdentify',
];

const noTokenList = ['oilstation.station.list'];

const h5UrlList = ['route.srvsecurity.huePersQrCode.getPersMiniProgramQrCode.h5'];
// 阿里的接口
const aliApi = ['alipay.mcdp.space.querySpaceInfo'];
// 接口防抖节流历史列表
let apiDebounceList = [];

export const wxPost = async (url, params, config) => {
    const timeData = new Date().getTime();
    if (isErrorCodeFlag && url == 'order.unPaidOrder') {
        if (!apiDebounce(url, timeData)) return;
    }
    let noRequestBody = false;
    let isAliResponse = false;
    let body = {};
    let extendFiled = await addExtendFiled(url, config);
    if (aliApi.includes(`${url}`)) {
        noRequestBody = true;
        isAliResponse = true;
        body = params;
    } else {
        if (extendFiled) {
            body = Object.assign({ extendFiled: JSON.parse(extendFiled) }, JSON.parse(JSON.stringify(params)));
        } else {
            body = Object.assign({}, JSON.parse(JSON.stringify(params)));
        }
        if (needAddressList.includes(url)) {
            if (store.state.locationV3_app.locationState && projectConfig.baseType == 'prd') {
                body.authPlace = store.state.locationV3_app.cityCode || '';
                body.authPlaceName = store.state.locationV3_app.cityName || '';
            } else {
                body.authPlace = '';
                body.authPlaceName = '';
            }
        }
    }
    // 将参数放入方法中，加风控参数返回
    const UUID = 'wx' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
    const tokenInfo = uni.getStorageSync('tokenInfo');
    const clientsign = JSON.stringify(body) + projectConfig.v3sign + timeData;
    const bizTrace =
        `${accountInfo.miniProgram.appId}--${accountInfo.miniProgram.version}-${clientCode}-${tokenInfo.memberNo}-${systemInfo.deviceId}`.replace(
            /\./g,
            '_',
        ); //biztrace 只支持中文、英文字母、数字、下划线和短横线;
    const pages = getCurrentPages();
    const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0]; // 获取栈实例
    console.log('当前页面的路由post：', pageUrl);
    let extraHeaderInfos = {
        // 统一请求头
        'Content-Type': 'application/json',
        token: tokenInfo?.accessToken || config?.accessToken || store.state.token3 || '',
        clientCode: clientCode,
        time: timeData + '',
        clientsign: md5(clientsign),
        clientuuid: UUID,
        'x-api-version': config.apiVersion,
        platform: postPlatform,
        mpaasAppid: accountInfo.miniProgram.appId || '', //小程序id
        appVersion: '',
        miniappVersion: accountInfo.miniProgram.version || '2.4.43', //小程序版本号
        channelCode: clientCode, //渠道
        did: systemInfo.deviceId, //设备id
        userId: tokenInfo.memberNo, //会员号
        bizTrace: bizTrace,
        pageUrl: pageUrl,
    };
    if (noTokenList.includes(url)) delete extraHeaderInfos.token;
    if (extendFiled) {
        extraHeaderInfos['risk-field'] = encodeURIComponent(extendFiled);
    }
    console.log(`----环境-----${projectConfig.baseType},--------3.0请求--------${url}----${UUID}`, '-------请求入参-------', body);
    return new Promise((resolve, reject) => {
        if (!isErrorCodeFlag && url !== 'user.refreshToken') {
            refreshLogin(url, params, config, wxPost, resolve);
            return;
        }
        if (pageUrl === '/pages/thirdHome/main') {
            store.commit('setLoadingStatus', false);
        }
        ShowLoading(config.isload);
        MP.MGS.call('rpc', {
            method: method,
            baseURL: projectConfig.rpcUrl,
            operationType: url,
            workspaceid: workspaceid,
            appid: mpaasAppId,
            data: body,
            noRequestBody: noRequestBody,
            extraHeaderInfos: extraHeaderInfos,
        })
            .then(response => {
                console.log(`----环境-----${projectConfig.baseType},--------3.0成功返回--------${url}----${UUID},`, response);
                if (config.isload) {
                    HideLoading(config.isload);
                }
                //是否触发限流
                if (currentLimiting(response, url, config)) {
                    reject();
                    return;
                }
                let res = {};
                if (typeof response.data === 'string' && response.data) {
                    res = JSON.parse(response.data);
                } else {
                    res = response.data;
                }
                if (!isAliResponse) {
                    if (res && res.success) {
                        isLoginExpired = false;
                        resolve(res);
                    } else {
                        //  重新登录的错误码
                        if (res.errorCode === 'B_C20_000005' || res.errorCode === 'B_C20_000004' || res.errorCode === 'B_B02_003201') {
                            // 重新登录
                            if (!isLoginExpired) {
                                isLoginExpired = true;
                                console.log(store.state.thirdLogin.loginButtonGrayedOut, '004-----005');
                                // uni.reLaunch({
                                //     url: '/packages/third-new-third-login/pages/login/main',
                                // });
                                uni.clearStorageSync();
                                // layer.backHomeFun();
                                uni.reLaunch({
                                    url: '/pages/thirdHome/main',
                                });
                            }
                            return;
                        }
                        // 刷新token的错误码
                        if (res.errorCode === 'B_C20_000008') {
                            refreshLogin(url, params, config, wxPost, resolve);
                            return;
                        }
                        // 是否需要自行展示自定义报错弹窗
                        showError({
                            result: res,
                            config: config,
                            url: url,
                        });
                        resolve(res);
                    }
                } else {
                    if (res && res.resultDesc) {
                        resolve(res);
                    } else {
                        resolve(res);
                    }
                }
            })
            .catch(error => {
                try {
                    showError({
                        result: { message: error.errorMessage || '请求异常', errorCode: error.error || '' },
                        config: config,
                        url: url,
                    });
                    HideLoading(config.isload);
                    reject(error);
                } catch (e) {
                    showError({ result: { message: '请求异常', errorCode: '' }, config: config, url: url });
                    HideLoading(config.isload);
                    reject(error);
                }
            });
    });
};

export const alPost = async (url, params, config) => {
    const timeData = new Date().getTime();
    let noRequestBody = false;
    let isAliResponse = false;
    let body = {};
    let extendFiled = await addExtendFiled(url, config);
    // 判断是否为阿里的营销位接口
    if (aliApi.includes(`${url}`)) {
        noRequestBody = true;
        isAliResponse = true;
        body = params;
    } else {
        if (extendFiled) {
            body = Object.assign({ extendFiled: JSON.parse(extendFiled) }, JSON.parse(JSON.stringify(params)));
        } else {
            body = Object.assign({}, JSON.parse(JSON.stringify(params)));
        }
        if (needAddressList.includes(url)) {
            if (store.state.locationV3_app.locationState && projectConfig.baseType == 'prd') {
                body.authPlace = store.state.locationV3_app.cityCode || '';
                body.authPlaceName = store.state.locationV3_app.cityName || '';
            } else {
                body.authPlace = '';
                body.authPlaceName = '';
            }
        }
    }
    const UUID = 'al' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
    const tokenInfo = await uni.getStorageSync('tokenInfo');
    const bizTrace =
        `${accountInfo.miniProgram.appId}--${accountInfo.miniProgram.version}-${clientCode}-${tokenInfo.memberNo}-${systemInfo.deviceId}`.replace(
            /\./g,
            '_',
        ); //biztrace 只支持中文、英文字母、数字、下划线和短横线;
    const pages = getCurrentPages();
    const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0]; // 获取栈实例
    console.log('当前页面的路由post：', pageUrl);
    let extraHeaderInfos = {
        'Content-Type': 'application/json',
        token: tokenInfo.accessToken || config?.accessToken || '',
        clientCode: clientCode,
        time: timeData + '',
        sign: md5(JSON.stringify(body) + projectConfig.v3sign + timeData),
        clientuuid: UUID,
        'x-api-version': config.apiVersion,
        platform: postPlatform,
        mpaasAppid: accountInfo.miniProgram.appId || '', //小程序id
        appVersion: '',
        miniappVersion: accountInfo.miniProgram.version || '', //小程序版本号
        channelCode: clientCode, //渠道
        did: systemInfo.deviceId, //设备id
        userId: tokenInfo.memberNo || '', //会员号
        bizTrace: bizTrace,
        pageUrl: pageUrl,
    };
    if (noTokenList.includes(url)) delete extraHeaderInfos.token;
    if (extendFiled) {
        extraHeaderInfos['risk-field'] = encodeURIComponent(extendFiled);
    }
    console.log(`--------3.0请求--------${url}----${UUID}`, body);
    return new Promise((resolve, reject) => {
        if (!isErrorCodeFlag && url !== 'user.refreshToken') {
            refreshLogin(url, params, config, alPost, resolve);
            return;
        }
        if (pageUrl === `${projectConfig?.subPath || ''}/pages/thirdHome/main`) {
            store.commit('setLoadingStatus', false);
        }
        ShowLoading(config.isload);
        MP.MGS.call('rpc', {
            method: method,
            baseURL: projectConfig.rpcUrl,
            operationType: url,
            workspaceid: workspaceid,
            appid: mpaasAppId,
            data: !isAliResponse ? body : [body],
            noRequestBody: noRequestBody,
            extraHttpConfig: {
                // 统一请求头
                dataType: 'json',
            },
            extraHeaderInfos: extraHeaderInfos,
        })
            .then(response => {
                console.log(`--------3.0成功返回--------${url}----${UUID}`, response);
                if (config.isload) {
                    HideLoading(config.isload);
                }
                //是否触发限流
                if (currentLimiting(response, url, config)) {
                    reject();
                    return;
                }
                let res = {};
                if (typeof response.data === 'string' && response.data) {
                    res = JSON.parse(response.data);
                } else {
                    res = response.data;
                }
                if (!isAliResponse) {
                    if (res && res.success) {
                        isLoginExpired = false;
                        resolve(res);
                    } else {
                        if (
                            (res.errorCode === 'B_C20_000005' || res.errorCode === 'B_C20_000004' || res.errorCode === 'B_B02_003201') &&
                            !isLoginExpired
                        ) {
                            isLoginExpired = true;
                            // 重新登录
                            my.clearStorageSync();
                            layer.backHomeFun();
                            return;
                        }
                        if (res.errorCode === 'B_C20_000008') {
                            refreshLogin(url, params, config, alPost, resolve);
                            return;
                        }
                        showError({
                            result: res,
                            config: config,
                            url: url,
                        });
                        resolve(res);
                    }
                } else {
                    if (res && res.resultDesc) {
                        resolve(res);
                    } else {
                        resolve(res);
                    }
                }
            })
            .catch(error => {
                try {
                    showError({
                        result: { message: error.errorMessage || '请求异常', errorCode: error.error || '' },
                        config: config,
                        url: url,
                    });
                    HideLoading(config.isload);
                    reject(error);
                } catch (e) {
                    showError({ result: { message: '请求异常', errorCode: '' }, config: config, url: url });
                    HideLoading(config.isload);
                    reject(error);
                }
            });
    });
};
export const ttPost = async (url, params, config) => {
    const timeData = new Date().getTime();
    let noRequestBody = false;
    let isAliResponse = false;
    let body;
    let extendFiled = await addExtendFiled(url, config);
    // 判断是否为阿里的营销位接口
    if (aliApi.includes(`${url}`)) {
        noRequestBody = true;
        isAliResponse = true;
        body = params;
    } else {
        if (extendFiled) {
            body = Object.assign({ extendFiled: JSON.parse(extendFiled) }, JSON.parse(JSON.stringify(params)));
        } else {
            body = Object.assign({}, JSON.parse(JSON.stringify(params)));
        }
    }
    const UUID = 'tt' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
    const tokenInfo = uni.getStorageSync('tokenInfo');
    console.log(tokenInfo, 'tokenInfo==头条post');
    let extraHeaderInfos = {
        'Content-Type': 'application/json',
        token: tokenInfo.accessToken || config?.accessToken || '',
        clientCode: clientCode,
        time: timeData + '',
        sign: md5(JSON.stringify(body) + projectConfig.v3sign + timeData),
        clientuuid: UUID,
        'x-api-version': config.apiVersion,
        platform: postPlatform,
        // productId: accountInfo.miniProgram.appId || '', //小程序id
        // productVersion: accountInfo.miniProgram.version || '', //小程序版本号
        channel: clientCode, //渠道
        did: systemInfo.deviceId, //设备id
        userId: tokenInfo.memberNo || '', //会员号
    };
    if (noTokenList.includes(url)) delete extraHeaderInfos.token;
    if (extendFiled) {
        extraHeaderInfos['risk-field'] = encodeURIComponent(extendFiled);
    }
    console.log(`--------3.0请求--------${url}----${UUID}`, body);
    return new Promise((resolve, reject) => {
        if (!isErrorCodeFlag && url !== 'user.refreshToken') {
            refreshLogin(url, params, config, alPost, resolve);
            return;
        }
        ShowLoading(config.isload);
        MP.MGS.call('rpc', {
            method: method,
            baseURL: projectConfig.rpcUrl,
            operationType: url,
            workspaceid: workspaceid,
            appid: mpaasAppId,
            data: !isAliResponse ? body : [body],
            noRequestBody: noRequestBody,
            extraHttpConfig: {
                // 统一请求头
                dataType: 'json',
            },
            extraHeaderInfos: extraHeaderInfos,
        })
            .then(response => {
                console.log(`--------3.0成功返回--------${url}----${UUID}`, response);
                if (config.isload) {
                    HideLoading(config.isload);
                }
                let res = {};
                if (typeof response.data === 'string' && response.data) {
                    res = JSON.parse(response.data);
                } else {
                    res = response.data;
                }

                if (!isAliResponse) {
                    if (res && res.success) {
                        resolve(res);
                    } else {
                        if (res.errorCode === 'B_C20_000005' || res.errorCode === 'B_C20_000004') {
                            // 重新登录
                            uni.clearStorageSync();
                            layer.backHomeFun();
                            return;
                        }
                        if (res.errorCode === 'B_C20_000008') {
                            refreshLogin(url, params, config, alPost, resolve);
                            return;
                        }
                        showError({
                            result: res,
                            config: config,
                            url: url,
                        });
                        resolve(res);
                    }
                } else {
                    if (res && res.resultDesc) {
                        resolve(res);
                    } else {
                        resolve(res);
                    }
                }
            })
            .catch(error => {
                try {
                    showError({
                        result: { message: error.errorMessage || '请求异常', errorCode: error.error || '' },
                        config: config,
                        url: url,
                    });
                    HideLoading(config.isload);
                    reject(error);
                } catch (e) {
                    showError({
                        result: { message: error.errorMessage || '请求异常', errorCode: error.error || '' },
                        config: config,
                        url: url,
                    });
                    HideLoading(config.isload);
                }
            });
    });
};
/**
 * @param {any} url
 * @param {{}} params
 * @param {{ isCustomErr: any; isload: any; isEncode?: boolean; handleErrorFn: any; }} config
 */
export const mpaasPost = async (url, params, config) => {
    return new Promise(async (resolve, reject) => {
        let timeData = new Date().getTime();
        let noRequestBody = false;
        let isAliResponse = false;
        let body = {};
        // 判断是否为阿里的营销位接口
        if (aliApi.includes(`${url}`)) {
            noRequestBody = true;
            isAliResponse = true;
        }
        await cachedCommonArgMiniVersion();
        let args;
        if (isHarmony) {
            args = await addExtendFiled(url, config);
        } else {
            args = await checkFKArgs.getFKArgs(url, config.isAuth);
        }
        let deviceName = encodeURIComponent(commonArgs.deviceName.split(/[\t\r\f\n\s]*/g).join(''));
        let jsonObj = JSON.parse(JSON.stringify(params));
        jsonObj.deviceId = commonArgs.deviceId;
        if (needAddressList.includes(url)) {
            if (store.state.locationV3_app.locationState && projectConfig.baseType == 'prd') {
                jsonObj.authPlace = store.state.locationV3_app.cityCode || '';
                jsonObj.authPlaceName = store.state.locationV3_app.cityName || '';
            } else {
                jsonObj.authPlace = '';
                jsonObj.authPlaceName = '';
            }
        }
        if (isHarmony) {
            body = args
                ? Object.assign({ extendFiled: JSON.parse(args) }, JSON.parse(JSON.stringify(jsonObj)))
                : Object.assign({}, JSON.parse(JSON.stringify(jsonObj)));
        } else {
            body = Object.assign(args, JSON.parse(JSON.stringify(jsonObj)));
        }
        let encryptStr = {};
        if (!isAliResponse) {
            encryptStr = await cnpcBridge.bangbangEncrypt(JSON.stringify(body));
        }
        let channelKey = '';
        if (workspaceid === 'sit' || workspaceid === 'default') {
            channelKey = 'sdfgzPIkfg60po';
        } else if (workspaceid === 'product') {
            channelKey = 'o98hh4kkrjnnwe';
        }
        let signStr = md5(encryptStr.data + channelKey + timeData);
        let requestData = [{ _requestBody: { jsonData: encryptStr.data } }];
        const UUID = 'app' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
        const pages = getCurrentPages();
        const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0]; // 获取栈实例
        console.log('当前页面的路由post：', pageUrl);
        let header = {
            // 统一请求头
            'Content-Type': 'application/json',
            clientCode: clientCode,
            time: timeData + '',
            clientsign: signStr,
            clientuuid: UUID,
            'x-api-version': config.apiVersion,
            deviceNo: commonArgs.deviceNo || '',
            deviceId: commonArgs.deviceId,
            deviceName: deviceName,
            did: commonArgs.deviceId, //设备id
            mpaasAppid: miniAppId,
            appVersion: commonArgs.appVersion, //app版本号
            miniappVersion: miniVersion, //小程序版本号
            channelCode: clientCode,
            token: userTokenInfo.token3 || '',
            userId: userTokenInfo.memberNo || '',
            pageUrl: pageUrl,
        };
        if (noTokenList.includes(url)) delete header.token;
        let isH5 = false;
        if (h5UrlList.includes(url)) {
            isH5 = true;
        }
        const bizTrace =
            `${miniAppId}-${commonArgs.appVersion}-${miniVersion}-${clientCode}-${userTokenInfo.memberNo}-${commonArgs.deviceId}`.replace(
                /\./g,
                '_',
            ); //biztrace 只支持中文、英文字母、数字、下划线和短横线
        header.bizTrace = bizTrace;
        if (!isHarmony) {
            header['risk-field'] =
                JSON.stringify(args) !== '{}' && JSON.stringify(args.extendFiled) !== '{}'
                    ? encodeURIComponent(JSON.stringify(args.extendFiled))
                    : '';
        } else {
            header['risk-field'] = args ? encodeURIComponent(args) : '';
        }
        console.log(
            '--------3.0请求--------' + 'app----UUID=====' + UUID,
            'url======' + url,
            'header=====' + JSON.stringify(header),
            'body=====' + JSON.stringify(body),
            'requestData====' + JSON.stringify(requestData),
            'encryptStr===' + JSON.stringify(encryptStr),
        );
        if (!isErrorCodeFlag && url !== 'user.refreshToken') {
            refreshLogin(url, params, config, mpaasPost, resolve);
            return;
        }
        if (pageUrl === '/pages/thirdHome/main') {
            store.commit('setLoadingStatus', false);
        }
        ShowLoading(config.isload);
        mpaasPostFun(isH5, url, body, requestData, header, noRequestBody, isAliResponse, userTokenInfo)
            .then(result => {
                console.log(`--------3.0成功返回--------${url}----${UUID}`, result);
                if (config.isload) {
                    HideLoading(config.isload);
                }
                if (!isAliResponse) {
                    if (result && result.success) {
                        isLoginExpired = false;
                        resolve(result);
                    } else {
                        if (
                            (result.errorCode == 'B_C20_000005' ||
                                result.errorCode == 'B_C20_000004' ||
                                result.errorCode === 'B_B02_003201') &&
                            !isLoginExpired
                        ) {
                            isLoginExpired = true;
                            // 重新登录
                            cnpcBridge.businessTypeAlertDialog('登录信息已过期,请重新登录', 'LoginExpired', res => {
                                //退出登录
                                commonUtil.logoutFun();
                            });
                            return;
                        }
                        if (result.errorCode === 'B_C20_000008') {
                            refreshLogin(url, params, config, mpaasPost, resolve);
                            return;
                        }
                        showError({ result: result, config: config, url: url });
                        resolve(result);
                    }
                } else {
                    if (result && result.resultDesc) {
                        resolve(result);
                    } else {
                        resolve(result);
                    }
                }
            })
            .catch(error => {
                console.log('mpaasPost error', error);
                try {
                    HideLoading(config.isload);
                    let message = '';
                    if (error?.error == '1002') {
                        if (error.errorMessage && error.errorMessage.indexOf(':')) {
                            let arr = error.errorMessage.split(':');
                            message = arr[arr.length - 1] || '服务繁忙，请稍后重试';
                        }
                    } else {
                        message = error.errorMessage || '请求异常';
                    }
                    showError({
                        result: { message: message || '请求异常', errorCode: error.error || '' },
                        config: config,
                        url: url,
                    });
                    reject(error);
                } catch (e) {
                    HideLoading(config.isload);
                    showError({ result: { message: '请求异常', errorCode: '' }, config: config, url: url });
                }
            });
    });
};

function mpaasPostFun(isH5, url, body, requestData, header, noRequestBody, isAliResponse, userTokenInfo) {
    return new Promise((resolve, reject) => {
        if (isH5) {
            MP.MGS.call('rpc', {
                method: 'post',
                baseURL: 'https://hkyznp.kunlunjyk.com:10443/gsms/mgw.htm',
                operationType: url,
                workspaceid: 'sit',
                appid: 'PRI5A96DC5141050',
                secretKey: '7f78842bc10f3b03ea1942a3dafc382e',
                signType: 'md5',
                data: body,
                noRequestBody: false,
                extraHttpConfig: {
                    dataType: 'json',
                },
                referrerStyle: 'full',
                extraHeaderInfos: {
                    'is-plugin': 'sf',
                    Authorization: 'Bearer ' + userTokenInfo.gsmsToken, //自定义请求头信息
                    'Client-Code': 'C10', //config.channel,
                    'client-code': 'C10', //config.channel, //options.keyboardParameter.clientCode, // 客户端ID
                    token: 'token',
                    time: 'time',
                    'Content-Type': 'application/json',
                    'content-type': 'application/json',
                    'client-uuid': '', //Math.floor(Math.random() * (9999999999999999 - 1000000000000000 + 1) + 1000000000000000), //随机数 16位
                    // 'client-sign': sign, // 支付宝/微信/mPaas渠道层签名
                    'client-sign': header.clientsign,
                    'x-api-version': 'v1', // 接口版本号
                    'risk-field': '', // 风控参数
                    'device-id': '',
                    'device-no': '',
                    'device-name': '',
                },
            })
                .then(res => {
                    resolve(res);
                })
                .catch(error => {
                    reject(error);
                });
        } else {
            my.call('rpc', {
                operationType: url,
                requestData: isAliResponse ? [body] : requestData,
                headers: header,
                noRequestBody: noRequestBody,
            })
                .then(res => {
                    resolve(res);
                })
                .catch(error => {
                    reject(error);
                });
        }
    });
}
export const cloudPost = async (url, params, config) => {
    const timeData = new Date().getTime();
    if (isErrorCodeFlag && url == 'order.unPaidOrder') {
        if (!apiDebounce(url, timeData)) return;
    }
    let noRequestBody = false;
    let isAliResponse = false;
    let body = {};
    // let urlH5
    // let extendFiled = await addExtendFiled(url, config);
    if (aliApi.includes(`${url}`)) {
        noRequestBody = true;
        isAliResponse = true;
        body = params;
    } else {
        // urlH5 = url ? url + '.h5' : ''
        body = Object.assign({}, JSON.parse(JSON.stringify(params)));
        console.log('cloudPost-body', JSON.stringify(body));
        if (needAddressList.includes(url)) {
            if (store.state.locationV3_app.locationState && projectConfig.baseType == 'prd') {
                body.authPlace = store.state.locationV3_app.cityCode || '';
                body.authPlaceName = store.state.locationV3_app.cityName || '';
            } else {
                body.authPlace = '';
                body.authPlaceName = '';
            }
        }
    }
    // 将参数放入方法中，加风控参数返回
    const UUID = 'cloud' + layer.getTYUUID('xxxxxxxxxxxxxxxx');
    const tokenInfo = uni.getStorageSync('tokenInfo');
    const clientsign = JSON.stringify(body) + projectConfig.v3sign + timeData;
    const pages = getCurrentPages();
    const pageUrl = pages[pages.length - 1].$page.fullPath.split('?')[0]; // 获取栈实例
    console.log('当前页面的路由post：', pageUrl);
    let extraHeaderInfos = {
        // 统一请求头
        'content-type': 'application/json',
        token: tokenInfo.accessToken || config?.accessToken,
        clientCode: clientCode,
        time: timeData + '',
        clientsign: md5(clientsign),
        clientuuid: UUID,
        'x-api-version': config.apiVersion,
        platform: postPlatform,
        mpaasAppid: projectConfig.mpaasAppId, //小程序id
        appVersion: systemInfo.appVersion,
        miniappVersion: projectConfig.miniappVersion, //小程序版本号
        channelCode: clientCode, //渠道
        did: systemInfo.deviceId, //设备id
        userId: tokenInfo.memberNo || '', //会员号
        pageUrl: pageUrl,
    };
    console.log('extraHeaderInfos--请求头', extraHeaderInfos);
    // if (extendFiled) {
    //     extraHeaderInfos['risk-field'] = encodeURIComponent(extendFiled);
    // }
    console.log(`----环境-----${projectConfig.baseType},--------3.0请求--------${url}----${UUID}`, '-------请求入参-------', body);

    try {
        return new Promise((resolve, reject) => {
            if (!isErrorCodeFlag && url !== 'user.refreshToken') {
                refreshLogin(url, params, config, cloudPost, resolve);
                return;
            }
            ShowLoading(config.isload);
            MP.MGS.call('rpc', {
                method: method,
                baseURL: projectConfig.rpcUrl,
                // operationType: urlH5,
                operationType: url,
                workspaceid: workspaceid,
                appid: mpaasAppId,
                data: body,
                noRequestBody: noRequestBody,
                // secretKey:projectConfig.secretKey,
                signType: 'md5',
                extraHttpConfig: {
                    // 统一请求头
                    dataType: 'json',
                },
                extraHeaderInfos: extraHeaderInfos,
            })
                .then(response => {
                    console.log(`----环境-----${projectConfig.baseType},--------3.0成功返回--------${url}----${UUID},`, response);
                    if (config.isload) {
                        HideLoading(config.isload);
                    }

                    console.log('typeof response.data', typeof response.data);
                    let res = {};
                    if (typeof response.data === 'string' && response.data) {
                        res = JSON.parse(response.data);
                    } else {
                        res = response.data;
                    }
                    console.log('cloudPostres----', res);
                    let resData;
                    if (typeof res === 'string' && res) {
                        resData = JSON.parse(res);
                    } else {
                        resData = res;
                    }
                    if (!isAliResponse) {
                        if (resData && resData.success) {
                            resolve(resData);
                        } else {
                            console.log('cloudPostres----', resData.errorCode);
                            //  重新登录的错误码
                            if (resData.errorCode === 'B_C20_000005' || resData.errorCode === 'B_C20_000004') {
                                // 重新登录
                                uni.clearStorageSync();

                                // uni.redirectTo({
                                //     url:'/pages/thirdHome/main'
                                // })
                                // return
                                uni.reLaunch({
                                    url: '/pages/thirdHome/main',
                                });
                                return;
                            }
                            // 刷新token的错误码
                            if (resData.errorCode === 'B_C20_000008') {
                                refreshLogin(url, params, config, cloudPost, resolve);
                                return;
                            }
                            // 是否需要自行展示自定义报错弹窗
                            showError({
                                result: resData,
                                config: config,
                                url: url,
                            });
                            resolve(resData);
                        }
                    } else {
                        if (resData && resData.resultDesc) {
                            resolve(resData);
                        } else {
                            resolve(resData);
                        }
                    }
                })
                .catch(error => {
                    // if (typeof response.data === 'string' && response.data) {
                    //     res = JSON.parse(response.data);
                    // } else {
                    //     res = response.data;
                    // }
                    console.log('MP.MGS.call---', error);
                    showError({
                        result: { message: error?.errorMessage || '请求异常', errorCode: error?.error || '' },
                        config: config,
                        url: url,
                    });
                    HideLoading(config.isload);
                    reject(error);
                });
        });
    } catch (error) {
        console.log(error, 'cloud-err-post');
    }
};

function currentLimiting(response, url, config) {
    if (response.header && (response.header['Result-Status'] == '1002' || response.header['result-status'] == '1002')) {
        console.log(url + '触发了限流');
        let message = response.data ? response.data.replace(/"/g, '') : '请求异常';
        showError({
            result: { message: message, errorCode: '1002' },
            config: config,
            url: url,
        });
        return true;
    } else {
        return false;
    }
}

function ShowLoading(isload) {
    if (isload) {
        if (needLoadingCount === 0) {
            uni.showLoading({
                title: '加载中',
                mask: true,
            });
        }
        needLoadingCount++;
    }
}

function HideLoading(isload) {
    if (isload) {
        if (needLoadingCount <= 0) {
            return;
        }
        needLoadingCount--;
        if (needLoadingCount === 0) {
            uni.hideLoading();
        }
    }
}
function cachedCommonArgMiniVersion() {
    return new Promise(async (resolve, reject) => {
        if (!commonArgs || !miniVersion || !userTokenInfo) {
            console.log('commonArgs走这里了?');
            try {
                [commonArgs, miniVersion, userTokenInfo] = await Promise.all([
                    cnpcBridge.getCommonArgs(),
                    cnpcBridge.getMiniVersion(miniAppId),
                    cnpcBridge.getUserTokenInfo(),
                ]);
                let systemInfo = my.getSystemInfoSync();
                console.log('system---', systemInfo.system);
                if (systemInfo && (systemInfo.system.includes('Harmony') || systemInfo.platform == 'Harmony')) {
                    isHarmony = true;
                }
                console.log('commonArgs走这里了?', miniVersion, commonArgs, isHarmony, userTokenInfo);
                resolve();
            } catch (error) {
                resolve();
            }
        } else {
            resolve();
        }
    });
}
// 统一报错弹窗
// result, config, url
const showError = obj => {
    let message = obj.result.message || '请求异常';
    if (obj.result?.status) {
        if (obj.result.status == 504) {
            obj.result.errorCode = '504';
            message = '系统繁忙~请稍后再试~';
        }
    } else if (obj.result.errorCode == 6666) {
        message = '系统繁忙~请稍后再试~';
    } else if ((obj.result.errorCode >= 0 && obj.result.errorCode <= 18) || (obj.result.errorCode >= 400 && obj.result.errorCode < 500)) {
        message = '网络出错，请稍后再试';
    } else if ((obj.result.errorCode < 400 && obj.result.errorCode >= 100) || (obj.result.errorCode > 500 && obj.result.errorCode < 600)) {
        message = '网络无法连接，请稍后再试';
    }
    // #ifndef H5-CLOUD
    bizExp({ errorCode: obj.result.errorCode, message: message, url: obj.url });
    // #endif
    if (obj.config?.isCustomErr) return;
    store.dispatch('zjShowModal', {
        title: message,
        content: obj.result.errorCode || '',
        type: 'http',
        success: res => {
            if (res.confirm) {
                console.log('用户点击确定', obj);

                if (obj.config.handleErrorFn) obj.config.handleErrorFn(obj.result);
            } else if (res.cancel) {
                console.log('用户点击取消');
            }
        },
    });
};

// 无感刷新token逻辑
const refreshLogin = async (url, params, config, post, postResolve) => {
    // if (url == 'user.logout') return;
    ddList.push({
        url,
        params,
        config,
        postResolve,
    });

    function logout() {
        isErrorCodeFlag = true;
        // #ifdef MP-MPAAS
        // 重新登录
        cnpcBridge.businessTypeAlertDialog('登录信息已过期,请重新登录', 'LoginExpired', res => {
            //退出登录
            commonUtil.logoutFun();
        });
        // #endif
        // #ifdef MP-ALIPAY
        my.clearStorageSync();
        layer.backHomeFun();
        // #endif
        // #ifdef MP-WEIXIN
        wx.clearStorageSync();
        layer.backHomeFun();
        // #endif
        // #ifdef MP-TOUTIAO
        uni.clearStorageSync();
        layer.backHomeFun();
        // #endif
        // #ifdef H5-CLOUD
        uni.clearStorageSync();
        layer.backHomeFun();
        // #endif
    }

    async function rePost() {
        isErrorCodeFlag = true;
        let i = 0;
        while (1) {
            const element = ddList.shift(0);
            if (element) {
                element.postResolve(await post(element.url, element.params, element.config));
            } else {
                break;
            }
        }
    }

    if (isErrorCodeFlag) {
        isErrorCodeFlag = false;
        store.dispatch('tokenExpired', {
            success: () => {
                rePost();
            },
            fail: res => {
                // 重新登录
                logout();
            },
        });
    }
};

// 接口入参统一添加风控字段
const addExtendFiled = async (url, config = {}) => {
    return new Promise(async (resolve, reject) => {
        if (riskControlInterfaceList.includes(`${url}`) || url == 'plugin') {
            let latitude = store.state.locationV3_app.riskManagementLatV3;
            let longitude = store.state.locationV3_app.riskManagementLonV3;
            let dfp = '';
            try {
                if (platform.isAlipay) dfp = (await zfbFPMixins?.methods?.getFP()) || '';
                const methodName = isHarmony ? 'getHmFP' : 'getFP';
                if (platform.isWeiXin) dfp = (await wxFPMixins?.methods?.[methodName]?.()) || '';
                // if (isHarmony) dfp = (await zfbFPMixins?.methods.getFP()) || '';
            } catch (err) {
                console.log(err, '测试鸿蒙====');
            }
            console.log(store.state.locationV3_app.oldLocationState, '获取位置信息对比值');
            let extendFiled = {
                dfp: dfp,
                gps: longitude !== '' ? `(${longitude},${latitude})` : '',
                gpsProvince: store.state.locationV3_app.provinceV3,
                gpsCity: store.state.locationV3_app.cityV3,
                gpsArea: store.state.locationV3_app.oldLocationState ? store.state.locationV3_app.district : '未知',
            };
            console.log('config.isAuth', config.isAuth);
            if (config.isAuth) {
                //如果是实人认证的过的，多拼两个参数
                extendFiled.authStatus = 1;
                extendFiled.authTime = url === 'invoice.make' ? new Date().getTime() : layer.getMyTime(new Date().getTime());
            }
            console.log('风控参数：', extendFiled);
            if (url == 'plugin') {
                resolve(encodeURIComponent(JSON.stringify(extendFiled)));
            } else {
                resolve(JSON.stringify(extendFiled));
            }
        } else {
            resolve(false);
        }
    });
};

const apiDebounce = (url, timeData) => {
    let flag = true;
    apiDebounceList = apiDebounceList.filter(item => {
        return timeData - item.timeData <= 1000;
    });
    let obj = apiDebounceList.find(item => {
        return item.url == url;
    });

    if (obj && timeData - obj.timeData < 1000) {
        flag = false;
    }
    apiDebounceList = apiDebounceList.filter(item => {
        return url !== item.url;
    });
    apiDebounceList.push({ url: url, timeData: timeData });
    console.log(obj, flag, apiDebounceList, 'apiDebounceList');
    return flag;
};

const noUpErrorList = [
    { errorCode: 'P_C20_007307', message: '未查询到订单信息' },
    { errorCode: 'P_C20_008311', message: '刷新token失败' },
    { errorCode: 'P_C20_002007', message: '未查询到证件信息' },
];
const bizExp = obj => {
    const errorObj = { errorCode: obj.errorCode, message: obj.message };
    let isObjectFound = noUpErrorList.some(item => item.errorCode === errorObj.errorCode && item.message === errorObj.message);
    if (isObjectFound) return;
    let bizMsg = obj.message.replace(/,/g, ' ');
    sKit.mpBP.tracker('业务异常埋点', {
        seed: 'biz_exp',
        clientSdkError: 'client_003', // 返回sdk标识
        clientSdkApi: obj.url, // 接口名
        clientSdkCode: obj.errorCode, // 错误码
        clientSdkMsg: bizMsg, // 错误文案
        clientChannel: clientCode, // 渠道C10/C12/C13
    });
};
export default {
    addExtendFiled,
    showError,
};
