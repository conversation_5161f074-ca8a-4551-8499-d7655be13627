<template>
    <div :style="oilBg + ';' + `padding-top: ${10 + Number(systemBar)}px`" class="content-box-page p-bf ov-hid fl-column bg-fff">
        <!-- <zj-navbar v-if="tabType" :border-bottom="false" :background="backgroundTab" :is-back="true"></zj-navbar>测试 -->
        <!-- #ifdef MP-WEIXIN || MP-MPAAS || H5-CLOUD -->
        <div :style="backBoxClass" @click="goBack">
            <div v-if="tabType" :style="borderColor" class="icon-back"></div>
        </div>
        <!-- #endif -->

        <main-tabs ref="mainTabs" :seltab="topTab" @changeTab="changeTab"></main-tabs>
        <div class="oil-tab" :style="{ left: topTab != 'code' ? 0 : '120%' }">
            <!-- #ifndef H5-CLOUD -->
            <map
                v-if="isShowMap"
                id="map"
                :enable-overlooking="false"
                :enable-rotate="false"
                :enable-scroll="isEnableScroll"
                :latitude="newMapCenterLatV3"
                :longitude="mapCenterLonV3"
                :markers="showMarkerArrV3_app"
                :scale="Number(scale_app)"
                :setting="mapSetting"
                :show-location="showLocation"
                :style="{ height: mapHeight + 'px', width: mapWidth + 'px' }"
                @callouttap="clickCallOut"
                @markertap="clickMarker"
            >
                <!-- #ifdef MP-WEIXIN -->
                <cover-view slot="callout" v-if="isCustomCallout">
                    <cover-view
                        v-for="item in showMarkerArrV3_app"
                        :key="item.id"
                        :marker-id="item.id"
                        class="marker-view fl-row fl-al-cen"
                    >
                        <cover-view class="marker-lable-view fl-row fl-al-cen">
                            <cover-image v-if="item.isFirst" class="navigation" :src="mapImgClosest" alt=""></cover-image>
                            <cover-image v-else class="navigation" :src="mapImg" alt=""></cover-image>
                            <cover-view class="fl-column mar-left-4">
                                <cover-view class="marker-name weight-600 color-000">
                                    {{ item.orgName + '　' }}
                                </cover-view>
                                <cover-view class="fl-row pa-t-5 fl-al-cen">
                                    <cover-view class="marker-text font-10 color-333">距离您</cover-view>
                                    <cover-view class="marker-text font-10 color-E64F22">{{ item.distance }}km </cover-view>

                                    <!-- <cover-view class="l-h28 font-10 color-333">， 驾车约</cover-view>
                                    <cover-view class="l-h28 font-10 color-E64F22">4min</cover-view>-->
                                </cover-view>
                            </cover-view>
                        </cover-view>
                    </cover-view>
                </cover-view>
                <!-- #endif -->
            </map>
            <template v-if="isShowMapContent">
                <!-- 定位悬停按钮 -->
                <div class="location-icon" @click="clickLocation" :style="{ bottom: iconBottom + 'px' }">
                    <img :src="icon" alt="" />
                </div>
                <!-- 位置授权未开启 -->
                <div v-if="isLocationBlockData && isHarmony && iconBottom > 0" :style="{ bottom: iconBottom + 'px' }" class="no-location">
                    <div class="location-cell">
                        <div class="tip">开启定位服务，为您推荐更多优质服务</div>
                        <div class="btn-icon" @click.stop="openLocationCheck">开启</div>
                        <div class="close-icon" @click.stop="closeLocation">
                            <img src="./image/loca-close.png" alt="" />
                        </div>
                    </div>
                </div>
                <div
                    v-if="topTab == 'charge' && isLogin"
                    ref="drag-content1"
                    class="drag-content drag-content1"
                    @touchend="fuelTouchend"
                    @touchstart="fuelTouchstart"
                    @touchmove.stop.prevent
                >
                    <chargeContentView
                        ref="chargeContentView"
                        :refer="refer"
                        class="popup-view"
                        @changeViewHeight="changeViewHeight"
                        @reMoveing="reMoveing"
                    ></chargeContentView>
                </div>
                <div
                    v-else-if="topTab == 'reserve' && isLogin"
                    ref="drag-content2"
                    class="drag-content drag-content2"
                    @touchend="fuelTouchend"
                    @touchstart="fuelTouchstart"
                    @touchmove.stop.prevent
                >
                    <reserveContentView
                        ref="reserveContentView"
                        :refer="refer"
                        class="popup-view"
                        @isShowDialog="isShowDialog"
                        @reMoveing="reMoveing"
                    >
                    </reserveContentView>
                </div>
                <div class="drag-content" v-if="!isLogin && !isHarmony" style="bottom: 0">
                    <noLoginView class="noLoginView" :topTab="topTab"></noLoginView>
                </div>
            </template>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <img class="bg-img" :src="oilHomeBg" alt="" />
            <div
                v-if="topTab == 'charge' && isLogin"
                ref="drag-content1"
                :style="{ top: Number(scrollTopDistance) > 0 ? 0 : boxTop + 'px' }"
                class="drag-content drag-content1"
                @click="drawerappClick(1)"
                @touchmove.stop.prevent
            >
                <chargeContentView
                    ref="chargeContentView"
                    :refer="refer"
                    class="popup-view"
                    @chageScrollTop="chageScrollTop"
                    @reMoveing="reMoveing"
                ></chargeContentView>
            </div>
            <div
                v-else-if="topTab == 'reserve' && isLogin"
                ref="drag-content2"
                :style="{ top: boxTop }"
                class="drag-content drag-content2"
                @click="drawerappClick(2)"
            >
                <reserveContentView
                    ref="reserveContentView"
                    :refer="refer"
                    class="popup-view"
                    @isShowDialog="isShowDialog"
                    @reMoveing="reMoveing"
                >
                </reserveContentView>
            </div>
            <div v-if="!isLogin" class="drag-content" style="bottom: 0">
                <noLoginView :topTab="topTab" class="noLoginView"></noLoginView>
            </div>
            <!-- #endif -->
        </div>
        <!-- #ifndef H5-CLOUD -->
        <div class="codePayment" v-if="topTab == 'code'">
            <codePaymentContentView @changeBg="changeViewBg" :tabType="tabType" ref="codePaymentConten" :refer="refer">
            </codePaymentContentView>
        </div>
        <!-- #endif -->
        <!-- #ifdef MP-MPAAS -->
        <custom-popup :maskClick="false" ref="oilPopup" type="bottom" v-if="!isHarmony">
            <div class="model-div">
                <div class="marlr16 padtb16">
                    <div class="fl-row fl-jus-bet fl-al-cen marb16">
                        <div class="font-16 weight-600 color-000">请您选择当前的状态</div>
                        <div class="close" @click="closepop">
                            <img src="./image/close.png" alt />
                        </div>
                    </div>
                    <div class="card-default fl-column fl-al-jus-cen marb12 padtb16" @click="changeCell('charge')">
                        <div class="font-18 color-E64F22 weight-600">已完成加油</div>
                        <div class="font-16 color-666">查询油枪订单进行支付</div>
                    </div>
                    <div class="card-default fl-column fl-al-jus-cen marb12 padtb16" @click="changeCell('code')">
                        <div class="font-18 color-E64F22 weight-600">已完成加油</div>
                        <div class="font-16 color-666">出示二维码进行支付</div>
                    </div>
                    <div class="card-default fl-column fl-al-jus-cen padtb16" @click="changeCell('reserve')">
                        <div class="font-18 color-333 weight-600">未完成加油</div>
                        <div class="font-16 color-666">使用e享加油预授权支付</div>
                    </div>
                </div>
            </div>
        </custom-popup>
        <!-- #endif -->
        <!-- #ifndef MP-MPAAS -->
        <div :class="{ zjMarketBox: isPageH, noZjMarketBox: !isPageH }" v-if="selectMarkerV3 && selectMarkerV3.orgCode && isLogin">
            <zjMarket
                v-if="topTab != 'code'"
                ref="maskBanner"
                :orgCode="selectMarkerV3.orgCode"
                marketType="screenMask"
                spaceType="home_page"
                @isPageH="getIsPageH"
            ></zjMarket>
        </div>
        <!-- #endif -->
    </div>
</template>

<script>
import platform from '@/s-kit/js/platform';
import { mapState, mapGetters } from 'vuex';
import mainTabs from './components/third-main-tabs/main.vue';
// #ifndef H5-CLOUD
import codePaymentContentView from './components/chagemain/code-payment-content-view.vue';
// #endif
import chargeContentView from './components/chagemain/charge-content-view.vue';
import reserveContentView from './components/chagemain/reserve-content-view.vue';
import noLoginView from './components/chagemain/noLogin-view.vue';
import { notPaidListApi } from '@/s-kit/js/v3-http/https3/oilStationService/index';
import projectConfig, { maxDistance, clientCode, name } from '../../.././../project.config';

import zfbMap from '@/mixins/zfb-map';
import zjMarket from '@/s-kit/components/layout/zj-marketing/zj-marketing.vue';
import { memberInfo } from '../../js/v3-http/https3/classInterest/index';
import { basicInfoQuery } from '../../js/v3-http/https3/oilCard';
export default {
    name: 'thirdOil',
    mixins: [zfbMap],
    components: {
        // 顶部导航栏切换
        mainTabs,
        // 扫码付款功能
        // #ifndef H5-CLOUD
        codePaymentContentView,
        // #endif
        //加油功能
        chargeContentView,
        // e享加油功能
        reserveContentView,
        noLoginView,
        zjMarket,
    },
    props: {
        tabType: {
            default: '',
            type: String,
        },
        stationCode: {
            default: '',
            type: String,
        },
        noInitLocation: {
            type: Boolean,
            default: false,
        },
        refer: {
            type: String,
            default: '',
        },
        myRefer: {
            type: String,
            default: '',
        },
        isLogin: {
            type: Boolean,
            default: true,
        },
        systemBarNum: {
            type: String,
            default: '',
        },
        pageChanged: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isLocationBlockData: false,
            // #ifndef MP-MPAAS
            topTab: 'charge',
            oilHomeBg: require('../../image/oil-home-bg.png'),
            // #endif
            // #ifdef MP-MPAAS
            topTab: 'reserve',
            // #endif
            // #ifdef MP-WEIXIN
            isWX: true,
            // #endif
            icon: require('./image/currentLocation-2.png'),
            mapImgClosest: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/stationClosest.png',
            mapImg: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/station.png',
            isCustomCallout: false,
            iconBottom: 0,
            // 地图高度
            mapHeight: 0,
            // 地图宽度
            mapWidth: 0,
            // 会员码切换的标识 用来修改扫码支付的背景颜色
            oilBgFlag: 'membershipCodeFlag',
            // 地图设置开启手指缩放
            mapSetting: {
                // #ifdef MP-MPAAS
                // 手势
                gestureEnable: 1,
                // 比例尺
                showScale: 1,
                // 指南针
                showCompass: 0,
                // 双手下滑
                tiltGesturesEnabled: 1,
                // 交通路况展示
                trafficEnabled: 0,
                // 地图POI信息
                showMapText: 1,
                // #endif
            },
            //系统导航栏高度
            systemBar: '',
            // 油品数据
            // fuelData:[],
            //油品选择
            selOilType: {},
            //油枪输入
            selectCurrent: '',
            //是否有加油油品订单
            orderOilStatus: false,
            //油品集合
            notPaidList: [],
            seletctOilOrderData: {},
            navH: 28,
            mapCtx: {},
            popupMoving: false,
            isPageH: false,
            isFuel: false, //表示是否在加油操作中,加油操作中无法点击地图
            scrollTopDistance: 0,
            isShowMap: false,
            isShowMapContent: false,
            showLocation: true,
        };
    },
    created() {
        console.log('platform', platform);
        // #ifdef MP-MPAAS
        this.$cnpcBridge.getBarHeight(res => {
            this.systemBar = res;
        });
        if (this.isHarmony) {
            this.showLocation = false;
        } else {
            this.isShowMap = true;
            this.isShowMapContent = true;
        }
        // #endif

        let systemInfo;
        // #ifndef MP-MPAAS || H5-CLOUD
        systemInfo = uni.getSystemInfoSync();
        console.log('systemInfo---', systemInfo);

        if (platform.isAlipay) {
            console.log('systemInfo.statusBarHeight----', systemInfo.statusBarHeight);
            this.systemBar = systemInfo.statusBarHeight + Number(this.navH);
        } else {
            this.systemBar = systemInfo.statusBarHeight;
        }
        // #endif
        // #ifdef H5-CLOUD
        this.topTab = 'charge';
        // #endif
        this.isCustomCallout = true;
        if (this.tabType == '' && !this.isHarmony) {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.getValueToNative('Define_is_first_popup', value => {
                if (this.tabType == '') {
                    if (value == '') {
                        this.$refs.oilPopup.open();
                    } else {
                        console.log('value-----', value);
                        this.changeTab(value);
                        let dataType = value == 'code' ? 'code' : value;
                        this.$refs.mainTabs.seltab = dataType;
                    }
                }
            });
            // #endif
        }
    },
    async mounted() {
        uni.hideLoading();
        console.log(this.isLogin, 'isLogin====');
        // #ifdef MP-MPAAS
        console.log('locationState---', this.locationState);
        console.log('this.isHarmony----', this.isHarmony);
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        if (this.homepageTags) {
            this.topTab = this.homepageTags;
            this.$refs.mainTabs.changeTab(this.homepageTags);
            console.log(this.$refs.seltab, 'this.$refs.seltab');
        }
        setTimeout(() => {
            this.$store.commit('setHomepageTags', '');
        }, 1000);
        // #endif
        if (this.topTab == 'code' && this.isHarmony) {
        } else {
            this.setMapHeight();
        }
        this.drawerappClick(3);
        // #ifdef MP-WEIXIN
        console.log('执行=============');
        if (this.officialAccountParams == 'yt' && this.ytPhone) {
            if (!this.phone) {
                let res = await memberInfo({}, { isload: false, is_error: false });
                if (res.success && res.data.phone) {
                    this.comparePhoneNumbers(res.data.phone, this.ytPhone);
                }
            }
        }
        // #endif
        // #ifdef H5-CLOUD
        console.log('getApp().globalData.systemBar', getApp().globalData.systemBar);
        this.systemBar = Number(getApp().globalData.systemBar) + Number(28);
        // #endif
        this.initLocation();
    },
    methods: {
        isLocationBlock() {
            if (this.locationState) {
                this.isLocationBlockData = false;
            } else {
                this.isLocationBlockData = true;
            }
        },
        chageScrollTop(top) {
            this.scrollTopDistance = top;
        },
        fuelTouchstart() {
            this.isFuel = true;
        },
        fuelTouchend() {
            setTimeout(() => {
                this.isFuel = false;
            }, 300);
        },
        getIsPageH(res) {
            this.isPageH = res && this.isUpAdPopupOil;
            this.$store.commit('setIsUpAdPopupOil', false);
        },
        // #ifdef MP-WEIXIN
        // 优途小程序跳转过来以后小判断手机号是否相等
        comparePhoneNumbers(phone1, phone2) {
            // 提取手机号的前三位和后四位
            const phone1Prefix = phone1.substring(0, 3);
            const phone1Suffix = phone1.substring(phone1.length - 4);
            const phone2Prefix = phone2.substring(0, 3);
            const phone2Suffix = phone2.substring(phone2.length - 4);

            // 比较前三位和后四位是否相等
            if (phone1Prefix === phone2Prefix && phone1Suffix === phone2Suffix) {
                return;
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: `当前登录手机号${phone1.substring(0, 3) + '****' + phone1.substring(7)}与优途${
                        this.ytPhone
                    }不一致,请您确认是否需要重新登录`,
                    confirmText: '我知道了',
                    cancelText: '',
                    confirmColor: '#333',
                    maskClosable: true, // 不允许点击遮罩层关闭弹窗
                    success: async res => {
                        if (res.confirm) {
                            this.$store.commit('setYtPhone', '');
                            this.$store.dispatch('setOfficialAccountParams', '');
                        } else if (res.cancel) {
                        }
                    },
                });
            }
        },
        // #endif
        //开启定位
        openLocationCheck() {
            this.$cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(res => {
                    if (res) {
                        this.initLocation();
                    }
                });
        },
        // 关闭位置未授权的提示框
        closeLocation() {
            this.isLocationBlockData = false;
        },
        async initLocation() {
            if (this.noInitLocation) return;
            const nextFun = async () => {
                let initParams = {};
                if (this.isHarmony) {
                } else if (JSON.stringify(name).includes('cnpc-cloudpay')) {
                    initParams = {
                        scene: 'site',
                    };
                } else {
                    initParams = {
                        callback: res => {
                            this.$nextTick(() => {
                                if (this.stationCode !== '') {
                                    uni.showLoading();
                                    let selectItem = this.showMarkerArrV3_app.find(item => item.orgCode == this.stationCode);
                                    if (selectItem) {
                                        this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                                            marker: selectItem,
                                        });
                                    }
                                    uni.hideLoading();
                                }
                                // #ifdef MP-WEIXIN
                                this.mapCtx.moveToLocation({ latitude: res.mapCenterLatV3, longitude: res.mapCenterLonV3 });
                                // #endif
                                // #ifdef MP-MPAAS
                                if (this.isHarmony) {
                                    this.mapCtx.moveToLocation({
                                        latitude: Number(res.mapCenterLatV3) - 0.04,
                                        longitude: res.mapCenterLonV3,
                                    });
                                } else {
                                    this.mapCtx.moveToLocation({
                                        latitude: Number(res.mapCenterLatV3) - 0.07,
                                        longitude: res.mapCenterLonV3,
                                    });
                                }
                                // #endif
                                // #ifdef MP-ALIPAY
                                this.mapCtx.moveToLocation({
                                    latitude: Number(res.mapCenterLatV3) - 0.035,
                                    longitude: res.mapCenterLonV3,
                                });
                                // #endif
                            });
                        },
                        type: 'initMap',
                    };
                }
                this.$nextTick(() => {
                    this.mapCtx = uni.createMapContext('map');
                    this.$store.dispatch('initLocationV3_app', initParams);
                });
            };

            // #ifdef MP-MPAAS
            if (this.isHarmony) {
                nextFun();
            } else {
                this.$nextTick(() => {
                    this.$cnpcBridge.checkPermission().then(res => {
                        console.log(res);
                        if (!res.appStatus) {
                            this.$cnpcBridge
                                .confirmDialog({
                                    title: '位置权限使用说明',
                                    message: '根据您的位置信息获取您附近的加油站网点信息服务,是否开启？',
                                    confirmBtnText: '去开启',
                                })
                                .then(() => {
                                    this.$cnpcBridge
                                        .openPermissions({
                                            code: 'location',
                                            explain: '位置权限使用说明',
                                            detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                                        })
                                        .then(res => {
                                            if (res) {
                                                nextFun();
                                            }
                                        });
                                });
                        } else {
                            nextFun();
                        }
                    });
                });
            }
            // if (this.isHarmony) {
            //     this.$cnpcBridge.isCutScreen(true);
            // }
            this.$refs.codePaymentConten.$refs.membershipCode.init();
            // #endif
            // #ifndef MP-MPAAS
            nextFun();
            // #endif
        },
        reMoveing(moveBoolean) {
            this.popupMoving = moveBoolean;
            console.log(this.popupMoving, '父组件 --- popupMoving');
        },
        changeViewHeight() {
            this.drawerappClick(3);
        },
        /**
         * @description  : 加油页面获取数据
         * @param         {string} topTab - 加油类型
         * @param         {Function} notPaidListGet - 获取待结交易数据
         * @param         {Function} storageDataFun - 获取预约加油的缓存金额和缓存油品
         * @param         {Function} restartTimer - 调用会员码初始化
         * @return        {*}
         */
        chargenotPaidListOrder() {
            console.log('---进入了');
            if (this.topTab == 'charge') {
                setTimeout(() => {
                    // 返回关闭待结交易列表页面
                    this.$refs.chargeContentView.clearOrderOilStatus();
                    this.$refs.chargeContentView.notPaidListGet();
                }, 300);
            } else if (this.topTab == 'reserve') {
                this.$refs.reserveContentView.storageDataFun();
            }
            if (this.topTab == 'code') {
                // 520版本临时处理支付宝小程序rops扫码轮询到结果跳转两次支付页面问题,530版本解决该问题
                this.$refs.codePaymentConten.restartTimer(true);
            }
        },
        /**
         * @description  : 处理地图组件的底部加油功能的上拉加载
         * @param         {string} type - 滑动类型，为3时，不滑动
         * @return        {*}
         */
        drawerappClick(type) {
            if (this.topTab == 'reserve' || this.topTab == 'charge') {
                if (type == 3) {
                    // if (!this.isHarmony) {
                    this.$nextTick(() => {
                        setTimeout(() => {
                            uni.createSelectorQuery()
                                .in(this)
                                .select('.drag-content')
                                .boundingClientRect(data => {
                                    if (data.height > 490) {
                                        this.iconBottom = 60;
                                    } else {
                                        this.iconBottom = data.height + 10;
                                    }
                                })
                                .exec();
                        }, 600);
                    });
                    // }
                    this.chageScrollTop(0);
                    // this.$store.commit('setLoadingStatus', false);
                }
            }
        },
        /**
         * @description  : 地图气泡点击事件
         * @param         {string} latitude - 纬度
         * @param         {string} longitude - 经度
         * @param         {string} name - 油站名称
         * @param         {string} address - 油站地址
         * @return        {*}
         */
        clickCallOut(e) {
            setTimeout(() => {
                // #ifndef MP-MPAAS
                console.log(this.isFuel, 'isFuel', this.popupMoving, 'popupMoving');
                if (this.guideStep || this.isFuel || this.popupMoving || (this.officialAccountParams == 'yt' && ytPhone)) return;
                // #endif
                let markerId = e.detail.markerId;
                let clickItem = this.showMarkerArrV3_app.find(item => {
                    return item.id == markerId;
                });
                // #ifdef MP-MPAAS
                this.$cnpcBridge.openLocation({
                    latitude: clickItem.latitude,
                    longitude: clickItem.longitude,
                    name: clickItem.orgName,
                    address: clickItem.address || clickItem.orgName,
                });
                // #endif
                // #ifndef MP-MPAAS
                uni.openLocation({
                    latitude: Number(clickItem.latitude),
                    longitude: Number(clickItem.longitude),
                    name: clickItem.orgName,
                    address: clickItem.address || clickItem.orgName,
                });
                // #endif
            }, 300);
        },
        /**
         * @description  : 点击重新定位点击事件
         * @return        {*}
         */
        // 点击重新定位点击事件
        clickLocation() {
            this.$store.dispatch('initLocationV3_app', {
                callback: res => {
                    console.log(res, '重新定位经纬度');
                    // #ifdef MP-WEIXIN
                    this.mapCtx.moveToLocation({ latitude: res.mapCenterLatV3, longitude: res.mapCenterLonV3 });
                    // #endif
                    // #ifdef MP-MPAAS
                    if (this.isHarmony) {
                        this.mapCtx.moveToLocation({ latitude: Number(res.mapCenterLatV3) - 0.07, longitude: res.mapCenterLonV3 });
                    } else {
                        this.mapCtx.moveToLocation({ latitude: Number(res.mapCenterLatV3) - 0.04, longitude: res.mapCenterLonV3 });
                    }
                    // #endif
                    // #ifdef MP-ALIPAY
                    this.mapCtx.moveToLocation({ latitude: Number(res.mapCenterLatV3) - 0.035, longitude: res.mapCenterLonV3 });
                    // #endif
                },
                type: 'initMap',
            });
        },
        /**
         * @description  : 获取窗口高度
         * @return        {*}
         */
        setMapHeight() {
            setTimeout(() => {
                // #ifdef MP-WEIXIN || H5-CLOUD
                uni.createSelectorQuery()
                    .in(this)
                    .select('.oil-tab')
                    .boundingClientRect(data => {
                        console.log('data-----', data);
                        this.mapHeight = data.height;
                        this.mapWidth = data.width;
                        this.isShowMap = true;
                        this.isShowMapContent = true;
                    })
                    .exec();
                // #endif
                // #ifndef MP-WEIXIN || H5-CLOUD
                my.createSelectorQuery()
                    .select('.oil-tab')
                    .boundingClientRect()
                    .exec(data => {
                        console.log('data-----', data);
                        this.mapHeight = data[0].height;
                        this.mapWidth = data[0].width;
                        this.isShowMap = true;
                        this.isShowMapContent = true;
                    });
                // #endif
            }, 100);
        },
        /**
         * @description  : 导航栏切换
         * @return        {*}
         */
        changeTab(tab) {
            if (!this.isLogin && tab == 'code') {
                this.toLoginPage();
                return;
            }
            this.topTab = tab;
            // this.$store.commit('setLoadingStatus', true);
            if (tab != 'code') {
                if (this.pageChanged) {
                    this.isShowMap = false;
                    this.$emit('changePageChange', false);
                }
                this.setMapHeight();
            }

            this.drawerappClick(3);
            // #ifdef MP-MPAAS
            console.log('locationState---', this.locationState);
            console.log('this.isHarmony----', this.isHarmony);
            if (this.isHarmony) {
                this.isLocationBlock();
            }
            // #endif
            // this.chargenotPaidListOrder()
        },
        // 跳转登录
        toLoginPage() {
            let url = '';
            // #ifdef MP-WEIXIN
            url = '/packages/transferAccount/pages/home/<USER>';
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/union/main?qKey=oil';
            // #endif
            // #ifdef H5-CLOUD
            url = '/packages/third-cloud-login/pages/home/<USER>';
            // #endif
            this.$sKit.layer.useRouter(url);
        },
        /**
         * @description  : 会员码切换的val 用来修改扫码支付的背景颜色
         * @return        {*}
         */
        changeViewBg(val) {
            this.oilBgFlag = val;
            console.log(this.oilBgFlag, 'oilBgFlag');
        },

        /**
         * @description  : 微信小程序端地图marker点击事件
         * @return        {*}
         */
        clickMarker(e) {
            setTimeout(() => {
                // #ifndef MP-MPAAS
                if (this.guideStep || this.isFuel || this.popupMoving || !this.isLogin) return;
                // #endif
                console.log(this.isFuel, 'isFuel', this.popupMoving, 'popupMoving');
                let markerId = e.detail.markerId;
                console.log(this.showMarkerArrV3_app, '油站列表');
                let index = this.showMarkerArrV3_app.findIndex(item => {
                    return item.stationId == markerId;
                });
                this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                    marker: this.showMarkerArrV3_app[index],
                });
                // #ifdef MP-MPAAS
                this.$cnpcBridge.setValueToNative(
                    'Define_Selected_Station',
                    encodeURIComponent(JSON.stringify(this.showMarkerArrV3_app[index])),
                );
                // #endif
            }, 300);
        },
        /**
         * @description  : 关闭首次选择默认加油方式
         * @return        {*}
         */
        closepop() {
            this.$refs.oilPopup.close();
        },
        /**
         * @description  : 选择首次选择默认加油方式
         * @return        {*}
         */
        changeCell(data) {
            console.log('dtata', data);
            this.changeTab(data);
            let dataType = data == 'code' ? 'code' : data;
            // #ifdef MP-MPAAS
            this.$cnpcBridge.setValueToNative('Define_is_first_popup', data);
            this.$refs.mainTabs.seltab = dataType;
            this.$refs.oilPopup.close();
            // #endif
        },
        /**
         * @description  : 返回首页和上一个页面
         * @return        {*}
         */
        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        //返回首页
        backHome() {
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
    },
    computed: {
        newMapCenterLatV3() {
            // #ifdef  MP-MPAAS
            if (this.isHarmony) {
                return Number(this.mapCenterLatV3) - 0.04;
            } else {
                return Number(this.mapCenterLatV3) - 0.07;
            }
            // #endif
            // #ifdef MP-WEIXIN
            return Number(this.mapCenterLatV3) - 0.035;
            // #endif
            // #ifdef MP-ALIPAY || MP-TOUTIAO
            return Number(this.mapCenterLatV3) - 0.035;
            // #endif
        },
        backgroundTab() {
            if (this.topTab == 'code') {
                return { background: 'transparent' };
            } else {
                return { background: '#fff' };
            }
        },
        /**
         * @description  : 加油和扫码页面背景
         * @return        {*}
         */
        oilBg() {
            if (this.topTab === 'code') {
                if (this.oilBgFlag === 'membershipCodeFlag') {
                    return 'background-image:linear-gradient(180deg, #DA4AF6 0%, #7A42F3 100%);';
                } else {
                    return 'background-image:linear-gradient(180deg, #ef8f4d 0%, #e65022 100%);  z-index: 9;';
                }
            } else {
                // #ifndef MP-MPAAS
                return 'background: #fff';
                // #endif
                // #ifdef MP-MPAAS
                return 'background: #F7F7FB';
                // #endif
                // return "background-image:linear-gradient(180deg, #ef8f4d 0%, #e65022 100%);  z-index: 9;";
            }
        },
        /**
         * @description  : 加油和扫码页面返回按钮
         * @return        {*}
         */
        borderColor() {
            if (this.topTab === 'code') {
                return 'border-color: #fff;';
            } else {
                return 'border-color: #333;';
            }
        },
        marginTop() {
            return Number(this.systemBar) + 10 + 'px';
        },

        // boxtop() {
        //     return 210 + 'px';
        // },
        isEnableScroll() {
            return (
                (!this.guideStep && !this.popupMoving) ||
                (this.officialAccountParams == 'yt' && this.ytPhone) ||
                !this.isFuel ||
                this.isHarmony
            );
        },
        backBoxClass() {
            const baseStyle = 'icon-back-box: width:25px';
            const heightStyle = 'height:20px';

            if (platform.isH5CLOUD || platform.isMPaas) {
                return baseStyle;
            } else {
                return `${baseStyle};${heightStyle}`;
            }
        },
        /**
         * @description  : 地图油站数据
         * @return        {*}
         */
        ...mapGetters(['showMarkerArrV3_app', 'token3', 'token']),
        ...mapState({
            // 地图缩放比例
            scale_app: state => state.locationV3_app.scale_app,
            mapCenterLatV3: state => state.locationV3_app.mapCenterLatV3,
            mapCenterLonV3: state => state.locationV3_app.mapCenterLonV3,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            guideStep: state => state.thirdIndex.guideStep, // 是否展示首次指引  true-是 false-否
            walletStatus: state => state.wallet.walletStatus,
            // #ifdef MP-WEIXIN
            phone: state => state.phone,
            ytPhone: state => state.location.ytPhone,
            officialAccountParams: state => state.location.officialAccountParams,
            // #endif
            isUpAdPopupOil: state => state.thirdIndex.isUpAdPopupOil,
            // #ifndef H5-CLOUD
            homepageTags: state => state.thirdLogin.homepageTags,
            // #endif
            token: state => state.token,
            token3: state => state.token3,
            isHarmony: state => state.thirdIndex.isHarmony,
            locationState: state => state.locationV3_app.locationState,
            boxTop: state => state.thirdIndex.boxTop,
        }),
    },
    watch: {
        tabType: {
            handler(val) {
                if (val) {
                    this.topTab = this.tabType;
                    console.log(this.tabType, this.topTab, 'topTab===');
                    // #ifdef MP-WEIXIN
                    // if (this.tabType == 'code') {
                    //     this.$parent.getPlugin()
                    // }
                    this.$store.dispatch('setOfficialAccountParams', '');
                    // #endif
                    this.$nextTick(() => {
                        console.log(this.$refs.mainTabs.seltab, 'this.$refs.mainTabs.seltab');
                        this.$refs.mainTabs.seltab = this.tabType;
                    });
                }
            },
            deep: true,
            immediate: true,
        },
        // #ifdef MP-WEIXIN
        '$store.state.locationV3_app.mapCenterLatV3': {
            handler: function (newValue, oldValue) {
                this.$nextTick(() => {
                    if (JSON.stringify(this.mapCtx) !== '{}') {
                        this.mapCtx.moveToLocation({ latitude: Number(this.mapCenterLatV3), longitude: Number(this.mapCenterLonV3) });
                    }
                });
            },
            deep: true,
        },
        '$store.state.locationV3_app.mapCenterLonV3': {
            handler: function (newValue, oldValue) {
                this.$nextTick(() => {
                    if (JSON.stringify(this.mapCtx) !== '{}') {
                        this.mapCtx.moveToLocation({ latitude: Number(this.mapCenterLatV3), longitude: Number(this.mapCenterLonV3) });
                    }
                });
            },
            deep: true,
            immediate: true,
        },
        // #endif
        '$store.state.locationV3_app.locationState': {
            handler: function (newValue, oldValue) {
                if (this.isHarmony) {
                    if (newValue) {
                        this.isLocationBlockData = false;
                    } else {
                        this.isLocationBlockData = true;
                    }
                }
            },
            deep: true,
            immediate: true,
        },
    },
    
    async beforeDestroy() {
        // #ifdef MP-MPAAS
        if(this.isHarmony){
            console.log('执行了吗---beforeDestroy')
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        if(this.isHarmony){
            console.log('执行了吗---destroyed')
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
};
</script>
<style scoped lang="scss">
.zjMarketBox {
    z-index: 9996;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}

.noZjMarketBox {
    height: 0;
    width: 0;
    display: none;
}

.content-box-page {
    height: 100%;

    .icon-back {
        width: 10px;
        height: 10px;
        border-style: solid;
        border-width: 0px 1px 1px 0px;
        display: inline-block;
        padding: 5px;
        transform: rotate(135deg);
        -webkit-transform: rotate(135deg);
        // margin-bottom: 15px;
        margin-left: 24px;
    }
}

.oil-tab {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;

    .bg-img {
        // background:red;
        width: 375px;
        height: 289px;
    }
}

.bg-img {
    // background:red;
    width: 375px;
    height: 289px;
}

.patb10 {
    padding: 10px 0;
}

.codePayment {
    height: 100%;
    min-height: 0;
    margin-top: 15px;
}

// 地图
map {
    .marker-view {
        position: relative;
        height: 51px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        width: fit-content;

        .marker-lable-view {
            padding: 0 8px 0 5px;
            height: 51px;
            background: #fff;
            box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            width: fit-content;

            .navigation {
                width: 39px;
                height: 39px;
            }

            .marker-name {
                line-height: 18px;
                font-size: 13px;
                overflow: visible;
                width: auto;
            }

            .marker-text {
                line-height: 14px;
            }

            .marker-118920 {
                width: 50px;
                height: 21px;
                background: rgba(17, 137, 32, 0.05);
                border-radius: 4px;
                line-height: 21px;
                text-align: center;
            }

            .pa-t-5 {
                padding-top: 5px;
            }
        }
    }
}

.location-icon {
    display: block;
    position: absolute;
    right: 45rpx;
    width: 40px;
    height: 40px;
    z-index: 999;

    img {
        width: 100%;
        height: 100%;
    }
}

.bottom-div {
    width: 100%;
    border-radius: 10px 10px 0px 0px;

    .mar-lr-16 {
        margin-left: 16px;
        margin-right: 16px;
    }

    .org-div {
        margin: 12px 12px 0 12px;

        .bord-oil {
            width: 56px;
            height: 17px;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            border-radius: 4px;
            border: 1px solid #e64f22;
            margin-left: 12px;
        }
    }
}

.no-bg {
    background: #fff;
}

.card-reser {
    margin: 0 16px 6px;
    padding: 9px 12px;
}

.marr3 {
    margin-right: 3px;
}

@media screen and (min-height: 710px) {
    .drag-content {
        position: absolute;
        width: 100%;
        display: flex;
        bottom: 0;
        max-height: 100%;
    }
}

@media screen and (max-height: 710px) {
    .drag-content {
        max-height: 50%;
        position: absolute;
        width: 100%;
        display: flex;
        bottom: 0;
    }
}

.noLoginView {
    width: 100%;
}

// #ifndef MP-TOUTIAO || H5-CLOUD
.popup-view {
    display: flex;
    flex: 1;
}

// #endif
// #ifdef H5-CLOUD
.popup-view {
    display: flex;
    flex: 1;
    flex-direction: column;
}

// #endif
// #ifdef MP-TOUTIAO
.popup-view {
    flex: 1;
}

// #endif
.model-div {
    background: #f7f7fb;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 16px;

    .marlr16 {
        margin: 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .close {
        img {
            width: 13px;
            height: 13px;
        }
    }
}

.no-location {
    position: absolute;
    right: 30px;
    z-index: 1200;

    .location-cell {
        width: 291px;
        height: 58px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 4px 4px 4px 4px;
        padding: 8px 17px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;

        &::before {
            position: absolute;
            content: '';
            bottom: -18px;
            right: 17px;
            border-width: 10px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }

        .tip {
            font-weight: 400;
            font-size: 13px;
            color: #ffffff;
            line-height: 21px;
            text-align: left;
            font-style: normal;
        }

        .btn-icon {
            width: 50px;
            height: 25px;
            background: linear-gradient(135deg, #ff3f01 0%, #ff7931 100%);
            border-radius: 42px 42px 42px 42px;
            line-height: 25px;
            margin-left: 13px;
            font-weight: 400;
            font-size: 13px;
            color: #ffffff;
            text-align: center;
        }

        .close-icon {
            img {
                width: 16px;
                height: 16px;
                margin-left: 17px;
            }
        }
    }
}

.model-div-charge {
    background: #f7f7fb;
    border-radius: 10px 10px 0px 0px;

    .marlr16 {
        margin: 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .pad12-7 {
        padding: 12px 7px 0;
    }

    .padlt127 {
        padding: 12px 7px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .close {
        padding: 10px;

        img {
            width: 13px;
            height: 13px;
        }
    }

    .con-list {
        justify-content: flex-start;
        width: 100%;
        flex-wrap: wrap;

        .item {
            width: 78px;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            font-size: 14px;
            color: #333333;
            margin-bottom: 12px;
            margin-left: 6px;
            box-sizing: border-box;
        }

        .item:nth-of-type(4n + 0) {
            margin-right: 0px;
        }

        .oil-type-sel {
            background: rgba(230, 79, 34, 0.16);
            border-radius: 4px;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }
}

._modal {
    flex: none;
    width: 560rpx;
    min-height: 207rpx;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;

    .iol-pop {
        padding: 38rpx 0 0;

        .content {
            padding: 0 20rpx;
            margin-bottom: 34rpx;
            width: 100%;
            min-height: 68rpx;
        }
    }

    .price-input-area {
        border-radius: 10px;
        height: 44px;

        .price-input {
            width: 100%;
            height: 100% !important;
            background: #f7f7fb;
            border-radius: 8px;
            text-align: center;
            box-sizing: border-box;
        }
    }

    .slot-btn-box {
        width: 100%;
        border-top: 1px solid #efeff4;

        .btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            font-weight: 400;
            line-height: 90rpx;

            &:active {
                background-color: #f7f7f7;
            }
        }

        .cancel_btn {
            border-right: 2rpx solid #efeff4;
        }

        .confirm {
            font-weight: bold;
        }
    }
}
</style>
