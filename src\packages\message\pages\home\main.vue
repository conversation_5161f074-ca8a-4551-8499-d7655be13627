<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            :background="pageConfig.bgColor"
            back-text="消息"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="type-list-view">
            <div class="type-list-item" v-for="(item, index) in messageTypeList" :key="index" @click="messageTypeClick(item)">
                <img class="type-item-icon" :src="item.icon" />
                <div class="item-text-view">
                    <div class="item-text-title">{{ item.msgTypeStr }}</div>
                    <div class="item-text-detail">{{ item.newMsgTitle }}</div>
                </div>
                <div class="item-info-view">
                    <div class="item-text-newDate">{{ item.newMsgTime }}</div>
                    <div class="item-text-noNum" v-if="item.msgTypeCnt > 0">{{ item.msgTypeCnt }}</div>
                </div>
            </div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getUnreadMsgTypeCnt } from '@/api/home.js';
export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            messageTypeList: [
                // {
                // 	icon: '/static/order-icon.png',
                // 	title: '加油订单',
                // 	detail: '最新加油订单提示。',
                // 	noNum: 0,
                // 	newDate: '2020-12-25'
                // },{
                // 	icon: '/static/message-icon.png',
                // 	title: '系统消息',
                // 	detail: '最新系统消息提示。',
                // 	noNum: 1,
                // 	newDate: '2020-12-25'
                // },{
                // 	icon: '/static/coupon-icon.png',
                // 	title: '优惠券',
                // 	detail: '最新优惠券消息提示。',
                // 	noNum: 3,
                // 	newDate: '2020-12-25'
                // },{
                // 	icon: '/static/online-icon.png',
                // 	title: '在线客服',
                // 	detail: '最新客服消息提示。',
                // 	noNum: 2,
                // 	newDate: '2020-12-25'
                // },
            ], // 消息类型list
        };
    },
    async onLoad() {},
    async onShow() {
        let {
            data: { msgTypeCntList },
        } = await getUnreadMsgTypeCnt();
        this.messageTypeList = msgTypeCntList.map(item => {
            let icon = '';
            const msgType = Number(item.msgType);
            switch (msgType) {
                case 1:
                    icon = '/static/message-icon.png';
                    break;
            }
            item.icon = icon;
            return item;
        });
    },
    methods: {
        messageTypeClick(item) {
            uni.navigateTo({
                url: `/packages/message/pages/message-list/main?msgType=${item.msgType}`,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
}

.type-list-view {
    margin-left: 15px;
    overflow: hidden;
    .type-list-item {
        display: flex;
        margin-top: 10px;
        align-items: center;
        height: 70px;
        width: 345px;
        background-color: #ffffff;
        border-radius: 5px;
        .type-item-icon {
            display: block;
            width: 20px;
            height: 20px;
            margin-left: 15px;
        }
        .item-text-view {
            margin-left: 15px;
            flex: 1;
            .item-text-title {
                font-weight: 700;
                font-size: 15px;
                line-height: 24px;
                color: #333333;
            }
            .item-text-detail {
                margin-top: 1px;
                font-size: 12px;
                line-height: 24px;
                color: #909090;
            }
        }
        .item-info-view {
            margin-right: 15px;
            .item-text-newDate {
                text-align: right;
                font-size: 12px;
                line-height: 24px;
                color: #909090;
            }
            .item-text-noNum {
                text-align: right;
                margin-top: 1px;
                font-size: 12px;
                line-height: 24px;
                color: #ff5a5a;
            }
        }
    }
}
</style>
