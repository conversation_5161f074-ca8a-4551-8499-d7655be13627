<template>
    <div class="detail-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor"
            :background="pageConfig.bgColor"
            :custom-back="clickCustomBackBtn"
            back-text="订单详情"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div v-if="detailObj != ''">
            <div class="head">
                <div class="tip-success">
                    <img v-if="detailObj.state == 2" class="tip-success-icon" src="@/static/homeIcon/select.png" mode="" />
                    <div class="tip-success-text">{{ detailObj.state == 2 ? '交易成功' : '交易取消' }}</div>
                </div>
                <div class="order-money"
                    ><span>¥</span
                    >{{
                        orderType == 2
                            ? detailObj.realAmt + (isInteger(detailObj.realAmt) ? '.00' : '')
                            : detailObj.amount + (isInteger(detailObj.amount) ? '.00' : '')
                    }}
                </div>
                <!-- <div class="pay-money">实付金额：¥{{detailObj.amount + (isInteger(detailObj.amount) ? '.00' : '')}}</div> -->
            </div>
            <div :class="'reasons'">
                <div class="order-title-view">
                    <div class="order-title">订单信息</div>
                    <!-- <div class="vehicle-item-view" :style="{
						border: '0.5px solid #DCDCDC',
						backgroundColor: '#f6f6f6'
					}">
						<div :class="'vehicle-item-pz vehicle-item-pz-select'">{{detailObj.carNumber}}</div>
						<div :class="'vehicle-item-yz vehicle-item-yz-select'">
							{{isNaN(Number(detailObj.oils)) ? detailObj.oils : detailObj.oils + '#'}}
						</div>
					</div> -->
                </div>
                <div class="reasons-cell" v-for="(item, index) in orderInfo" :key="index">
                    <div class="label-text">{{ item.title }}</div>
                    <div style="flex: 1"></div>
                    <div class="label-text title-font">
                        {{ item.detail }}
                    </div>
                </div>
            </div>
            <div :class="'reasons'" v-if="orderType == 2">
                <div class="order-title-view">
                    <div class="order-title">商品信息</div>
                </div>
                <div class="reasons-cell" v-for="(item, index) in commodityInfo" :key="index">
                    <div class="label-text">{{ item.title }}</div>
                    <div style="flex: 1"></div>
                    <div class="label-text title-font">
                        {{ item.detail }}
                    </div>
                </div>
            </div>
            <div :class="'reasons'" v-if="orderType == 2">
                <div class="order-title-view">
                    <div class="order-title">优惠信息</div>
                </div>
                <div class="reasons-cell" v-for="(item, index) in discountInfo" :key="index">
                    <div class="label-text">{{ item.title }}</div>
                    <div style="flex: 1"></div>
                    <div class="label-text title-font">
                        {{ item.detail }}
                    </div>
                </div>
            </div>
            <!-- <div class="reasons" style='padding-top: 11px;'>
				<div class="reasons-cell">
					<div class="label-text w-10">订单号</div>
					<div style="flex: 1"></div>
					<div class="label-text  title-font">{{ detailObj.orderNo }}</div>
				</div> -->
            <!-- 只有通过微信支付过来的订单才显示交易流水 -->
            <!-- <template v-if="detailObj.detailObj === 'wx' ">
					<div class="reasons-cell" v-if='detailObj.tradeNo'>
						<div class="label-text w-10">交易流水号</div>
						<div style="flex: 1"></div>
						<div class="label-text title-font" v-if='detailObj.tradeNo'>{{ detailObj.tradeNo }}</div>
					</div>
					<canvas class="order-barcode-icon" canvas-id='barcode' v-if="detailObj.tradeNo"></canvas>
				</template>
			</div>
			<div class="reasons-footer">
		  <button class="footer-btn title-font" open-type="share" type="default">分享订单</button>
		  <div class='footer-btn title-font' style='margin-left: 10px;' v-if='detailObj.isInvoice == 0 && detailObj.state == 2' @click='clickKaiPiao'>申请开票</div>
		</div> -->
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import wxbarcode from 'wxbarcode';
import { orderDetail, getRechargeDetail } from '@/api/home.js';
export default {
    name: 'order-detail',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            orderId: -1,
            detailObj: '',
            orderInfo: [
                {
                    title: '订单号',
                    detail: '',
                },
                {
                    title: '支付时间',
                    detail: '',
                },
                {
                    title: '支付方式',
                    detail: '',
                },
                {
                    title: '车牌号',
                    detail: '',
                },
                {
                    title: '油站',
                    detail: '',
                },
            ],
            commodityInfo: [
                {
                    title: '油枪',
                    detail: '',
                },
                {
                    title: '油品',
                    detail: '',
                },
                {
                    title: '加油量',
                    detail: '',
                },
                {
                    title: '油价',
                    detail: '',
                },
            ],
            discountInfo: [
                {
                    title: '优惠券',
                    detail: '',
                },
                {
                    title: '油卡优惠',
                    detail: '',
                },
            ],
            orderType: 0, // 区分充值订单和加油订单 1是充值订单 2是加油订单
        };
    },
    onLoad() {
        console.log(this.$mp.query);
        let options = this.$mp.query;
        this.orderId = options.id;
        this.orderType = options.orderType;
        this.getDetail();
    },
    onShareAppMessage() {},
    methods: {
        // 判断是否是整数
        isInteger(obj) {
            return Number.isInteger(obj);
        },
        async getDetail() {
            let res;
            if (this.orderType == 1) {
                res = await getRechargeDetail({
                    id: this.orderId,
                });
            } else {
                res = await orderDetail({
                    id: this.orderId,
                });
            }

            this.detailObj = res.data;
            wxbarcode.barcode('barcode', res.data.tradeNo, 630, 120);

            let payMode;
            if (this.detailObj.payMode == 'wx') {
                payMode = '微信支付';
            } else if (this.detailObj.payMode == 'oilCard') {
                payMode = `油卡（${this.detailObj.payCardNo.substring(
                    this.detailObj.payCardNo.length - 4,
                    this.detailObj.payCardNo.length,
                )}）`;
            }

            this.orderInfo[0].detail = this.detailObj.orderNo;
            this.orderInfo[1].detail = this.detailObj.orderTime;
            this.orderInfo[2].detail = payMode;
            this.orderInfo[3].detail = this.detailObj.carNumber;
            this.orderInfo[4].detail = this.detailObj.stationName;

            // 充值订单操作
            if (this.orderType == 1) {
                this.orderInfo[2].title = '充值卡号';
                this.orderInfo[2].detail = this.detailObj.cardNo;
                this.orderInfo[3].title = '订单金额';
                this.orderInfo[3].detail = '￥' + this.detailObj.amount + (this.isInteger(this.detailObj.amount) ? '.00' : '');
                this.orderInfo.pop();
            }

            this.commodityInfo[0].detail = this.detailObj.oilGun + '号枪';
            this.commodityInfo[1].detail = isNaN(Number(this.detailObj.oils)) ? this.detailObj.oils : this.detailObj.oils + '#';
            this.commodityInfo[2].detail = this.detailObj.quantity;
            this.commodityInfo[3].detail = this.detailObj.unitPrice + '元/升';

            this.discountInfo[0].detail = '-￥' + this.detailObj.otherAmt;
            this.discountInfo[1].detail = '-￥' + this.detailObj.discountAmt;
        },
        clickKaiPiao() {
            let selectOrderArr = [this.detailObj];
            // console.log(selectOrderArr)
            uni.redirectTo({
                url: '/packages/invoice-center/pages/add-invoice/main?list=' + encodeURIComponent(JSON.stringify(selectOrderArr)),
            });
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 12px;

.detail-center {
    width: 100%;
    height: 100%;
    background: #f6f6f6;

    .head {
        text-align: center;
        color: $btn-color;
        padding: 20px 0 6px 0;
        box-sizing: border-box;

        .tip-success {
            display: flex;
            align-items: center;
            justify-content: center;

            .tip-success-icon {
                height: 24px;
                width: 24px;
            }

            .tip-success-text {
                margin-left: 5px;
                line-height: 25px;
                font-weight: 700;
                color: #333;
                font-size: 18px;
            }
        }

        .order-money {
            font-size: 36px;
            line-height: 50px;
            font-weight: 700;

            span {
                font-size: 24px;
                font-weight: 700;
            }
        }

        .pay-money {
            font-size: 15px;
            font-weight: 700;
        }
    }

    .reasons {
        width: 91%;
        overflow: hidden;
        margin: 0 auto 10px;
        background-color: #ffffff;
        border-radius: 5px;
        padding: 0 0 11px 0;
        position: relative;

        .order-title-view {
            display: flex;
            align-items: center;
            height: 44px;
            justify-content: space-between;

            .order-title {
                margin-left: 10px;
                color: #333333;
                font-size: 15px;
                font-weight: 700;
            }

            .vehicle-item-view {
                height: 24px;
                margin-right: 10px;
                width: 104px;
                border-radius: 3px;
                position: relative;

                .vehicle-item-pz {
                    font-size: 12px;
                    line-height: 24px;
                    margin-left: 10px;
                    color: #909090;
                    width: 104px;
                }

                .vehicle-item-pz-select {
                    color: #333333;
                }

                .vehicle-item-yz {
                    position: absolute;
                    right: -0.5px;
                    top: 0px;
                    width: 32px;
                    font-size: 12px;
                    line-height: 24px;
                    background-color: #909090;
                    text-align: center;
                    color: #ffffff;
                    border-radius: 3px;
                }

                .vehicle-item-yz-select {
                    background-color: #333333;
                }
            }
        }

        .reasons-cell {
            display: flex;
            align-items: center;
            padding: 0 10px;
            box-sizing: border-box;

            .label-text {
                color: #333333;
                font-size: $font14;
                line-height: 30px;
            }

            .w-10 {
                width: 100px;
            }
        }

        .order-barcode-icon {
            width: 325px;
            height: 60px;
            margin-left: 10px;
            margin-top: 15px;
            margin-bottom: 7px;
        }
    }

    .order-code {
        width: 345;
        height: 60px;
        background: #222222;
        margin: 15px auto 6px;
    }

    .reasons-footer {
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .footer-btn {
            flex: 1;
            height: 44px;
            background: $btn-color;
            color: #ffffff;
            text-align: center;
            line-height: 44px;
            font-size: 15px;
            border-radius: 5px;
        }
    }

    .title-font {
    }

    .full {
        font-size: 15px;
        color: $btn-color;
    }
}
</style>
