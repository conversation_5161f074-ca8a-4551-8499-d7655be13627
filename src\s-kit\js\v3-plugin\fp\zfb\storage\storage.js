function getdfp() {
	let res = my.getStorageSync({ key: 'dfp' });
	return JSON.stringify(res.data).split("\"").join('');
}

function setdfp(dfp) {
	my.setStorageSync({
		key: 'dfp',
		data: dfp,
	});
}

function gettime() {
	let res = my.getStorageSync({ key: 'time' });
	return JSON.stringify(res.data).split("\"").join('');
}

function settime(time) {
	my.setStorageSync({
		key: 'time',
		data: time,
	});
}

function setCookieCode(cookieCode) {
	my.setStorageSync({
		key: 'cookieCode',
		data: cookieCode,
	});
}

function getCookieCode() {
	let res = my.getStorageSync({ key: 'cookieCode' });
	return JSON.stringify(res.data).split("\"").join('');
}

module.exports = {
	getdfp: getdfp,
	setdfp: setdfp,
	gettime: gettime,
	settime: settime,
	setCookieCode: setCookieCode,
	getCookieCode: getCookieCode,
}
