import { unPaidOrdeQueryrApi, calculateOrdeDiscountsApi } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { balance } from '../../../../../s-kit/js/v3-http/https3/wallet.js';
import checkFKArgs from '../../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import { clientCode } from '../../../../../../project.config';
import post from '../../../../../s-kit/js/v3-http/post';
import { mapGetters, mapState } from 'vuex';

export default {
    data() {
        return {
            // 支付插件参数
            resData: {},
            // 支付参数
            payParams: {},
            // 微信加券二次支付 payMethod == 11的数据
            order: null,
            wxSecondaryPaymentObj: {},
        };
    },

    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    onShow() {
        if (this.isRefresh && !this.isToSelectTicketPage && !this.isToSelectGiftCardPage) {
            uni.hideLoading();
            // 获取待支付订单
            this.getPayChannelInfo();
        }
    },
    async mounted() {
        if (this.isHarmony) {
            let result = await this.$sKit.keyBordPlugin.initRef();
            this.$store.commit('setAccountDataPlugin', result);
        } else {
            let tempData = JSON.parse(JSON.stringify(this.unPaidInfo));
            this.businessDay = tempData.businessDay || '';
            this.gunNo = this.originalOrder ? this.originalOrder.gunNo : tempData.gunNo;
            this.transNo = this.originalOrder ? this.originalOrder.transNo : tempData.tillNo;
            console.log(this.gunNo, this.transNo, this.businessDay);
        }
    },
    methods: {
        //优惠券
        calculateOrdeDiscountsPost(selectTicketsInfo) {
            const params = {
                stationCode: this.unPaidInfo.stationCode,
                orderNo: this.unPaidInfo.orderNo,
                channelType: this.unPaidInfo.channelType,
                orderMoney: this.unPaidInfo.orderMoney,
                productInOrderVoList: this.unPaidInfo.productList,
                businessDay: this.businessDay,
                usedCouponList: selectTicketsInfo,
            };
            calculateOrdeDiscountsApi(params)
                .then(res => {
                    if (res.success) {
                        console.log('OrdeDiscounts---', res.data);
                        this.unPaidInfo = { ...res.data, cancel: this.unPaidInfo?.cancel };
                        this.calculateGiftCard();
                        this.isPaymentMethodAvailable();
                    } else {
                        this.getPayChannelInfo();
                    }
                    this.isCanClickPay = true;
                })
                .catch(err => {
                    this.getPayChannelInfo();
                    this.isCanClickPay = true;
                });
        },
        /**
         * 消费收银台
         * @param stationCode 站编码
         */
        async getBuyPayTypeList() {
            if (!this.isHarmony) {
                let params = {
                    paramsJsonStr: encodeURIComponent(
                        JSON.stringify({
                            stationCode: this.unPaidInfo.stationCode,
                            amount: this.unPaidInfo.payMoney,
                        }),
                    ),
                };
                this.$paymentCenter.getBuyPayTypeList(params, res => {
                    if (res.code === 'PAY_SUCCESS') {
                        this.payList = res.data;
                        console.log(this.payList, 'this.payList=====获取支付方式成功');
                        this.getRechargeMarketingCopy(this.unPaidInfo);
                        if (this.payList && this.payList.length > 0) {
                            this.curPayChannelInfo = this.payList[0];
                            if (this.payList[0].payType == '6' && this.payList[0].memberAssets) {
                                this.isShowOil = true;
                            } else {
                                this.curCardPayInfo = '';
                                this.isShowOil = false;
                            }
                            this.isPaymentMethodAvailable();
                        }
                    } else {
                        this.$store.dispatch('zjShowModal', {
                            title: res.msg,
                            content: res.code,
                            confirmText: '确定',
                            success(res) {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                    }
                });
            } else {
                let params = {
                    stationCode: this.unPaidInfo.stationCode,
                };
                uni.showLoading({
                    title: '加载中',
                    mask: true,
                });
                let res = await this.payPlugin.GetBuyTypeList(params);
                console.log(res.code, 'res.code====鸿蒙支付结果');
                uni.hideLoading();
                if (res.code === 'PAY_SUCCESS') {
                    this.payList = this.isHarmony
                        ? res.data.filter(item => item.payType == 1 || item.payType == 2 || item.payType == 5 || item.payType == 6)
                        : res.data;
                    console.log('支付方式', this.payList);
                    // this.payList = res.data;
                    console.log(this.payList, 'this.payList=====获取支付方式成功');
                    this.getRechargeMarketingCopy(this.unPaidInfo);
                    if (this.payList && this.payList.length > 0) {
                        this.curPayChannelInfo = this.payList[0];
                        if (this.payList[0].payType == '6' && this.payList[0].memberAssets) {
                            this.isShowOil = true;
                        } else {
                            this.curCardPayInfo = '';
                            this.isShowOil = false;
                        }
                        this.isPaymentMethodAvailable();
                    }
                } else if (res.code === 'PAY_ERROR_003') {
                    this.isCanClickPay = true;
                    this.isPaying = false;
                    //需要实人认证
                    const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (riskRes == 13) {
                        // 打开实人认证的表单弹窗
                        this.realNameDialogFlag = true;
                    }
                } else {
                    console.log('鸿蒙支付失败');
                    // 支付失败的情况查询待支付订单
                    this.getPayChannelInfo();
                    // 关闭遮罩
                    this.isOrderShow = false;
                    uni.hideLoading();
                    // 截取字符串后面的数据
                    let errIndex = this.resData.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = this.resData.msg.slice(0, errIndex);
                        customErr = this.resData.msg.slice(errIndex + 1, this.resData.msg.length);
                    } else {
                        customErr = this.resData.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }

                console.log('payList---', res);
            }
        },
        //         String stationCode 是 站编码
        //         String bizOrderNo 是 业务订单编号
        //         String rcvAmt 是 应收总金额
        //         String realAmt 是 支付金额
        //         String payType 是 支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；
        //         String bizDay 否 营业日
        //         String extendFiled 是 风控字段（json字符串透传，不校验里面内容）
        //         Array insidePayList 是 账户预支付场景：内部支付方式列表(组合支付)
        //         String accountNo 否 账户编号，电子账户类型支付时必传
        // 拉起收银台进行支付
        async sdkRealPay(isAuth = false) {
            uni.showLoading({
                title: '支付中',
                mask: true,
            });
            if (!this.isHarmony) {
                // this.isOrderShow = true;
                let paras = {
                    stationCode: this.unPaidInfo.stationCode,
                    bizOrderNo: this.unPaidInfo.orderNo,
                    rcvAmt: this.unPaidInfo.orderMoney + '',
                    // realAmt: this.unPaidInfo.payMoney + '',
                    realAmt: this.orderPayMoney + '',
                    payType: this.curPayChannelInfo.payType + '',
                    bizDay: this.businessDay,
                    extendFiled: JSON.stringify(await checkFKArgs.getFKArgs('sdk', isAuth)),
                    insidePayList: [],
                };
                if (this.selectCard.length > 0 && this.orderPayMoney == 0) {
                    //用户有选择礼品卡，并且待付金额为0，则默认传e享卡的支付方式
                    paras.payType = 5;
                    // this.curPayChannelInfo.payType = 5
                    let item = this.payList.find(item => item.payType == 5);
                    if (item) this.curPayChannelInfo = item;
                }
                if (this.verifiedCoupon) {
                    //有已核销的券，就拼接核销券的数据
                    // paras.insidePayList.push({
                    //   payType: "11",
                    //   payAmt: this.verifiedCoupon.payAmount + "",
                    //   couponNo: this.verifiedCoupon.couponNo,
                    //   couponTemplateNo: this.verifiedCoupon.couponTemplateNo
                    // })
                } else {
                    if (this.unPaidInfo.couponList.length > 0) {
                        for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                            if (this.unPaidInfo.couponList[i].used == 1) {
                                paras.insidePayList.push({
                                    payType: '11',
                                    payAmt: this.unPaidInfo.couponList[i].couponDiscount + '',
                                    couponNo: this.unPaidInfo.couponList[i].couponNo,
                                    couponTemplateNo: this.unPaidInfo.couponList[i].couponTemplateNo,
                                });
                            }
                        }
                    }
                }
                try {
                    const giftInfo = await this.calculateGiftCardPay();
                    paras.insidePayList.push(...giftInfo);
                    //判断是不是加油卡支付
                    if (this.curPayChannelInfo.payType == '6') {
                        paras.cardIdx = this.curCardPayInfo.cardSequence + '';
                        paras.fuelCardNo = this.curCardPayInfo.fuelCardNo;
                    } else if (this.curPayChannelInfo.payType == '5') {
                        paras.cardIdx = '1';
                        paras.accountNo = this.walletInfo.ewalletNo;
                    }
                    if (this.curPayChannelInfo.memberAssets) {
                        let obj = await this.$sKit.commonUtil.biometricPay(this.unPaidInfo.orderNo, this.orderPayMoney, !isAuth);
                        if (obj.bioType) {
                            Object.assign(paras, obj);
                            this.$sKit.mpBP.tracker('后支付加油', {
                                seed: 'hpayoilBiz',
                                pageID: 'fingerPayment', // 返回sdk标识
                                refer: this.refer || '',
                                channelID: clientCode,
                                address: this.cityName,
                            });
                        }
                    }
                    this.isRefresh = true;
                    this.$sKit.mpBP.tracker('后支付加油', {
                        seed: 'hpayoilBiz',
                        pageID: 'inputPsdbut', // 返回sdk标识
                        refer: this.refer || '',
                        channelID: clientCode,
                        address: this.cityName,
                    });
                    this.$paymentCenter.buyPay({ paramsJsonStr: encodeURIComponent(JSON.stringify(paras)) }, async res => {
                        this.isRefresh = false;
                        uni.hideLoading();
                        if (this.$paymentCenter.resStatus(res)) {
                            // success
                            this.beginTime = new Date().getTime();
                            const URL = `/packages/third-oil-charge-payment/pages/query-payment-results/main`;
                            const params = { ...this.unPaidInfo, refer: this.refer, offlineOrder: this.offlineOrder };
                            const type = 'redirectTo';
                            this.$sKit.layer.useRouter(URL, params, type);
                            return;
                        }
                        if (this.$paymentCenter.resAuthStatus(res)) {
                            // need risk
                            this.isCanClickPay = true;
                            this.isPaying = false;
                            //需要实人认证
                            const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                            // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                            if (riskRes == 13) {
                                // 打开实人认证的表单弹窗
                                this.realNameDialogFlag = true;
                            }
                            // else {
                            //   // 如果不是13的时候认证成功后唤起密码键盘支付
                            //   this.sdkRealPay(true);
                            // }
                            return;
                        }
                        // 获取待支付订单
                        this.getPayChannelInfo();
                        // app上键盘唤起后取消键盘
                        if (res.code == 'PAY_ERROR_001') {
                            if (res.msg && res.msg.includes('P_SDK07_007000')) {
                                return;
                            }
                        }
                        // 截取字符串后面的数据
                        this.$store.dispatch('zjShowModal', {
                            title: res.msg,
                            content: res.code,
                            type: 'http',
                            success(res) {
                                if (res.confirm) {
                                    if (res.code == 'PAY_ERROR_001') {
                                        if (res.msg && (res.msg.includes('P_SDK07_005002') || res.msg.includes('P_SDK07_004005'))) {
                                            const pages = getCurrentPages();
                                            if (pages.length <= 1) {
                                                // #ifdef MP-MPAAS
                                                this.$cnpcBridge.closeMriver(res => {});
                                                // #endif
                                                // #ifndef MP-MPAAS
                                                this.$sKit.layer.backHomeFun();
                                                // #endif
                                            } else {
                                                uni.navigateBack();
                                            }
                                        }
                                    }
                                }
                            },
                        });
                    });
                } catch (error) {}
            } else {
                await this.$cnpcBridge.isCutScreen(true);
                this.isOrderShow = true;
                this.payParams = {
                    stationCode: this.unPaidInfo.stationCode,
                    bizOrderNo: this.unPaidInfo.orderNo,
                    rcvAmt: this.unPaidInfo.orderMoney,
                    // realAmt: this.order ? Number(this.order.rcvAmt) : this.unPaidInfo.payMoney,
                    realAmt: this.orderPayMoney + '',
                    payType: this.curPayChannelInfo.payType,
                    bizDay: this.businessDay,
                    // 3.0.4风控字段
                    extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                    insidePayList: [],
                };
                if (this.selectCard.length > 0 && this.orderPayMoney == 0) {
                    this.payParams.payType = 5;
                    // this.curPayChannelInfo.payType = 5
                    let item = this.payList.find(item => item.payType == 5);
                    if (item) this.curPayChannelInfo = item;
                }
                if (this.curPayChannelInfo.payType == '6') {
                    this.payParams.cardIdx = this.curCardPayInfo.cardSequence + '';
                    this.payParams.fuelCardNo = this.curCardPayInfo.fuelCardNo;
                } else if (this.curPayChannelInfo.payType == '5') {
                    this.payParams.cardIdx = '1';
                    this.payParams.accountNo = this.walletInfo.ewalletNo;
                }
                if (JSON.stringify(this.wxSecondaryPaymentObj) !== '{}') {
                } else {
                    if (this.unPaidInfo.couponList.length > 0) {
                        for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                            if (this.unPaidInfo.couponList[i].used == 1) {
                                this.payParams.insidePayList.push({
                                    payType: '11',
                                    payAmt: this.unPaidInfo.couponList[i].couponDiscount + '',
                                    couponNo: this.unPaidInfo.couponList[i].couponNo,
                                    couponTemplateNo: this.unPaidInfo.couponList[i].couponTemplateNo,
                                });
                            }
                        }
                    }
                }
                this.payParams.insidePayList.push(...(await this.calculateGiftCardPay()));
                console.log('油卡或电子账户支付参数', this.payParams);
                console.log('SDK支付方式', this.curPayChannelInfo.payType);

                try {
                    this.$sKit.mpBP.tracker('后支付加油', {
                        seed: 'hpayoilBiz',
                        pageID: 'inputPsdbut', // 返回sdk标识
                        refer: this.refer || '',
                        channelID: clientCode,
                        address: this.cityName,
                    });

                    this.resData = await this.payPlugin.Buy(JSON.parse(JSON.stringify(this.payParams)), this.keyBoardRef);
                    console.log('zfb---res---成功', this.resData);
                    this.$nextTick(async () => {
                        await this.$cnpcBridge.isCutScreen(false);
                    });
                    if (this.resData.code === 'PAY_SUCCESS') {
                        this.beginTime = new Date().getTime();
                        this.$sKit.layer.useRouter(
                            '/packages/third-oil-charge-payment/pages/query-payment-results/main',
                            { ...this.unPaidInfo, offlineOrder: this.offlineOrder },
                            'redirectTo',
                        );
                    } else if (this.resData.code === 'PAY_ERROR_003') {
                        // need risk
                        this.isCanClickPay = true;
                        this.isPaying = false;
                        //需要实人认证
                        const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                        // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                        if (riskRes == 13) {
                            // 打开实人认证的表单弹窗
                            this.realNameDialogFlag = true;
                        }
                    } else {
                        this.getPayChannelInfo();
                        this.isOrderShow = false;
                        uni.hideLoading();
                        // 截取字符串后面的数据
                        let errIndex = this.resData.msg.indexOf(':');
                        let errorCode = '';
                        let customErr = '';
                        if (errIndex !== -1) {
                            errorCode = this.resData.msg.slice(0, errIndex);
                            customErr = this.resData.msg.slice(errIndex + 1, this.resData.msg.length);
                        } else {
                            customErr = this.resData.msg;
                        }
                        this.$store.dispatch('zjShowModal', {
                            title: customErr,
                            content: `${errorCode}`,
                            confirmText: '确定',
                            success(res) {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            },
                        });
                    }
                } catch (error) {
                    this.isCanClickPay = true;
                    this.isPaying = false;
                    console.log('zfb---res---失败', error);
                }
            }
        },
    },
};
