import { POST } from './httpKit.js';
import utils from '../utils/index.js';
// 获取电子卡开卡机构
export const openAccessCard = (params, config) => {
    return POST('/app/json/member/getCardApplyOrg', params, config);
};
export const verificationCode = (params, config) => {
    return POST('/app/json/member/sendCheckCode', params, config);
};
// 申请电子卡
export const verifictoApplyForAnEcard = (params, config) => {
    return POST('/app/json/cnpc_card/applyECard', params, config);
};
// 获取车辆列表
export const getVehicleList = (params, config) => {
    return POST('/app/json/car/getCarList', params, config);
};
// 保存车牌号
export const addLicensePlate = (params, config) => {
    return POST('app/json/car/save', params, config);
};
// 编辑车牌号
export const editLicensePlate = (params, config) => {
    return POST('/app/json/car/update', params, config);
};
// 删除车牌号
export const deleteLicensePlate = (params, config) => {
    return POST('/app/json/car/delete', params, config);
};
// 开通电子卡发送验证码校验
export const checkVerificationCode = (params, config) => {
    return POST('/app/json/cnpc_card/checkECard', params, config);
};

// 电子卡迁移
export const moveAnECard = (params, config) => {
    return POST('/app/json/cnpc_card/moveECard', params, config);
};
// 电子卡迁移验证码
export const moveAnECardCode = (params, config) => {
    return POST('/app/json/member/sendVerifyCode', params, config);
};
// 获取服务协议接口
export const serviceAgreement = (params, config) => {
    return POST('/app/json/news/getNewsList', params, config);
};
// 获取城市所有油站
export const getStationsByCityName = (params, config) => {
    return POST('/app/json/station/getStationsByCityName', params, config);
};
// 判断选择的机构是不是试点机构
export const getCityFlag = (params, config) => {
    return POST('/app/json/third/getCityFlag', params, config);
};
