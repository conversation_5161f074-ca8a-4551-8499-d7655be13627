import { mapGetters, mapState } from 'vuex';
export default {
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        bindGiftCardFun() {
            return new Promise((resolve, reject) => {
                uni.showLoading();
                this.$accountCenter.bindGiftCard({ cardCode: this.cardKey }, res => {
                    uni.hideLoading();
                    if (res.isSuccessed) {
                        resolve();
                    } else {
                        reject(res.desString);
                    }
                });
            });
        },
    },
};
