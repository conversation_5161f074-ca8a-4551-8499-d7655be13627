<template>
    <div>
        <div>
            <div>
                <FrostedGlass @change="loadData"></FrostedGlass>
            </div>
        </div>
        <div class="xf-records">
            <u-navbar
                :background="pageConfig.bgColor"
                :back-icon-size="40"
                :back-icon-color="pageConfig.titleColor.backIconColor"
                :height="44"
                :title-color="pageConfig.titleColor.color"
                back-text="消费记录"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>
            <div class="date-div">
                <picker mode="date" :value="startTime" :start="startSTime" :end="startETime" @change="pickerChange($event, 'start')">
                    <div>{{ startTime }}</div>
                </picker>
                <div class="paddlr"></div>
                <picker mode="date" :value="endTime" :start="endSTime" :end="endETime" @change="pickerChange($event, 'end')">
                    <div class="now-txt">{{ endTime }}</div>
                </picker>
                <!-- <div @click="timeChose(0)">{{startTime.replace('-','.')}}</div>
    <span class="paddlr"></span>
                <div class="now-txt" @click="timeChose(1)">{{currentEndTime}}</div>-->
                <!-- <div>{{startTime.replace('-','.')}}</div> -->
                <!-- <u-picker mode="time" v-model="startTimeShow"
     @cancel="startTimeShow = false"
                :params="params"></u-picker>-->
                <!-- <u-popup v-model="startTimeShow" length="40%" mode="bottom">
      <div class="content">
        <div class="content-flex">
          <div class="item-text" @click="startTimeShow= false">取消</div>
          <div class="item-text" @click="startTimeClick">完成</div>
        </div>
        <div class="item-div">
          <picker-view :indicator-style="indicatorStyle" :value="values" @change="bindChange" class="picker-view-popup">
            <picker-view-column>
              <view class="item" v-for="(item,index) in years" :key="index">{{item}}年</view>
            </picker-view-column>
            <picker-view-column>
              <view class="item" v-for="(item,index) in months" :key="index">{{item}}月</view>
            </picker-view-column>
            <picker-view-column>
              <view class="item" v-for="(item,index) in days" :key="index">{{item}}日</view>
            </picker-view-column>
          </picker-view>
        </div>
      </div>

                </u-popup>-->

                <!-- <u-popup v-model="endTimeShow" length="40%" mode="bottom">
      <div class="content">
        <div class="content-flex">
          <div class="item-text" @click="endTimeShow= false">取消</div>
          <div class="item-text" @click="endTimeClick">完成</div>
        </div>
        <div class="item-div">
          <picker-view :indicator-style="indicatorStyle" :value="values" @change="bindChange" class="picker-view-popup">
            <picker-view-column>
              <view class="item" v-for="(item,index) in years" :key="index">{{item}}年</view>
            </picker-view-column>
            <picker-view-column>
              <view class="item" v-for="(item,index) in months" :key="index">{{item}}月</view>
            </picker-view-column>
            <picker-view-column>
              <view class="item" v-for="(item,index) in days" :key="index">{{item}}日</view>
            </picker-view-column>
          </picker-view>
        </div>
      </div>

                </u-popup>-->
            </div>
            <scroll-view class="content-list" scroll-y @scrolltolower="loadMore">
                <div class="content-box" v-if="dataList.length > 0">
                    <div class="list-item" v-for="(item, index) in dataList" :key="index" @click="enterDetail(item)">
                        <div class="title">
                            <img src="@/static/cnpc-logo.png" alt />
                            {{ item.stationName }}
                            <u-icon name="arrow-right"></u-icon>
                        </div>
                        <div class="detail">商品内容：{{ item.goodsName }}</div>
                        <div class="detail">创建时间：{{ item.tradeTime }}</div>
                        <div class="prize">
                            <span>￥</span>
                            <my-price color="#f96702" intFont="40rpx" floatFont="22rpx" :price="item.realAmount"></my-price>
                            <!-- <div class="jifen" v-else>{{ item.amount }}<span>分</span></div> -->
                        </div>
                        <div v-if="item.invoiceStatus == 0" @click.stop="seeInvoice(item)"></div>
                        <div class="info-btn" v-else-if="item.invoiceStatus == 1" @click.stop="seeInvoice(item)">开发票</div>
                        <div class="info-btn" v-else-if="item.invoiceStatus == 2" @click.stop="seeInvoice(item)">查看发票</div>
                        <div class="info-btn" v-else-if="item.invoiceStatus == 3" @click.stop="seeInvoice(item)">开票中</div>
                        <div class="info-btn" v-else @click.stop="seeInvoice(item)">红冲中</div>
                    </div>
                </div>
                <div v-if="dataList.length == 0 || !hasMore" class="no-more">
                    <u-loadmore status="nomore" />
                </div>
            </scroll-view>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import myPrice from '@/components/price/price.vue';
import FrostedGlass from '@/components/frostedGlass/frostedGlass.vue';
import { mapGetters, mapState } from 'vuex';
import { getFuelListApi } from '@/api/home.js';
import message from '../../../../components/uni-popup/message';
export default {
    components: {
        myPrice,
        FrostedGlass,
    },
    data() {
        const date = new Date();
        const years = [];
        const year = date.getFullYear();
        const months = [];
        const month = date.getMonth() + 1;
        const days = [];
        const day = date.getDate();
        for (let i = 2020; i <= date.getFullYear(); i++) {
            years.push(i);
        }
        for (let i = 1; i <= date.getMonth() + 1; i++) {
            months.push(i);
        }
        for (let i = 1; i <= date.getDate(); i++) {
            days.push(i);
        }
        return {
            pageConfig: pageConfig, // 页面配置
            startTime: '',
            startSTime: '2020-01-01',
            startETime: '',
            endTime: '',
            endSTime: '2020-01-07',
            endETime: '',

            dataList: [],
            page: 1,
            pageSize: 10,
            hasMore: true,
            // startTimeShow: false,
            // endTimeShow: false,
            years,
            year,
            months,
            month,
            days,
            day,
            values: [9999, month - 1, day - 1],
            indicatorStyle: `height: ${Math.round(uni.getSystemInfoSync().screenWidth / (750 / 100))}px;`,
            currentYear: 2020,
            currentMonth: 1,
            currentDay: 1,
            currentEndTime: '目前',
            refreshInvoice: false,
        };
    },
    onLoad(options) {
        this.getTimes('init');
        // this.loadData()
        // if (this.startTime && this.endTime && this.islogin) {
        //   this.loadData()
        // }
        if (this.startTime && this.endTime && this.isLogin) {
            this.loadData();
        }
    },
    onShow() {
        if (this.refreshInvoice) {
            this.loadData();
        }
    },
    methods: {
        pickerChange(e, type) {
            this.dataList = [];
            this.page = 1;
            this.hasMore = true;
            let value = e.detail.value;
            if (type == 'start') {
                this.startTime = value;
                this.getTimes('startChange');
                this.loadData();
            } else if (type == 'end') {
                this.endTime = value;
                this.getTimes('endChange');
                this.loadData();
            }
        },
        // 获取六个对应时间的方法 type的值有 init startChange endChange
        getTimes(type) {
            console.log('33333');
            let weekTimeStamp = 7 * 86400 * 1000;
            const curDate = new Date();
            const curTimeStamp = new Date().getTime();
            if (type == 'init') {
                const curDateType = this.objectToDateType(curDate);
                this.startTime = this.objectToDateType(new Date(curTimeStamp - weekTimeStamp));
                this.startETime = curDateType;
                this.endETime = curDateType;
                this.endTime = '当前';
                this.endSTime = this.startTime;
            } else if (type == 'startChange') {
                this.endSTime = this.startTime;
            } else if (type == 'endChange') {
                this.startETime = this.endTime;
            }
        },
        // 将时间格式转化成时间戳
        dateTypeToTimeStamp(dateType) {
            let dateArr = dateType.split('-');
            return new Date(dateArr[0], dateArr[1] - 1, dateArr[2]).getTime();
        },
        // 将时间对象转化成时间格式
        objectToDateType(date) {
            let year = date.getFullYear();
            let mon = date.getMonth() + 1;
            let day = date.getDate();
            return `${year}-${this.addBefLing(mon)}-${this.addBefLing(day)}`;
        },
        addBefLing(num) {
            num = Number(num);
            return num < 10 ? '0' + num : num + '';
        },
        enterDetail(item) {
            uni.navigateTo({
                url: '/packages/order/pages/order-detail/main?orderId=' + item.tradeNo,
            });
        },

        getweekTime(endDate, number) {
            console.log('endDate', endDate);
            const num = number;
            const date = endDate ? new Date(endDate) : new Date();
            console.log(date);
            let year = date.getFullYear();
            let mon = date.getMonth() + 1;
            let day = date.getDate();
            if (day <= num) {
                if (mon > 1) {
                    mon = mon - 1;
                } else {
                    year = year - 1;
                    mon = 12;
                }
            }
            date.setDate(date.getDate() - num);
            year = date.getFullYear();
            mon = date.getMonth() + 1;
            day = date.getDate();
            // const s = year + '-' + (mon < 10 ? ('0' + mon) : mon) + '-' + (day < 10 ? ('0' + day) : day);
            const s = year + '-' + mon + '-' + day;
            return s;
        },

        bindChange(e) {
            const val = e.detail.value;
            this.year = this.years[val[0]];
            this.month = this.months[val[1]];
            this.day = this.days[val[2]];

            //将选择的年月日变为number形式，便于比较之用
            var y = parseInt(this.year);
            var m = parseInt(this.month);
            var d = parseInt(this.day);

            //选择不同月份显示的天数不同
            if (m == 1 || m == 3 || m == 5 || m == 7 || m == 8 || m == 10 || m == 12) {
                if (this.days.length != 31) {
                    this.days = [];
                    for (let i = 1; i <= 31; i++) {
                        this.days.push(i);
                    }
                }
            } else if (m == 4 || m == 6 || m == 9 || m == 11) {
                if (this.days.length != 30) {
                    this.days = [];
                    for (let i = 1; i <= 30; i++) {
                        this.days.push(i);
                    }
                }
            } else if (m == 2) {
                if ((y % 4 == 0 && y % 100 != 0) || y % 400 == 0) {
                    //闰年
                    if (this.days.length != 29) {
                        this.days = [];
                        for (let i = 1; i <= 29; i++) {
                            this.days.push(i);
                        }
                    }
                } else {
                    //平年
                    if (this.days.length != 28) {
                        this.days = [];
                        for (let i = 1; i <= 28; i++) {
                            this.days.push(i);
                        }
                    }
                }
            }
            //处理选择今年的情况
            if (y == this.currentYear) {
                //最多显示到当前月份
                if (this.months.length != this.currentMonth) {
                    this.months = [];
                    for (let i = 1; i <= this.currentMonth; i++) {
                        this.months.push(i);
                    }
                }
                //如果选择的是当前月份，那么日最多显示到今天
                if (m == this.currentMonth) {
                    if (this.days.length != this.currentDay) {
                        this.days = [];
                        for (let i = 1; i <= this.currentDay; i++) {
                            this.days.push(i);
                        }
                    }
                }
            } else {
                this.months = [];
                for (let i = 1; i <= 12; i++) {
                    this.months.push(i);
                }
            }
            let time = this.year.toString() + '-' + this.month.toString() + '-' + this.day.toString();
            if (this.timeIndex == 0) {
                this.startTime = time;
            } else {
                this.endTime = time;
                this.currentEndTime = time.replace('-', '.');
            }
        },
        timeChose(index) {
            let that = this;
            this.timeIndex = index;
            if (index === 1) {
                this.endTimeShow = true;
                let chooTime = this.getweekTime(this.startTime, -7);
                console.log('chooTime', chooTime);
                let yearsTime = chooTime.split('-')[0];
                console.log('yearsTime', yearsTime);
                that.years = [];
                for (let i = yearsTime; i <= this.currentYear; i++) {
                    that.years.push(i);
                }
                let monthTime = chooTime.split('-')[1];
                console.log('monthTime', monthTime);
                this.months = [];
                for (let i = 1; i <= monthTime; i++) {
                    that.months.push(i);
                }
                let dayTime = chooTime.split('-')[2];
                console.log('dayTime', dayTime);
                this.days = [];
                for (let i = 1; i <= dayTime; i++) {
                    that.days.push(i);
                }
            } else {
                this.startTimeShow = true;
            }
        },

        startTimeClick() {
            this.startTimeShow = false;
            this.loadData();
        },
        endTimeClick() {
            this.endTimeShow = false;
            this.loadData();
        },
        seeInvoice(item) {
            if (item.invoiceStatus == 2) {
                uni.navigateTo({
                    url: `/packages/invoice-center/pages/invoice-detail/main?id=${item.invoiceId}&type=0`,
                });
            } else {
                uni.navigateTo({
                    url: '/packages/order/pages/order-detail/main?orderId=' + item.tradeNo,
                });
            }
        },
        async loadData(loadMore) {
            const curDate = new Date();
            const curDataType = this.objectToDateType(curDate);
            this.page = loadMore ? this.page : 1;
            if (!loadMore) this.dataList = [];
            let params = {
                pageNo: this.page,
                pageSize: this.pageSize,
                startTime: this.startTime,
                endTime: this.endTime == '当前' ? curDataType : this.endTime,
            };
            let res = await getFuelListApi(params);
            if (res.status == 0 && res.data.length > 0) {
                this.hasMore = true;
                this.dataList = this.dataList.concat(res.data);
            }
            // this.dataList = loadMore ? this.dataList.concat(res.data) : res.data
            if (res.data.length == 0 || res.data.length < this.pageSize) {
                this.hasMore = false;
            }
            if (res.status == -1) {
                uni.showModal({
                    title: '提示',
                    content: res.info,
                    confirmColor: '#FF8200',
                    showCancel: false,
                    success: () => {},
                });
            }
        },
        loadMore() {
            if (!this.hasMore) {
                return;
            }
            this.page++;
            this.loadData(true);
        },
    },
    computed: {
        ...mapGetters(['isLogin']),
    },
};
</script>

<style lang="scss" scoped>
.solid {
    border: 1px solid red;
}

.xf-records {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);

    .date-div {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        color: #333;
        font-size: 14px;

        .paddlr {
            width: 10px;
            height: 1px;
            background-color: #333;
            margin-left: 5px;
            margin-right: 5px;
            &.paddlr:before {
                content: '';
                position: absolute;
                width: 15px;
                border-top: 1px solid #333333;
                top: 0;
                left: 8px;
                right: 0;
            }
        }

        .now-txt {
            color: #048de9;
        }
    }

    .content-list {
        min-height: 0;
        flex: 1;

        .content-box {
            padding: 24rpx;

            .list-item {
                padding: 24rpx;
                border-radius: 16rpx;
                background: #fff;
                margin-bottom: 24rpx;
                position: relative;

                .title {
                    font-size: 26rpx;

                    img {
                        display: inline-block;
                        height: 36rpx;
                        width: 36rpx;
                        margin-right: 16rpx;
                    }
                }

                .detail {
                    font-size: 22rpx;
                    color: #999999;
                    margin-top: 15rpx;
                }

                .prize {
                    margin-top: 25rpx;
                    display: flex;
                    align-items: baseline;
                    font-size: 40rpx;
                    color: #f96702;

                    span {
                        font-size: 22rpx;
                    }
                }
            }
        }

        .info-btn {
            padding: 6px 12px;
            box-sizing: border-box;
            flex: 1;
            font-size: 12px;
            text-align: center;
            color: #909090;
            border-radius: 6px;
            border: 1px solid #ededed;
            margin-left: 4px;
            // width: 85px;
            position: absolute;
            right: 12px;
            bottom: 12px;
        }

        .no-more {
            padding: 24rpx 0;
            text-align: center;
            ::v-deep .u-load-more-wrap {
                background-color: transparent !important;
                view {
                    background-color: transparent !important;
                }
            }
        }
    }

    .content {
        width: 100%;
        height: 100%;

        .content-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            height: 64rpx;
            line-height: 64rpx;
            padding: 0 10px;

            .item-text {
                color: #048de9;
                font-size: 28rpx;
                line-height: 1;
            }

            &.content-flex:before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 1px;
                border-bottom: 1px solid #ededed;
            }
        }
    }

    .item-div {
        width: 100%;
        height: 50%;
        margin-top: 60px;
        transform: translateX(-50%);
        margin-left: 50%;

        .picker-view-popup {
            width: 100%;
            height: 100%;

            .item {
                text-align: center;
                width: 100%;
                height: 88rpx;
                line-height: 88rpx;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 40rpx;
            }
        }
    }
}
</style>
