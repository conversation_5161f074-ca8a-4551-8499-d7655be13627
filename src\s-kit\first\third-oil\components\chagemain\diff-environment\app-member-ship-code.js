import { accountRiskEngineSupport } from '@/s-kit/js/v3-http/https3/wallet';
import wxcode from 'uniapp-qrcode';
import { mapState } from 'vuex';
import projectConfig from '../../../../../../../project.config';
export default {
    data() {
        return {
            clientName: '',
            HMAccountDataPlugin: {},
        };
    },
    created() {},
    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
            mpaasAccountDataPlugin: state => state.thirdIndex.mpaasAccountDataPlugin,
        }),
    },
    methods: {
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        enablePositioning() {
            this.$cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(res => {
                    if (res) {
                        console.log(res, '开启定位权限res');
                        this.locationPermission = true;
                        this.init(true);
                    }
                });
        },
        /**
         * @description  : 获取会员码code值，生成会员码
         * @param         {Function} getPayCode -获取会员码方法
         * @param         {Function} codeChange -将获取的生成的会员码的code值分发给父组件
         * @param         {Boolean} isOrder -是否获取订单
         * @return        {*}
         */
        async pollGetQrCode(isOrder) {
            if (this.isHarmony) {
                console.log(this.mpaasAccountDataPlugin, '会员码1');
                this.mpaasAccountDataPlugin
                    .genCode()
                    .then(res => {
                        console.log(res, '会员码2');
                        this.memberCode = res.msg;
                        this.$emit('codeChange', this.memberCode);
                        setTimeout(() => {
                            this.generateQRCode(this.memberCode);
                        }, 0);

                        // 会员码风控
                        this.riskControl();

                        // 查询订单
                        if (isOrder) {
                            console.log('测试会员码=====');
                            this.getRposUnPayList();
                        }
                    })
                    .catch(err => {
                        console.log(err, '会员码3');
                        uni.showToast({ title: err.msg || '会员码获取失败' });
                    });
            } else {
                this.$accountCenter.getPayCode({ cardIdx: 0 }, res => {
                    if (res.isSuccessed) {
                        this.memberCode = res.desString;
                        this.$emit('codeChange', this.memberCode);
                        setTimeout(() => {
                            // wxcode.qrcode('qrcode', res.desString, 525, 525);
                            // wxcode.qrcode('qrcode', res.desString, 555, 555);
                            this.generateQRCode(res.desString);
                        }, 0);
                        // this.clearTimerMember()
                        // this.clearTimerQuqeyOrder()
                        // 会员码风控
                        // this.riskControl();

                        // 查询订单
                        if (isOrder) {
                            console.log('测试会员码=====');
                            this.getRposUnPayList();
                        }
                    } else {
                        uni.showToast({ title: res.desString || '会员码获取失败' });
                    }
                });
            }
        },
        /**
         * @description  : 轮询订单
         * @param         {Function} getRposUnPayList -查询pos待支付订单
         * @param         {Function} resStatus -查询订单成功或失败
         * @return        {*}
         */
        async getRposUnPayList() {
            // 清除查询订单的定时器
            this.clearTimerQuqeyOrder();
            // 防止查询订单成功后继续执行递归
            if (this.turnOffRecursionMember) {
                let res = await this.getRposUnPayFun();
                console.log('轮询会员码订单3', res, this.turnOffRecursionMember);
                if (this.$paymentCenter.resStatus(res) && this.turnOffRecursionMember) {
                    if (!res.data) {
                        this.memberCodeTimer = setTimeout(() => {
                            this.getRposUnPayList();
                        }, this.pollTime);
                        return;
                    }
                    if (this.isJump) {
                        this.isJump = false;
                        // this.clearTimerQuqeyOrder();
                        let url = '/packages/third-scan-code-payment/pages/member-order-price/main';
                        let params = { ...res.data, refer: this.refer };
                        let type = 'navigateTo'; // 默认  uni.navigateTo({})
                        this.$sKit.layer.useRouter(url, params, type);
                        this.clearTimerQuqeyOrder();
                        this.clearTimerMember();
                        this.turnOffRecursionMember = false;
                    }
                } else {
                    // 未查询到订单继续查询
                    this.memberCodeTimer = setTimeout(() => {
                        this.getRposUnPayList();
                    }, this.pollTime);
                }
            }
        },
        getRposUnPayFun() {
            return new Promise(async (resolve, reject) => {
                if (this.isHarmony) {
                    console.log('轮询会员码订单0');

                    let userTokenInfo = await this.$cnpcBridge.getUserTokenInfo();
                    let gsmsToken = userTokenInfo.gsmsToken;
                    let params = {
                        token: gsmsToken,
                        data: {},
                        baseType: projectConfig.baseType,
                    };
                    console.log('轮询会员码订单1', params, this.$sKit.mpaasPayPlugin);
                    this.$sKit.mpaasPayPlugin
                        .GetRposUnPayList(params)
                        .then(res => {
                            console.log('轮询会员码订单2then', res);
                            resolve(res);
                        })
                        .catch(err => {
                            console.log('轮询会员码订单2catch', err);
                            resolve(err);
                        });
                } else {
                    this.$paymentCenter.getRposUnPayList(res => {
                        resolve(res);
                    });
                }
            });
        },
    },
};
