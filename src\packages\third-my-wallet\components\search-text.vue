<template>
    <div class="search-area">
        <div class="input-area" :style="inputStyle">
            <img class="search-icon" src="../images/icon-serch.png" alt />
            <input type="text" :enableNative="false" placeholder="输入油站名称" v-model="searchText" class="input" />
            <div class="font-14 color-999 bor-seach" @click="searchClick">搜索</div>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
    data() {
        return {
            searchText: '',
            addressList: [],
            isListenSearchText: true,
            inputFlag: false, //输入框控制
        };
    },
    created() {},
    mounted() {},
    methods: {
        /**
         * @description  : 根据关键词搜索
         * @param         {*} item:关键词
         * @return        {*}
         */
        searchClick() {
            this.$emit('searchClick', this.searchText);
        },
    },
    computed: {
        // #ifdef MP-WEIXIN
        inputStyle() {
            return `box-shadow:${this.inputFlag ? '0px 2px 3px 0px rgba(192,187,187,0.5)' : ''})`;
        },
        // #endif
        ...mapGetters(['latV3', 'lonV3']),
    },
    components: {},
    watch: {
        searchText(val, oldVal) {
            if (!val) {
                this.$emit('searchClick', val);
            }
        },
    },
};
</script>
<style scoped lang="scss">
.search-area {
    display: flex;
    align-items: center;
    padding: 16px 0 6px 0;

    .input-area {
        flex: 1;
        height: 40px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e7e7e7;
        display: flex;
        align-items: center;
        padding: 0px 14px;
        position: relative;

        .search-icon {
            width: 17px;
            height: 17px;
        }

        .input {
            margin-left: 9px;
            font-size: 14px;
            caret-color: #e64a1d;
            flex: 1;
            height: 40px;
            background: none;
        }

        .bor-seach {
            // width: 40px;
            height: 16px;
            border-left: 1px solid #999;
            padding-left: 9px;
            line-height: 16px;
        }

        .input-pop {
            position: absolute;
            top: 40px;
            left: 0;
            width: 100%;
            height: 270px;
            background: #f7f7fb;
            box-shadow: 0px 2px 3px 0px rgba(192, 187, 187, 0.5);
            border-radius: 0 0 10px 10px;
            z-index: 1000;
            overflow: auto;

            .address-search-item {
                padding: 10px 40px;
                box-sizing: border-box;

                .search-item-location {
                    box-sizing: border-box;
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                }
            }
        }
    }

    .ali-search {
        width: 100%;
        height: 100%;
        .input {
            padding: 0;
            width: 100%;
        }
    }
}
</style>
