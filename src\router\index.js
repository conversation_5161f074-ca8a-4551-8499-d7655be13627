
import Vue from 'vue';
import Router from 'uni-simple-router';
import wxLogin from '../utils/login';
import Store from '../store';
import { getOnlineProvince, setedPayPass, material, getMsgTemplates, openCloseConfig } from '../api/home.js';
import { autoUpdate } from '../utils/edition-upload.js';
import globalData from '../utils/global-data.js';
import CONFIG from '@/utils/config.js';
import utils from '../utils';

Vue.use(Router);
const files = require.context('./modules/', true, /\.js$/);
const modules = [];
let ISREADY = false;
files.keys().forEach(key => {
    const item = files(key).default;
    modules.push(...item);
});
//初始化
const router = new Router({
    encodeURI: false,
    routes: [...modules], //路由表
});

const navigateToMiniProgram = () => {
    return new Promise((resolve, reject) => {
        wx.navigateToMiniProgram({
            appId: 'wx7cd1712834749dcb',
            path: 'pages/thirdHome/home',
            complete: async () => {
                resolve();
            },
        });
    });
};

//全局路由前置守卫
router.beforeEach(async (to, from, next) => {
    // #ifdef MP-WEIXIN
    // Store.commit('setMaskDialogFlag', true)
    console.log('全局路由前置守卫', to);
    let isLogin = !to.query.isLogin ? false : true;
    if (!ISREADY || !isLogin) {
        autoUpdate(); //强制更新新版本
        let isSkip = 0; // 是否跳转能源e站
        if (CONFIG.appId == 'wx7b0e61953d0c38a2') {
            let res = await openCloseConfig({ configName: 'minipro.window.open' });
            isSkip = res.data;
        }
        if (CONFIG.appId == 'wx7b0e61953d0c38a2' && isSkip) {
            // 如果是昆仑加油
            while (1) {
                await utils.showModal(
                    '感谢您使用昆仑加油小程序！昆仑加油现已将全部业务、功能转移至能源e站小程序，点击下方【确定】跳转使用。',
                    true,
                );
                await navigateToMiniProgram();
            }
        } else {
            globalData.systemInfo = uni.getSystemInfoSync();
            let pages = getCurrentPages(); // 获取栈实例
            let page = pages[pages.length - 1]; // 获取当前页面的数据，包含页面路由
            let prevPage = pages[pages.length - 2]; // 获取上个页面的数据，包含页面路由
            console.log('当前页面的路由：', pages[pages.length - 1].$page.fullPath);
            let tokenInfo = uni.getStorageSync('tokenInfo');
            if (pages[pages.length - 1].$page.fullPath === '/pages/home/<USER>' && !tokenInfo.token) {
                console.log('wangyulei');
                uni.reLaunch({
                    url: '/packages/transferAccount/pages/home/<USER>',
                });
            } else {
                console.log(to.query.goods, 'to.query.goods');
                // if (to.query.goods) {
                //     await material({ goods: to.query.goods });
                // }
            }

            // await wxLogin.init().catch(err => {});
            // if (Store.getters.isLogin) {
            //     // let isPassword = await setedPayPass()
            //     // Store.commit('setIsSetPassword', isPassword.data)
            //     await Store.dispatch('card/getAllCardList');
            // }
            // let provinceData = await getOnlineProvince()
            // Store.commit('setLocationArr', provinceData.data)
            // to.query.goods = '1:1'  测试使用

            // let msgTList = await getMsgTemplates()
            // globalData.msgTemplates = msgTList.data
        }

        ISREADY = true;
    }
    // #endif
    next();
});
// 全局路由后置守卫
router.afterEach((to, from) => {});
export default router;
