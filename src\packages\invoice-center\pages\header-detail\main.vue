<template>
    <div class="invoice-header">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="抬头详情"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <div class="content-box">
            <div class="qrcode-box">
                <canvas @click="previewImg" class="qrcode" canvas-id="qrcode"></canvas>
                <div>商家扫码获取抬头信息</div>
            </div>
            <div class="my-line"></div>
            <div class="other-msg">
                <div class="section-item">
                    <div class="left">{{ detail.islogo == 1 ? '公司名称' : '名称' }}</div>
                    <div class="right">{{ detail.invoicetitle }}</div>
                </div>
                <div class="section-item">
                    <div class="left">{{ detail.islogo == 1 ? '公司税号' : '税号' }}</div>
                    <div class="right">{{ detail.taxcode }}</div>
                </div>
                <div class="section-item">
                    <div class="left">地址</div>
                    <div class="right">{{ detail.addresstax }}</div>
                </div>
                <div class="section-item">
                    <div class="left">电话</div>
                    <div class="right">{{ detail.telephone }}</div>
                </div>
                <div class="section-item">
                    <div class="left">开户银行</div>
                    <div class="right">{{ detail.openingbank }}</div>
                </div>
                <div class="section-item">
                    <div class="left">银行帐号</div>
                    <div class="right">{{ detail.bankaccount }}</div>
                </div>
            </div>
        </div>
        <div class="action-box">
            <div class="edit" @click="editItem">编辑</div>
            <div class="delete" @click="deleteItem">删除抬头</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import wxbarcode from 'wxbarcode';
import { getInvoiceQrCodeApi, delInvoiceTitlePost, getTitleDetails } from '@/api/my-center';

export default {
    // 发票抬头详情页面
    data() {
        return {
            pageConfig,
            detail: {},
        };
    },

    onLoad(options) {
        console.log('options', options);
        if (options.id) {
            this.loadDetail(options.id);
        }
    },
    methods: {
        async loadDetail(id) {
            let { data } = await getTitleDetails({ id });
            this.detail = data;
            this.loadQrCode(data.id);
        },
        async loadQrCode(id) {
            let { data } = await getInvoiceQrCodeApi({ id });
            wxbarcode.qrcode('qrcode', data, 400, 400);
        },
        // 编辑
        editItem() {
            uni.redirectTo({
                url:
                    '/packages/invoice-center/pages/add-invoice-title/main?type=edit&detail=' +
                    encodeURIComponent(JSON.stringify(this.detail)),
            });
        },
        // 删除
        deleteItem() {
            let that = this;
            uni.showModal({
                title: '提示',
                content: '你确定删除此发票抬头吗？',
                cancelText: '我再想想',
                cancelColor: '#1677FF',
                confirmText: '确定删除',
                confirmColor: '#1677FF',
                success(res) {
                    if (res.confirm) {
                        that.deleteHeaderDetail();
                    }
                },
            });
        },
        async deleteHeaderDetail() {
            let res = await delInvoiceTitlePost({ id: this.detail.id });
            if (res.status == 0) {
                this.$util.tipsToastNoicon('删除成功');
                let timer = setTimeout(() => {
                    clearTimeout(timer);
                    uni.navigateBack({
                        delta: 1,
                    });
                }, 500);
            }
        },
        async previewImg() {
            let imgUrl = await this.getImgUrl();
            uni.previewImage({
                urls: [imgUrl],
                success(res) {
                    console.log(res);
                },
                fail(err) {
                    console.log('err', err);
                },
            });
        },
        getImgUrl() {
            let that = this;
            return new Promise(resolve => {
                uni.canvasToTempFilePath(
                    {
                        canvasId: 'qrcode',
                        fileType: 'png',
                        success(res) {
                            resolve(res.tempFilePath);
                        },
                    },
                    that,
                );
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.invoice-header {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 24rpx 24rpx env(safe-area-inset-bottom);
    .content-box {
        border-radius: 16rpx;
        background: #fff;
        padding: 24rpx 0;
        .qrcode-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            .qrcode {
                width: 400rpx;
                height: 400rpx;
            }
            div {
                margin: 30rpx 0;
            }
        }
        .my-line {
            opacity: 0.4;
            border-bottom: 2px dotted #979797;
            position: relative;
            margin: 0 24rpx 32rpx;
            &::before,
            &::after {
                content: '';
                display: block;
                position: absolute;
                width: 30rpx;
                height: 30rpx;
                border-radius: 50%;
                background: #f5f5f5;
                top: -13rpx;
                z-index: 9;
            }
            &::before {
                left: -36rpx;
            }
            &::after {
                right: -36rpx;
            }
        }
        .section-item {
            display: flex;
            font-size: 28rpx;
            margin-bottom: 32rpx;
            padding: 0 24rpx;
            justify-content: space-between;
            align-items: center;
            &:last-child {
                margin-bottom: 0;
            }
            .left {
                color: #999999;
                min-width: 70px;
            }
            .right {
                color: #333333;
                flex: 1;
                text-align: right;
            }
        }
    }
    .action-box {
        margin-top: 60rpx;
        div {
            border-radius: 4rpx;
            padding: 24rpx 0;
            text-align: center;
            &.edit {
                background: #f96702;
                color: #fff;
                margin-bottom: 24rpx;
            }
            &.delete {
                background: #ffffff;
                border: 2rpx solid #e5e5e5;
                color: #333;
            }
        }
    }
}
</style>
