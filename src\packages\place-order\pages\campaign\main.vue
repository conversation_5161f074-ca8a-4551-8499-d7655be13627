<template>
    <div class="campaign">
        <div class="advertisingPopups" v-if="visible && campaignObj.activityImg !== ''">
            <div class="full-div">
                <img class="ad-img-class" :src="campaignObj.activityImg" mode="widthFix" @click="enterNav()" />
                <!-- <img class="ad-img-class" src="../campaign/img/测试.png" mode="widthFix" @click="enterNav()" /> -->
                <div class="close-tbn-3" @click="close">
                    <img src="../../../../static/X.png" />
                </div>
            </div>
        </div>

        <movable-area class="movableArea" v-else>
            <movable-view class="movableView" direction="all" x="600rpx" y="800rpx">
                <div class="advertisingPopupsSmall">
                    <img @click="showBigPic" :src="campaignObj.miniActivityImg" alt />
                </div>
            </movable-view>
        </movable-area>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
export default {
    name: 'campaign',
    components: {},
    props: {
        campaign: {
            type: Object,
            default: '',
        },
    },
    data() {
        return {
            visible: false, // 大图flag
            campaignObj: {},
        };
    },
    onLoad() {},
    computed: {},
    watch: {
        campaign: {
            handler(val, oldVal) {
                if (JSON.stringify(val) !== '{}') {
                    this.campaignObj = val;
                    this.$forceUpdate();
                    this.visible = true;
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        close() {
            this.visible = false;
        },
        async enterNav() {
            if (this.campaignObj.activityLinks === '') {
                return;
            }
            uni.navigateTo({
                url: `/packages/web-view/pages/home/<USER>
            });
            this.visible = false;
            this.$parent.campaignIDFlag = false;
        },
        showBigPic() {
            this.visible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.campaign {
    width: 100%;
    height: 100%;
    .advertisingPopups {
        position: fixed;
        bottom: 0;
        top: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 9;
        background-color: rgba(0, 0, 0, 0.6);
        .full-div {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-flow: column;
            .ad-img-class {
                object-fit: contain;
                // width: auto;
                // height: auto;
                max-width: 70%;
            }
            .close-tbn-3 {
                position: relative;
                z-index: 10;
                margin-top: 30px;
                height: 0px;
                img {
                    display: block;
                    width: 25px;
                    height: 25px;
                    border: 1px solid white;
                    border-radius: 50%;
                }
            }
        }
    }
    .movableArea {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
        .movableView {
            pointer-events: auto; //可以点击
            width: 120rpx;
            height: 110rpx;
            .advertisingPopupsSmall {
                width: 50px;
                height: 50px;
                border-radius: 4px;
                .img {
                    width: 100%;
                    height: 100%;
                    border-radius: 4px;
                }
            }
            image {
                width: 100%;
                height: 100%;
            }

            text {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 120rpx;
                height: 18rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16rpx;
                color: #ffffff;
            }
        }
    }
}
</style>
