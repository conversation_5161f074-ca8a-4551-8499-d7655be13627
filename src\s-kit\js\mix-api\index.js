import { POST } from '../v3-http';
import account from '../v3-native-jsapi/accountCenter.js';
import payment from '../v3-native-jsapi/paymentCenter.js';
import layer from '../layer';
const NATIVE = {
    ...account,
    ...payment,
};
const TYPEENUM = {
    SKIP: 'SKIP',
    REQUESR: 'REQUESR',
    PLUGIN: 'PLUGIN',
};
class FType {
    /**
     * @param {string} str 非原生的一些信息（小程序环境）
     * @param {string} native 原生方法名 (app环境)
     */
    constructor(str, native) {
        const mes = FType.fTypeStrPrase(str);
        this.mes = mes; // 非原生信息
        this.native = native; // 原生sdk方法名
    }

    static fTypeStrPrase(str) {
        const arr = str.split('**');
        return {
            type: arr[1],
            str: arr[0],
        };
    }
    /**
     *
     * @param {string} str 对应类型传不同的值
     * @param {string} typeEnum 对应类型的枚举值
     */
    static fTypeStrGenerate(str, typeEnum) {
        return `${str}**${typeEnum}`;
    }
}

// 出参处理的地方
class ResData {
    constructor(obj) {
        this.status = obj.status ? obj.status : obj.isSuccessed ? 0 : 1;
        this.info = obj.info ? obj.info : obj.desString;
        this.data = obj.data ? obj.data : obj.isSuccessed; // 返回数据
        this.success = obj.success ? obj.success : obj.isSuccessed;

        this.currentTime = obj.currentTime ? obj.currentTime : '';
    }
}
// 相当于配置路由
const funName = {
    zjzc: new FType(FType.fTypeStrGenerate('card.rollout', TYPEENUM.REQUESR), 'capitalOutflow'), // 资金转出
    xgmm: new FType(FType.fTypeStrGenerate('/packages/third-my-wallet/pages/change-payment-password/main', TYPEENUM.SKIP), 'changePW'), // 修改密码
    klexk: new FType(FType.fTypeStrGenerate('/开通昆仑e享卡的接口名', TYPEENUM.SKIP), 'openEwallet'), // 昆仑e享卡
};

const mixF = (type, params) => {
    return new Promise(async (res, rej) => {
        // 江艺大哥救命！！！！！！！！！！！！！
        NATIVE[funName[type].native](params, e => {
            res(new ResData(e));
        });
        return;
        if (process.env.VUE_APP_PLATFORM == 'mpaas') {
            NATIVE[funName[type].native](params, e => {
                res(new ResData(e));
            });
        } else {
            return;
            const mes = funName[type].mes;
            if (mes.type == TYPEENUM.REQUESR) {
                const e = await POST(mes.str, params);
                res(new ResData(e));
            } else if (mes.type == TYPEENUM.SKIP) {
                layer.useRouter(mes.str, {}, 'navigateTo');
                res(new ResData({ isSuccessed: true, desString: '' }));
            } else {
                rej(`FType没有${mes.type}类型`);
            }
        }
    });
};

// 入参处理的地方
export default {
    /**
     * 资金转出方法
     * @param {string} cardNo 卡号
     * @param {string} amount 金额
     * @param {string} password 卡号密码
     * @returns Promise
     */
    mixCapitalOutflow: params => {
        return new Promise(async (res, rej) => {
            const e = await mixF('zjzc', params);
            res(e);
        });
    },
    mixChangePW: params => {
        return new Promise(async (res, rej) => {
            const e = await mixF('xgmm', params);
            res(e);
        });
    },
    /**
     * 资金转出方法
     * @param {string} idNo 身份证号
     * @param {string} usedPlace 地区编码
     * @returns Promise
     */
    mixOpenEwallet: params => {
        return new Promise(async (res, rej) => {
            const e = await mixF('klexk', params);
            res(e);
        });
    },
};
