<template>
    <div class="newETCSigning">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="新增ETC签约"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="section-box-wrap">
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>预授权油品 </div>
                <div class="bottom" @click="clickAuthorizedProducts">
                    <input class="center" disabled placeholder="请选择预授权油品" v-model="authorizedProductsLabel" />
                    <u-select
                        v-model="preAuthorizedProductsShow"
                        mode="mutil-column-auto"
                        :list="preAuthorizedProducts"
                        @confirm="authorizedProductsConfirm"
                    ></u-select>
                </div>
            </div>
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>预授权金额 </div>
                <div class="bottom" @click="clickAuthorizedAmount">
                    <input class="center" disabled placeholder="请选择预授权金额" v-model="authorizedAmountLabel" />
                    <u-select
                        v-model="authorizedAmountShow"
                        mode="mutil-column-auto"
                        :list="preAuthorizationAmount"
                        @confirm="authorizedAmountConfirm"
                    ></u-select>
                </div>
            </div>
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>签约用户姓名 </div>
                <div class="bottom">
                    <input class="center" placeholder="请输入开通ETC账号的用户姓名" v-model="userName" />
                </div>
            </div>
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>签约用户身份证号码 </div>
                <div class="bottom">
                    <input class="center" placeholder="请输入开通ETC账号的用户身份证号码" v-model="idNumber" />
                </div>
            </div>
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>车牌号码 </div>
                <div class="bottom" @click="clickLicensePlateNumber">
                    <input class="center" disabled placeholder="请输入开通ETC账号的车牌号码" v-model="licensePlateNumber" />
                </div>
            </div>
            <div class="section-box">
                <div class="top"> <div class="danger">*</div>车牌颜色 </div>
                <div class="bottom" @click="clickLicensePlateType">
                    <input class="center" disabled placeholder="请选择车牌颜色" v-model="licensePlateTypeLabel" />
                    <u-select
                        v-model="licensePlateTypeShow"
                        mode="mutil-column-auto"
                        :list="licensePlateColorList"
                        @confirm="licensePlateTypeConfirm"
                    ></u-select>
                </div>
            </div>
            <div class="section-box">
                <div class="top">车辆品牌</div>
                <div class="bottom">
                    <input class="center" placeholder="请输入开通ETC账号的车辆品牌" v-model="vehicleBrand" />
                </div>
            </div>
            <div class="section-box">
                <div class="top">车辆颜色</div>
                <div class="bottom" @click="clickVehicleColor">
                    <input class="center" disabled placeholder="请选择开通ETC账号的车辆颜色" v-model="vehicleColorLabel" />
                    <u-select
                        v-model="vehicleColorShow"
                        mode="mutil-column-auto"
                        :list="vehicleColorList"
                        @confirm="vehicleColorConfirm"
                    ></u-select>
                </div>
            </div>
            <!-- 阅读协议 -->
            <div class="oil-footer">
                <div class="agreement">
                    <u-checkbox v-model="agreementchecked" shape="circle" active-color="#FF8200">
                        <div class="agreement-txt">
                            同意
                            <span @click.stop="clickXieyi" class="oil-money-info">《中国石油昆仑ETC加油客户服务协议》</span>
                        </div>
                    </u-checkbox>
                </div>
                <div class="oil-btn">
                    <div class="oli-paybtn-gray" @click="agreedToOpen" :class="{ 'oli-pay-info': agreementchecked }">下一步</div>
                </div>
            </div>
        </div>
        <CarNumberPlateNew
            :carNo="licensePlateNumber"
            @markCar="markCar"
            ref="CarNumberPlateNew"
            v-if="licensePlateNumberShow"
        ></CarNumberPlateNew>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import pageConfig from '@/utils/pageConfig.js';
import CarNumberPlateNew from '../../components/CarNumberPlateNew.vue';
import { etcAddition } from '@/api/ETC.js';
export default {
    name: 'newETCSigning',
    data() {
        return {
            pageConfig,
            preAuthorizedProducts: [
                {
                    value: '92#汽油',
                    label: '92#汽油',
                },
                {
                    value: '95#汽油',
                    label: '95#汽油',
                },
                {
                    value: '98#汽油',
                    label: '98#汽油',
                },
                {
                    value: '0#柴油',
                    label: '0#柴油',
                },
                {
                    value: '-10#柴油',
                    label: '-10#柴油',
                },
                {
                    value: '-20#柴油',
                    label: '-20#柴油',
                },
                {
                    value: '-35#柴油',
                    label: '-35#柴油',
                },
            ], // 预授权油品
            preAuthorizationAmount: [
                { value: '200', label: '200' },
                { value: '400', label: '400' },
                { value: '600', label: '600' },
                { value: '800', label: '800' },
                { value: '1000', label: '1000' },
                { value: '1200', label: '1200' },
                { value: '1400', label: '1400' },
                { value: '1600', label: '1600' },
                { value: '1800', label: '1800' },
                { value: '2000', label: '2000' },
            ], // 预授权金额
            licensePlateColorList: [
                {
                    value: '0',
                    label: '蓝色',
                },
                {
                    value: '1',
                    label: '黄色',
                },
                {
                    value: '2',
                    label: '黑色',
                },
                {
                    value: '3',
                    label: '白色',
                },
                {
                    value: '4',
                    label: '渐变绿色',
                },
                {
                    value: '5',
                    label: '黄绿双拼色',
                },
                {
                    value: '6',
                    label: '蓝白渐变色',
                },
                {
                    value: '7',
                    label: '绿色',
                },
                {
                    value: '8',
                    label: '红色',
                },
                {
                    value: '9',
                    label: '未确定',
                },
            ], // 车牌颜色
            vehicleColorList: [
                { value: '黑色', label: '黑色' },
                { value: '白色', label: '白色' },
                { value: '红色', label: '红色' },
                { value: '灰色', label: '灰色' },
                { value: '银色', label: '银色' },
                { value: '蓝色', label: '蓝色' },
                { value: '黄色', label: '黄色' },
                { value: '棕色', label: '棕色' },
                { value: '绿色', label: '绿色' },
                { value: '橙色', label: '橙色' },
                // { value: '紫色', label: '紫色' },
                { value: '金色', label: '金色' },
                { value: '粉红', label: '粉红' },
                { value: '香槟', label: '香槟' },
                { value: '其他', label: '其他' },
            ], // 车辆颜色

            authorizedProductsValue: '', //预授权油品
            authorizedAmountValue: '', // 预授权金额
            userName: '', // 用户名
            idNumber: '', // 身份证
            licensePlateNumber: '', // 车牌号
            licensePlateTypeValue: '', // 车牌颜色
            vehicleBrand: '', // 车辆品牌
            vehicleColorValue: '', // 车辆颜色

            authorizedProductsLabel: '',
            authorizedAmountLabel: '',
            licensePlateTypeLabel: '',
            vehicleColorLabel: '',

            agreementchecked: false,
            preAuthorizedProductsShow: false, // 预授权油品
            authorizedAmountShow: false, // 预授权金额
            licensePlateNumberShow: false, // 车牌号
            licensePlateTypeShow: false, // 车牌颜色
            vehicleColorShow: false, // 车辆颜色
        };
    },
    created() {},
    mounted() {},
    methods: {
        authorizedProductsConfirm(e) {
            //  预授权油品
            this.authorizedProductsLabel = e[0].label;
            this.authorizedProductsValue = e[0].value;
        },
        authorizedAmountConfirm(e) {
            // 预授权金额
            this.authorizedAmountLabel = e[0].label;
            this.authorizedAmountValue = e[0].value;
        },
        licensePlateTypeConfirm(e) {
            // 车牌颜色
            this.licensePlateTypeLabel = e[0].label;
            this.licensePlateTypeValue = e[0].value;
        },
        vehicleColorConfirm(e) {
            // 车辆颜色
            this.vehicleColorLabel = e[0].label;
            this.vehicleColorValue = e[0].value;
        },
        // 同意协议
        clickXieyi() {
            uni.navigateTo({
                url: `/packages/setting/pages/agreement/main?value=92`,
            });
        },
        clickAuthorizedProducts() {
            // 预授权油品
            this.preAuthorizedProductsShow = !this.preAuthorizedProductsShow;
        },
        clickAuthorizedAmount() {
            // 预授权金额
            this.authorizedAmountShow = !this.authorizedAmountShow;
        },
        clickLicensePlateNumber() {
            // 车牌号
            this.licensePlateNumberShow = !this.licensePlateNumberShow;
            // this.$refs.CarNumberPlateNew.show = this.licensePlateNumberShow
            // this.$refs.CarNumberPlateNew.openKeyboard()
        },
        clickLicensePlateType() {
            // 车牌颜色
            this.licensePlateTypeShow = !this.licensePlateTypeShow;
        },
        clickVehicleColor() {
            // 车辆颜色
            this.vehicleColorShow = !this.vehicleColorShow;
        },
        markCar(e) {
            this.licensePlateNumber = e;
        },
        agreedToOpen() {
            if (this.authorizedProductsValue === '') {
                uni.showToast({
                    title: '预授权油品不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.authorizedAmountValue === '') {
                uni.showToast({
                    title: '预授权金额不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.userName === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.userName !== '' && !this.$test.checkName(this.userName)) {
                uni.showToast({
                    title: '姓名格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // // 校验身份证号
            if (this.idNumber === '') {
                uni.showToast({
                    title: '身份证不能为空，请输入身份证号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.$test.checkIdCard(this.idNumber, '1')) {
                uni.showToast({
                    title: '身份证号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.licensePlateNumber == '') {
                uni.showToast({
                    title: '车牌号码不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            const carNoValid =
                /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{6})|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/.test(
                    this.licensePlateNumber,
                );
            if (!carNoValid) {
                uni.showToast({
                    title: '车牌号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.licensePlateTypeValue == '') {
                uni.showToast({
                    title: '车牌颜色不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.agreementchecked) {
                uni.showToast({
                    icon: 'none',
                    title: '请同意《中国石油昆仑ETC加油客户服务协议》',
                    duration: 3000,
                });
                return;
            }
            let params = {
                agreementOil: this.authorizedProductsValue, // 签约油品
                agreementAmount: this.authorizedAmountValue, // 签约金额
                userName: this.userName, // 用户名
                idNo: this.idNumber, // 身份证
                carNo: this.licensePlateNumber, // 车牌号
                carNoColor: this.licensePlateTypeValue, // 车牌颜色
                carColor: this.vehicleColorValue, // 车辆颜色
                carBrand: this.vehicleBrand, // 车辆品牌

                // agreementAmount: '200', // 签约金额
                // agreementOil: '92#汽油', // 签约油品
                // userName: '齐占强', // 用户名
                // idNo: '150426198809023413', // 身份证
                // carNo: '冀A45678', // 车牌号
                // carNoColor: '0', // 车牌颜色
                // carColor: '',// 车辆颜色
                // carBrand: '',// 车辆品牌
            };
            etcAddition(params).then(res => {
                if (res.status === 0) {
                    uni.navigateTo({
                        // 关闭当前页面，跳转到应用内的某个页面
                        url: `/packages/web-view/pages/home/<USER>
                    });
                }
            });
        },
    },
    components: {
        CarNumberPlateNew,
    },
    watch: {
        agreementchecked: function (newVal, oldVal) {
            this.$Storage.isAgreePay.update(newVal);
        },
    },
};
</script>
<style scoped lang="scss">
.newETCSigning {
    width: 100%;
    // height: 100%;
    background: #efefef;
    .section-box-wrap {
        overflow-y: hidden;
        .section-box {
            height: 100%;
            margin: 15px;
            display: flex;
            flex-direction: column;
            .top {
                display: flex;
                .danger {
                    padding-top: 4px;
                }
                div {
                    color: #f43d45;
                }
            }
            .bottom {
                background-color: #fff;
                width: 100%;
                height: 45px;
                line-height: 45px;
                padding-left: 10px;
                display: flex;
                align-items: center;
                margin-right: 15px;
                .center {
                    width: 100%;
                }
                // .u-select__header__confirm
                // ::v-deep
                //   .u-drawer
                //   .u-drawer-content
                //   .u-drawer__scroll-view
                //   .u-select
                //   .u-select__header
                //   .u-select__header
                //   .u-select__header__cancel {
                //   font-size: 18px;
                // }
                // ::v-deep .u-select__header__title .u-select__header__confirm,
                // .u-select__header__btn {
                //   font-size: 18px !important;
                // }
            }
        }
        .oil-footer {
            bottom: 0;
            left: 0;
            width: 100%;
            padding-left: 10px;
            .agreement {
                font-size: 12px;
                font-weight: 500;
                color: #909090;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                position: relative;
                .agreement-txt {
                    font-weight: bold;
                    font-size: 14px;
                    .oil-money-info {
                        color: $btn-color;
                        width: 210px;
                        text-align: left;
                        // overflow: hidden;
                    }
                }
            }
            .oil-btn {
                // background: #ffffff;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                margin-right: 10px;

                .oil-money {
                    font-size: 15px;
                    font-weight: 500;
                    width: 210px;
                    text-align: left;
                    .oil-money-bold {
                        font-size: 24px;
                    }
                }
                .oli-paybtn-gray {
                    // width: 120px;
                    width: 700px;
                    height: 44px;
                    margin-top: 10px;
                    margin-bottom: 10px;
                    background: #ff8200;
                    border-radius: 5px;
                    font-size: 15px;
                    font-weight: 500;
                    color: #ffffff;
                    line-height: 44px;
                    text-align: center;
                }

                .oli-pay-info {
                    background: $btn-color;
                }
            }
        }
    }
    ::v-deep .u-select__header__confirm {
        font-size: 18px;
    }
    ::v-deep .u-select__header__cancel {
        font-size: 18px;
    }
}
</style>
