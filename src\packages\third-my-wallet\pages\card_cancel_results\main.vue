<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details p-bf bg-F7F7FB">
            <zj-navbar title="昆仑e享卡注销" :border-bottom="false"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div">
                    <!-- <img :src="amount  >  0 ? '../../images/recharge.png' :'../../images/consumption.png'" alt /> -->
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'success'">
                        <img src="../../images/success_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">您已完成注销</div>
                        <div class="font-12 color-999 text">如果您需要使用昆仑e享卡进行加油支付，请您重新开通</div>
                    </div>
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'fail'">
                        <img src="../../images/fail_img.png" alt />
                        <div class="amount2 font-16 color-333 weight-bold">注销e享卡失败</div>
                        <div class="text_div">
                            <!-- #ifdef MP-MPAAS -->
                            <div>{{ alertInfo.desString }}</div>
                            <!-- #endif -->
                            <!-- #ifdef MP-WEIXIN-->
                            <div>{{ alertInfo.message }}</div>
                            <!-- #endif -->
                            <!-- #ifdef MP-ALIPAY-->
                            <div>{{ alertInfo.message }}</div>
                            <!-- #endif -->
                            <!-- <div>二.昆仑e享卡已被冻结，请您携带本人身份证到线下网点解冻后，再进行注销;</div>
            <div>三.昆仑e享卡存在未结清的历史交易，请您将昆仑 e享卡的预约订单与待支付订单 、实体卡的待支付订单完成支付后，再进行注销;</div>-->
                        </div>
                    </div>
                </div>

                <div class="btn_div fl-jus-bet">
                    <div class="finish_verification btn-plain color-E64F22 font-16" @click="backClick()">返回首页</div>
                    <div class="finish_verification primary-btn color-fff font-16" @click="getCharge()">返回钱包</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
// import { rechargeStatusQuery } from '../api/wallet'
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-charge-result',
    data() {
        return {
            status: 'fail',
            alertInfo: '',
        };
    },
    onLoad(option) {
        let queryInfo = JSON.parse(decodeURIComponent(option.data));
        console.log(queryInfo, 'queryInfo===================');
        // #ifdef MP-WEIXIN
        this.status = queryInfo.type;
        this.alertInfo = queryInfo.alertInfo;
        // #endif
        // #ifdef MP-ALIPAY
        this.status = queryInfo.type;
        this.alertInfo = queryInfo.alertInfo;
        // #endif
        // #ifdef MP-MPAAS
        this.status = queryInfo.type;
        this.alertInfo = queryInfo.alertInfo;
        // #endif
    },
    mounted() {},
    methods: {
        //关闭当前小程序
        backClick() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver();
            // #endif
            // #ifdef MP-ALIPAY
            this.$sKit.layer.backHomeFun();
            // #endif
            // #ifdef MP-WEIXIN
            this.$sKit.layer.backHomeFun();
            // #endif
        },
        //返回钱包页
        getCharge() {
            uni.navigateBack({ delta: 4 });
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../../../s-kit/css/index.scss';
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;

            padding-top: 40px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 12px;
            }

            .amount {
                margin-top: 20px;
                line-height: 23px;
            }

            .amount2 {
                margin-top: 16px;
                line-height: 23px;
            }

            .text_div {
                margin-top: 40px;
                margin-bottom: 2px;

                div {
                    color: #333;
                    font-size: 14px;
                    line-height: 22px;
                    margin-bottom: 12px;
                }
            }

            .text {
                line-height: 23px;
                margin-bottom: 40px;
            }
        }
    }
}

.btn_div {
    font-size: 15px;
    display: flex;

    flex-direction: row;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
