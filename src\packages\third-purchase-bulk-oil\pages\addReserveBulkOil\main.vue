<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div>
        <div class="pageMpaas">
            <div class="fl-column p-bf bg-F7F7FB">
                <zj-navbar :height="44" title="散装油预约"></zj-navbar>
                <div class="view padding-16 f-1">
                    <div class="spacing p-LR-16 bg-fff border-rad-8">
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen border-bottom">
                            <div class="font-14 color-333 weight-400">姓名</div>
                            <div class="font-14 color-999 weight-400">{{ userInformation.realName }}</div>
                        </div>
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen border-bottom">
                            <div class="font-14 color-333 weight-400">手机号</div>
                            <div class="font-14 color-999 weight-400">{{ userInformation.phone }}</div>
                        </div>
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen">
                            <div class="font-14 color-333 weight-400">身份证号</div>
                            <div class="font-14 color-999 weight-400">{{ userInformation.idNo }}</div>
                        </div>
                    </div>
                    <div class="spacing p-LR-16 bg-fff border-rad-8">
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen border-bottom">
                            <div class="font-14 color-333 weight-400">居住地</div>
                            <div class="fl-row fl-al-cen color-999 weight-400" @click="openPicker">
                                <div class="font-14">{{ provinceCityDistrict || '请选择' }}</div>
                                <u-icon :name="addressShow ? 'arrow-down' : 'arrow-right'" class="uicon"></u-icon>
                            </div>
                        </div>
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen te-right">
                            <input
                                id="address-input"
                                v-model="detailedInformation"
                                class="address-input"
                                placeholder="如道路、门牌号、小区楼号、单元"
                                type="text"
                            />
                        </div>
                    </div>
                    <div class="spacing p-LR-16 bg-fff border-rad-8">
                        <div
                            v-for="(item, index) in pickerConfigs"
                            :key="index"
                            class="containerMiddle fl-row fl-jus-bet fl-al-cen border-bottom"
                        >
                            <div class="font-14 color-333 weight-400">{{ item.title }}</div>
                            <div class="substance-tip-right" @click="openPicker2(index)">
                                <div class="fl-row font-14 color-999 weight-400 fl-al-cen">
                                    <div class="picker-content">
                                        {{ item.selectedLabel }}
                                    </div>
                                    <u-icon :name="item.pickerShow ? 'arrow-down' : 'arrow-right'" class="uicon"></u-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="spacing p-LR-16 bg-fff border-rad-8">
                        <div class="containerMiddle fl-row fl-jus-bet fl-al-cen border-bottom">
                            <div class="font-14 color-333 weight-400">购油数量</div>
                            <div class="fl-row fl-al-cen f-1">
                                <div class="width100 fl-row fl-al-cen color-999 weight-400 te-right">
                                    <input
                                        id="address-input"
                                        v-model="quantityOfPurchasedOil"
                                        class="address-input"
                                        :placeholder="placeholderText"
                                        type="number"
                                        @input="handleNum"
                                    />
                                    <div style="line-height: 97rpx">L</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="btnPlaceholder"> </div>
                    <div class="btn primary-btn border-rad-8 shad-ef color-fff" @click="submit">提交</div>
                </div>
            </div>
        </div>
        <SelectCity
            :provinceCityArray="provincesAndCitiesList"
            :show="addressShow"
            v-if="addressShow"
            @changeClick="changeClick"
            @hideShow="onhideShow"
            @sureSelectArea="onsetCity"
        >
        </SelectCity>
        <view v-if="isMaskVisible" class="mask"></view>
        <!-- 弹窗部分 -->
        <view v-for="(item, index) in pickerConfigs" v-if="item.pickerShow" :key="index" class="picker-modal">
            <view class="picker-header">
                <view class="picker-cancel color-E64F22" @click="closePicker(index)">取消</view>
                <view class="picker-confirm color-E64F22" @click="confirmPicker(index)">确定</view>
            </view>
            <picker-view :value="item.pickerValue" class="picker-view" @change="handlePickerChange(index, $event)">
                <picker-view-column v-if="item.usageArray">
                    <view v-for="(option, optionIndex) in item.usageArray" :key="optionIndex" class="picker-item">{{ option.label }}</view>
                </picker-view-column>
                <picker-view-column v-if="item.selectStationArray">
                    <view v-for="(option, optionIndex) in item.selectStationArray" :key="optionIndex" class="picker-item">{{
                        option.orgName
                    }}</view>
                </picker-view-column>
                <picker-view-column v-if="item.varietyArray">
                    <view v-for="(option, optionIndex) in item.varietyArray" :key="optionIndex" class="picker-item">{{
                        option.fuelName
                    }}</view>
                </picker-view-column>
            </picker-view>
        </view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { bulkOilRegister, fuelLiters } from '../../../../s-kit/js/v3-http/https3/bulkOil/index';
import { identityAuthInfo, basicInfoQuery } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { getFuelGunByOrgCodeApi, stationListApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
export default {
    mixins: [publicMixinsApi],
    components: {
        SelectCity,
    },

    data() {
        return {
            // 用户姓名
            name: '',
            // 用户手机号
            phone: '',
            // 用户身份证号
            idNo: '',
            // 用作展示的用户身份证号
            onlyDisplayIdNo: '',
            // 省市区显示和关闭
            addressShow: false,
            // 详细地址
            detailedInformation: '',
            // 省市区
            provinceCityDistrict: '',
            // 省市区数组
            provincesAndCitiesList: [],
            // 购油数量
            quantityOfPurchasedOil: '',
            // 用户基本信息
            userInformation: {},
            // 弹窗配置项
            pickerConfigs: [
                {
                    title: '购油用途',
                    usageArray: [
                        {
                            label: '车船用油',
                            value: '1',
                        },
                        {
                            label: '农机',
                            value: '2',
                        },
                        {
                            label: '自驾车主',
                            value: '3',
                        },
                        {
                            label: '其他',
                            value: '4',
                        },
                    ],
                    pickerShow: false,
                    // 默认选中第一项
                    pickerValue: [0],
                    selectedLabel: '请选择',
                },
                {
                    title: '请选择加油站',
                    selectStationArray: [],
                    pickerShow: false,
                    // 默认选中第一项
                    pickerValue: [0],
                    selectedLabel: '请选择',
                },
                {
                    title: '购油品种',
                    varietyArray: [],
                    pickerShow: false,
                    // 默认选中第一项
                    pickerValue: [0],
                    selectedLabel: '请选择',
                },
                // 可以继续添加更多弹窗配置
            ],
            // 当前显示的弹窗索引
            activePickerIndex: null,
            // 控制遮罩显示
            isMaskVisible: false,
            // 最大加油升数
            maximumRefuelingLiters: '',
        };
    },
    computed: {
        ...mapState({
            lonV3: state => state.locationV3_app.lonV3,
            latV3: state => state.locationV3_app.latV3,
            bookingRefueling: state => state.locationV3_app.bookingRefueling,
            walletInfo: state => state.wallet.walletInfo,
            pullDownToSelectAnArray: state => state.bulkOil.pullDownToSelectAnArray,
        }),
        placeholderText() {
            return '请填写购油升数' + (this.maximumRefuelingLiters ? `1-${this.maximumRefuelingLiters}` : '1-10');
        },
    },
    watch: {},
    mounted() {
        this.obtainUserInformation();
    },
    methods: {
        openPicker2(index) {
            // 隐藏所有弹窗
            this.pickerConfigs.forEach((config, i) => {
                if (i !== index) {
                    config.pickerShow = false;
                }
            });
            // 显示当前点击的弹窗
            this.pickerConfigs[index].pickerShow = true;
            // 设置当前显示的弹窗索引
            this.activePickerIndex = index;
            // 显示遮罩
            this.isMaskVisible = true;
        },
        closePicker(index) {
            // 关闭当前点击的弹窗
            this.pickerConfigs[index].pickerShow = false;
            if (this.activePickerIndex === index) {
                // 清除当前显示的弹窗索引
                this.activePickerIndex = null;
            }
            // 隐藏遮罩
            this.isMaskVisible = false;
        },
        /**
         * @description 弹窗确认事件
         * @param {number} index - 当前弹窗的索引
         * @returns {void}
         */
        confirmPicker(index) {
            // 获取当前弹窗的配置信息
            const config = this.pickerConfigs[index];
            // 初始化一个变量来存储选中的选项
            let selectedOption = null;
            // 使用 switch 语句根据当前弹窗的类型来获取选中的选项
            switch (true) {
                /*
                 * !!config.selectStationArray 的作用是检查 config.selectStationArray 是否存在且不为空。
                 * 如果 config.selectStationArray 是一个非空数组，!!config.selectStationArray 的结果是 true，代码会进入这个 case 分支。
                 * 如果 config.selectStationArray 是 null、undefined 或其他假值，!!config.selectStationArray 的结果是 false，代码不会进入这个 case 分支。
                 * */
                // 如果当前弹窗有 usageArray（购油用途数组）
                case !!config.usageArray:
                    // 获取选中的选项
                    selectedOption = config.usageArray[config.pickerValue[0]];
                    // 将选中的选项存储到 selectedUsage 中
                    this.selectedUsage = selectedOption;
                    // 更新当前弹窗的显示标签为选中的选项的 label
                    config.selectedLabel = selectedOption.label;
                    break;
                // 如果当前弹窗有 selectStationArray（选择站点数组）
                case !!config.selectStationArray:
                    // 获取选中的选项
                    selectedOption = config.selectStationArray[config.pickerValue[0]];
                    // 将选中的选项存储到 selectStationInfo 中
                    this.selectStationInfo = selectedOption;
                    this.$store.commit('setSelectBulkOilStationList', [selectedOption]);
                    this.$store.commit('setSelectBulkOilStationObject', selectedOption);
                    // 更新当前弹窗的显示标签为选中的选项的 orgName
                    config.selectedLabel = selectedOption.orgName;
                    console.log(config, 'config====');
                    // 清空品种数组
                    const varietyConfig = this.pickerConfigs.find(item => item.varietyArray);
                    if (varietyConfig) {
                        varietyConfig.varietyArray = [];
                        varietyConfig.pickerValue = [0]; // 重置选择值
                        varietyConfig.selectedLabel = '请选择'; // 重置显示标签
                        this.selectedVariety = null; // 清空选中的品种
                    }
                    this.getFuelGunByOrgCode();
                    // 获取最大加油升数限制
                    this.getFuelLiters();
                    // 切换油站时将购油数量清空
                    this.quantityOfPurchasedOil = '';
                    break;

                // 如果当前弹窗有 varietyArray（品种数组）
                case !!config.varietyArray:
                    // 获取选中的选项
                    selectedOption = config.varietyArray[config.pickerValue[0]];
                    // 将选中的选项存储到 selectedVariety 中
                    this.selectedVariety = selectedOption;
                    // 更新当前弹窗的显示标签为选中的选项的 fuelName
                    config.selectedLabel = selectedOption.fuelName;
                    break;
            }

            console.log(this.selectedUsage, this.selectStationInfo, this.selectedVariety, '选择的值');

            // 关闭当前弹窗
            config.pickerShow = false;

            // 隐藏遮罩层
            this.isMaskVisible = false;

            // 如果当前弹窗是活动的弹窗，清除活动弹窗索引
            if (this.activePickerIndex === index) {
                this.activePickerIndex = null;
            }
        },
        /**
         * @description 滑动停下但是未点击确认时的值
         * @param {number} index - 当前弹窗的索引
         * @param {Object} e - 选中的值
         * @returns {void}
         */
        handlePickerChange(index, e) {
            this.pickerConfigs[index].pickerValue = e.detail.value;
            console.log(e.detail, '选择的值');
        },
        /**
         * @description 获取用户信息(脱敏和未脱敏)和获取油站列表，以及获取油站列表后调用获取油品
         * @param {String} longitude - 经度
         * @param {String} latitude - 纬度
         * @param {Number} pageNum - 页码
         * @param {Number} pageSize - 条数
         * @param {String} distance - 范围
         * @param {String} bookingRefueling - 是否是新站
         * @param {String} mapType - 地图坐标系标识（0高德，1百度，2腾讯）
         * @param {String} orgCode - 会员开户地
         * @param {Boolean} isload - 是否显示加载Loding
         * @returns {void}
         */
        async obtainUserInformation() {
            try {
                // 显示加载提示
                this.showLoading();

                // 并行调用前三个接口
                const [notDesensitizedRes, desensitizationRes, provinceAndCityRes] = await Promise.all([
                    identityAuthInfo({}, { isload: false }),
                    basicInfoQuery({}, { isload: false }),
                    provinceAndCityList({}, { isload: false }),
                ]);

                // 合并用户信息
                this.userInformation = {
                    ...(notDesensitizedRes.success ? notDesensitizedRes.data : {}),
                    ...(desensitizationRes.success ? desensitizationRes.data : {}),
                };

                // 处理省市列表
                if (provinceAndCityRes.success) {
                    this.provincesAndCitiesList = (provinceAndCityRes.data || []).map(province => ({
                        name: province.parentAreaName,
                        code: province.parentAreaCode,
                        city: province.areaList,
                    }));
                }

                // 设置油站选择数组
                this.pickerConfigs[1].selectStationArray = this.pullDownToSelectAnArray;

                // 获取油品油号
                await this.getFuelGunByOrgCode();

                // 打印结果
                console.log(this.provincesAndCitiesList, '省市获取成功了吗');
                console.log(this.pickerConfigs[1].selectStationArray, '油站列表');
                console.log(this.userInformation, '合并后的用户信息');

                return Promise.resolve();
            } catch (error) {
                console.error('获取用户信息失败:', error);
                // 如果整个流程出错，设置默认值
                this.userInformation = {};
                return Promise.reject(error);
            } finally {
                // 隐藏加载提示
                this.hideLoading();
            }
        },
        /**
         * @description 获取油品
         * @param {String} orgCode - 油站编码
         * @param {String} onlineType -
         * @param {String} hosCode -
         * @returns {void}
         */
        async getFuelGunByOrgCode() {
            // 根据 stationListApi 的结果调用 getFuelGunByOrgCodeApi
            if (this.pickerConfigs[1].selectStationArray.length > 0) {
                try {
                    console.log(this.selectStationInfo, 'this.selectStationInf===');
                    // 构建请求参数对象
                    let params = {
                        // 从选中的站点对象中获取 orgCode 属性，如果对象不存在则为 undefined
                        orgCode: this.selectStationInfo?.orgCode || this.pickerConfigs[1].selectStationArray[0]?.orgCode,
                        // 根据选中站点的 stationType 属性判断 onlineType 的值
                        onlineType:
                            this.selectStationInfo?.stationType || this.pickerConfigs[1].selectStationArray[0]?.stationType == 1
                                ? '1'
                                : '0',
                        // 从选中的站点对象中获取 hosCode 属性，如果对象不存在则为空字符串
                        hosCode: this.selectStationInfo?.hosCode || this.pickerConfigs[1].selectStationArray[0]?.hosCode || '',
                    };
                    console.log(params, '获取油品参数');
                    let oilProductNumberRes = await getFuelGunByOrgCodeApi(params, { isload: false });

                    this.pickerConfigs[2].varietyArray = oilProductNumberRes.data || [];
                    console.log(this.pickerConfigs[2].varietyArray, '获取油品成功？');
                } catch (error) {
                    console.error('getFuelGunByOrgCodeApi 请求失败:', error);
                }
            }
        },
        async getFuelLiters() {
            let params = {
                stationCode: this.selectStationInfo?.orgCode || this.pickerConfigs[1].selectStationArray[0]?.orgCode,
            };
            let res = await fuelLiters(params);
            console.log('获取购油升数', res.data);
            if (res.success && res.data.enabledStatus === 0) {
                this.maximumRefuelingLiters = res.data.maxApplyLiters;
            } else {
                this.maximumRefuelingLiters = '';
            }
        },
        /**
         * @description 购油升数正则校验
         * @returns {void}
         */
        handleNum(e) {
            let value = e.detail.value;
            let price = value.toString().match(/^\d+(\.\d{0,2})?$/) || '';
            this.$nextTick(() => {
                this.quantityOfPurchasedOil = typeof price === 'string' ? price : price[0];
            });
        },
        /**
         * @description 省市区改变小箭头
         * @returns {void}
         */
        openPicker() {
            this.addressShow = !this.addressShow;
        },
        /**
         * @description 省市区滑动的时候改变的省
         * @returns {void}
         */
        changeClick(e) {
            console.log(e, 'changeClick');
        },
        /**
         * @description 省市区选择的确定事件
         * @param {Object} detail - 省市
         * @returns {void}
         */
        onsetCity({ detail }) {
            console.log('获取的省市区', detail.province + detail.city);
            this.addressShow = false;
            this.provinceCityDistrict = detail.province + detail.city;
        },

        /**
         * @description 关闭选择省市区的弹窗
         * @returns {void}
         */
        onhideShow() {
            this.addressShow = false;
        },
        /**
         * @description 用户提交购油申请
         * @param {String} userName 用户姓名（必填）
         * @param {String} phone 用户手机号（必填）
         * @param {String} idNum 用户身份证号（必填）
         * @param {Integer} idType 用户身份证类型（必填）
         * @param {String} region 用户选择的省市（必填）
         * @param {String} address 用户填写的详细地址（必填）
         * @param {String} useOilPurpose 用油用途（可选）
         * @param {String} productNo 油品号（可选）
         * @param {String} productName 油品名称（必填）
         * @param {String} oilQuantity 购油数量（可选）
         * @param {String} stationCode 油站编码（必填）
         * @returns {*} 返回提交结果，具体类型根据接口定义
         */
        async submit() {
            let params = {
                userName: this.userInformation.realName,
                phone: this.userInformation.phone,
                idNum: this.userInformation.identityNo,
                idType: '1',
                region: this.provinceCityDistrict,
                address: this.detailedInformation,
                useOilPurpose: this.selectedUsage?.label,
                productNo: this.selectedVariety?.fuelNo,
                productName: this.selectedVariety?.fuelName,
                oilQuantity: this.quantityOfPurchasedOil,
                stationCode: this.selectStationInfo?.orgCode,
            };
            const isValid = this.validateFormData(params);
            if (!isValid) return;
            console.log(params, '用户提交购油申请');
            let res = await bulkOilRegister(params);
            if (res.success) {
                // 移除散装油查询标识，查所有状态的数据
                uni.removeStorage({
                    key: 'bulkParamFlag',
                });
                this.$store.commit('setRefreshBulkOilList', true);
                setTimeout(() => {
                    uni.navigateBack(
                        {
                            delta: 1,
                            success: () => {
                                // // 获取页面栈
                                // const pages = getCurrentPages();
                                // // 获取上一个页面实例
                                // const prevPage = pages[pages.length - 1];
                                // // 调用上一个页面的刷新方法
                                // console.log(prevPage, prevPage.$vm.bulkOilReservationList, '返回测试');
                                // if (prevPage && prevPage.$vm.bulkOilReservationList) {
                                //     prevPage.$vm.bulkOilReservationList();
                                // }
                            },
                        },
                        300,
                    );
                });
                // let url = '/packages/third-purchase-bulk-oil/pages/home/<USER>';
                // let type = 'redirectTo';
                // this.$sKit.layer.useRouter(url, {}, type);
            }
        },
        /**
         * @description 提交申请单时候的校验
         * @returns {void}
         */
        validateFormData(data) {
            // 整合字段信息，包含中文名称、类型和提示信息
            const requiredFields = {
                region: {
                    chineseName: '居住地',
                    type: 'select',
                    message: '请选择',
                },
                address: {
                    chineseName: '详细地址',
                    type: 'input',
                    message: '请填写',
                },
                useOilPurpose: {
                    chineseName: '购油用途',
                    type: 'select',
                    message: '请选择',
                },
                stationCode: {
                    chineseName: '加油站',
                    type: 'select',
                    message: '请选择',
                },
                productName: {
                    chineseName: '购油品种',
                    type: 'select',
                    message: '请选择',
                },
                oilQuantity: {
                    chineseName: '购油数量',
                    type: 'input',
                    message: '请填写',
                },
            };

            // for (const [field, { chineseName, message }] of Object.entries(requiredFields)) {
            //     if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
            //         console.log(`${chineseName}: ${message}`);
            //         uni.showToast({
            //             title: `${message}${chineseName} `,
            //             duration: 2000,
            //             mask: true,
            //             icon: 'none',
            //         });
            //         return false;
            //     }
            // }
            // return true;
            for (const [field, { chineseName, message }] of Object.entries(requiredFields)) {
                if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
                    console.log(`${chineseName}: ${message}`);
                    uni.showToast({
                        title: `${message}${chineseName} `,
                        duration: 2000,
                        mask: true,
                        icon: 'none',
                    });
                    return false;
                }

                if (field === 'oilQuantity') {
                    const maximumRefuelingLiters = this.maximumRefuelingLiters;
                    const maxValue = maximumRefuelingLiters ? parseFloat(maximumRefuelingLiters) : 10;
                    const minValue = 1; // 新增：设置最小值为 1
                    const oilQuantity = parseFloat(data.oilQuantity);
                    if (isNaN(oilQuantity)) {
                        console.log(`购油数量: 请填写有效的数字`);
                        uni.showToast({
                            title: `请填写有效的购油数量`,
                            duration: 2000,
                            mask: true,
                            icon: 'none',
                        });
                        return false;
                    }
                    if (oilQuantity < minValue) {
                        console.log(`购油数量不能小于 ${minValue}`);
                        uni.showToast({
                            title: `购油数量不能小于 ${minValue}L`,
                            duration: 2000,
                            mask: true,
                            icon: 'none',
                        });
                        return false;
                    }
                    if (oilQuantity > maxValue) {
                        console.log(`购油数量不能超过 ${maxValue}`);
                        uni.showToast({
                            title: `购油数量不能超过 ${maxValue}L`,
                            duration: 2000,
                            mask: true,
                            icon: 'none',
                        });
                        return false;
                    }
                }
            }

            return true;
        },
        showLoading() {
            uni.showLoading({
                title: '加载中...',
                mask: true,
                icon: 'none',
            });
        },
        hideLoading() {
            uni.hideLoading();
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    position: relative;
    padding-bottom: 98rpx;
}
.spacing {
    margin-bottom: 25rpx;
}

.containerMiddle {
    height: 97rpx;
    width: 100%;
    padding: 10px 0;
    .picker-content {
        white-space: nowrap;
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;
    }
}
img {
    width: 32rpx;
    height: 32rpx;
}
.address-input {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: #999999;
    //direction: rtl;
}
.uicon {
    width: 32rpx;
    height: 32rpx;
}
.btnPlaceholder {
    width: 100%;
    height: 44px;
}
.btn {
    width: 92%;
    height: 44px;
    position: absolute;
    line-height: 44px;
    bottom: 10px;
}

.container {
    padding: 20px;
}

.picker-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 1000;
}

.picker-header {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #eee;
    font-size: 30rpx;
}

.picker-cancel,
.picker-confirm {
    //color: #007aff;
    font-size: 16px;
}

.picker-view {
    height: 200px;
}

.picker-item {
    text-align: center;
    line-height: 40px;
}
/* 遮罩样式 */
.mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色 */
    z-index: 999; /* 遮罩层级低于弹窗 */
}
</style>
