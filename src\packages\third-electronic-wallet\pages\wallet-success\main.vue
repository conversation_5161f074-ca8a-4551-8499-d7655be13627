<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <zj-navbar :height="44" title="开通成功"></zj-navbar>
        <div class="content-view">
            <img class="success-img" src="../../images/successOrigin.png" alt="" />
            <div class="success-text">恭喜您，昆仑e享卡开通成功</div>
            <!-- #ifdef MP-ALIPAY -->
            <ecard-bind :userData="userData" :tapClick="next" />
            <div class="btns">
                <div class="btn success-btn" @click="next()">立即充值</div>
                <div class="btn backhome-btn" @click="backHome()">回到首页</div>
            </div>
            <!-- #endif -->
            <!-- #ifndef MP-ALIPAY -->
            <div class="btns2">
                <div class="btn success-btn" @click="next()">立即充值</div>
                <div class="btn backhome-btn" @click="backHome()">回到首页</div>
            </div>
            <!-- #endif -->
            <zj-marketing-coupon class="zj-marketing-coupon"></zj-marketing-coupon>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { baseType } from '../../../../../project.config';
import Layer from '../../../../s-kit/js/layer';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'walletSuccess',
    data() {
        return {
            userData: {},
        };
    },
    onLoad(options) {
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        this.init();
        // #endif
    },
    computed: {
        ...mapState({
            cityCode: state => state.locationV3_app.cityCode,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        async init() {
            const tokenInfo = await uni.getStorageSync('tokenInfo');
            this.userData.cnpcUid = tokenInfo.memberNo;
            if (baseType == 'prd') {
                this.userData.env = 'online';
            } else {
                this.userData.env = 'offline';
            }
            this.userData.cityCode = this.cityCode;
            // this.userData.cityCode = '110000';

            console.log(this.userData, 'userData');
        },
        next(e) {
            let url = '/packages/third-my-wallet/pages/home/<USER>';
            let params = { refer: 'r04' };
            let type = 'reLaunch';
            Layer.useRouter(url, params, type);
        },
        backHome() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.backHomeFun();
            // #endif
        },
    },
};
</script>
<style scoped lang="scss">
.content-view {
    width: 100%;
    height: 100%;
    background: #f7f7fb;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 16px;
    position: relative;

    .success-img {
        height: 128rpx;
        width: 128rpx;
        margin-top: 80rpx;
    }

    .success-text {
        margin-top: 32rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        line-height: 46rpx;
        margin-bottom: 40px;
    }
    .btns {
        position: absolute;
        top: 188px;
        left: 18px;
        right: 18px;
        .btn {
            // width: 686rpx;
            height: 88rpx;
            text-align: center;
            font-weight: bold;
            font-size: 36rpx;
            line-height: 88rpx;
            border-radius: 16rpx;
        }
        .success-btn {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            color: #ffffff;
            margin-bottom: 15px;
        }
        .backhome-btn {
            background: #ffffff;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }

    .btns2 {
        width: 100%;

        .btn {
            // width: 686rpx;
            height: 88rpx;
            text-align: center;
            font-weight: bold;
            font-size: 36rpx;
            line-height: 88rpx;
            border-radius: 16rpx;
        }
        .success-btn {
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            color: #ffffff;
            margin-bottom: 15px;
        }
        .backhome-btn {
            background: #ffffff;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }
    .zj-marketing-coupon {
        width: 100%;
    }
}
</style>
