// #ifdef MP-MPAAS
import Config from '../third-config.js';
import store from '../../../store/index';
import { singleton } from '../singleton';
import { jwtTokenvalue } from '../v3-http/post.js';

import cnpcBridge from '../v3-native-jsapi/cnpcBridge.js';
let isHarmony = false;
// #endif
class mpaasPayPlugin {
    // #ifdef MP-MPAAS
    // constructor() {
    //     this.init();
    // }
    init() {
        return new Promise(async (resolve, reject) => {
            // 获取用户信息
            let gsmsToken;
            let platform;
            let systemInfo = my.getSystemInfoSync();
            console.log('system---', systemInfo.system);
            if (systemInfo && (systemInfo.system.includes('Harmony') || systemInfo.platform == 'Harmony')) {
                isHarmony = true;
            }
            if (isHarmony) {
                platform = 'harmony';
                let userTokenInfo = await cnpcBridge.getUserTokenInfo();
                gsmsToken = userTokenInfo.gsmsToken;
            } else {
                gsmsToken = await jwtTokenvalue();
            }
            // let openid = userTokenInfo.memberNo
            my.loadPlugin({
                plugin: `${Config.app.mpassplugins.payPlugin.provider}@*`,
                success: async () => {
                    try {
                        const { mpaasAppId, baseType, clientCode, rpcUrl, workspaceid, secretKey, noRequestBody } = Config;
                        let params = {
                            token: gsmsToken,
                            data: {},
                            baseType,
                            clientCode,
                            platform: platform,
                            mpaas: {
                                appid: mpaasAppId,
                                baseUrl: rpcUrl,
                                workspaceid,
                                secretKey,
                                signType: 'md5',
                                noRequestBody: false,
                            },
                        };

                        console.log('支付插件', params);
                        const payPlugin = requirePlugin(`dynamic-plugin://${Config.app.mpassplugins.payPlugin.provider}`);
                        let initResult = await payPlugin.InitPay(params);
                        console.log('mpaasPayPlugin初始化', initResult, payPlugin);
                        if (initResult.code === 'PAY_SUCCESS') {
                            console.log('支付插件初始化-succ', initResult);

                            // let resparams = {
                            //     stationCode: '1-A4301-C001-S005',
                            //     // stationCode: this.unPaidInfo.stationCode,
                            // };
                            // const res = await payPlugin.GetBuyTypeList(resparams)
                            // console.log('payList---', res);

                            Object.assign(this, payPlugin);
                            resolve(payPlugin);
                        } else {
                            console.log('支付插件初始化-fail', initResult);
                            // 截取字符串后面的数据
                            let errIndex = initResult.msg.indexOf(':');
                            let errorCode = '';
                            let customErr = '';
                            if (errIndex !== -1) {
                                errorCode = initResult.msg.slice(0, errIndex);
                                customErr = initResult.msg.slice(errIndex + 1, initResult.msg.length);
                            } else {
                                customErr = initResult.msg;
                            }
                            store.dispatch('zjShowModal', {
                                title: customErr,
                                content: `错误码：${errorCode}`,
                                confirmText: '确定',
                                success(res) {
                                    if (res.confirm) {
                                        console.log('用户点击确定');
                                    } else if (res.cancel) {
                                        console.log('用户点击取消');
                                    }
                                },
                            });
                        }
                    } catch (err) {
                        console.log('payPlugin初始化失败', err);
                        reject(err);
                    }
                },
            });
        });
    }
    // #endif
}
// #ifdef MP-MPAAS
export default singleton(mpaasPayPlugin);
// #endif
