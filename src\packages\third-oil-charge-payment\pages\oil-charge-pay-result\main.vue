<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas" style="background-color: #f7f7fb">
        <div class="oil-charge-pay-result fl-column">
            <zj-navbar :height="44" title="加油账单" :border-bottom="false"></zj-navbar>
            <div class="content">
                <div class="station-title"><img src="../../image/icon-logo-24.png" alt="" />{{ result.stationName }}</div>
                <!-- #ifdef MP-WEIXIN || MP-MPAAS || H5-->
                <div class="wxCode-box" v-if="codeImg && orderNo">
                    <addAnagerBox @getCodeImg="getCodeImg" :orgCode="result.stationCode" :orderNo="orderNo" type="order"></addAnagerBox>
                </div>
                <!-- #endif -->
                <div class="mar2412">
                    <oilChargeBillTime :resultData="result" ref="child"></oilChargeBillTime>
                </div>
                <zj-marketing-coupon @getMarketCouponShow="getMarketCouponShow"></zj-marketing-coupon>
                <!-- #ifdef MP-MPAAS || H5-->
                <div v-if="intMarketText && !marketCouponShow" class="wxCode-box2">
                    <intMarket :pageID="intMarketPageID" :stationCode="result.stationCode" @getIntMarketText="getIntMarketText"></intMarket>
                </div>
                <!-- #endif -->
                <oilChargeBillInfo :resultData="result" :o2oGoodList="o2oGoodList"></oilChargeBillInfo>
                <div class="o2o-card" v-if="o2oGoodList.length > 0">
                    <div class="card-title">
                        <div class="card-title-left">本站推荐</div>
                        <div class="card-title-right" @click="toO2o(false)">
                            更多
                            <div class="arrow"></div>
                        </div>
                    </div>
                    <myGoodsWaterfall
                        :dataList="o2oGoodList"
                        @goodsClick="goodsClick"
                        @goodsCountChange="goodsCountChange"
                        idKey="pluId"
                        ref="myGoodsWaterfall"
                    ></myGoodsWaterfall>
                </div>
            </div>
            <div
                :class="{
                    'bottom-area': true,
                    padL110: o2oGoodList.length > 0,
                    padL15: o2oGoodList.length <= 0,
                }"
            >
                <div class="shopping-cart" @click="toO2o(true)" v-if="o2oGoodList.length > 0">
                    <div class="shopping-cart-dot">{{ o2oShoppingNum || 0 }}</div>
                    <img src="../../image/shoppingCart.png" alt="" />
                </div>
                <!-- #ifndef H5-CLOUD -->
                <div
                    class="btn btn-plain font-16 color-E64F22 weight-400"
                    @click="evaluationAction"
                    v-if="orderCommentFlag == '1' && result.orderNo.length >= 20"
                    >去评价</div
                >
                <div class="btn btn-plain font-16 color-E64F22 weight-400" @click="backMainPage" v-else>返回首页</div>
                <!-- #endif -->
                <!-- #ifdef H5-CLOUD -->
                <div class="btn btn-plain font-16 color-E64F22 weight-400" @click="backMainPage">返回首页</div>
                <!-- #endif -->
                <div class="btn primary-btn font-16 color-fff weight-400" style="margin-left: 15px" @click="openInvoice">去开票</div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
        <zjMarket
            v-if="maskPage"
            marketType="screenMask"
            ref="maskBanner"
            :payDoneMoney="actualPayTotalAmount"
            :oilProductCode="result.productNo"
            :orgCode="showMarkerArrV3_app[0] ? showMarkerArrV3_app[0].orgCode : ''"
            spaceType="payment_page"
        ></zjMarket>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { beforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
// import { savedOrderApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { consumeOrderDetailApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import oilChargeBillInfo from '../../components/pay-result/oil-charge-bill-info/index.vue';
import oilChargeBillTime from '../../components/pay-result/oil-charge-bill-time/index.vue';
import { orderCommentFlag } from '../../../../s-kit/js/v3-http/https3/order/index.js';
import { mayBillingOrder } from '../../../../api/home';
import zjMarket from '../../../../s-kit/components/layout/zj-marketing/zj-marketing.vue';
import myGoodsWaterfall from '../../components/goods-waterfall/index.vue';
import {
    shoppingGoodsApi,
    shoppingAddApi,
    shoppingDeleteApi,
    pickOfTheWeekApi,
    queryStkCount,
    queryStoreInfoListByCodeList,
} from '../../../../s-kit/js/v3-http/https3/o2o/index';
import { clientCode } from '../../../../../project.config';
import addAnagerBox from '@/s-kit/components/layout/add-anager-box/index.vue';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import intMarket from '@/s-kit/components/layout/int-market/main.vue';
export default {
    mixins: [publicMixinsApi],
    components: {
        addAnagerBox,
        intMarket,
        oilChargeBillInfo,
        oilChargeBillTime,
        zjMarket,
        myGoodsWaterfall,
    },
    data() {
        return {
            //加油账单数据
            result: {},
            //订单编号
            orderNo: '',
            orderCommentFlag: 0,
            orderInfo: {},
            actualPayTotalAmount: 0,
            maskPage: false,
            o2oGoodList: [],
            o2oShoppingList: [],
            o2oShoppingNum: 0,
            o2oBusiness: 0,
            codeImg: true,
            intMarketText: false,
            intMarketPageID: '',
            marketCouponShow: false,
        };
    },
    onLoad(options) {
        this.orderInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : '';
        this.orderNo = this.orderInfo.orderNo;
        this.$nextTick(() => {
            this.resultPost();
        });
        this.evaluationAction = this.$sKit.commonUtil.throttleUtil(this.evaluationAction);
        this.openInvoice = this.$sKit.commonUtil.throttleUtil(this.openInvoice);
        // #ifdef MP-MPAAS
        this.$cnpcBridge.getSwitch('NonOilActivityRecommend', res => {
            if (res == 'yes') {
                this.intMarketText = true;
            } else {
                this.intMarketText = false;
            }
        });
        // #endif
    },
    onShow() {
        // #ifdef MP-MPAAS
        if (this.result.orderSubType == 13 && this.maskPage && this.o2oBusiness == 1) {
            this.handleo2oData();
        }
        // #endif
    },
    methods: {
        getMarketCouponShow(marketCouponShow) {
            this.marketCouponShow = marketCouponShow;
        },
        getIntMarketText(intMarketText) {
            this.intMarketText = intMarketText;
        },
        getCodeImg(codeImg) {
            this.codeImg = codeImg;
        },
        implementBiz() {
            let seed = '';
            let text = '';
            if (this.result.orderSubType == 11) {
                text = 'e享加油';
                seed = 'exoilBiz';
            } else if (this.result.orderSubType == 13) {
                text = '后支付加油';
                seed = 'hpayoilBiz';
            }
            const payMethod = this.result.payDetailList
                .map(item => {
                    const newItem = item.payMethodName;
                    return newItem;
                })
                .join(' ');
            this.$sKit.mpBP.tracker(text, {
                seed: seed,
                pageID: 'oilBillPage', // 返回sdk标识
                refer: this.orderInfo.refer,
                channelID: clientCode,
                stationCode: this.result.stationCode,
                oilNumber: this.result.productName,
                oilGun: this.result.gunNo,
                payMethod: payMethod,
                xfAmount: this.result.actualPayTotalAmount,
                yhAmount: this.result.discountTotalAmount,
                couponsAmount: this.getDiscountAmount('优惠券'),
                zkAmount: this.getDiscountAmount('支付折扣'),
                hdSellAmount: this.getDiscountAmount('活动促销'),
                address: this.cityName || '',
            });
        },
        getDiscountAmount(key) {
            let value = '';
            if (this.result.discountList.length > 0) {
                value = this.result.discountList.find(item => item.payMethodName == key)?.payAmount || '';
            }
            return value;
        },
        async handleo2oData() {
            // 获取推荐商品
            Promise.all([this.geto2oGoodList(), this.geto2oShoppingList()])
                .then(res => {
                    let o2oGoodListPro = res[0];
                    console.log(o2oGoodListPro, '将要回显数量的商品列表');
                    let o2oShoppingListPro = res[1];
                    console.log(o2oShoppingListPro, '购物车列表');
                    if (o2oGoodListPro.length > 0 && o2oShoppingListPro.length > 0) {
                        this.o2oGoodList = o2oGoodListPro.map(item => {
                            const shopItem = o2oShoppingListPro.find(shoppingItem => shoppingItem.pluId == item.pluId);
                            item.count = shopItem ? shopItem.count : '0';
                            return item;
                        });
                        console.log(o2oGoodListPro, '已回显数量的商品列表');
                    } else {
                        this.o2oGoodList = o2oGoodListPro;
                        console.log(o2oGoodListPro, '已回显数量的商品列表');
                    }
                    this.o2oShoppingList = o2oShoppingListPro;
                })
                .catch(err => {
                    console.log('走了catch', err);
                });
        },
        geto2oGoodList() {
            return new Promise(async (resolve, reject) => {
                const params = {
                    stationCode: this.result.stationCode,
                };
                const res = await pickOfTheWeekApi(params);
                if (res && res.success) {
                    let o2oGoodListPro = res.data.rows || [];
                    if (o2oGoodListPro.length > 0) {
                        o2oGoodListPro = o2oGoodListPro.map(item => {
                            const newItem = { ...item, count: '0' };
                            return newItem;
                        });
                        o2oGoodListPro = await this.geto2oStkCount(o2oGoodListPro);
                    }
                    console.log(o2oGoodListPro, '接口返回的商品列表');
                    resolve(o2oGoodListPro);
                } else {
                    reject();
                }
            });
        },
        geto2oShoppingList() {
            return new Promise(async (resolve, reject) => {
                const params = {
                    orgCode: this.result.stationCode,
                };
                const res = await shoppingGoodsApi(params);
                if (res && res.success) {
                    const o2oShoppingListPro = res.data.rows[0]?.rowGoods || [];
                    let o2oShoppingNumPro = 0;
                    for (let i = 0; i < o2oShoppingListPro.length; i++) {
                        o2oShoppingNumPro += Number(o2oShoppingListPro[i].count);
                    }
                    if (o2oShoppingNumPro <= 99) {
                        this.o2oShoppingNum = o2oShoppingNumPro;
                    } else {
                        this.o2oShoppingNum = '99+';
                    }
                    resolve(o2oShoppingListPro);
                } else {
                    reject();
                }
            });
        },
        geto2oStkCount(o2oGoodListPro) {
            return new Promise(async (resolve, reject) => {
                const pluCodes = o2oGoodListPro.map(item => item.pluCode);
                const params = {
                    orgCode: this.result.stationCode,
                    pluCodes: pluCodes,
                };
                const res = await queryStkCount(params);
                if (res && res.success) {
                    const arr = res.data;
                    let newo2oGoodListPro = JSON.parse(JSON.stringify(o2oGoodListPro));
                    console.log(newo2oGoodListPro, '商品库存回显赋值前的商品列表');
                    newo2oGoodListPro = newo2oGoodListPro.map(item => {
                        console.log('商品库存回显赋值前');
                        const countItem = arr.find(countItem => countItem.pluId == item.pluId);
                        item = { ...item, availableNum: countItem ? countItem.availableNum : 0 };
                        console.log(item, '商品库存回显赋值');
                        return item;
                    });
                    console.log(newo2oGoodListPro, '增加库存字段后的商品列表');
                    newo2oGoodListPro = await this.stockHandle(newo2oGoodListPro);
                    console.log(newo2oGoodListPro, '去掉库存不足商品后的商品列表');
                    resolve(newo2oGoodListPro);
                } else {
                    resolve(o2oGoodListPro);
                }
            });
        },
        stockHandle(o2oGoodListPro) {
            return new Promise(async (resolve, reject) => {
                let newo2oGoodListPro = JSON.parse(JSON.stringify(o2oGoodListPro));
                for (let i = newo2oGoodListPro.length - 1; i >= 0; i--) {
                    const item = newo2oGoodListPro[i];
                    const availableNum = Number(item.availableNum || 0);
                    const inventoryWarning = Number(item.inventoryWarning || 0);
                    // 是否无货(金额为0的时候 || 库存判断不通过)
                    const noPrice = !Number(item.salPrice || 0);
                    // 售罄状态库存校验逻辑
                    // 库存预警存在: 实时库存 <= 库存预警;
                    // 库存预警不存在: 实时库存 < 1;
                    const noCount = inventoryWarning ? availableNum <= inventoryWarning : availableNum < 1;
                    const isSoldOut = noPrice || noCount;
                    console.log(item, availableNum, noPrice, noCount, isSoldOut, '库存判断过程');

                    if (isSoldOut) {
                        console.log(newo2oGoodListPro[i], '因库存去掉的商品');
                        newo2oGoodListPro.splice(i, 1);
                    }
                }
                resolve(newo2oGoodListPro);
            });
        },
        async goodsCountChange(goodObj) {
            const { value, index } = goodObj;
            const item = this.o2oGoodList[index];
            if (item.count == value) return;
            const oldCount = item.count;
            this.changeItemCount(index, value);
            try {
                if (value == 0 || (item.onSaleCount && value < item.onSaleCount)) {
                    await this.shoppingDelete(item);
                    this.changeItemCount(index, 0, true);
                } else {
                    await this.shoppingAdd(item, value);
                }
            } catch (error) {
                this.changeItemCount(index, oldCount, true);
            } finally {
                this.geto2oShoppingList();
            }
        },
        changeItemCount(index, value, different = false) {
            const temp = different ? 300 : 0;
            setTimeout(() => {
                this.$nextTick(() => {
                    this.$set(this.o2oGoodList[index], 'count', value);
                    this.$refs.myGoodsWaterfall.modify(this.o2oGoodList[index].pluId, 'count', value);
                });
            }, temp);
        },
        shoppingAdd(item, count) {
            return new Promise(async (resolve, reject) => {
                const isUpdate = this.o2oShoppingList.some(goods => goods.pluId === item.pluId);
                const shopCarItem = {
                    clsCode: item.clsCode,
                    pluId: item.pluId,
                    pluName: item.pluName,
                    pluPrice: item.salPrice,
                    check: 2,
                    count: count,
                    buttonType: isUpdate ? 2 : 1, // 按钮类型 1新增、2修改
                };
                const params = {
                    orgCode: this.result.stationCode,
                    buttonType: this.o2oShoppingList.length ? 2 : 1, // 1添加、2更新
                    check: 2, // 勾选 1选中、2未选中
                    shopCarItemDtoList: [shopCarItem],
                };
                const res = await shoppingAddApi(params);
                if (res && res.success) {
                    resolve();
                } else {
                    reject();
                }
            });
        },
        shoppingDelete(item) {
            return new Promise(async (resolve, reject) => {
                let params = [];
                // id存在则删除单个商品,否则清空店铺购物车
                if (item.pluId) {
                    params.push({
                        orgCode: this.result.stationCode,
                        pluId: [item.pluId], // 商品id集合
                    });
                }
                const res = await shoppingDeleteApi({ deleteProducts: params });
                if (res && res.success) {
                    resolve();
                } else {
                    reject();
                }
            });
        },
        goodsClick(goodItem) {
            let routerParams = {
                shopId: this.result.stationCode,
                pluCode: goodItem.pluCode,
            };
            let url = `/packages/o2o-product/pages/product-detail/main?data=${encodeURIComponent(
                encodeURIComponent(JSON.stringify(routerParams)),
            )}`;
            this.$sKit.layer.cubeMini(url, '4908542685197380');
        },
        toO2o(showCart) {
            let routerParams = {
                shopId: this.result.stationCode, // 店铺id
                showCart: showCart, // 是否弹出购物车, 默认false
            };
            let url = `/packages/o2o-shop/pages/shop-home/main?data=${encodeURIComponent(
                encodeURIComponent(JSON.stringify(routerParams)),
            )}`;
            this.$sKit.layer.cubeMini(url, '4908542685197380');
        },
        // 下拉刷新
        refreshPullDown(e) {
            //  重置数据
            this.resultPost();
            setTimeout(() => {
                this.$refs.pullDownRefreshRef.stopRefresh();
                console.log('加载完成');
            }, 1000);
        },
        //查询已支付完成的订单详情
        async resultPost() {
            let res = await consumeOrderDetailApi({
                orderNo: this.orderNo,
                stationCode: this.orderInfo.stationCode,
            });
            if (res.success) {
                this.result = res.data || '';
                console.log(JSON.stringify(this.result), '订单信息');
                this.actualPayTotalAmount = Number(res.data.actualPayTotalAmount);
                if (this.result.orderSubType == 13) {
                    this.intMarketPageID = 'hpayoilBillPage';
                } else if (this.result.orderSubType == 11) {
                    this.intMarketPageID = 'exoilBillPage';
                }

                this.getOrderCommentFlag();
                // #ifdef MP-MPAAS
                if (this.result.orderSubType == 13 && !this.isHarmony) {
                    this.judgeo2o();
                }
                // #endif
                this.maskPage = true;
                this.implementBiz();
            }
        },
        async judgeo2o() {
            let params = {
                orgCodes: [this.orderInfo.stationCode],
            };
            let res = await queryStoreInfoListByCodeList(params);
            if (res.success) {
                this.o2oBusiness = res.data[0].o2oBusiness;
                if (this.o2oBusiness == 1) {
                    this.handleo2oData();
                }
            }
        },
        // 去开票
        async openInvoice() {
            let apiParams = {
                orderList: [
                    {
                        stationCode: this.result.stationCode,
                        businessDay: this.orderInfo.businessDay,
                        orderNo: this.result.orderNo,
                    },
                ],
            };
            let res = await beforeMakeInvoiceCheckApi(apiParams);
            if (res && res.success) {
                if (res.data.flag) {
                    let goods = this.result.productName;
                    let params = {
                        orderNoList: [this.result.orderNo],
                        checkAllAmount: this.result.actualPayTotalAmount,
                        type: 'invoice',
                        createTime: this.result.payItemList[0].payTime,
                        orgName: this.result.stationName,
                        goods: goods,
                        refer: 'r33',
                    };
                    this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params, 'redirectTo');
                }
            }
        },
        // 返回首页
        backMainPage() {
            // this.$sKit.layer.useRouter('/pages/thirdhome/main', '','switchTab');
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.backHomeFun();
            // #endif
        },
        // 获取订单去评价状态
        /**
         * 订单子类型：
            11—预约加油；
            13—线上后支付加油(e站加油订单)；
            23—O2O订单；
            26—异业订单(能源商城订单)；
         */
        async getOrderCommentFlag() {
            let params = {
                orderNo: this.result.orderNo,
                stationCode: this.result.stationCode,
                payAmount: this.result.actualPayTotalAmount,
                orderType: this.result.orderType,
                orderSubType: this.result.orderSubType,
                createTime: this.result.payItemList[0].payTime,
            };
            let res = await orderCommentFlag(params);
            if (res.success) {
                this.orderCommentFlag = res.data.commentFlag == '0' ? '2' : '1';
            }
        },
        // 去评价
        evaluationAction() {
            let url = '/packages/third-evaluate/pages/home/<USER>';
            let type = 'redirectTo';
            let params = {
                evaluateType: 'order',
                ...this.result,
                isOil: '1',
            };
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            cityName: state => state.locationV3_app.cityName,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
};
</script>
<style scoped lang="scss">
.oil-charge-pay-result {
    height: 100%;
    max-height: 100%;
    background-color: #f7f7fb;
    position: relative;
    padding-bottom: 150rpx;

    .content {
        margin: 0 16px;
        flex: 1;
        min-height: 0;
        overflow-y: scroll;
        overflow-x: hidden;
        // overflow: auto;

        .station-title {
            margin-top: 26rpx;
            font-weight: bold;
            font-size: 32rpx;
            color: #000000;
            line-height: 44rpx;
            display: flex;
            img {
                width: 24px;
                height: 24px;
                margin-right: 12rpx;
            }
        }

        .wxCode-box {
            margin-top: 24rpx;
        }
        .wxCode-box2 {
            margin: 24rpx 0;
        }
    }

    .mar2412 {
        margin: 24px 0 12px 0;
    }

    .o2o-card {
        .card-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .card-title-left {
                font-weight: bold;
                font-size: 32rpx;
                color: #666666;
                line-height: 45rpx;
            }
            .card-title-right {
                font-size: 26rpx;
                color: #666666;
                line-height: 37rpx;
                display: flex;
                align-items: center;
                padding-right: 8rpx;
                .arrow {
                    line-height: 37rpx;
                    width: 15rpx;
                    height: 15rpx;
                    border-top: 4rpx solid #666;
                    border-right: 4rpx solid #666;
                    transform: rotate(45deg);
                }
            }
        }
    }

    .padL110 {
        padding: 15px 15px 15px 110px;
    }

    .padL15 {
        padding: 15px 15px 15px 15px;
    }

    .bottom-area {
        display: flex;
        flex-direction: row;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 150rpx;
        bottom: 0;
        left: 0;
        right: 0;
        background: #ffffff;
        box-shadow: 0rpx -2rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
        z-index: 9;

        .shopping-cart {
            position: absolute;
            width: 100rpx;
            height: 118rpx;
            left: 0px;
            bottom: 53rpx;

            img {
                width: 100rpx;
                height: 118rpx;
            }

            .shopping-cart-dot {
                width: 42rpx;
                height: 42rpx;
                background: linear-gradient(315deg, #ff634f 0%, #ff000b 100%);
                text-align: center;
                line-height: 42rpx;
                font-size: 24rpx;
                color: #ffffff;
                border: 2rpx solid #ffffff;
                border-radius: 50% 50%;
                position: absolute;
                right: -20rpx;
                top: -20rpx;
            }
        }

        .btn {
            flex: 1;
            height: 44px;
            line-height: 44px;
            font-weight: bold;
            text-align: center;
            border-radius: 8px;
        }
    }
}
</style>
