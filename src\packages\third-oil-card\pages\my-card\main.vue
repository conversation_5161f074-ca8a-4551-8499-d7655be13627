<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" title="我的实体卡"></zj-navbar>
            <div class="f-1 mh-0 padding-16">
                <zj-pull-down-refresh
                    @refreshPullDown="refreshPullDown"
                    :showEmpty="showEmpty"
                    :emptyImage="noPicture"
                    ref="pullDownRefreshRef"
                >
                    <div>
                        <div
                            class="cardWrap fl-sh-0 mar-bom-12 border-rad-8"
                            v-for="(item, index) in thirdCardList"
                            :key="index"
                            @click="skipFuelCardManagement(item)"
                        >
                            <img v-if="!item.imgUrl" class="border-rad-8" src="../../images/oil_card_list_bg.png" alt mode="widthFix" />
                            <img v-else class="border-rad-8" :src="item.imgUrl" alt mode="widthFix" />
                            <div class="cardInfo fl-row fl-jus-bet fl-al-cen">
                                <div class="cardNum font-16 weight-bold color-fff">
                                    <!-- <div>卡号:</div> -->
                                    <div class="cardNumNo">{{ item.cardNo }}</div>
                                </div>
                                <div class="address font-12 weight-400 color-fff">{{ item.address || '' }}</div>
                            </div>
                        </div>
                    </div>
                    <div @click="addCard()" class="addCard primary-btn fl-row color-fff fl-al-jus-cen border-rad-8" :class="{ gray: gray }">
                        <div>绑定实体卡</div>
                    </div>
                    <!-- #ifndef H5-CLOUD -->
                    <div @click="transferOutFund()" class="addCard primary-btn fl-row color-fff fl-al-jus-cen border-rad-8" :class="{ gray: false }">
                        <div>未绑定加油卡资金归集</div>
                    </div>
                    <!-- #endif -->
                </zj-pull-down-refresh>
            </div>
            <zj-show-modal>
                <fundCollectionRemind ref="fundCollectionRemind"></fundCollectionRemind>
            </zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import { clientCode } from '../../../../../project.config';
import { cardList } from '../../../../s-kit/js/v3-http/https3/oilCard/index.js';
import fundCollectionRemind from '../../components/fundCollectionRemind/main.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    // components: {},
    // 我的加油卡
    name: 'my-card',
    data() {
        return {
            // 3.0油卡列表
            thirdCardList: [],
            noPicture: require('../../images/kt7yk.png'),
            // 列表没数据时变为true显示暂无数据图片
            showEmpty: false,
            // 绑卡上限张数
            limit: '',
            // 用户名下卡的数量
            cardAmount: '',
            // uni.navigateBack 需要的参数
            prveDataObject: {
                refreshListFlag: false,
            },
            qKey: '',
            refer: '',
        };
    },
    onShow() {
        // 读取本地缓存的数据，当value为true时，用来调用刷新卡列表
        const value = uni.getStorageSync('refreshCardListFlag');
        // 移除本地缓存
        uni.removeStorageSync('refreshCardListFlag');
        // const value = uni.getStorage('refreshCardListFlag')
        // uni.removeStorage("refreshCardListFlag")
        if (value) {
            this.getThirdCardList();
        }
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let personalObj = JSON.parse(decodeURIComponent(options.data));
            // #ifdef MP-ALIPAY
            my.hideBackHome();
            if (personalObj.qKey) {
                this.qKey = personalObj.qKey;
            }
            // #endif
            if (personalObj.refer) {
                this.refer = personalObj.refer;
            }
            if (personalObj.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'cardListPage',
                    refer: personalObj.myRefer,
                    channelID: clientCode,
                });
            }
        }
        this.$sKit.mpBP.tracker('绑定实体卡', {
            seed: 'bindCardBiz',
            pageID: 'cardListPage',
            refer: this.refer,
            channelID: clientCode,
        });
    },
    components: {
        fundCollectionRemind,
    },
    mounted() {
        // 获取3.0油卡
        this.getThirdCardList();
    },
    methods: {
        /**
         * @description  : 跳转到转出到昆仑e享卡列表页面
         * @return        {*}
         */
        transferOutFund() {
            let _this = this;
            this.$store.dispatch('zjShowModal', {
                confirmText: '确定',
                cancelText: '取消',
                confirmColor: '#E64F22',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let url = '/packages/third-oil-card/pages/oil-card-cashsweep/main';
                        let params = {};
                        let type = 'navigateTo';
                        _this.$sKit.layer.useRouter(url, params, type);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        /**
         * @description  : 获取油卡列表
         * @param         {Boolean} isInit:是否刷新数据
         * @return        {*}
         */
        getThirdCardList({ isInit = false } = {}) {
            // #ifdef MP-ALIPAY
            // 是否是车生活跳转过来
            if (this.qKey == 'oilCardList') {
                // 功能限制
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        cardList().then((res, err) => {
                            if (isInit)
                                // 关闭下拉刷新
                                this.$refs.pullDownRefreshRef.stopRefresh();
                            if (res && res.success) {
                                // 绑卡上限张数
                                this.limit = res.data.limit;
                                // 用户名下卡的数量
                                this.cardAmount = res.data.cardAmount;
                                // 3.0油卡列表
                                this.thirdCardList = res.data.rows;
                            }
                        });
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.activate = 'WDYK'; //返回充值
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    freeze: 0,
                    walletAddParams: {
                        refer: 'r16',
                    },
                });
                return;
            }
            // #endif
            cardList().then((res, err) => {
                if (isInit)
                    // 关闭下拉刷新
                    this.$refs.pullDownRefreshRef.stopRefresh();
                if (res && res.success) {
                    // 绑卡上限张数
                    this.limit = res.data.limit;
                    // 用户名下卡的数量
                    this.cardAmount = res.data.cardAmount;
                    // 3.0油卡列表
                    this.thirdCardList = res.data.rows;
                }
            });
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        async refreshPullDown() {
            this.getThirdCardList({ isInit: true });
        },
        /**
         * @description  : 添加油卡
         * @return        {*}
         */
        addCard() {
            // #ifdef MP-ALIPAY
            // 是否是车生活跳转过来
            if (this.qKey == 'oilCardList') {
                // 功能限制
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        // 如果当前卡列表长度 >= 绑卡上线张数时 禁止点击 按钮置灰
                        if (this.thirdCardList.length >= this.limit) {
                            uni.showToast({
                                title: '绑卡数量已达上限，可解绑后再操作',
                                icon: 'none',
                                duration: 2000,
                            });
                            return;
                        } else {
                            // 如果绑卡张数大于5张时跳转至填写绑卡信息页面
                            let url = '/packages/third-oil-card/pages/oil-add-card/main';
                            let params = { refer: this.refer };
                            let type = 'navigateTo';
                            this.$sKit.layer.useRouter(url, params, type);
                        }
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: res => {
                        this.activate = 'WDYK'; //返回充值
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    freeze: 0,
                    walletAddParams: {
                        refer: 'r16',
                    },
                });
                return;
            }
            // #endif
            // 如果当前卡列表长度 >= 绑卡上线张数时 禁止点击 按钮置灰
            if (this.thirdCardList.length >= this.limit) {
                uni.showToast({
                    title: '绑卡数量已达上限，可解绑后再操作',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else {
                // 如果绑卡张数大于5张时跳转至填写绑卡信息页面
                let url = '/packages/third-oil-card/pages/oil-add-card/main';
                let params = { refer: this.refer };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        /**
         * @description  : 跳转油卡管理
         * @param         {Object} item:当前卡的数据
         * @return        {*}
         */
        skipFuelCardManagement(item) {
            console.log(item, '获取油卡item');
            let url = '/packages/third-oil-card/pages/oil-card-management/main';
            let params = item;
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    computed: {
        /**
         * @description  : 达到绑定上限时是否置灰绑定按钮
         * @return        {*}
         */
        gray() {
            return this.thirdCardList.length >= this.limit && this.thirdCardList.length !== 0;
        },
        ...mapState({}),
    },
    beforeDestroy() {
        /**
         * @description  : 清空本地缓存的数据
         * @return        {*}
         */
        uni.removeStorage('refreshCardListFlag');
    },
};
</script>
<style scoped lang="scss">
.view {
    .cardWrap {
        // height: 100%;
        position: relative;
        // height: 210px;

        img {
            // position: absolute;
            width: 100%;
            // height: 100%;
            // height: 210px;
            // height: 320rpx;
        }

        .cardInfo {
            width: 100%;
            height: 44px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0px 0px 16px 16px;
            backdrop-filter: blur(10px);
            position: absolute;
            bottom: 0;

            .cardNum {
                margin-left: 15px;
                display: flex;

                .cardNumNo {
                    // margin-top: 4px;
                    height: 17px;
                }
            }

            .address {
                // height: 17px;
                margin-right: 15px;
                max-width: 270rpx;
                // margin-top: 3px;
            }
        }
    }

    .addCard {
        height: 44px;
        line-height: 44px;
        margin-bottom: 12px;
        width: 100%;
    }

    .gray {
        opacity: 0.3;
    }
}
</style>
