<template>
    <div>
        <!--自定义地址选择器-->
        <div class="cc_area_mask" v-if="show == true"></div>
        <div :class="'cc_area_view ' + (show ? 'show' : 'hide')">
            <div class="cc_area_view_btns">
                <text class="cc_area_view_btn_cancle font_color" @tap="handleNYZAreaCancle">取消</text>
                <text>选择常用地</text>
                <text class="cc_area_view_btn_sure font_color" @tap="handleNYZAreaSelect" :data-province="province" :data-city="city"
                    >确定</text
                >
            </div>
            <picker-view class="cc_area_pick_view" @change="handleNYZAreaChange" :value="value">
                <picker-view-column>
                    <view v-for="(item, index) in provinces" :key="index" class="cc_area_colum_view">{{ item }}</view>
                </picker-view-column>
                <picker-view-column>
                    <view v-for="(item, index) in citys" :key="index" class="cc_area_colum_view">{{ item }}</view>
                </picker-view-column>
                <!-- <picker-view-column>
          <div v-for="(item, index) in areas" :key="index" class="cc_area_colum_view">{{item}}</div>
        </picker-view-column>-->
            </picker-view>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            provinces: [],
            citys: [],
            // areas: getAreas(index[0], index[1]),
            value: [0, 0, 0],
            AreaJson: [],
            index: [0, 0, 0],
            // 是否滑动
            slidingOrNotFlag: false,
            // echoCityIndex: 0,
            // echoProvinceIndex: 0
            provincesCodeList: [],
            citysCodeList: [],
        };
    },

    components: {},
    props: {
        // 省
        province: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },
        // 市
        city: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },
        // 区
        area: {
            //控制area_select显示隐藏
            type: String,
            default: '',
        },

        show: {
            //控制area_select显示隐藏
            type: Boolean,
            default: false,
        },
        maskShow: {
            //是否显示蒙层
            type: Boolean,
            default: true,
        },
        // 省市数组
        provinceCityArray: {
            type: Array,
            default: [],
        },
        // 常用地数据是否存在
        commonlyUsed: {
            type: Object,
            default: {},
        },
    },
    async mounted() {
        this.AreaJson = this.provinceCityArray;
        this.getProvinces();
        // this.getMyCity()
        this.getMyCity(this.index[0], this.index[1]);
    },
    methods: {
        handleNYZAreaChange: function (e) {
            this.slidingOrNotFlag = true;
            var that = this;
            console.log('e:' + JSON.stringify(e));
            var value = e.detail.value;
            /**
             * 滚动的是省
             * 省改变 市、区都不变
             */
            if (this.index[0] != value[0]) {
                this.index = [value[0], 0, 0];
                let selectCitys = this.getMyCity(this.index[0]);
                // console.log(selectCitys, 'selectCitys');
                // let selectAreas = getAreas(this.index[0], 0);
                that.selectData({
                    citys: selectCitys,
                    // areas: selectAreas,
                    value: [this.index[0], 0, 0],
                    province: this.provinces[this.index[0]],
                    city: selectCitys[0],
                    // area: selectAreas[0]
                });
                return;
            } else if (this.index[1] != value[1]) {
                /**
                 * 市改变了 省不变 区变
                 */
                this.index = [value[0], value[1], 0];
                let selectCitys = this.getMyCity(this.index[0]);
                // let selectAreas = getAreas(this.index[0], value[1]);
                that.selectData({
                    citys: selectCitys,
                    // areas: selectAreas,
                    value: [this.index[0], this.index[1], 0],
                    province: this.provinces[this.index[0]],
                    city: selectCitys[this.index[1]],
                    // area: selectAreas[0]
                });
            } else if (this.index[2] != value[2]) {
                /**
                 * 区改变了
                 */
                this.index = [value[0], value[1], value[2]];
                let selectCitys = this.getMyCity(this.index[0]);
                // let selectAreas = getAreas(this.index[0], value[1]);
                that.selectData({
                    citys: selectCitys,
                    // areas: selectAreas,
                    value: [this.index[0], this.index[1], this.index[2]],
                    province: this.provinces[this.index[0]],
                    city: selectCitys[this.index[1]],
                    // area: selectAreas[index[2]]
                });
            }
        },

        /**
         * 确定按钮的点击事件
         */
        handleNYZAreaSelect: function (e) {
            console.log('eeeeeee', e);
            let detail = {};
            if (this.slidingOrNotFlag) {
                detail = e.target.dataset;
            } else {
                detail = {
                    province: this.provinces[0],
                    city: this.citys[0],
                    provinceCode: this.provincesCodeList[0],
                    cityCode: this.citysCodeList[0],
                };
            }
            this.provinces.map((item, index) => {
                if (item === detail.province) {
                    console.log(item, index, '省市名字');
                    detail.provincesCode = this.citysCodeList[index];
                }
            });
            this.citys.map((item, index) => {
                if (item === detail.city) {
                    console.log(item, index, '市区名字');
                    detail.cityCode = this.citysCodeList[index];
                }
            });
            this.$emit('sureSelectArea', { detail: detail });
            console.log(detail, 'detail');
            this.index = [0, 0, 0];
            this.value = [0, 0, 0];
            this.getMyCity(this.index[0], this.index[1]);
        },

        /**
         * 取消按钮的点击事件
         */
        handleNYZAreaCancle: function (e) {
            this.$emit('hideShow');
            // 复原初始状态
            this.index = [0, 0, 0];
            this.value = [0, 0, 0];
            this.getMyCity(this.index[0], this.index[1]);
        },
        selectData: function (obj, callback) {
            let that = this;
            const handleData = (tepData, tepKey, afterKey) => {
                tepKey = tepKey.split('.');
                tepKey.forEach(item => {
                    if (tepData[item] === null || tepData[item] === undefined) {
                        let reg = /^[0-9]+$/;
                        tepData[item] = reg.test(afterKey) ? [] : {};
                        tepData = tepData[item];
                    } else {
                        tepData = tepData[item];
                    }
                });
                return tepData;
            };
            const isFn = function (value) {
                return typeof value == 'function' || false;
            };
            Object.keys(obj).forEach(function (key) {
                let val = obj[key];
                key = key.replace(/\]/g, '').replace(/\[/g, '.');
                let front, after;
                let index_after = key.lastIndexOf('.');
                if (index_after != -1) {
                    after = key.slice(index_after + 1);
                    front = handleData(that, key.slice(0, index_after), after);
                } else {
                    after = key;
                    front = that;
                }
                if (front.$data && front.$data[after] === undefined) {
                    Object.defineProperty(front, after, {
                        get() {
                            return front.$data[after];
                        },
                        set(newValue) {
                            front.$data[after] = newValue;
                            console.log(newValue, 'newValue');
                            that.$forceUpdate();
                        },
                        enumerable: true,
                        configurable: true,
                    });
                    front[after] = val;
                } else {
                    that.$set(front, after, val);
                }
            });
            isFn(callback) && this.$nextTick(callback);
        },
        getProvinces() {
            this.provinces = [];
            for (var i = 0; i < this.AreaJson.length; i++) {
                this.provinces.push(this.AreaJson[i].name);
                this.provincesCodeList.push(this.AreaJson[i].name);
            }
            // console.log(this.provincesCodeList, 'this.provincesCodeList');
            // console.log(this.provinces, 'this.provinces');
            return this.provinces;
        },
        /**
         * 获取省对应的所有城市
         */
        getMyCity(provinceIndex) {
            console.log();
            this.citys = [];
            this.citysCodeList = [];
            for (var i = 0; i < this.AreaJson[provinceIndex].city.length; i++) {
                if (['北京市', '上海市', '重庆市', '天津市'].includes(this.AreaJson[provinceIndex].city[i].areaName)) {
                    console.log(...this.AreaJson[provinceIndex].city[i].area, '...this.AreaJson[provinceIndex].city[i].area');
                    // this.citys.push(...this.AreaJson[provinceIndex].city[i].area);
                    // this.citysCodeList.push()
                    // console.log(this.citys, 'this.citys=====直辖市');
                } else {
                    this.citys.push(this.AreaJson[provinceIndex].city[i].areaName);
                    this.citysCodeList.push(this.AreaJson[provinceIndex].city[i].areaCode);
                    // console.log(this.citysCodeList, 'this.citysCodeList');
                }
            }
            // console.log(this.citys, 'this.citys');
            return this.citys;
        },
    },
};
</script>
<style scoped lang="scss">
.cc_area_view {
    width: 100%;
    position: fixed;
    bottom: -1000px;
    left: 0px;
    background-color: #fff;
    z-index: 981;
    transition: all 0.3s;
}

.cc_area_pick_view {
    height: 200px !important;
    width: 100%;
}

.cc_area_colum_view {
    line-height: 35px !important;
    text-align: center;
    font-size: 28upx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.hide {
    bottom: -1000upx;
    transition: all 0.3s;
}

.show {
    bottom: 0upx;
    transition: all 0.3s;
}

.cc_area_view_btns {
    background-color: #fff;
    border-bottom: 1px solid #eeeeee;
    font-size: 30upx;
    padding: 18upx 0upx;
    display: flex;
    justify-content: space-between;
}

.cc_area_view_btns > text {
    // display: inline-block;
    // word-spacing: 4upx;
    // letter-spacing: 4upx;
}

.cc_area_view_btn_cancle {
    color: #939393;
    padding-right: 20upx;
    padding-left: 25upx;
}

.cc_area_view_btn_sure {
    float: right;
    padding-left: 20upx;
    padding-right: 25upx;
}

.cc_area_mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(28, 28, 28, 0.6);
    position: absolute;
    top: 0upx;
    left: 0upx;
    z-index: 980;
}
.font_color {
    color: #e64f22;
}
</style>
