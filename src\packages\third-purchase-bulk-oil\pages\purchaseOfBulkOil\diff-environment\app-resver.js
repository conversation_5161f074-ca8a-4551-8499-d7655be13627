import checkFKArgs from '../../../../../s-kit/js//v3-native-jsapi/checkFKArgs';
export default {
    mounted() {},
    methods: {
        initiatePaymentAppAndHs(params = {}, isAuth) {
            return new Promise(async (resolve, reject) => {
                // APP
                if (!this.isHarmony) {
                    params.payType = '5';
                    params.extendFiled = JSON.stringify(await checkFKArgs.getFKArgs('sdk', isAuth));
                    let obj = await this.$sKit.commonUtil.biometricPay(params.preAuthzOrderNo, params.preAuthzAmount, !isAuth);
                    Object.assign(params, obj);
                    console.log('成功', Object.assign(params, obj));
                    this.$paymentCenter.qryPreOrder({ paramsJsonStr: encodeURIComponent(JSON.stringify(params)) }, res => {
                        resolve(res);
                    });
                } else {
                    // 鸿蒙
                    params.extendFiled = JSON.stringify({
                        dfp: '',
                        gps:
                            this.riskManagementLonV3 && this.riskManagementLatV3
                                ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                                : '',
                        gpsProvince: '',
                        gpsCity: '',
                        gpsArea: '',
                    });
                    params.lockAll = 0;
                    await this.$sKit.mpaasPayPlugin.QryPreOrder(params, this.accountDataPlugin).then(res => {
                        resolve(res);
                    });
                }
            });
        },
    },
};
