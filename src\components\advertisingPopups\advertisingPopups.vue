<template>
    <div class="advertisingPopups" v-if="visible">
        <div class="full-div">
            <img class="ad-img-class" :src="advertisData.image" mode="widthFix" @click="enterNav()" />
            <div class="close-tbn-3" @click="close">
                <img src="../../static/X.png" />
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { mobilePhoneNumberEncryption, openidEncryption } from '@/api/home.js';
export default {
    name: 'advertisingPopups',
    components: {},
    props: {},
    data() {
        return {
            visible: true,
        };
    },
    onLoad() {
        console.log(this.advertisData, 'advertisData');
    },
    computed: {
        ...mapState({
            advertisData: state => state.location.advertisData,
        }),
        ...mapGetters(['registerLoginInformation']),
    },
    methods: {
        close() {
            this.$emit('closeEvent');
        },
        async enterNav() {
            if (this.advertisData.path === '') {
                return;
            }
            // // 点击广告图跳转h5或者其他小程序时 关闭广告  在跳出去的时候让它消失，然后再跳转回来的时候不在展示广告图
            // this.$store.state.location.advertisementFlag = 0
            this.visible = false;
            // this.$store.commit('setAdvertisFlag', false)
            this.$store.commit('setAdvertisFlag', false);
            // 外部链接
            if (this.advertisData.topNavType == '2') {
                if (this.advertisData.path.includes('openId')) {
                    let openIdRes = await openidEncryption();
                    if (openIdRes.status === 0) {
                        uni.navigateTo({
                            url: `/packages/web-view/pages/home/<USER>
                                this.advertisData.path.indexOf('?') != -1
                                    ? `${this.advertisData.path}&miniProgramOpenId=${openIdRes.data}`
                                    : `${this.advertisData.path}?miniProgramOpenId=${openIdRes.data}`,
                            )}`,
                        });
                    }
                } else {
                    let res = await mobilePhoneNumberEncryption({
                        phone: this.registerLoginInformation.phone,
                    });
                    // 如果返回的是成功的响应
                    if (res.status === 0) {
                        // 跳转外部网页
                        uni.navigateTo({
                            url: `/packages/web-view/pages/home/<USER>
                                this.advertisData.path.indexOf('?') != -1
                                    ? `${this.advertisData.path}&phone=${res.data}`
                                    : `${this.advertisData.path}?phone=${res.data}`,
                            )}`,
                        });
                    }
                }

                // 小程序
            } else if (this.advertisData.topNavType == '3') {
                // 跳转小程序
                wx.navigateToMiniProgram({
                    appId: this.advertisData.appId,
                    // appId: 'wx05a52e68e325221d',
                    path: this.advertisData.path,
                    envVersion: 'release', // 体验版    envVersion: 'develop', //开发版    envVersion: 'release',     //正式版
                });
            } else if (this.advertisData.topNavType == '1') {
                uni.navigateTo({
                    url: this.advertisData.path,
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.advertisingPopups {
    position: fixed;
    bottom: 0;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.6);
    .full-div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column;
        .ad-img-class {
            object-fit: contain;
            // width: auto;
            // height: auto;
            max-width: 70%;
        }
        .close-tbn-3 {
            position: relative;
            z-index: 10;
            margin-top: 30px;
            height: 0px;
            img {
                display: block;
                width: 25px;
                height: 25px;
                border: 1px solid white;
                border-radius: 50%;
            }
        }
    }
}
</style>
