<template>
    <div class="version-list">
        <u-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :back-icon-size="40"
            :height="44"
            back-text="功能介绍"
            :back-text-style="pageConfig.titleStyle"
        >
        </u-navbar>
        <div class="version-list-content">
            <scroll-view
                class="version-list-scroll"
                :scroll-y="true"
                :enhanced="true"
                :show-scrollbar="false"
                @scrolltolower="handleGetNextPage"
            >
                <div @click="handleOpenDetailsPage(item)" v-for="(item, index) in versionList" :key="index" class="version-item">
                    <div class="version-info">
                        <span class="version-no version-text">{{ item.title }}</span>
                        <span class="version-date">{{ item.publishDate }}</span>
                    </div>
                    <img src="@/static/homeIcon/rightjt.png" class="version-arrow" alt />
                </div>
            </scroll-view>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { getVersionList } from '@/api/home.js';
import pageConfig from '@/utils/pageConfig.js';
export default {
    data() {
        return {
            pageConfig, // 页面配置
            versionList: [],
        };
    },
    onShow() {
        this.handleReLoadVersionList();
    },
    methods: {
        /**
         * 获取版本列表
         */
        handleGetVersionList() {
            return getVersionList(this._pageParams).then(res => {
                if (res.status == 0) {
                    const { data = [], totalRecords, totalPages } = res;
                    this.versionList = data;
                    this.totalRecords = totalRecords;
                    this._totalPages = totalPages;
                }
            });
        },
        /**
         * 重新加载版本列表
         * 初始化分页配置
         */
        handleReLoadVersionList() {
            this._pageParams = {
                pageNo: 1,
                pageSize: 5,
            };
            this._totalPages = 0;
            this.handleGetVersionList();
        },
        /**
         * 获取下一页
         */
        handleGetNextPage() {
            if (this._pageParams.pageNo < this._totalPages) {
                this._pageParams.pageNo++;
                this.handleGetVersionList();
            }
        },
        /**
         * 跳转到详情页
         */
        handleOpenDetailsPage(item) {
            uni.navigateTo({
                url: `/packages/version/pages/version-details/main?content=${encodeURIComponent(item.content)}`,
            });
        },
    },
    filters: {
        filterDate(val = '') {
            const [, month, day] = val.split('-');
            return `${month}月${day}日`;
        },
    },
};
</script>

<style></style>
<style lang="scss" scoped>
page {
    background: #f9f9f9;
}
.version-list {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    .version-list-content {
        flex: 1;
        min-height: 0;
        background: #f9f9f9;
        overflow: scroll;
        .version-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 77px;
            background: #fff;
            padding: 0 15px;
            border-radius: 5px;
            margin: 13px 14px;
            line-height: 1;
            .version-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                min-width: 0;
                white-space: nowrap;
                margin-right: 20px;
                .version-no {
                    font-size: 16px;
                    color: #2d2d2d;
                }
                .version-date {
                    margin-top: 5px;
                    color: #a3a3a3;
                    font-size: 14px;
                }
                .version-text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .version-arrow {
                width: 5px;
                height: 9px;
            }
        }
    }
}
</style>
