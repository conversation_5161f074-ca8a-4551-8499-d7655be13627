<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="查看登录设备" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <zj-data-list ref="dataList" @refreshPullDown="refreshPullDown" @scrolltolower="scrolltolower" :showEmpty="showEmpty">
                    <div class="card-default mar16">
                        <div
                            class="fl-row fl-al-cen padd12 line_bottom marlr12"
                            @click="headersClick('header')"
                            v-for="(item, index) of productList"
                            :key="index"
                        >
                            <div class="f-1 font-14 weight-400 color-333"
                                >{{ item.loginDeviceName ? uncodeUtf16(item.loginDeviceName) : '未知设备' }}
                            </div>
                            <div class="fl-column fl-al-cen te-right">
                                <div class="font-12 weight-400 color-333 marb6">{{ item.loginLocation || '' }}</div>
                                <div class="font-12 weight-400 color-333">{{ item.loginTime }}</div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { queryLoginDevicesApi,queryLoginRecordApi } from '../../../../s-kit/js/v3-http/https3/user.js';
import Vue from 'vue';
const PAGE_SIZE = 20;
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            productList: [],
            // 页码
            page: 1,
            // 加载状态 // 0 不显示 1 显示加载动画 2 显示没有更多
            loadState: 0,
            showEmpty: false,
        };
    },
    onLoad(option) {
        this.$nextTick(() => {
            // 查看登录设备
            this.getGoodsListFun({ isInit: true });
        });
    },
    onShow() {},
    methods: {
        // 将Unicode码点转换为字符。在处理9位编码时，将高位和低位分别转换为字符，并拼接起来返回。
        uncodeUtf16(str) {
            var reg = /\&#.*?;/g;
            var result = str.replace(reg, function (char) {
                var H, L, code;
                if (char.length == 9) {
                    code = parseInt(char.match(/[0-9]+/g));
                    H = Math.floor((code - 0x10000) / 0x400) + 0xd800;
                    L = ((code - 0x10000) % 0x400) + 0xdc00;
                    return unescape('%u' + H.toString(16) + '%u' + L.toString(16));
                } else {
                    return char;
                }
            });
            return result;
        },
        // uncodeUtf16(str) {
        //     var reg = /\&#.*?;/g;
        //     var result = str.replace(reg, function (char) {
        //         var H, L, code;
        //         if (char.length == 9) {
        //             code = parseInt(char.match(/[0-9]+/g));
        //             H = Math.floor((code - 0x10000) / 0x400) + 0xd800;
        //             L = ((code - 0x10000) % 0x400) + 0xdc00;
        //             return String.fromCharCode(H) + String.fromCharCode(L);
        //         } else {
        //             return char;
        //         }
        //     });
        //     return result;
        // },
        // 查看登录设备
        async getGoodsListFun({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    productList: [],
                    page: 1,
                });
                // 重置入参页码
            }
            this.$refs.dataList.loadStatus = 'loading';
            let { page, productList } = this;
            let params = {
                current: page,
                size: PAGE_SIZE,
                // loginChannel: 2,
            };
            // let res = await queryLoginDevicesApi(params);
            let res = await queryLoginRecordApi(params);
            if (res && res.success) {
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];
                productList = productList.concat(list);
                if (res.data && page >= res.data.totalPage) {
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                Object.assign(this, {
                    productList,
                    page: Number(page) + 1,
                });
            }
            this.showEmpty = this.productList.length <= 0 ? true : false;
        },
        // 上拉加载
        scrolltolower() {
            if (this.$refs.dataList.loadStatus !== 'nomore') {
                this.$refs.dataList.loadStatus = 'loading';
                this.page + 1;
                this.getGoodsListFun();
            }
        },
        // 下拉刷新
        refreshPullDown(e) {
            this.page = 1;
            this.getGoodsListFun({ isInit: true });
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.mar16 {
    margin: 16px;
}

.marlr12 {
    margin: 0 12px;
}

.padd12 {
    padding: 12px 0;
}

.marb6 {
    margin-bottom: 6px;
}
</style>
