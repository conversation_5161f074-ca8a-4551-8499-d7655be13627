import { calculateOrdeDiscountsApi } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import post from '../../../../../s-kit/js/v3-http/post';
import { mapGetters, mapState } from 'vuex';
export default {
    data() {
        return {
            // 支付插件参数
            resData: {},
            // 支付参数
            payParams: {},
            // 微信加券二次支付 payMethod == 11的数据
            order: null,
            wxSecondaryPaymentObj: {},
        };
    },

    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },

    async mounted() {
        let result = await this.$sKit.keyBordPlugin.initRef();
        this.$store.commit('setAccountDataPlugin', result);
    },
    methods: {
        //优惠券
        calculateOrdeDiscountsPost(selectTicketsInfo) {
            // let dataParams = JSON.parse(JSON.stringify(this.unPaidInfo));
            let newProductList = this.unPaidInfo.productList;
            let newSelectTicketsInfo = JSON.parse(JSON.stringify(selectTicketsInfo));
            newProductList.forEach(item => {
                if (!item.productType) {
                    delete item.productType;
                }
                item.activityDiscountList.forEach(acItem => {
                    if (!acItem.groupId) {
                        delete acItem.groupId;
                    }
                });
            });
            console.log('newProductList----', newProductList);
            const params = {
                stationCode: this.unPaidInfo.stationCode,
                // stationCode: '1-A4201-C001-S001',
                orderNo: this.unPaidInfo.orderNo,
                channelType: this.unPaidInfo.channelType,
                orderMoney: this.unPaidInfo.orderMoney,
                productInOrderVoList: newProductList,
                businessDay: this.businessDay,
                usedCouponList: newSelectTicketsInfo,
            };
            calculateOrdeDiscountsApi(params)
                .then(res => {
                    if (res.success) {
                        console.log('OrdeDiscounts---', res.data);
                        this.unPaidInfo = { ...res.data, cancel: this.unPaidInfo?.cancel };
                        this.calculateGiftCard();
                    }
                    this.isCanClickPay = true;
                })
                .catch(err => {
                    this.isCanClickPay = true;
                });
        },
        /**
         * 消费收银台
         * @param stationCode 站编码
         */
        getBuyPayTypeList() {
            let params = {
                // stationCode: '1-A4201-C001-S001',
                stationCode: this.unPaidInfo.stationCode,
            };
            uni.showLoading({
                title: '加载中',
                mask: true,
            });
            console.log('params-getBuyPayTypeList', params);
            console.log('params-this.payPlugin', this.payPlugin);
            this.$nextTick(async () => {
                const res = await this.payPlugin.GetBuyTypeList(params);
                uni.hideLoading();
                if (res.code === 'PAY_SUCCESS') {
                    // this.$store.commit('mSetRefuelingMigrationFlag', false)
                    this.payList = this.isHarmony ? res.data.filter(item => item.payType == 2) : res.data;
                    console.log('支付方式', this.payList);
                    if (this.payList && this.payList.length > 0) {
                        this.curPayChannelInfo = this.payList[0];
                    }
                } else {
                    this.$store.dispatch('zjShowModal', {
                        title: res.msg,
                        content: `${res.code}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
    
                console.log('payList---', JSON.stringify(res));
                
            });
        },
        //         String stationCode 是 站编码
        //         String bizOrderNo 是 业务订单编号
        //         String rcvAmt 是 应收总金额
        //         String realAmt 是 支付金额
        //         String payType 是 支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；
        //         String bizDay 否 营业日
        //         String extendFiled 是 风控字段（json字符串透传，不校验里面内容）
        //         Array insidePayList 是 账户预支付场景：内部支付方式列表(组合支付)
        //         String accountNo 否 账户编号，电子账户类型支付时必传
        // 拉起收银台进行支付
        async sdkRealPay() {
            // this.isOrderShow = true;
            // uni.showLoading({
            //     title: '支付中',
            //     mask: true,
            // });
            this.payParams = {
                stationCode: this.unPaidInfo.stationCode,
                // stationCode: '1-A4201-C001-S001',
                bizOrderNo: this.unPaidInfo.orderNo,
                rcvAmt: this.unPaidInfo.orderMoney,
                // realAmt: this.order ? Number(this.order.rcvAmt) : this.unPaidInfo.payMoney,
                realAmt: this.orderPayMoney + '',
                payType: this.curPayChannelInfo.payType + '',
                bizDay: this.businessDay,
                // 3.0.4风控字段
                extendFiled: await post.addExtendFiled('plugin'),
                insidePayList: [],
                lockAll: 0,
            };
            if (this.selectCard.length > 0 && this.orderPayMoney == 0) {
                this.payParams.payType = 5;
                // this.curPayChannelInfo.payType = 5
                let item = this.payList.find(item => item.payType == 5);
                if (item) this.curPayChannelInfo = item;
            }
            if (this.curPayChannelInfo.payType == '6') {
                this.payParams.cardIdx = this.curCardPayInfo.cardSequence + '';
                this.payParams.fuelCardNo = this.curCardPayInfo.fuelCardNo;
            } else if (this.curPayChannelInfo.payType == '5') {
                this.payParams.cardIdx = '1';
                this.payParams.accountNo = this.walletInfo.ewalletNo;
            }
            if (JSON.stringify(this.wxSecondaryPaymentObj) !== '{}') {
                // this.payParams.insidePayList.push({
                //   payType: '11',
                //   payAmt: this.wxSecondaryPaymentObj.payAmount + '',
                //   // 已经核销过得券再次支付时不用携带
                //   // couponNo: this.wxSecondaryPaymentObj.couponNo,
                //   couponTemplateNo: this.wxSecondaryPaymentObj.couponTemplateNo,
                // });
            } else {
                if (this.unPaidInfo.couponList.length > 0) {
                    for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                        if (this.unPaidInfo.couponList[i].used == 1) {
                            this.payParams.insidePayList.push({
                                payType: '11',
                                payAmt: this.unPaidInfo.couponList[i].couponDiscount + '',
                                couponNo: this.unPaidInfo.couponList[i].couponNo,
                                couponTemplateNo: this.unPaidInfo.couponList[i].couponTemplateNo,
                            });
                        }
                    }
                }
            }
            this.payParams.insidePayList.push(...(await this.calculateGiftCardPay()));
            console.log('油卡或电子账户支付参数', this.payParams);
            console.log('SDK支付方式', this.curPayChannelInfo.payType);
            try {
                let accountDataPlugin = this.keyBoardRef;
                console.log('accountDataPlugin--', accountDataPlugin);
                this.isCanClickPay = true;
                this.resData = await this.payPlugin.Buy(JSON.parse(JSON.stringify(this.payParams)), this.keyBoardRef);
                console.log('zfb---res---成功', this.resData);
                // if (this.curPayChannelInfo.payType == '1') {
                //     // this.wxPay();
                //     return;
                // }

                if (this.resData.code != 'PAY_SUCCESS') {
                    // if (this.resData.code != '0') {
                    this.getPayChannelInfo();
                    this.isOrderShow = false;
                    uni.hideLoading();
                    // 截取字符串后面的数据
                    let errIndex = this.resData.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = this.resData.msg.slice(0, errIndex);
                        customErr = this.resData.msg.slice(errIndex + 1, this.resData.msg.length);
                    } else {
                        customErr = this.resData.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                } else if (this.resData.code === 'PAY_ERROR_003') {
                    // need risk
                    this.isCanClickPay = true;
                    this.isPaying = false;
                    //需要实人认证
                    const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (riskRes == 13) {
                        // 打开实人认证的表单弹窗
                        this.realNameDialogFlag = true;
                    }
                }else {
                    this.beginTime = new Date().getTime();
                    this.$sKit.layer.useRouter(
                        '/packages/third-oil-charge-payment/pages/query-payment-results/main',
                        this.unPaidInfo,
                        'redirectTo',
                    );
                    // this.isOrderShow = false;
                    // uni.redirectTo({
                    //     url: `/packages/third-oil-charge-payment/pages/query-payment-results/main?data=${JSON.stringify(this.unPaidInfo)}`,
                    // });
                    // uni.showLoading({
                    //     title: '加载中',
                    //     mask: true,
                    // });
                    // setTimeout(() => {
                    //     this.statuslOrderPost();
                    // }, 1000);
                }
            } catch (error) {
                uni.hideLoading()
                this.isCanClickPay = true;
                this.isPaying = false;
                console.log(error);
            }
        },
        // 关闭人脸认证协议弹窗
        // enterNavEvent() {
        //     // 关闭人脸认证协议弹窗
        //     this.$store.dispatch('changeFacePop', false);
        //     this.$sKit.commonUtil.nextOilTriggerRisk().then(res => {
        //         this.sdkRealPay(true);
        //     });
        // },
        // // 查询是否存在待核销的电子券
        // queryOrderVerifiedCoupon() {
        //     this.$paymentCenter.queryOrderVerifiedCoupon(
        //         {
        //             paramsJsonStr: encodeURIComponent(
        //                 JSON.stringify({
        //                     bizModel: '3',
        //                     provinceCode: this.unPaidInfo.stationCode,
        //                     bizOrderNo: this.unPaidInfo.orderNo,
        //                 }),
        //             ),
        //         },
        //         res => {
        //             if (this.$paymentCenter.resStatus(res)) {
        //                 if (res.data && res.data.length > 0) {
        //                     res.data.forEach(payItem => {
        //                         if (Number(payItem.payMethod) === 11 && Number(payItem.payStatus) === 2) {
        //                             //支付方式11电子券，payStatus为2已核销
        //                             this.verifiedCoupon = payItem;
        //                         }
        //                     });
        //                 }
        //             }
        //         },
        //     );
        // },
    },
};
