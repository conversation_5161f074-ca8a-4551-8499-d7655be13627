import {
    orderQuery,
    getKLOrderQuery,
    queryUnionPayOrder,
    queryKunPengPay,
    queryCCBPay,
    rechargeByCardPost,
    getKLPayOrderInfo,
    getUnionPayOrder,
    getKunPengPay,
    getCCBPay,
    userAgreement,
} from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { oilCardRecharge, cardRecharge } from '../../../../../s-kit/js/v3-http/https3/user';
import { mixRecharge, name } from '../../../../../../project.config';
export default {
    methods: {
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            setTimeout(() => {
                // 停止下拉刷新
                this.$refs.pullDownRefreshRef.stopRefresh();
                console.log('加载完成');
            }, 1000);
            console.log('下拉刷新了');
        },

        /**
         * @description  : 获取地区充值方式
         * @param         {Function} getLocation -获取位置信息
         * @param         {String} province -省
         * @param         {String} city -市
         * @return        {*}
         */
        getAreaRecharge() {
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    provinceName: res.province,
                    cityName: res.city,
                };
                let oilCardRechargeRes = await oilCardRecharge(params);
                if (oilCardRechargeRes.status == 0) {
                    this.paymentModeArr = this.isHarmony ? oilCardRechargeRes.data.filter(item => item.alias == 'alipay') : oilCardRechargeRes.data;                
                    // 默认取第0项充值方式
                    this.payType = this.paymentModeArr[0].alias;
                    // 给每个充值方式添加图片
                    this.paymentModeArr.map(item => {
                        // item.img = require(`./../../images/${item.alias}.png`) || ''
                        let img = '';
                        switch (item.alias) {
                            case 'wx':
                                img = require('../../../images/wx.png');
                                break;
                            case 'alipay':
                                img = require('../../../images/aliPay.png');
                                break;
                            case 'unionpay':
                                img = require('../../../images/unionpay.png');
                                break;
                            case 'kunlun':
                                img = require('../../../images/kunlun.png');
                                break;
                            case 'kunpeng':
                                img = require('../../../images/kunpeng.png');
                                break;
                            case 'jianhang':
                                img = require('../../../images/jianhang.png');
                                break;
                            case 'card':
                                img = require('../../../images/card.png');
                                break;
                            default:
                                break;
                        }
                        item.img = img || '';
                    });
                    console.log('paymentModeArr',JSON.stringify(this.paymentModeArr))
                }
            });
        },
        /**
         * @description  : 确认充值
         * @param         {Boolean} agreementFlag -是否同意协议
         * @param         {String} payType -支付方式
         * @return        {*}
         */
        confirmRecharge() {
            // 校验是否同意协议，是否选择支付方式，根据支付方式调用不同的支付逻辑
            if (!this.agreementFlag) {
                uni.showToast({
                    title: '请先勾选同意充值协议',
                    icon: 'none',
                });
                return;
            }
            if (!this.payType) {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                });
                return;
            }
            if (this.paymentSelectIndex == '-1') {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                });
                return;
            }
            if (this.payType == 'card') {
                console.log('测试====');
                this.stopRecharge()
                return;
            }
            switch (this.payType) {
                case 'alipay':
                case 'wx':
                    this.weixinOrAliPay();
                    break;
                // case 'card':
                //     this.rechargeByCard();
                //     break;
                case 'kunlun':
                    this.rechargeByKunlun();
                    break;
                case 'unionpay':
                    //银联
                    this.rechargeByUnionPay();
                    break;
                case 'kunpeng':
                    //鲲鹏
                    this.rechargeByKunPeng();
                    break;
                case 'jianhang':
                    //建行
                    this.rechargeByJianHang();
                    break;
            }
        },
        /**
         * @description  : 微信支或支付宝充值
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {Function} getUserTokenInfo -获取用户信息
         * @param         {String} phone -手机号
         * @param         {String} cardNo -卡号
         * @param         {String} money -金额
         * @param         {String} invoice -是否开票
         * @param         {String} rechargeMode -支付方式
         * @param         {String} ip -手机终端ip
         * @param         {String} orderNo -订单号
         * @return        {*}
         */
        async weixinOrAliPay() {
            let money = this.rechargeMoney();
            if (!money) return;
            let userPhone;
            userPhone = await this.$cnpcBridge.getUserTokenInfo().phone;
            let params = {
                cardNo: this.thirdCardItemRecharge.cardNo,
                // rechargeMode: "0" + this.payType, //01-WeiXin，02-AliPay
                rechargeMode: this.payType === 'wx' ? '01' : '02', //01-WeiXin，02-AliPay
                amount: this.money,
                invoice: '0', //0-不开，1-未开，2-已开
                ip: '', //手机终端ip
            };
            (params.phone = userPhone), console.log('params', params);
            let _this = this;
            cardRecharge(params).then(
                res => {
                    let data = res.data;
                    if (res.status == 0) {
                        if (_this.payType === 'wx') {
                            _this.orderNo = data.out_trade_no;
                            _this.payForWX(data);
                        } else if (_this.payType === 'alipay') {
                            //通过截取字符串然后取到outtradNo
                            let aliPayStr = data.aliPrepay;
                            let arrary = aliPayStr.split('&');
                            arrary.forEach(item => {
                                if (item.indexOf('out_trade_no') !== -1) {
                                    let lastArrary = item.split('=');
                                    if (lastArrary.length > 0) {
                                        _this.orderNo = lastArrary[1];
                                    }
                                }
                            });
                            _this.payForAli(data.aliPrepay);
                        }
                    } else {
                        uni.showToast({
                            title: res.info || '充值失败',
                            icon: 'none',
                        });
                    }
                },
                error => {
                    //this.$Loading.close();
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                },
            );
        },
        /**
         * @description  : 充值卡充值
         * @param         {String} rechargeableCard -充值卡卡号
         * @param         {Function} getUserTokenInfo -获取用户信息
         * @param         {String} cardNo -卡号
         * @return        {*}
         */
        rechargeByCard: function () {
            if (!(this.rechargeableCard && this.rechargeableCard.length === 19)) {
                uni.showToast({
                    title: '请输入正确的充值卡密码',
                    icon: 'none',
                });
                return;
            }
            let params = {
                cardNo: this.thirdCardItemRecharge.cardNo,
                rechargeableCard: this.rechargeableCard,
            };
            console.log('params', params);
            let _this = this;
            rechargeByCardPost(params).then(
                res => {
                    console.log('res--'.res);
                    let data = res.data;

                    if (res.status == 0) {
                        let money = data / 100;
                        _this.goSuccess(money);
                    } else {
                        uni.showToast({
                            title: res.info ? res.info : '充值失败',
                            icon: 'none',
                        });
                    }
                },
                error => {
                    //this.$Loading.close();
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                },
            );
        },

        /**
         * @description  : 点击查看协议
         * @param         {string} type -协议类型
         * @param         {string} cityName -城市编码
         * @param         {string} name -协议名称
         * @param         {Function} checkPDF -打开协议
         * @return        {*}
         */
        async goToagreement() {
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: '1',
                    cityName: res.cityCode,
                    name: 'App充值协议',
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    } else {
                        uni.showToast({ title: '未找到该协议' });
                    }
                }
            });
        },
        /**
         * @description  : 支付宝支付
         * @param         {Function} aliPay -支付宝支付
         * @return        {*}
         */
        payForAli: function (info) {
            console.log(info, 'infooo---');
            this.$cnpcBridge.aliPay(info, res => {
                if (res.result) {
                    // 查询支付状态
                    this.queryOrderPayStatus();
                } else {
                    uni.showToast({
                        title: res.msg || '充值失败',
                        icon: 'none',
                    });
                }
            });
        },
        /**
         * @description  : 微信支付
         * @param         {Function} wxPay -微信支付
         * @return        {*}
         */
        payForWX: function (info) {
            this.$cnpcBridge.wxPay(info, res => {
                if (res.result) {
                    // 查询支付状态
                    this.queryOrderPayStatus();
                } else {
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                }
            });
        },
        /**
         * @description  : 查询支付状态
         * @return        {*}
         */
        queryOrderPayStatus() {
            this.queryOrder()
                .then(() => {
                    // 支付成功以后调用该方法查询油卡信息，并返回油卡管理页面
                    this.goSuccess();
                })
                .catch(() => {
                    this.alertType = '1';
                });
        },
        /**
         * @description  : 昆仑银行支付
         * @param         {Function} getCommonArgs -获取设备公共参数
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {String} cardNo -卡号
         * @param         {String} money -金额
         * @param         {String} amount -金额
         * @param         {String} macId -设备macId
         * @param         {String} chargeIp -设备chargeIp
         * @param         {String} rechargeMode -支付方式
         * @param         {String} location -省份城市信息
         * @param         {String} fuelIs -'后端没接当前参数'
         * @param         {String} orderNo -订单号
         * @param         {Function} isHasKLBank -判断昆仑银行app是否安装
         * @param         {Function} kunlunPay -昆仑银行支付
         * @param         {Function} zjShowModal -全局报错弹窗
         * @param         {Function} mobileBrowsers -用手机浏览器打开url
         * @return        {*}
         */
        async rechargeByKunlun() {
            let money = this.rechargeMoney();
            if (!money) return;
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            let _this = this;
            this.$cnpcBridge.getLocation(async locRes => {
                let params = {
                    amount: money,
                    cardNo: this.thirdCardItemRecharge.cardNo,
                    macId: commonArgs.macId,
                    chargeIp: commonArgs.ip || '127.0.0.1',
                    location: locRes.province + locRes.city,
                    fuelIs: '1',
                };
                getKLPayOrderInfo(params).then(
                    async res => {
                        let data = res.data;
                        if (res.status == 0) {
                            let payobj = JSON.parse(data);
                            _this.orderNo = payobj.orderNo;
                            let isHasKLBank = await this.$cnpcBridge.isHasKLBank();
                            if (isHasKLBank == 1)
                                //昆仑银行app已安装
                                _this.$cnpcBridge.kunlunPay(payobj, resultObj => {
                                    if (resultObj.status) {
                                        //成功 请求接口
                                        this.queryOrderPayStatus();
                                    } else {
                                        //mountd 会比回调快，所以这些都不能限制住弹窗
                                        // _this.alertType = "";
                                        // _this.pageType = "1";
                                        // _this.orderNo = 0;
                                        uni.showToast({
                                            title: resultObj.msg || '网络不给力，请稍后再试',
                                            icon: 'none',
                                        });
                                    }
                                });
                            else {
                                //昆仑银行app未安装
                                this.$store.dispatch('zjShowModal', {
                                    title: '温馨提示',
                                    content: '您的手机没有下载昆仑直销银行APP,立即下载？',
                                    confirmText: '下载',
                                    cancelText: '取消',
                                    confirmColor: '#333333',
                                    cancelColor: '#666666',
                                    success: res => {
                                        if (res.confirm) {
                                            console.log('用户点击确定');
                                            this.$cnpcBridge.mobileBrowsers('http://www.eklb.cn/moneymanager/pages/appDownloan.html');
                                        } else if (res.cancel) {
                                            console.log('用户点击取消');
                                        }
                                    },
                                });
                            }
                        } else {
                            uni.showToast({
                                title: res.info ? res.info : '充值失败',
                                icon: 'none',
                            });
                        }
                    },
                    error => {
                        //this.$Loading.close();
                        uni.showToast({
                            title: '充值失败',
                            icon: 'none',
                        });
                    },
                );
            });
        },
        /**
         * @description  : 银联预下单
         * @param         {Function} getCommonArgs -获取设备公共参数
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {String} cardNo -卡号
         * @param         {String} amount -金额
         * @param         {String} macId -设备macId
         * @param         {String} chargeIp -设备chargeIp
         * @param         {Function} unionPay -银联支付
         * @return        {*}
         */
        async rechargeByUnionPay() {
            let money = this.rechargeMoney();
            if (!money) return;
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            let params = {
                amount: money,
                cardNo: this.thirdCardItemRecharge.cardNo,
                macId: commonArgs.macId,
                chargeIp: commonArgs.ip || '127.0.0.1',
            };
            ////this.$Loading.open();
            getUnionPayOrder(params).then(
                async res => {
                    //this.$Loading.close();
                    let data = res.data;
                    if (res.status == 0) {
                        if (data) {
                            let payobj = JSON.parse(data);
                            this.orderNo = payobj.orderno;
                            let tn = payobj.tn;
                            this.$cnpcBridge.unionPay(tn, data => {
                                let resultobj = typeof data === 'string' ? JSON.parse(data) : data;

                                if (resultobj.status) {
                                    //成功 请求接口  银联支付框没有遮住当前页面，所以在支付成功后查询下
                                    this.queryOrder()
                                        .then(() => {
                                            // 支付成功以后调用该方法查询油卡信息，并返回油卡管理页面
                                            this.goSuccess();
                                        })
                                        .catch(() => {
                                            this.alertType = '1';
                                        });
                                } else {
                                    uni.showToast({
                                        title: resultobj.msg || '网络不给力，请稍后再试',
                                        icon: 'none',
                                    });
                                }
                            });
                        } else {
                            uni.showToast({
                                title: res.info ? res.info : '充值遇到未知异常，请稍后再试',
                                icon: 'none',
                            });
                        }
                    } else {
                        uni.showToast({
                            title: res.info ? res.info : '充值失败',
                            icon: 'none',
                        });
                    }
                },
                error => {
                    //this.$Loading.close();
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                },
            );
        },
        /**
         * @description  : 鲲鹏预下单
         * @param         {Function} getCommonArgs -获取设备公共参数
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {String} amount -金额
         * @param         {String} macId -设备macId
         * @param         {String} chargeIp -设备chargeIp
         * @param         {Function} kpPay -鲲鹏支付
         * @return        {*}
         */
        async rechargeByKunPeng() {
            let money = this.rechargeMoney();
            if (!money) return;
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            let params = {
                amount: money,
                cardNo: this.thirdCardItemRecharge.cardNo,
                chargeIp: commonArgs.ip || '127.0.0.1',
            };
            ////this.$Loading.open();
            getKunPengPay(params).then(
                async res => {
                    //this.$Loading.close();
                    let data = res.data;
                    if (res.status == 0) {
                        if (data) {
                            let payobj = data;
                            this.orderNo = payobj.payOrderNo;
                            let prepayid = payobj.prepayId;
                            this.$cnpcBridge.kpPay(prepayid, resultobj => {
                                if (resultobj.status) {
                                    //成功 请求接口
                                    this.queryOrderPayStatus();
                                } else {
                                    uni.showToast({
                                        title: resultobj.msg || '网络不给力，请稍后再试',
                                        icon: 'none',
                                    });
                                }
                            });
                        } else {
                            uni.showToast({
                                title: res.info ? res.info : '充值遇到未知异常，请稍后再试',
                                icon: 'none',
                            });
                        }
                    } else {
                        uni.showToast({
                            title: res.info ? res.info : '充值失败',
                            icon: 'none',
                        });
                    }
                },
                error => {
                    //this.$Loading.close();
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                },
            );
        },
        /**
         * @description  : 建行预下单
         * @param         {Function} getCommonArgs -获取设备公共参数
         * @param         {Function} rechargeMoney -选择充值金额的方法
         * @param         {String} amount -金额
         * @param         {String} macId -设备macId
         * @param         {String} chargeIp -设备chargeIp
         * @param         {Function} mobileBrowsers -用手机浏览器打开url
         * @return        {*}
         */
        async rechargeByJianHang() {
            let money = this.rechargeMoney();
            if (!money) return;
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            let params = {
                amount: money,
                cardNo: this.thirdCardItemRecharge.cardNo,
                chargeIp: commonArgs.ip || '127.0.0.1',
            };
            ////this.$Loading.open();
            getCCBPay(params).then(
                async res => {
                    //this.$Loading.close();
                    let data = res.data;
                    if (res.status == 0) {
                        if (data) {
                            let payobj = data;
                            this.orderNo = payobj.orderid;
                            //建行支付
                            if (payobj.payUrlStr) this.$cnpcBridge.mobileBrowsers(encodeURI(payobj.payUrlStr));
                            else {
                                uni.showToast({
                                    title: '充值遇到未知异常，请稍后再试',
                                    icon: 'none',
                                });
                            }
                        } else {
                            uni.showToast({
                                title: res.info ? res.info : '充值遇到未知异常，请稍后再试',
                                icon: 'none',
                            });
                        }
                    } else {
                        uni.showToast({
                            title: res.info ? res.info : '充值失败',
                            icon: 'none',
                        });
                    }
                },
                error => {
                    //this.$Loading.close();
                    uni.showToast({
                        title: '充值失败',
                        icon: 'none',
                    });
                },
            );
        },
        /**
         * @description  : 匹配充值金额
         * @param         {String} moneyType -匹配充值类型的type
         * @return        {*}
         */
        rechargeMoney() {
            let money = '';
            if (this.moneyType == '1') {
                money = '200';
            } else if (this.moneyType == '2') {
                money = '300';
            } else if (this.moneyType == '3') {
                money = '500';
            } else if (this.moneyType == '4') {
                money = '800';
            } else if (this.moneyType == '5') {
                money = '1000';
            } else if (this.moneyType == '6') {
                money = '2000';
            } else {
                money = this.money;
            }
            if (!money) {
                uni.showToast({
                    title: '请输入充值金额',
                    icon: 'none',
                });
                return;
            }
            // if (this.$sKit.commonUtil.judgeEnv()) {
            if (money < (mixRecharge || 1)) {
                uni.showToast({
                    title: '充值金额不能小于一元',
                    icon: 'none',
                });
                return;
            }
            // }
            return money;
        },
        /**
         * @description  : 查询充值订单，匹配相应的类型调用相应的支付方法
         * @return        {*}
         */
        queryOrder() {
            let _this = this;
            return new Promise((resolve, reject) => {
                ////this.$Loading.open();
                let post = orderQuery;
                if (_this.payType === 'kunlun') {
                    //昆仑银行支付
                    post = getKLOrderQuery;
                } else if (_this.payType === 'unionpay') {
                    post = queryUnionPayOrder;
                } else if (_this.payType === 'kunpeng') {
                    post = queryKunPengPay;
                } else if (_this.payType === 'jianhang') {
                    post = queryCCBPay;
                }
                let params = {
                    rechargeMode: _this.payType === 'wx' ? '1' : '2', //微信是1 支付宝是2
                    orderNo: _this.orderNo,
                    cardNo: _this.thirdCardItemRecharge.cardNo,
                };
                post(params).then(
                    res => {
                        //this.$Loading.close();
                        let data = res.data;
                        if (res.status == 0) {
                            resolve();
                        } else {
                            reject();
                        }
                    },
                    error => {
                        reject();
                    },
                );
            });
        },
        /**
         * @description  : 支付成功以后调用该方法查询油卡信息，并返回油卡管理页面
         * @return        {*}
         */
        async goSuccess(rechargeMoney) {
            // await this.getBindCardInfo();
            this.$sKit.mpBP.tracker('充值', {
                seed: 'rechargeBiz',
                pageID: 'cardsucessToast', // 页面名
                refer: this.thirdCardItemRecharge.refer || '', // 来源
                channelID: 'C10', // C10/C12/C13
                czMoney: this.money,
                address: this.thirdCardItemRecharge.address, // 归属地
            });
            uni.showToast({
                title: '充值成功',
                icon: 'none',
                mask: true,
                duration: 2000,
            });
            setTimeout(() => {
                uni.navigateBack({ detail: 1 });
                // 存储本地的用来刷新卡信息的标识
                uni.setStorageSync('refreshCardManagement', true);
            }, 1500);

            // let url = ''
            // let type = "navigateBack";
            // let prevParams = {
            //   // 得到上一页面的实例需要传2 若要返回上上页面的实例就传3，以此类推
            //   delta: 2,
            //   // 返回指定页面的data中的标识，用于在调用接口成功后返回该页面时候，调用指定的函数或更改要返回页面中定义的属性值
            //   refreshListFlag: true
            // }
            // this.$sKit.layer.useRouter(url, params, type, prevParams);
            return;
            let money = '';
            if (this.moneyType == '1') {
                money = '200';
            } else if (this.moneyType == '2') {
                money = '300';
            } else if (this.moneyType == '3') {
                money = '500';
            } else if (this.moneyType == '4') {
                money = '800';
            } else if (this.moneyType == '5') {
                money = '1000';
            } else if (this.moneyType == '5') {
                money = '2000';
            } else {
                money = this.money;
            }
            let payMode = '';
            if (this.payType === 'wx') payMode = '微信';
            else if (this.payType === 'alipay') payMode = '支付宝';
            else if (this.payType === 'card') {
                money = rechargeMoney;
                payMode = '充值卡充值';
            } else if (this.payType === 'kunlun') payMode = '昆仑银行';
            else if (this.payType === 'unionpay') payMode = '银联支付';
            else if (this.payType === 'kunpeng') payMode = '数字人民币';
            else if (this.payType === 'jianhang') payMode = '建行龙支付';
        },
    },
};
