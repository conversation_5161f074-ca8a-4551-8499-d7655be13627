<template>
    <div class="bg-F7F7FB" style="height: 100%">
        <zj-navbar :height="44" title="测试jsapi"></zj-navbar>
        <div class="primary-btn btn-class" @click="getFinger()">获取风控设备指纹</div>
        <div class="primary-btn btn-class" @click="paymentInit()">支付sdk初始化</div>
        <div class="primary-btn btn-class" @click="accountInit()">账户sdk初始化</div>
        <div class="primary-btn btn-class" @click="storeItem()">存储数据</div>
        <div class="primary-btn btn-class" @click="getItem()">获取存储数据</div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';

export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
        };
    },
    async onLoad() {},
    async onShow() {},
    methods: {
        getFinger() {
            this.$cnpcBridge.getFinger(res => {
                uni.showToast({
                    title: JSON.stringify(res),
                    icon: 'none',
                    duration: 2000,
                });
            });
        },
        paymentInit() {
            this.$paymentCenter.initPay(res => {
                uni.showToast({
                    title: JSON.stringify(res),
                    icon: 'none',
                    duration: 2000,
                });
            });
        },
        accountInit() {
            this.$accountCenter.initAccountSDK(res => {
                uni.showToast({
                    title: JSON.stringify(res),
                    icon: 'none',
                    duration: 2000,
                });
            });
        },
        storeItem() {
            this.$cnpcBridge.setValueToNative('token', '*********', res => {
                uni.showToast({
                    title: JSON.stringify(res),
                    icon: 'none',
                    duration: 2000,
                });
            });
        },
        getItem() {
            this.$cnpcBridge.getValueToNative('token', res => {
                uni.showToast({
                    title: JSON.stringify(res),
                    icon: 'none',
                    duration: 2000,
                });
            });
        },
        messageTypeClick(item) {
            uni.navigateTo({
                url: `/packages/message/pages/message-list/main?msgType=${item.msgType}`,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.btn-class {
    width: 80%;
    margin-left: 10%;
    margin-top: 20px;
    height: 40px;
    color: white;
    font-size: 20px;
    line-height: 40px;
}
</style>
