<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <view class="page-union">
        <image class="transferAccountBg_style" :src="transferAccountBg" alt=""></image>
        <view class="content_div">
            <view class="container">
                <!-- <image class="logo" :src="logo"></image>
            <view class="title">能源e站</view>
            <view class="subtitle">中国石油官方服务</view> -->
                <view class="logo_div">
                    <image class="logo_style" :src="logo"></image>
                    <view class="">
                        <view class="title">能源e站</view>
                        <view class="text">e享加油生活</view>
                    </view>
                </view>
            </view>
            <view class="agreement_div">
                <view class="select_div">
                    <!-- <image class="" :src="empty" v-if="select" @click="modalClick()"></image> -->
                    <image class="" :src="empty" v-if="agree" @click="agree = false"></image>
                    <image class="" :src="successSel" @click="agree = true" v-else></image>
                </view>
                <view class="agreement_text">
                    <view class='font-12 agreement_name weight-400' @click="agree = !agree">我已阅读并同意能源e站</view>
                    <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(17)">
                        《用户协议》
                    </view>
                    <view class='font-12 agreement_name'>和</view>

                    <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view>
                </view>
            </view>
            <!-- #ifdef MP-ALIPAY || H5 -->
            <view class="auth-box" v-if="!isLogin">
                <!-- <view class="agreement-box">
                    <u-checkbox-group>
                        <u-checkbox v-model="agree" shape="circle"></u-checkbox>
                    </u-checkbox-group>
                    <view class="agreement">
                        <view class="label" @click="onCheckedAgree()">同意</view>
                        <view class="link" @click="onAgreementUrlClick()">《用户授权协议》</view>
                        <view class="label" @click="onCheckedAgree()">使用你的支付宝手机号快捷登录</view>
                    </view>
                </view> -->
                <view v-if="!agree">
                    <ZfbLoginV3 :autoV3="autoV3" :layer="true" :silence="false" @success="onLoginAfterEvent">
                        <button class="primary-btn2">授权登录</button>
                    </ZfbLoginV3>
                </view>
                <button v-if="agree" class="primary-btn2" @click="onCheckAgree()">授权登录</button>
            </view>
            <!-- #endif -->

            <view class="loading-box" v-if="isLogin">
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
            </view>
        </view>

        <view class="footer">如需帮助，可联系：956100</view>
        <zj-show-modal>
            <div style="display: inline"
                >
                <view class='agreement_name font-12 weight-400'>登录能源e站，请先阅读并同意能源e站</view>
                <view
                    class="font-12 color-E64F22 weight-400 agreement_name"
                    @click="clickXieyi(17)"
                    >《用户协议》</view
                ><view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view></div
            >
        </zj-show-modal>
    </view>
</template>

<script>
// #ifdef MP-ALIPAY
import CSH from '@/utils/csh.config';
import ZfbLoginV3 from '@/components/zfb-login-v3/zfb-login-v3.vue';
import { userAgreement } from '../../s-kit/js/v3-http/https3/user';
// #endif
import { mapGetters, mapState } from 'vuex';

import logo from '@/static/img/cnpc-logo.png';
import transferAccountBg from '@/static/transferAccount-bg.png';
import empty from '@/static/empty.png';
import successSel from '@/static/successSel.png';
import projectConfig from '../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        // #ifdef MP-ALIPAY
        ZfbLoginV3,
        // #endif
    },
    data() {
        return {
            logo,
            title: '登录', // 标题
            type: 0, // 1 登录 2 注册
            autoV3: '', // 非3.0账号自动升级为3.0账号
            phone: '', // 一键登录手机号
            encUserInfo: null,
            agree: true,
            // #ifdef MP-ALIPAY
            isLogin: true,
            // #endif
            // #ifdef MP-WEIXIN
            isLogin: false,
            // #endif
            query: {},
            buttonGrayedOut: true,
            transferParameters: '',
            transferAccountBg,
            empty,
            successSel,
        };
    },
    computed: {
        ...mapState({
            token: state => state.token,
            token3: state => state.token3,
        }),
        ...mapGetters(['walletStatus']),
    },
    onLoad(query) {
        // #ifdef MP-ALIPAY
        my.hideShareMenu();
        my.hideBackHome();
        if (query.v3) {
            this.autoV3 = Number(query.v3);
        }
        this.query = query;
        console.log(query, 'csh');
        setTimeout(_ => {
            this.cshLogic();
        }, 1500);
        // #endif
    },
    mounted() {},
    onShow() {},
    methods: {
        modalClick() {
            this.$store.dispatch('zjShowModal', {
                confirmText: '同意',
                cancelText: '我再想想',
                cancelColor: '#666666',
                confirmColor: '#FF3E00',
                success: async res => {
                    if (res.confirm) {
                        this.agree = false;
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 授权一键登录/获取手机号
        async onGetAuthorize(e) {
            if (e.detail?.errMsg !== 'getPhoneNumber:ok') {
                console.warn('用户拒绝授权');
                return;
            }
            console.log(e, this.$refs.loginRef);
            if (this.$refs.loginRef) {
                this.$refs.loginRef.login(true);
            }
        },
        // 授权异常
        onAuthError(err) {
            console.error(err);
        },
        // 同意/不同意协议
        onCheckedAgree() {
            this.agree = !this.agree;
        },
        // // 用户授权协议
        // onAgreementUrlClick() {
        //     my.navigateTo({ url: '/pages/userAgreement/userAgreement' });
        // },
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('1', '支付宝隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
        // 检查是否同意协议
        onCheckAgree() {
            // my.showToast({
            //     content: '请先同意用户授权协议',
            // });
            this.modalClick();
        },
        // 车生活联跳逻辑
        async cshLogic() {
            const userInfo = uni.getStorageSync('tokenInfo') || {};
            let v3token = uni.getStorageSync('v3token');
            if (!userInfo?.accessToken) {
                this.isLogin = false;
                return;
            }
            const vKey = v3token ? 'v3' : 'v2';
            const qKey = await this.checkWalletStatus(this.query.qKey, vKey);
            this.query.qKey = qKey;
            if (!qKey || !CSH[qKey] || !CSH[qKey][vKey]) {
                if (vKey == 'v3') {
                    uni.reLaunch({
                        url: `${projectConfig?.subPath || ''}/pages/thirdHome/main`,
                    });
                } else if (vKey == 'v2') {
                    uni.reLaunch({
                        url: `/pages/index/index`,
                    });
                }
                return console.warn('联跳参数错误', qKey, vKey);
            }
            let url = '';
            // 跳转到电子券或者电子券使用记录
            if (qKey == 'couponList' && vKey == 'v3') {
                let type;
                try {
                    type = this.query?.type.replace(/'/g, ''); // 去除所有单引号
                } catch (error) {
                    type = '1'; // 设置默认的 type 值
                }

                const urlMap = {
                    '': '/packages/third-coupon-module/pages/coupon-list/main',
                    1: '/packages/third-coupon-module/pages/coupon-list/main',
                    2: '/packages/third-coupon-module/pages/coupon-list/main',
                    10: '/packages/third-coupon-module/pages/coupon-used-record/main',
                    20: '/packages/third-coupon-module/pages/coupon-used-record/main',
                };
                this.query.refer = 'r41';
                url = (projectConfig?.subPath || '') + (urlMap[type] || '/pages/thirdHome/main'); // 默认 URL，如果没有匹配的 type
            } else {
                if (vKey == 'v3') {
                    if (qKey == 'walletOpen') this.query.refer = 'r06';
                    if (qKey == 'wallet') this.query.refer = 'r02';
                    if (qKey == 'walletRecharge') this.query.refer = 'r19';
                    if (qKey == 'invoiceList') this.query.refer = 'r36';
                    if (qKey == 'oilCardList') this.query.refer = 'r47';
                    if (qKey == 'orderDetail' || qKey == 'orderList') this.query.refer = 'r51';
                }
                url = CSH[qKey][vKey];
            }
            const toUrl = url + '?data=' + encodeURIComponent(JSON.stringify(this.query));
            let tokenInfo = uni.getStorageSync('tokenInfo');
            // 埋点
            this.$sKit.mpBP.tracker('车生活跳转区分2.0和3.0会员', {
                seed: 'car_life_member_type',
                member_type: vKey,
                member_no: tokenInfo?.memberNo,
                openid: tokenInfo?.openId,
                toUrl: toUrl,
            });

            console.log('车生活联跳：', this.query, toUrl);
            my.reLaunch({
                url: toUrl,
            });
        },
        // 登录成功后逻辑
        onLoginAfterEvent(type) {
            console.log('登录成功后逻辑', type);
            this.cshLogic();
        },
        checkWalletStatus(qKey, vKey) {
            return new Promise(async (resolve, reject) => {
                if (qKey == 'walletOpen' && vKey == 'v3') {
                    const res = await this.$store.dispatch('getSetWalletStatus');
                    if (res && res.success) {
                        if (res.data.status) {
                            resolve('wallet');
                        } else {
                            resolve(qKey);
                        }
                    } else {
                        resolve(qKey);
                    }
                } else {
                    resolve(qKey);
                }
            });
        },
    },
};
</script>

<style scoped lang="scss">
.page-union {
    width: 100%;
    background: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

// .container {
//     padding: 0 24rpx;
// }

.logo {
    width: 140rpx;
    height: 140rpx;
    margin: 200rpx auto 30rpx;
    display: block;
}

.btn-auth-login {
    color: #fff;
    background-color: rgb(51, 107, 225);
    border-radius: 60rpx;

    &:active {
        opacity: 0.7;
    }
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
}

.subtitle {
    font-size: 28rpx;
    color: #888;
    text-align: center;
    margin: 20rpx 0 80rpx;
}

.agreement-box {
    margin-top: 30rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    white-space: nowrap;
    margin-left: -10px;
    margin-bottom: -1px;

    .link {
        color: #1d77cd;
    }
}

.loading-box {
    padding-top: 80rpx;
    display: flex;
    justify-content: center;
    gap: 10rpx;

    .loading-dot {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: #888;
        animation: bolling 1s alternate infinite;
    }
}

.footer {
    position: fixed;
    bottom: 50rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    width: 100%;
}
.agreement_name {
    display: inline;
}
@keyframes bolling {
    0% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(5px);
    }
}

.transferAccountBg_style {
    width: 100%;
    height: 365px;
    position: absolute;
    z-index: 0;
}
.content_div {
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 152px;
    padding: 0 63px;
}
.logo_style {
    width: 47px;
    height: 45px;
    margin-right: 14px;
}
.container {
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    margin-bottom: 43px;
}

.logo_div {
    display: flex;
}

.title {
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
}
.text {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    letter-spacing: 1px;
}
.agreement_div {
    margin-bottom: 16px;
    display: flex;
    .select_div {
        display: inline-block;
        margin-right: 5px;
        image {
            width: 17px;
            height: 17px;
        }
    }
}
.agreement_text {
    width: 94%;
}
.agreement_name {
    display: inline;
}
</style>
