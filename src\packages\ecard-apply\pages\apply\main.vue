<template>
    <div class="view">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="申请电子卡"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <!-- 申请电子卡页面表单  -->
        <view>
            <!-- 完善申请信息  -->
            <div class="section-box-con">
                <div class="section-box">完善申请信息</div>
                <!-- 姓名 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>姓名 </div>
                    <div class="right">
                        <input type="text" :disabled="disabledFlagName" placeholder="请输入姓名" v-model="applyInfo.name" />
                    </div>
                </div>
                <!-- 证件类型 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>证件类型 </div>
                    <div class="right">
                        <input type="text" placeholder="身份证" disabled v-model="applyInfo.documentType" />
                    </div>
                </div>
                <!-- 证件号码 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>证件号码 </div>
                    <div class="right">
                        <input type="text" placeholder="请输入您的证件号码" v-model="applyInfo.idNumber" />
                    </div>
                </div>
                <!-- 开卡机构 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>开卡机构 </div>
                    <div class="right" @click="openCardMechanism">
                        <input class="center" maxlength="0" disabled placeholder="请选择开卡机构" v-model="applyInfo.provinces" />
                    </div>
                    <!-- 点击开卡机构弹出的下拉选 -->
                    <u-select v-model="show" mode="mutil-column-auto" :list="multiSelector" @confirm="confirm"></u-select>

                    <u-icon
                        class="right-icon uicon-arrow-right"
                        name="arrow-right"
                        @click="openCardMechanism"
                        v-if="show === false"
                    ></u-icon>
                    <u-icon class="right-icon uicon-arrow-down" name="arrow-down" @click="openCardMechanism" v-if="show"></u-icon>
                </div>
                <!-- 开票类型 -->
                <div class="section-box">
                    <div class="left">开票类型</div>
                    <div class="right">
                        <input type="text" disabled placeholder="消费时消费地单笔开普通发票" v-model="applyInfo.typeMakeOutAnInvoice" />
                    </div>
                </div>
                <!-- 手机号 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>手机号 </div>
                    <div class="right">
                        <input type="text" :disabled="disabledFlagPhone" placeholder="请输入手机号" v-model="applyInfo.mobilePhoneNo" />
                    </div>
                </div>
                <!-- 验证码 -->
                <div class="section-box">
                    <div class="left"> <div class="danger">*</div>验证码 </div>
                    <div class="right">
                        <input
                            type="Number"
                            maxlength="4"
                            class="center code"
                            placeholder="请输入验证码"
                            v-model.trim="applyInfo.codeNumber"
                        />
                    </div>

                    <div class="right-code" @click="getVerificationCode" v-show="numShow">发送验证码</div>
                    <div class="right-code" v-show="!numShow">{{ count }}s</div>
                </div>
                <!-- 车牌管理 -->
                <div class="section-box">
                    <div class="left">车牌管理</div>
                    <div class="right" @click="licenseManagement">
                        <input disabled class="center" v-model.trim="applyInfo.licensePlate" placeholder="请选择车牌号(可多选)" />
                        <!-- <view v-model.trim="applyInfo.licensePlate" class="center centerWidth info-right"></view> -->
                    </div>
                    <u-icon class="right-icon uicon-arrow-right" name="arrow-right" @click="licenseManagement"></u-icon>
                </div>
            </div>
            <!-- 选填文字提醒 -->
            <div class="section-box-text">协助您申请电子卡的员工推荐码（选填）</div>
            <!-- 当前位置 -->
            <div class="section-box-con" @click="changeStation">
                <div class="section-box">
                    <div class="left">当前位置</div>
                    <div class="right">
                        <input class="center position" placeholder="请输入或选择位置" v-model.trim="applyInfo.currentPosition" />
                    </div>
                    <div class="right-position">更改油站</div>
                </div>
            </div>
            <!-- 员工推荐码 -->
            <div class="section-box-con">
                <div class="section-box">
                    <div class="left">员工推荐码</div>
                    <div class="right">
                        <input class="center" placeholder="请输入员工推荐码" v-model.trim="applyInfo.referralCode" />
                    </div>
                </div>
            </div>
            <!-- 阅读协议 -->
            <div class="oil-footer">
                <div class="agreement">
                    <u-checkbox v-model="agreementchecked" shape="circle" active-color="#FF8200">
                        <div class="agreement-txt">
                            同意
                            <span @click.stop="clickXieyi" class="oil-money-info">《中石油电子卡服务协议》</span>
                        </div>
                    </u-checkbox>
                </div>
                <div class="oil-btn">
                    <div class="oli-paybtn-gray" @click="agreedToOpen" :class="{ 'oli-pay-info': agreementchecked }"
                        >同意协议并刷脸开通</div
                    >
                </div>
            </div>
            <!-- 温馨提示  verificationCodeWindow-->
            <div class="reminder">温馨提示：使用电子卡加油消费，仅支持消费开票，请在“消费记录”中申请开具发票。</div>
            <view style="position: relative">
                <VerificationCodeWindow
                    ref="windows"
                    :isShow="showCodeWindow"
                    @close="hideCodeWindow"
                    :maxlength="4"
                    @migrationVerificationCode="migrationVerificationCode"
                    :reservedPhoneNumber="reservedPhoneNumber"
                ></VerificationCodeWindow>
            </view>
        </view>
        <zj-show-modal></zj-show-modal>
        <ZjNewStation
            class="new_station"
            @close="closeNewStationDialog"
            @submit="submitUpgrade"
            v-if="upgradePopUpWindow"
            :isUpgradeFlag="isUpgradeFlag"
        ></ZjNewStation>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import pageConfig from '@/utils/pageConfig.js';

import ZjNewStation from '@/components/zj-new-station/index.vue';
// import {getLocation } from '@'
import {
    openAccessCard,
    verificationCode,
    verifictoApplyForAnEcard,
    checkVerificationCode,
    moveAnECard,
    moveAnECardCode,
    serviceAgreement,
    getCityFlag,
} from '@/api/anEcardToOpenCard.js';
import VerificationCodeWindow from '../verificationCodeWindow/main.vue';
// import {getOilCardPhoneCodePost,} from "@/api/my-center";
export default {
    components: {
        VerificationCodeWindow,
        ZjNewStation,
    },
    computed: {
        ...mapGetters(['registerLoginInformation', 'isLogin', 'cardList']),
        ...mapState({
            lat: state => state.location.lat, // 纬度
            lon: state => state.location.lon, // 经度
            province: state => state.location.province, // 省份
            city: state => state.location.city, // 城市
            showMarkerArr: state => state.location.showMarkerArr, // marker数据
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            venicleList: state => state.venicle.venicleList, // 车辆列表
            scale: state => state.location.scale, // 地图缩放比例
            isCustomCallout: state => state.location.isCustomCallout, // 版本判断
            bannerListCity: state => state.location.bannerListCity, // 城市banner
            bannerListCountry: state => state.location.bannerListCountry, // 国家banner
            newStationFlag: state => state.location.newStationFlag, // 打开升级弹窗
        }),
    },
    watch: {
        agreementchecked: function (newVal, oldVal) {
            this.$Storage.isAgreePay.update(newVal);
        },
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            applyInfo: {
                documentType: '身份证',
                provinces: '', //  开卡机构省市区
                name: '',
                typeMakeOutAnInvoice: '消费时消费地单笔开普通发票', //  开票类型
                mobilePhoneNo: '', // 手机号
                codeNumber: '', // 验证码
                licensePlate: '', // 车牌号
                currentPosition: '', // 当前位置
                currentPositionCode: '', // 当前位置code码
                referralCode: '', // 员工推荐码
                provincesCode: '',
                // phone: registerLoginInformation.phone,
                idNumber: '',
            },
            password: '',
            // 电子卡
            anEcard: { name: '' },
            show: false,
            loading: false,
            multiSelector: [], //  机构数组
            selected: [0, 0],
            agreementchecked: false,
            numShow: true, // 验证码
            count: '',
            carInfo: [],
            makerObj: {},
            showCodeWindow: false,
            migrationCode: '', // 迁移验证码
            migrationPhoneNumber: '', // 迁移手机号
            disabledFlagName: false,
            disabledFlagPhone: false,
            reservedPhoneNumber: '', // 预留手机号
            carInfoArr: [],
            isUpgradeFlag: 'ktdzk',
            pilotInstitutions: '',
            isPilotInstitutionsFlag: '',
            upgradePopUpWindow: true,
        };
    },
    onShow() {},
    onLoad() {
        this.$store.commit('setCloseDialog', true);
        return;
        this.agreedToOpen = this.$util.throttleUtil(this.agreedToOpen);
        this.submitUpgrade = this.$util.throttleUtil(this.submitUpgrade);
        // this.getOpenAccessCard();
        // 如果卡列表存在就拿到卡列表中当前持卡人的名字进行回显
        if (JSON.stringify(this.cardList) !== '[]') {
            this.applyInfo.name = this.cardList[0].userName;
            this.disabledFlagName = true;
        }
        // 如果个人信息有值就拿到个人的手机号进行回显
        if (JSON.stringify(this.registerLoginInformation) !== '{}') {
            this.applyInfo.mobilePhoneNo = this.registerLoginInformation.phone;
            this.disabledFlagPhone = true;
        }
        if (JSON.stringify(this.selectMarker) !== '{}') {
            this.applyInfo.currentPosition = this.selectMarker.stationName;
            this.applyInfo.currentPositionCode = this.selectMarker.stationCode || '';
        }
        this.getOpenAccessCard(); //  获取开卡机构
        this.agreementchecked = this.$Storage.isAgreePay.value;
        // 监听事件
        uni.$on('selectCar', usnerinfo => {
            this.carInfo = usnerinfo;
            console.log(this.carInfo, 'this.carInfo');
            this.dealWithLicensePlate();
        });
        // 更改油站位置
        uni.$on('markerdata', usnerinfo => {
            this.applyInfo.currentPosition = usnerinfo.item.stationName;
            this.applyInfo.currentPositionCode = usnerinfo.item.stationCode;
        });
        // this.agreedToOpen = this.$util.throttleUtil(this.agreedToOpen);
    },
    onUnload() {
        // 移除监听事件
        uni.$off('selectCar');
        uni.$off('markerdata');
    },
    methods: {
        hideCodeWindow() {
            console.log('关闭', this.$refs.windows.password);
            this.$refs.windows.password = '';
            this.showCodeWindow = false;
        },
        // 获取开卡机构
        async confirm(e) {
            this.pilotInstitutions = e[0].label;
            this.applyInfo.provinces = e[0].label + ' ' + e[1].label;
            this.applyInfo.provincesCode = e[1].value;
            this.institutionsProvinces = e[0].label + e[1].label;
            this.pilotInstitutionsFun().then(res => {
                if (res) {
                    this.$store.commit('setCloseDialog', true);
                } else {
                    this.$store.commit('setCloseDialog', false);
                }
            });
        },
        // 当前机构是否为试点机构
        pilotInstitutionsFun() {
            return new Promise(async (resolve, reject) => {
                let params = {
                    cityName: this.pilotInstitutions,
                };
                let res = await getCityFlag(params);
                if (res.status == 0 && res.data.flag == 1) {
                    this.isPilotInstitutionsFlag = res.data.flag;
                    console.log(res.data.flag, '当前地区是试点地区吗');
                    // 打开升级弹窗
                    resolve(res.data.flag);
                } else {
                    this.isPilotInstitutionsFlag = res.data.flag;
                    resolve(res.data.flag);
                }
            });
        },
        changeHandler() {},
        // 点击查看协议详情
        clickXieyi() {
            uni.navigateTo({
                url: `/packages/setting/pages/agreement/main?value=91&institutionsProvinces=${this.institutionsProvinces}`,
            });
        },
        // 点击开发机构右侧图标 显示下拉选 并切换图标状态
        openCardMechanism() {
            this.show = !this.show;
            console.log(this.show, 'this.show');
        },
        // 获取开卡机构
        getOpenAccessCard() {
            let parasm = {
                token: this.$store.state.token,
            };
            openAccessCard(parasm).then(res => {
                if (res.status === 0) {
                    this.multiSelector = this.$test.changeTreeDate(
                        this.$test.changeTreeDate(
                            this.$test.changeTreeDate(this.$test.changeTreeDate(res.data, 'label', 'provinceName'), 'children', 'cities'),
                            'value',
                            'departmentCode',
                        ),
                        'label',
                        'cityName',
                    );
                    this.multiSelector.map(item => {
                        if (this.province.indexOf(item.label) > -1) {
                            console.log(item.label, item.value, 'LLLLLL');
                            item.children.map(ite => {
                                if (this.city.indexOf(ite.label) > -1) {
                                    console.log('11111', item.label);
                                    this.pilotInstitutions = item.label;
                                    this.applyInfo.provinces = item.label + ' ' + ite.label;
                                    this.applyInfo.provincesCode = ite.value;
                                    this.institutionsProvinces = item.label + ite.label;
                                    this.pilotInstitutionsFun();
                                }
                            });
                        }
                    });
                }
            });
        },
        // 在进行电子卡 迁移的时候会弹出一个验证码框 输入4位验证码以后将输入的值分发在这个方法里
        migrationVerificationCode(e) {
            this.migrationCode = e;
            // moveAnECard
            let params = {
                token: this.$store.state.token,
                idType: '1',
                idNum: this.applyInfo.idNumber,
                verifyCode: e,
                phone: this.migrationPhoneNumber,
            };
            moveAnECard(params).then(res => {
                if (res.status == 0) {
                    this.showCodeWindow = false;
                    wx.showModal({
                        title: '提示',
                        content: `迁移成功`,
                        confirmText: '确定',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: res => {
                            if (res.confirm) {
                                console.log(res, 'resresresrersresrersrersrersrers');
                                this.$store.dispatch('card/getAllCardList');
                                uni.redirectTo({
                                    url: '/packages/oil-card/pages/manage-oilcard/main',
                                });
                                // this.$store.dispatch("card/getAllCardList");
                                this.showCodeWindow = false;
                                // this.applyInfo = {};
                            } else if (res.cancel) {
                                // 目前什么也不做处理
                            }
                        },
                    });
                } else {
                    console.log(this.$refs.windows.password, 'this.$refs.windows.password');
                    wx.showModal({
                        title: '提示',
                        content: res.info,
                        confirmText: '确定',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: res => {
                            this.$refs.windows.password = '';
                        },
                    });
                }
            });
        },
        // 获取验证码
        async getVerificationCode() {
            if (this.isPilotInstitutionsFlag) {
                this.$store.commit('setCloseDialog', true);
                return;
            }
            // uni.showToast({
            //   title: "当前证件号与已绑卡证件号不一致",
            //   icon: "none",
            // })
            // this.showCodeWindow = true
            // return
            // 校验名字
            if (this.applyInfo.name === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.name !== '' && !this.$test.checkName(this.applyInfo.name)) {
                uni.showToast({
                    title: '姓名格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // // 校验身份证号
            if (this.applyInfo.idNumber === '') {
                uni.showToast({
                    title: '身份证号为空，请输入身份证号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (!this.$test.checkIdCard(this.applyInfo.idNumber, '1')) {
                uni.showToast({
                    title: '身份证号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 校验开卡机构
            if (this.applyInfo.provinces === '') {
                uni.showToast({
                    title: '开卡机构不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 手机号
            if (this.applyInfo.mobilePhoneNo === '') {
                uni.showToast({
                    title: '手机号不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.mobilePhoneNo !== '' && !this.$test.checkMobilePhoneNumber(this.applyInfo.mobilePhoneNo)) {
                uni.showToast({
                    title: '手机号格式不正确',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let params = {
                token: this.$store.state.token,
                idType: '1',
                idNum: this.applyInfo.idNumber,
            };
            checkVerificationCode(params).then(res => {
                if (res.status === 0) {
                    // 验证码倒计时
                    const TIME_COUNT = 60;
                    if (!this.timer) {
                        this.count = TIME_COUNT;
                        this.numShow = false;
                        this.timer = setInterval(() => {
                            if (this.count > 0 && this.count <= TIME_COUNT) {
                                this.count--;
                            } else {
                                this.numShow = true;
                                clearInterval(this.timer);
                                this.timer = null;
                            }
                        }, 1000);
                    }
                } else {
                    if (res.errorCode == '88800201') {
                        this.migrationPhoneNumber = res.info;
                        wx.showModal({
                            title: '提示',
                            content: `当前证件号已申请电子卡,是否将卡片迁移至${this.applyInfo.mobilePhoneNo.replace(
                                /(\d{3})\d{4}(\d{4})/,
                                '$1****$2',
                            )}账户下？`,
                            confirmText: '确定',
                            confirmColor: '#FF8200',
                            showCancel: true,
                            success: res => {
                                if (res.confirm) {
                                    this.showCodeWindow = false;
                                    let params = {
                                        token: this.$store.state.token,
                                        phone: this.migrationPhoneNumber,
                                        channelCode: '1004',
                                    };
                                    moveAnECardCode(params).then(res => {
                                        if (res.status === 0) {
                                            // 验证码倒计时
                                            const TIME_COUNT = 60;
                                            if (!this.timer) {
                                                this.count = TIME_COUNT;
                                                this.numShow = false;
                                                this.timer = setInterval(() => {
                                                    if (this.count > 0 && this.count <= TIME_COUNT) {
                                                        this.count--;
                                                    } else {
                                                        this.numShow = true;
                                                        clearInterval(this.timer);
                                                        this.timer = null;
                                                    }
                                                }, 1000);
                                            }
                                            // 验证码已发送至手机号: 156****8601
                                            this.reservedPhoneNumber = res.data;
                                            this.showCodeWindow = true;
                                        }
                                    });
                                } else if (res.cancel) {
                                    this.showCodeWindow = false;
                                }
                            },
                        });
                    } else {
                        wx.showModal({
                            title: '提示',
                            content: res.info,
                            confirmText: '确定',
                            confirmColor: '#FF8200',
                            showCancel: false,
                            success: res => {},
                        });
                    }
                }
            });
        },
        // // 判断是否升级
        // async isUpgrade() {
        //     let params = {
        //         cityName: this.pilotInstitutions,
        //     };
        //     console.log(params.cityName, '试点机构是什么');
        //     let res = await getCityFlag(params);
        //     if (res.status == 0 && res.data.flag == 1) {
        //         console.log(res.data.flag, '当前地区是试点地区吗');
        //         // 打开升级弹窗
        //         this.$store.commit('setCloseDialog', true);
        //     } else {
        //         this.agreedToOpen();
        //     }
        // },
        // 关闭升级新站弹窗
        closeNewStationDialog() {
            this.$store.commit('setCloseDialog', false);
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                uni.reLaunch({
                    url: '/pages/home/<USER>',
                });
                this.$store.dispatch('setOfficialAccountParams', '');
            } else {
                uni.navigateBack();
            }
        },
        // 立即升级
        async submitUpgrade() {
            this.$store.dispatch('getToken3', 'upgrade');
        },
        // 同意协议并刷脸开通
        agreedToOpen() {
            // let params = {}
            // uni.redirectTo({
            //   url: `/packages/ecard-apply/pages/transferPage/main?params=${JSON.stringify(
            //     params
            //   )}&id=${0}`,
            // });
            // return
            if (!this.agreementchecked) {
                uni.showToast({
                    icon: 'none',
                    title: '请先勾选中石油电子卡服务协议',
                });
                return;
            }
            // 校验名字
            if (this.applyInfo.name === '') {
                uni.showToast({
                    title: '姓名不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.name !== '' && !this.$test.checkName(this.applyInfo.name)) {
                uni.showToast({
                    title: '姓名格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 校验身份证号
            if (this.applyInfo.idNumber === '') {
                uni.showToast({
                    title: '身份证号为空，请输入身份证号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.idNumber !== '' && !this.$test.checkIdCard(this.applyInfo.idNumber, '1')) {
                uni.showToast({
                    title: '身份证号格式错误',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 校验开卡机构
            if (this.applyInfo.provinces === '') {
                uni.showToast({
                    title: '开卡机构不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 手机号
            if (this.applyInfo.mobilePhoneNo === '') {
                uni.showToast({
                    title: '手机号不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.mobilePhoneNo !== '' && !this.$test.checkMobilePhoneNumber(this.applyInfo.mobilePhoneNo)) {
                uni.showToast({
                    title: '手机号格式不正确',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            // 判断验证码长度不能小于4位
            if (this.applyInfo.codeNumber === '') {
                uni.showToast({
                    title: '验证码不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            } else if (this.applyInfo.codeNumber.length < 4) {
                uni.showToast({
                    title: '验证码格式不正确,验证码长度为4位',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }

            this.face();
        },
        // 刷人脸
        face() {
            wx.startFacialRecognitionVerify({
                name: this.applyInfo.name, //姓名
                idCardNumber: this.applyInfo.idNumber, //身份证号
                success: res => {
                    // 验证成功后触发
                    this.openCardInterface(res.verifyResult);
                    // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                    setTimeout(() => {
                        // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                        console.log(res);
                    }, 500);
                },
                fail: err => {
                    // 验证失败时触发
                    // err 包含错误码，错误信息，弹窗提示错误
                    setTimeout(() => {
                        console.log(err);
                        wx.showModal({
                            title: '提示',
                            content: err.ErrorMsg,
                            showCancel: false,
                        });
                    }, 500);
                },
            });
        },
        // 人脸识别通过后调用开卡接口
        openCardInterface(verifyResult) {
            try {
                wx.showLoading();
                let arr = [];
                this.carInfo.map(item => {
                    if (item.checked) {
                        arr.push({
                            carLicense: item.carNo,
                            oilCode: item.oilNo,
                        });
                    }
                });
                let params = {
                    idType: '1', // 卡类型
                    verifyResult: verifyResult, //  人脸识别后的码
                    idNum: this.applyInfo.idNumber, //  身份证号
                    idName: this.applyInfo.name, // 姓名
                    invoiceType: '1', // 开票类型
                    orgCode: this.applyInfo.provincesCode, // 开卡机构（编码
                    orgName: this.applyInfo.provinces, // 开卡机构(省市中文)
                    carInfo: JSON.stringify(arr), // 车牌号
                    position: this.applyInfo.currentPosition, // 当前位置
                    staffReferralCode: this.applyInfo.referralCode, // 员工推荐码
                    verifyCode: this.applyInfo.codeNumber, // 验证码
                    stationCode: this.applyInfo.currentPositionCode, // 当前位置code码
                };

                verifictoApplyForAnEcard(params).then(async res => {
                    if (res.status === 0) {
                        await this.$store.dispatch('card/getAllCardList');
                        // this.applyInfo = {};
                        params.cardNo = res.data;
                        console.log(params, '电子卡开卡跳转时携带的参数');
                        wx.hideLoading();
                        uni.redirectTo({
                            url: `/packages/ecard-apply/pages/transferPage/main?params=${JSON.stringify(params)}&id=${0}`,
                        });
                    } else {
                        if (res.status == -1) {
                            wx.hideLoading();
                            wx.showModal({
                                title: '提示',
                                content: res.info,
                                showCancel: false,
                            });
                        }
                        console.log(res, '开通电子卡报错');
                    }
                });
            } catch (error) {
                console.log(error, '开通电子卡报错--catch');
                wx.hideLoading();
            }
        },
        // 车牌管理
        licenseManagement() {
            uni.navigateTo({
                url: `/packages/ecard-apply/pages/managementTheVehicle/main?car=${JSON.stringify(this.carInfoArr)}`,
            });
        },
        // 更改油站
        async changeStation() {
            await this.$store.dispatch('initLocation', {
                callback: () => {
                    uni.navigateTo({
                        url: `/packages/location/pages/home/<USER>
                    });
                },
                val: 'loactionFlag',
            });
        },
        // 对车牌管理页面分发出来的参数进行操作  只取checked为true的车辆
        dealWithLicensePlate() {
            this.carInfoArr = [];
            let arr = [];
            this.carInfo.map(item => {
                if (item.checked) {
                    arr.push(item.carNo);
                    this.carInfoArr.push({
                        carLicense: item.carNo,
                        oilCode: item.oilNo,
                    });
                }
            });
            this.applyInfo.licensePlate = arr.join(',');
            // console.log(JSON.stringify(arr2), 'LLLLLLLL');
            // console.log(this.applyInfo.licensePlate, 'this.applyInfo.licensePlate');
        },
    },
};
</script>

<style lang="scss" scoped>
page {
    height: 100%;
}
.view {
    width: 100%;
    height: 100vh;
    background-color: #f6f6f6;
    .section-box-con {
        background: #fff;
        margin: 10px;
        .section-box {
            padding: 10px 12px;
            border-top: solid 2rpx #eee;
            display: flex;
            height: 100%;
            font-size: 30rpx;
            color: #333;
            .left {
                font-size: 15px;
                color: #333;
                width: 30%;
                display: flex;
                align-items: center;
                min-width: 80px;
                .danger {
                    padding-top: 4px;
                }
                div {
                    color: #f43d45;
                }
            }
            .right {
                width: 100%;
                padding-left: 10px;
            }
            // 验证码样式
            .right-code {
                min-width: 140rpx;
                background: #f96702;
                border-radius: 8rpx;
                font-size: 24rpx;
                line-height: 52rpx;
                text-align: center;
                color: #fff;
            }
            .right-position {
                min-width: 100rpx;
                border: 1px solid;
                line-height: 20px;
                color: #f96702;
                font-size: 22rpx;
                text-align: center;
                border-radius: 8rpx;
                padding-top: 1px;
            }
        }
    }
    // 选填文字
    .section-box-text {
        font-size: 10px;
        padding-left: 10px;
        color: #f96702;
    }
    .oil-footer {
        bottom: 0;
        left: 0;
        width: 100%;
        padding-left: 10px;
        .agreement {
            font-size: 12px;
            font-weight: 500;
            color: #909090;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            position: relative;
            .agreement-txt {
                font-weight: bold;
                font-size: 14px;
                .oil-money-info {
                    color: $btn-color;
                    width: 210px;
                    text-align: left;
                    // overflow: hidden;
                }
            }
        }
        .oil-btn {
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            // padding: 0 15px env(safe-area-inset-bottom);
            padding: 0 15px;
            box-sizing: border-box;
            margin-right: 10px;

            .oil-money {
                font-size: 15px;
                font-weight: 500;
                width: 210px;
                text-align: left;
                .oil-money-bold {
                    font-size: 24px;
                }
            }
            .oli-paybtn-gray {
                // width: 120px;
                width: 700px;
                height: 44px;
                margin-top: 10px;
                margin-bottom: 10px;
                background: #ff8200;
                border-radius: 5px;
                font-size: 15px;
                font-weight: 500;
                color: #ffffff;
                line-height: 44px;
                text-align: center;
            }

            .oli-pay-info {
                background: $btn-color;
            }
        }
    }
    .reminder {
        padding-left: 10px;
        color: #808080;
        font-size: 10px;
    }
}
</style>
