<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="消费记录详情"></zj-navbar>
            <div class="f-1 bg-F7F7FB p-LR-16">
                <div class="detail-header fl-column bg-fff fl-al-cen border-rad-8">
                    <div class="header-text font-12 color-666 weight-400">支付金额</div>
                    <div class="header-number font-27 color-F93F00 weight-500 te-center">&yen;{{ 999.99 }}</div>
                    <div class="system-time fl-row fl-jus-bet fl-al-cen color-E64F22 font-16 weight-bold">
                        <div class="color-fff border-rad-10 te-center font-18 shad-ef font-style">{{ hour }}</div>: <div
                            class="color-fff border-rad-10 te-center font-18 shad-ef font-style">{{ minutes }}</div>: <div
                            class="color-fff border-rad-10 te-center font-18 shad-ef font-style">{{ seconds }}</div>:
                        <div class="color-fff border-rad-10 te-center font-18 shad-ef font-style">{{ count }}</div>
                    </div>
                </div>
                <div class="detail-body bg-fff border-rad-8">
                    <div class="body p-LR-16 font-12 color-666 weight-400">
                        <div class="body-text font-12 color-666 weight-400">消费记录</div>
                        <div class="fl-column">
                            <div class="body-img-wrap fl-row fl-jus-sta fl-al-cen">
                                <img src="../../images/logo.png" alt />
                                <div class="font-18 color-333 weight-600">现场演示环境加油站</div>
                            </div>
                            <div class="detail-info fl-row fl-jus-bet fl-al-sta">
                                <div class="font-12 color-666 weight-400">订单号</div>
                                <div class="font-12 color-666 weight-400">1321321316541321</div>
                            </div>
                            <div class="detail-info fl-row fl-jus-bet fl-al-sta">
                                <div class="font-12 color-666 weight-400">油品</div>
                                <div class="font-12 color-666 weight-400">95号汽油</div>
                            </div>
                            <div class="detail-info fl-row fl-jus-bet fl-al-sta">
                                <div class="font-12 color-666 weight-400">交易类型</div>
                                <div class="font-12 color-666 weight-400">移动支付</div>
                            </div>
                            <div class="detail-info fl-row fl-jus-bet fl-al-sta">
                                <div class="font-12 color-666 weight-400">账户类型</div>
                                <div class="font-12 color-666 weight-400">备用金</div>
                            </div>
                            <div class="detail-info fl-row fl-jus-bet fl-al-sta">
                                <div class="font-12 color-666 weight-400">支付时间</div>
                                <div class="font-12 color-666 weight-400">22:54</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            timer: null,
            // 倒计时展示绑定值
            hour: '00',
            minutes: '00',
            seconds: '00',
            count: '00',
        };
    },
    created() {
        clearInterval(this.timer);
    },
    mounted() {
        this.goto();
    },
    methods: {
        goto() {
            this.timer = setInterval(() => {
                this.count = Number(this.count);
                if (this.count < 99) {
                    this.count = this.count + 1;
                } else {
                    this.count = 0;
                }
                if (this.count < 10) {
                    this.count = '0' + this.count;
                }
                var date = new Date();
                this.hour = date.getHours(); // 时
                if (this.hour >= 0 && this.hour <= 9) {
                    this.hour = '0' + this.hour;
                }
                this.minutes = date.getMinutes(); // 分
                if (this.minutes >= 0 && this.minutes <= 9) {
                    this.minutes = '0' + this.minutes;
                }
                this.seconds = date.getSeconds(); //秒
                if (this.seconds >= 0 && this.seconds <= 9) {
                    this.seconds = '0' + this.seconds;
                }
            }, 10);
        },
    },
    // 销毁计时器
    beforeDestroy() {
        clearInterval(this.timer);
    },
};
</script>

<style scoped lang="scss">
.view {
    .detail-header {
        width: 100%;
        height: 161px;
        margin-top: 16px;

        .system-time {
            width: 375px;
            height: 80px;
            // background: #ffffff;
            margin-bottom: 20px;
            // display: flex;
            // justify-content: space-between;
            // align-items: center;
            // color: #e64f22;
            // font-size: 16px;
            // font-weight: bold;
            padding: 0 70px;

            div {
                width: 44px;
                height: 44px;
                // color: #ffffff;
                // border-radius: 10px;
                // text-align: center;
                line-height: 44px;
                // font-size: 18px;
                // font-weight: bold;
                background-image: linear-gradient(288deg, #ff501c 0%, #ff7b33 100%);
                // box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            }
        }

        .header-text {
            margin-top: 16.5px;
        }

        .header-number {
            height: 43.5px;
            margin-top: 4.5px;
            line-height: 43.5px;
        }
    }

    .detail-body {
        width: 100%;
        height: 212px;
        margin-top: 12px;

        .body {
            .body-text {
                padding-top: 16px;
            }

            .body-img-wrap {
                width: 100%;
                margin-top: 18.5px;
                margin-bottom: 16px;

                img {
                    width: 21.5px;
                    height: 21.5px;
                    margin-right: 8px;
                }
            }

            .detail-info {
                width: 100%;
                height: 16.5px;
                margin-bottom: 8px;
            }
        }
    }
}</style>
