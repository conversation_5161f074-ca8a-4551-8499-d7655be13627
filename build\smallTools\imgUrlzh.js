var fs = require('fs');
var path = require('path');

//解析需要遍历的文件夹
var filePath = path.resolve('./src');
console.log(filePath);
fileDisplay(filePath);
let num = 0;
function fileDisplay(filePath) {
    fs.readdir(filePath, (err, files) => {
        if (err) {
            console.warn(err);
        } else {
            //遍历读取到的文件列表
            files.forEach(function (filename) {
                //获取当前文件的绝对路径
                var filedir = path.join(filePath, filename);
                //根据文件路径获取文件信息，返回一个fs.Stats对象
                let stats = fs.statSync(filedir);
                let isFile = stats.isFile(); //是文件
                let isDir = stats.isDirectory(); //是文件夹
                if (isFile) {
                    if (filedir.endsWith('.vue')) {
                        let contentText = fs.readFileSync(filedir, 'utf8');
                        let writeContentText = '';
                        while (1) {
                            let matchReg = /<img.*?(?:>|\/>)/i; // 匹配图片中的img标签
                            let img = contentText.match(matchReg); //拿到标签中所有的img
                            if (!img) {
                                break;
                            }
                            let contentTextS = contentText.substring(0, img.index);
                            let contentTextE = contentText.substring(img.index + img[0].length);
                            console.log(img);
                            console.log(contentText.substring(0, img.index + img[0].length));
                            let imgStr = img[0];
                            console.log(imgStr);
                            let srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i; // 匹配图片中的src
                            let src = imgStr.match(srcReg);

                            let srcStr = src[1];
                            if (srcStr.substring(0, 7) == '/static') {
                                imgStr = imgStr.replace(srcStr, `@${srcStr}`);
                            }
                            if (srcStr.substring(0, 6) == 'static') {
                                imgStr = imgStr.replace(srcStr, `@/${srcStr}`);
                            }
                            contentText = contentTextE;
                            writeContentText = writeContentText + contentTextS + imgStr;
                        }
                        fs.writeFileSync(filedir, writeContentText + contentText);
                        // if (arr2 != null) {
                        //   for (let i = 0; i < arr2.length; i++) {
                        //     let src = arr2[i].match(srcReg);
                        //     let srcStr = src[1]

                        //   }
                        // }
                        // console.log('------',filedir)

                        num++;
                        console.log('---------------', num);
                    }
                }
                if (isDir) {
                    fileDisplay(filedir); //递归，如果是文件夹，就继续遍历该文件夹下面的文件
                }
            });
        }
    });
}
