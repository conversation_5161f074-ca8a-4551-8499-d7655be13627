export default {
  // #ifdef MP-WEIXIN
  onLoad () {
    // 获取键盘的实例
    let result = this.selectComponent('#passwordKeyboardId');
    // 调用账户插件API
    this.$sKit.keyBordPlugin.initRef(result);
    this.passwordKeyboardRef = result;
  },
  methods: {
    // 确认修改
    async confirmBtn () {
      let samepassword = this.passwordKeyboardRef.equal('password_unique1', 'password_unique2');
      if (!this.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none',
        });
        return;
      } else {
        if (this.newPasswordLength !== 6) {
          uni.showToast({
            title: '请输入6位新密码',
            icon: 'none',
          });
          return;
        }
      }
      if (!this.confirmPassword) {
        uni.showToast({
          title: '请再次输入新密码',
          icon: 'none',
        });
        return;
      } else {
        if (this.confirmNewPasswordLength !== 6) {
          uni.showToast({
            title: '请再次输入6位新密码',
            icon: 'none',
          });
          return;
        }
      }
      if (samepassword == 1) {
        this.newPassword = '';
        this.newPasswordShow = '';
        this.confirmNewPassword = '';
        this.confirmNewPasswordShow = '';
        uni.showToast({
          title: '两次密码不一致，请重新输入',
          icon: 'none',
        });
        return;
      }
      // 打开人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', true)
    },
    enterNavEvent () {
      // 关闭人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', false)
      // 支付宝小程序实名--初始化实人认证--人脸验证-实名认证方法
      this.$sKit.wxRealPersonAuthentication.startVerification(this.personInfo).then(response => {
        // console.log('链路通过，调用卡通电子账户接口', response);
        if (response.success) {
          this.submitForgotPassword(response.data.authInfo);
        } else {
        }
      });
    },
  },
  // #endif
};
