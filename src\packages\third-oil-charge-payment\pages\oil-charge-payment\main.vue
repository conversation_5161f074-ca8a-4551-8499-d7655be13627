<!--created by lq 2023/1/17-->
<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="oil-charge-payment">
            <zj-navbar :height="44" title="支付订单" :border-bottom="false"></zj-navbar>
            <div class="content">
                <div class="scroll-content" v-if="Object.keys(unPaidInfo).length > 0">
                    <p class="title let-spacing">
                        <span class="unit">&yen;</span>
                        <span class="price font-style">{{ orderPayMoney }}</span>
                    </p>
                    <p class="left-time-content" v-if="orderStatus == 1 && lefttime && lefttime > 0">
                        请在
                        <span class="time">{{ leftTimeStr }}</span
                        >秒内完成支付
                    </p>
                    <div class="order-info card-shadow">
                        <div class="cell">
                            <p class="left">订单金额</p>
                            <p class="right color-333 let-spacing">&yen;{{ unPaidInfo.orderMoney }}</p>
                        </div>
                        <div class="line"></div>
                        <div
                            class="cell"
                            v-for="(item, index) in unPaidInfo.productList[0].activityDiscountList"
                            :key="index"
                            v-if="item.shareMoney !== null"
                        >
                            <p class="left">{{ item.activityTypeDesc || '' }}</p>
                            <p class="right color-E64F22">-&yen;{{ item.shareMoney }}</p>
                        </div>
                        <div class="line" v-if="unPaidInfo.productList[0].activityDiscountList.length > 0"></div>
                        <!-- #ifdef MP-MPAAS  -->
                        <div class="cell" v-if="isVerifyCoupons">
                            <p class="left">优惠券</p>
                            <div class="right">
                                <p class="normal">-&yen;{{ verifiedCoupon.couponDiscount || getSelectedCouponName() }}</p>
                            </div>
                        </div>
                        <div class="cell" v-else @click="chooseTicketAction">
                            <p class="left">优惠券</p>
                            <div class="right right_expRemDiv" v-if="unPaidInfo">
                                <div class="item_expRemDiv">
                                    <p
                                        class="normal coupon_text"
                                        :style="{
                                            color: unPaidInfo.couponList && unPaidInfo.couponList.length > 0 ? '#E64F22' : '#999',
                                        }"
                                    >
                                        {{ getSelectedCouponName() }}</p
                                    >
                                    <p class="expRemText" v-if="unPaidInfo.couponList && unPaidInfo.couponList.length > 0">
                                        {{ expRemText }}</p
                                    >
                                </div>
                                <div class="arroe-right-666" v-if="unPaidInfo.couponList.length > 0"></div>
                            </div>
                        </div>
                        <!-- #endif -->
                        <!-- #ifndef MP-MPAAS -->
                        <div class="cell" v-if="isVerifyCoupons">
                            <p class="left">优惠券</p>
                            <div class="right">
                                <p class="normal">-&yen;{{ wxSecondaryPaymentObj.couponDiscount || getSelectedCouponName() }}</p>
                            </div>
                        </div>
                        <div class="cell" v-else @click="chooseTicketAction">
                            <p class="left">优惠券</p>
                            <div class="right right_expRemDiv" v-if="unPaidInfo">
                                <div class="item_expRemDiv">
                                    <p
                                        class="normal coupon_text"
                                        :style="{
                                            color: unPaidInfo.couponList && unPaidInfo.couponList.length > 0 ? '#E64F22' : '#999',
                                            marginTop: expRemText ? ' 13px' : '',
                                        }"
                                    >
                                        {{ getSelectedCouponName() }}</p
                                    >
                                    <p class="expRemText" v-if="unPaidInfo.couponList && unPaidInfo.couponList.length > 0">
                                        {{ expRemText }}</p
                                    >
                                </div>
                                <div class="arroe-right-666" v-if="unPaidInfo.couponList.length > 0"></div>
                            </div>
                        </div>
                        <!-- #endif -->
                        <div v-if="stationType && stationType == 1 && giftCardUse && giftVerified != 0">
                            <div class="line"></div>
                            <div class="cell">
                                <p class="left">礼品卡</p>
                                <div class="right">
                                    <template>
                                        <p class="normal">-&yen;{{ giftVerified }}</p>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <!-- 只有新站支持礼品卡支付 -->
                        <div v-if="stationType && stationType == 1 && giftCardUse">
                            <div class="line"></div>
                            <div class="cell" @click="cardChoose">
                                <p class="left">礼品卡</p>
                                <div class="right">
                                    <template v-if="giftCardDeductAmount == 0">
                                        <p class="color-E64F22" v-if="consumeCardNumber">{{ consumeCardNumber }}张可用</p>
                                        <p class="normal color-E64F22" v-else>无可用</p>
                                    </template>
                                    <template v-else>
                                        <p class="color-E64F22">-&yen;{{ giftCardDeductAmount }}</p>
                                    </template>
                                    <div class="arroe-right-666" v-if="consumeCardNumber != ''"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pay-info card-shadow" v-if="orderPayMoney != 0">
                        <div v-for="(item, index) in payList" :key="index" @click="curPayChannelClick(item)">
                            <div :class="item.payType == '5' && !setPayDetails(item) ? 'openWalletCell' : 'cell'" class="ov-hid">
                                <div class="left ov-hid">
                                    <div class="f-1 fl-row fl-al-cen ov-hid">
                                        <div v-if="item.payType == '1'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/pyament-wechat.png" alt />
                                            <img v-else class="pay-icon" src="../../image/pyament-wechat-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '5'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/pyament-oilwallet.png" alt />
                                            <img v-else class="pay-icon" src="../../image/pyament-oilwallet-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '6'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/oil-card.png" alt />
                                            <img v-else class="pay-icon" src="../../image/oil-card-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '2'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/pyament-zfb.png" alt />
                                            <img v-else class="pay-icon" src="../../image/pyament-zfb-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '8'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/yun_unionpay.png" alt />
                                            <img v-else class="pay-icon" src="../../image/yun_unionpay-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '4'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/kunpeng.png" alt />
                                            <img v-else class="pay-icon" src="../../image/kunpeng-gray.png" alt />
                                        </div>
                                        <div v-else-if="item.payType == '23'">
                                            <img v-if="item.isCanSelect" class="pay-icon" src="../../image/icbc.png" alt />
                                            <img v-else class="pay-icon" src="../../image/icbc-gray.png" alt />
                                        </div>
                                        <div class="width100 fl-column">
                                            <div class="title-c fl-row fl-al-sta">
                                                <span>{{ item.payTypeName }}</span>
                                                <span v-if="item.payType == '5' && setPayDetails(item)">
                                                    <!-- (&yen;{{balanceData.walletBalance}}) -->
                                                    <template v-for="cardItem in item.memberAssets.details">
                                                        (&yen;{{ cardItem.amount }})
                                                    </template>
                                                </span>
                                                <span v-if="item.payType == '5'" class="recommend">推荐</span>
                                                <span
                                                    v-if="isShowChannelDescription(item)"
                                                    class="wxText"
                                                    :class="item.isCanSelect ? 'description-color' : 'opcity3'"
                                                    >{{ item.channelDescription }}</span
                                                >
                                                <span v-if="item.payType == '6' && !setPayDetails(item)">(请绑定加油卡)</span>
                                                <span v-if="item.payType == '6' && setPayDetails(item)">
                                                    ({{ item.memberAssets.details.length }}张)
                                                </span>
                                                <span v-else></span>
                                            </div>
                                            <p
                                                v-if="item.payType == '5' && item.channelDescription && setPayDetails(item)"
                                                class="marketingCopy"
                                                :class="item.isCanSelect ? 'description-color' : 'opcity3'"
                                                >{{ item.channelDescription }}</p
                                            >
                                            <img
                                                class="openImg"
                                                v-if="item.payType == '5' && !setPayDetails(item)"
                                                src="../../image/openWalletTip.png"
                                                alt=""
                                            />
                                        </div>
                                    </div>
                                    <div v-if="item.payType != '6' && item.isCanSelect">
                                        <img
                                            class="selicon"
                                            src="../../image/oil-ticket-selected.png"
                                            v-if="curPayChannelInfo.payType == item.payType"
                                            alt
                                        />
                                        <img class="selicon" src="../../image/oil-ticket-unselect.png" alt v-else />
                                    </div>
                                </div>
                                <template v-if="item.payType == '6' && item.memberAssets">
                                    <img class="arrow-right" v-if="!isShowOil" src="../../image/arrowhead.png" alt="" />
                                    <img class="arrowdown" v-else src="../../image/arrow_down.png" alt="" />
                                </template>
                            </div>
                            <div class="oil-cell" v-if="item.payType == '6' && isShowOil">
                                <template v-for="(oilItem, icx) in item.memberAssets.details">
                                    <div
                                        class="left"
                                        v-if="item.payType == '6'"
                                        @click.stop="oilTypeClick(oilItem, item.isCanSelect)"
                                        :key="icx"
                                    >
                                        <div v-if="item.isCanSelect">
                                            <img
                                                class="selicon"
                                                src="../../image/oil-ticket-selected.png"
                                                v-if="curCardPayInfo.fuelCardNo == oilItem.fuelCardNo"
                                                alt
                                            />
                                            <img class="selicon" src="../../image/oil-ticket-unselect.png" alt v-else />
                                        </div>
                                        <div class="fl-column">
                                            <p class="title-c">
                                                <span>{{ oilItem.fuelCardNo }}</span>
                                                <span>(&yen;{{ oilItem.amount }})</span>
                                            </p>
                                            <p
                                                v-if="item.channelDescription"
                                                class="marketingCopy"
                                                :class="item.isCanSelect ? 'description-color' : 'opcity3'"
                                                >{{ item.channelDescription }}</p
                                            >
                                        </div>
                                    </div>
                                </template>
                            </div>
                            <div class="line" v-if="index != payList.length - 1"></div>
                        </div>
                    </div>
                    <div class="fl-row fl-al-jus-cen mart12" v-if="false">
                        <div class="font-12 color-999 weight-400">查看全部</div>
                        <div class="arrow-down-10 marbl38"></div>
                    </div>
                </div>
                <div class="bottom-area">
                    <div class="left-btn" @click="backEvent" v-if="unPaidInfo.cancel">取消订单</div>
                    <div class="sure-area" :class="{ btnColor: isCanClickPay, 'bg-opacity-288': !isCanClickPay }" @click="payAction"
                        >确认支付</div
                    >
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <zj-unrealized-authentication v-if="realNameDialogFlag" @realNameDialogClose="realNameDialogClose" @realNameInfo="realNameInfo">
            </zj-unrealized-authentication>
            <div class="mask" v-if="!isCanClickPay && !isHarmony"></div>
            <!-- #ifdef MP-MPAAS  -->
            <div v-if="isHarmony">
                <keyboard-plugin></keyboard-plugin>
            </div>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="safeKeyboardIdEoil" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="safeKeyboardIdEoil" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <cloudKeyBordPlugin ref="cloudKeyboardRef"></cloudKeyBordPlugin>
            <!-- #endif -->
            <zj-old-account v-if="isTransfer"></zj-old-account>
            <zj-agreement @enterNavEvent="enterNavEvent" @cancelClick="cancelClick"></zj-agreement>
        </div>
    </div>
</template>

<script>
const currency = require('currency.js');
import {
    cancelOrderApi,
    statuslOrderApi,
    unPaidOrdeQueryrApi,
    calculateOrdeDiscountsApi,
} from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { balance } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import { mapState, mapGetters } from 'vuex';
import CONFIG from '../../../../s-kit/js/third-config.js';
import checkFKArgs from '../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import { availableConsumeNumber } from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import Vue from 'vue';
import { clientCode } from '../../../../../project.config';
import { paymentMarketing } from '../../../../s-kit/js/v3-http/https3/order/index';
// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-oil-charge.js';
// #endif
// #ifndef MP-MPAAS  || H5-CLOUD
import wxMixin from './diff-environment/wx-oil-charge.js';
import zfbMixin from './diff-environment/zfb-oil-charge.js';
// #endif
// #ifdef H5-CLOUD
import cloudKeyBordPlugin from '../../../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue';
import cloudMixin from './diff-environment/cloud-oil-charge.js';
// #endif
// #ifdef MP-WEIXIN
const payPlugin = requirePlugin('pay-plugin');
// #endif
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        wxMixin,
        zfbMixin,
        // #endif
        // #ifdef H5-CLOUD
        cloudMixin,
        // #endif
    ],
    data() {
        return {
            //待支付订单详情
            unPaidInfo: {},
            payType: 0,
            orderStatus: 1,
            leftTimeStr: null,
            lefttime: 300,
            payList: [],
            curPayChannelInfo: '',
            businessDay: '', //营业日，因为重新计算优惠没有返回，所以保存下
            gunNo: '', //枪号，因为重新计算优惠没有返回，所以保存下
            transNo: '', //锁单用到
            originalOrder: null, //待结交易里原始订单
            requestStatusInterval: null,
            isToSelectTicketPage: false,
            pollTime: 3000,
            // accountDataPlugin: '',
            beginTime: 0,
            isOrderShow: false,
            loadingText: '加载中',
            isShowOil: false, //显示油卡信息
            fuelCardNo: '', //加油卡号
            curCardPayInfo: '',
            timerInter: null,
            ktOrQyFlag: false, // 开通或迁移后刷新支付方式
            verifiedCoupon: null, //该订单已核销的电子券
            walletInfo: null, //钱包信息
            realNameDialogFlag: '', //未实名弹窗(输入姓名和身份证号)
            paymentLock: true,
            // 三方支付未响应是否在onshow中刷新
            isRefresh: false,
            // 是否能点击确认支付
            isCanClickPay: false,
            isVerifyCoupons: true,
            isPaying: false,
            // 查询用户名下可用礼品卡数量
            consumeCardNumber: '',
            // 礼品卡与实付金额计算后的金额
            orderPayMoney: 0,
            // 选择礼品卡的总扣减金额
            giftCardDeductAmount: 0,
            // 选择礼品卡的总金额
            giftCardTotalAmount: 0,
            // 控制跳转礼品卡
            isToSelectGiftCardPage: false,
            expRemText: '',
            expRemIdList: [],
            stationType: '', //新老站
            giftCardUse: true, //礼品卡功能是否可用，主要是app上判断版本号
            giftVerified: 0, //礼品卡已核销金额
            payPlugin: {}, // 微信支付宝插件实例
            refer: '',
            bizContent: '',
            recharged: false,
            offlineOrder: false,
        };
    },
    computed: {
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
            cityName: state => state.locationV3_app.cityName, //风控经度
            isHarmony: state => state.thirdIndex.isHarmony,
            // selectMarkerV3: state => state.locationV3_app.selectMarkerV3
        }),
        ...mapGetters(['walletStatus', 'selectTickets', 'selectCard']),
    },
    components: {
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin,
        // #endif
    },
    //生命周期 - 创建完成（访问当前this实例）
    async created() {
        this.timerInter = null;
        // #ifdef MP-MPAAS
        this.giftCardUse = await this.$cnpcBridge.judgeProtocolCall('3.6.4');
        // #endif
    },
    watch: {
        unPaidInfo: {
            handler(newName, oldName) {
                if (JSON.stringify(newName) === '{}') {
                    this.isVerifyCoupons = true;
                    return;
                }
                if (newName.verifyCoupons ? newName?.verifyCoupons.length > 0 : false) {
                    this.isVerifyCoupons = true;
                } else {
                    this.isVerifyCoupons = false;
                }
            },
            immediate: true,
            deep: true,
        },
    },
    async onShow() {
        // await this.$store.dispatch('getAccountBalanceAction');
        if (this.selectTickets && this.isToSelectTicketPage && this.isCanClickPay) {
            this.isCanClickPay = false;
            this.$store.dispatch('selectCardAction', []);

            let selectTicketsInfo = JSON.parse(decodeURIComponent(this.selectTickets));
            this.calculateOrdeDiscountsPost(selectTicketsInfo);
            this.isToSelectTicketPage = false;
            this.$store.dispatch('selectTicketsAction', undefined);
            /**
             * 客户累计选择的礼品卡大于实付金额，礼品卡扣减金额为实付金额，订单实付显示0元，
             * 累计选择的礼品卡金额小于实付金额，礼品卡扣减金额为所选礼品卡金额总额，
             * 实付金额为当前实付减所选礼品卡后的金额
             * */
            // this.unPaidInfo.payMoney = Number
        } else if (this.isPaying == false) {
            this.isCanClickPay = true;
        }
        if (this.selectCard.length > 0 && this.isToSelectGiftCardPage && this.isCanClickPay) {
            console.log('this.selectCard', this.selectCard);
            this.calculateGiftCard();
            this.isToSelectGiftCardPage = false;
        } else {
            console.log('这里了吗');
            this.$store.dispatch('selectCardAction', []);
            this.isToSelectGiftCardPage = false;
            this.calculateGiftCard();
            // this.$store.dispatch('selectCardAction', []);
        }
        if (this.recharged) {
            this.getBuyPayTypeList();
            this.recharged = false;
        }
    },
    //生命周期 - 挂载完成（访问DOM元素）
    async onLoad(options) {
        this.backEvent = this.$sKit.commonUtil.throttleUtil(this.backEvent);
        this.payAction = this.$sKit.commonUtil.throttleUtil(this.payAction);
        this.$store.dispatch('selectCardAction', []);
        console.log('options====', options);
        let optionsData;
        // #ifdef MP-ALIPAY
        // 支付宝referer过长兼容
        my.hideBackHome();
        const nowPage = getCurrentPages().pop();
        optionsData = getApp().globalData.query?.[nowPage.$page.fullPath];

        if (!optionsData?.zfbRefererMax) {
            optionsData = JSON.parse(decodeURIComponent(options.data));
        }
        // #endif
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            const nowPage = getCurrentPages().pop();
            optionsData = getApp().globalData.query?.[nowPage.$page.fullPath];
            if (!optionsData?.zfbRefererMax) {
                optionsData = JSON.parse(decodeURIComponent(options.data));
            }
            console.log(this.isHarmony, 'isHarmony---');
        } else {
            optionsData = JSON.parse(decodeURIComponent(options.data));
        }
        this.offlineOrder = optionsData?.offlineOrder || false;
        // #endif
        // #ifdef H5-CLOUD
        const nowPage = getCurrentPages().pop();
        optionsData = getApp().globalData.query?.[nowPage.$page.fullPath];

        if (!optionsData?.zfbRefererMax) {
            optionsData = JSON.parse(decodeURIComponent(options.data));
        }
        // #endif
        // #ifdef MP-WEIXIN
        optionsData = JSON.parse(decodeURIComponent(options.data));
        // #endif
        this.refer = optionsData.refer || '';
        console.log('optionsData---', optionsData);
        if (optionsData.unPaidInfo) {
            this.unPaidInfo = Object.freeze(optionsData.unPaidInfo);
            this.businessDay = this.unPaidInfo.businessDay;
            this.orderPayMoney = this.unPaidInfo.payMoney;
            this.calculateVerifiedGiftCard();
            console.log(this.unPaidInfo, this.unPaidInfo.orderNo, this.unPaidInfo.stationCode, 'IIIIIIIII');
            this.lefttime = this.unPaidInfo.orderExpirationTime || 0;
            if (this.lefttime != 0) this.startTimer(this.unPaidInfo);
            console.log('unPaidInfo', this.unPaidInfo);
        }
        if (optionsData.originalOrder) {
            this.originalOrder = optionsData.originalOrder;
            console.log('originalOrder', this.originalOrder);
        }
        this.stationType = optionsData.stationType;
        if (this.offlineOrder) {
            this.stationType = 1;
        }
        console.log('stationType---', this.stationType);
        // 获取电子钱包余额
        await this.getWalletBalance();
        // 遍历查询当前用户是否核销过电子券
        this.whetherToCancelElectronicVouchers(this.unPaidInfo);
        this.judgeExpRem(); //添加临期券
        // #ifdef MP-WEIXIN
        let res = await this.$sKit.wxPayPlugin?.initPayPlugin();
        if (res.code == 'PAY_SUCCESS') {
            this.payPlugin = payPlugin;
            this.getBuyPayTypeList();
        }
        // #endif
        // #ifdef MP-ALIPAY
        this.payPlugin = await this.$sKit.aliPayPlugin?.init();
        this.getBuyPayTypeList();
        // #endif
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.payPlugin = await this.$sKit.mpaasPayPlugin?.init();
            this.getBuyPayTypeList();
        } else {
            this.getBuyPayTypeList();
        }
        // #endif
        // #ifdef H5-CLOUD
        this.getBuyPayTypeList();
        // #endif
    },
    mounted() {
        if (this.stationType && this.stationType == 1 && this.giftCardUse) {
            this.getAvailableConsumeNumber();
        }
        setTimeout(() => {
            this.payOrderPageBiz();
        }, 1000);
    },
    methods: {
        payOrderPageBiz() {
            let pageID = '';
            if (this.offlineOrder) {
                pageID = 'offlinePayOrderPage';
            } else {
                pageID = 'payOrderPage';
            }
            let bizParams = {
                seed: 'hpayoilBiz',
                pageID: pageID, // 返回sdk标识
                refer: this.refer,
                channelID: clientCode,
                address: this.cityName,
            };
            if (this.bizContent) {
                bizParams.content = this.bizContent;
                bizParams.dataType = 'exposure';
            }
            this.$sKit.mpBP.tracker('后支付加油', bizParams);
        },
        isShowChannelDescription(item) {
            if (item.payType != '6' && item.channelDescription) {
                if (item.payType == '5') {
                    if (!this.setPayDetails(item)) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            }
        },
        /**
         * @description  : 获取e享卡和加油卡充值营销文案
         * @param  {string} orderNo 是 订单编号  V4.3.0
         * @param  {int} channelType 是 来源渠道 V4.3.0
         * @param  {string} stationCode 是  油站编码 V4.3.0
         * @param  {decimal} orderMoney 是 订单总金额 V4.3.0
         * @param  {decimal} remainMoney 是 剩余支付金额 V4.3.0
         * @param  {List<PayType>} payTypeList 是 支付方式列表 V4.3.0
         * @param {int} payType 是 支付方
         * @param {string} payOrderNo 是 支付订单号
         * @param {decimal} payMoney 是 支付金额
         * @param {string} cardNo 否 卡号，加油卡必填
         * @param {string} couponTemplateNo 否 券模板编码，优惠券方式必填
         * @param {string} couponNo 否 券编号，优惠券方式必填
         * @return   {*}
         */
        async getRechargeMarketingCopy() {
            console.log(this.unPaidInfo, 'this.unPaidInfo======');

            // 遍历this.payList数组，根据不同的payType值进行相应的操作
            this.payList.forEach(item => {
                if (item.payType === 5) {
                    this.eShareCardAcquisitionCopy(item);
                } else if (item.payType === 6 && item.memberAssets) {
                    this.gasCardAcquisitionCopy(item);
                }
            });
        },

        // e享卡获取文案
        eShareCardAcquisitionCopy(item) {
            // 解构this.unPaidInfo对象，方便后续使用
            let { orderNo, channelType, stationCode, orderMoney, couponList, productList } = this.unPaidInfo;

            // 初始化请求参数对象params
            let params = this.initParams(orderNo, channelType, stationCode, orderMoney, couponList, productList);

            // 处理payType等于5的情况
            let paymentMethodJson = this.handlePaymentType5(params, orderNo, orderMoney, couponList, productList);
            // 将创建好的对象添加到params的payTypeList数组中
            params.payTypeList.push(paymentMethodJson);
            console.log(params, 'e享卡=====params=====');
            this.obtainPaymentMarketingCopy(params);
        },

        // 加油卡获取文案
        gasCardAcquisitionCopy(item) {
            // 解构this.unPaidInfo对象，方便后续使用
            let { orderNo, channelType, stationCode, orderMoney, couponList, productList } = this.unPaidInfo;

            // 初始化请求参数对象params
            let paramsTemplate = this.initParams(orderNo, channelType, stationCode, orderMoney, couponList, productList);

            // 处理payType等于6且存在memberAssets属性的情况
            item.memberAssets.details.forEach(ite => {
                let params = JSON.parse(JSON.stringify(paramsTemplate));
                let paymentMethodJson = this.handlePaymentType6(orderNo, orderMoney, ite.fuelCardNo);
                // 将创建好的对象添加到params的payTypeList数组中
                params.payTypeList.push(paymentMethodJson);
                this.obtainPaymentMarketingCopy(params);
            });
        },

        // 初始化请求参数对象的函数
        initParams(orderNo, channelType, stationCode, orderMoney, couponList, productList) {
            let params = {
                orderNo,
                channelType,
                stationCode,
                orderMoney,
                remainMoney: orderMoney,
                payTypeList: [],
            };
            params.payTypeList = this.handleCouponLogic(params, orderNo, orderMoney, couponList, productList);
            return params;
        },

        // 处理payType等于5的情况的函数
        handlePaymentType5(params, orderNo, orderMoney, couponList, productList) {
            let commonPayInfo = {
                payOrderNo: orderNo,
                payMoney: orderMoney,
            };

            return {
                ...commonPayInfo,
                payType: 5,
            };
        },

        // 处理payType等于6的情况的函数
        handlePaymentType6(orderNo, orderMoney, fuelCardNo) {
            let commonPayInfo = {
                payOrderNo: orderNo,
                payMoney: orderMoney,
            };

            return {
                ...commonPayInfo,
                payType: 6,
                cardNo: fuelCardNo,
            };
        },

        // 处理优惠券逻辑的函数
        handleCouponLogic(params, orderNo, orderMoney, couponList, productList) {
            let commonPayInfo = {
                payOrderNo: orderNo,
                payMoney: orderMoney,
            };

            let payTypeList = couponList
                .filter(ite => ite.used === 1)
                .map(ite => {
                    let couponJson = {
                        ...commonPayInfo,
                        payType: 11,
                    };
                    if (productList[0].couponDiscountList[0].couponNo === ite.couponNo) {
                        couponJson.payMoney = productList[0].couponDiscountList[0].shareMoney;
                        couponJson.couponNo = ite.couponNo;
                        couponJson.couponTemplateNo = ite.couponTemplateNo;
                    }

                    return couponJson;
                });
            return payTypeList;
        },
        // 获取营销文案调用接口
        async obtainPaymentMarketingCopy(params) {
            try {
                // 5. 调用paymentMarketing函数，并等待其返回结果
                // 这里使用await关键字，因为该函数是异步函数，需要等待其完成并返回结果
                let res = await paymentMarketing(params, { isload: false, isCustomErr: true });
                if (res.success) {
                    // 6. 如果paymentMarketing函数调用成功且返回结果中的success为true
                    let arr = res.data.payTypeList;
                    this.payList = this.payList.map(item => {
                        // 6.1 创建一个item的副本，避免直接修改原对象
                        // 这样可以确保在后续操作中不会对原始的this.payList中的元素造成意外修改
                        let newItem = { ...item };
                        arr.forEach(ite => {
                            if (ite.payType === 5) {
                                // 6.2 当payType等于5时
                                // 将从返回结果中获取的折扣信息和渠道描述信息赋值给新创建的对象对应的属性
                                newItem.payDiscount = ite.payDiscount;
                                newItem.channelDescription = ite.discountInfo || '';
                            } else if (ite.payType === 6 && newItem?.memberAssets) {
                                // 6.3 当payType等于6且新创建的对象存在memberAssets属性时
                                newItem.memberAssets?.details.forEach(it => {
                                    // 将从返回结果中获取的渠道描述信息赋值给对应子对象的属性
                                    newItem.channelDescription = ite.discountInfo || '';
                                });
                            }
                        });
                        // 6.4 返回处理后的新对象，以便更新this.payList数组
                        console.log(newItem, 'newItem');
                        if (newItem.payType !== 5 && newItem.payType !== 6) {
                            newItem.channelDescription = item.channelDescription;
                        }
                        return newItem;
                    });
                    console.log(this.payList, 'this.payList===组合后的数据');
                } else {
                    // 7. 如果paymentMarketing函数调用成功但返回结果中的success为false
                    // 打印错误信息，这里可以根据实际需求添加更多的错误处理逻辑，比如给用户展示错误提示等
                    console.error('支付营销获取失败，原因：', res.message);
                }
            } catch (error) {
                // 8. 如果在调用paymentMarketing函数过程中出现错误
                // 打印错误信息，同样可以根据实际需求添加更多的错误处理逻辑
                console.error('获取支付营销信息时出错：', error);
            }
        },
        // 遍历支付方式是否存在detail
        setPayDetails(item) {
            if (item.memberAssets) {
                for (const key in item.memberAssets) {
                    if (key === 'details') {
                        if (item.memberAssets.details) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
            } else {
                return false;
            }
        },
        // 遍历查询当前用户是否核销过电子券
        whetherToCancelElectronicVouchers(value) {
            console.log(value, 'value=====');
            // verifyCoupons 有值代表有券已经核销过
            if (!value.verifyCoupons) {
                return;
            }
            if (value.verifyCoupons[0]) {
                console.log(value.verifyCoupons[0], 'value.verifyCoupons[0]====');
                // 用订单的电子券列表中的数据做遍历比对
                // 存在相同就禁止用户再次选择电子券
                value.couponList.map(item => {
                    if (value.verifyCoupons[0] == item.couponNo) {
                        this.verifiedCoupon = item;
                        this.wxSecondaryPaymentObj = item;
                    }
                });
            }
        },
        async getWalletBalance() {
            //  获取电子钱包金额
            await balance().then(res => {
                if (res.success) {
                    this.walletInfo = res.data;
                }
            });
        },
        // 户进行消费时，可以查询用户名下可用礼品卡数量，一般一个用户名下礼品卡总数量不超过99张。
        async getAvailableConsumeNumber() {
            let params = {
                stationCode: this.unPaidInfo.stationCode,
            };
            let res = await availableConsumeNumber(params, { isCustomErr: true });
            console.log('res00---', res);
            this.consumeCardNumber = res.data.availableCount;
        },

        refreshPullDown() {},
        payTitle(str) {
            return str ? str.replace(/\s*/g, '') : '';
        },
        // 订单倒计时处理
        startTimer(value) {
            this.timerInter = setInterval(() => {
                this.lefttime -= 1;
                this.refreshLeftTimeStr();
                if (this.lefttime <= 0) {
                    clearInterval(this.timerInter);
                    // 如果是券已经被核销的的时候停留在当前页面
                    if (value.verifyCoupons) {
                        if (value.verifyCoupons[0]) return;
                    }
                    this.closeEvent();
                }
                console.log('倒计时内', this.lefttime);
            }, 1000);
            this.refreshLeftTimeStr();
        },
        // 订单倒计时刷新
        refreshLeftTimeStr() {
            let minute = parseInt(this.lefttime / 60);
            let second = this.lefttime - minute * 60;
            this.leftTimeStr = this.formatTime(minute) + ':' + this.formatTime(second);
        },
        // 小于10补零操作
        formatTime(time) {
            if (time < 10) {
                return '0' + time;
            }
            return '' + time;
        },
        //加油卡卡号选择
        oilTypeClick(oilItem, isCanSelect) {
            // if(this.curPayChannelInfo.payType == '6'){
            // console.log('oilItem',Object.assign(this.curPayChannelInfo,oilItem))
            if (!isCanSelect) return;
            this.curCardPayInfo = oilItem;
        },
        /*判断电子券临期
         */
        judgeExpRem() {
            if (this.unPaidInfo && this.unPaidInfo.couponList && this.unPaidInfo.couponList.length > 0) {
                // 遍历券
                this.expRemIdList = [];
                for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                    let item = this.unPaidInfo.couponList[i];
                    if (item.expRem) {
                        this.expRemIdList.push(item.couponNo);
                    }
                }
            }
        },
        //优惠方式
        getSelectedCouponName() {
            if (this.unPaidInfo && this.unPaidInfo.couponList && this.unPaidInfo.couponList.length > 0) {
                for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                    let item = this.unPaidInfo.couponList[i];
                    if (item.used == 1) {
                        // 10一满减券;20一记次券;30-兑换券;40-折扣券
                        if (item.couponType == 10) {
                            this.bizContent = '满减券';
                        } else if (item.couponType == 20) {
                            this.bizContent = '记次券';
                        } else if (item.couponType == 30) {
                            this.bizContent = '兑换券';
                        } else if (item.couponType == 40) {
                            this.bizContent = '折扣券';
                        }
                        // return item.couponTemplateName;
                        if (this.expRemIdList.length > 0 && !this.expRemIdList.includes(item.couponNo)) {
                            this.expRemText = '您有其他电子券即将到期';
                        } else {
                            this.expRemText = '';
                        }
                        return '-￥' + item.couponDiscount;
                    }
                }
            }
            if (this.unPaidInfo && this.unPaidInfo.couponList && this.unPaidInfo.couponList.length == 0) {
                return '无可用';
            } else {
                this.expRemText = this.expRemIdList.length > 0 ? '您有其他电子券即将到期' : '';
                return '请选择优惠券';
            }
        },
        //选择支付方式
        curPayChannelClick(item) {
            if (!item || !item.isCanSelect || (item.payType == '6' && !this.setPayDetails(item))) {
                if (!item) {
                    this.curPayChannelInfo = '';
                }
                return;
            }
            this.curPayChannelInfo = item;
            if (item.payType == '6' && item.memberAssets) {
                this.isShowOil = true;
            } else {
                this.curCardPayInfo = '';
                this.isShowOil = false;
            }
        },
        //判断支付方式是否可用
        isPaymentMethodAvailable() {
            if (
                this.unPaidInfo.productList[0].couponDiscountList &&
                this.unPaidInfo.productList[0].couponDiscountList.length > 0 &&
                this.unPaidInfo.couponList &&
                this.unPaidInfo.couponList.length > 0
            ) {
                for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                    let item = this.unPaidInfo.couponList[i];
                    if (item.used == 1) {
                        if (item.couponPayTypeList && item.couponPayTypeList.length > 0) {
                            for (let j = 0; j < this.payList.length; j++) {
                                this.payList[j].isCanSelect = Boolean(
                                    item.couponPayTypeList.find(item2 => {
                                        return item2 == this.payList[j].payType;
                                    }),
                                );
                            }
                        } else {
                            this.payList.forEach(item2 => {
                                item2.isCanSelect = true;
                            });
                        }
                        break;
                    }
                }

                const newcurPayChannelInfo = this.payList.find(item => this.curPayChannelInfo.payType == item.payType);
                if (!newcurPayChannelInfo.isCanSelect) {
                    for (let j = 0; j < this.payList.length; j++) {
                        if (this.payList[j].isCanSelect) {
                            this.curPayChannelClick(this.payList[j]);
                            break;
                        } else {
                            this.curPayChannelClick('');
                        }
                    }
                }
            } else {
                this.payList.forEach(item2 => {
                    item2.isCanSelect = true;
                });
            }
        },
        // 选择优惠券
        chooseTicketAction() {
            //无可用优惠券
            if (!this.isCanClickPay || !this.unPaidInfo.couponList || this.unPaidInfo.couponList.length == 0) {
                return;
            }

            this.isToSelectTicketPage = true;
            let URL = `/packages/third-scan-code-payment/pages/choose-coupon/main`;
            let params = { couponList: this.unPaidInfo.couponList, refer: this.refer, bizContent: this.bizContent };
            if (this.isHarmony) {
                params.zfbRefererMax = true;
            }
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        cardChoose() {
            //无可用礼品卡
            if (!this.isCanClickPay || this.consumeCardNumber == '') {
                return;
            }
            this.isToSelectGiftCardPage = true;
            let URL = `/packages/third-gift-card/pages/choose-gift/main`;
            let params = {
                stationCode: this.unPaidInfo.stationCode,
                orderNo: this.unPaidInfo.orderNo,
                payMoney: this.order ? this.order.rcvAmt : this.unPaidInfo.payMoney,
                refer: this.refer,
            };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        // 实人认证弹窗的确认事件
        realNameInfo(val) {
            let that = this;
            // 调用实人认证公用方法
            this.$sKit.commonUtil
                .triggerRiskAuth(val.name, val.idNumber)
                .then(res => {
                    this.realNameDialogFlag = false;
                    // #ifdef H5-CLOUD
                    if (res) {
                        this.faceValueResponse = res;
                        this.isAuthCloud = true;
                        upsdk.pluginReady(function () {
                            upsdk.createWebView({
                                url: res.certifyUrl,
                                isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                            });
                        });
                    }
                    // #endif
                    // #ifndef H5-CLOUD
                    this.sdkRealPay(true);
                    // #endif
                })
                .catch(err => {
                    uni.showToast({ title: err });
                });
        },
        // 关闭实人认证弹窗
        realNameDialogClose() {
            this.realNameDialogFlag = false;
            // #ifdef H5-CLOUD
            this.isCanClickPay = true;
            this.isPaying = false;
            // #endif
        },
        // 查询加油订单是否支付完成
        async statuslOrderPost() {
            return;
            // uni.showLoading({ title: '查询中...' });
            try {
                let params = {
                    stationCode: this.unPaidInfo.stationCode,
                    orderNo: this.unPaidInfo.orderNo,
                };
                let res = await statuslOrderApi(params, { isload: false });
                // this.isOrderShow = true
                // this.loadingText = '订单查询中'
                if (res.success) {
                    // 支付完成，跳转支付账单页
                    if (res.data.orderPayStatus == 4) {
                        // uni.hideLoading();
                        console.log('=====订单状态为4，支付成功');
                        let URL = `/packages/third-oil-charge-payment/pages/oil-charge-pay-result/main`;
                        let params = this.unPaidInfo;
                        let type = 'redirectTo';
                        this.$sKit.layer.useRouter(URL, params, type);
                        uni.hideLoading();
                    } else {
                        // 轮询支付结果
                        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);

                        if (new Date().getTime() - this.beginTime > 60 * 1000) {
                            uni.hideLoading();
                            // this.isOrderShow = false
                            // this.$util.showModal('未查询到支付结果，请去我的订单中查看订单状态！')
                            this.$store.dispatch('zjShowModal', {
                                title: '',
                                content: '未查询到支付结果，请去我的订单中查看订单状态！',
                                confirmText: '确定',
                                success(res) {
                                    if (res.confirm) {
                                        // uni.navigateBack({
                                        //   delta: 1
                                        // })
                                    } else if (res.cancel) {
                                        console.log('用户点击取消');
                                    }
                                },
                            });
                            return;
                        }
                        this.requestStatusInterval = setTimeout(() => {
                            this.statuslOrderPost();
                        }, this.pollTime);
                    }
                } else {
                    if (this.requestStatusInterval) {
                        clearTimeout(this.requestStatusInterval);
                        this.requestStatusInterval = setTimeout(() => {
                            this.statuslOrderPost();
                        }, this.pollTime);
                    }
                }
            } catch (error) {
                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                if (new Date().getTime() - this.beginTime > 60 * 1000) {
                    uni.hideLoading();
                    // this.isOrderShow = false
                    // this.$util.showModal('未查询到支付结果，请去我的订单中查看订单状态！')
                    this.$store.dispatch('zjShowModal', {
                        title: '',
                        content: '未查询到支付结果，请去我的订单中查看订单状态！',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                // uni.navigateBack({
                                //   delta: 1
                                // })
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                    return;
                }
                this.requestStatusInterval = setTimeout(() => {
                    this.statuslOrderPost();
                }, this.pollTime);
            }
        },
        //手动取消待支付订单
        async backEvent() {
            let params = {
                stationCode: this.unPaidInfo.stationCode,
                orderNo: this.unPaidInfo.orderNo,
            };
            // wx.showLoading({
            //   title: '加载中',
            //   mask: true
            // })
            let res = await cancelOrderApi(params);
            console.log('res---', res);
            if (res.success) {
                this.$store.commit('setIsOrderDot', false);
                uni.showToast({
                    title: '订单取消成功',
                    icon: 'none',
                    duration: 2000,
                    mask: true,
                });
                // uni.navigateBack();
                this.closeEvent();
            }
        },
        // 关闭小程序
        closeEvent() {
            const pages = getCurrentPages();
            console.log('pages--payment', pages.length);
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS || H5-CLOUD
                if (JSON.stringify(CONFIG.name).includes('-zfb')) {
                    this.$sKit.layer.backHomeFun();
                } else {
                    // uni.navigateBack();
                    uni.reLaunch({
                        url: '/pages/thirdHome/main',
                    });
                }
                // #endif
                // #ifdef H5-CLOUD
                uni.navigateBack();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        // 确认支付
        async payAction() {
            // if (!paymentLock) {
            //     return;
            // }
            // 判断石油钱包
            if (!this.isCanClickPay) return;
            if (!this.curPayChannelInfo && this.orderPayMoney != 0) {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.curPayChannelInfo.payType == '6' && !this.curCardPayInfo && this.orderPayMoney != 0) {
                uni.showToast({
                    title: '请选择加油卡',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            this.isCanClickPay = false;
            this.isPaying = true;

            const compareBalances = (item, payType) => {
                return new Promise((resolve, reject) => {
                    let flag = Number(item.amount) >= Number(this.unPaidInfo.orderMoney);
                    let routerFun = () => {
                        if (payType == 5) {
                            let url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
                            let params = { refer: 'r23' };
                            this.$sKit.layer.useRouter(url, params);
                        } else if (payType == 6) {
                            let url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
                            let params = {
                                cardNo: item.fuelCardNo,
                                address: item.usedPlaceName,
                                refer: 'r23',
                            };
                            this.$sKit.layer.useRouter(url, params);
                        }
                    };
                    if (!flag) {
                        this.$store.dispatch('zjShowModal', {
                            content: '您的余额可能不足，是否先去充值？',
                            confirmText: '去充值',
                            cancelText: '继续支付',
                            confirmColor: '#000',
                            cancelColor: '#666',
                            success: res => {
                                if (res.confirm) {
                                    this.$sKit.mpBP.tracker('后支付加油', {
                                        seed: 'hpayoilBiz',
                                        pageID: 'rechargeBut', // 返回sdk标识
                                        refer: this.refer || '',
                                        channelID: clientCode,
                                        address: this.cityName,
                                    });
                                    this.isCanClickPay = true;
                                    this.isPaying = false;
                                    this.recharged = true;
                                    routerFun();
                                } else if (res.cancel) {
                                    this.$sKit.mpBP.tracker('后支付加油', {
                                        seed: 'hpayoilBiz',
                                        pageID: 'paymentBut', // 返回sdk标识
                                        refer: this.refer || '',
                                        channelID: clientCode,
                                        address: this.cityName,
                                    });
                                    flag = true;
                                    resolve(flag);
                                }
                            },
                        });
                    } else {
                        resolve(flag);
                    }
                });
            };

            if (this.curPayChannelInfo.payType == '5') {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: async () => {
                        let res = await compareBalances(this.curPayChannelInfo.memberAssets.details[0], this.curPayChannelInfo.payType);
                        if (res) {
                            this.sdkRealPay();
                        }
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: () => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        this.isCanClickPay = true;
                        this.isPaying = false;
                    },
                    cancelFun: () => {
                        this.isCanClickPay = true;
                        this.isPaying = false;
                    },
                    walletAddParams: {
                        refer: 'r07',
                    },
                });
                return;
            }
            if (this.curPayChannelInfo.payType == '6') {
                //加油卡支付，要判断钱包是否设置过密码
                this.$sKit.commonUtil.cardOperationPwd({
                    nextFun: async () => {
                        let res = await compareBalances(this.curCardPayInfo, this.curPayChannelInfo.payType);
                        if (res) {
                            this.sdkRealPay();
                        }
                    },
                    cancelFun: () => {
                        this.isCanClickPay = true;
                        this.isPaying = false;
                    },
                    walletAddParams: {
                        refer: 'r07',
                    },
                });
                return;
            }
            this.sdkRealPay();
        },
        async getPayChannelInfo() {
            let res = await unPaidOrdeQueryrApi(
                {},
                {
                    handleErrorFn: res => {
                        this.queryHandleErrorFn(res);
                    },
                },
            );
            if (res && res.success && res.data && res.data.orderNo) {
                this.unPaidInfo = res.data;
                this.whetherToCancelElectronicVouchers(res.data);
                this.$store.dispatch('selectCardAction', []); //刷新待支付订单时，把已选择的礼品卡清空
                this.calculateGiftCard();
                this.calculateVerifiedGiftCard();
                this.getAvailableConsumeNumber();
                this.isCanClickPay = true;
                this.isPaying = false;
                this.isPaymentMethodAvailable();
            }
        },
        queryHandleErrorFn(res) {
            if (res.errorCode == 'B_D04_000100') {
                const URL = `/packages/third-oil-charge-payment/pages/query-payment-results/main`;
                const params = { ...this.unPaidInfo, refer: this.refer };
                const type = 'redirectTo';
                this.$sKit.layer.useRouter(URL, params, type);
            } else {
                const pages = getCurrentPages();
                if (pages.length <= 1) {
                    // #ifdef MP-MPAAS
                    this.$cnpcBridge.closeMriver(res => {});
                    // #endif
                    // #ifndef MP-MPAAS
                    this.$sKit.layer.backHomeFun();
                    // #endif
                } else {
                    uni.navigateBack();
                }
            }
        },
        //计算已核销的礼品卡金额
        calculateVerifiedGiftCard() {
            this.giftVerified = 0;
            if (this.unPaidInfo && this.unPaidInfo.verifiedGiftCardList && this.unPaidInfo.verifiedGiftCardList.length > 0) {
                for (let i = 0; i < this.unPaidInfo.verifiedGiftCardList.length; i++) {
                    this.giftVerified = currency(this.giftVerified).add(this.unPaidInfo.verifiedGiftCardList[i].payAmount);
                }
            }
        },
        // 礼品卡方式
        calculateGiftCard() {
            /**
             * 客户累计选择的礼品卡大于实付金额，礼品卡扣减金额为实付金额，订单实付显示0元，
             * 累计选择的礼品卡金额小于实付金额，礼品卡扣减金额为所选礼品卡金额总额，
             * 实付金额为当前实付减所选礼品卡后的金额
             * */
            if (this.selectCard.length > 0) {
                this.giftCardTotalAmount = 0;
                for (let i = 0; i < this.selectCard.length; i++) {
                    this.giftCardTotalAmount = currency(this.giftCardTotalAmount).add(this.selectCard[i].availableAmount);
                }
                if (this.unPaidInfo && this.unPaidInfo.payMoney) {
                    if (Number(this.giftCardTotalAmount) >= Number(this.unPaidInfo.payMoney)) {
                        this.orderPayMoney = 0;
                        //礼品卡扣减金额，如果礼品卡金额总共大于等于订单金额的话，礼品卡的扣减金额其实就是订单待付金额了
                        this.giftCardDeductAmount = this.unPaidInfo.payMoney;
                    } else {
                        this.orderPayMoney = currency(this.unPaidInfo.payMoney).subtract(this.giftCardTotalAmount).value;
                        //礼品卡扣减金额
                        this.giftCardDeductAmount = this.giftCardTotalAmount;
                    }
                    console.log(this.orderPayMoney, 'this.orderPayMoney');
                }
            } else {
                //没有选择礼品卡
                this.orderPayMoney = this.unPaidInfo.payMoney;
                this.giftCardDeductAmount = 0;
                this.giftCardTotalAmount = 0;
            }
        },
        /**
         * 计算礼品卡的支付传参
         */
        calculateGiftCardPay() {
            return new Promise(async (resolve, reject) => {
                if (this.selectCard.length > 0) {
                    if (this.selectCard.length === 1) {
                        //如选择的礼品卡只有一张
                        //1.判断礼品卡的金额大于等于订单的待支付金额,礼品卡的扣减金额需要传订单待支付金额
                        if (Number(this.selectCard[0].availableAmount) >= Number(this.unPaidInfo.payMoney)) {
                            return resolve([
                                {
                                    payType: '12',
                                    cardNo: this.selectCard[0].giftCardNo,
                                    payAmt: this.unPaidInfo.payMoney,
                                },
                            ]);
                        }
                        //2.礼品卡金额小于订单待付金额，那就是礼品卡传余额
                        return resolve([
                            {
                                payType: '12',
                                cardNo: this.selectCard[0].giftCardNo,
                                payAmt: this.selectCard[0].availableAmount,
                            },
                        ]);
                    } else {
                        let cardList = [];
                        //多张礼品卡，最后一张判断传的扣减金额，其他的都传余额
                        if (Number(this.giftCardTotalAmount) >= Number(this.unPaidInfo.payMoney)) {
                            //礼品卡的总金额大于等于订单待付,除了最后一张卡的扣减金额需要计算，其他的都传余额
                            let removeLastAmout = 0; //除了最后一张礼品的其他卡总金额
                            for (let i = 0; i < this.selectCard.length - 1; i++) {
                                removeLastAmout = currency(removeLastAmout).add(this.selectCard[i].availableAmount);
                                cardList.push({
                                    payType: '12',
                                    cardNo: this.selectCard[i].giftCardNo,
                                    payAmt: this.selectCard[i].availableAmount,
                                });
                            }
                            cardList.push({
                                payType: '12',
                                cardNo: this.selectCard[this.selectCard.length - 1].giftCardNo,
                                payAmt: currency(this.unPaidInfo.payMoney).subtract(removeLastAmout).value,
                            });
                        } else {
                            //礼品卡的总金额小于订单待付金额的话，所有礼品卡都传余额
                            this.selectCard.forEach(cardItem => {
                                cardList.push({
                                    payType: '12',
                                    cardNo: cardItem.giftCardNo,
                                    payAmt: cardItem.availableAmount,
                                });
                            });
                        }
                        resolve(cardList);
                    }
                } else {
                    resolve([]);
                }
            });
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.$sKit.commonUtil.nextOilTriggerRisk().then(res => {
                // #ifdef H5-CLOUD
                if (res) {
                    this.faceValueResponse = res;
                    this.isAuthCloud = true;
                    upsdk.pluginReady(function () {
                        upsdk.createWebView({
                            url: res.certifyUrl,
                            isFinish: '0', //是否关闭当前的窗口，1':关闭，'0':不关闭
                        });
                    });
                }
                // #endif
                // #ifndef H5-CLOUD
                this.sdkRealPay(true);
                // #endif
            });
        },
        cancelClick() {
            this.isCanClickPay = true;
            this.isPaying = false;
        },
    },
    async beforeDestroy() {
        clearInterval(this.timerInter);
        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
    async destroyed() {
        clearInterval(this.timerInter);
        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            await this.$cnpcBridge.isCutScreen(false);
        }
        // #endif
    },
};
</script>

<style lang="scss" scoped>
.oil-charge-payment {
    width: 100%;
    height: 100%;
    background-color: #f7f7fb;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #f7f7fb;
        overflow: hidden;

        .scroll-content {
            flex: 1;
            overflow: auto;

            .title {
                text-align: center;
                color: #333333;
                font-weight: bold;
                margin-top: 30px;

                .unit {
                    font-size: 24px;
                }

                .price {
                    font-size: 36px;
                }
            }

            .left-time-content {
                width: 100%;
                font-size: 14px;
                color: #333333;
                text-align: center;

                .time {
                    color: #e64f22;
                    font-size: 14px;
                    font-weight: bold;
                }
            }

            .order-info {
                margin: 30px 15px 0px 15px;
                border-radius: 8px;

                .cell {
                    padding: 15px 12px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .left {
                        font-size: 14px;
                        color: #333333;
                    }

                    .right {
                        color: #e64f22;
                        font-size: 14px;
                        font-weight: bold;
                        display: flex;
                        align-items: center;
                        padding-left: 40px;

                        .normal {
                            font-weight: normal;
                            color: #999999;
                        }

                        .arrow {
                            width: 16px;
                            height: 16px;
                            margin-left: 3px;
                        }
                    }
                }

                .line {
                    margin: 0px 15px;
                    position: relative;

                    &.line:before {
                        content: ' ';
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        height: 1px;
                        border-top: 1px solid #efeff4;
                        -webkit-transform-origin: 0 0;
                        transform-origin: 0 0;
                        -webkit-transform: scaleY(0.5);
                        transform: scaleY(0.5);
                    }
                }
            }

            .pay-info {
                margin: 10px 15px 0px 15px;
                border-radius: 8px;
                background: #ffffff;
                box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.05);
                padding: 9rpx 24rpx;

                .cell {
                    padding: 23rpx 0;
                    display: flex;
                    align-items: center;
                }

                .openWalletCell {
                    padding: 23rpx 0 15rpx;
                    display: flex;
                    align-items: center;
                }

                .left {
                    display: flex;
                    align-items: center;
                    flex: 1;

                    .selicon {
                        width: 21px;
                        height: 20px;
                    }

                    .pay-icon {
                        width: 20px;
                        height: 20px;
                        // margin-left: 15px;
                    }

                    .title-c {
                        font-size: 14px;
                        color: #333333;
                        margin-left: 10px;
                        align-items: center;

                        .textWrap {
                            width: 100%;
                        }

                        .wxText {
                            margin-left: 20rpx;
                            max-width: 56%;
                            font-size: 12px;
                            align-items: center;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }

                    .marketingCopy {
                        margin-left: 20rpx;
                        //width: 90%;
                        width: 80%;
                        font-size: 12px;
                        align-items: center;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    .recommend {
                        // width: 35px;
                        height: 21px;
                        padding: 0 8px;
                        background: #ffffff;
                        border-radius: 4px;
                        border: 1px solid #e64f22;
                        font-size: 10px;
                        color: #e64f22;
                        margin-left: 10px;
                        line-height: 21px;
                        text-align: center;
                        display: block;
                    }

                    .desc {
                        margin-left: 5px;
                        font-size: 12px;
                        color: #e64f22;
                    }
                }

                // .arrow-down {
                //   border: 1px solid #999999;
                //   border-width: 0px 1px 1px 0px;
                //   display: inline-block;
                //   padding: 4px;
                //   transform: rotate(45deg);
                //   -webkit-transform: rotate(45deg);
                // }
                .arrow-right {
                    width: 15px;
                    height: 15px;
                }

                .arrowdown {
                    width: 15px;
                    height: 15px;
                }

                .right {
                    margin-left: 5px;
                    color: #e64f22;
                    font-size: 14px;
                    font-weight: 500;
                    // font-weight bold
                    display: flex;
                    align-items: center;

                    .normal {
                        font-weight: normal;
                        color: #999999;
                    }

                    .arrow {
                        width: 16px;
                        height: 16px;
                        margin-left: 3px;
                    }
                }

                .oil-cell {
                    // display: flex;
                    padding-left: 48px;
                    padding-bottom: 15px;
                    box-sizing: border-box;

                    .left {
                        display: flex;
                        box-sizing: border-box;
                        align-items: center;

                        &:nth-of-type(n + 2) {
                            padding-top: 20px;
                        }

                        .selicon {
                            width: 21px;
                            height: 20px;
                        }

                        .title-c {
                            font-size: 14px;
                            color: #333333;
                            margin-left: 10px;
                        }

                        .marketingCopy {
                            margin-left: 20rpx;
                            width: 90%;
                            font-size: 12px;
                            align-items: center;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                }

                .line {
                    margin: 0px 15px;
                    position: relative;

                    &.line:before {
                        content: ' ';
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        height: 1px;
                        border-top: 1px solid #efeff4;
                        -webkit-transform-origin: 0 0;
                        transform-origin: 0 0;
                        -webkit-transform: scaleY(0.5);
                        transform: scaleY(0.5);
                    }
                }
            }
        }

        .bottom-area {
            margin: 20px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;

            .left-btn {
                flex: 1;
                height: 44px;
                line-height: 44px;
                font-size: 14px;
                font-weight: 500;
                text-align: center;
                background: #ffffff;
                border-radius: 8px;
                border: 1px solid #e64f22;
                color: #e64f22;
                margin-right: 15px;
            }

            .sure-area {
                flex: 1;
                // background-color: #e64f22;
                height: 44px;
                line-height: 44px;
                border-radius: 6px;
                text-align: center;
                color: white;
                font-weight: 500;
                font-size: 15px;
            }

            .btnColor {
                background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            }

            .gray {
                background-color: lightgray;
            }
        }
    }

    .mart12 {
        margin-top: 12px;
    }

    .marbl38 {
        margin-bottom: 3px;
        margin-left: 8px;
    }

    .mask {
        position: fixed;
        background: rgba(0, 0, 0, 0.1);
        z-index: 98;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;

        .showPassword {
            position: fixed;
            width: 100%;
            top: 50%;
            z-index: 999;

            .payment-box {
                position: relative;
                overflow: hidden;
                z-index: 200;
                display: flex;
                justify-content: center;
                align-items: center;

                .loading {
                    position: relative;
                    width: 48px;
                    height: 48px;
                    animation: satellite 3s infinite linear;
                    border: 1px solid #e64f22;
                    border-radius: 100%;
                }

                .loading:before,
                .loading:after {
                    position: absolute;
                    left: 1px;
                    top: 1px;
                    width: 12px;
                    height: 12px;
                    content: '';
                    border-radius: 100%;
                    background-color: #e64f22;
                    box-shadow: 0 0 10px #e64f22;
                }

                .loading:after {
                    right: 0;
                    width: 20px;
                    height: 20px;
                    margin: 13px;
                }

                .box-text {
                    height: 50px;
                    margin: 20px 15px 0;
                    color: #e64f22;
                    line-height: 50px;
                    text-align: center;
                    font-size: 18px;
                }
            }
        }
    }
}

@keyframes satellite {
    from {
        transform: rotate(0) translateZ(0);
    }

    to {
        transform: rotate(360deg) translateZ(0);
    }
}

.right_expRemDiv {
    width: 80%;
    flex-wrap: wrap;
    justify-content: flex-end;
    padding-left: 0 !important;
}

.item_expRemDiv {
    width: 96%;
    flex-wrap: wrap;
    justify-content: flex-end;
    display: flex;
    // padding-right: 5px;
}

.expRemText {
    width: 100%;
    font-size: 10px;
    font-weight: 400;
    text-align: right;
}

.coupon_text {
    // font-weight: 600 !important;
}

.description-color {
    color: #dc3e40 !important;
}

.opcity3 {
    opacity: 0.3;
}

.openImg {
    width: 105px;
    height: 27px;
    margin-top: 3px;
    margin-left: 10px;
}
</style>
