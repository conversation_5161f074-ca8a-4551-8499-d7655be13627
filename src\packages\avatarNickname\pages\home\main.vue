<template>
    <div>
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :custom-back="clickCustomBackBtn"
            back-text="个人信息"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="personalInformation">
            <div class="headPortraitWrap">
                <span class="headPortraitText">头像</span>
                <div class="avatar-wrapper">
                    <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                        <img class="headPortrait" :src="headImg" alt />
                    </button>
                </div>
            </div>
            <div class="line"></div>
            <div class="nicknameWrap">
                <span>昵称</span>
                <input type="nickname" maxlength="32" @blur="onBlur" v-model="nickName" class="nickname" />
            </div>
            <div class="line"></div>
        </div>
        <div class="limit">昵称限制32个字符以内,一个汉字为2个字符</div>
        <button class="confirm" @click="submit">确认</button>

        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import pageConfig from '@/utils/pageConfig.js';
import { mapGetters } from 'vuex';
import { getOtherUserInfoPost, saveUserInfoApi } from '@/api/my-center';
export default {
    name: 'Hello-Word',
    data() {
        return {
            pageConfig, // 页面配置
            nickName: '',
            avatarUrl: '',
            headImg: '',
        };
    },
    created() {},
    mounted() {
        if (!this.registerLoginInformation.headImg) {
            this.headImg =
                'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
        } else {
            this.headImg = this.registerLoginInformation.headImg;
        }
        this.nickName = this.registerLoginInformation.nickName !== '' ? this.registerLoginInformation.nickName : '微信用户';
    },
    methods: {
        onChooseAvatar(e) {
            const { avatarUrl } = e.detail;
            console.log(e.detail, 'e.detail');
            this.headImg = avatarUrl;
            // this.$store.commit('setRegisterLoginInformation', { nickName: this.nickName })
            this.$forceUpdate();
        },
        async submit() {
            wx.hideLoading();
            var strlen = 0;
            for (var i = 0; i < this.nickName.length; i++) {
                if (this.nickName.charCodeAt(i) > 255)
                    //如果是汉字，则字符串长度加2
                    strlen += 2;
                else strlen++;
            }
            if (strlen > 32) {
                uni.showToast({
                    title: '昵称限制32个字符以内',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            console.log(this.nickName, 'this.nickName');
            let params = {
                avtUrl: this.headImg,
                nickName: this.nickName === '' ? '微信用户' : this.nickName,
            };
            await saveUserInfoApi(params).then(res => {
                if (res.status === 0) {
                    this.registerLoginInformation.headImg = this.headImg;
                    this.registerLoginInformation.nickName = this.nickName;
                    uni.navigateBack({
                        delta: 1, //返回层数，2则上上页
                    });
                }
            });
        },
        onBlur(e) {
            this.nickName = e.detail.value;
            this.$forceUpdate();
            wx.hideLoading();
        },
    },
    components: {},
    computed: {
        ...mapGetters(['registerLoginInformation']),
    },
    watch: {},
};
</script>
<style scoped lang="scss">
.personalInformation {
    display: flex;
    flex-direction: column;
    align-items: center;

    .headPortraitWrap {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;
        height: 30px;
        .headPortraitText {
            color: #333;
            margin-left: 15px;
            width: 10%;
        }
        .avatar-wrapper {
            margin-right: 5px;
            width: 90%;
            .headPortrait {
                width: 40px;
                height: 40px;
                border-radius: 8px;
                float: right;
            }
        }
    }
    .nicknameWrap {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        align-items: center;
        height: 30px;
        span {
            margin-top: -2px;
            width: 10%;
            margin-left: 15px;
        }
        .nickname {
            line-height: 20px;
            width: 100%;
            // width: 90%;
            margin-right: 5px;
            text-align: right;
            margin-right: 10px;
        }
    }
    .line {
        border-bottom: 1px solid #eee;
        height: 1px;
        width: 96%;
        margin-left: 2%;
    }
}
.limit {
    color: #cacaca;
    margin-left: 15px;
    margin-top: 8px;
}
.confirm {
    background: #f96702;
    // margin: 0 auto;
    height: 40px;
    line-height: 40px;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 75px;
    color: #fff;
    font-size: 16px;
    // position: absolute;
    // top: 50%;
    // left: 50%;
    // width: 100%;
    // transform: translate(-50%, -50%);
}
</style>
