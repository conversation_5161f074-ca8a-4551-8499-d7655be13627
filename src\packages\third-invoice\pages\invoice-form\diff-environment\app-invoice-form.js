import { makeRechargeInvoiceAsync } from '../../../../../s-kit/js/v3-http/https3/invoice/index';
export default {
    methods: {
        /**
         * @description  : 2.0开票接口
         * @return        {*}
         */
        async invoiceMake2() {
            let params = {
                orderId: this.formData.orderNoList[0],
                cardNo: this.formData.cardNo,
                openingBank: this.titleDetail.buyerFinancial || '',
                bankAccount: this.titleDetail.buyerAccount || '',
                invoiceTittle: this.titleDetail.buyerName,
                taxCode: this.titleDetail.buyerTaxId || '',
                addressTax: this.titleDetail.buyerAddr || '',
                telephone: this.titleDetail.buyerTel || '',
            };
            if (this.titleDetail.buyerNature == 3) {
                if (this.titleDetail.buyerTaxId) {
                    params.enterpriseLogo = 1;
                } else {
                    params.enterpriseLogo = 2;
                }
            } else if (this.titleDetail.buyerNature == 4) {
                params.enterpriseLogo = 2;
            } else {
                params.enterpriseLogo = this.titleDetail.buyerNature;
            }
            let res = await makeRechargeInvoiceAsync(params);
            if (res.status == 0) {
                this.closeSubmitPopup();
                this.isSuccess = true;
            }
        },
        /**
         * @description  : 实人认证弹窗的确认事件
         * @param         {String} name -姓名
         * @param         {String} idNumber -身份证号
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameInfo(val) {
            if (this.isHarmony) {
                this.realNameDialogFlag = false;
                this.$sKit.harmonyRealPersonAuthentication
                    .startVerification({
                        name: val.name,
                        idNo: val.idNumber,
                        type: '11',
                    })
                    .then(response => {
                        if (response.success) {
                            this.invoiceMake(true);
                        } else {
                        }
                    });
            } else {
                this.$sKit.commonUtil
                    .triggerRiskAuth(val.name, val.idNumber)
                    .then(res => {
                        // 关闭实人认证的表单弹窗
                        this.realNameDialogFlag = false;
                        this.invoiceMake(true);
                    })
                    .catch(err => {
                        uni.showToast({ title: err });
                    });
            }
        },
    },
};
