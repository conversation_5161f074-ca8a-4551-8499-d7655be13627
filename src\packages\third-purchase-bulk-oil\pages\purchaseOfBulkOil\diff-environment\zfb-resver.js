import post from '@/s-kit/js/v3-http/post';
export default {
    // #ifdef MP-ALIPAY
    mounted() {},
    methods: {
        initiatePaymentAli(params = {}, isAuth) {
            return new Promise(async (resolve, reject) => {
                params.extendFiled = await post.addExtendFiled('plugin', { isAuth });
                this.hideLoading();
                this.$sKit.aliPayPlugin
                    .QryPreOrder(params, this.accountDataPlugin)
                    .then(async res => {
                        resolve(res);
                    })
                    .catch(err => {
                        resolve(err);
                    });
            });
        },
    },
    // #endif
};
