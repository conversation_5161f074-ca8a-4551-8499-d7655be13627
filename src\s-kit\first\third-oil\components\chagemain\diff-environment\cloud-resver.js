// #ifdef H5-CLOUD
import { mapGetters, mapState } from 'vuex';
import * as paysdk from '@petro-gsms/paysdk-js';
console.log('Plugin---', paysdk);
import { realPersonIdentify } from '../../../../../js/v3-http/https3/oilCard/index';
import post from '../../../../../../s-kit/js/v3-http/post';

export default {
    data() {
        return {
            faceValueResponse: null,
            isAuthCloud: false,
        };
    },
    mounted() { },

    methods: {
        // 调用预授权下单插件
        async preLicensingPlaceAnOrder(dataInfo, isAuth = false) {
            /**
             * 发起预授权下单接口
             * @param areaCode 地区编码
             * @param bizOrderNo 业务订单编号
             * @param stationCode 站编码
             * @param extendFiled 风控字段
             * @param accountPreType  账户预支付场景：1.扫码支付；2.室外支付；3.支付预授权
             * @param amount 需要冻结的金额
             */
            console.log(dataInfo, 'dataInfo--');
            let params = {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: dataInfo.preAuthzOrderNo,
                stationCode: this.selectMarkerV3.orgCode,
                extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                accountPreType: '3',
                amount: this.preaAuthNumber,
                accountNo: this.walletInfo.ewalletNo,
                lockAll: 0,
            };
            this.dataInfo = dataInfo;
            uni.showLoading();
            let accountDataPlugin = this.accountDataPlugin;
            console.log('accountDataPlugin', accountDataPlugin);

            await paysdk.QryPreOrder(params, accountDataPlugin).then(async res => {
                console.log(res, '插件加油预下单');
                setTimeout(() => {
                    this.isPreaAuthing = false;
                }, 500);
                // PAY_ERROR_004 新增错误码，代表不是正在意义上的失败，可能是超时导致的，这种情况处理逻辑跟成功一样，跳转页面查询授权码，查不到再取消订单
                if (res.code === 'PAY_SUCCESS' || res.code === 'PAY_ERROR_004') {
                    // 获取电子钱包金额
                    this.$store.dispatch('getAccountBalanceAction');
                    uni.showLoading({
                        title: '加载中',
                        mask: true,
                    });
                    //成功要跳转
                    let URL = `/packages/third-oil-charge-payment/pages/authorization-code/main`;
                    let type = 'navigateTo';
                    this.$sKit.layer.useRouter(URL, { ...dataInfo, refer: this.refer }, type);
                    uni.setStorageSync('Define_PreAuth_Price', this.preaAuthNumber);
                    uni.setStorageSync('Define_PreAuth_Fuel', encodeURIComponent(JSON.stringify(this.seletFuel)));
                    uni.hideLoading();
                } else if (res.code === 'PAY_ERROR_003') {
                    //需要实人认证
                    this.$sKit.commonUtil
                        .oilTriggerRisk()
                        .then(res => {
                            // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                            if (res == 13) {
                                // 打开实人认证的表单弹窗
                                this.realNameDialogFlag = true;
                            }
                        })
                        .catch(err => {
                            this.cancelAction(dataInfo);
                        });
                } else {
                    this.$refs['popDialogFlag'].open();
                    this.oilDialogFlagType = 'cipherDialogFlag';
                    this.confirmText = '确认';
                    this.cancelText = '';
                    let errIndex = res.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        this.oilDialogCode = res.msg.slice(0, errIndex);
                        this.oilDialogtitle = res.msg.slice(errIndex + 1, res.msg.length);
                    } else {
                        this.oilDialogtitle = res.msg;
                        this.oilDialogCode = res.code;
                    }
                    this.cancelAction(dataInfo);
                }
            });
        },

        realNameAuthentication(faceValue) {
            if (faceValue) {
                return new Promise(async (resolve, reject) => {
                    let params = {
                        // 认证校验码，初始化实人认证接口返回的数据。
                        authInfo: faceValue.authInfo,
                        // 实人认证场景：1—开通昆仑e享卡；2—忘记支付密码；7—绑定加油卡；10-资金转出；
                        type: faceValue.type || 1,
                        // 认证接入方式：1—APP接入；2—PC或H5接入；
                        verifyMode: '2',
                        // 当type=1时该字段为必填项。
                        idNo: faceValue.idNo,
                        // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项。
                        verifyUnique: faceValue.verifyUnique,
                        // 实人认证三方系统的标识(初始化实人认证接口返回)；若为App或者支付宝小程序渠道，该字段为必填项
                        certifyId: faceValue.certifyId,
                        gpsLocation: '1', // gps定位 下个版本等渠道层后台发版 去掉该字段
                    };
                    console.log(params, 'H5传入实人认证接口的参数');

                    let res = await realPersonIdentify(params);
                    if (res.success && res.data.authInfo) {
                        console.log(res, '实人认证方法接口返回结果');
                        // uni.setStorageSync('cloudAuth', 0);
                        this.preLicensingPlaceAnOrder(this.preOrderData, true);
                        resolve(res);
                    } else {
                        this.cancelAction(this.dataInfo);
                    }
                });
            }
        },
    },
    watch: {},
};
// #endif
