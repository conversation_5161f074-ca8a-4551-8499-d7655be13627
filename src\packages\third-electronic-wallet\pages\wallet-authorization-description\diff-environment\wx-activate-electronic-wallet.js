import { realNameAuth, initRealPersonIdentify, realPersonIdentify } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { openAccount } from '../../../../../s-kit/js/v3-http/https3/wallet';
import projectConfig from '../../../../../../project.config';
export default {
    // #ifdef MP-WEIXIN
    mounted() {},
    methods: {
        async agreeActivate() {
            this.$sKit.wxRealPersonAuthentication.startVerification(this.personInfo).then(response => {
                console.log('微信认证结束', response);
                // console.log('链路通过，调用卡通电子账户接口', response);
                if (response.success) {
                    this.openEwallet(response.data.authInfo);
                } else {
                }
            });
        },

        /**
         * @description  :  开通电子钱包
         * @param         {String} type: 实人认证场景：1—开通电子账户；2—忘记支付密码；
         * @return        {*}
         */
        async openEwallet(authInfo) {
            try {
                uni.showLoading({
                    title: '加载中',
                });
                let params = {
                    ...this.personInfo, // 开通钱包流程前面传下来的参数
                    // name: this.beforeParamsForm.name, // 会员姓名
                    // idNo: this.beforeParamsForm.idNo, //  身份证号码（18位证件号）
                    // local: this.beforeParamsForm.local, //  归属地（传国标地市编号，形如：110101）
                    // inviteCode: this.beforeParamsForm.inviteCode, //  邀请码
                    // payPassword: this.beforeParamsForm.payPassword, //  支付密码
                    // confirmPassword: this.beforeParamsForm.confirmPassword, //   确认支付密码
                    // passwordFree: "0", //  免密标志(1—开通免密；0—关闭免密)
                    // freeAmount: this.beforeParamsForm.freeAmount, //  免密金额（元）；passwordFree=1时，该字段为必输项。
                    authInfo: authInfo, //  人脸识别校验码（实人认证接口返回该值）
                };
                console.log(params, 'params===开通昆仑e享卡参数');
                let res = await openAccount(params);
                console.log('接口res----开通电子钱包', res.data);
                if (res && res.success) {
                    await this.$sKit.commonUtil.eWalletNormal({
                        nextFun: () => {
                            this.$sKit.mpBP.tracker('e享卡开通', {
                                seed: 'eCardActiveBiz',
                                pageID: 'sucessPage', // 页面名
                                refer: this.personInfo.refer || '', // 来源
                                channelID: projectConfig.clientCode, // C10/C12/C13
                                address: this.personInfo.address,
                            });
                            if (this.backWalletRechange) {
                                this.$store.dispatch('zjShowModal', {
                                    title: '恭喜您，昆仑e享卡开通成功！',
                                    confirmText: '确认',
                                    confirmColor: '#E64F22',
                                    success: res => {
                                        if (res.confirm) {
                                            // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
                                            let params = {};
                                            let url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
                                            params.refer = 'r17';
                                            let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                            this.$sKit.layer.useRouter(url, params, type);
                                        }
                                    },
                                });
                            } else {
                                let url = '/packages/third-electronic-wallet/pages/wallet-success/main';
                                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                                this.$sKit.layer.useRouter(url, {}, type);
                            }
                        },
                        freezeReasonArr: [9, 10],
                        cancelCallback: () => {
                            // 获取用户非脱敏身份信息和获取电子卡迁移数据
                            this.activate = 'ktqb';
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                        walletAddParams: {
                            refer: 'r14',
                        },
                    });
                }
                return;
            } catch (error) {
                uni.hideLoading();
            }
        },
    },
    // #endif
};
