<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas bg-F7F7FB">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="实体卡充值"></zj-navbar>
            <!-- 充值金额与充值方式 -->
            <div class="recharge card-default">
                <div class="recharge-text font-16 color-000">选择充值金额</div>
                <div class="rechargeAmount fl-row fl-jus-bet fl-wrap">
                    <div
                        class="rechargeAmount-item fl-column fl-al-jus-cen border-rad-8"
                        v-for="(item, index) in amountArr"
                        :key="index"
                        :class="moneyType == item.type ? 'selected' : ''"
                        @click="selectMoney(item, index)"
                    >
                        <div class="fl-row fl-al-jus-cen">
                            <div class="font-16 weight-600 color-666" :class="moneyType == item.type ? 'moneyText' : ''"
                                >{{ item.amount }}
                            </div>
                            <span class="font-12 color-666" :class="moneyType == item.type ? 'moneyText' : ''">元</span>
                        </div>
                        <!-- <div class="title font-12 color-E64F22">{{ item.text }}</div> -->
                    </div>
                </div>
                <!-- 自定义金额 -->
                <div class="recharge-text font-16 color-000">其他金额</div>
                <div class="input-wrap border-rad-8">
                    <input
                        v-model="money"
                        class="input border-box border-rad-8"
                        placeholder="请输入充值金额"
                        placeholder-style="color:#999999;font-size:28rpx;"
                        type="digit"
                        @focus="otherAmountsFocus"
                        @input="otherAmountsInput"
                    />
                </div>

                <!-- 支付渠道 -->
                <div class="paymentChannels">
                    <!-- #ifdef MP-MPAAS -->
                    <div class="payment-text font-16 color-000 weight-500">支付方式</div>
                    <div class="payment-mode" v-if="paymentModeArr.length > 0" @touchmove.stop.prevent>
                        <div :class="{ fold_class: paymentModeArr.length > 3 && !showMore, open_div: showMore }">
                            <div
                                class="payment-item fl-row fl-jus-bet fl-al-sta line_bottom"
                                v-for="(item, index) in paymentModeArr"
                                @click="paymentSelect(item, index)"
                                :key="index"
                            >
                                <div class="payment-item-left fl-al-cen fl-row">
                                    <div class="payment-img">
                                        <img :src="item.img" alt />
                                    </div>
                                    <div class="payment-title">{{ item.name }}</div>
                                    <div class="marketing_copy" v-if="item.alias == 'unionpay'">随机立减，最高5元</div>
                                </div>
                                <div class="select-img" v-if="paymentSelectIndex == index">
                                    <img src="../../images/select.png" alt />
                                </div>
                            </div>
                        </div>
                        <div v-if="paymentModeArr.length > 3" class="check_more" @click="checkMore()"
                            >{{ !showMore ? '查看更多支付方式' : '收起支付方式' }}
                            <img class="down_icon" :class="{ rotate: showMore }" src="../../images/down_icon.png"
                        /></div>
                    </div>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN || H5 -->
                    <div class="payment-text font-16 color-000 weight-500">支付方式</div>
                    <div class="payment-mode" v-if="wechatPayBarRechargeArray.length > 0" @touchmove.stop.prevent>
                        <div :class="{ fold_class: wechatPayBarRechargeArray.length > 3 && !showMore, open_div: showMore }">
                            <div
                                class="payment-item fl-row fl-jus-bet fl-al-sta"
                                v-for="(item, index) in wechatPayBarRechargeArray"
                                :class="{
                                    line_bottom: index !== wechatPayBarRechargeArray.length - 1,
                                }"
                                @click="paymentSelect(item, index)"
                                :key="index"
                            >
                                <div class="payment-item-left fl-al-cen fl-row">
                                    <div class="payment-img">
                                        <img :src="item.img" alt />
                                    </div>
                                    <div class="payment-title">{{ item.name }}</div>
                                </div>
                                <div class="select-img" v-if="paymentSelectIndex == index">
                                    <img src="../../images/select.png" alt />
                                </div>
                            </div>
                        </div>
                        <div v-if="wechatPayBarRechargeArray.length > 3" class="check_more" @click="checkMore()"
                            >{{ !showMore ? '查看更多支付方式' : '收起支付方式' }}
                            <img class="down_icon" :class="{ rotate: showMore }" src="../../images/down_icon.png"
                        /></div>
                    </div>
                    <!-- #endif -->

                    <!-- #ifdef MP-ALIPAY || H5-->
                    <div class="payment-text font-16 color-000 weight-500">支付方式</div>
                    <div class="huabei-mode">
                        <div class="huabei-select">
                            <div class="payment-mode" v-if="aliPayBarRechargeArray.length > 0" @touchmove.stop.prevent>
                                <div :class="{ fold_class: aliPayBarRechargeArray.length > 3 && !showMore, open_div: showMore }">
                                    <div
                                        class="payment-item fl-row fl-jus-bet fl-al-sta"
                                        v-for="(item, index) in aliPayBarRechargeArray"
                                        :class="{ line_bottom: index != aliPayBarRechargeArray.length - 1 }"
                                        @click="paymentSelect(item, index)"
                                        :key="index"
                                    >
                                        <div class="payment-item-left fl-al-cen fl-row">
                                            <div class="payment-img">
                                                <img :src="item.img" alt />
                                            </div>
                                            <div class="payment-title">{{ item.name }}</div>
                                        </div>
                                        <div class="select-img" v-if="paymentSelectIndex == index">
                                            <img src="../../images/select.png" alt />
                                        </div>
                                    </div>
                                    <!-- <div class="fl-row fl-al-cen">
                                        <div class="huabei-select-img">
                                            <img v-if="useHuaBei" src="../../images/icon_pay_checked_active.png" />
                                            <img v-else src="../../images/icon_pay_checked.png" />
                                        </div>
                                         <div @click="onUseHuaBei">使用花呗分期支付</div>
                                    </div> -->
                                    <div class="hb-list" v-if="useHuaBei">
                                        <div
                                            v-for="(item, index) in stagingList"
                                            :key="index"
                                            class="li"
                                            :class="{ isActive: stagingIndex === index }"
                                            @click="changeStage(index)"
                                        >
                                            <div v-if="index === stagingIndex">
                                                <div style="font-size: 12px; text-align: center; color: #e64747">
                                                    分{{ item.StagingNum }}期￥{{ item.EachTotalFee }}/期
                                                </div>
                                                <div v-if="item.useTotalFee">
                                                    <div style="font-size: 8px; text-align: center; margin-top: 2px; color: #e64747">
                                                        （含总手续费{{ item.TotalInsFee }}）
                                                    </div>
                                                </div>
                                                <div v-else>
                                                    <div style="font-size: 8px; text-align: center; margin-top: 2px; color: #e64747">
                                                        （含手续费{{ item.EachFee }}/期）
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div v-if="item.InterestFree" style="font-size: 12px; text-align: center">
                                                    分{{ item.StagingNum }}期(免手续费)
                                                </div>
                                                <div v-else style="font-size: 12px; text-align: center"
                                                    >分{{ item.StagingNum }}期(含手续费)
                                                </div>
                                                <div v-if="item.InterestFree" style="font-size: 8px; text-align: center; margin-top: 2px"
                                                    >含手续费0/期
                                                </div>
                                                <div v-else style="font-size: 8px; text-align: center; margin-top: 2px">
                                                    ￥{{ item.EachTotalFee }}/期
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="aliPayBarRechargeArray.length > 3" class="check_more" @click="checkMore()"
                                    >{{ !showMore ? '查看更多支付方式' : '收起支付方式' }}
                                    <img class="down_icon" :class="{ rotate: showMore }" src="../../images/down_icon.png"
                                /></div>
                            </div>
                        </div>
                    </div>
                    <!-- #endif -->
                </div>
                <div class="tips_div">
                    <div class="title"> <img src="../../images/Vector.png" alt />温馨提示： </div>
                    <div class="tips"
                        ><div
                            ><div class="dots_div"></div>实体卡每天充值次数最多为<div class="color-ff6b2c">5次</div
                            >，仅限使用本人付款账户充值。</div
                        >
                    </div>
                    <div class="tips"><div class="dots_div"></div>您可按办理加油卡时的开票类型开具电子发票。</div>
                    <!-- #ifndef MP-MPAAS-->
                    <!-- <div class="tips"
                        ><div class="dots_div"></div
                        >即日起能源e站将不再支持充值卡充值，存量充值卡可下载能源e站App兑换为等额礼品卡，在加油和购买商品时直接抵扣，如有疑问可咨询加油站工作人员。</div
                    > -->
                    <!-- #endif -->
                </div>
                <div class="btn_div">
                    <div class="protocol fl-row box">
                        <div @click="checkItemChange" class="fl-row fl-al-cen">
                            <radio
                                color="#e64f22"
                                :checked="agreementFlag"
                                :name="1"
                                value="agreementFlag"
                                style="transform: scale(0.65)"
                            />
                            <div class="box_first font-12 color-999">我已阅读并同意</div>
                        </div>
                        <div class="font-12 fl-row fl-al-cen font-14 weight-400;">
                            <div @click="goToagreement" class="font-12 color-E64F22">《中国石油昆仑加油卡客户网上充值协议》</div>
                        </div>
                    </div>
                    <!-- #ifdef MP-MPAAS -->
                    <div @click="confirmRecharge()" class="primary-btn fl-row fl-al-jus-cen confirmRecharge border-rad-8">
                        <div class="color-fff">确认充值</div>
                    </div>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <div @click="confirmRecharge()" class="primary-btn fl-row fl-al-jus-cen confirmRecharge border-rad-8">
                        <div class="color-fff">确认充值</div>
                    </div>
                    <!-- #endif -->

                    <!-- #ifdef MP-ALIPAY -->
                    <form
                        class="primary-btn fl-row fl-al-jus-cen confirmRecharge border-rad-8"
                        report-submit="true"
                        @submit="confirmRecharge"
                    >
                        <button class="btn color-fff font-16" form-type="submit"> 确认充值 </button>
                    </form>
                    <!-- #endif -->
                    <div class="card_recharge" @click="toECardRecharge()"> 昆仑e享卡充值 </div>
                </div>
            </div>
            <!-- 充值成功弹窗 -->
            <div class="popupMask" v-show="popupShow"></div>
            <div class="popup" v-show="popupShow">
                <p>订单查询中</p>
                <div class="countDown">
                    <span>{{ nums }}</span>
                </div>
            </div>
            <uniPopup v-if="!isOpenSwitchGiftCard" ref="inputDialog" type="dialog">
                <UniPopupDialog
                    ref="inputClose"
                    mode="input"
                    title="充值卡充值"
                    placeholder="请输入19位充值卡密码"
                    @confirm="dialogInputConfirm"
                    @close="closeDialog"
                ></UniPopupDialog>
            </uniPopup>
            <zj-show-modal>
                <div class="tc_div" v-if="showModalPop">
                    <div class="title">温馨提示</div>
                    <div class="text">
                        尊敬的中国石油实体加油卡客户：您好，本卡仅限本人付款账户进行充值，若付款账户与本卡账户实名信息不一致，将无法进行充值。
                    </div>
                </div>
                <rechargeCardDeactivated v-if="rechargeCardDeactivatedFlag"></rechargeCardDeactivated>
            </zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import checkFKArgs from '../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import { bindCardInfo, paycard, rechargeByCardPost } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { rechargeByCard } from '../../../../s-kit/js/v3-http/https3/wallet';
// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-card-recharge.js';
// #endif
// #ifndef MP-MPAAS
import wxMixin from './diff-environment/wx-card-recharge.js';
import zfbMixin from './diff-environment/zfb-card-recharge.js';
// #endif
import projectConfig from '../../../../../project.config';
import uniPopup from '../../../../s-kit/components/layout/custom-popup/uni-popup/uni-popup.vue';
import UniPopupDialog from '../../../../s-kit/components/layout/custom-popup/uni-popup-dialog/uni-popup-dialog.vue';
import { tripleDESToBase64 } from '@/s-kit/js/TripleDES';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import rechargeCardDeactivated from '@/components/rechargeCardDeactivated/main.vue';
export default {
    name: 'oil-card-recharge',
    components: {
        UniPopupDialog,
        uniPopup,
        rechargeCardDeactivated,
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS
        wxMixin,
        zfbMixin,
        // #endif
    ],
    props: {},
    data() {
        return {
            username: '',
            // 选择充值金额的数组
            amountArr: [
                { amount: 200, selected: false, type: 1 },
                { amount: 300, selected: false, type: 2 },
                { amount: 500, selected: false, type: 3 },
                { amount: 800, selected: false, type: 4 },
                { amount: 1000, selected: false, type: 5 },
                { amount: 2000, selected: false, type: 6 },
            ],
            // 选择支付的金额
            money: '',
            // 微信支付宝支付方式数组
            wechatPayBarRechargeArray: [
                { name: '微信', alias: '1', img: require('../../images/wechat.png') },
                // { name: '充值卡', alias: 'card', img: require('../../images/card.png') },
            ],
            aliPayBarRechargeArray: [
                { name: '支付宝', alias: '1', img: require('../../images/aliPay.png') },
                { name: '使用花呗分期支付', alias: 'huabei', img: require('../../images/huabei.png') },
                // { name: '充值卡', alias: 'card', img: require('../../images/card.png') },
            ],
            // 支付方式数组
            paymentModeArr: [],
            // 通过index来显示选中的支付方式
            paymentSelectIndex: 0,
            // 微信和支付宝充值显示选中的支付方式
            paymentSelectWxOrAliIndex: 0,
            // 协议是否选中
            agreementFlag: false,
            // // 油卡备用金/余额/卡积分/备用金积分对象
            // cardInfoRecharge: {},
            // 油卡管理页面传递卡信息
            thirdCardItemRecharge: {},
            // 支付类型
            payType: '',
            // 微信和支付宝支付类型
            payTypeWxOrAli: '',
            // 订单编码
            orderNo: '',
            alertType: '',
            // 充值卡卡密绑定值
            rechargeableCard: '',
            // 选中的金额
            moneyType: 0,
            // 充值成功后的弹窗
            popupShow: false,
            // 倒计时6s
            nums: 6,
            // 充值成功后倒计时
            timer: null,
            showModalPop: false,
            showMore: false,
            // 充值卡充值停用弹窗标识
            rechargeCardDeactivatedFlag: false,
            // 充值卡兑换礼品卡开关
            isOpenSwitchGiftCard: false,
        };
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    created() {},
    onLoad(options) {
        console.log('🚀 ~ file: main.vue:294 ~ onLoad ~ options:', JSON.parse(decodeURIComponent(options.data)));
        const popFlag = uni.getStorageSync('card_recharge_popUp_prompt');
        console.log(popFlag, 'card_recharge_popUp_prompt=======');
        if (!popFlag) {
            this.showModal();
        }
        // 选中需要充值的油卡的信息
        this.thirdCardItemRecharge = JSON.parse(decodeURIComponent(options.data));
        this.$sKit.mpBP.tracker('充值', {
            seed: 'rechargeBiz',
            pageID: 'cardRecharge', // 页面名
            refer: this.thirdCardItemRecharge.refer || '', // 来源
            channelID: projectConfig.clientCode, // C10/C12/C13
            address: this.thirdCardItemRecharge.address || '', // 归属地
        });
    },
    mounted() {
        // #ifdef MP-MPAAS
        this.openSwitchGiftCard();
        // #endif
        // 获取地区充值方式
        this.getAreaRecharge();
    },
    methods: {
        openSwitchGiftCard() {
            this.$cnpcBridge.getSwitch('RechargeCardToGiftCard', res => {
                console.log(res, '获取开关');
                if (res == 'yes') {
                    this.isOpenSwitchGiftCard = true;
                }
            });
        },
        checkMore() {
            this.showMore = !this.showMore;
        },
        showModal() {
            this.showModalPop = true;
            setTimeout(() => {
                // this.$refs.tipsPopup.open();
                this.$store.dispatch('zjShowModal', {
                    confirmText: '我知道了',
                    confirmColor: '#E64F22',
                    success: res => {
                        if (res.confirm) {
                            uni.setStorageSync('card_recharge_popUp_prompt', true);
                            this.showModalPop = false;
                        }
                    },
                });
            }, 300);
        },
        toECardRecharge() {
            let url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
            let params = { refer: 'r13' };
            this.$sKit.layer.useRouter(url, params);
        },
        submitForm(e) {
            console.log('🚀 ~ file: main.vue:326 ~ submitForm ~ this.$refs.submitFormRef:', this.$refs.submitFormRef);
            console.log('🚀 ~ file: zfb-card-recharge.js:74 ~ submitForm ~ e:', e);
        },
        /**
         * @description  : 获取油卡备用金/余额/卡积分/备用金积分
         * @param        {String}cardNo -卡号
         * @return       {*}
         */
        // getBindCardInfo() {
        //     let params = {
        //         cardNo: this.thirdCardItemRecharge.cardNo,
        //     };
        //     bindCardInfo(params).then(res => {
        //         if (res.success) {
        //             // 油卡备用金/余额/卡积分/备用金积分对象
        //             this.cardInfoRecharge = res.data;
        //             this.$refs.pullDownRefreshRef.stopRefresh();
        //         }
        //     });
        // },
        /**
         * @description  : 选择需要充值的金额
         * @param         {Number} index -下标
         * @param         {String} otherAmounts -其他金额
         * @param         {String} money -选中的金额
         * @param         {String} moneyType -选中的金额
         * @return        {*}
         */
        selectMoney(item, index) {
            // 选择充值金额的某一项
            this.moneyType = item.type;
            // 选择充值的金额
            this.money = item.amount;
            // #ifdef MP-ALIPAY
            if (this.useHuaBei) this.getAliHBPayListFn();
            // #endif
        },
        /**
         * @description  : 点击其他金额 获取输入框焦点时触发
         * @return        {*}
         */
        otherAmountsFocus() {
            this.paymentModeArr.map(item => {
                // selected ==true 时 选中的金额某一项变高亮
                // 点击其他金额时 将选中的高亮的金额变灰
                item.selected = false;
            });
            // 选中的金额置空
            this.money = '';
            // 选中的充值金额item的样式置空
            this.moneyType = -1;
            // #ifdef MP-ALIPAY
            if (this.useHuaBei) this.getAliHBPayListFn();
            // #endif
        },
        /**
         * @description  : 输入自定义金额
         * @param         {*} e:input输入的值
         * @return        {*}
         */
        otherAmountsInput(e) {
            // 输入的金额
            let value = e.detail.value;
            // 校验小数点后保留两位
            const price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
            setTimeout(_ => {
                this.money = typeof price === 'string' ? price : price[0];
                // #ifdef MP-ALIPAY
                if (this.useHuaBei) this.getAliHBPayListFn();
                // #endif
            }, 10);
        },
        /**
         * @description  : 选择支付方式
         * @param         {Object} item -选中的支付方式数据
         * @param         {Number} index -选中的支付方式下标
         * @return        {*}
         */
        paymentSelect(item, index) {
            console.log('item ==== index', item, index);
            this.paymentSelectIndex = index;
            this.payType = item.alias;
            this.money = '';
            this.moneyType = 0;
        },
        // paymentSelectWxOrAli(item, index) {

        //     this.paymentSelectWxOrAliIndex = index;
        //     this.payTypeWxOrAli = item.alias;
        // },
        dialogInputConfirm(e) {
            this.rechargeableCard = e;
            this.rechargeCard();
        },
        closeDialog() {
            this.paymentSelectIndex = '-1';
            this.payType = '';
            this.$refs.inputDialog.close();
        },
        async rechargeCard() {
            let tokenInfo;
            tokenInfo = await uni.getStorageSync('tokenInfo');
            console.log(this.rechargeableCard, 'rechargeableCard');
            if (!(this.rechargeableCard && this.rechargeableCard.length === 19)) {
                // this.$toast("请输入正确的充值卡密码");
                uni.showToast({
                    title: '请输入正确的充值卡密码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }

            let res = null;
            // #ifdef MP-MPAAS
            res = await rechargeByCardPost({
                cardNo: this.thirdCardItemRecharge.cardNo,
                rechargeableCard: this.rechargeableCard,
            });
            // #endif
            // #ifdef MP-WEIXIN
            res = await rechargeByCardPost({
                cardNo: this.thirdCardItemRecharge.cardNo,
                rechargeableCard: this.rechargeableCard,
            });
            // #endif
            // #ifdef MP-ALIPAY
            // const requestData = {
            //     memberNo: tokenInfo.memberNo,
            //     cardNo: this.thirdCardItemRecharge.cardNo,
            //     rechargeableCard: this.rechargeableCard,
            // };
            // // 调整密钥长度
            // const encryptedData = tripleDESToBase64(
            //     requestData.memberNo + '&' + requestData.cardNo + '&' + requestData.rechargeableCard,
            //     projectConfig.v2sign,
            // );
            // console.log(tokenInfo.cppeiLoginInfo.phoneToken, 'tokenInfo.cppeiLoginInfo.phoneToken');
            res = await rechargeByCard({
                cardPwd: this.rechargeableCard,
                cardNo: this.thirdCardItemRecharge.cardNo,
                channel: '05',
                tokenForAli: tokenInfo.accessToken,
            });
            // res = await rechargeByCardPost({ EncodeData: encryptedData, channel: '05' });
            res.result = res.InfoCode == 1 ? 'success' : 'fail';
            res.status = res.InfoCode == 1 ? 0 : 1;
            res.data = res.Data.amount;
            // #endif
            let data = res.data;
            if (res.status == 0) {
                // this.$toast('充值成功');
                if (data) {
                    uni.setStorageSync('refreshCardManagement', true);
                    // this.$dialog.alert({ message: })//返回单位是分，处理一下
                    this.$store.dispatch('zjShowModal', {
                        content: `恭喜您，成功充值${data / 100}元`,
                        success: res => {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                this.rechargeableCard = '';
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },

        /**
         * @description  : 勾选协议
         * @param         {Boolean} agreementFlag -是否勾选
         * @return        {*}
         */
        checkItemChange() {
            this.agreementFlag = !this.agreementFlag;
        },
        stopRecharge() {
            const params = { refer: 'r55' };
            const url = '/packages/third-gift-card/pages/binding/main';
            this.$sKit.layer.useRouter(url, params, 'redirectTo');
            // let isShowConfirmOnly = projectConfig.clientCode === 'C10' || false;
            // console.log(projectConfig.clientCode, 'this.clientCode===');
            // this.rechargeCardDeactivatedFlag = true;
            // if (isShowConfirmOnly && this.isOpenSwitchGiftCard) {
            //     this.jumpGiftCardBind(isShowConfirmOnly);
            // } else {
            //     this.$refs.inputDialog.open();
            // }
        },
        jumpGiftCardBind(isShowConfirmOnly) {
            const modalConfig = {
                confirmText: isShowConfirmOnly ? '去绑定' : '确定',
                cancelText: isShowConfirmOnly ? '取消' : undefined,
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        this.rechargeCardDeactivatedFlag = false;
                        // 只有APP和鸿蒙展示去绑定按钮
                        if (!isShowConfirmOnly) return;
                        const params = { refer: 'r55' };
                        const url = '/packages/third-gift-card/pages/binding/main';
                        this.$sKit.layer.useRouter(url, params, 'redirectTo');
                    } else if (res.cancel) {
                        this.rechargeCardDeactivatedFlag = false;
                        console.log('用户点击取消');
                    }
                },
            };
            this.$store.dispatch('zjShowModal', modalConfig);
        },
    },

    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.view {
    .image-view-area {
        img {
            width: 100%;
            height: 218px;
        }
    }

    .content-view {
        height: 173rpx;
        margin: 0 16px;

        .content-view-item {
            margin: 0 10px;

            .chineseCharacter {
                line-height: 16px;
            }

            .num {
                line-height: 22px;
                margin-top: 4px;
            }
        }
    }

    .recharge {
        margin: 12px 16px 0 16px;
        padding-bottom: 157px;
        overflow: scroll;
        .recharge-text {
            margin-top: 16px;
            // margin-left: 16px;
            padding: 0 16px;
            font-weight: bold;
        }

        .rechargeAmount {
            padding: 0 16px;

            .rechargeAmount-item {
                width: 95px;
                height: 45px;
                /*height: 60px;*/
                margin-top: 12px;
                // box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
                border: 1rpx solid #e8e8e8;
            }

            .selected {
                box-sizing: border-box;
                background: rgba(230, 79, 34, 0.1);
                border: 1px solid #e64f22;

                .moneyText {
                    color: #e64f22;
                }
            }

            .title {
                display: table-cell;
                /*按照单元格的样式显示元素*/
                vertical-align: bottom;
                /*底对齐*/
            }
        }

        .input-wrap {
            padding: 0 16px;
            width: 100%;

            .input {
                width: 100%;
                height: 40px;
                margin-top: 12px;
                line-height: normal;
                border: 1px solid #dfdfed;
                padding-left: 15px;
            }
        }

        .promptWrap {
            padding: 0 16px;
            margin: 12px 0;

            .promptWrap-top {
                img {
                    width: 15px;
                    height: 15px;
                    margin-right: 5px;
                }
            }

            .promptWrap-top-box {
                div {
                    line-height: 36rpx;
                }

                .promptWrap-bottom {
                    // margin-left: 20px;
                    // margin-top: 3px;
                }
            }
        }

        .paymentChannels {
            padding: 0 16px;

            .payment-text {
                margin-top: 12px;
            }

            .payment-mode {
                margin-top: 15px;
                overflow-y: scroll;
                .payment-item {
                    height: 32px;
                    margin-top: 12px;

                    .payment-item-left {
                        .payment-title {
                            margin-left: 10px;
                        }

                        .payment-img {
                            width: 20px;
                            height: 20px;

                            img {
                                width: 20px;
                                height: 20px;
                            }
                        }
                    }

                    .select-img {
                        img {
                            width: 17.5px;
                            height: 17.5px;
                            margin-right: 10px;
                        }
                    }
                }
            }

            .huabei-mode {
                .huabei-select {
                    // display: flex;
                    // align-items: center;
                    padding: 15px 0;
                }

                .huabei-select-img {
                    line-height: 0;

                    img {
                        width: 17.5px;
                        height: 17.5px;
                        margin-right: 10px;
                    }
                }

                .hb-list {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 20rpx;

                    .li {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        width: 32%;
                        height: 50px;
                        border: 2rpx solid #bba3a3;
                        border-radius: 8px;
                    }

                    .isActive {
                        border: 2rpx solid #e64747;
                    }
                }
            }
        }

        .protocol {
            margin-left: -5px;
            align-items: center;
            margin-top: 10px;
            img {
                flex-shrink: 0;
                width: 17.5px;
                height: 17.5px;
            }
            div {
                div {
                    line-height: 12px;
                }
            }
        }

        .confirmRecharge {
            height: 44px;
            margin-bottom: 20px;
            margin-top: 16px;

            .btn {
                width: 100%;
                height: 44px;
                border: none;
                line-height: 44px;
            }
        }

        .recharge_moudle_style {
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            background: white;
            width: 100%;
            margin-bottom: 10px;
            box-sizing: border-box;
            padding: 0 10px;

            .line_style {
                display: flex;
                position: relative;
                align-items: center;
                justify-content: space-between;
                height: 50px;
                font-weight: 500;
                flex-wrap: wrap;
                font-size: 16px;
            }

            .div_input {
                width: 96%;
                height: 40px;
                border: 1px solid #dfdfed;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        }
    }

    .popupMask {
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        position: fixed;
        top: 0;
        left: 0;
        background: #333;
        opacity: 0.5;
    }

    .popup {
        width: 300px;
        height: 150px;
        background-color: #fff;
        border-radius: 10px;
        z-index: 10000;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;

        p {
            font-size: 18px;
            padding-top: 50px;
        }

        .countDown {
            font-size: 16px;
            margin-top: 10px;

            span {
                color: #ff8200;
            }
        }
    }
}

.btn_div {
    width: 92%;
    position: fixed;
    padding-bottom: 20px;
    bottom: env(safe-area-inset-bottom);
    background: #f7f7fb;
}

.color-ff6b2c {
    color: #ff6b2c;
    display: inline-block;
    text-indent: 0;
}

.marketing_copy {
    color: #dc3e40;
    font-size: 10px;
    margin-left: 10px;
}

.card_recharge {
    height: 22px;
    font-size: 14px;
    font-weight: 500;
    color: #ff6b2c;
    line-height: 22px;
    text-align: right;
}

.tips_div {
    margin: 12px 16px;

    .title {
        display: flex;
        align-items: center;
        flex-direction: row;
        font-size: 12px;
        color: #333333;
        line-height: 18px;
    }

    img {
        width: 14px;
        height: 14px;
        margin-right: 5px;
    }

    .color-ff6b2c {
        color: #ff6b2c;
        display: inline-block;
        text-indent: 0;
    }

    p {
        text-indent: 20px;
        font-size: 12px;
        line-height: 18px;
    }

    .tips {
        font-size: 12px;
        line-height: 18px;
    }
}

.dots_div {
    width: 5px;
    height: 5px;
    background: #e6e6e6;
    border-radius: 50%;
    margin-top: 5px;
    float: left;
    margin: 6px 7px 0px 20px;
}

.tc_div {
    .title {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        text-align: center;
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-top: 10px;
        text-indent: 2em;
    }
}

.dots_div {
    width: 5px;
    height: 5px;
    background: #e6e6e6;
    border-radius: 50%;
    margin-top: 5px;
    float: left;
    margin: 6px 7px 0px 20px;
}
.check_more {
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    color: #999;
}
.fold_class {
    max-height: 127px;
    overflow: hidden;
}
.open_div {
    // height: 218px;
    // overflow: scroll;
    transition: all 0.5s ease;
}
.down_icon {
    width: 12px;
    height: 7px;
    margin-left: 8px;
}
.rotate {
    transform: rotate(180deg);
}
</style>
