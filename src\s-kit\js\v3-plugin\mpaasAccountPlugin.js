import { singleton } from '../singleton';
import Config from '../third-config.js';
import cnpcBridge from '../v3-native-jsapi/cnpcBridge.js';
import Store from '../../../store/index';
import { jwtTokenvalue } from '../v3-http/post.js';
class mpaasAccountPlugin {
    // #ifdef MP-MPAAS
    constructor() {}
    initRef(ref) {
        return new Promise(async (resolve, reject) => {
            let gsmsToken = '';
            let memberNo = '';
            let isHarmony = false;
            let systemInfo = my.getSystemInfoSync();
            if (systemInfo && (systemInfo.system.includes('Harmony') || systemInfo.platform == 'Harmony')) {
                isHarmony = true;
            }
            // 获取用户信息
            if (isHarmony) {
                let userTokenInfo = await cnpcBridge.getUserTokenInfo();
                gsmsToken = userTokenInfo.gsmsToken;
                memberNo = userTokenInfo.memberNo;
            }
            // 设备信息
            my.loadPlugin({
                plugin: `${Config.app.mpassplugins.accountPlugin.provider}@*`,
                success: async () => {
                    /**
                    * 1 必填 插件实例
                     2 必填 memberNo
                    3 必填 网关验证的token
                    4 必填 环境 dev sit prd
                    * */
                    //因为baseType生产环境和灰度环境值都是prd，在给账户插件传环境标识的时候区分不开，所以再次做下appid的判断，之后进行优化
                    //检查是否满足灰度发布条件
                    const isGrayRelease = Config.baseType === 'prd' && Config.mpaasAppId === 'PRI089DD0D241053';
                    // 根据条件确定 baseType 的值
                    let baseType = isGrayRelease ? 'gray' : Config.baseType;
                    console.log('账户插件---params', ref, memberNo, gsmsToken, baseType);
                    try {
                        const accountPlugin = requirePlugin(`dynamic-plugin://${Config.app.mpassplugins.accountPlugin.provider}`);
                        accountPlugin
                            .init(ref, memberNo, gsmsToken, baseType)
                            .then(res => {
                                console.log('accountPlugin初始化成功', res);
                            })
                            .finally(() => {
                                Store.commit('setMpaasAccountDataPlugin', accountPlugin);
                                resolve(accountPlugin);
                            });
                    } catch (err) {
                        my.showToast({
                            title: err.data.msg,
                            icon: 'none',
                            duration: 2000,
                        });
                        console.log('accountPlugin初始化失败', err);
                        reject(err);
                    }
                },
                fail: error => {
                    console.log(error, '账户插件获取失败');
                },
            });
        });
    }
    // #endif
}
// #ifdef MP-MPAAS
export default singleton(mpaasAccountPlugin);
// #endif
