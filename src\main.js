import Vue from 'vue';
import App from './App';

// #ifdef MP-WEIXIN
import { RouterMount } from 'uni-simple-router';
import wxLogin from './utils/login';
Vue.prototype.$wxLogin = wxLogin;
import './router';
import http from './utils/http';
Vue.prototype.$http = http;
import './assets/stylus/index.styl';
// #endif
import util from './utils/index';
import test from './utils/test';
import store from './store/index';
import Storage from './utils/storage.js';
import globalData from './utils/global-data.js';
// uniapp 运行时
// import '@/s-kit/js/uni-runtime'
Vue.prototype.$test = test;
Vue.prototype.$util = util;
Vue.prototype.$store = store;
Vue.prototype.$Storage = Storage;
Vue.prototype.$GlobalData = globalData;
Vue.config.productionTip = false;

import uView from 'uview-ui';

import sKit from './s-kit/js';

Vue.prototype.$sKit = sKit;
// #ifdef H5-CLOUD
import VueCupUi from 'vue-cup-ui';
import 'vue-cup-ui/lib/vue-cup-ui.css';
Vue.use(VueCupUi);
import passwordInstance from './s-kit/js/v3-plugin/password-keyboard.umd.min.js';
Vue.component('password-instance',passwordInstance);
// #endif
// #ifdef MP-MPAAS
// 链接原生的jsapi
import cnpcBridge from './s-kit/js/v3-native-jsapi/cnpcBridge.js';
Vue.prototype.$cnpcBridge = cnpcBridge;

// 支付SDK
import paymentCenter from './s-kit/js/v3-native-jsapi/paymentCenter.js';
Vue.prototype.$paymentCenter = paymentCenter;

// 账户SDK
import accountCenter from './s-kit/js/v3-native-jsapi/accountCenter.js';
Vue.prototype.$accountCenter = accountCenter;
// #endif

// utils
import utils from './s-kit/js/v3-native-jsapi/utils';
Vue.prototype.$cnpcUtils = utils;
// 埋点
App.mpType = 'app';
Vue.use(uView);
const app = new Vue({
    ...App,
    //定义全局事件总线 $bus
    beforeCreate: function () {
        Vue.prototype.$bus = this;
    },
});
export default app;

// #ifdef H5
RouterMount(app, '#app');
// #endif

// #ifndef H5
app.$mount();
// #endif
