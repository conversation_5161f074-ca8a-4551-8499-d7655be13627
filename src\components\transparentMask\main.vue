<template>
    <div class="container">
        <div class="mask"></div>
    </div>
</template>

<script>
export default {
    data() {
        return {};
    },
    computed: {},
    methods: {},
};
</script>

<style>
.container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10; /* 设置遮罩层位于其他元素之上 */
    opacity: 1;
    /* background: red; */
}
.mask {
    top: 0;
    left: 0;
    opacity: 1;
}
</style>
