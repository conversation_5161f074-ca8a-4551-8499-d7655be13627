<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column p-hw bg-F7F7FB">
            <zj-navbar :border-bottom="false" title="账号注销" height="44"></zj-navbar>
            <div class="page-warp f-1 mh-0">
                <div class="fl-column fl-al-jus-cen mart16 marb6">
                    <img src="../../image/account-out.png" alt class="icon-50" />
                    <div class="color-E64F22 font-18 weight-500 padd12">将{{ memberBaseInfo.phone }}所绑定的账号注销</div>
                </div>
                <div class="mar16 font-14 color-999 weight-400">注销您的账号首先需要满足如下条件：</div>
                <div class="wrap marlr16">
                    <div class="fl-row fl-al-cen padd12">
                        <img src="../../image/icon-money.png" alt class="icon-40" />
                        <div class="font-14 weight-400 color-333 marl10">昆仑e享卡余额为0且所有交易已完结、无纠纷</div>
                    </div>
                    <!-- <div class="fl-row fl-al-cen padd12">
                            <img src="../../image/icon-oil.png" alt class="icon-40" />
                            <div class="font-14 weight-400 color-333 marl10">解绑名下绑定的全部油卡</div>
                        </div> -->
                </div>
                <div class="marlr16 font-14 color-999 weight-400 padd12">
                    若您满足条件并选择注销本账号，将无法登录该账号且 留存的所有信息将被清空无法找回。
                </div>
                <div class="marlr16 font-14 color-999 weight-400 padd12">具体包括以下信息：</div>
                <div class="wrap marlr16">
                    <div class="fl-row fl-al-cen padd12">
                        <img src="../../image/icon-gr.png" alt class="icon-40" />
                        <div class="font-14 weight-400 color-333 marl10">个人资料</div>
                    </div>
                    <div class="fl-row fl-al-cen padd12">
                        <img src="../../image/icon-jy.png" alt class="icon-40" />
                        <div class="font-14 weight-400 color-333 marl10">交易记录、发票记录</div>
                    </div>
                    <div class="fl-row fl-al-cen padd12">
                        <img src="../../image/icon-coupon.png" alt class="icon-40" />
                        <div class="font-14 weight-400 color-333 marl10">电子券、优惠折扣等权益</div>
                    </div>
                </div>
                <div class="card-shadow bg-288 btn marlr16" @click="cancelAccountClick()">已清楚风险，确认注销</div>
            </div>
            <!-- <zj-show-modal v-if="cancelStatus == 'init'"></zj-show-modal>
    <zj-show-modal v-if="cancelStatus == 'fail'"></zj-show-modal>v-if="cancelStatus == 'success'"-->
            <zj-show-modal></zj-show-modal>
            <zj-agreement @enterNavEvent="enterNavEvent"></zj-agreement>
        </div>
    </div>
</template>

<script>
import { cancelAccountApi } from '../../../../s-kit/js/v3-http/https3/user.js';
import { userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { initRealPersonIdentify, realPersonIdentify } from '../../../../s-kit/js/v3-http/https3/oilCard/index';

import { mapGetters } from 'vuex';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            cancelStatus: 'init',
            memberData: '',
            verifyUnique: '',
            authInfo: '',
            locationInfo: '',
        };
    },
    onLoad(option) {
        // 查询用户详细信息
        this.getBasicInfoQuery();
    },
    async onShow() {
        // 客户名称，手机号，会员等级等基本信息和能源币，油卡，电子券，积分和余额。
        await this.$store.dispatch('memberBaseInfoAction');
    },
    methods: {
        /** 查询用户详细信息
         * @description
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res.success) {
                this.memberData = res.data;
            }
        },
        // 关闭人脸认证协议弹窗
        enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            //去实人认证
            this.initFaceAuthentication();
        },
        //确认注销
        async cancelAccountClick() {
            if (this.cancelStatus == 'init') {
                this.$store.dispatch('zjShowModal', {
                    content: '注销账号会清空所有的信息和数据（含余额，优惠券，交易记录、发票记录等），您是否确认注销？',
                    confirmText: '我再想想',
                    cancelText: '立即注销',
                    showCancel: true,
                    confirmColor: '#333333',
                    cancelColor: '#666666',
                    success: async res => {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            if (this.memberData.identityAuthStatus == '15') {
                                // 打开人脸认证协议弹窗
                                this.$store.dispatch('changeFacePop', true);
                            } else {
                                let url = '/packages/third-account/pages/account-cancel/main'; //去短信验证码验证
                                let type = 'navigateTo';
                                this.$sKit.layer.useRouter(url, {}, type);
                            }
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        /**人脸验证初始化
     * returnUrl:  业务回跳地址；PC或H5接入时 必传，APP接入时为空
       metaInfo:  MetaInfo环境参数，需要通过JS获取或SDK获取
       verifyMode:  认证接入方式：1—APP接入；2—PC或H5接入；
     */
        async initFaceAuthentication() {
            let params = {
                returnUrl: '',
                metaInfo: await this.$cnpcBridge.aliMetaInfo(),
                verifyMode: '1',
            };
            let res = await initRealPersonIdentify(params);
            if (res && res.success) {
                this.verifyUnique = res.data.verifyUnique;
                this.$cnpcBridge.aliFaceCollec(res.data.certifyId, async result => {
                    if (result.status) {
                        this.realNameAuthentication(res.data);
                    } else {
                        //
                        uni.showToast({
                            title: result.msg,
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                });
            }
        },
        /** 人脸实名验证
     *
     * @param {*} data
     *  type: 实人认证场景：
        1—开通电子账户；
        2—忘记支付密码；
        7—绑定加油卡；
        10—资金转出；
        11—风控验证；
        6—修改用户信息；
        9—注销会员；
        authInfo: 认证校验码，实名认证接口返回的数据。
        verifyUnique:  身份认证唯一标识(初始化实人认证接口返回)
        certifyId: 实人认证三方系统的标识(初始化实人认证接口返回)
        verifyMode: 认证接入方式：1—APP接入；2—PC或H5接入；
    */
        realNameAuthentication(data) {
            let params = {
                type: '9',
                // authInfo: this.authInfo,
                verifyUnique: data.verifyUnique,
                certifyId: data.certifyId,
                verifyMode: '1',
                // idNo: this.personInfo.idNo
            };
            realPersonIdentify(params).then(res => {
                if (res.success) {
                    let params = {
                        type: '1',
                        authInfo: res.data.authInfo,
                    };
                    this.cancelClick(params);
                    // this.$sKit.layer.useRouter('/packages/third-my-center/pages/account-cancel/main', {}, 'navigateTo')
                }
            });
        },
        //退出登录
        outLogin() {
            this.$sKit.commonUtil.logoutFun();
        },
        /*注销方法
      type	是	校验场景：
      1—实人认证后续操作；
      2—验证短信后续操作；
     */
        async cancelClick(params) {
            let res = await cancelAccountApi(params, { isCustomErr: true });
            const _this = this;
            if (res.success) {
                this.$store.dispatch('zjShowModal', {
                    title: '账号已注销成功， 感谢您的关注与支持！',
                    success: res => {
                        if (res.confirm) {
                            _this.outLogin();
                        }
                    },
                });
            } else {
                //
                this.$store.dispatch('zjShowModal', {
                    title: res.message,
                    success: res => {
                        if (res.confirm) {
                            uni.navigateBack({ delta: 1 });
                        }
                    },
                });
            }
        },
        // 下拉刷新
        // refreshPullDown () {
        //   setTimeout(() => {
        //     this.$refs.pullDownRefreshRef.stopRefresh();
        //     console.log('加载完成');
        //   }, 1000);
        //   console.log('下拉刷新了');
        // },
    },
    computed: {
        ...mapGetters(['memberBaseInfo', 'facePop']),
    },
    components: {},
};
</script>
<style scoped lang="scss">
.icon-50 {
    width: 50px;
    height: 50px;
}

.icon-40 {
    width: 40px;
    height: 40px;
}

.mart16 {
    margin-top: 16px;
}

.mar16 {
    margin: 16px 16px 0;
}

.marlr16 {
    margin: 0 16px;
}

.marl10 {
    margin-left: 10px;
}

.marlr12 {
    margin: 0 12px;
}

.padd12 {
    padding: 12px 0 0;
}

.marb6 {
    margin-bottom: 6px;
}

.btn {
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
    margin-top: 12px;
    margin-bottom: 125px;
}
</style>
