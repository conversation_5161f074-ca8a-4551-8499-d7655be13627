import checkFKArgs from '../../../../../s-kit/js/v3-native-jsapi/checkFKArgs';
import { mapGetters, mapState } from 'vuex';

export default {
    data() {
        return {
            payPlugin: {},
        };
    },
    onLoad() {},
    methods: {
        //获取充值方式
        async getRechargeMethod(params) {
            uni.showLoading({
                mask: true,
            });
            console.log('getRechargeMethod====');
            return new Promise(async (resolve, reject) => {
                try {
                    let res;
                    if (this.isHarmony) {
                        res = await this.setIconAndRechar(await this.payPlugin.GetRechargeTypeList(params));
                        resolve(res);
                    } else {
                        await this.$paymentCenter.getRechargePayTypeList(params, async resAPP => {
                            console.log(resAPP, 'resAPP====');
                            res = await this.setIconAndRechar(resAPP);
                            console.log(res, 'res是返回成功和失败的结果吗');
                            resolve(res);
                        });
                    }
                } catch (e) {
                    uni.hideLoading();
                    reject(e);
                }
            });
        },
        /**
         * 发起充值支付
         * @param areaCode 地区编码
         * @param bizOrderNo 业务订单编号
         * @param rcvAmt 应收总金额
         * @param realAmt 支付金额
         */
        async callUpPayment(resMP) {
            if (!this.isHarmony) {
                if (resMP.success) {
                    this.orderId = resMP.data.prePayId;
                    let params = {
                        areaCode: this.walletInfo.addressNo,
                        bizOrderNo: this.orderId,
                        rcvAmt: resMP.data.rechargeAmt + '',
                        realAmt: resMP.data.rechargeAmt + '',
                        payType: resMP.data.paymentType + '',
                        extendFiled: JSON.stringify(await checkFKArgs.getFKArgs('sdk')),
                    };
                    console.log(params, '111111');
                    this.$paymentCenter.rechargePay(
                        {
                            paramsJsonStr: encodeURIComponent(JSON.stringify(params)),
                        },
                        payRes => {
                            this.payingFlag = false;
                            if (this.$paymentCenter.resStatus(payRes)) {
                                this.otherAmount = '';
                                this.$sKit.layer.useRouter(
                                    '/packages/third-remaining-sum/pages/third-charge-result/main',
                                    { orderId: this.orderId, refer: this.refer, addressName: this.addressName },
                                    'navigateTo',
                                );
                            } else {
                                uni.showToast({
                                    title: payRes.msg || '支付失败，请重试',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            }
                        },
                    );
                }
            } else {
                uni.showLoading({
                    title: '加载中',
                });
                console.log('🚀 ~ file: zfb-wallet-recharge.js:80 ~ callUpPayment ~ resMP:', resMP);
                let params = {
                    areaCode: this.walletInfo.addressNo,
                    bizOrderNo: resMP.data.prePayId,
                    rcvAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                    realAmt: Number(this.otherAmount) || Number(this.rechargeAmt),
                    // 3.0.4风控字段
                    extendFiled: JSON.stringify({
                        dfp: '',
                        gps:
                            this.riskManagementLonV3 && this.riskManagementLatV3
                                ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
                                : '',
                        gpsProvince: '',
                        gpsCity: '',
                        gpsArea: '',
                    }),
                    openId: this.openId,
                    payType: resMP.data.paymentType + '',
                };
                const res = await this.payPlugin.RechargePay(params);
                uni.hideLoading();
                this.payingFlag = false;
                if (res.code === 'PAY_SUCCESS') {
                    this.otherAmount = '';
                    this.$sKit.layer.useRouter(
                        '/packages/third-remaining-sum/pages/third-charge-result/main',
                        { orderId: resMP.data.prePayId },
                        'navigateTo',
                    );
                } else {
                    this.closePopup();
                    // 截取字符串后面的数据
                    let errIndex = res.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = res.msg.slice(0, errIndex);
                        customErr = res.msg.slice(errIndex + 1, res.msg.length);
                    } else {
                        customErr = res.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
