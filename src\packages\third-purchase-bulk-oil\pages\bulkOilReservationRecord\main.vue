<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" title="预约记录"></zj-navbar>
            <div class="padding-16 f-1 mh-0">
                <zj-data-list
                    ref="dataList"
                    :emptyImage="noPicture"
                    :showEmpty="showEmpty"
                    background="#F7F7FB"
                    emptyText="暂无预约记录"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrollToLower"
                >
                    <div v-if="gasStationsList.length > 0">
                        <div
                            v-for="(station, index) in gasStationsList"
                            :key="index"
                            class="width100 border-rad-8 containerWrap bg-fff padding-16 fl-row fl-jus-bet fl-al-cen"
                        >
                            <div class="fl-column fl-jus-bet leftWrap">
                                <div class="titleLeft weight-500 font-14 color-333">{{ station.stationName }}</div>
                                <div class="fl-column weight-400 font-12 color-999">
                                    <div class="text">购油品种 {{ station.productNo }}</div>
                                    <div class="text">购油用途 {{ station.useOilPurpose }}</div>
                                    <div class="text">有效期至 {{ station.validTime }}</div>
                                </div>
                            </div>
                            <div class="imgWrap">
                                <img :src="pictureMap[station.applyStatus] || ''" alt="" />
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { bulkOilRegisterList } from '../../../../s-kit/js/v3-http/https3/bulkOil/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 预约记录列表
            gasStationsList: [
                // {
                //     stationName: '石大2号站加油站名称加油站名称…',
                //     productNo: '92号',
                //     useOilPurpose: '自驾车主',
                //     validTime: '2025-10-28 14:32:08',
                //     applyStatus: 7,
                // },
                // {
                //     stationName: '另一个加油站',
                //     productNo: '95号',
                //     useOilPurpose: '公司用车',
                //     validTime: '2024-12-31 23:59:59',
                //     applyStatus: 3,
                // },
                // {
                //     stationName: '另一个加油站',
                //     productNo: '95号',
                //     useOilPurpose: '公司用车',
                //     validTime: '2024-12-31 23:59:59',
                //     applyStatus: 8,
                // },
                // {
                //     stationName: '另一个加油站',
                //     productNo: '95号',
                //     useOilPurpose: '公司用车',
                //     validTime: '2024-12-31 23:59:59',
                //     applyStatus: 4,
                // },
            ],
            // 是否展示空态标识
            showEmpty: false,
            // 暂无电子券图片
            noPicture: require('../../images/kt1yhq.png'),
            pageNum: 1,
            pageSize: 10,
            totalPage: 0,
        };
    },
    components: {},
    computed: {
        pictureMap() {
            return {
                4: require('../../images/refuse.png'),
                2: require('../../images/applicationInProgress.png'),
                5: require('../../images/completed.png'),
                8: require('../../images/cancelled.png'),
                3: require('../../images/alreadyPassed.png'),
                7: require('../../images/invalid.png'),
            };
        },
    },
    mounted() {
        // 获取预约记录
        this.getAppointmentRecordList();
    },
    onLoad() {},
    methods: {
        /**
         * @description 获取预约列表
         * @param {Integer} pageNum 当前页码（必填）
         * @param {Integer} pageSize 每页大小（必填）
         * @param {Array<Number>} [applyStatus] 申请状态列表（可选），传空值表示查询所有状态
         *   1-创建
         *   2-审核中
         *   3-审核通过
         *   4-审核拒绝
         *   5-已提油
         *   6-拒绝提油
         *   7-已失效
         *   8-已取消
         * @returns {*}
         */
        async getAppointmentRecordList({ isInit = false } = {}) {
            // 初始化操作
            if (isInit) {
                this.gasStationsList = [];
                this.$refs.dataList.loadStatus = 'loading';
            }

            // 请求参数
            const params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                applyStatus: [],
            };
            console.log(params, 'params===');
            // 发起请求
            const res = await bulkOilRegisterList(params);
            // 处理响应
            if (res.success) {
                if (res.data.rows?.length > 0) {
                    console.log(res.data.rows, '预约记录');
                    // 更新数据
                    // 合并上一次的数据和本次请求的数据
                    this.gasStationsList = this.gasStationsList.concat(res.data.rows);

                    // 更新加载状态
                    this.$refs.dataList.stopRefresh();
                    this.$refs.dataList.pullDownHeight = 0;
                    this.$refs.dataList.pullingDown = false;
                }
            } else {
                // 处理请求失败的情况
                console.error('Failed to fetch appointment records:', res.message);
                this.$refs.dataList.loadStatus = 'error';
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
            }
            // 判断是否还有更多数据
            if (this.gasStationsList.length >= 10) {
                this.$refs.dataList.loadStatus = 'contentdown';
            } else {
                this.$refs.dataList.loadStatus = 'nomore';
            }
            // 判断是否显示空状态
            this.showEmpty = this.gasStationsList.length === 0;
        },
        /**
         * @description  : 上拉加载
         * @return        {*}
         */
        scrollToLower() {
            if (this.$refs.dataList.loadStatus === 'contentdown') {
                this.pageNum++;
                this.$refs.dataList.loadStatus = 'loading';
                this.getAppointmentRecordList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown() {
            // 重置数据获取电子券列表
            this.pageNum = 1;
            this.getAppointmentRecordList({ isInit: true });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .containerWrap {
        height: 230rpx;
        margin-bottom: 24rpx;
        .leftWrap {
            width: 65%;
            height: 100%;
            .titleLeft {
                white-space: nowrap; /* 文本强制不换行 */
                text-overflow: ellipsis; /* 文本溢出显示省略号 */
                overflow: hidden; /* 溢出的部分隐藏 */
            }
            .text {
                margin-bottom: 3rpx;
                line-height: 33rpx;
            }
        }

        .imgWrap {
            width: 199rpx;
            height: 199rpx;
            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
