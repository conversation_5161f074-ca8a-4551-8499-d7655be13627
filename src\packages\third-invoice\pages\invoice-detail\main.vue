<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <!-- 发票详情页面 -->
    <div class="pageMpaas">
        <zj-navbar :border-bottom="false" title="查看发票"></zj-navbar>
        <div class="bg-F7F7FB view" v-if="Object.keys(pageInfo).length > 0">
            <div class="content">
                <div class="content-text-item">
                    <div class="content-text-left">发票类型</div>
                    <div class="content-text-right">{{ getInvoiceType(pageInfo.invoiceTypeCode) }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">开票日期</div>
                    <div class="content-text-right">{{ pageInfo.issueDate }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">纳税人识别号</div>
                    <div class="content-text-right">{{ pageInfo.sellerTaxId }}</div>
                </div>
                <div class="content-text-item">
                    <div class="line"></div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">销售方</div>
                    <div class="content-text-right">{{ pageInfo.sellerName }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">销售方地址</div>
                    <div class="content-text-right">{{ pageInfo.sellerAddrTel }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">购买方</div>
                    <div class="content-text-right">{{ pageInfo.buyerName }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">购买方识别号</div>
                    <div class="content-text-right">{{ pageInfo.buyerTaxId || '' }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">购买方性质</div>
                    <div class="content-text-right">{{ getBuyerNature(pageInfo.buyerNature) }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">发票代码</div>
                    <div class="content-text-right">{{ pageInfo.invoiceCode || '' }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">发票号码</div>
                    <div class="content-text-right">{{ pageInfo.invoiceNo }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">合计金额</div>
                    <div class="content-text-right">{{ pageInfo.taxInclusiveTotalAmount }}</div>
                </div>
                <div class="content-text-item">
                    <div class="content-text-left">合计税额</div>
                    <div class="content-text-right last">{{ pageInfo.taxTotalAmount }}</div>
                </div>
            </div>
            <div class="content2" v-if="pageInfo.orderNumList.length > 0 && pageInfo.orderNumList[0].length > 20">
                <div class="left">
                    发票含
                    <div>{{ pageInfo.orderNumList.length }}</div
                    >个消费订单
                </div>
                <div class="right" @click="seeOrderList">
                    查看订单
                    <img src="../../image/right.png" alt />
                </div>
            </div>
            <div class="tips">仅支持换开365天内开具的发票</div>
            <!-- #ifdef MP-MPAAS -->
            <template v-if="pageInfo.invoiceTypeCode == '082'">
                <div class="sdBtnWrap">
                    <div @click="copyPath('out')">下载发票</div>
                </div>
            </template>
            <template v-else>
                <div class="btnWrap">
                    <div v-if="pageInfo.invoiceTypeCode !== '025'" @click="clickLookInvoice">预览</div>
                    <div v-if="pageInfo.invoiceTypeCode !== '025'" @click="copyPath('download')">下载发票</div>
                </div>
                <div class="detail-download" v-if="pageInfo.invoiceTypeCode == '026'">
                    <div class="box_first">请使用自助开票设备扫描二维码进行打印。</div>
                    <div class="box_second" @click="selfServicePrinting">自助打印</div>
                </div>
            </template>
            <!-- #endif -->
            <!-- #ifndef MP-MPAAS -->
            <template v-if="pageInfo.invoiceTypeCode == '082'">
                <div class="sdBtnWrap">
                    <div @click="copyPath('copy')">复制下载链接</div>
                </div>
            </template>
            <template v-else>
                <div class="btnWrap">
                    <div v-if="pageInfo.invoiceTypeCode !== '025'" @click="clickLookInvoice">预览</div>
                    <div v-if="pageInfo.invoiceTypeCode == '026'" @click="selfServicePrinting">自助打印</div>
                </div>
                <div class="detail-download" v-if="pageInfo.invoiceTypeCode !== '025'">
                    <div class="box_first">请复制该链接在电脑浏览器打开进行下载。</div>
                    <div class="box_second" @click="copyPath('copy')">复制下载链接</div>
                </div>
            </template>
            <!-- #endif -->
            <div class="replaceInvoice" v-if="isShowReplace" @click="exchangeInvoice">
                发票抬头换开
                <div class="arroeRight"></div>
            </div>
            <div class="replaceInvoice" v-else-if="pageInfo.invoiceTypeCode == '082' && pageInfo.changeTitleMark == 4">
                发票拾头不可换开，原因：数电蓝票已使用，无法换开！</div
            >
        </div>
        <zj-show-modal>
            <invoiceChangeTips></invoiceChangeTips>
        </zj-show-modal>
    </div>
</template>
<script>
import { invoiceDownloadApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import invoiceChangeTips from '../../components/invoice-change-tips/main.vue';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'invoiceHeaderDetails',
    components: { invoiceChangeTips },
    data() {
        return {
            // 展示数据汇总
            pageInfo: {},
            isShowReplace: false,
        };
    },
    onLoad(options) {
        this.pageInfo = JSON.parse(decodeURIComponent(options.data));
        if (!this.pageInfo.issueDate) {
            this.pageInfo.issueDate = this.pageInfo.invoiceIssueDate;
        }
        this.getIsShowReplace();
        this.$sKit.mpBP.tracker('电子发票', {
            seed: 'invoiceBiz',
            pageID: 'invoiceDetailPage',
            invoiceDate: this.pageInfo.issueDate,
            invoiceMoney: this.pageInfo.taxInclusiveTotalAmount,
            invoiceCode: this.pageInfo.invoiceCode || '',
            refer: this.pageInfo.refer || '',
            channelID: clientCode,
        });
    },
    created() {},
    mounted() {},
    computed: {},
    methods: {
        /**
         * @description  : 查看关联订单列表
         * @return        {*}
         */
        seeOrderList() {
            let params = {
                orderNum: this.pageInfo.orderNumList,
                invoiceCode: this.pageInfo.invoiceCode || '',
                issueDate: this.pageInfo.issueDate,
            };
            if (this.pageInfo.unifiedTags[0] == '1') {
                params.orderType = 'charge';
            }
            let url = '/packages/third-invoice/pages/invoice-order-list/main';
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 判断发票是否可以抬头换开，开票时间到现在超过365天无法换开，已换开发票无法换开
         * @return        {*}
         */
        getIsShowReplace() {
            if (this.pageInfo.invoiceTypeCode == '025') {
                this.isShowReplace = false;
                return;
            }
            let date = Date.parse(new Date()); //获取当前时间戳（毫秒）
            let timestamp = Date.parse(new Date(this.pageInfo.issueDate.replace(/-/g, '/'))); //获取开票时间戳（毫秒）
            let ninetyDays = 31536000000; //365天的毫秒数
            let noExpiration = timestamp + ninetyDays > date; //开票时间加365天大于当前时间说明没有过期
            // 等于1是换开过的不在展示 365天以后不在显示换开
            this.isShowReplace = !this.pageInfo.changeTitleMark && noExpiration;
        },
        /**
         * @description  : 发票预览
         * @return        {*}
         */
        async clickLookInvoice() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'previewInvoicePage',
                refer: this.pageInfo.refer,
                channelID: clientCode,
            });
            let params = {
                orgCode: this.pageInfo.orgCode,
                invoiceCode: this.pageInfo.invoiceCode || '',
                invoiceNo: this.pageInfo.invoiceNo || '',
                issueDate: this.pageInfo.issueDate,
                dlType: '3', //1-pdf文件流 2-自助打印码 3-短链接
                invoiceType: this.pageInfo.invoiceTypeCode,
            };
            let res = await invoiceDownloadApi(params);
            if (res && res.success) {
                if (res.data.fileUrl) {
                    // #ifdef MP-MPAAS
                    this.$cnpcBridge.checkPDF(res.data.fileUrl);
                    // #endif
                    // #ifdef MP-ALIPAY
                    uni.navigateTo({
                        url: `/mpaas/packages/web-view/pages/home/<USER>
                    });
                    // #endif
                    // #ifdef MP-WEIXIN
                    uni.navigateTo({
                        url: `/packages/web-view/pages/home/<USER>
                    });
                    // #endif
                    // #ifdef H5-CLOUD
                    this.$sKit.layer.viewPdfAgreement(res.data.fileUrl);
                    // #endif
                }
            }
        },
        /**
         * @description  : 复制发票预览链接
         * @return        {*}
         */
        async copyPath(type) {
            let params = {
                orgCode: this.pageInfo.orgCode,
                invoiceCode: this.pageInfo.invoiceCode || '',
                invoiceNo: this.pageInfo.invoiceNo || '',
                issueDate: this.pageInfo.issueDate,
                dlType: '3', //1-pdf文件流 2-自助打印码 3-短链接
                invoiceType: this.pageInfo.invoiceTypeCode,
            };
            let res = await invoiceDownloadApi(params);
            if (res && res.success) {
                if (res.data.fileUrl) {
                    if (type == 'copy') {
                        uni.setClipboardData({
                            data: res.data.fileUrl,
                            success() {
                                uni.showToast({
                                    title: '复制成功，请在浏览器打开',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            },
                        });
                    } else if (type == 'download') {
                        const data = {
                            url: res.data.fileUrl,
                        };
                        this.$cnpcBridge.downloadPdf(data, res => {
                            if (res.code == '1') {
                                //ios不需要弹窗，downloadPdf协议会打开系统保存框
                                if (uni.getSystemInfoSync().osName === 'ios') return;
                                this.$store.dispatch('zjShowModal', {
                                    title: '提示',
                                    content: `发票已保存至${res.filePath}目录下,请打开文件管理器查看。`,
                                    confirmText: '查看文件',
                                    cancelText: '取消',
                                    success: async res2 => {
                                        if (res2.confirm) {
                                            this.$cnpcBridge.openFile({ filePath: res.filePath });
                                        } else if (res2.cancel) {
                                            console.log('用户点击取消');
                                        }
                                    },
                                });
                            } else {
                                uni.showToast({
                                    title: res.error,
                                });
                            }
                        });
                    } else if (type == 'out') {
                        this.$cnpcBridge.mobileBrowsers(res.data.fileUrl);
                    }
                }
            }
        },
        // 发票类型编码文本转换
        getInvoiceType(invoiceTypeCode) {
            let text = '';
            switch (invoiceTypeCode) {
                case '026':
                    text = '增值税电子普通发票';
                    break;
                case '025':
                    text = '增值税普通发票（卷式）';
                    break;
                case '082':
                    text = '数字化电子普通发票';
                    break;
            }
            return text;
        },
        /**
         * @description  : 根据发票抬头类型字段内容回显文本
         * @param         {*} buyerNature:抬头类型
         * @return        {*}
         */
        getBuyerNature(buyerNature) {
            let text = '';
            if (this.pageInfo.invoiceTypeCode == '082') {
                if (buyerNature == 1 || buyerNature == 2) {
                    text = '非自然人';
                } else if (buyerNature == 3) {
                    text = '自然人';
                }
            } else {
                if (buyerNature == 1) {
                    text = '企业';
                } else if (buyerNature == 2) {
                    text = '个人';
                }
            }
            return text;
        },
        /**
         * @description  : 自助开票页面跳转
         * @return        {*}
         */
        async selfServicePrinting() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'selfPrintingPage',
                refer: this.pageInfo.refer,
                channelID: clientCode,
            });
            let params = {
                orgCode: this.pageInfo.orgCode,
                invoiceCode: this.pageInfo.invoiceCode || '',
                invoiceNo: this.pageInfo.invoiceNo || '',
                issueDate: this.pageInfo.issueDate,
                //dlType  1-pdf文件流 2-自助打印码 3-短链接
                dlType: '2',
                invoiceType: this.pageInfo.invoiceTypeCode,
            };
            let res = await invoiceDownloadApi(params);
            if (res && res.success) {
                let url = '/packages/third-invoice/pages/self-invoice/main';
                let params = { printCode: res.data.printCode };
                this.$sKit.layer.useRouter(url, params);
            }
        },
        /**
         * @description  : 发票抬头换开（红冲）页面跳转
         * @return        {*}
         */
        exchangeInvoice() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'invoice_hk_toast',
                refer: this.pageInfo.refer,
                channelID: clientCode,
            });
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认换开',
                cancelText: '我再想想',
                confirmColor: '#000',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'confirmReplaceBut',
                            refer: this.pageInfo.refer,
                            channelID: clientCode,
                        });
                        let params = {
                            invoiceInfo: this.pageInfo,
                            type: 'change',
                            refer: this.pageInfo.refer || '',
                            orderType: this.pageInfo.unifiedTags[0] == '1' ? 'charge' : '',
                        };
                        let url = '/packages/third-invoice/pages/invoice-form/main';
                        this.$sKit.layer.useRouter(url, params);
                    } else if (res.cancel) {
                        this.$sKit.mpBP.tracker('电子发票', {
                            seed: 'invoiceBiz',
                            pageID: 'thinkBut',
                            refer: this.pageInfo.refer,
                            channelID: clientCode,
                        });
                        console.log('用户点击取消');
                    }
                },
            });
        },
    },
};
</script>
<style scoped lang="scss">
.view {
    flex: 1;
    min-height: 0;
    padding: 32rpx;
    overflow-y: scroll;

    .content {
        display: flex;
        flex-direction: column;
        background: #fff;
        border-radius: 16rpx;

        .content-text-item {
            display: flex;
            justify-content: space-between;
            margin-left: 15px;
            margin-top: 13px;
            margin-right: 15px;

            .content-text-left {
                width: 50%;
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                color: #666666;
                line-height: 20px;
            }

            .content-text-right {
                width: 100%;
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                word-break: break-word;
                white-space: pre-line;
                text-align: right;
            }

            .line {
                width: 100%;
                border-bottom: 1px solid #efeff4;
            }

            .last {
                margin-bottom: 15px;
            }
        }
    }

    .content2 {
        height: 127rpx;
        background: #fff;
        padding: 0 28rpx;
        border-radius: 16rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;

        .left {
            font-size: 24rpx;
            font-weight: bold;
            color: #333333;
            display: flex;
            align-items: baseline;

            div {
                font-size: 24rpx;
                font-weight: bold;
                color: #e64f22;
            }
        }

        .right {
            font-size: 24rpx;
            font-weight: bold;
            color: #999999;
            display: flex;
            align-items: center;

            img {
                width: 33rpx;
                height: 33rpx;
            }
        }
    }

    .tips {
        font-size: 24rpx;
        font-weight: 400;
        color: #333333;
        line-height: 33rpx;
        text-align: center;
        margin-top: 24rpx;
    }

    .btnWrap {
        display: flex;
        justify-content: space-between;
        margin-top: 32rpx;

        div {
            width: 331rpx;
            height: 88rpx;
            border-radius: 8px;
            font-weight: 400;
            text-align: center;
            line-height: 44px;
            font-size: 32rpx;

            &:nth-of-type(1) {
                border: 1px solid #e64f22;
                color: #e64f22;
                background: #ffffff;
            }

            &:nth-of-type(2) {
                color: #ffffff;
                background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            }
        }
    }

    .sdBtnWrap {
        display: flex;
        justify-content: space-between;
        margin-top: 32rpx;

        div {
            width: 100%;
            height: 88rpx;
            border-radius: 8px;
            font-weight: 400;
            text-align: center;
            line-height: 44px;
            font-size: 32rpx;
            color: #ffffff;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        }
    }

    .detail-download {
        padding: 24rpx;
        border-radius: 16rpx;
        background: #fff;
        color: #999;
        font-size: 26rpx;
        margin-top: 24rpx;
        // display: inline-block;
        // white-space: pre-wrap;
        // word-break: break-all;

        .box_first {
            float: left;
        }

        .box_second {
            color: #f96702;
            text-decoration: underline;
        }

        // div {
        //     display: inline-block;
        // }
    }

    .replaceInvoice {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        color: #e64f22;
        line-height: 20px;
        margin-top: 24rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        .arroeRight {
            box-sizing: border-box;
            width: 8px;
            height: 8px;
            border: solid #e64f22;
            border-width: 0 2px 2px 0;
            transform: rotate(-45deg);
        }
    }
}
</style>
