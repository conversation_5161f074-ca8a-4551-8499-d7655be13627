<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="bg-F7F7FB fl-column" style="height: 100%">
            <zj-navbar :background="{}" :height="44" title="消息">
                <!-- #ifdef MP-MPAAS -->
                <img
                    v-if="list.length > 0"
                    @click="removalMessage"
                    class="title_img"
                    slot="title_img"
                    src="../../images/delete.png"
                    alt=""
                />
                <!-- #endif -->
            </zj-navbar>
            <!-- #ifdef MP-MPAAS -->
            <div class="header-top" v-if="list.length > 0">
                <div :style="{ height: `${systemBar}px` }"></div>
                <div @click="allRead()" class="title_text">全部已读</div>
            </div>
            <!-- #endif -->
            <div class="f-1 mh-0">
                <zj-data-list
                    background="#F7F7FB"
                    ref="dataList"
                    emptyText="暂无数据"
                    :emptyImage="require('../../images/no_data_new.png')"
                    :showEmpty="showEmpty"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrolltolower"
                >
                    <div class="content_div">
                        <div v-for="(item, index) in list" :key="index" class="item_div" @click="toDetail(item, item.id, index)">
                            <div @longpress="handleLongPress(item)" class="content">
                                <div v-if="unSelectFlag" class="img_wrap" @click.stop="isSelcectImg(index)">
                                    <img v-if="!item.checked" class="unSelect_img" src="../../images/unSelect.png" alt="" />
                                    <img v-if="item.checked" class="unSelect_img" src="../../images/select.png" alt="" />
                                </div>
                                <div class="content_bottom">
                                    <div class="time_div">{{ item.createTime }}</div>
                                    <div class="message_div bg-fff">
                                        <div class="title_div">
                                            <div class="title ellipsis">{{ item.messageTitle }}</div>
                                            <div v-if="item.messageStatus == 1" class="right_circle"></div>
                                        </div>
                                        <div class="more_div line_top">
                                            <div class="f-1 more_div_text" style="margin-right: 15px" v-if="checkTitle(item, 'content')">
                                                <!-- <u-parse :html="escape2Html(item.messageContent)"></u-parse> -->
                                                <rich-text class="rich_text" :nodes="item.messageContent"></rich-text>
                                            </div>
                                            <div class="f-1 more_div_text ellipsis" style="margin-right: 15px" v-else>{{
                                                removeHTMLTag(item.messageContent)
                                            }}</div>
                                            <img class="right" src="../../images/right.png" alt="" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <div class="bill-box" v-if="unSelectFlag">
                <div class="order-operation" @click="checkAll()">
                    <div>
                        <radio color="#e64f22" :checked="allCheckBox" value="isAlll" style="transform: scale(0.65)" />
                    </div>
                    <div class="all">全选</div>
                </div>
                <div class="error" @click="deleteAll" :style="{ opacity: list.length > 0 ? '1' : '0.2' }">确认删除 </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userGroupNewsList, userMessageUpdateStatus, userMessageDelete } from '../../../../s-kit/js/v3-http/https3/preferentialGroup/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // selectId-1为系统消息2为个人消息根据撞他判断你点击的是哪个从而实现高亮
            //list-页面循环出来的消息列表
            //pageNum-当前页数
            //pageSize-显示多少条
            //bottomBounce-上拉加载下拉刷新返回第一页
            selectId: 2,
            diffHeight: 0,
            list: [],
            showEmpty: false,
            pageNum: 1,
            pageSize: 10,
            show: false,
            bottomBounce: false,
            flag: false,
            // 未选中图片显示
            unSelectFlag: false,
            // 全选
            isSelectFlag: true,
            // 选中的站内信数组
            checkedList: [],
            //
            allCheckBox: false,
            // 系统导航栏的高度
            systemBar: '',
        };
    },
    created() {
        // location.reload();
    },
    onShow() {
        // this.pageNum = 1
        // this.list = []
        // this.getData({ isInit: true });
    },
    async mounted() {
        // #ifdef MP-MPAAS
        this.$cnpcBridge.getBarHeight(res => {
            this.systemBar = res;
        });
        // #endif
        this.$nextTick(async () => {
            setTimeout(async () => {
                await this.getData({ isInit: true });
                this.messageBiz('exposure');
            }, 100);
        });
    },
    watch: {
        list: {
            handler(newValue, oldValue) {
                if (newValue.length > 0) {
                    // this.checkedList = val.filter(item => item.checked).map(item => item.id);
                    // console.log(this.checkedList, 'this.checkedList');
                    // if (this.checkedList.length == this.list.length) {
                    //     this.allCheckBox = true;
                    // } else {
                    //     this.allCheckBox = false;
                    // }
                    this.checkedList = newValue.filter(item => item.checked).map(item => item.id);
                    console.log(this.checkedList, 'this.checkedList');
                    this.allCheckBox = newValue.every(item => item.checked);
                    console.log(this.allCheckBox, 'watch=====');
                }
                // if (oldValue != undefined && oldValue.length > 0 && newValue.length > 0) {
                //     if (!this.arraysAreEqual(newValue, oldValue)) {
                //         console.log('Array updated:', newValue);
                //         // 在这里添加你要执行的操作，比如把新的数组覆盖给旧的数组
                //         this.list = newValue; // 覆盖给旧数组
                //     }
                // }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // // 数组比对
        // arraysAreEqual: function (arr1, arr2) {
        //     if (arr1.length !== arr2.length) {
        //         return false;
        //     }
        //     for (let i = 0; i < arr1.length; i++) {
        //         if (arr1[i] !== arr2[i]) {
        //             return false;
        //         }
        //     }
        //     return true;
        // },
        messageBiz(dateType) {
            let bizContent = this.list.map(item => {
                let state = '';
                if (item.messageStatus == 1) {
                    state = 'unread';
                } else if (item.messageStatus == 2) {
                    state = 'read';
                }
                let obj = {
                    state: state,
                    messageID: item.id,
                    messageType: item.messageType,
                    bizMessageTitle: item.messageTitle,
                };
                return obj;
            });
            bizContent = JSON.stringify(bizContent).replace(/,/g, ' ');
            this.$sKit.mpBP.tracker('消息中心', {
                seed: 'messageBiz',
                pageID: 'messagePage',
                dateType: dateType,
                content: bizContent,
                channelID: clientCode,
            });
        },
        escape2Html(str) {
            var arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };
            let newStr = str.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all, t) {
                return arrEntities[t];
            });
            newStr = newStr.replace(/\<p/gi, '<p class="rich-txt-p" ');
            return newStr;
        },
        // 上拉加载
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getData();
            }
        },
        // 下拉刷新
        refreshPullDown(e) {
            this.getData({ isInit: true }); // 重置数据
        },
        // 获取列表信息
        async getData({ isInit = false } = {}) {
            return new Promise(async (resolve, reject) => {
                if (isInit) {
                    // 重置入参页码
                    this.list = [];
                    this.pageNum = 1;
                    this.$refs.dataList.loadStatus = 'loading';
                }
                let params = {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                };
                let res = await userGroupNewsList(params);
                //获取数据成功后
                this.$refs.dataList.stopRefresh();
                if (res && res.success) {
                    let arr = res.data.rows.map(item => {
                        item.checked = false;
                        // if (this.checkTitle(item, 'content')) {
                        item.messageContent = this.escape2Html(item.messageContent);
                        // }
                        return item;
                    });
                    if (this.pageNum == 1) {
                        this.list = arr;
                    } else {
                        this.list = this.list.concat(arr || []);
                    }
                    // 如果下一页还有数据的情况下 取消全选
                    if (arr.length > 0) {
                        this.allCheckBox = false;
                    }
                    this.pageNum++;
                    if (this.pageSize > arr.length) {
                        this.$refs.dataList.loadStatus = 'nomore';
                    } else {
                        this.$refs.dataList.loadStatus = 'contentdown';
                    }
                }
                if (this.list.length == 0) {
                    this.showEmpty = true;
                } else {
                    this.showEmpty = false;
                }
                this.flag = true;
                resolve();
            });
        },
        //点击超链接跳转吧href清除掉
        // getJump(event) {
        //     if (event.target.nodeName == 'A') {
        //         let tagA = event.target
        //         tagA.removeAttribute("href")
        //     }
        // },

        //点击查看详情跳转详情的页面并且携带参数id
        toDetail(item, id, i) {
            const resObj = this.checkTitle(item, 'route');
            if (item.messageStatus == 1) {
                this.allRead(item, i);
            } else {
                this.list[i].messageStatus = 2;
            }
            this.messageBiz('click');
            if (resObj) {
                this.$sKit.layer.useRouter(resObj.url, resObj.params, resObj.type);
            }
        },
        checkTitle(item, funType) {
            let url = '/packages/third-message/pages/detail/main';
            let params = { id: item.id };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            let flag = false;
            switch (item.messageTitle) {
                case '电子券到账提醒':
                    url = '/packages/third-coupon-module/pages/coupon-list/main';
                    params = {
                        refer: 'r39',
                    };
                    flag = true;
                    break;
                case '充值成功通知':
                    url = '/packages/third-order/pages/home/<USER>';
                    params = { navActive: 2, refer: 'r50' };
                    flag = true;
                    break;
                case '昆仑e享卡退款成功通知':
                    url = '/packages/third-my-wallet/pages/home/<USER>';
                    params = { refer: 'r05' };
                    flag = true;
                    break;
                case '实体卡资金归集成功通知':
                    url = '/packages/third-my-wallet/pages/home/<USER>';
                    params = { refer: 'r05' };
                    flag = true;
                    break;
                case '订单支付成功通知':
                    url = '/packages/third-order/pages/order-detail/main';
                    let extraAttribute = JSON.parse(item.extraAttribute);
                    if (extraAttribute?.orderNo) {
                        params = {
                            orderNo: extraAttribute.orderNo,
                            refer: 'r50',
                        };
                    } else {
                        if (funType == 'route') {
                            return false;
                        }
                    }
                    flag = true;
                    break;
                case '发票开具成功通知':
                    url = '/packages/third-invoice/pages/home/<USER>';
                    params = {
                        navActiveProps: 2,
                        refer: 'r34',
                    };
                    flag = true;
                    break;
            }
            if (funType == 'route') {
                return { url, params, type };
            } else if (funType == 'content') {
                return flag;
            }
        },
        // 去除html标签
        removeHTMLTag(str) {
            str = str.replace(/<\/?[^>]*>/g, ''); //去除HTML tag
            str = str.replace(/[ | ]*\n/g, '\n'); //去除行尾空白
            str = str.replace(/\n[\s| | ]*\r/g, '\n'); //去除多余空行
            str = str.replace(/&nbsp;/gi, ''); //去掉&nbsp;
            str = str.replace(/&lt;/gi, '');
            str = str.replace(/&gt;/gi, '');
            str = str.replace(/[div|\/div]/g, '');
            str = str.replace(/[span|\/span]/g, '');
            str = str.replace(/&lt;[ -~]*&gt;/gi, ''); //去掉替换后的<>标签
            return str;
        },
        // 解析模板参数
        // parseTemplate(tempStr) {
        //     let obj = JSON.parse(tempStr);
        //     let arr = [];
        //     for (const key in obj) {
        //         if ((key == '优惠金额' && obj[key] == '0') || key == 'orderNo') {
        //         } else {
        //             arr.push({
        //                 key: key,
        //                 value: obj[key],
        //             });
        //         }
        //     }
        //     console.log(arr, '解析模板字符串');
        //     return arr;
        // },
        // 点击删除按钮显示多选框
        removalMessage() {
            if (this.list.length > 0) {
                this.list.forEach(item => {
                    item.checked = false;
                });
                this.unSelectFlag = !this.unSelectFlag;
            }
        },
        // 选中某一条站内信
        isSelcectImg(index) {
            this.list[index].checked = !this.list[index].checked;
            // this.isSelectFlag = !this.isSelectFlag;
        },
        // 全选
        async checkAll() {
            // 根据勾选状态生成选中项的列表
            this.checkedList = this.list.filter(item => item.checked).map(item => item.id);
            console.log(this.checkedList, 'this.checkedList');

            // 检查是否所有项都被勾选
            this.allCheckBox = this.list.every(item => item.checked);

            // 切换所有项的勾选状态
            this.list.forEach(async (item, index) => {
                item.checked = !this.allCheckBox;
                if (this.list.length === index + 1) {
                    console.log('1111111', this.list.length, index + 1);
                    await new Promise(resolve => setTimeout(resolve, 0)); // 添加await来暂停循环
                }
            });
            console.log(this.allCheckBox, 'this.allCheckBox===全选');
            // this.allCheckBox = !this.allCheckBox;
            console.log(this.allCheckBox, 'this.allCheckBox===全选222222');
            // 切换全选框状态

            // // 根据勾选状态生成选中项的列表
            // this.checkedList = this.list.filter(item => item.checked).map(item => item.id);
            // console.log(this.checkedList, 'this.checkedList');

            // // 检查是否所有项都被勾选
            // this.allCheckBox = this.list.every(item => item.checked);
            // console.log(this.allCheckBox,'this.allCheckBox');
            // // 切换所有项的勾选状态
            // for (const item of this.list) {
            //     item.checked = !this.allCheckBox;
            //     // 此处可以添加一些逻辑，如果有的话
            //     await new Promise(resolve => setTimeout(resolve, 0)); // 添加await来暂停循环
            // }
            // 循环执行完后切换全选框状态
            // this.allCheckBox = !this.allCheckBox;
        },
        // 确认删除吗
        deleteAll() {
            if (this.checkedList.length > 0) {
                console.log(this.checkedList, 'this.checkedList');
                this.$store.dispatch('zjShowModal', {
                    content: '确认从消息列表中删除？消息删除后将无法恢复！',
                    // title: '确认删除？',
                    confirmText: '确认',
                    cancelText: '我再想想',
                    confirmColor: '#333',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            console.log('站内信删除事件用户点击确定');
                            this.confirmDelete(this.checkedList);
                        } else if (res.cancel) {
                            // this.checkedList = [];
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                uni.showToast({
                    title: '请选择要删除的消息',
                    icon: 'none',
                    mask: true,
                    duration: 2000,
                });
            }
        },
        // 确认删除
        async confirmDelete(ids) {
            let params = {
                ids,
            };
            let res = await userMessageDelete(params);
            if (res.success) {
                this.list = [];
                this.checkedList = [];
                this.unSelectFlag = false;
                this.allCheckBox = false;
                this.getData({ isInit: true });
                uni.showToast({
                    title: '删除成功',
                    icon: 'none',
                    mask: true,
                });
            }
        },
        // 长按删除确定事件
        handleLongPress(item) {
            if (this.unSelectFlag) return;
            console.log(item, '长按删除');
            this.$store.dispatch('zjShowModal', {
                content: '确认删除该消息？',
                // title: '确认删除？',
                confirmText: '确认',
                cancelText: '我再想想',
                confirmColor: '#333',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        this.longPressDelete([item.id]);
                        console.log('站内信长按删除事件用户点击确定');
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 长按删除
        async longPressDelete(ids) {
            let params = {
                ids,
            };
            let res = await userMessageDelete(params);
            if (res.success) {
                this.getData({ isInit: true });
                uni.showToast({
                    title: '删除成功',
                    icon: 'none',
                    mask: true,
                });
            }
        },
        async allRead(item, i) {
            console.log('全部已读');
            let ids = this.list.filter(item => item.messageStatus === 1).map(item => item.id);
            if (ids.length > 0) {
                let params = {
                    messageStatus: 1,
                };
                if (item) {
                    params.ids = [item.id];
                    this.list[i].messageStatus = 2;
                }
                console.log(params, '更新站内信状态的入参');
                let res = await userMessageUpdateStatus(params);
                if (res.success) {
                    if (!item) {
                        this.getData({ isInit: true });
                    }
                }
            } else {
                uni.showToast({
                    title: '暂无未读消息',
                    icon: 'none',
                    duration: 2000,
                    mask: true,
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.title_img {
    width: 14.5px;
    height: 14.5px;
    position: absolute;
    top: 24%;
    left: 65%;
}
.header-top {
    position: fixed;
    // width: 100%;
    right: 15px;
    z-index: 999;
    .title_text {
        height: 44px;
        line-height: 44px;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        // margin-top: 53px;
    }
}

.content_div {
    padding: 16px;
    .unSelect_img {
        width: 20px;
        height: 20px;
        margin-right: 15px;
        margin-top: 52rpx;
    }
    .time_div {
        font-size: 10px;
        font-weight: 400;
        color: #666666;
        margin-bottom: 12px;
        margin-top: 12px;
    }
    .content {
        display: flex;
        align-items: center;
        .content_bottom {
            width: 100%;
            overflow: hidden;
        }
    }
    .message_div {
        border-radius: 8px;
        padding: 12px;
        flex: 1;
        overflow: hidden;
        .title_div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            line-height: 45rpx;
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 16rpx;

            .right_circle {
                width: 8px;
                height: 8px;
                border-radius: 50% 50%;
                background: #e64f22;
            }
        }

        .more_div {
            min-height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 14rpx;

            .more_div_text {
                line-height: 40rpx;
                font-size: 14px;
                color: #666666;
            }
            .right {
                width: 14rpx;
                height: 24rpx;
            }
        }

        .line_top {
            border-top: 1px solid #eeeeee;
        }
    }
}
::v-deep .rich_text {
    .rich-txt-p {
        line-height: 20px;
        font-size: 14px;
        color: #666666;
        padding: 0;
        margin: 0;
    }
}
.bill-box {
    width: 100%;
    height: 152rpx;
    background: #ffffff;
    border-radius: 4rpx 4rpx 0rpx 0rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;

    .order-operation {
        display: flex;
        align-items: center;

        .all {
            margin-right: 15px;
        }

        .order-operation-price {
            span {
                font-size: 24rpx;
                font-weight: bold;
                color: #333333;
                line-height: 33rpx;
            }

            .heightLight {
                color: #e64f22;
            }
        }
    }

    .error {
        width: 331rpx;
        height: 88rpx;
        border-radius: 4px;
        text-align: center;
        line-height: 44px;
        font-size: 16px;
        color: #e64f22;
        // opacity: 0.2;
        border: 1rpx solid #e64f22;
    }
}
</style>
