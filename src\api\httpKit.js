// import http from '../utils/http3'
import http from '../utils/http';

export const POST = (url, params, config, isload, isCustomErr, handleError) => {
    // http.config.isLoad = (isload === false ? false : true)
    // http.config.isCustomErr = (isCustomErr === false ? false : true)
    if (typeof handleError === 'function') {
        http.config.handleError = handleError;
    }
    config = {
        isLoad: isload === false ? false : true,
        isCustomErr: isCustomErr === false ? false : true,
    };
    return http.post(url, params, config);
};

export const GET = (url, params, config, isload, isCustomErr, handleError) => {
    http.config.isLoad = isload === false ? false : true;
    http.config.isCustomErr = isCustomErr === false ? false : true;
    if (typeof handleError === 'function') {
        http.config.handleError = handleError;
    }
    return http.get(url, params, config);
};
