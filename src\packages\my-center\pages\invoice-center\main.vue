<template>
    <div class="add-center">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            back-text="发票设置"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>

        <scroll-view class="scroll-view_H" :scroll-left="scrollLeft" scroll-x="true">
            <div class="slide_type_list">
                <div
                    class="slide_type_list_view"
                    v-for="(item, index) in typeList"
                    :key="index"
                    :class="{ is_selected: active == index }"
                    @click="changeType(item, index)"
                >
                    <div>{{ item.name }}</div>
                </div>
            </div>
        </scroll-view>

        <div class="mask-view flex-list" v-if="active == 0">
            <template v-if="companyTitleList.length < 0">
                <u-empty text="暂无发票抬头信息" mode="list" :margin-top="80"></u-empty>
            </template>
            <template v-else>
                <div class="mask-list mask-flex" v-for="(item, index) in companyTitleList" :key="item.id">
                    <div class="mask-cell">
                        <div class="label-txt font15 title-bold">
                            {{ item.invoicetitle }}
                            <span class="mask-default" v-if="item.id == defaultEnterpriseInvoice">默认</span>
                        </div>
                        <div class="label-txt">税号：{{ item.taxcode }}</div>
                        <!--          <div class="label-txt">常用邮箱：<EMAIL></div>-->
                    </div>
                    <div class="right-div">
                        <img src="/static/edit-icon.png" alt="" mode="widthFix" :data-item="item" @click="comptap($event, active)" />
                        <img src="@/static/detele-icon.png" alt="" mode="widthFix" @click="delClick(item.id)" />
                    </div>
                </div>
            </template>
        </div>
        <div class="mask-view flex-list" v-if="active == 1">
            <template v-if="personalTitleList.length < 0">
                <u-empty text="暂无发票抬头信息" mode="list" :margin-top="80"></u-empty>
            </template>
            <template v-else>
                <div class="mask-list mask-flex" v-for="(item, index) in personalTitleList" :key="item.id">
                    <div class="mask-cell">
                        <div class="label-txt font15 title-bold" style="padding-bottom: 0">
                            {{ item.invoicetitle }}
                            <span class="mask-default" v-if="item.id == defaultPersonalInvoice">默认</span>
                        </div>
                        <!--          <div class="label-txt">常用邮箱：<EMAIL></div>-->
                    </div>
                    <div class="right-div">
                        <img src="/static/edit-icon.png" alt="" mode="widthFix" :data-item="item" @click="comptap($event, active)" />
                        <img src="@/static/detele-icon.png" alt="" mode="widthFix" @click="delClick(item.id)" />
                    </div>
                </div>
            </template>
        </div>
        <div class="bottom-view">
            <div class="bottom-btn" @click="clickAddIncoive">添加发票抬头</div>
        </div>

        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getCompanyTitleListPost, getPersonalTitleListPost, delInvoiceTitlePost } from '@/api/my-center';

export default {
    name: 'invoice-center',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            //nav选中index
            active: 0,
            //navList
            typeList: [
                {
                    name: '企业抬头',
                },
                {
                    name: '个人/非企业抬头',
                },
            ],
            //企业抬头列表
            companyTitleList: [],
            //个人抬头列表
            personalTitleList: [],
            // 默认抬头(企业)id
            defaultEnterpriseInvoice: '',
            // 默认抬头(个人)id
            defaultPersonalInvoice: '',
        };
    },
    onShow() {
        // this.getPersonalTitleList();
        // this.getCompanyTitleList();
        this.getPageDataFun();
    },
    methods: {
        /**
         * 方法
         */
        //获取页面数据方法
        async getPageDataFun() {
            const [{ data: companyTitleList }, { data: personalTitleList }] = await Promise.all([
                getCompanyTitleListPost(),
                getPersonalTitleListPost(),
            ]);
            Object.assign(this, {
                companyTitleList,
                personalTitleList,
                defaultEnterpriseInvoice: this.$Storage.defaultEnterpriseInvoice.value,
                defaultPersonalInvoice: this.$Storage.defaultPersonalInvoice.value,
            });
        },
        // 添加发票抬头
        clickAddIncoive() {
            uni.navigateTo({
                url: '/packages/invoice-center/pages/add-invoice-title/main?itemdata=' + this.active + '&type=add',
            });
        },
        //nav点击事件
        changeType(item, index) {
            this.active = index;
        },
        //点击编辑事件
        comptap(e, index) {
            let item = index;
            let iteminfo = e.currentTarget.dataset.item;
            console.log(iteminfo);
            let iteminfoobj = encodeURIComponent(JSON.stringify(iteminfo));
            uni.navigateTo({
                url:
                    '/packages/invoice-center/pages/add-invoice-title/main?itemdata=' + item + '&iteminfoobj=' + iteminfoobj + '&type=edit',
            });
        },
        //点击删除事件
        async delClick(id) {
            this.$util
                .showModal('是否要删除该发票抬头')
                .then(async res => {
                    await delInvoiceTitlePost({ id });
                    return this.$util.showModal('删除成功', true);
                })
                .then(async res => {
                    if (this.active == 0) {
                        const { data: companyTitleList } = await getCompanyTitleListPost();
                        this.companyTitleList = companyTitleList;
                    } else {
                        const { data: personalTitleList } = await getPersonalTitleListPost();
                        this.personalTitleList = personalTitleList;
                    }
                });
        },
    },
};
</script>

<style scoped lang="scss">
$font14: 14px;
$font15: 16px;
$colorgray: #909090;
.add-center {
    width: 100%;
    background-color: #f6f6f6;
    min-height: 100%;
    overflow: hidden;

    // 横向滑动tab
    .scroll-view_H {
        background-color: #f6f6f6;

        .slide_type_list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            padding: 15px 0;

            .slide_type_list_view {
                padding-bottom: 5px;
                font-size: 15px;
                color: #333333;
            }

            .is_selected {
                color: $btn-color;
                font-weight: bold;
                position: relative;
            }

            .is_selected:before {
                content: '';
                position: absolute;
                width: 24px;
                height: 3px;
                background: linear-gradient(270deg, rgba(255, 130, 0, 0) 0%, $btn-color 100%);
                left: 50%;
                bottom: -4rpx;
                transform: translateX(-50%);
            }
        }
    }

    .mask-view {
        padding: 10px 15px;
        padding-top: 0px;
        padding-bottom: calc(env(safe-area-inset-bottom) + 64px);
        .mask-flex {
            display: flex;
            margin: 10px 0;
            margin-top: 0;
            align-items: center;
        }

        .mask-list {
            padding: 9px 10px 12px;
            background: #ffffff;
            border-radius: 5px;

            .mask-cell {
                flex: 0.9;
            }
            .mask-default {
                width: 30px;
                height: 16px;
                background: $btn-mantle-color;
                border-radius: 3px;
                border: 1px solid $btn-color;
                line-height: 18px;
                text-align: center;
                font-size: 10px;
                color: $btn-color;
                display: inline-block;
                margin-left: 8px;
            }

            .right-div {
                img {
                    width: 18px;
                    margin-left: 22px;
                    height: 17.5px;
                }
            }
        }
    }

    .label-txt {
        font-size: $font14;
        padding-bottom: 4px;
    }

    .title-bold {
        font-weight: bold;
    }

    .color-font {
        color: $btn-color;
    }

    .no-data {
        text-align: center;
        margin-top: 80px;
    }

    .bottom-view {
        padding-bottom: env(safe-area-inset-bottom);
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-btn {
            margin-top: 10px;
            margin-bottom: 10px;
            line-height: 44px;
            width: 345px;
            background-color: $btn-color;
            color: #ffffff;
            font-size: 15px;
            border-radius: 5px;
            text-align: center;
        }
    }
}
</style>
