// 对不同的接口添加不同的公共参数 promise 和 async await esmodule
// promise 只能执行一次，需要多次执行的时候需要放到方法里面，返回promise
import cnpcBridge from './cnpcBridge.js';
import layer from '../layer';

let checkFKArgs = {
    /**
     * 拼接风控参数
     * @returns {Promise<any>}
     */
    returnFengkongArg() {
        return new Promise((resolve, reject) => {
            Promise.all([checkFKArgs.getFingerArg(), checkFKArgs.getLocationArg()]).then(resultList => {
                resolve(Object.assign(resultList[0], resultList[1]));
            });
        });
    },
    /**
     * 获取设备指纹
     * @returns {Promise<any>}
     */
    getFingerArg() {
        return new Promise((resolve, reject) => {
            cnpcBridge.getFinger(fingerDic => {
                resolve({ dfp: fingerDic.finger });
            });
        });
    },
    /**
     * 获取设备位置信息
     * @returns {Promise<any>}
     */
    getLocationArg() {
        return new Promise((resolve, reject) => {
            cnpcBridge.getLocation(
                locationInfo => {
                    //
                    let state = locationInfo.longitude == '116.404421' && locationInfo.latitude == '39.914579';
                    let province = locationInfo.province;
                    let city = locationInfo.city;
                    let district = locationInfo.district;
                    let longitude = locationInfo.longitude;
                    let latitude = locationInfo.latitude;
                    resolve({
                        gps: state ? '' : '(' + longitude + ',' + latitude + ')',
                        gpsProvince: state ? '' : province,
                        gpsCity: state ? '' : city,
                        gpsArea: state ? '' : district,
                    });
                },
                false,
                false,
                true,
            );
        });
    },
    /**
     * 检查接口是否需要拼接风控参数
     * @param url
     * @returns {boolean}
     */
    checkFengkong(url) {
        let fengkongList = [
            'account.preOrder.recharge', // 电子钱包充值预下单
            'account.openAccount', //开通电子账户
            'account.password.modify', //修改支付密码
            'account.password.forget', //忘记支付密码
            'card.bindCard', //绑定加油卡
            'card.fastBindCard', //油卡快捷绑定接口card/rollout
            'card.rollout', //资金转出接口
            'order.unPaidOrder', //生成待支付订单
            'user.plate.modifyOrCreate', //修改/创建车牌号接口
            'user.cancelAccount', //用户注销
            'user.refreshToken', //刷新token
            'order.preAuth.generateOrder', //预约加油生成预授权订单
            'card.unbindCardList',
            'user.basicInfo.modify', //修改用户基本信息
            'user.cancelAccount', //用户注销
            'account.riskEngineSupport', //会员码风控支持
            'invoice.make', // 3.0消费开票
            'account.rechargeStatus.query',//昆仑e享卡充值状态查询
        ];
        let obj = fengkongList.find(item => item.indexOf(url) != -1);
        return !!obj;
    },

    /**
     * 获取设备参数信息
     * @returns {Promise<any>}
     */
    getDeviceInfo() {
        return new Promise(async (resolve, reject) => {
            let common = await cnpcBridge.getCommonArgs();
            resolve({
                operIp: common.ip || '',
                appVersion: common.appVersion || '',
                os: common.os || '',
                osVersion: common.osVersion || '',
            });
        });
    },
    // 2.0充值卡充值和充值获取风控参数
    async getFKArgs2(url, isAuth = false) {
        return new Promise(async (resolve, reject) => {
            let lastargs = {};
            let urlList = [
                '/app/json/card/recharge', // 充值
                '/app/json/card/rechargeByCard', // 充值卡充值
                '/app/json/user/modifyPwd', //修改登录密码
            ];
            let obj = urlList.find(item => item.indexOf(url) != -1);
            if (!!obj) {
                let commonArg = await checkFKArgs.getDeviceInfo();
                lastargs = Object.assign(commonArg, lastargs);
                let args = await checkFKArgs.returnFengkongArg();
                lastargs = Object.assign(args, lastargs);
            }
            resolve(lastargs);
        });
    },
    // 获取风控参数
    async getFKArgs(url, isAuth = false) {
        return new Promise(async (resolve, reject) => {
            let lastargs = {};
            if (checkFKArgs.checkFengkong(url) || url === 'sdk') {
                let args = await checkFKArgs.returnFengkongArg();
                let params = {};
                params = Object.assign(args, params);
                params.sourceChan = '2'; //能源e站app渠道号
                params.operIp = ''; //IP
                if (isAuth) {
                    //如果是实人认证的过的，多拼两个参数
                    params.authStatus = 1;
                    params.authTime = layer.getMyTime(new Date().getTime());
                }
                lastargs.extendFiled = params;
                if (url === 'sdk') {
                    resolve(params);
                } else {
                    resolve(lastargs);
                }
            } else {
                resolve(lastargs);
            }
        });
    },

    /**
     * 获取设备位置信息
     * @returns {Promise<any>}
     */
    getLocationInfo() {
        return new Promise((resolve, reject) => {
            cnpcBridge.getLocation(locationInfo => {
                resolve(locationInfo);
            });
        });
    },
};
export default checkFKArgs;
