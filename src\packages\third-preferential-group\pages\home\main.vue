<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="bg-F7F7FB" style="height: 100%">
            <zj-navbar :height="44" title="优待群组申请"></zj-navbar>
            <div class="main">
                <!-- //未开通电子钱包展示 -->
                <wallet-close v-if="num == 1" />
                <!-- //已开通电子钱包展示 -->
                <wallet-open @userJoin="userJoin" v-if="num == 2" />
                <!-- //优待群组审核中展示 -->
                <group-audit @back="back" v-if="num == 3" />
                <!-- //优待群组已加入展示 -->
                <group-join @userJoin="userJoin" v-if="num == 4" />
                <!-- //邀请展示 -->
                <group-Invite @userJoin="userJoin" :groupNo="groupNo" @reset="reset" v-if="num == 5" />
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import WalletOpen from '../../components/wallet-open.vue';
import WalletClose from '../../components/wallet-close.vue';
import GroupAudit from '../../components/group-audit.vue';
import GroupJoin from '../../components/group-join.vue';
import GroupInvite from '../../components/group-Invite.vue';
import { accountStatus } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import {
    userPreferentialGroupApprovalStatus,
    userPreferentialGroupApplyJoin,
} from '../../../../s-kit/js/v3-http/https3/preferentialGroup/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        WalletOpen,
        WalletClose,
        GroupAudit,
        GroupJoin,
        GroupInvite,
    },
    name: 'thirdPreferentialGroup',
    data() {
        return {
            num: 0, //组件指针
            applyStatus: '', //申请加入优待证群组审核状态
            falg: true,
            groupNo: '', //群组id
            walletStatus: {},
        };
    },
    async onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'ydEquityPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
    },
    mounted() {
        //获取优待群组状态
        this.getaccountStatus();
    },

    methods: {
        // 刷新数据
        reset() {
            this.getData();
        },
        // 获取昆仑e享卡状态
        async getaccountStatus() {
            let res = await accountStatus();
            console.log(res, 'res============昆仑e享卡状态');
            if (res && res.success) {
                this.walletStatus = res.data;
                this.getData();
            }
        },
        //获取优待群组状态
        async getData() {
            console.log(this.walletStatus);
            if (this.walletStatus.status && this.walletStatus.accountStatus != '3') {
                console.log('已开通昆仑e享卡');
                let res = await userPreferentialGroupApprovalStatus();
                if (res && res.success) {
                    this.applyStatus = res.data.applyStatus;
                    this.groupNo = res.data.groupNo ? res.data.groupNo : '';
                    if (this.applyStatus == 5 || this.applyStatus == 10 || this.applyStatus == 11) {
                        this.num = 2;
                    } else if (this.applyStatus == 6 || this.applyStatus == 7) {
                        this.num = 3;
                    } else if (this.applyStatus == 9) {
                        this.num = 4;
                    } else if (this.applyStatus == 8) {
                        this.num = 5;
                    }
                }
            } else {
                this.num = 1;
                return;
            }
        },
        //申请加入优待群组
        async userJoin(data) {
            if (this.applyStatus == 11) {
                data = 2;
            }
            let res = await userPreferentialGroupApplyJoin({ repeatApply: data });
            if (res && res.success) {
                this.getData();
            }
        },
        // 返回上一页
        back() {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.closeMriver(res => {});
            // #endif
            // #ifndef MP-MPAAS
            uni.navigateBack({ delta: 1 });
            // #endif
        },
    },
};
</script>
<style></style>
