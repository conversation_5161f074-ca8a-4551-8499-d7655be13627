<template>
    <div class="view">
        <div v-if="locationPermission">
            <div v-if="!distanceOut">
                <div class="codeNum font-14 weight-bold color-333">{{ memberCode }}</div>
                <div class="fl-comuln fl-al-jus-cen"></div>
                <div class="qr-code-wrap fl-row fl-jus-cen">
                    <!--                    <canvas class="qrcode" canvas-id="qrcode" id="qrcode" @click="init()" />-->
                    <canvas v-if="!imagePath" canvas-id="myCanvas" style="width: 555px; height: 555px"></canvas>
                    <img v-if="imagePath" :src="imagePath" class="qrcode" mode="aspectFit" />
                </div>
                <div class="card-text fl-row fl-al-cen font-12 weight-500 te-center fl-jus-cen color-666">
                    （点击二维码更新）
                    <div @click="refreshEvent()" class="fl-row fl-al-cen">
                        <div>刷新</div>
                        <img src="../../image/shuaxin.png" alt />
                    </div>
                </div>
            </div>
            <div v-else class="bg-fff border-rad-8 padding-16">
                <div class="no-code-box">
                    <img class="no-code-img" src="../../image/noQRCode.png" alt />
                    <div class="code-text"
                        >您距离网点超过{{
                            allMarkerArr.length > 0 && Number(allMarkerArr[0].payDistance) > 0
                                ? Number(allMarkerArr[0].payDistance)
                                : maxDistance
                        }}km，请到网点后使用会员码</div
                    >
                    <div class="code-refresh" @click="refreshEvent()">刷新</div>
                </div>
            </div>
        </div>
        <div v-else class="bg-fff border-rad-8 padding-16">
            <div class="no-code-box">
                <img class="no-code-img" src="../../image/positioningNotEnabled.png" alt />
                <div class="code-text">
                    会员码在距离中石油加油站500米内才可以使用，现在还不知道您在哪里，请在设置中开启能源e站定位权限。
                </div>
                <div class="code-enablePositioning" @click="enablePositioning()">开启定位</div>
            </div>
        </div>
    </div>
</template>
<script>
import { maxDistance } from '../../../../../../project.config';
import QRCode from 'qrcode';

const app = getApp();
// #ifdef MP-MPAAS
import appMemberShipCode from './diff-environment/app-member-ship-code';
// #endif
// #ifdef MP-WEIXIN
import wxMemberShipCode from './diff-environment/wx-member-ship-code';
// #endif
// #ifdef MP-ALIPAY
import zfbMemberShipCode from './diff-environment/zfb-member-ship-code';
// #endif
import { mapState } from 'vuex';
// import { accountRiskEngineSupport } from '@/s-kit/js/v3-http/https3/wallet';
import { accountRiskEngineSupport } from '../../../../js/v3-http/https3/wallet';

export default {
    name: 'member-ship-code',
    components: {},
    props: {
        refer: {
            default: '',
            type: String,
        },
    },
    // #ifdef MP-MPAAS
    mixins: [appMemberShipCode],
    // #endif
    // #ifdef MP-WEIXIN
    mixins: [wxMemberShipCode],
    // #endif
    // #ifdef MP-ALIPAY
    mixins: [zfbMemberShipCode],
    // #endif
    data() {
        return {
            // 每隔一分钟刷新会员码Timer
            memberTimingTimer: null,
            // 会员码code值
            memberCode: '',
            // 查询订单的定时器
            memberCodeTimer: null,
            // 查询订单的定时器的每隔3s查询一次
            pollTime: 3000,
            // 查询到订单后执行跳转标识
            isJump: true,
            // 页面销毁后阻止查询订单
            turnOffRecursionMember: true,
            // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
            callbackFlag: true,
            // 当前油站是否超过了500米
            distanceOut: false,
            maxDistance: maxDistance || 0.5,
            // 用户是否开启定位标识
            locationPermission: true,
            imagePath: '',
        };
    },
    watch: {
        // // 当前方法小程序使用
        // '$store.state.member.memberCodeBoolean': {
        //     handler(val) {
        //         if (val) {
        //             this.init();
        //         }
        //     },
        //     deep: true,
        //     immediate: true,
        // },
    },
    computed: {
        ...mapState({
            allMarkerArr: state => state.locationV3_app.allMarkerArr, // 油站数组
            isHarmony: state => state.thirdIndex.isHarmony,
            locationState: state => state.locationV3_app.locationState,
        }),
    },
    created() {
        // #ifdef MP-WEIXIN
        // 初始化账户插件
        this.$sKit.keyBordPlugin.initRef(this.accountDataPlugin);
        // #endif
    },
    mounted() {
        console.log('会员码走了mounted');
        // 初始化方法
        // this.init();
        // 清除刷新会员码的定时器
        this.clearTimerMember();
        // 清除查询订单的定时器
        this.clearTimerQuqeyOrder();
        // 设置节流，防止用户在短时间内频繁点击
        this.refreshEvent = this.$sKit.commonUtil.throttleUtil(this.refreshEvent);
    },
    methods: {
        generateQRCode(qrCode) {
            console.log(qrCode, 'qrCode=====');
            const canvasId = 'myCanvas';
            const ctx = uni.createCanvasContext(canvasId, this);
            ctx.setFillStyle('#********'); // 红色
            ctx.fillRect(0, 0, 300, 300); // 绘制一个红色矩形
            ctx.draw(false); // 提交绘制
            // 生成二维码的文本数据
            QRCode.toString(
                qrCode, // 二维码内容
                { type: 'utf8' }, // 配置选项
                (err, qrCodeText) => {
                    if (err) {
                        console.error('生成二维码失败', err);
                        return;
                    }

                    // 解析二维码文本数据并绘制到 Canvas
                    this.drawQRCodeToCanvas(ctx, qrCodeText, 555, 555);
                },
            );
        },
        // 将二维码文本数据绘制到 Canvas
        drawQRCodeToCanvas(ctx, qrCodeText, width, height) {
            console.log('二维码文本数据:', qrCodeText);
            // 将 SVG 转换为 Base64 格式
            this.imagePath = `data:image/svg+xml;base64,${Buffer.from(qrCodeText).toString('base64')}`;
            console.log(this.imagePath, 'this.imagePath');
        },
        /**
         * @description  : 刷新
         * @return        {*}
         */
        refreshEvent() {
            // 清除刷新会员码的定时器
            this.clearTimerMember();
            // 初始化
            this.init(false, false);
        },
        /**
         * @description  : 初始化方法
         * @return        {*}
         */
        async init(retry = false, isOrder = true) {
            console.log('测试执行几次');
            const initMemberCode = async () => {
                if (!this.memberTimingTimer) {
                    // 获取当前位置是否超过500米，如果超出500米不展示会员码
                    // await this.$store.dispatch('initLocationV3_app', {
                    //     callback: getLocationState => {
                    // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
                    if (this.callbackFlag) {
                        // 页面销毁后阻止查询订单
                        if (!this.memberTimingTimer) {
                            // 点击刷新或者重新进将当前值置为true ，防止下面的查询订单方法不执行
                            this.turnOffRecursionMember = true;
                            this.isJump = true;
                            // #ifndef MP-MPAAS
                            // this.locationPermission = getLocationState;
                            // console.log(this.locationPermission, '是否可以获取到位置2');
                            // #endif
                            let judgeMent = false;
                            if (this.allMarkerArr.length > 0) {
                                judgeMent =
                                    Number(this.allMarkerArr[0].payDistance) > 0
                                        ? Number(this.allMarkerArr[0].distance) > Number(this.allMarkerArr[0].payDistance)
                                        : Number(this.allMarkerArr[0].distance) > (maxDistance || 0.5);
                            } else {
                                judgeMent = true;
                            }

                            if (JSON.stringify(this.allMarkerArr) == '[]' || judgeMent) {
                                // 如果当前距离 > 500米不展示会员码
                                this.distanceOut = true;
                            } else {
                                // 如果当前距离 < 500米展示会员码
                                this.distanceOut = false;
                                // 获取会员码
                                this.pollGetQrCode(isOrder);
                                // 在开启定时器之前，先清除下定时器，避免造成多个定时器同时存在
                                this.clearTimerMember();
                                // 每60S刷新一次
                                this.memberTimingTimer = setInterval(() => {
                                    //需要刷新的数据
                                    this.pollGetQrCode();
                                }, 1000 * 60);
                                // app.globalData.memberCodeAndpayCodeTimerList.push(this.memberTimingTimer)
                                // console.log(app.globalData.memberCodeAndpayCodeTimerList)
                            }
                        }
                    }
                    //     },
                    // });
                }
            };
            // 是否调用检查用户开启定位权限标识
            if (!retry) {
                // #ifdef MP-MPAAS
                // this.$cnpcBridge.checkPermission() 检查用户定位权限是否开启
                let locationRes = await this.$cnpcBridge.checkPermission();
                if (this.isHarmony) {
                    this.locationPermission = locationRes.appStatus;
                } else {
                    this.locationPermission = locationRes.appStatus && locationRes.osStatus;
                }
                console.log('用户开启定位了吗===会员码', this.locationPermission);
                if (!this.locationPermission) return;
                // #endif
                // #ifndef MP-MPAAS
                this.checkIfTheVehicleHasEnabledPositioning(initMemberCode);
                return;
                // #endif
            }
            initMemberCode();
            // 判断当前是否存在倒计时一分钟刷新会员码的定时器
        },
        //清除定时器
        clearTimerQuqeyOrder() {
            // 清除查询订单的定时器
            if (this.memberCodeTimer) {
                clearInterval(this.memberCodeTimer);
                this.memberCodeTimer = null;
            }
        },
        // 清除刷新会员码的定时器
        clearTimerMember() {
            // 清除刷新会员码的定时器
            if (this.memberTimingTimer) {
                clearInterval(this.memberTimingTimer);
                this.memberTimingTimer = null;
            }
        },
        /**
         * @description  : 会员码风控
         * @return        {*}
         */
        async riskControl() {
            // 获取用户信息，根据不同环境调用不同方法获取并整理相关参数
            let res;
            // 根据是否是MP-MPAAS环境来获取用户信息
            // #ifdef MP-MPAAS
            res = await this.$cnpcBridge.getUserTokenInfo();
            // #endif
            // #ifndef MP-MPAAS
            res = await this.$store.dispatch('memberBaseInfoAction');
            // #endif
            let params;
            // 确保能正确获取到memberPhone，根据res的不同情况处理
            if (res && res.phone) {
                params = {
                    memberPhone: res.phone,
                };
            } else if (res && res.data && res.data.phone) {
                params = {
                    memberPhone: res.data.phone,
                };
            } else {
                // 这里可以根据实际需求添加处理逻辑，比如抛出错误或者赋予默认值等
                console.error('无法获取有效的用户手机号信息');
                params = {};
            }
            await accountRiskEngineSupport(params, { isCustomErr: true, isload: false });
        },
    },
    filter: {},
    beforeDestroy() {
        this.turnOffRecursionMember = false;
        this.callbackFlag = false;
        this.clearTimerQuqeyOrder();
        this.clearTimerMember();

        // app.globalData.memberCodeClearRecursionFlag = false
    },
};
</script>
<style scoped lang="scss">
.view {
    .codeNum {
        display: flex;
        justify-content: center;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .qr-code-wrap {
        width: 275px;
        height: 275px;
        margin: 0 auto;

        // margin-top: 20px;
        .qrcode {
            margin: 0 auto;
            width: 100%;
            height: 100%;
        }
    }

    .card-text {
        margin-bottom: 10px;
        margin-top: 10px;

        img {
            width: 8px;
            height: 8px;
            margin-left: 3px;
        }
    }

    .no-code-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 90rpx;

        .no-code-img {
            width: 114rpx;
            height: 114rpx;
        }

        .code-text {
            margin-top: 28rpx;
            font-size: 30rpx;
            font-weight: 400;
            color: #333333;
            line-height: 42rpx;
        }

        .code-refresh {
            margin-top: 60rpx;
            width: 218rpx;
            height: 80rpx;
            background: #e64f22;
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(255, 87, 36, 0.25);
            border-radius: 41rpx;
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 80rpx;
            text-align: center;
            text-shadow: 0px 9px 9px rgba(255, 87, 36, 0.25);
        }
        .code-enablePositioning {
            margin-top: 60rpx;
            width: 218rpx;
            height: 80rpx;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(255, 87, 36, 0.25), 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 41rpx;
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 80rpx;
            text-align: center;
            text-shadow: 0px 9px 9px rgba(255, 87, 36, 0.25);
        }
    }

    .img-wrap {
        margin: 0;
        padding: 0;
        height: 30px;
        min-height: 30px;

        .img_style {
            width: 100%;
            height: 100%;
        }
    }

    .footer {
        width: 100%;
        // height: 200px;
        padding-bottom: 37px;
        border-radius: 0 0 8px 8px;

        .text1 {
            margin-bottom: 7px;
        }

        .text2 {
        }

        .payMethods {
            margin-top: 5px;
            margin-bottom: 15px;

            .img-wrap-sj {
                margin-left: 5px;
                width: 7px;
                height: 7px;
                line-height: 7px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .code-line {
            padding-bottom: 15px;
        }
    }
}
</style>
