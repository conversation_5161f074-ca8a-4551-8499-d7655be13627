<template>
    <div class="view">
        <div v-if="locationPermission">
            <div v-if="!distanceOut" class="code_div">
                <div class="codeNum font-14 weight-bold color-333">{{ paymentCode }}</div>
                <div class="bar-wrap">
                    <canvas id="barcode" canvas-id="barcode"></canvas>
                </div>
                <div class="qr-code-wrap fl-row fl-jus-cen">
                    <!--                    <canvas class="qrcode" id="qrcode" canvas-id="qrcode"></canvas>-->
                    <canvas v-if="!imagePath" canvas-id="myCanvas" style="width: 555px; height: 555px"></canvas>
                    <img v-if="imagePath" :src="imagePath" class="qrcode" mode="aspectFit" />
                </div>

                <div class="card-text fl-row fl-al-cen font-12 weight-500 te-center fl-jus-cen color-666">
                    （点击二维码更新）
                    <div @click="refreshEvent()" class="fl-row fl-al-cen">
                        <div>刷新</div>
                        <img src="../../image/shuaxin.png" alt />
                    </div>
                </div>
            </div>
            <div v-if="distanceOut" class="bg-fff border-rad-8 padding-16">
                <div class="no-code-box">
                    <img class="no-code-img" src="../../image/noQRCode.png" alt />
                    <div class="code-text">您距离网点超过{{ maxDistance }}米，请到网点后使用付款码</div>
                    <div class="code-refresh" @click="refreshEvent()">刷新</div>
                </div>
            </div>
        </div>

        <div v-if="!locationPermission" class="bg-fff border-rad-8 padding-16">
            <div class="no-code-box">
                <img class="no-code-img" src="../../image/positioningNotEnabled.png" alt />
                <div class="code-text"
                    >付款码在距离中石油加油站500米内才可以使用，现在还不知道您在哪里，请在设置中开启能源e站定位权限。</div
                >
                <div class="code-enablePositioning" @click="enablePositioning()">开启定位</div>
            </div>
        </div>

        <!-- <div class="img-wrap">
      <img class="img_style" mode="scaleToFill" src="../../image/line.png" alt />
    </div>
    <div class="footer bg-fff p-LR-16">
      <div class="text1 font-15 color-333 weight-bold">温馨提示</div>
      <div class="text2 font-15 color-666 weight-4f00">请根据实际需求询问加油站工作人员出示紫色会员码还是橙色付款码。</div>
    </div>-->
    </div>
</template>
<script>
import { maxDistance } from '../../../../../../project.config';

const app = getApp();
import { queryUnpaymentOrderByMemberNo, queryUnusedCouponList } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import wxcode from 'uniapp-qrcode';
import QRCode from 'qrcode';
import { mapState } from 'vuex';
export default {
    name: 'pay-ment-code',
    components: {},
    props: {
        refer: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            // 查询订单定时器Timer
            queryOrderTimer: null,
            // 每隔一分钟刷新付款码Timer
            codeTimingTimer: null,
            // 会员码code值
            paymentCode: '',
            // 查询到订单后执行跳转标识
            isJump: true,
            // 查询订单的定时器的每隔3s查询一次
            pollTime: 3000,
            // 页面销毁后阻止查询订单
            turnOffRecursionCode: true,
            // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
            callbackCodeFlag: true,
            // 当前油站是否超过了500米
            distanceOut: false,
            // 500配置项
            maxDistance: (maxDistance || 0.5) * 1000,
            // 用户是否开启定位标识
            locationPermission: false,
            // 是否调用检查用户开启定位权限标识
            checkLocationPermission: true,
            // 图片路径
            imagePath: '',
        };
    },
    computed: {
        ...mapState({
            allMarkerArr: state => state.locationV3_app.allMarkerArr, // 油站数组
        }),
    },
    watch: {},
    created() {},
    mounted() {
        console.log('付款码走了mounted');
        // 初始化方法
        // this.init();
        // 清除刷新付款码的定时器
        this.clearTimerCode();
        // 清除查询订单的定时器
        this.clearTimerQuqeyOrder();
        // 设置节流，防止用户在短时间内频繁点击
        this.refreshEvent = this.$sKit.commonUtil.throttleUtil(this.refreshEvent);
        // 检查当前用户是否开启定位
    },
    methods: {
        generateQRCode(qrCode) {
            console.log(qrCode, 'qrCode=====');
            const canvasId = 'myCanvas';
            const ctx = uni.createCanvasContext(canvasId, this);
            ctx.setFillStyle('#00000000'); // 红色
            ctx.fillRect(0, 0, 300, 300); // 绘制一个红色矩形
            ctx.draw(false); // 提交绘制
            // 生成二维码的文本数据
            QRCode.toString(
                qrCode, // 二维码内容
                { type: 'utf8' }, // 配置选项
                (err, qrCodeText) => {
                    if (err) {
                        console.error('生成二维码失败', err);
                        return;
                    }

                    // 解析二维码文本数据并绘制到 Canvas
                    this.drawQRCodeToCanvas(ctx, qrCodeText, 555, 555);
                },
            );
        },
        // 将二维码文本数据绘制到 Canvas
        drawQRCodeToCanvas(ctx, qrCodeText, width, height) {
            console.log('二维码文本数据:', qrCodeText);
            // 将 SVG 转换为 Base64 格式
            this.imagePath = `data:image/svg+xml;base64,${Buffer.from(qrCodeText).toString('base64')}`;
            console.log(this.imagePath, 'this.imagePath');
        },
        /**
         * @description  : 刷新
         * @return        {*}
         */
        refreshEvent() {
            // 清除刷新付款码的定时器
            this.clearTimerCode();
            // 初始化
            this.init();
        },
        /**
         * @description  : 开启定位
         * @return        {*}
         */
        enablePositioning() {
            this.$cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(res => {
                    if (res) {
                        console.log(res, '开启定位权限res');
                        this.locationPermission = true;
                        this.checkLocationPermission = false;
                        this.init();
                    }
                });
        },
        async init() {
            // 是否调用检查用户开启定位权限标识
            if (this.checkLocationPermission) {
                // this.$cnpcBridge.checkPermission() 检查用户定位权限是否开启
                let locationRes = await this.$cnpcBridge.checkPermission();
                this.locationPermission = locationRes.appStatus && locationRes.osStatus;
                console.log('用户开启定位了吗===付款码', this.locationPermission);
                if (!this.locationPermission) return;
            }
            // 判断当前是否存在倒计时一分钟刷新付款码的定时器
            if (!this.codeTimingTimer) {
                console.log('1111111');
                // 获取当前位置是否超过500米，如果超出500米不展示付款码
                await this.$store.dispatch('initLocationV3_app', {
                    callback: () => {
                        // 为了阻断页面快速切换后，接口调用慢，callback在接口返回后执行导致的定时器一直在刷新
                        if (this.callbackCodeFlag) {
                            // 页面销毁后阻止查询订单
                            if (!this.codeTimingTimer) {
                                // 点击刷新或者重新进将当前值置为true ，防止下面的查询订单方法不执行
                                this.turnOffRecursionCode = true;
                                this.isJump = true;
                                if (
                                    JSON.stringify(this.allMarkerArr) == '[]' ||
                                    Number(this.allMarkerArr[0].distance) > (maxDistance || 0.5)
                                ) {
                                    // 如果当前距离 > 500米不展示会员码
                                    this.distanceOut = true;
                                } else {
                                    // 如果当前距离 < 500米展示会员码
                                    this.distanceOut = false;
                                    // 获取付款码
                                    this.pollGetQrCode();
                                    // 在开启定时器之前，先清除下定时器，避免造成多个定时器同时存在
                                    this.clearTimerCode();
                                    // 每60S刷新一次
                                    this.codeTimingTimer = setInterval(() => {
                                        //需要刷新的数据
                                        this.pollGetQrCode();
                                    }, 1000 * 60);
                                    this.getUnpaymentOrderByMemberNo();
                                    // app.globalData.memberCodeAndpayCodeTimerList.push(this.memberTimingTimer)
                                }
                            }
                        }
                    },
                    // });
                });
            }
        },
        /**
         * @description  : 获取付款码码code值，生成付款码
         * @param         {Function} getPayCode -获取会员码方法
         * @param         {Function} codeChange -将获取的生成的会员码的code值分发给父组件
         * @return        {*}
         */
        pollGetQrCode() {
            this.$accountCenter.getPayCode({ cardIdx: 1 }, res => {
                if (res.isSuccessed) {
                    this.paymentCode = res.desString;
                    console.log(res.desString, 'res.desString=====付款码更新code值');
                    this.$emit('codeChange', this.paymentCode);

                    setTimeout(() => {
                        wxcode.barcode('barcode', res.desString, 440, 120);
                        // wxcode.qrcode('qrcode', res.desString, 555, 555);
                        this.generateQRCode(res.desString);
                    }, 0);
                    // 查询订单
                    this.getUnpaymentOrderByMemberNo();
                } else {
                    uni.showToast({ title: res.desString || '付款码获取失败' });
                }
            });
        },
        /**
         * @description  : 新会员到老站出示付款码后主动轮询订单
         * @return        {*}
         */
        getUnpaymentOrderByMemberNo() {
            // 清除查询订单的定时器
            this.clearTimerQuqeyOrder();
            // 防止查询订单成功后继续执行递归
            if (this.turnOffRecursionCode) {
                queryUnpaymentOrderByMemberNo({}, { isload: false }).then(
                    res => {
                        // 没查询到继续查询
                        if (!res.success || !res?.data) {
                            this.queryOrderTimer = setTimeout(() => {
                                this.getUnpaymentOrderByMemberNo();
                            }, this.pollTime);
                            return;
                        }
                        if (this.isJump) {
                            console.log(res.data, 'res.data');
                            // return;
                            // 查到订单后置为false,防止二次跳转
                            this.isJump = false;
                            let url = '/packages/third-scan-code-payment/pages/confir-order/main';
                            // 订单数据，作为路由跳转的参数
                            let params = { ...res.data, refer: this.refer };
                            let type = 'navigateTo'; // 默认  uni.navigateTo({})
                            this.$sKit.layer.useRouter(url, params, type);
                            // 跳转成功后清除查询订单的定时器
                            this.clearTimerQuqeyOrder();
                            // 清除刷新付款码的定时器
                            this.clearTimerCode();
                            this.turnOffRecursionCode = false;
                            // this.$store.commit('payCodeFun', false);
                        }
                    },
                    error => {
                        // 未查询到订单继续查询
                        this.clearTimerQuqeyOrder();
                        this.queryOrderTimer = setTimeout(() => {
                            this.getUnpaymentOrderByMemberNo();
                        }, this.pollTime);
                    },
                );
            }
            // if (!app.globalData.paymentCodeClearRecursionFlag) return
        },
        //清除定时器
        clearTimerQuqeyOrder() {
            // 清除查询订单的定时器
            if (this.queryOrderTimer) {
                clearInterval(this.queryOrderTimer);
                this.queryOrderTimer = null;
            }
        },
        // 清除刷新付款码的定时器
        clearTimerCode() {
            // 清除刷新付款码的定时器
            if (this.codeTimingTimer) {
                clearInterval(this.codeTimingTimer);
                this.codeTimingTimer = null;
            }
        },
    },
    filter: {},
    beforeDestroy() {
        this.clearTimerCode();
        this.clearTimerQuqeyOrder();
        this.turnOffRecursionCode = false;
        this.callbackCodeFlag = false;
    },
};
</script>
<style scoped lang="scss">
.view {
    margin: 0;
    padding: 0;

    .codeNum {
        display: flex;
        justify-content: center;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .code_div {
        // margin-bottom: 10px;
        .bar-wrap {
            width: 218px;
            height: 60px;
            margin: 0 auto;
            // margin-top: 20px;
            img {
                margin: 0 auto;
                width: 100%;
                height: 100%;
            }
        }
        .qr-code-wrap {
            width: 275px;
            height: 275px;
            margin: 0 auto;
            // margin-top: 20px;
            .qrcode {
                margin: 0 auto;
                width: 100%;
                height: 100%;
            }
        }
        .card-text {
            // padding-bottom: 10px;
            margin-top: 10px;
            img {
                width: 8px;
                height: 8px;
                margin-left: 3px;
            }
        }
    }

    .no-code-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 90rpx;
        .no-code-img {
            width: 114rpx;
            height: 114rpx;
        }
        .code-text {
            margin-top: 28rpx;
            font-size: 30rpx;
            font-weight: 400;
            color: #333333;
            line-height: 42rpx;
        }
        .code-refresh {
            margin-top: 60rpx;
            width: 218rpx;
            height: 80rpx;
            background: #e64f22;
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(255, 87, 36, 0.25);
            border-radius: 41rpx;
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 80rpx;
            text-align: center;
            text-shadow: 0px 9px 9px rgba(255, 87, 36, 0.25);
        }
        .code-enablePositioning {
            margin-top: 60rpx;
            width: 218rpx;
            height: 80rpx;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(255, 87, 36, 0.25), 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 41rpx;
            font-size: 30rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 80rpx;
            text-align: center;
            text-shadow: 0px 9px 9px rgba(255, 87, 36, 0.25);
        }
    }
    .img-wrap {
        margin: 0;
        padding: 0;
        height: 30px;
        min-height: 30px;
        .img_style {
            width: 100%;
            height: 100%;
        }
    }
    .footer {
        width: 100%;
        // height: 200px;
        padding-bottom: 37px;
        border-radius: 0 0 8px 8px;
        .text1 {
            margin-bottom: 7px;
        }
        .text2 {
        }
        .payMethods {
            margin-top: 5px;
            margin-bottom: 15px;
            .img-wrap-sj {
                margin-left: 5px;
                width: 7px;
                height: 7px;
                line-height: 7px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .code-line {
            padding-bottom: 15px;
        }
    }
}
</style>
