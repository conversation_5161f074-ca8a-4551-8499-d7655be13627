<template>
    <div class="content">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            back-text="移动支付设置"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <swiper
            class="card-swiper"
            indicator-active-color="#FF8200"
            :indicator-dots="oilCardArr.length > 1"
            :autoplay="true"
            :interval="3000"
            :duration="1000"
        >
            <swiper-item class="card-swiper-item" v-for="(item, index) in oilCardArr" :key="index">
                <div class="oil-card-view">
                    <div class="card-title-view">
                        <img class="card-icon" src="@/static/homeIcon/petroChinaIcon.png" mode="" />
                        <div class="card-title">昆仑加油卡</div>
                    </div>
                    <div class="card-no">{{ item.cardNo | cardNo }}</div>
                    <img class="card-bg" src="@/static/card-bg.png" mode="" />
                    <div class="card-pay-text">已开通移动支付</div>
                </div>
            </swiper-item>
        </swiper>
        <div class="btn">关闭移动支付</div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { cardListPost, getCardDetails } from '@/api/home.js';
export default {
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            oilCardArr: [],
        };
    },
    async onLoad() {
        let oilCardRes = await cardListPost();
        for (let i = 0; i < oilCardRes.data.length; i++) {
            let oilCard = await getCardDetails({
                cardNo: oilCardRes.data[i].cardNo,
            });
            oilCardRes.data[i] = oilCard.data;
            oilCardRes.data[i].afterNum = oilCard.data.cardNo.substring(oilCard.data.cardNo.length - 4, oilCard.data.cardNo.length);
        }
        this.oilCardArr = oilCardRes.data;
    },
    filters: {
        cardNo(code) {
            let codeArr = code.split('');
            let str = '';
            for (let i = 0; i < codeArr.length; i++) {
                if (i % 4 == 0) {
                    if (i != 0) {
                        str += ' ';
                    }
                }
                if (i > 3 && i < 12) {
                    str += '*';
                } else {
                    str += codeArr[i];
                }
            }
            return str;
        },
    },
    methods: {},
};
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100%;
    background: #f6f6f6;
    .card-swiper {
        height: 260px;
        .card-swiper-item {
            padding: 15px;
            width: 345px;
            .oil-card-view {
                background-color: #01a2e2;
                height: 200px;
                width: 345px;
                border-radius: 10px;
                position: relative;
                .card-title-view {
                    display: flex;
                    align-items: center;
                    padding-top: 17.5px;
                    margin-left: 15px;
                    .card-icon {
                        width: 44px;
                        height: 44px;
                    }
                    .card-title {
                        margin-left: 10px;
                        font-size: 15px;
                        color: #ffffff;
                    }
                }
                .card-no {
                    position: absolute;
                    left: 15px;
                    bottom: 15px;
                    font-size: 18px;
                    line-height: 25px;
                    font-weight: 700;
                    color: #ffffff;
                }
                .card-bg {
                    position: absolute;
                    right: 0px;
                    bottom: -10px;
                    width: 115px;
                    height: 200px;
                    opacity: 0.2;
                }
                .card-pay-text {
                    position: absolute;
                    right: 0;
                    top: 30px;
                    line-height: 20px;
                    font-size: 11px;
                    width: 97px;
                    color: #ffffff;
                    padding-left: 10px;
                    padding-right: 10px;
                    border-bottom-left-radius: 10px;
                    border-top-left-radius: 10px;
                    background-color: #333333;
                }
            }
        }
    }
    .btn {
        width: 200px;
        line-height: 44px;
        border-radius: 5px;
        color: #ffffff;
        font-weight: 700;
    }
}
</style>
