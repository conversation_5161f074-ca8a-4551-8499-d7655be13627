<template>
    <div class="view fl-column">
        <zj-navbar :height="44" title="更换绑定手机号"></zj-navbar>

        <div class="reminder">您以下电子券与当前手机号绑定，暂不支持变更到新的手机号上</div>
        <div class="coupon-wrap">
            <!-- <zj-data-list
        background="#F7F7FB"
        ref="dataList"
        emptyText="暂无电子券"
        :showEmpty="showEmpty"
        @refreshPullDown="refreshPullDown"
        @scrolltolower="scrolltolower"
      >-->
            <div class="padding_16">
                <div v-for="(item, index) in couponArray" :key="index" @click="unusedDetails(item, index)">
                    <div
                        class="border-rad-8"
                        :class="{
                            'bg-coupon': item.bgColor,
                            toTakeEffect: !item.bgColor,
                        }"
                    >
                        <div
                            class="upperLeft te-center weight-500"
                            :class="{
                                'bg-ff6133': item.bgColor,
                                'bg-999': !item.bgColor,
                            }"
                            >{{ getCouponType(item.voucherType) }}</div
                        >
                        <div class="content-wrap fl-row">
                            <div class="left-wrap fl-row">
                                <div class="content-left fl-column fl-al-jus-cen">
                                    <div
                                        class="price fl-row fl-al-base"
                                        :class="{
                                            'color-E64F22': item.bgColor,
                                            'color-666': !item.bgColor,
                                        }"
                                    >
                                        <div class="symbol" v-if="item.voucherType === '1'">&yen;</div>
                                        <div class="price_amount">{{ item.price }}</div>
                                        <div v-if="item.voucherType === '2'" class="symbol">折</div>
                                    </div>
                                    <div
                                        class="instructions"
                                        :class="{
                                            'color-EB5130': item.bgColor,
                                            'color-666': !item.bgColor,
                                        }"
                                        >{{ item.instructions }}</div
                                    >
                                </div>
                                <div class="content-sx"></div>
                            </div>
                            <div class="right-wrap">
                                <div class="content-right">
                                    <div class="title">{{ item.title }}</div>

                                    <div class="fl-column fl-jus-cen">
                                        <div
                                            class="type-cou bg-transparent btn-plain-tran border-rad-2"
                                            :class="{
                                                'color-FA6400 border-fa6400': item.bgColor,
                                                'color-666 border-999': !item.bgColor,
                                            }"
                                            >油品券</div
                                        >
                                        <div class="time">有效期至：{{ item.validityPeriod }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- </zj-data-list> -->
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 油品、非油、全部、默认选中的值
            selectId: 20,
            // 全排序类型默认值
            sortId: '',
            couponArray: [],
        };
    },
    mounted() {
        // 获取电子券列表
        this.getCouponList();
    },
    methods: {
        // 电子券类型
        getCouponType(val) {
            console.log(val, '电子券类型');
            return val == 1 ? '汽油券' : '折扣券';
        },

        // 上拉加载
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getData();
            }
        },
        // 下拉刷新
        refreshPullDown(e) {
            //  重置数据
            this.getData({ isInit: true });
        },
        getCouponList({ isInit = false } = {}) {
            if (isInit) {
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let data = [
                {
                    voucherType: '2',
                    price: '50',
                    title: '在回龙观，新昌加油站购买95号汽油满100可抵扣100元',
                    instructions: '满100元可用',
                    validityPeriod: '2023-09-17 00:00:00',
                },
                {
                    voucherType: '2',
                    price: '50',
                    title: '购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-09-17 00:00:00',
                },
                {
                    voucherType: '2',
                    price: '50',
                    title: '购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-09-17 00:00:00',
                },
                {
                    voucherType: '1',
                    price: '50',
                    title: '购买非油商品购买非油商品购买非油商品购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-10-17 00:00:00',
                },
                {
                    voucherType: '1',
                    price: '50',
                    title: '购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-10-17 00:00:00',
                },
                {
                    voucherType: '2',
                    price: '50',
                    title: '购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-10-17 00:00:00',
                },

                {
                    voucherType: '2',
                    price: '50',
                    title: '购买非油商品',
                    instructions: '满100元可用',
                    validityPeriod: '2023-09-17 00:00:00',
                },
            ];
            const currentTimestamp = new Date().getTime();
            data = data.map(item => {
                var handelTime = new Date(item.validityPeriod.replace(/-/g, '/'));
                if (currentTimestamp > Date.parse(handelTime)) {
                    // 到使用日期
                    item.bgColor = true;
                } else {
                    // 未到使用日期
                    item.bgColor = false;
                }
                return item;
            });
            this.couponArray = data;
            // 获取数据成功后
            this.$refs.dataList.stopRefresh();
            // 处理数据
            // 数据为空
            this.showEmpty = true;
            // 页码不为最大值
            this.$refs.dataList.loadStatus = 'contentdown';
            // 页码为最大值
            this.$refs.dataList.loadStatus = 'nomore';
        },
    },
};
</script>

<style scoped lang="scss">
.view {
    .reminder {
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 12px;
        color: #333;
        background: #fff7dc;
    }
    .coupon-wrap {
        .bg-coupon {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);
            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                color: #fff;
                font-size: 10px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.5;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    .content-right {
                        // margin-top: -17px;
                        .title {
                            // line-height: 21px;
                            margin-top: 14px;
                            // margin-bottom: 20px;
                            font-size: 24px;
                            color: #111;
                            font-weight: 500;
                            height: 66px;
                        }

                        .time {
                            font-weight: 400;
                            color: #999999;
                            line-height: 28px;
                            margin-top: 6px;
                        }
                        .type-cou {
                            width: 36px;
                            height: 12.5px;
                            line-height: 12.5px;
                            margin-top: 8px;
                            font-size: 9px;
                        }
                    }
                }
            }
        }
        // 券未生效
        .toTakeEffect {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;
            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }
            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                color: #fff;
                font-size: 10px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;
                .left-wrap {
                    width: 30%;
                    .content-left {
                        // margin-top: 25px;
                        width: 100%;
                    }
                    .content-sx {
                        height: 136rpx;
                        opacity: 0.3;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }
                .right-wrap {
                    width: 70%;
                    height: 100%;
                    .content-right {
                        // margin-top: -17px;
                        .title {
                            margin-top: 14px;
                            // margin-bottom: 20px;
                            font-size: 24px;
                            color: #666;
                            font-weight: 500;
                            height: 66px;
                        }

                        .time {
                            font-weight: 400;
                            color: #999999;
                            line-height: 28px;
                            margin-top: 6px;
                        }
                        .type-cou {
                            width: 36px;
                            height: 12.5px;
                            line-height: 12.5px;
                            margin-top: 8px;
                            font-size: 9px;
                        }
                    }
                }
            }
        }
    }
}
.padding_16 {
    padding: 0 16px;
}
.color-666 {
    color: #666;
}
.border-rad-2 {
    border-radius: 2px;
}
// 默认尺寸-透明
.btn-plain-tran {
    background: transparent;
    text-align: center;
}
.bg-transparent {
    background: transparent;
}
.border-fa6400 {
    border: 0.5px solid #fa6400;
}
.border-999 {
    border: 0.5px solid #999999;
}
.fl-al-end {
    align-items: flex-end;
}
.fl-jus-bet {
    justify-content: space-between;
}
.fl-row {
    display: flex;
    flex-direction: row;
}
.fl-jus-cen {
    justify-content: center;
}
.fl-column {
    display: flex;
    flex-direction: column;
}
.weight-600 {
    font-weight: 600;
}
.weight-500 {
    font-weight: 500;
}
.color-EB5130 {
    color: #eb5130;
}
.fl-al-base {
    align-items: baseline;
}
.bg-ff6133 {
    background: #ff6133;
}
.bg-999 {
    background: #999;
}
.border-rad-8 {
    border-radius: 8px;
}

.color-fff {
    color: #fff;
}
.te-center {
    text-align: center;
}
.color-FA6400 {
    color: #fa6400;
}
.symbol {
    font-size: 14px;
}
.price_amount {
    font-size: 28px;
}
.instructions {
    font-size: 13px;
}
</style>
