<template>
    <div class="xf-records">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="充值记录"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="content-box" v-if="dataList.length > 0">
            <div class="list-item" v-for="(item, index) in dataList" :key="index" @click="toDetail(item)">
                <div class="title">{{ item.orgName }}</div>
                <div class="detail">创建时间：{{ item.tradeTime }}</div>
                <div class="prize">
                    <div class="left">
                        <span>￥</span>
                        <my-price color="#f96702" intFont="40rpx" floatFont="22rpx" :price="item.amount"></my-price>
                    </div>
                    <div v-if="item.invoiceStatus == 1" class="cell-btn" @click.stop="seeInvoice(item)">开票</div>
                    <div v-if="item.invoiceStatus == 3" class="cell-btn" @click.stop="seeInvoice(item)">开票中</div>
                    <div v-if="item.invoiceStatus == 4" class="cell-btn" @click.stop="seeInvoice(item)">红冲中</div>
                    <div v-if="item.invoiceStatus == 2" @click.stop="seeInvoice(item)" class="cell-btn">查看发票</div>
                </div>
            </div>
        </div>
        <div v-if="dataList.length == 0 || !hasMore" class="no-more">
            <u-loadmore status="nomore" />
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import myPrice from '@/components/price/price.vue';
import { getRechargeList } from '@/api/home';
export default {
    components: {
        myPrice,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            dataList: [],
            page: 1,
            pageSize: 10,
            cardNo: '',
            hasMore: true,
        };
    },
    onLoad(options) {
        this.cardNo = options.cardNo;
        this.loadData();
    },
    onShow() {},
    onReachBottom() {
        if (!this.hasMore) {
            return;
        }
        this.loadData(true);
    },
    methods: {
        async loadData(loadMore) {
            this.page = loadMore ? this.page + 1 : 1;
            let params = {
                cardNo: this.cardNo,
                pageNo: this.page,
                pageSize: this.pageSize,
            };
            let res = await getRechargeList(params);
            this.dataList = loadMore ? this.dataList.concat(res.data) : res.data;
            if (res.data.length == 0 || res.data.length < this.pageSize) {
                this.hasMore = false;
            }
            if (res.data.length == 0) {
                // uni.showModal({
                //   title: '提示',
                //   content: '本查询结果只显示12个月内记录，如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                //   confirmColor: '#FF8200',
                //   showCancel: false,
                //   success: () => {
                //   }
                // })
            }
            if (res.status == -1) {
                // let str = ''
                // if (res.info.includes('95504')) {
                //   str = '本查询结果只显示12个月内记录如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)'
                // }
                uni.showModal({
                    title: '提示',
                    content: res.info,
                    confirmColor: '#FF8200',
                    showCancel: false,
                    success: () => {},
                });
            }
        },
        // 查看详情
        toDetail(item) {
            uni.navigateTo({
                url: `/packages/order/pages/recharge-order-detail/main?id=${item.orderId}&invoiceStatus=${item.invoiceStatus}&type=0`,
            });
        },
        // seeInvoice(item) {
        //   uni.navigateTo({
        //     url: `/packages/invoice-center/pages/invoice-detail/main?id=${item.invoiceId}&type=0`,
        //   })
        // },
        seeInvoice(item) {
            if (item.invoiceStatus == 2) {
                uni.navigateTo({
                    url: `/packages/invoice-center/pages/invoice-detail/main?id=${item.invoiceId}&type=0`,
                });
            } else {
                uni.navigateTo({
                    url: `/packages/order/pages/recharge-order-detail/main?id=${item.orderId}&invoiceStatus=${item.invoiceStatus}&type=0`,
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.xf-records {
    min-height: 100vh;
    background: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);
    .content-box {
        padding: 24rpx;
        .list-item {
            padding: 24rpx;
            border-radius: 16rpx;
            background: #fff;
            margin-bottom: 24rpx;
            .title {
                font-size: 26rpx;
            }
            .detail {
                font-size: 22rpx;
                color: #999999;
                margin-top: 15rpx;
            }
            .prize {
                margin-top: 25rpx;
                display: flex;
                align-items: baseline;
                justify-content: space-between;
                .left {
                    display: flex;
                    align-items: baseline;
                    font-size: 40rpx;
                    color: #f96702;
                    span {
                        font-size: 22rpx;
                    }
                }
                .cell-btn {
                    min-width: 146rpx;
                    text-align: center;
                    padding: 8rpx 25rpx;
                    border: solid 1rpx #e5e5e5;
                    font-size: 24rpx;
                    border-radius: 8rpx;
                }
            }
        }
    }
    .no-more {
        padding: 24rpx 0;
        text-align: center;
        ::v-deep .u-load-more-wrap {
            background-color: transparent !important;
            view {
                background-color: transparent !important;
            }
        }
    }
}
</style>
