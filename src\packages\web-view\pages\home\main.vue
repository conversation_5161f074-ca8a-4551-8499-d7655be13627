<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <view class="view">
        <div>
            <web-view :src="src" @onPostMessage="onPostMessage" @message="message">
                <cover-view v-if="type == 'invoice'" class="copy" @click="copyUrl()">复制链接</cover-view>
            </web-view>
        </div>
        <zj-show-modal></zj-show-modal>
    </view>
</template>

<script>
// import pageConfig from "@/utils/pageConfig";
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    // props: {
    //     src: String
    // }
    data() {
        return {
            src: '',
            name: '',
            type: '',
        };
    },
    onLoad(options) {
        if (options) {
            this.src = options.src ? decodeURIComponent(options.src) : '';
            this.type = options.type ? options.type : '';
            this.name = options.name ? options.name : '';
        } else {
            let option = getApp().globalData.pageParams;
            this.src = option.src ? decodeURIComponent(option.src) : '';
            this.type = option.type ? option.type : '';
            this.name = option.name ? option.name : '';
        }
        this.getPageType();
        console.log(this.src, this.name, 'vVVVVVV');
        uni.setNavigationBarTitle({
            title: this.name,
        });
        // #ifdef MP-ALIPAY || MP-WEIXIN
        this.copyModal();
        // #endif
        // #ifdef MP-ALIPAY
        this.webViewContext = my.createWebViewContext('web-view-1');
        // #endif
    },
    onShow() {
        setTimeout(() => {
            wx.hideHomeButton(); // 隐藏返回首页图标
        }, 100);
    },
    computed: {
        ...mapState({
            token: state => state.token,
            token3: state => state.token3,
        }),
    },
    methods: {
        copyModal() {
            if (this.type == 'invoice') {
                uni.showModal({
                    title: '',
                    content: '复制当前链接到浏览器打开即可下载发票',
                    confirmText: '复制',
                    showCancel: false,
                    success: () => {
                        uni.setClipboardData({
                            data: this.src,
                            success() {
                                uni.showToast({
                                    title: '复制成功，请在浏览器打开',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            },
                        });
                    },
                });
            }
        },
        getPageType() {
            console.log('进入判断');
            if (this.src.substr(-4) === '.zip') {
                console.log('是zip链接');
                let url = '/packages/web-view/pages/copy-url/main';
                let params = { src: this.src };
                let type = 'reLaunch';
                this.$sKit.layer.useRouter(url, params, type);
            } else {
                console.log('不是zip链接');
            }
        },
        message(e) {
            console.log('非实时接收到的 web-view 消息：', e.detail);
            this.$sKit.layer.requestSubscribeMessage();
        },
        onPostMessage(e) {
            console.log('实时接收到的 web-view 消息：', e.detail);
        },
        copyUrl() {
            uni.setClipboardData({
                data: String(this.src), //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        // handleWebviewMessage(e) {
        //     // this.webViewContext.postMessage({'sendToWebView': '1'});
        //     console.log(e, 'webview返回方法触发了吗');
        //     if (e.detail.data === 'backHome') {
        //         if (this.token && this.token3 == '') {
        //             uni.reLaunch({
        //                 url: '/page/home/<USER>',
        //             });
        //         } else if (this.token == '') {
        //             uni.reLaunch({
        //                 url: '/packages/setting/pages/login/main',
        //             });
        //         } else if (this.token3) {
        //             uni.reLaunch({
        //                 url: '/page/thirdHome/main',
        //             });
        //         }
        //     }
        // },
    },
};
</script>
<style lang="scss" scoped>
.copy {
    position: fixed;
    bottom: 30px;
    right: 5px;
    width: 165px;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 400;
    line-height: 44px;
    text-align: center;
    z-index: 999999;
    background: #ff3e00 0%;
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
    color: #ffffff;
}
</style>
