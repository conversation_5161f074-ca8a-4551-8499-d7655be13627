<!-- 自定义弹出层组件 -->
<template>
    <view class="page" v-if="isShow">
        <!-- 测试按钮 -->
        <!-- <view class="button-group">
            <button @click="showPopup('top')">顶部弹出</button>
            <button @click="showPopup('center')">中间弹出</button>
            <button @click="showPopup('bottom')">底部弹出</button>
        </view> -->

        <!-- 自定义弹出层 -->
        <view class="custom-popup" :class="[type]" @touchmove.stop.prevent>
            <!-- 遮罩层 -->
            <view class="mask" :class="{ 'mask-show': overlay }" @click="maskClick ? close() : null"></view>

            <!-- 内容区域 -->
            <view class="popup-content" :class="[type, isTransition ? 'transition' : '', showContent ? 'content-show' : '']">
                <view class="popup-body" @click.stop>
                    <slot>
                        <text>默认内容</text>
                    </slot>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
/**
 * CustomPopup 自定义弹出层组件
 * @description 一个支持多种弹出方式的弹出层组件，支持顶部、底部、中间弹出，带有过渡动画效果
 * @property {Boolean} maskClick - 是否允许点击遮罩层关闭弹窗，默认为 true
 * @event {Function} change - 弹窗状态改变时触发，返回 {show: Boolean, type: String}
 * @example <custom-popup :mask-clickable="true" @change="onChange" />
 */
export default {
    name: 'CustomPopup',
    props: {
        // 是否可以点击遮罩关闭弹窗
        maskClick: {
            type: Boolean,
            default: false,
            desc: '是否允许点击遮罩层关闭弹窗',
        },
        type: {
            type: String,
            default: 'center',
            desc: '弹出方式：top-顶部弹出，center-中间弹出，bottom-底部弹出',
        },
        safeArea: {
            type: Boolean,
            default: true,
            desc: '是否开启安全区域',
        },
        overlay: {
            type: Boolean,
            default: true,
            desc: '是否开启遮罩层',
        },
    },
    data() {
        return {
            // 控制整个弹窗的显示隐藏
            isShow: false,
            // 控制内容区域的显示状态
            showContent: false,
            // 是否启用过渡动画
            isTransition: true,
            // 动画持续时间（毫秒）
            duration: 300,
        };
    },
    methods: {
        /**
         * 显示弹出层
         * @param {String} type - 弹出方式，可选值：top/center/bottom
         */
        open() {
            this.isShow = true;
            // 显示动画
            setTimeout(() => {
                this.showContent = true;
            }, 50);
        },

        /**
         * 关闭弹出层
         * @description 关闭时会先执行动画，动画结束后才真正隐藏组件
         * @fires change 弹窗状态改变事件
         */
        close() {
			console.log('测试执行了吗')
            this.showContent = false;
            // this.overlay = false
            // 延迟隐藏整个组件
            // setTimeout(() => {
            this.isShow = false;
            this.$emit('maskClick', {
                show: false,
                type: this.type,
            });
            // }, this.duration)
        },
    },
};
</script>

<style lang="scss" scoped>
.page {
    position: fixed;
    z-index: 9997;
}

.button-group {
    padding: 20px;

    button {
        margin: 10px 0;
        padding: 10px 20px;
        background-color: #007aff;
        color: #fff;
        border: none;
        border-radius: 4px;

        &:active {
            opacity: 0.8;
        }
    }
}

.custom-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    //z-index: 9999;

    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9998;
        background-color: rgba(0, 0, 0, 0);
        transition: background-color 0.3s;

        &.mask-show {
            background-color: rgba(0, 0, 0, 0.4);
        }
    }

    .popup-content {
        z-index: 9999;
        position: fixed;
        // background-color: #fff;
        transition: all 0.3s;
        // border-radius: 8px;

        &.transition {
            transition: all 0.3s;
        }

        // 顶部弹出
        &.top {
            left: 0;
            right: 0;
            transform: translateY(-100%);

            &.content-show {
                transform: translateY(0);
            }
        }

        // 底部弹出
        &.bottom {
            left: 0;
            right: 0;
            bottom: 0;
            transform: translateY(100%);

            &.content-show {
                transform: translateY(0);
            }
        }

        // 中间弹出
        &.center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0;

            &.content-show {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    }

    .popup-body {
        display: block;
    }
}
</style>
