// issue: uni-app支付宝小程序分包bug node_modules/@dcloudio/uni-mp-alipay/dist/index.js:2092
// 分包属性丢失，地图标点Market、气泡CallOut点击事件无效，此文件临时兼容处理此问题

import projectConfig from '../../project.config';

export default {
    // #ifdef MP-ALIPAY
    created() {
        // 法一，根据组件路由补充注册事件
        let subPath = projectConfig?.subPath || '';
        if (subPath) {
            let path = this.$scope.is || this.$scope.route;
            if (path.indexOf('/') === 0) {
                path = path.substr(1);
            }
            my.specialMethods = my.specialMethods || {};
            my.specialMethods[path] = ['clickCallOut', 'clickMarker'];
        }

        // 法二，白名单路由补充注册事件
        // if (subPath.indexOf('/') === 0) {
        //     subPath = subPath.substr(1);
        // }
        // if (subPath) {
        //     my.specialMethods = {
        //         [`${subPath}/packages/third-oil-charge-payment/pages/oil-station-module/main`]: ['clickCallOut', 'clickMarker'],
        //         [`${subPath}/pages/thirdHome/components/third-oil/main`]: ['clickCallOut', 'clickMarker'],
        //     };
        // }
    },
    mounted() {
        console.log('zfb map mounted');
    },
    methods: {},
    // #endif
};
