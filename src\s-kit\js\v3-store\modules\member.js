import { memberInfo, basicCoupon } from '../../v3-http/https3/classInterest/index.js';
export default {
    // 挂在store到全局Vue原型上
    state: {
        //会员的油卡，电子券，积分和余额信息，七日内余额等信息
        memberAccountInfo: {
            ptsAvailableAmount: '', //客户能源币数量
            fuelCardCount: 0, //油卡数量
            couponsCount: 0, //电子券可使用张数
            loyaltyPtsAvailableAmount: '0', //积分余额
            eWalletAvailableAmount: '0', //电子钱包余额（元）
            ptsAmountExpiresInOneWeek: '', //7日内过期的能源币数量
            loyaltyPtsAmountExpiresInOneWeek: '', //7日内过期的积分数值
            localInfo: '', //开户地信息
        },
        // 会员信息对象
        memberBaseInfo: {
            userId: '',
            username: '', //真实姓名
            levelRuleItemId: '', //会员等级id
            levelName: '', //会员等级
            phone: '', //客户手机号（脱敏）
            avatarPhoto: '', //头像
            usedPlace: '', //常用地
            memberNo: '', //会员编码
        },
        // memberCodeBoolean: false,
    },

    getters: {
        //会员的油卡，电子券，积分和余额信息，七日内余额等信息
        memberAccountInfo: state => state.memberAccountInfo,
        memberBaseInfo: state => state.memberBaseInfo,
    },
    mutations: {
        //会员的油卡，电子券，积分和余额信息，七日内余额等信息
        setMemberAccountInfo(state, info) {
            state.memberAccountInfo = info;
        },
        // 会员信息对象
        setMemberBaseInfo(state, info) {
            state.memberBaseInfo = info;
        },
    },
    actions: {
        //获取能源币，油卡，电子券，积分和余额，七日内余额
        async basicCouponAction({ state, commit, dispatch }) {
            let res = await basicCoupon({}, { isload: false, isCustomErr: true });
            if (res.success) {
                commit('setMemberAccountInfo', res.data);
            } else {
                commit('setMemberAccountInfo', {
                    ptsAvailableAmount: '', //客户能源币数量
                    fuelCardCount: 0, //油卡数量
                    couponsCount: 0, //电子券可使用张数
                    loyaltyPtsAvailableAmount: '0', //积分余额
                    eWalletAvailableAmount: '0', //电子钱包余额（元）
                    ptsAmountExpiresInOneWeek: '', //7日内过期的能源币数量
                    loyaltyPtsAmountExpiresInOneWeek: '', //7日内过期的积分数值
                    localInfo: '', //开户地信息
                });
            }
        },
        // 会员中心基本信息
        async memberBaseInfoAction({ state, commit, dispatch }) {
            return new Promise(async (resolve, reject) => {
                let res = await memberInfo({}, { isload: false, isCustomErr: true });
                if (res.success) {
                    commit('setMemberBaseInfo', res.data);
                    resolve(res);
                }
            });

            // console.log(' state.memberBaseInfo --', state.memberBaseInfo )
        },
    },
};
