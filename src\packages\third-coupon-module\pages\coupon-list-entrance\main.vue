<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="我的电子券"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <div class="tabs_style p-LR-16 mh-0 fl-row fl-al-cen">
                    <div
                        v-for="(tab, index) in tabs"
                        :key="index"
                        :class="{ select_style: selectId == tab.id }"
                        class="tab_style"
                        @click="selectClick(tab, index)"
                    >
                        <div class="font-15 weight-500">{{ tab.title }}({{ tab.quantity || 0 }})</div>
                    </div>
                </div>
                <div class="tips_style">
                    <div class="tips_text">自动匹配最大优惠券</div>
                    <img v-if="!switchTab" alt="" class="img_style" src="../../images/switch_off.png" @click="switchTabClick()" />
                    <img v-else @click="switchTabClick()" class="img_style" src="../../images/switch_on.png" alt="" />
                </div>
                <div class="p-LR-16">
                    <div class="tips_div">
                        <div class="title"> <img src="../../images/tips.png" alt />温馨提示： </div>
                        <div class="tips">
                            <div class="fl-row">
                                <div class="dots_div"></div>
                                <div>系统将优先使用您选择的优惠券，如您想确保最大优惠，可仅开启按钮不额外选择电子券。</div></div
                            >
                        </div>
                    </div>
                </div>
                <div class="coupon-wrap f-1 mh-0">
                    <!-- <zj-data-list background="#F7F7FB" ref="dataList" emptyText="暂无电子券" :showEmpty="showEmpty" :emptyImage="noPicture"> -->
                    <div class="padding-16 content_div" v-if="couponArray.length > 0">
                        <div
                            v-for="(item, index) in couponArray"
                            :key="index"
                            class="f-1 fl-row fl-al-cen"
                            @click="ticketCellClickAction(item, index)"
                        >
                            <div class="select-img-wrap border-rad-8" v-if="item.bgColor">
                                <img v-if="selectedCouponInfo.couponNo == item.couponNo" alt src="../../images/successSel.png" />
                                <img v-else src="../../images/empty.png" alt />
                            </div>
                            <div
                                :class="{
                                    'bg-coupon': item.bgColor,
                                    toTakeEffect: !item.bgColor,
                                }"
                                class="border-rad-8"
                            >
                                <!-- <div
                                    class="upperLeft font-10 weight-500 color-fff te-center"
                                    :class="{
                                        'bg-ff6133': item.bgColor,
                                        'bg-999': !item.bgColor,
                                    }"
                                    >{{ differentiationType(item.kind) }}</div
                                > -->
                                <div class="content-wrap fl-row">
                                    <div class="left-wrap fl-row">
                                        <div class="content-left fl-column fl-al-jus-cen">
                                            <!-- <img src="../../images/kt1yhq.png" alt=""> -->
                                            <div v-if="!item.templatePicOss">
                                                <div
                                                    :class="{
                                                        'color-E64F22': item.bgColor,
                                                        'color-666': !item.bgColor,
                                                    }"
                                                    class="price fl-row fl-al-base fl-jus-cen"
                                                >
                                                    <div v-if="item.couponType && item.couponType != '40'" class="symbol font-14 weight-400"
                                                        >&yen;</div
                                                    >
                                                    <div class="font-28 weight-600">
                                                        {{
                                                            item.couponType && item.couponType != '40'
                                                                ? item.couponAmount
                                                                : item.couponAmount
                                                        }}
                                                    </div>
                                                    <div v-if="item.couponType && item.couponType == '40'" class="symbol font-14 weight-400"
                                                        >折</div
                                                    >
                                                </div>
                                                <div
                                                    :class="{
                                                        'color-EB5130': item.bgColor,
                                                        'color-666': !item.bgColor,
                                                    }"
                                                    class="font-13 weight-400"
                                                    >{{ thresholdAmount(item) }}</div
                                                >
                                            </div>
                                            <img v-else-if="item.templatePicOss" :src="item.templatePicOss" />
                                        </div>
                                        <div class="content-sx"></div>
                                    </div>
                                    <div class="right-wrap fl-column fl-jus-bet">
                                        <div class="title font-14 color-1E1E1E weight-600">{{ item.couponTemplateName }} </div>
                                        <div class="title2 fl-row fl-jus-bet fl-al-end">
                                            <div class="fl-column fl-jus-cen">
                                                <div
                                                    :class="{
                                                        'color-FA6400 border-fa6400': item.bgColor,
                                                        'color-666 border-999': !item.bgColor,
                                                    }"
                                                    class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400"
                                                    >{{ item.categoryType == 1 ? '油品券' : '非油券' }}</div
                                                >
                                                <div class="time font-10 color-999 weight-400">{{ screen(item) }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <zj-no-data v-if="couponArray.length == 0" :emptyImage="noPicture" emptyText="暂无电子券"> </zj-no-data>
                    <!-- </zj-data-list> -->
                </div>
                <div class="btnWrap p-LR-16">
                    <div class="weight-500 btn-plain-sure font-15 border-rad-8" @click="couponAusageRecord">确认</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
// 一次请求多少条
// #ifndef MP-MPAAS
import cnpcBridge from '../../../../s-kit/js/v3-native-jsapi/cnpcBridge';
// #endif
const PAGE_SIZE = 20;
import { mapGetters, mapState } from 'vuex';
import { couponList, couponAmount } from '../../../../s-kit/js/v3-http/https3/conpon/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 一级筛选条件
            tabs: [
                { id: 1, title: '可用优惠券', quantity: '' },
                { id: 0, title: '不可用优惠券', quantity: '' },
            ],
            // 油品、非油、全部、默认选中的值
            selectId: '1',
            // 全排序类型默认值
            sortId: '0',
            // 电子券数组
            couponArray: [],
            // 页码
            page: 1,
            // 暂无电子券图片
            noPicture: require('../../images/kt1yhq.png'),
            // 是否展示空态标识
            showEmpty: false,
            // 总条数
            totalPage: 0,
            switchTab: true,
            unCouponList: [],
            availableList: [],
            selectedCouponInfo: {
                couponNo: '',
                couponTemplateNo: '',
                couponType: '',
                couponAmount: '',
            },
        };
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    onLoad(query) {
        let params;
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            const nowPage = getCurrentPages().pop();
            params = getApp().globalData.query?.[nowPage.$page.fullPath];
            if (!params?.zfbRefererMax) {
                console.log('params?.zfbRefererMax', params.zfbRefererMax);
                params = JSON.parse(decodeURIComponent(query.data));
            }
        } else {
            params = JSON.parse(decodeURIComponent(query.data));
        }
        // console.log(params, 'params====');
        if (Object.keys(params).length !== 0) {
            this.unCouponList = params.unCouponList;
            this.availableList = params.availableList;
            this.couponArray = this.availableList;
            // this.couponArray = [];
            this.tabs[0].quantity = this.availableList.length || 0;
            this.tabs[1].quantity = this.unCouponList.length || 0;
            if (this.couponArray.length) {
                this.showEmpty = true;
            }
            if (params.type) {
                this.selectId = params.type == '' ? '20' : params.type == 1 ? '40' : params.type == 2 ? '70' : 20;
                this.categoryType = params.type;
            }
        }
        // #endif
        // #ifdef H5-CLOUD
        const nowPage = getCurrentPages().pop();
        params = getApp().globalData.query?.[nowPage.$page.fullPath];
        if (!params?.zfbRefererMax) {
            console.log('params?.zfbRefererMax', params.zfbRefererMax);
            params = JSON.parse(decodeURIComponent(query.data));
        }
        if (Object.keys(params).length !== 0) {
            this.unCouponList = params.unCouponList;
            this.availableList = params.availableList;
            // this.couponArray = this.availableList;
            // this.couponArray = [];
            this.tabs[0].quantity = this.availableList.length || 0;
            this.tabs[1].quantity = this.unCouponList.length || 0;
            if (this.couponArray.length) {
                this.showEmpty = true;
            }
            if (params.type) {
                this.selectId = params.type == '' ? '20' : params.type == 1 ? '40' : params.type == 2 ? '70' : 20;
                this.categoryType = params.type;
            }
        }
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD
        if (Object.keys(query).length !== 0) {
            console.log('query--', query);
            params = JSON.parse(decodeURIComponent(query.data));
            console.log(params, 'params====');
            this.unCouponList = params.unCouponList;
            this.availableList = params.availableList;
            this.couponArray = this.availableList;
            // this.couponArray = [];
            this.tabs[0].quantity = this.availableList.length || 0;
            this.tabs[1].quantity = this.unCouponList.length || 0;
            if (this.couponArray.length) {
                this.showEmpty = true;
            }
            if (params.type) {
                this.selectId = params.type == '' ? '20' : params.type == 1 ? '40' : params.type == 2 ? '70' : 20;
                this.categoryType = params.type;
            }
        }
        // #endif
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
    },
    mounted() {
        // setTimeout(() => {
        console.log('走到这coupon-list-entrance');
        this.handleCouponList();
        this.handleUnCouponList();
        // }, 1000);
    },

    methods: {
        /**
         * @description  : 选择电子券
         * @return        {*}
         */
        ticketCellClickAction(item) {
            if (!item.bgColor) return;
            // this.switchTab = false;
            console.log(item, '==========11');
            if (this.selectedCouponInfo.couponNo == item.couponNo) {
                this.selectedCouponInfo = {
                    couponNo: '',
                    couponTemplateNo: '',
                    couponType: '',
                    couponAmount: '',
                };
            } else {
                this.selectedCouponInfo = item;
            }
        },
        /**
         * @description  : 匹配选中的优惠券
         * @return        {*}
         */
        // setDefaultSelectedCoupon() {
        //     for (let i = 0; i < this.couponArray.length; i++) {
        //         let item = this.couponArray[i];
        //         if (item.used == 1) {
        //             this.selectedCouponInfo = item;
        //             break;
        //         }
        //     }
        // },
        couponAusageRecord() {
            let params = {
                couponInfo: {},
                switchTab: this.switchTab,
            };
            if (this.selectedCouponInfo.couponNo) {
                params.couponInfo = {
                    couponNo: this.selectedCouponInfo.couponNo,
                    couponTemplateNo: this.selectedCouponInfo.couponTemplateNo,
                    couponType: this.selectedCouponInfo.couponType,
                    couponAmount: this.selectedCouponInfo.couponAmount,
                };
            }
            console.log(params, 'couponAusageRecord===');
            this.$store.commit('setSelectedCoupon', params);
            // #ifdef MP-MPAAS
            this.$cnpcBridge.setValueToNative('Define_Selected_Coupon', encodeURIComponent(JSON.stringify(params)));
            // #endif
            // #ifndef MP-MPAAS
            uni.setStorageSync('setSelectCoupon', params);
            // #endif
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        // 处理不可用电子券
        handleUnCouponList() {
            let list = this.unCouponList.map(item => {
                item.bgColor = false;
                return item;
            });
            this.unCouponList = list;
        },
        handleCouponList() {
            let list = this.availableList.map(item => {
                item.bgColor = true;
                return item;
            });
            let couponList = null;
            // #ifdef MP-MPAAS
            this.$cnpcBridge.getValueToNative('Define_Selected_Coupon', value => {
                if (value) {
                    couponList = JSON.parse(decodeURIComponent(value)) ? JSON.parse(decodeURIComponent(value)) : '';
                    console.log(couponList, '缓存中的获取的电子券数据');
                    this.displayElectronicCoupons(list, couponList);
                } else {
                    this.displayElectronicCoupons(list, couponList);
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            couponList = uni.getStorageSync('setSelectCoupon');
            console.log(JSON.stringify(list), 'listy-----');
            this.displayElectronicCoupons(list, couponList);
            // #endif
        },
        // 回显优惠券
        displayElectronicCoupons(list, couponList) {
            if (couponList?.couponInfo?.couponNo) {
                list.map((item, index) => {
                    if (item.couponNo === couponList.couponInfo.couponNo) {
                        this.selectedCouponInfo = item;
                    }
                });
            }
            this.switchTab = couponList ? couponList.switchTab : true;
            // #ifndef H5-CLOUD
            this.availableList = list;
            // #endif
            // #ifdef H5-CLOUD
            this.couponArray = list;
            // #endif
        },
        // 保存选择结果
        switchTabClick() {
            this.switchTab = !this.switchTab;
            if (this.switchTab) {
                this.selectedCouponInfo = {
                    couponNo: '',
                    couponTemplateNo: '',
                    couponType: '',
                    couponAmount: '',
                };
            }
        },
        /**
         * @description  : 选择一级油品非油的类型
         * @return        {*}
         */
        selectClick(tab, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.selectId === tab.id) return;
            // 将选中的id赋值在页面上高亮展示
            this.selectId = tab.id;
            if (this.selectId) {
                this.couponArray = this.availableList;
            } else {
                this.couponArray = this.unCouponList;
            }
            if (this.couponArray.length) {
                this.showEmpty = true;
            }
        },

        /**
         * @description  : 电子券类型
         * @return        {*}
         */
        getCouponType(val) {
            console.log(val, '电子券类型');
            return val == 10 ? '满减券' : val == 20 ? '计次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
        },
        /**
         * @description  : 区分券类型
         * @return        {*}
         */
        differentiationType(val) {
            console.log(val, '油品非油品------');
            // 券可用品类 1 油品 2 非油品
            return val == 1 ? '现金券' : '优惠券';
        },
        /**
         * @description  : 上拉加载
         * @return        {*}
         */
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getCouponList();
            }
        },

        buttonText(id, item) {
            return item.bgColor ? '立即使用' : '未生效';
        },
        /**
         * @description  : 处理折和元
         * @return        {*}
         */
        faceValueFilter(item) {
            if (item.couponType && item.couponType == '40') {
                return item.discountValue + '<span style="font-size: 12px;">折</span>';
            } else {
                return (
                    '<span class="font-style" style="font-size: 12px">&yen;</span>' + `<span style="font-size: 28px;">${item.amount}</span>`
                );
            }
        },
        /**
         * @description  : 满减券和折扣券说明
         * @return        {*}
         */
        thresholdAmount(item) {
            if (item.useThresholdAmount) {
                if (item.couponType && item.couponType == '40') {
                    return '满' + item.useThresholdAmount + '元可用';
                } else {
                    return '满' + item.useThresholdAmount + '减' + item.couponAmount;
                }
            } else {
                return '无金额门槛';
            }
        },
        /**
         * @description  : 处理时间(10.08与产品确定列表只展示年月日，详情展示时分秒)
         * @return        {*}
         */
        screen(item) {
            let text = '';
            if (item.bgColor) {
                text = '有效期至：' + item.endDate.slice(0, 10);
            } else {
                text = '生效日期：' + item.beginDate.slice(0, 10);
            }
            return text;
        },
    },
};
</script>

<style scoped lang="scss">
.pageMpaas {
    background: #fff;
}

.view {
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;

        .tab_style {
            // margin-right: 22px;
            line-height: 35px;
            width: 50%;
            text-align: center;
        }

        .selected {
            color: #e64f22;
        }
    }

    .type-sort {
        width: 100%;
        height: 44px;

        .sort-item {
            height: 30px;
            line-height: 30px;
            margin-right: 12px;
            padding: 0 6.5px; // 上右下左
        }

        .selectSorted {
            color: #e64f22;
        }
    }

    .reminder {
        width: 100%;
        height: 40px;
        //line-height: 20px;
        //height: 30px;
        //line-height: 30px;
        word-wrap: break-word;
        word-break: normal;
    }

    .coupon-wrap {
        .bg-coupon {
            width: 90%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    width: 30%;

                    .content-left {
                        // margin-top: 25px;
                        width: 100%;

                        img {
                            width: 65px;
                            height: 65px;
                            line-height: 65px;
                            margin-top: 23rpx;
                        }
                    }

                    .content-sx {
                        height: 136rpx;
                        opacity: 0.5;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin-top: 15px;
                    }
                }

                .right-wrap {
                    width: 70%;
                    height: 100%;
                    padding-left: 10px;

                    // padding: 12px 0;
                    .title {
                        // margin-top: 14px;
                        margin-right: 22px;
                        overflow: hidden;
                        // max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        margin-top: 12px;
                    }

                    .title2 {
                        margin-bottom: 12px;
                    }

                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }

                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }

                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }

        // 券未生效
        .toTakeEffect {
            width: 100%;
            height: 100px;
            position: relative;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    width: 30%;

                    .content-left {
                        // margin-top: 25px;
                        width: 100%;

                        img {
                            width: 65px;
                            height: 65px;
                            line-height: 65px;
                            margin-top: 23rpx;
                        }
                    }

                    .content-sx {
                        height: 136rpx;
                        opacity: 0.3;
                        border: 1rpx solid;
                        border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                        margin-right: 8px;
                        margin-top: 15px;
                    }
                }

                .right-wrap {
                    width: 70%;
                    height: 100%;

                    // padding: 12px 0;
                    .title {
                        margin-right: 22px;
                        overflow: hidden;
                        // max-height: 32px;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        // font-family: PingFangSC-Medium, PingFang SC;
                        margin-top: 12px;
                    }

                    .title2 {
                        margin-bottom: 12px;
                    }

                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }

                    .time {
                        margin-top: 5px;
                        font-family: PingFangSC-Regular, PingFang SC;
                    }

                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }
    }

    .btnWrap {
        width: 100%;
        // height: 44px;
        line-height: 44px;
        // padding-bottom: 15px;
        margin: 0 auto;
        position: fixed;
        padding-bottom: env(safe-area-inset-bottom);
        bottom: 0;
        background: #fff;
    }
}

.btn-plain-sure {
    background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    text-align: center;
    color: #fff;
    margin-bottom: 15px;
    margin-top: 10px;
}

.select_style {
    color: #e64f22;
    position: relative;
    display: inline-block;
    // border-bottom: 1px solid #e64f22;
}

.select_style:after {
    background-color: #e64f22;
    /* 高亮状态下横线颜色 */
}

.tab_style:after {
    content: '';
    position: absolute;
    left: 50%;
    /* 将横线居中 */
    transform: translateX(-50%);
    /* 将横线居中 */
    bottom: 0;
    width: 50%;
    /* 控制横线长度的关键属性 */
    height: 2px;
    /* 横线高度 */
}

.img_style {
    width: 42px;
    height: 23px;
}

.tips_style {
    width: 92%;
    height: 44px;
    display: flex;
    margin: 15px 15px 0 15px;
    justify-content: space-between;
    padding: 0 15px;
    align-items: center;
    border: 1px solid #FEE1CB;
    border-radius: 8px;
    background: linear-gradient(90deg, #fff2e7 0%, #ffffff 100%);
}
.tips_div {
    padding: 10px 15px 0px 15px;
    background: linear-gradient(180deg, #fff9f5 0%, #ffffff 100%);
    .title {
        display: flex;
        align-items: center;
        flex-direction: row;
        font-size: 12px;
        color: #333333;
        line-height: 18px;
    }
    .tips {
        font-size: 12px;
        line-height: 18px;
        .dots_div {
            width: 5px;
            height: 5px;
            background: #e6e6e6;
            border-radius: 50%;
            float: left;
            margin: 6px 7px 0px 20px;
        }
    }

    img {
        width: 14px;
        height: 14px;
        margin-right: 5px;
    }
}
.tips_text {
    font-size: 16px;
    color: #333333;
    padding: 5px 0;
}

.select-img-wrap {
    width: 20px;
    height: 20px;
    margin-right: 13.5px;

    img {
        width: 20px;
        height: 20px;
    }
}

.content_div {
    padding-bottom: 100px;
}
</style>
