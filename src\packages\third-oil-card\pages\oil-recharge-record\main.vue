<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view-con p-bf bg-F7F7FB fl-column">
            <zj-navbar :height="44" title="充值记录"></zj-navbar>
            <div class="title bg-FFF7DC font-12 color-333 weight-400">结果只显示本卡三个月内记录，如需更多查询记录，请登录</div>
            <div class="f-1 mh-0 p-LR-16">
                <!-- :emptyImage="require('@/static/trirdImage/kt11ykjl.png')" -->
                <zj-data-list background="#F7F7FB" ref="dataList" emptyImage emptyText="暂未查询到油卡充值记录" :showEmpty="showEmpty"
                    @refreshPullDown="refreshPullDown" @scrolltolower="scrolltolower">
                    <div class="record-wrap">
                        <div class="record-item fl-row bg-fff p-LR-16 fl-al-cen fl-jus-bet p-bf border-rad-8"
                            v-for="(item, index) in recordList" :key="index">
                            <div class="item-left fl-column fl-jus-bet">
                                <div class="font-12 color-999 weight-400">订单号:{{ item.order }}</div>
                                <div class="font-12 color-999 weight-400">充值时间:{{ item.recharge }}</div>
                            </div>
                            <div class="item-right fl-row fl-al-jus-cen">
                                <span class="symbol font-20 weight-500 color-E64F22">+</span>
                                <span class="font-16 weight-500 color-E64F22">
                                    {{ `&yen;${item.money}` }}
                                </span>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
const PAGE_SIZE = 10;
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'rechargeRecord',
    data() {
        return {
            // pageConfig,
            recordList: [
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
                {
                    order: '1231231231',
                    recharge: '14:32',
                    money: '1.00',
                },
            ],
            cardItem: null,
            loadState: 0,
            productList: [],
            page: 1,
            totalPage: 0,
            updateState: 0,
            topState: 0,
            showEmpty: false,
        };
    },
    onLoad(options) {
        // this.cardItem = JSON.parse(options.item)
        // console.log(this.cardItem, '充值记录-------this.cardItem')
        // this.getRechargeRecords()// 获取充值记录列表
    },
    methods: {
        refreshPullDown(e) {
            // 下拉刷新触发
            this.$refs.dataList.loadStatus = 'loading';
            this.getRechargeRecords({ isInit: true });
        },
        stopRefresh(e) {
            // 下拉长按后慢慢放回去 不刷新事件
            this.$refs.dataList.pullingDown = false;
            this.$refs.dataList.pullDownHeight = 0;
        },
        scrolltolower() {
            // 上拉加载事件
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getRechargeRecords();
            }
        },
        async getRechargeRecords({ isInit = false } = {}) {
            return;
            // 获取充值记录列表
            if (isInit) {
                Object.assign(this, {
                    productList: [],
                    page: 1,
                    topState: 0,
                });
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, productList, totalPage } = this;
            let params = {
                cardNo: this.cardItem.cardNo,
                pageNum: page,
                pageSize: PAGE_SIZE,
            };
            this.loadState = 1;
            let res = await getRechargeList(params, { zj_is_load: false });
            this.$refs.dataList.pullDownHeight = 0; // 刷新成功后将下拉获取的高度重新置为0
            this.$refs.dataList.pullingDown = false; // 关闭下拉刷新状态
            if (res?.status === 0) {
                productList = productList.concat(res.data);
                Object.assign(this, {
                    productList,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                console.log(PAGE_SIZE, res.data.length, 'DDDD');
                if (!(PAGE_SIZE < res.data.length)) {
                    this.$refs.dataList.loadStatus = 'nomore'; // 没有更多了
                } else {
                    this.$refs.dataList.loadStatus = 'contentdown'; // 上拉加载更多
                }
                if (productList.length <= 0) {
                    this.showEmpty = true; // 显示暂无数据图片
                }
            } else {
                this.showEmpty = true;
                this.$refs.dataList.loadStatus = 'nomore'; // 没有更多了
            }
        },
    },
    components: {
        // ZjGoodsLoading
    },
};
</script>
<style scoped lang="scss">
.view-con {
    .title {
        width: 100%;
        height: 36px;
        line-height: 36px;
        margin-bottom: 16px;
        padding-left: 16px;
    }

    .record-wrap {
        width: 100%;

        // height: 64.5px;
        .record-item {
            margin-bottom: 12px;
            height: 64.5px;

            .item-left {
                width: 200px;
                height: 37px;
            }

            .item-right {
                .symbol {
                    margin-right: 3px;
                }
            }
        }
    }
}
</style>
