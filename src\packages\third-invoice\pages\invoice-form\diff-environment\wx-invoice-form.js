// #ifdef MP-WEIXIN
import {
    makeRechargeInvoiceAsync,
    getWeixinInvoiceAuthUrl,
    getWeixinInvoiceAuthInfo,
} from '../../../../../s-kit/js/v3-http/https3/invoice/index';
import { messageTmpIds, appId } from '../../../../../../project.config';
export default {
    mounted() {
        this.getMessageSubStatus();
    },
    onShow() {
        this.getAuthorizeStatus();
    },
    methods: {
        cardPackTips() {
            this.$store.dispatch('zjShowModal', {
                title: '目前可同步至微信卡包的地区如下',
                content:
                    '甘肃、上海、湖北、辽宁、吉林、黑龙江、天津、河北、山西、内蒙古、陕西、青海、宁夏、新疆、重庆、四川、贵州、西藏、江苏、江西、山东、河南',
                confirmText: '我知道了',
                cancelText: '',
                confirmColor: '#333',
                maskClosable: true, // 不允许点击遮罩层关闭弹窗
                success: async res => {
                    if (res.confirm) {
                    }
                },
            });
        },
        async getAuthorizeStatus() {
            let openId = uni.getStorageSync('tokenInfo').openId || '';
            let params = {
                out_user_id: `${appId}@${openId}`,
            };
            let res = await getWeixinInvoiceAuthInfo(params);
            if (res && res.success) {
                this.authorizeStatus = res.data.has_auth;
                this.authorizeOpenId = res.data.openid || '';
            }
        },
        async goAuthorize() {
            let openId = uni.getStorageSync('tokenInfo').openId || '';
            let params = {
                source: 'wxa',
                wxa_appid: appId,
                out_user_id: `${appId}@${openId}`,
            };
            let res = await getWeixinInvoiceAuthUrl(params);
            if (res && res.success) {
                let params = {
                    authKey: res.data.auth_key,
                };
                this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-wx-cardpack/main', params);
            }
        },
        subMessagePopupSubmit() {
            this.$refs.subMessagePopup.close();
        },
        getMessageSubStatus() {
            const projectTmpIds = messageTmpIds.split(',') || [];
            wx.getSetting({
                withSubscriptions: true,
                success: res => {
                    if (res.subscriptionsSetting.mainSwitch) {
                        console.log(res.subscriptionsSetting, 'getSetting返回');
                        for (let i = 0; i < projectTmpIds.length; i++) {
                            if (res.subscriptionsSetting.itemSettings[projectTmpIds[i]] == 'reject') {
                                this.$nextTick(() => {
                                    setTimeout(() => {
                                        this.$refs.subMessagePopup.open();
                                    }, 300);
                                });
                                break;
                            }
                        }
                    } else {
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.$refs.subMessagePopup.open();
                            }, 300);
                        });
                    }
                },
                fail: res => {},
            });
        },
        /**
         * @description  : 2.0开票接口
         * @return        {*}
         */
        async invoiceMake2() {
            let params = {
                orderId: this.formData.orderNoList[0],
                cardNo: this.formData.cardNo,
                openingBank: this.titleDetail.buyerFinancial || '',
                bankAccount: this.titleDetail.buyerAccount || '',
                invoiceTittle: this.titleDetail.buyerName,
                taxCode: this.titleDetail.buyerTaxId || '',
                addressTax: this.titleDetail.buyerAddr || '',
                telephone: this.titleDetail.buyerTel || '',
            };
            if (this.titleDetail.buyerNature == 3) {
                if (this.titleDetail.buyerTaxId) {
                    params.enterpriseLogo = 1;
                } else {
                    params.enterpriseLogo = 2;
                }
            } else if (this.titleDetail.buyerNature == 4) {
                params.enterpriseLogo = 2;
            } else {
                params.enterpriseLogo = this.titleDetail.buyerNature;
            }
            let res = await makeRechargeInvoiceAsync(params);
            if (res.status == 0) {
                this.closeSubmitPopup();
                this.isSuccess = true;
            }
        },
        /**
         * @description  : 实人认证弹窗的确认事件
         * @param         {String} name -姓名
         * @param         {String} idNumber -身份证号
         * @param         {Boolean} realNameDialogFlag -打开获关闭弹窗
         * @return        {*}
         */
        realNameInfo(val) {
            this.realNameDialogFlag = false;
            this.$sKit.wxRealPersonAuthentication.startVerification({ name: val.name, idNo: val.idNumber, type: '11' }).then(response => {
                if (response.success) {
                    this.invoiceMake(true);
                } else {
                }
            });
            // 打开人脸认证协议弹窗
        },
    },
};
// #endif
