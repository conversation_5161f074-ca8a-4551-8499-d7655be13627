<template>
    <div class="view">
        <div class="top-div">
            <div class="navTop">
                <img class="navTopbg" src="@/static/oil-station-bg.png" alt />
            </div>
            <div class="top-img-div" :style="{ height: head_up_h }"></div>
            <div class="backImg-div" @click="go_back" :style="{ height: capsule_h, top: head_up_h }">
                <img class="backImg" src="@/static/back.png" alt />
            </div>
        </div>
        <div class="oilstation-info">
            <div class="station-top flex justify-between">
                <div class="stop-left">
                    <div class="sta-left">{{ selectMarker.stationName }}</div>
                    <div class="station-mid flex align-center">
                        <img src="@/static/homeIcon/location-fill.png" alt />
                        <div class="station-juli">{{ selectMarker.address }}</div>
                    </div>
                    <div class="station-time">
                        <span v-for="(item, index) in selectMarker.services" :key="index">{{ item }}</span>
                    </div>
                </div>
                <div class="stop-right" @click="clickNaviStateion">
                    <img src="@/static/txmap.png" alt />
                    <div>{{ selectMarker.distance }}km</div>
                </div>
            </div>
            <div class="station-bot">
                <div class="yh-div">
                    <div class="yh-title">选择油号</div>
                    <div class="select-list-view">
                        <div
                            :class="olisValue == item.oliCode ? 'yp-item ypItemSelect' : 'yp-item'"
                            v-for="(item, index) in multiSelector"
                            :key="index"
                            @click="clickPopYPItem(index, item)"
                            >{{ item.oilName + '#' }}</div
                        >
                    </div>
                </div>
                <div class="qh-div">
                    <div class="qh-top flex align-center justify-between">
                        <div class="qh-title">选择油枪</div>
                        <div class="oilman">请向加油员确认枪号</div>
                    </div>
                    <div class="select-list-view">
                        <div
                            @click="clickSelectYqCode(index, item)"
                            :class="selectYqCodeIndex == index ? 'yp-item ypItemSelect' : 'yp-item'"
                            v-for="(item, index) in selectYqCodeArr"
                            :key="index"
                            >{{ item | setyqItem }}</div
                        >
                        <input
                            v-if="showOthers"
                            @click="selectQtQq"
                            :class="otherYqCodeIndex == 1 ? 'ypItemSelectOther' : 'yp-item'"
                            class="yqinput"
                            placeholder="其他"
                            maxlength="2"
                            v-model.trim="otherYqCode"
                            @input="oilotherYqInput"
                            :placeholder-style="
                                otherYqCodeIndex == 1
                                    ? 'color:#fff;background-color: #F96702;font-size:15px;'
                                    : 'color:#F96702;background-color: #FFF8F3;font-size:15px;'
                            "
                            type="number"
                        />
                    </div>
                </div>

                <div class="btn-view" @click="queryOrder">
                    <div class="btn-fast-fuel">查询订单</div>
                </div>
            </div>
        </div>
        <div>
            <div class="horn-text-wrap">
                <img class="horn-img" src="@/static/horn.png" alt />
                <div class="horn-text">温馨提示：</div>
            </div>

            <div class="oil-prompt">· 电子发票的销售方名称与您的昆仑加油卡归属地或交易消费加油站主体一致；</div>
        </div>
        <!-- 加载动画 -->
        <uni-pop ref="load" type="center" :maskClick="false" @change="uniPopChange">
            <!-- <div class='load-img' :style='"background-image: url(" + loadImage + "); width: 100px; height: 100px;"' mode=""></div> -->
            <img src="@/static/load.gif" style="width: 300px; height: 128px" mode />
        </uni-pop>
        <ZjNewStation class="new_station" @close="closeNewStationDialog" @submit="submitUpgrade" v-if="newStationFlag"></ZjNewStation>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import UniPopup from '../../../../components/uni-popup/uni-popup';
let timer = null;
import { mapState } from 'vuex';
import uniPop from '@/components/uni-popup/uni-popup.vue';
import { getTradeList, getOilGunsByStationCode, preOrder, lockOrder, waitPayOrder, userMigration, getUserMigrateFlag } from '@/api/home.js';
import { getVehicleList } from '@/api/anEcardToOpenCard.js';
import pageConfig from '@/utils/pageConfig.js';
import couponList from '@/components/coupon-list/coupon-list.vue';
import Config from '@/utils/config.js';
import ZjNewStation from '@/components/zj-new-station/index.vue';

const TEST_DATA = {
    stationCode: 'KKKK',
    province: '北京市',
};

export default {
    components: {
        UniPopup,
        couponList,
        uniPop,
        ZjNewStation,
    },
    data() {
        return {
            isShowQT: false,
            showOthers: false,
            head_up_h: uni.getMenuButtonBoundingClientRect().top + 'px',
            capsule_h: uni.getMenuButtonBoundingClientRect().height + 'px',
            /**********上方为假数据***********/
            isStartDown: false, // 是否开始刷新
            otherYqCode: '',
            yqArr: [
                {
                    amount: 0,
                },
            ], //油枪数据
            pageConfig: pageConfig, // 页面配置
            isShowSelectYP: false, // 是否展示油品弹窗
            ypArr: [], // 油品类型
            selectYPIndex: -1, // 选中的常用油品index
            selectYQIndex: -1, // 选中的油枪index
            payPrice: 0, // 最终价格
            isMore: false, // 是否显示展示更多
            showYQArr: [], // 油枪数据
            carData: {}, // 常用车辆
            yqCodeArr: [], // 油枪（所有油品对应的油枪）
            selectYqCodeArr: [], // 要展示的油枪数组
            selectYqCodeIndex: 0, // 油枪编号选中的index
            otherYqCodeIndex: '', //其他搜索油枪的index
            navH: 64,
            isIphoneX: false,
            // loadImage: '/static/net-loading.png',
            isFirstShow: false,
            isTips: true,
            downNum: 0, // 倒计时时间
            oilstationStatus: true, //是否查询到油枪，油品信息
            time: true,
            cacheHasGunIndex: '', // 油枪缓存
            oilCacheIndex: '', // 油号缓存
            oilGunOilArray: [], //  油品油枪信息
            station: {}, // 油站信息
            multiSelector: [], // 返回的油品数组
            productNoList: [],
            olisValue: '', // 油品对应的value值，作缓存使用
            olisValueLabel: '',
            noSameCode: true,
            newMemberFlag: false,
            Timer: null,
        };
    },
    onUnload() {
        uni.$off('confirmBack');
        if (timer) {
            clearInterval(timer);
        }
        if (this.Timer) {
            clearInterval(this.Timer);
        }
    },
    async onLoad(options) {
        clearInterval(this.Timer);
        this.station = JSON.parse(options.station);
        let systemInfo = uni.getSystemInfoSync();
        // systemInfo = {}
        this.navH = 44 + systemInfo.statusBarHeight;
        let name = 'iPhone X';
        if (systemInfo.model.indexOf(name) > -1) {
            this.isIphoneX = true;
        }
        // let strData = JSON.parse(decodeURIComponent(this.$mp.query.venicledata))
        // this.carData = strData
        // let res = await waitPayOrder()
        // @TODO 上线的时候需要f放开
        let ypRes = await getOilGunsByStationCode({
            stationCode: Config.testEnvironment ? TEST_DATA.stationCode : this.selectMarker.stationCode,
            // province: Config.testEnvironment ? TEST_DATA.province : '北京市'
        });
        if (ypRes.data.length === 0) {
            this.oilstationStatus = false;
            this.$util.showModal('未查询到油站相关信息', true);
            return;
        }

        if (!ypRes) {
            return this.$refs.load.close();
        }
        this.multiSelector = ypRes.data;
        let ypArr = [];
        ypRes.data.map(item => {
            ypArr.push(isNaN(Number(item.oilName)) ? item.oilName : item.oilName + '#');
        });
        let data = {};
        ypRes.data.forEach(item => {
            data[item.oilName] = item.oilGunList;
        });
        this.ypArr = ypArr;
        this.yqCodeArr = data;
        console.log(this.yqCodeArr, '油枪数组');
        this.getCarList(); // 获取车辆列表
        // 确认订单返回监听
        uni.$on('confirmBack', async params => {
            if (params) {
                const { downNum, payMode } = params;
                this.payMode = payMode;
                if (downNum) {
                    this.downNum = downNum;
                    timer = setInterval(async () => {
                        this.downNum--;
                        if (this.downNum == 0) {
                            clearInterval(timer);
                            this.$refs.load.open();
                            await this.getyqData();
                            this.$refs.load.close();
                            this.setYQArr();
                        }
                    }, 1000);
                }
            } else {
                this.downNum = 0;
                this.$refs.load.open();
                await this.getyqData();
                this.$refs.load.close();
                this.setYQArr();
            }
        });
        this.queryOrder = this.$util.throttleUtil(this.queryOrder);
        this.submitUpgrade = this.$util.throttleUtil(this.submitUpgrade);
        console.log(this.markerArr, 'markerArr');
    },
    filters: {
        setyqItem(item) {
            return item + '号';
        },
    },
    computed: {
        ...mapState({
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
            markerArr: state => state.location.markerArr, // marker数据
            province: state => state.location.province, // 选中的省份
            newStationFlag: state => state.location.newStationFlag, // 打开升级弹窗
        }),
    },
    watch: {},
    methods: {
        uniPopChange(params) {
            this.isShowQT = !params.show;
        },
        // 点击导航
        clickNaviStateion() {
            if (this.selectMarker.openState != 1) {
                return uni.showModal({
                    title: '提示',
                    content: `该油站暂未开通线上加油功能，是否继续前往？`,
                    cancelText: '更换油站',
                    confirmText: '去导航',
                    success: res => {
                        if (res.confirm) {
                            uni.openLocation({
                                latitude: Number(this.selectMarker.latitude),
                                longitude: Number(this.selectMarker.longitude),
                                name: this.selectMarker.stationName,
                                address: this.selectMarker.address,
                            });
                        }
                    },
                });
            } else {
                uni.openLocation({
                    latitude: Number(this.selectMarker.latitude),
                    longitude: Number(this.selectMarker.longitude),
                    name: this.selectMarker.stationName,
                    address: this.selectMarker.address,
                });
            }
        },
        go_back() {
            uni.navigateBack({
                delta: 1,
            });
        },
        // onload 网络请求
        async onLoadNetwork() {
            // 获取加载动画背景图
            // let base64 = uni.getFileSystemManager().readFileSync(this.loadImage, 'base64');
            // this.loadImage = 'data:image/png;base64,' + base64
            this.$refs.load.open();
            // let ypRes = await getOilGunsByStationCode({
            // 	stationCode: this.selectMarker.stationCode,
            // 	province: this.province
            // })
            await this.getyqData();
            this.$refs.load.close();
            this.setYQArr();
        },
        // 更换油站
        clickReplaceStation() {
            uni.navigateTo({
                url: '/packages/location/pages/home/<USER>',
            });
        },
        // 倒计时结束事件
        downEnd() {
            this.isStartDown = false;
        },
        // 下拉刷新
        async onRefreash() {
            this.isStartDown = true;
            this.isTips = false;
            this.isStartDown = false;
            this.isTips = true;
        },
        // 油枪选择点击事件
        async clickSelectYqCode(index, item) {
            this.otherYqCode = '';
            this.otherYqCodeIndex = '';
            this.selectYqCodeIndex = index;
            this.isShowSelectYP = false;
        },
        //选择其他输入油枪号搜索订单（切换样式）
        selectQtQq() {
            // this.otherYqCode
            this.selectYqCodeIndex = -1;
            this.otherYqCodeIndex = 1;
        },
        //点击按钮查询订单
        async queryOrder() {
            this.$store.dispatch('getNewOilStation', {
                callBack: () => {
                    this.getOrderCallBack();
                },
                params: this.markerArr,
            });
        },
        //点击按钮查询订单
        async getOrderCallBack() {
            console.log(this.olisValueLabel, 'this.olisValueLabel');
            // 判断当前获取油站是否为新站的接口是不是调用失败 this.isStationNewObj = {} 调用失败
            // if (this.isStationNewObj.onlineType == 1 && JSON.stringify(this.isStationNewObj) !== '{}' && Number(this.selectMarker.distance) < 500) {
            //   this.$store.commit('setCloseDialog', true)
            //   return
            // }

            if (this.selectMarker.distance > 1) {
                this.$util.showModal('距离加油站超过1公里，请到达加油站内选择油枪', true);
                return;
            }
            if (this.selectYPIndex == -1 && this.olisValueLabel == '') {
                console.log(this.olisValueLabel, 'this.olisValueLabel ');
                this.$util.showModal('请选择油号', true);
                return;
            }
            if (!this.oilstationStatus) {
                this.$util.showModal('未查询到油站相关信息', true);
                return;
            }
            if (this.otherYqCodeIndex == 1 && this.otherYqCode == '') {
                this.$util.showModal('请输入油枪号', true);
                return;
            }
            this.$refs.load.open();
            await this.getyqData(); //  查询当前油枪下的订单数据
            if (this.yqArr.length == 0) {
                this.$util.showModal('未查询到订单', true);

                this.$refs.load.close();
                return;
            }
            this.$refs.load.close();
            this.setYQArr();
            if (this.selectYQIndex == -1) return;
            if (timer) {
                clearInterval(timer);
            }
            let params = {
                stationCode: Config.testEnvironment ? TEST_DATA.stationCode : this.selectMarker.stationCode,
                province: Config.testEnvironment ? TEST_DATA.province : this.province,
                oil: this.olisValueLabel, // 92 95  98
                oilGun: this.otherYqCode ? this.otherYqCode : this.selectYqCodeArr[this.selectYqCodeIndex], //    2 3 4
            };
            // oilGun 油枪号  productNo 油品号  oilGunOilArray 油枪油品数组
            this.oilGunOilArray = wx.getStorageSync('ypIndex');
            this.oilGunOilArray === '' ? (this.oilGunOilArray = []) : this.oilGunOilArray;
            let index = this.oilGunOilArray.findIndex(item => item.stationCode === this.station.stationCode);
            if (index !== -1) {
                this.oilGunOilArray.splice(index, 1);
            }
            this.oilGunOilArray.unshift({
                productNoLabel: this.olisValueLabel,
                productNoValue: this.olisValue,
                stationCode: this.station.stationCode,
            });
            if (this.oilGunOilArray.length === 4) {
                this.oilGunOilArray.pop();
            }
            wx.setStorageSync('ypIndex', this.oilGunOilArray);
            if (this.yqArr.length > 0) {
                uni.navigateTo({
                    url:
                        '/packages/place-order/pages/confirm-order/main?orderdata=' +
                        JSON.stringify(this.yqArr) +
                        '&ypCode=' +
                        this.olisValueLabel +
                        '&stationName=' +
                        this.selectMarker.stationName +
                        '&carCode=' +
                        this.carData.carNo +
                        '&stationInfo=' +
                        JSON.stringify(params),
                });
            }
        },
        //处理其他按钮输入数据的格式
        oilotherYqInput(e) {
            let val = e.detail.value;
            // this.otherYqCode = e.detail.value
            let newVal = val.substring(0, 1);
            let newValTwo = e.detail.value.substring(1, 2);
            if (val == 0 || val == '') {
                this.otherYqCode = '';
            }
            if (newVal == 0) {
                if (newValTwo != '') {
                    //有第二位
                    this.otherYqCode = newValTwo;
                } else if (newValTwo == 0) {
                    this.otherYqCode = '';
                } else {
                    this.otherYqCode = newValTwo;
                }
            } else {
                this.otherYqCode = e.detail.value;
            }
        },
        // 获取油枪数据
        async getyqData() {
            // @TODO 上线的时候需要f放开
            // let res = await getTradeList({
            // 	stationCode: this.selectMarker.stationCode,
            // 	province: this.province,
            // 	oil: this.ypArr[this.selectYPIndex].replace('#', ''),
            // 	oilGun: this.selectYqCodeArr[this.selectYqCodeIndex],
            // })
            let res = await getTradeList({
                stationCode: Config.testEnvironment ? TEST_DATA.stationCode : this.selectMarker.stationCode,
                province: Config.testEnvironment ? TEST_DATA.province : this.province,
                oil: this.olisValueLabel,
                oilGun: this.otherYqCode ? this.otherYqCode : this.selectYqCodeArr[this.selectYqCodeIndex],
            });

            this.yqArr = res ? res.data : [];
            // this.otherYqCode = ''
            if (this.yqArr.length == 0) {
                this.selectYQIndex = -1;
            } else {
                this.selectYQIndex = 0;
            }
        },
        // 判断是否是整数
        isInteger(obj) {
            if (obj === undefined) {
                return;
            }
            return Number.isInteger(obj);
        },
        // 设置油枪按钮
        setYQArr() {
            if (this.yqArr == '') {
                this.yqArr = [];
                this.showYQArr = this.yqArr;
            } else {
                if (this.yqArr.length > 3) {
                    this.isMore = true;
                    this.showYQArr = [this.yqArr[0], this.yqArr[1], this.yqArr[2]];
                } else {
                    this.showYQArr = this.yqArr;
                }
            }
            if (this.selectYQIndex == -1) return;
            this.setPrice();
        },
        // 设置金额方法
        setPrice() {
            this.payPrice = this.yqArr[this.selectYQIndex].amount;
        },
        // 油品选择点击事件
        async clickPopYPItem(index, item) {
            this.multiSelector.find(element => {
                if (element.oliCode === item.oliCode) {
                    return (this.olisValueLabel = element.oilName), (this.showOthers = true), (this.olisValue = element.oliCode);
                }
            });
            this.selectYPIndex = index;
            this.otherYqCode = '';
            this.otherYqCodeIndex = '';
            this.selectYqCodeIndex = -1;
            this.selectYqCodeArr = this.yqCodeArr[this.olisValueLabel];
            this.isShowSelectYP = false;
        },
        // 弹起油品弹窗
        clickSelectYP(index) {
            this.isShowSelectYP = true;
        },
        // 关闭油品弹窗事件
        clickCloseYPPop() {
            this.isShowSelectYP = false;
        },
        // 有枪选择点击事件
        clickYQItem(index) {
            this.selectYQIndex = index;
            this.setPrice();
        },
        // 显示更多点击事件
        clickLookMore() {
            this.isMore = false;
            this.showYQArr = this.yqArr;
        },
        // 继续支付
        async clickContinuePay() {
            let res = await waitPayOrder();
            if (timer) {
                clearInterval(timer);
            }
            uni.navigateTo({
                url:
                    '/packages/place-order/pages/await-pay/main?data=' +
                    encodeURIComponent(JSON.stringify(res.data)) +
                    '&payMode=' +
                    this.payMode,
            });
        },
        // 获取车辆列表
        getCarList() {
            let params = {
                token: this.$store.state.token,
            };
            getVehicleList(params).then(res => {
                if (res.status === 0) {
                    this.vehicleList = res.data;
                    // 读取油枪油品油站数组
                    var getOilGunOilArray = wx.getStorageSync('ypIndex') || [];
                    // 首先判断车牌列表中是否有默认车辆 isDefault
                    let isDefaultIndex = this.vehicleList.findIndex(item => item.isDefault === 1);
                    console.log(isDefaultIndex, 'isDefaultIndexisDefaultIndexisDefaultIndexisDefaultIndex');
                    console.log(isDefaultIndex, 'isDefaultIndexisDefaultIndexisDefaultIndex');
                    if (isDefaultIndex !== -1) {
                        this.multiSelector.find((item, index) => {
                            if (item.oliCode == this.vehicleList[isDefaultIndex].oilNo) {
                                this.olisValueLabel = item.oilName;
                                this.olisValue = item.oliCode;
                                this.selectYqCodeArr = this.yqCodeArr[item.oilName];
                                this.selectYqCodeIndex = -1;
                                this.showOthers = true;
                            }
                        });
                        return;
                    } else {
                        if (this.vehicleList.length > 0) {
                            // 判断车牌列表是否有值
                            this.multiSelector.find(item => {
                                // 判断当前车牌列表中的第0项中的code在油品数组中是否存在
                                if (item.oliCode == this.vehicleList[0].oilNo) {
                                    this.olisValueLabel = item.oilName;
                                    this.noSameCode = false;
                                    this.olisValue = item.oliCode;
                                    this.selectYqCodeArr = this.yqCodeArr[item.oilName];
                                    this.selectYqCodeIndex = -1;
                                    this.showOthers = true;
                                }
                                return;
                            });
                        }
                    }
                    // 如果车辆列表中不存在与油品数组中相同的code 那就获取缓存中的油品信息
                    if (getOilGunOilArray.length > 0 && this.noSameCode) {
                        // 判断是否存在缓存
                        let cacheIndex = this.multiSelector.findIndex(item => item.oliCode === getOilGunOilArray[0].productNoValue);
                        if (cacheIndex !== -1) {
                            this.multiSelector.map(item => {
                                if (item.oliCode === getOilGunOilArray[0].productNoValue) {
                                    this.olisValueLabel = item.oilName;
                                    this.olisValue = item.oliCode;
                                    this.selectYqCodeArr = this.yqCodeArr[item.oilName];
                                    this.selectYqCodeIndex = -1;
                                    this.showOthers = true;
                                }
                            });
                        } else {
                            // 如果维护的车辆的油品在当前油站中不存在  并且  油站缓存中也不存在相同的油品的code
                            this.olisValue = '';
                            this.showOthers = false;
                        }
                    }
                    if (getOilGunOilArray.length == 0 && this.olisValue == '') {
                        // 三元判断 获得当前的下标
                        this.olisValueLabel = '';
                        this.showOthers = false;
                        return;
                    }
                }
            });
        },
        // 关闭升级新站弹窗
        closeNewStationDialog() {
            this.$store.commit('setCloseDialog', false);
        },
        // 立即升级
        async submitUpgrade() {
            this.$store.dispatch('getToken3', 'upgrade');
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
.flex {
    display: flex;
}
.align-center {
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.flex-1 {
    flex: 1;
}
.view {
    width: 100%;
    min-height: 100vh;
    background-color: #f5f5f5;
    .top-div {
        position: relative;
        .navTop {
            height: 520rpx;
            .navTopbg {
                display: block;
                width: 100%;
                height: 520rpx;
            }
        }
        .top-img-div {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .backImg-div {
            position: absolute;
            left: 0;
            width: 100%;
            .backImg {
                width: 40rpx;
                height: 45rpx;
                margin: 8rpx 0 0 20rpx;
            }
        }
    }
    .oilstation-info {
        margin: 0 20rpx;
        margin-top: -145rpx;
        position: relative;
        .station-top {
            background-color: #fff;
            border-radius: 20rpx;
            padding: 20rpx;
            .stop-left {
                font-size: 28rpx;
                color: #999;
                font-family: PingFangSC-Regular;
                .sta-left {
                    color: #333;
                    font-size: 36rpx;
                    font-weight: 700;
                }
                .station-mid {
                    margin-top: 10rpx;
                    img {
                        width: 30rpx;
                        height: 30rpx;
                    }
                    .station-juli {
                        margin-left: 10rpx;
                    }
                    .station-address {
                        margin-left: 10rpx;
                        max-width: 470rpx;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
                .station-time {
                    background-color: #fff;
                    padding: 12rpx 0;
                    font-size: 20rpx;
                    span {
                        border: 1px solid #999;
                        border-radius: 5rpx;
                        padding: 0 5rpx;
                        margin-right: 10rpx;
                    }
                }
                .activity {
                    margin-top: 10rpx;
                    img {
                        width: 35rpx;
                        height: 35rpx;
                    }
                    div {
                        margin-left: 10rpx;
                        color: #333;
                        font-family: PingFangSC-Regular;
                    }
                }
            }
            .stop-right {
                font-size: 24rpx;
                color: #999;
                font-family: PingFangSC-Regular;
                text-align: center;
                img {
                    width: 70rpx;
                    height: 70rpx;
                    border-radius: 10rpx;
                    margin-left: 50%;
                    transform: translateX(-50%);
                }
                div {
                    margin-top: 10rpx;
                }
            }
        }
        .station-bot {
            background-color: #fff;
            border-radius: 20rpx;
            padding: 20rpx;
            margin-top: 20rpx;
            .yh-div {
                .yh-title {
                    padding: 10rpx 0;
                    color: #333;
                    font-size: 32rpx;
                    font-weight: 700;
                    font-family: PingFangSC-Regular;
                }
                .select-list-view {
                    display: flex;
                    flex-wrap: wrap;
                    .yp-item {
                        box-sizing: border-box;
                        margin-top: 5px;
                        width: calc((100vw - 70px) / 4);
                        margin-left: 6px;
                        background-color: #fff8f3;
                        text-align: center;
                        border-radius: 5px;
                        height: 46px;
                        color: $btn-color;
                        font-size: 15px;
                        line-height: 46px;
                    }

                    .ypItemSelect {
                        background-color: #f96702;
                        color: #fff;
                    }
                    .ypItemSelectOther {
                        background-color: #f96702;
                        color: $btn-color;
                        box-sizing: border-box;
                        margin-top: 5px;
                        width: calc((100vw - 70px) / 4);
                        margin-left: 6px;
                        text-align: center;
                        border-radius: 5px;
                        font-size: 15px;
                    }
                    .yqinput {
                        height: 46px;
                        line-height: 46px;
                        color: #fff;
                    }
                }
            }
            .qh-div {
                margin-top: 20rpx;
                .qh-top {
                    padding: 10rpx 0;
                    .qh-title {
                        color: #333;
                        font-size: 32rpx;
                        font-weight: 700;
                        font-family: PingFangSC-Regular;
                    }
                    .oilman {
                        font-size: 26rpx;
                        color: #333;
                        font-family: PingFangSC-Regular;
                    }
                }
                .select-list-view {
                    display: flex;
                    flex-wrap: wrap;
                    .yp-item {
                        box-sizing: border-box;
                        margin-top: 5px;
                        width: calc((100vw - 70px) / 4);
                        margin-left: 6px;
                        background-color: #fff8f3;
                        text-align: center;
                        border-radius: 5px;
                        height: 46px;
                        color: $btn-color;
                        font-size: 15px;
                        line-height: 46px;
                    }

                    .ypItemSelect {
                        background-color: #f96702;
                        color: #fff;
                    }
                    .ypItemSelectOther {
                        background-color: #f96702;
                        color: $btn-color;
                        box-sizing: border-box;
                        margin-top: 5px;
                        width: calc((100vw - 70px) / 4);
                        margin-left: 6px;
                        text-align: center;
                        border-radius: 5px;
                        font-size: 15px;
                    }
                    .yqinput {
                        height: 46px;
                        line-height: 46px;
                        color: #fff;
                    }
                }
            }
            .btn-view {
                margin-top: 30rpx;
                padding: 10rpx 0;
                .btn-fast-fuel {
                    background-color: #f96702;
                    color: #ffffff;
                    border-radius: 5px;
                    height: 45px;
                    line-height: 45px;
                    text-align: center;
                    font-family: PingFangSC-Regular;
                    font-size: 36rpx;
                }
            }
        }
    }
    .horn-text-wrap {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
        margin-top: 10px;
        .horn-text {
            font-size: 28rpx;
            color: #ef8934;
        }
        .horn-img {
            width: 18px;
            height: 16px;
            margin-right: 5px;
        }
    }

    .oil-prompt {
        font-size: 24rpx;
        color: #999999;
        padding-left: 15px;
        padding-right: 15px;
        margin-top: 5px;
    }
}
.load-img {
    background-size: 1200px 100px;
    animation: load 0.5s steps(1, end);
    animation-iteration-count: infinite;
}
@keyframes load {
    0% {
        background-position: -1200px 0;
    }
    8.33% {
        background-position: -100px 0;
    }
    16.66% {
        background-position: -200px 0;
    }
    24.99% {
        background-position: -300px 0;
    }
    33.33% {
        background-position: -400px 0;
    }
    41.66% {
        background-position: -500px 0;
    }
    49.99% {
        background-position: -600px 0;
    }
    58.33% {
        background-position: -700px 0;
    }
    66.66% {
        background-position: -800px 0;
    }
    74.99% {
        background-position: -900px 0;
    }
    83.33% {
        background-position: -1000px 0;
    }
    91.66% {
        background-position: -1100px 0;
    }
    100% {
        background-position: -1200px 0;
    }
}
</style>
