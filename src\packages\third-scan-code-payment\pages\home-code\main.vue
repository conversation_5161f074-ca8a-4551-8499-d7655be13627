
<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <div class="pageMpaas">
        <div class="view">
            <ThirdOil
                v-if="pageLoaded"
                ref="oilCharge"
                :myRefer="myRefer"
                :noInitLocation="noInitLocation"
                :pageChanged="pageChanged"
                :refer="refer"
                :stationCode="stationCode"
                :tabType="tabType"
                @changePageChange="changePageChange"
            ></ThirdOil>
            <keyboard-plugin v-if="isHarmony"></keyboard-plugin>
            <zj-show-modal></zj-show-modal>
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="safeKeyboardIdHome" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="safeKeyboardIdHome" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <div v-if="isHarmony">
                <account-plugin ref="accountPlugin"></account-plugin>
            </div>
        </div>
    </div>
</template>

<script>
import ThirdOil from '../../../../s-kit/first/third-oil/main.vue';
import Vue from 'vue';
import { mapState } from 'vuex';
import { clientCode } from '../../../../../project.config';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: '',
    components: {
        ThirdOil,
    },
    data() {
        return {
            tabType: '',
            bgColor: 'background-image:linear-gradient(180deg, #DA4AF6 0%, #7A42F3 100%);',
            stationCode: '',
            noInitLocation: false,
            refer: '',
            myRefer: '',
            pageChanged: false,
            pageLoaded: false,
        };
    },
    async onLoad(options) {
        if (options.data) {
            // 个人中心路由跳转携带过来的参数
            let personalObj = JSON.parse(decodeURIComponent(options.data));
            if (personalObj.tabType) {
                // 传递给ThirdOil组件
                this.tabType = personalObj.tabType;
            }
            if (personalObj.stationCode) {
                // 传递给ThirdOil组件
                this.stationCode = personalObj.stationCode;
            }
            if (personalObj.noInitLocation) {
                this.noInitLocation = personalObj.noInitLocation;
            }
            if (personalObj.refer) {
                this.refer = personalObj.refer;
            }
            if (personalObj.myRefer) {
                this.myRefer = personalObj.myRefer;
                if (this.tabType == 'code') {
                    this.$sKit.mpBP.tracker('我的页面', {
                        seed: 'minePageBiz',
                        pageID: 'codePage',
                        refer: this.myRefer,
                        channelID: clientCode,
                    });
                }
            }
        }
        this.pageLoaded = true;
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$sKit.mpaasPayPlugin?.init();
            this.$nextTick(async () => {
                let result = await this.$sKit.keyBordPlugin.initRef();
                this.$store.commit('setAccountDataPlugin', result);
            });
        }
        // #endif
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        this.$nextTick(async () => {
            const result = this.$refs['handlePasswordKeyboardRef'];
            this.$sKit.keyBordPlugin.initRef(result);
            console.log('handlePasswordKeyboardRef-result', result);
            this.$store.commit('setAccountDataPlugin', result);
        });
        // #endif
        // #ifdef MP-WEIXIN
        this.$sKit.wxPayPlugin?.initPayPlugin();
        let result = await this.selectComponent('#safeKeyboardIdHome');
        this.$store.commit('setAccountDataPlugin', result);
        if (this.officialAccountParams == 'hym') {
            this.tabType = 'code';
            return;
        }
        // #endif
    },
    onShow() {
        this.notPaidListGet();
    },
    mounted() {
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$nextTick(async () => {
                const accountPluginRef = this.$refs.accountPlugin;
                this.$sKit.accountPlugin.initRef(accountPluginRef);
            });
        }
        // #endif
    },
    methods: {
        changePageChange(pageChanged) {
            this.pageChanged = pageChanged;
        },
        resizeCallback(e, changed) {
            console.log('this.$refs.oilCharge', this.$refs.oilCharge);
            // if (this.footerTabbar[0].pageLoad) {
            this.$refs.oilCharge.isShowMap = false;
            // this.$refs.oilCharge[0].isShowMapContent = false;
            this.$refs.oilCharge.setMapHeight();
            // }
            this.pageChanged = changed;
        },
        //
        notPaidListGet() {
            setTimeout(() => {
                this.$refs.oilCharge.chargenotPaidListOrder();
            }, 100);
        },
        // 关闭小程序
        closeEvent() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
    },
    computed: {
        ...mapState({
            // #ifdef MP-WEIXIN
            officialAccountParams: state => state.location.officialAccountParams, // 存储外部跳转进来携带的参数和小程序跳转过来的参数
            // #endif
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    beforeDestroy() {},
};
</script>

<style scoped lang="scss">
.view {
    width: 100%;
    height: 100%;
}
</style>
