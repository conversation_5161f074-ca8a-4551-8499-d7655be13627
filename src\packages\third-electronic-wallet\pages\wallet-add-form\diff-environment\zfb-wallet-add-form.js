import { userAgreement, getAlipayUserInfo } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
    // #ifdef MP-ALIPAY
    mounted() {},
    methods: {
        getUserNameId() {
            my.getAuthCode({
                scopes: ['auth_user'], // 静默授权
                success: async res => {
                    this.zfbUserInfoEcho = true;
                    let params = {
                        authToken: res.authCode,
                    };
                    let postRes = await getAlipayUserInfo(params);
                    if (postRes && postRes.success) {
                        this.current.name = postRes.data.userName;
                        if (postRes.data.certType == 0) {
                            const reg = /^(.{4})(?:\d+)(.{2})$/; // 匹配身份证号前4位和后2位的正则表达式
                            this.zfbEchoIdNo = postRes.data.certNo.replace(reg, '$1********$2');
                            this.current.idNo = postRes.data.certNo;
                        }
                    }
                },
                fail: err => {
                    this.zfbUserInfoEcho = false;
                },
            });
        },
        /**
         * @description  : 选择加油站
         * @param         {Object} params -路由跳转参数对象
         * @param         {String} type -路由跳转参数,用来后续功能限制
         * @return        {*}
         */
        async selectStation() {
            let url = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {
                type: 'ekl', //昆仑e享卡
            };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 点击查看协议
         * @param         {string} type - 协议类型
         * @param         {string} cityName - 城市编码
         * @param         {string} name - 协议名称
         * @return        {*}
         */
        async getAgreeOn() {
            //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
            // name传
            /*
                App用户使用协议
                App隐私协议
                电子钱包开通协议
                App充值协议
            */
            let params = {
                type: '1',
                cityName: '全国',
                name: '昆仑e享卡开通协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: userAgreementRes.message || '未找到该协议' });
            }
        },
    },
    // #endif
};
