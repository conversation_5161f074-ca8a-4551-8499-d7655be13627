
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
  // #ifdef H5-CLOUD
  methods: {
    /**
     * @description : 点击打开协议
     * @returns {Promise<void>}
     */
    clickXieyi(values) {
      if (values == 17) {
        uni.navigateTo({
          url: '/packages/setting/pages/agreement/main?value=17',
        });
      } else if (values == 63) {
        uni.navigateTo({
          url: '/packages/setting/pages/agreement/main?value=63',
        });
      }
    },
    /**
     * 隐私政策/用户协议
     * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
     * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
     * @returns {Promise<void>}
     */
    async getAgreeOn(type, name) {
      //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
      // name传
      /*
          App用户使用协议
          App隐私协议
          电子钱包开通协议
          App充值协议
      */
      let params = {
        type: type,
        cityName: '全国',
        name: name,
      };
      let userAgreementRes = await userAgreement(params);
      if (userAgreementRes.success) {
        if (userAgreementRes.data.fileUrl) {
          // 打开pdf
          this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
        }
      } else {
        uni.showToast({ title: '未找到该协议' });
      }
    },
  },
  // #endif
};
