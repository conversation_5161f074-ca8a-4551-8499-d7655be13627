
<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize($event, resizeCallback)"></page-meta>
    <view class="tabar-page-class">
        <oilCharge ref="oilCharge" :pageChanged="pageChanged" :refer="refer" @changePageChange="changePageChange"></oilCharge>
        <div v-if="isHarmony">
            <keyboard-plugin></keyboard-plugin>
            <account-plugin ref="accountPlugin"></account-plugin>
        </div>
        <zj-show-modal></zj-show-modal>
    </view>
</template>

<script>
import { mapState } from 'vuex';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import oilCharge from '../../s-kit/first/third-oil/main.vue';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            refer: 'r24',
            pageChanged: false,
        };
    },
    components: {
        oilCharge,
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
        // this.changeCell()
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {
        my.on('hmHideTimer', res => {
            this.hideTimeHm();
        });
        my.on('pageWillShow', res => {
            this.getSelectMarkerV3();
            this.notPaidListGet();
            // #ifdef MP-MPAAS
            // 重启鸿蒙会员码定时器
            this.startTime(res);

            //如果是mpaas小程序，判断下用户是否切后台超过15分钟，超过的话，重新刷新一下油站数据
            this.$cnpcBridge.getValueToNative('appToBackTime', value => {
                if (value) {
                    this.$cnpcBridge.setValueToNative('appToBackTime', '');
                    let timestamp = new Date().getTime(); // 当前时间的时间戳
                    // 计算时间间隔（以分钟为单位）
                    let timeDiff = Math.abs(timestamp - Number(value)) / (1000 * 60); // 转换为分钟
                    // 判断时间间隔是否大于十五分钟
                    if (timeDiff > 15) {
                        this.$cnpcBridge.setValueToNative('Define_Selected_Station', '');
                        //重新根据当前位置刷新下附近油站
                        this.$store.dispatch('initLocationV3_app', () => {});
                    }
                }
            });
            // #endif
        });
        this.notPaidListGet();
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) this.refer = params.refer;
        }
    },
    onShow() {
        // #ifdef MP-MPAAS
        console.log('isHarmony---', this.isHarmony);
        if (this.isHarmony) {
            this.$sKit.mpaasPayPlugin?.init();
            this.$nextTick(async () => {
                let result = await this.$sKit.keyBordPlugin.initRef();
                this.$store.commit('setAccountDataPlugin', result);
                const accountPluginRef = this.$refs.accountPlugin;
                this.$sKit.accountPlugin.initRef(accountPluginRef);
            });
        }

        // #endif
    },
    methods: {
        hideTimeHm() {
            console.log('鸿蒙调用');
            // 清除查询订定时器
            this.$refs.oilCharge.$refs.codePaymentConten.$refs.membershipCode.clearTimerQuqeyOrder();
            // 清除刷新会员码定时器
            this.$refs.oilCharge.$refs.codePaymentConten.$refs.membershipCode.clearTimerMember();
        },
        startTime(res) {
            if (res?.data?.startTimer && this.$refs.oilCharge.topTab === 'code' && this.isHarmony) {
                // 清除查询订定时器
                this.$refs.oilCharge.$refs.codePaymentConten.$refs.membershipCode.clearTimerQuqeyOrder();
                // 清除刷新会员码定时器
                this.$refs.oilCharge.$refs.codePaymentConten.$refs.membershipCode.clearTimerMember();
                this.$refs.oilCharge.$refs.codePaymentConten.$refs.membershipCode.init();
            }
        },
        changePageChange(pageChanged) {
            this.pageChanged = pageChanged;
        },
        resizeCallback(e, changed) {
            this.$refs.oilCharge.isShowMap = false;
            // this.$refs.oilCharge[0].isShowMapContent = false;
            this.$refs.oilCharge.setMapHeight();
            this.pageChanged = changed;
        },
        // 拿到地理位置的油站信息
        // #ifdef MP-MPAAS
        getSelectMarkerV3() {
            this.$cnpcBridge.getValueToNative('Define_Selected_Station', value => {
                if (value) {
                    this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                        marker: JSON.parse(decodeURIComponent(value)),
                        upLocation: true,
                    });
                }
            });
        },
        // #endif

        // 查询待结交易订单
        notPaidListGet() {
            this.$refs.oilCharge.chargenotPaidListOrder();
        },
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
};
</script>
<style scoped lang="scss">
.tabar-page-class {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .f-1 {
        height: 100%;
        padding-bottom: 50px;
    }
}
</style>
