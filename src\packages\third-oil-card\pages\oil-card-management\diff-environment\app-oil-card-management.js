 import { rollout } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';

export default {
    methods: {
        showKeyboard(){
            //密码键盘显示与操作
            /**
             * keyboardType 必填  number： 数字键盘  letter：字母键盘
                passwordType 必填  payment：支付密码；setup：设置密码；modify：修改密码
                numberPassword 非必填 数字密码的长度，最小为4最大为8，默认为4
                letterPassword 非必填 字母键盘密码长度 最小为4最大为25，默认4-25
                regular 非必填 字母键盘正则校验，只能验证类型，如数字字母特殊符号
                setText 非必填，默认文字为支付
                passwordInputShow 非必填，默认显示。 有且值为0时不显示
                mongolianlayer 非必填，蒙层。 有且值为1显示
             * 
             * */ 
                let params = {
                  keyboardType: "number",
                  passwordType: "payment",
                  numberPassword: 6,
                  setText: "支付",
                  mongolianlayer:1
                }
                console.log('this.passwordKeyboardRef',this.passwordKeyboardRef)
                this.passwordKeyboardRef.openKeyboard(params,()=>{
                  this.passwordlength = this.passwordKeyboardRef.getFirstLength()
                    // console.log('pwdLength--',pwdLength)
                    // this.newPasswordShow = pwdLength
                  },
                  (val)=>{
                    console.log('密码参数：',val)
                    this.password = val.cipherText
                    this.passWordOpenVal = val
                    this.transferOut()
                })
          
        },
        /**
         * @description  : 资金转出方法
         * @param         {String} cardNo -卡号
         * @param         {String} amount -金额
         * @param         {String} password -密码
         * @param         {Function} mixCapitalOutflow -资金转出方法
         * @param         {Function} cardOperationPwd -加油卡操作的时候，如果钱包是冻结状态，并且冻结原因是10未设置密码，给出提示。
         * @return        {*}
         */
        async fundTransferMethod() {
            if(!this.isHarmony){
                this.$sKit.commonUtil.cardOperationPwd({
                    nextFun: async () => {
                        let params = {
                            cardNo: this.thirdCardItem.cardNo,
                            amount: this.cardInfoDetail.cash,
                            password: this.password,
                        };
                       
              
                        let res = await this.$sKit.mixApi.mixCapitalOutflow(params);
    
                        if (res.success) {
                            let url = '/packages/third-oil-card/pages/fund-transfer-out-result/main';
                            let params = {};
                            let type = 'navigateTo';
                            this.$sKit.layer.useRouter(url, params, type);
                        } else {
                            uni.showToast({ title: res.info });
                        }
                    },
                    walletAddParams: {
                        refer: 'r12',
                    },
                });
            }else{
                this.$sKit.commonUtil.cardOperationPwd({
                    nextFun: async () => {
                        this.showKeyboard();
                    }
                });
            }
        },
        async transferOut() {
            let params = {
                cardNo: this.thirdCardItem.cardNo,
                amount: this.cardInfoDetail.cash,
                password: this.password,
            };
            params.deviceId = this.passWordOpenVal.deviceId
            params.keyboardDataCacheId = this.passWordOpenVal.keyboardDataCacheId
            params.uniqueId = this.passWordOpenVal.uniqueId
            let res = await rollout(params);
  
            if (res.success) {
                let url = '/packages/third-oil-card/pages/fund-transfer-out-result/main';
                let params = {};
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            } else {
                uni.showToast({ title: res.info });
            }
        },
    },
};
