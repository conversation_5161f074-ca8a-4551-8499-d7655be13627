<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas view">
        <zj-navbar title="开具发票" :border-bottom="false"></zj-navbar>
        <div class="p-LR-16">
            <div class="font-16 weight-500 color-333 title1"> 发票备注打印示例 </div>
            <div class="title2 font-12 weight-400 color-999"> 发票备注信息会在发票右下角“备注”栏中显示 </div>
            <img class="invoicepng" src="../../image/invoiceExample.png" alt="" />
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
};
</script>

<style lang="scss" scoped>
.view {
    background: #f7f7fb;
    .title1 {
        margin-top: 60rpx;
        text-align: center;
    }
    .title2 {
        width: 100%;
        height: 46rpx;
        line-height: 46rpx;
        text-align: center;
    }
    .invoicepng {
        width: 100%;
        height: 442rpx;
        margin-top: 15px;
    }
}
</style>
