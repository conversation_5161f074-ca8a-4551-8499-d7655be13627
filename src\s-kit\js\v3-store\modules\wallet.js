import {
    balance,
    accountStatus,
    queryECardInfoByIdNo,
    queryECardInfoByIdNoMiniProgram,
    queryECardInfoByIdAliMiniProgram,
    migrateECard,
} from '../../v3-http/https3/wallet.js';
import { identityAuthInfo } from '../../v3-http/https3/oilCard/index.js';
import { currentUsed, skinDetailApi, skinList } from '../../v3-http/https3/user.js';
import MAlAYER from '../../../js/layer.js';
import cnpcBridge from '../../v3-native-jsapi/cnpcBridge.js';
import CONFIG from '../../third-config.js';
import store from '../../../../store/index.js';
import sKit from '../../index.js';

export default {
    // 挂在store到全局Vue原型上
    state: {
        // 昆仑e享卡信息
        walletInfo: {},
        // 昆仑e享卡皮肤信息
        walletSkin: {
            fileUrl: '',
            skinId: '',
            skinTitle: '',
            chargeType: '',
            subtitle: '',
            extFileUrl: require('../../../image/card-default1.png'),
        },
        // 昆仑e享卡皮肤id
        walletSkinId: '',
        // walletStatus: ''
        walletStatus: {},
        // 用户非脱敏信息
        nonDesensitizationInfo: {},
        // 控制电子卡迁移弹窗标识
        isTransfer: false,
        // 获取待迁移的电子卡数据
        electronicCardData: {},
        // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
        backWalletRechange: '',
        // 申领昆仑e享卡实体卡选中油站
        walletSelectStation: {},
        mySkin: '', //我的皮肤数量
        walletAddChannel: '',
    },
    getters: {
        // 昆仑e享卡信息
        walletInfo: state => state.walletInfo,
        // 昆仑e享卡皮肤信息
        walletSkin: state => state.walletSkin, // walletStatus: (state) => state.walletStatus ? JSON.parse(decodeURIComponent(state.walletStatus)) : "",
        // 昆仑e享卡状态信息
        walletStatus: state => state.walletStatus,
        // 昆仑e享卡皮肤id
        walletSkinId: state => state.walletSkinId,
        mySkin: state => state.mySkin,
    },
    mutations: {
        // 开卡渠道信息
        setWalletAddChannel(state, info) {
            state.walletAddChannel = info;
        },
        // 昆仑e享卡信息
        setWalletInfo(state, info) {
            state.walletInfo = info;
        },
        // 昆仑e享卡皮肤信息
        setWalletSkin(state, list) {
            state.walletSkin = list;
        },
        // 昆仑e享卡皮肤id
        setWalletSkinId(state, status) {
            state.walletSkinId = status;
        },
        // 昆仑e享卡状态信息
        setWalletStatus(state, status) {
            state.walletStatus = status;
        },
        // 获取用户非脱敏身份信息
        mSetNonDesensitizationInfo(state, info) {
            state.nonDesensitizationInfo = info || {};
        },
        // 获取电子卡迁移数据(手机号、卡号)
        mSetElectronicCardData(state, info) {
            state.electronicCardData = info || {};
        },
        // 公众号跳转到小程序钱包充值页面，在成功开通电子钱包时返回钱包充值页面标识
        mSetBackWalletRechange(state, info) {
            state.backWalletRechange = info;
        },
        // 电子卡迁移弹窗标识
        mSetIsTransfer(state, info) {
            state.isTransfer = info.flag;
            // 首页触发迁移，点击弹窗上的X或者稍后迁移执行关闭小程序
            if (info.activate == 'GB') {
                //  关闭小程序
                cnpcBridge.closeMriver();
                // 开通电子钱包，触发迁移，点击弹窗上的X或者稍后迁移执行关闭小程序
            } else if (info.activate == 'ktqb') {
                //
                let url = '/packages/third-my-wallet/pages/home/<USER>';
                let params = { refer: 'r06' };
                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                MAlAYER.useRouter(url, params, type);
                // 公众号跳转到小程序钱包充值页面，在迁移成功时返回钱包充值页面标识
            } else if (info.activate == 'FHCZ') {
                let url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
                let params = { refer: 'r18' };
                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                MAlAYER.useRouter(url, params, type);
            } else if (info.activate == 'WDYK') {
                let url = '/mpaas/packages/third-oil-card/pages/my-card/main';
                let params = {};
                let type = 'redirectTo'; // 默认  uni.navigateTo({})
                MAlAYER.useRouter(url, params, type);
            }
        },
        setWalletSelectStation(state, info) {
            state.walletSelectStation = info;
        },
        setMySkin(state, info) {
            state.mySkin = info;
        },
    },
    actions: {
        // 昆仑e享卡皮肤信息
        selectWalletSkinAction(context, status) {
            context.commit('setWalletSkin', status);
        },
        // 昆仑e享卡皮肤id
        setWalletSkinId(context, status) {
            context.commit('setWalletSkinId', status);
        },
        // 昆仑e享卡信息
        setWalletInfoAct(context, status) {
            context.commit('setWalletInfo', status);
        },
        // 获取我的皮肤的数量
        async getMySkin({ state, commit, dispatch }) {
            let res = await skinList({}, { isload: false });
            if (res.success) {
                if (res.data.length > 0) {
                    let skinListIds = res.data.map(item => {
                        return item.skinId;
                    });
                    let skinslength = skinListIds.length;
                    commit('setMySkin', skinslength);
                }
            }
        },
        //获取当前使用皮肤id
        async getCurrentId({ state, commit, dispatch }) {
            return new Promise(async (resolve, reject) => {
                let res = await currentUsed({}, { isload: false });
                if (res && res.success) {
                    resolve(res.data.skinId);
                    commit('setWalletSkinId', res.data.skinId);
                    if (Object.keys(res.data).length !== 0 && res.data) {
                        commit('setWalletSkin', res.data);
                    } else {
                        commit('setWalletSkin', {
                            fileUrl: require('../../../image/card-default1.png'),
                            skinId: '',
                            skinTitle: '',
                            chargeType: '',
                            subtitle: '',
                            extFileUrl: require('../../../image/card-default1.png'),
                        });
                    }
                } else {
                    commit('setWalletSkin', {
                        fileUrl: require('../../../image/card-default1.png'),
                        skinId: '',
                        skinTitle: '',
                        chargeType: '',
                        subtitle: '',
                        extFileUrl: require('../../../image/card-default1.png'),
                    });
                }
            });
        },
        // 获取当前使用皮肤id详情
        async getCurrentImg({ state, commit, dispatch }, { callback, isload }) {
            let arr = null;
            let walletSkinId = await dispatch('getCurrentId');
            if (!walletSkinId) {
                commit('setWalletSkin', {
                    fileUrl: require('../../../image/card-default1.png'),
                    skinId: '',
                    skinTitle: '',
                    chargeType: '',
                    subtitle: '',
                    extFileUrl: require('../../../image/card-default1.png'),
                });
                return;
            }
            if (callback && typeof callback === 'function') {
                callback();
            }
            return;
            // #ifdef MP-MPAAS
            arr = walletSkinId ? [walletSkinId] : [];
            // #endif
            // #ifdef MP-ALIPAY || MP-TOUTIAO
            arr = walletSkinId ? [walletSkinId] : [];
            // #endif
            // #ifdef MP-WEIXIN
            arr = walletSkinId ? walletSkinId : '';
            // #endif
            let res = await skinDetailApi({ skinIds: arr, path: 'PUBLIC_CLOUD' }, { isload: isload });
            // #ifndef MP-MPAAS
            // TODO 支付宝获取皮肤详情数据字段映射
            if (JSON.stringify(CONFIG.name).includes('-zfb')) {
                res.result = res.InfoCode == 1 ? 'success' : 'fail';
                res.data = res.Data;
            }
            // #endif
            // path: "PUBLIC_CLOUD"
            if (callback && typeof callback === 'function') {
                callback();
            }
            if (res.result == 'success') {
                if (res.data.length) {
                    commit('setWalletSkin', res.data[0]);
                } else {
                    commit('setWalletSkin', {
                        fileUrl: require('../../../image/card-default1.png'),
                        skinId: '',
                        skinTitle: '',
                        chargeType: '',
                        subtitle: '',
                        extFileUrl: require('../../../image/card-default1.png'),
                    });
                }
            } else {
                commit('setWalletSkin', {
                    fileUrl: require('../../../image/card-default1.png'),
                    skinId: '',
                    skinTitle: '',
                    chargeType: '',
                    subtitle: '',
                    extFileUrl: require('../../../image/card-default1.png'),
                });
            }
        },
        //查看当前电子账户余额及相关信息
        async getAccountBalanceAction({ state, commit, dispatch }, suc) {
            return new Promise(async (resolve, reject) => {
                let res = await balance({}, { isload: false });
                console.log(res, 'res============');
                if (res.success) {
                    commit('setWalletInfo', res.data);
                }
                resolve(res);
                sKit.mpBP.setUserInfo();
            });
        },
        //查看当前电子账户状态
        async getSetWalletStatus({ state, commit, dispatch }, suc) {
            return new Promise(async (resolve, reject) => {
                let res = await accountStatus({}, { isload: false });
                console.log(res, 'res============昆仑e享卡状态');
                if (res.success) {
                    commit('setWalletStatus', res.data);
                    resolve(res);
                    if (suc && suc.callback && typeof suc.callback === 'function') {
                        suc.callback(res.data);
                    }
                }
            });
        },
        // 获取用户非脱敏身份信息和获取电子卡迁移数据
        async getIdentityInformationAndElectronicCardData(context, info) {
            // 获取用户非脱敏身份信息
            let identityAuthInfoRes = await identityAuthInfo();
            if (identityAuthInfoRes.success) {
                context.commit('mSetNonDesensitizationInfo', identityAuthInfoRes.data);
                let res = null;
                // 获取电子卡迁移数据(手机号、卡号)
                // #ifndef MP-MPAAS
                if (JSON.stringify(CONFIG.name).includes('-zfb')) {
                    // TODO 支付宝调用获取电子卡迁移数据接口
                    const { memberNo } = uni.getStorageSync('tokenInfo') || {};
                    res = await queryECardInfoByIdAliMiniProgram({ encryptIdNo: identityAuthInfoRes.data.identityNo, userId: memberNo });
                    res.result = res.InfoCode == 1 ? 'success' : 'fail';
                    res.status = res.InfoCode == 1 ? 0 : 1;
                    res.data = res.Data;
                }
                // #endif
                // #ifdef MP-WEIXIN
                const { memberNo } = uni.getStorageSync('tokenInfo') || {};
                res = await queryECardInfoByIdNoMiniProgram({
                    encryptIdNo: identityAuthInfoRes.data.identityNo,
                    userId: memberNo || '',
                });
                // #endif
                // #ifdef MP-MPAAS
                let userInfo = await cnpcBridge.getUserTokenInfo();
                res = await queryECardInfoByIdNo({
                    encryptIdNo: identityAuthInfoRes.data.identityNo,
                    path: 'PUBLIC_CLOUD',
                    userId: userInfo.memberNo || '',
                });
                // #endif

                if (res.status == 0) {
                    // 获取电子卡迁移数据(手机号、卡号)
                    context.commit('mSetElectronicCardData', res.data);
                    if (res.data && res.data.cardStatus == '2') {
                        // 电子卡迁移弹窗标识
                        context.commit('mSetIsTransfer', { flag: true });
                    }
                }
            }
        },
        // 电子卡迁移
        async electronicCardMigration(context, data) {
            let params = {
                idNo: context.state.nonDesensitizationInfo.identityNo,
                memberPhone: context.state.electronicCardData.phone,
            };

            let res = await migrateECard(params);
            if (res.success) {
                // 电子卡迁移弹窗标识
                context.commit('mSetIsTransfer', data);
                context.dispatch('zjShowModal', {
                    title: '您的电子卡已迁移并升级为昆仑e享卡，请到钱包页面中查看',
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            let url = '/packages/third-my-wallet/pages/home/<USER>';
                            let params = { refer: 'r06' };
                            let type = 'redirectTo'; // 默认  uni.navigateTo({})
                            MAlAYER.useRouter(url, params, type);
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                context.dispatch('zjShowModal', {
                    title: '您的电子卡暂时无法迁移，请稍后再试',
                    content: `${res.errorCode}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
    },
};
