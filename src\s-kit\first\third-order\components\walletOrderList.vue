<template>
    <div>
        <!-- 昆仑e享卡充值订单 -->
        <div class="order-item" v-for="(item, index) in orderList" :key="index" @click="toDetail(1, item)">
            <div class="item-top">
                <div class="item-left">
                    <div class="title">
                        <div class="title-text">充值支付方式：{{ item.paymentTypeName }}</div>
                        <div class="name-arrow-right"></div>
                    </div>
                    <div class="time" v-if="item.orderStatus == 1">{{ '创建时间：' + item.createTime }}</div>
                    <div class="time" v-if="item.orderStatus == 4">{{ '支付时间：' + item.endTime }}</div>
                </div>
                <div class="item-right">
                    <div class="state">{{ item.orderStatusName }}</div>
                    <div class="amount">{{ item.rechargeAmount }}</div>
                </div>
            </div>
            <div class="countdown" v-if="item.orderStatus == 1">
                <div></div>
                <div class="bottom-button-pay" @click.stop="payOrder(item)">立即支付</div>
            </div>
        </div>
        <!-- 实体卡充值订单 -->
        <div class="order-item" v-for="(item, index) in cardOrderList" :key="index" @click="toDetail(2, item)">
            <div class="item-top">
                <div class="item-left">
                    <!-- <div class="title">
                      <div class="title-text">充值支付方式：{{ item.tradeType }}</div>
                      <div class="name-arrow-right"></div>
                    </div>-->
                    <div class="time">{{ '支付时间:' + (item.occurTime || '') }}</div>
                </div>
                <div class="item-right">
                    <!-- <div class="state">{{ checkFp(item) }}</div> -->
                    <div class="amount">&yen;{{ item.amount }}</div>
                </div>
            </div>
            <div class="countdown">
                <div></div>
                <div class="bottom-button-open" v-if="item.isInvoice == '1'" @click.stop="invoice2(item)">开具发票</div>
                <div class="bottom-button-see" v-if="item.isInvoice == '2'" @click.stop="seeInvoice(item)">查看发票</div>
            </div>
        </div>
    </div>
</template>

<script>
import { eCardRechargeRecord } from '../../../js/v3-http/https3/wallet';
import { invoiceListApi } from '../../../js/v3-http/https3/invoice/index';
import { getRechargeDetail } from '../../../js/v3-http/https3/oilCard';
import { getInvoiceByOrderNoApi } from '../../../js/v3-http/https3/order/index';
import appWalletOrderList from './diff-environment/app-wallet-order-list';
import zfbWalletOrderList from './diff-environment/zfb-wallet-order-list';
import wxWalletOrderList from './diff-environment/wx-wallet-order-list';
import h5WalletOrderList from './diff-environment/h5-wallet-order-list';

export default {
    name: 'walletOrderList',
    // #ifdef MP-MPAAS
    mixins: [appWalletOrderList],
    // #endif
    // #ifndef MP-MPAAS || H5-CLOUD
    mixins: [zfbWalletOrderList, wxWalletOrderList],
    // #endif
    // #ifdef H5-CLOUD
    mixins: [h5WalletOrderList],
    // #endif
    props: {
        refer: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            // 充值订单列表
            orderList: [],
            // 实体卡充值订单列表
            cardOrderList: [],
            // 页数
            pageNum: 1,
            // 页码
            pageSize: 10,
        };
    },
    mounted() {
        // 给支付逻辑增加防抖节流
        this.payOrder = this.$sKit.commonUtil.throttleUtil(this.payOrder);
        this.invoice2 = this.$sKit.commonUtil.throttleUtil(this.invoice2);
    },
    methods: {
        /**
         * @description  : 钱包充值逻辑
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        payOrder(item) {
            // 钱包充值
            let trueNum = null;
            // #ifdef MP-WEIXIN
            trueNum = 4;
            // #endif
            // #ifdef MP-ALIPAY
            trueNum = 5;
            // #endif
            // #ifdef MP-MPAAS
            trueNum = 2;
            // #endif
            // #ifdef H5-CLOUD
            trueNum = 29;
            // #endif
            if (item.sourceChannelNo == trueNum) {
                let params = item;
                let url = '/packages/third-order/pages/pay-order/main';
                this.$sKit.layer.useRouter(url, params);
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: '请切换至下单渠道进行支付',
                    success: res => {
                        if (res.confirm) {
                        }
                    },
                });
            }
        },
        /**
         * @description  : 根据isInvoice字段值判断发票状态并显示
         * @param         {Object} item:实体卡订单数据
         * @return        {String} 发票状态字符串
         */
        checkFp(item) {
            //    0 不开发票
            //    1 等待开票
            //    2 开票成功
            //    3 开票中
            //    4 红冲中
            let tag = item.isInvoice;
            if (tag == '0') {
                return '不可开票';
            } else if (tag == '1') {
                return '等待开票';
            } else if (tag == '2') {
                return '开票成功';
            } else if (tag == '3') {
                return '开票中';
            } else if (tag == '4') {
                // 红冲中
                return '换开中';
            }
        },
        /**
         * @description  : 根据父组件页面上的入参，判断获取哪种类型的充值订单列表
         * @param         {Boolean} isInit: 是否初始化订单数据
         * @param         {Object} listParams: 查询订单接口入参
         * @return        {*}
         */
        getWalletOrderList({ isInit = false, listParams = {} } = {}) {
            if (listParams.secondNavActive == 4) {
                if (listParams.refuelCardAccountNo == '') {
                    this.$emit('loadStatusChange', 'nomore');
                    this.$emit('showEmptyChange', true);
                    return;
                }
                if (listParams.refuelCardAccountNo == 'eCard') {
                    this.eCardRechargeRecordPost({ isInit: isInit, listParams: listParams });
                } else {
                    this.rechargeRecordListPost({ isInit: isInit, listParams: listParams });
                }
            } else {
                this.eCardRechargeRecordPost({ isInit: isInit, listParams: listParams });
            }
        },
        async eCardRechargeRecordPost({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            if (isInit) {
                this.orderList.forEach(item => {
                    if (item?.timer) clearInterval(item.timer);
                });
                this.cardOrderList = [];
                this.orderList = [];
            }
            this.$emit('loadStatusChange', 'loading');
            let params = {
                orderStatus: listParams.secondNavActive,
                startTime: listParams.timeObj.startTime + ' 00:00:00',
                endTime: listParams.timeObj.endTime + ' 23:59:59',
            };

            let res = await eCardRechargeRecord(params, { isload: false });
            this.$emit('stopRefresh');
            if (res && res.success) {
                let arr = res.data || [];
                let orderStatusObj = {
                    1: '待支付',
                    4: '已完成',
                    6: '已取消',
                };
                let paymentTypeObj = {
                    1: '微信',
                    2: '支付宝',
                    3: '银联卡',
                    4: '数字人民币',
                    5: '昆仑e享卡',
                    6: '加油卡',
                    7: '信用账户',
                    8: '云闪付',
                    9: '和包支付',
                    10: '翼支付',
                    11: '优惠券',
                    27: '现金券',
                    12: '礼品卡',
                    13: '油币',
                    14: '能源币',
                    15: '电子账户积分',
                    29: '现金支付',
                    99: '聚合扫码',
                    98: '聚合刷卡',
                    16: '易积分',
                    17: 'BP支付',
                    18: 'DT支付',
                    19: '平安壹钱包',
                    20: '优途支付',
                    21: '建行龙支付',
                    22: '昆仑银行支付',
                    23: '工银e支付',
                    24: '民生付',
                    25: '银联二维码',
                    26: '充值卡',
                    100: '云南企业支付',
                    101: '云南本地支付',
                    102: '加油卡积分',
                    999: '其它支付',
                    559: '洗车券支付',
                };
                let sourceChannelNoObj = {
                    0: '充值卡',
                    2: '能源e站APP',
                    4: '能源e站微信小程序',
                    5: '能源e站支付宝小程序',
                    15: '室内现金充值',
                    29: '云闪付小程序',
                    30: '平安口袋银行',
                    48: '平安壹钱包',
                    50: '云梦泽能源',
                    51: '平安好车主',
                    58: '和包支付',
                };
                this.orderList = arr.map(item => {
                    item.orderStatusName = orderStatusObj[item.orderStatus];
                    item.paymentTypeName = paymentTypeObj[item.paymentType];
                    item.sourceChannelNoName = sourceChannelNoObj[item.sourceChannelNo];
                    return item;
                });
                this.$emit('loadStatusChange', 'nomore');
            }
            if (this.orderList.length == 0) {
                this.$emit('showEmptyChange', true);
            } else {
                this.$emit('showEmptyChange', false);
            }
        },
        /**
         * @description  : 订单详情跳转逻辑，判断是否为待支付订单，如果是待支付订单走支付逻辑，如果不是跳转详情
         * @param         {*} type:1 昆仑e享卡充值订单   2 实体卡充值订单
         * @param         {*} item:订单数据
         * @return        {*}
         */
        toDetail(type, item) {
            if (type == 1 && item.orderStatus == 1) {
                this.payOrder(item);
            } else {
                let params = { ...item, walletOrderType: type, refer: this.refer };
                this.$sKit.layer.useRouter('/packages/third-order/pages/wallet-order-detail/main', params);
            }
        },
        /**
         * @description  : 查看发票逻辑，根据订单号对发票列表筛选，因产品设计原因，只取第一个发票展示
         * @param         {*} item:订单数据
         * @return        {*}
         */
        async seeInvoice(item) {
            let params = {
                orderNum: item.orderId,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.order-item {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    padding: 29rpx 27rpx 29rpx 29rpx;
    display: flex;
    flex-direction: column;

    &:nth-last-of-type(1) {
        margin-bottom: 0;
    }

    .item-top {
        display: flex;
        justify-content: space-between;

        .item-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .title {
                display: flex;
                align-items: center;

                .title-text {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 33rpx;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                    margin-left: 15rpx;
                }
            }

            .time {
                margin-top: 10rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 33rpx;
            }
        }

        .item-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .state {
                font-size: 24rpx;
                font-weight: bold;
                color: #333333;
                line-height: 33rpx;
            }

            .amount {
                margin-top: 10rpx;
                font-size: 24rpx;
                font-weight: bold;
                color: #e64f22;
                line-height: 33rpx;
            }
        }
    }

    .countdown {
        margin-top: 10rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .bottom-button-open {
            text-align: center;
            width: 209rpx;
            height: 80rpx;
            border-radius: 16rpx;
            border: 1rpx solid #e64f22;
            font-size: 30rpx;
            font-weight: 400;
            color: #e64f22;
            line-height: 80rpx;
        }

        .bottom-button-pay {
            text-align: center;
            width: 209rpx;
            height: 80rpx;
            background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
            border-radius: 16rpx;
            font-size: 30rpx;
            font-weight: 400;
            color: #ffffff;
            line-height: 80rpx;
        }

        .bottom-button-see {
            text-align: center;
            font-size: 30rpx;
            font-weight: 400;
            color: #333333;
            line-height: 80rpx;
        }
    }
}
</style>
