<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div>
        <div class="pageMpaas">
            <div class="view fl-column p-bf bg-F7F7FB">
                <zj-navbar :height="44" title="预约详情"></zj-navbar>
                <div class="padding-16">
                    <div v-for="(infoList, listIndex) in infoLists" :key="listIndex" class="containerTop p-LR-16 bg-fff border-rad-8">
                        <div
                            v-for="(item, index) in infoList"
                            :key="index"
                            :class="{ 'border-bottom': index !== infoList.length - 1 }"
                            class="info-item fl-row fl-jus-bet fl-al-cen"
                        >
                            <div class="label weight-400 color-333 font-14">{{ item.label }}</div>
                            <div
                                :class="{
                                    detailsEllipsis: shouldShowEllipsis(item, keysToCheck),
                                }"
                                class="value weight-400 color-333 font-14"
                                >{{ item.value }}</div
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { bulkOilRegisterInfo } from '../../../../s-kit/js/v3-http/https3/bulkOil/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'bulkOilReservationRecord',
    data() {
        return {
            userInfo: [
                { label: '姓名', value: '', userName: '' },
                { label: '手机号', value: '', phone: '' }, // 部分隐藏手机号
                { label: '身份证号', value: '', idNum: '' }, // 部分隐藏身份证号
            ],
            addressInfo: [
                { label: '居住地', value: '', region: '' },
                { label: '', value: '', address: '' },
            ],
            oilPurchaseInfo: [
                { label: '购油用途', value: '', useOilPurpose: '' },
                { label: '购油品种', value: '', productName: '' },
                { label: '购油站点', value: '', stationName: '' },
            ],
            quantityOfPurchasedOilInfo: [{ label: '购油数量', value: '', oilQuantity: '' + 'L' }],
            reserveInformation: {},
            // 过长是否需要变为省略号
            keysToCheck: ['stationName', 'address'],
        };
    },
    computed: {
        // 将多个列表合并为一个数组，方便动态渲染
        infoLists() {
            return [this.userInfo, this.addressInfo, this.oilPurchaseInfo, this.quantityOfPurchasedOilInfo];
        },
    },
    onLoad(options) {
        this.reserveInformation = JSON.parse(decodeURIComponent(options.data));
        this.obtainRecordInformation();
        console.log(this.infoLists, 'infoLists');
    },
    methods: {
        // 隐藏部分信息的函数
        hidePartialInfo(info, start, length) {
            return info.substring(0, start) + '*'.repeat(length) + info.substring(start + length);
        },
        /**
         * @description  : 查看预约记录
         * @applyId        : {String} 预约信息中获取的ID
         * @stationCode        : {String} 油站编码
         * @return       : 无返回值，但会将获取到的预约散装油订单信息赋值到对应的字段中
         */
        async obtainRecordInformation() {
            // 定义请求参数，通常是从预约信息中获取的 ID
            let params = {
                applyId: this.reserveInformation?.applyId, // 使用可选链操作符避免 this.reserveInformation 为 undefined 时报错
                stationCode: this.reserveInformation?.stationCode, // 使用可选链操作符避免 this.reserveInformation 为 undefined 时报错
            };

            // 发起请求
            let res = await bulkOilRegisterInfo(params);
            // 请求成功的情况
            if (res.success) {
                // 模拟请求返回的数据对象
                // const obj = {
                //     name: '张三',
                //     phone: '14426543143',
                //     idNo: '132165412165125421',
                //     address: '北京市 昌平区',
                //     detailedAddress: '回龙观街道回南家园6栋33号',
                //     purposeOfPurchasingOil: '农机',
                //     purchaseOilVarieties: '02号',
                //     oilPurchasingStation: '石大2号站加油站',
                //     quantityOfPurchasedOil: '10.0L',
                // };

                // 将返回的数据赋值到对应的字段中
                // 使用 assignValues 方法动态匹配并赋值
                this.assignValues(this.userInfo, res.data); // 赋值用户信息
                this.assignValues(this.addressInfo, res.data); // 赋值地址信息
                this.assignValues(this.oilPurchaseInfo, res.data); // 赋值购油信息
                this.assignValues(this.quantityOfPurchasedOilInfo, res.data); // 赋值购油数量信息
            }
        },
        /**
         * @description  : 动态匹配并赋值
         * @infoArray        : {Array} infoArray - 需要赋值的数组，数组中的每个对象包含 label 和 value 等字段
         * @obj        : {Object} obj - 数据源对象，包含需要赋值的数据
         * @return       : 无返回值，但会将 obj 中的数据赋值到 infoArray 中对应的 value 字段
         *
         * 功能：遍历 infoArray 中的每个对象，根据 obj 中的对应字段赋值给 value，
         * 并对手机号和身份证号进行部分隐藏处理。
         */
        assignValues(infoArray, obj) {
            // 遍历 infoArray 中的每个对象
            infoArray.forEach(item => {
                // 遍历当前对象的所有 key
                for (const key in item) {
                    // 排除 'label' 和 'value' 字段，只处理其他字段（如 name、phone 等）
                    if (key !== 'label' && key !== 'value' && obj[key] !== undefined) {
                        // 如果当前项的 label 是 '手机号'，则对手机号进行部分隐藏处理
                        if (item.label === '手机号') {
                            // 调用 hidePartialInfo 方法，隐藏手机号中间 4 位
                            item.value = this.hidePartialInfo(obj[key], 3, 4);
                        }
                        // 如果当前项的 label 是 '身份证号'，则对身份证号进行部分隐藏处理
                        else if (item.label === '身份证号') {
                            // 调用 hidePartialInfo 方法，隐藏身份证号中间 8 位
                            item.value = this.hidePartialInfo(obj[key], 6, 8);
                        }
                        // 其他情况，直接赋值
                        else {
                            // 将 obj 中对应字段的值赋给 item.value
                            item.value = obj[key];
                        }
                    }
                }
            });
        },
        shouldShowEllipsis(item, keys) {
            console.log('测试执行了吗 ', item);
            return keys.some(key => key in item);
        },
    },
};
</script>

<style lang="scss" scoped>
.containerTop {
    width: 100%;
    //height: 292rpx;
    margin-bottom: 25rpx;
    .info-item {
        width: 100%;
        height: 97rpx;
        .detailsEllipsis {
            //text-align-last: left;
            text-align: right;
            width: 200px;
            line-height: 17.5px;
            overflow: hidden; /* 隐藏超出的文本 */
            display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
            -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
            -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
            //word-break: break-all; /* 允许在单词内换行 */
        }
    }
}
</style>
