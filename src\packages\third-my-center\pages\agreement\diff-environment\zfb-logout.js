import { logoutApi } from '@/s-kit/js/v3-http/https3/user';

import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';

export default {
    // #ifdef MP-ALIPAY
    mounted() {},
    methods: {
        async logOut() {
            let tokenInfo = await uni.getStorageSync('tokenInfo');
            if (tokenInfo?.accessToken) {
                let res = await logoutApi();
                if (res.success) {
                    uni.clearStorageSync();
                    this.$store.commit('setCleanTheRegisteredAddress', true);
                    this.$store.commit('setLongTimeNotLogin', null);
                    this.$sKit.layer.backHomeFun();
                }
            }
        },
    },
    // #endif
};
