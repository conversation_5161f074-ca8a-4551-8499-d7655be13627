<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="bg-F7F7FB" style="height: 100%">
            <zj-navbar :height="44" title="消息"></zj-navbar>
            <div class="text_div">
                <!-- {{ detailtext ? removeHTMLTag(detailtext.messageContent) : '' }} -->
                <u-parse :html="detailtext.messageContent" :show-with-animation="true" @linkpress="goInvite2"></u-parse>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userGroupNewsDetail } from '../../../../s-kit/js/v3-http/https3/preferentialGroup/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            id: '',
            detailtext: {
                messageContent: '',
            },
            intkey: '1',
        };
    },
    onLoad(option) {
        //接收列表页传参
        this.id = JSON.parse(decodeURIComponent(option.data)).id;
    },
    mounted() {
        //调用消息详情接口
        this.getUserGroupNewsDetail();
    },
    methods: {
        // 转义
        escape2Html(str) {
            var arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };
            let newStr = str.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all, t) {
                return arrEntities[t];
            });
            console.log(newStr, '站内信');
            return newStr;
        },
        //消息详情接口
        async getUserGroupNewsDetail() {
            let res = await userGroupNewsDetail({ id: this.id });
            if (res && res.success) {
                this.detailtext = res.data;
                this.detailtext.messageContent = this.escape2Html(this.detailtext.messageContent);
                //通过改变key的值实现页面的重新渲染
                if (this.intkey == 1) {
                    this.intkey = 2;
                } else {
                    this.intkey = 1;
                }
                let bizState = '';
                if (this.detailtext.messageStatus == 1) {
                    bizState = 'unread';
                } else if (this.detailtext.messageStatus == 2) {
                    bizState = 'read';
                }
                let bizContent = JSON.stringify([
                    {
                        state: bizState,
                        messageID: this.id,
                        messageType: this.detailtext.messageType,
                        messageTitle: this.detailtext.messageTitle,
                    },
                ]).replace(/,/g, ' ');
                this.$sKit.mpBP.tracker('消息中心', {
                    seed: 'messageBiz',
                    pageID: 'messageDetailPage',
                    dateType: 'exposure',
                    content: bizContent,
                    channelID: clientCode,
                });
            }
        },
        //点击事件触发
        addeventvale(event) {
            //判断当前点击的是否是a标签
            if (event.target.nodeName === 'A') {
                let address = event.target.href;
                let tagA = event.target;
                //删除当前点击a标签前的href 走路由
                tagA.removeAttribute('href');
                //截取a标签中的参数
                const getQuery = (url, query) => {
                    // str为？之后的参数部分字符串
                    const str = url.substr(url.indexOf('?') + 1);
                    // arr每个元素都是完整的参数键值
                    const arr = str.split('&');
                    // result为存储参数键值的集合
                    const result = {};
                    for (let i = 0; i < arr.length; i++) {
                        // item的两个元素分别为参数名和参数值
                        const item = arr[i].split('=');
                        result[item[0]] = item[1];
                    }
                    return result;
                };
                //url截取参数
                const res = getQuery(address);
                let url = '/packages/third-message/pages/invite/main';
                let params = { id: res.groupNo };
                let type = 'navigateTo'; // 默认  uni.navigateTo({})
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        //去站内信邀请列表
        goInvite() {
            //解析详情内群组id
            let str = this.detailtext.messageContent;
            var pattern = /<a href="(.*?)">.*?<\/a>/g;
            var result = str.match(pattern);
            if (result == null) {
                return;
            }
            var href = result[0]
                .replace(/<a href="(.*?)">.*?<\/a>/, '$1')
                .split('"')[0]
                .split('?')[1]
                .split('=')[1];
            //跳转页面
            let url = '/packages/third-message/pages/invite/main';
            let params = { id: href };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        //去站内信邀请列表
        goInvite2(obj) {
            //去掉群组的，站内信跳转-最终敲定版：
            //差评回访：?type=evaluate&id=xxx
            //意度：?type=satisfaction&id=xx
            //站内信链接地址配置为：
            //http://www/baidu.com?type=evaluate&id={{id}}
            //http://www/baidu.com??type=satisfaction&id={{id}}
            //解析详情内群组id
            obj.ignore();
            let urlParams = this.getUrlParams(obj.href);
            console.log(obj, '跳转');
            if (urlParams.hasOwnProperty('type')) {
                let path = '';
                switch (urlParams.type) {
                    //满意度调查//差评回访
                    case 'satisfaction':
                    case 'evaluate':
                        path = '/packages/third-questionnaire/pages/home/<USER>';
                        break;
                    case 'groupInvite':
                        path = '/packages/third-message/pages/invite/main';
                        break;
                }
                let type = 'navigateTo'; // 默认  uni.navigateTo({})
                this.$sKit.layer.useRouter(path, urlParams, type);
            } else {
                //群组权益
                var href = obj.href.split('"')[0].split('?')[1].split('=')[1];
                //跳转页面
                let url = '/packages/third-message/pages/invite/main';
                let params = { id: href };
                let type = 'navigateTo'; // 默认  uni.navigateTo({})
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        //去除html标签
        removeHTMLTag(str) {
            str = str.replace(/<\/?[^>]*>/g, ''); //去除HTML tag
            str = str.replace(/[ | ]*\n/g, '\n'); //去除行尾空白
            str = str.replace(/\n[\s| | ]*\r/g, '\n'); //去除多余空行
            str = str.replace(/&nbsp;/gi, ''); //去掉&nbsp;
            str = str.replace(/&lt;/gi, '');
            str = str.replace(/&gt;/gi, '');
            str = str.replace(/[div|\/div]/g, '');
            str = str.replace(/[span|\/span]/g, '');
            str = str.replace(/&lt;[ -~]*&gt;/gi, ''); //去掉替换后的<>标签
            return str;
        },
        getUrlParams(url) {
            const reg = /(\w+)=(\w+)/g; // 匹配参数键值对的正则表达式
            const params = {};
            let match;
            while ((match = reg.exec(url))) {
                const key = decodeURIComponent(match[1]); // 解码参数键
                const value = decodeURIComponent(match[2]); // 解码参数值
                params[key] = value;
            }
            return params;
        },
    },
};
</script>

<style lang="scss" scoped>
.text_div {
    padding: 32rpx;
}
</style>
