<template>
    <div>
        <CustomPopup ref="customPopup">
            <view class="_modalBox">
                <view class="_modal">
                    <div class="_modal_top_http" v-if="type == 'http'">
                        <view class="title">{{ title }}</view>
                        <view class="content" v-if="content">错误码：{{ content }}</view>
                    </div>
                    <div class="_modal_top" v-else>
                        <view v-if="title || content">
                            <view class="title">{{ title }}</view>
                            <view class="content" v-if="content">{{ content }}</view>
                            <view class="content content2" v-if="content2">{{ content2 }}</view>
                        </view>
                        <slot v-else></slot>
                    </div>
                    <view class="btnbox">
                        <slot name="button">
                            <view
                                class="btn cancel_btn"
                                v-if="cancelText"
                                :style="{ color: cancelColor, background: cancelBackgroundColor }"
                                @click.stop="clickBtn('cancel')"
                            >
                                {{ cancelText }}
                            </view>
                            <view
                                class="btn confirm"
                                :style="{
                                    color: confirmColor,
                                    background: confirmBackgroundColor,
                                }"
                                @click.stop="clickBtn('confirm')"
                                >{{ confirmText }}</view
                            >
                        </slot>
                    </view>
                </view>
            </view>
        </CustomPopup>
    </div>
</template>

<script>
import CustomPopup from './custom-popup.vue';
export default {
    name: 'show-modal',
    computed: {
        type() {
            return this.$store.state.thirdInitModal.type;
        },
        show() {
            return this.$store.state.thirdInitModal.show;
        },
        title() {
            return this.$store.state.thirdInitModal.title;
        },
        content() {
            return this.$store.state.thirdInitModal.content;
        },
        content2() {
            return this.$store.state.thirdInitModal.content2;
        },
        showCancel() {
            return this.$store.state.thirdInitModal.showCancel;
        },
        cancelText() {
            return this.$store.state.thirdInitModal.cancelText;
        },
        cancelColor() {
            return this.$store.state.thirdInitModal.cancelColor;
        },
        cancelBackgroundColor() {
            return this.$store.state.thirdInitModal.cancelBackgroundColor;
        },
        confirmText() {
            return this.$store.state.thirdInitModal.confirmText;
        },
        confirmColor() {
            return this.$store.state.thirdInitModal.confirmColor;
        },
        confirmBackgroundColor() {
            return this.$store.state.thirdInitModal.confirmBackgroundColor;
        },
    },
    components: {
        CustomPopup,
    },
    mounted() {
        //
    },
    watch: {
        '$store.state.thirdInitModal.show': {
            handler(val, oldVal) {
                if (val) {
                    console.log('弹窗开启');
                    this.$refs.customPopup.open();
                } else {
                    console.log('弹窗关闭');
                    this.$refs.customPopup.close();
                }
            },
            deep: true,
        },
    },
    methods: {
        closeModal() {
            this.$store.dispatch('zjHideModal');
        },
        clickBtn(res) {
            this.$store.dispatch('zjSuccessModal', res);
        },
    },
    beforeDestroy() {
        this.$store.dispatch('zjHideModal');
    },
    data() {
        return {};
    },
};
</script>

<style lang="scss" scoped>
// ._showModal {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   z-index: 10000;

//   ._shade {
//     width: 100%;
//     height: 100%;
//     position: absolute;
//     top: 0;
//     left: 0;
//     background: #000;
//     opacity: 0.6;
//     z-index: 11000;
//   }

._modalBox {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99999999;
    display: flex;
    justify-content: center;
    align-items: center;

    ._modal {
        flex: none;
        width: 560rpx;
        min-height: 207rpx;
        background: #ffffff;
        overflow: hidden;
        border-radius: 20rpx;

        ._modal_top_http {
            padding: 38rpx 0 0;
            min-height: 180rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .title {
                text-align: center;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 36rpx;
                padding: 0 39rpx;
            }

            .content {
                padding: 0 32rpx;
                margin-top: 8rpx;
                width: 100%;
                min-height: 68rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 36rpx;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                word-wrap: break-word;
                word-break: break-all;
            }
        }

        ._modal_top {
            padding: 40rpx;
            min-height: 114rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .title {
                text-align: center;
                font-size: 28rpx;
                font-weight: bold;
                color: #333333;
                line-height: 46rpx;
            }

            .content {
                text-align: center;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 36rpx;
                margin-top: 32rpx;
                word-wrap: break-word;
                word-break: break-all;
            }

            .content2 {
                margin-top: 10rpx;
            }
        }

        .btnbox {
            border-top: 2rpx solid #efeff4;
            display: flex;
            width: 100%;
            height: 90rpx;
            flex-direction: row;
            justify-content: space-between;

            .btn {
                flex: 1;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 28rpx;
                font-weight: 400;
                line-height: 90rpx;

                &:active {
                    background-color: #f7f7f7;
                }
            }

            .cancel_btn {
                border-right: 2rpx solid #efeff4;
            }

            .confirm {
                font-weight: bold;
            }
        }
    }
}

// }
</style>
