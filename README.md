# 能源e站

## 环境 node 14

```
nvm use 14
```

## 安装依赖

```
npm install
```

### app mPaaS

```
// 开发
npm run dev:mpaas cnpc-fuel-mini-dev
// 生产
npm run build:mpaas cnpc-fuel-mini-prd
```

### 微信小程序

```
// 开发
npm run dev:wx cnpc-wx-sit
// 生产
npm run build:wx cnpc-wx-prd
```

### 支付宝小程序

```
// 开发
npm run dev:al cnpc-zfb-sit
// 生产
npm run build:al cnpc-zfb-prd
```

### 支付宝小程序分包

```
// 开发
npm run dev:sub cnpc-zfb-sit
// 生产
npm run build:sub cnpc-zfb-prd
```

### 技术支持

- [uni-app 开发文档](https://uniapp.dcloud.net.cn/)
- [uni小程序 SDK 集成文档](https://nativesupport.dcloud.net.cn/)
- [支付宝小程序开发文档](https://opendocs.alipay.com/mini/introduce?pathHash=dcc784a3)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)

### 编译平台区分

> 需独立配置 package.json

```
"uni-app": {
    "scripts": {
        "mp-mpaas": {
            "title": "mPaas小程序",
            "BROWSER": "",
            "env": {
                "UNI_PLATFORM": "mp-alipay"
            },
            "define": {
                "MP-MPAAS": true,
                "MP-ALIPAY": false
            }
        }
    }
}
```

|                                         | APP-MPAAS | 微信小程序 | 支付宝小程序 | H5 |
|-----------------------------------------|-----------|-------|--------|----|
| #ifdef MP-MPAAS                         | 1         | 0     | 0      | 0  |
| #ifdef MP-WEIXIN                        | 0         | 1     | 0      | 0  |
| #ifdef MP-ALIPAY                        | 0         | 0     | 1      | 0  |
| #ifndef MP-MPAAS                        | 0         | 1     | 1      | 1  |
| #ifndef MP-WEIXIN                       | 1         | 0     | 1      | 1  |
| #ifndef MP-ALIPAY                       | 1         | 1     | 0      | 1  |
| #ifndef MP-MPAAS \|\| MP-WEIXIN \|\| H5 | 0         | 0     | 1      | 0  |

### 整体项目架构

```
├──dist 编译后的文件路径
  ├──package.json 配置项
  ├──src 核心内容
  ├──api 公共项目接口
  ├──config 项目配置文件
  ├──pages 主包
  ├──static 全局静态资源
  ├──store vuex
  ├──utils 公共方法
  ├──App.vue 应用配置，配置App全局样式以及监听
  ├──main.js Vue初始化入口文件
  ├──manifest.json 配置应用名称、appid等打包信息
  ├──pages.json 配置页面路由、导航条、选项卡等页面类信息
  ├──s-kit 全局公共组件
     ├──js 全局公共方法
     ├──css 全局公共样式
     └─components 全局公众组件

```

### 安装原生小程序开发工具

- AntCubeTool 工具基于 NodeJS 进行设计开发，在使用 AntCubeTool 之前请确保已正确安装 NodeJS 环境。 重要 NodeJS 版本不低于
  LTS v14。 查看 NodeJS 版本请使用命令：`node -v`
- cnpm 的使用请参考： https://cnpmjs.org/。
- cnpm 安装方法：请在命令行执行：`npm install -g cnpm --registry=https://registry.npm.taobao.org`
- 建议使用：node 版本 14.19.3

#### 安装工具

- 安装 minidev
- 构建工具 `cnpm install minidev -g`
- 安装 xcube/mini
- 构建工具 `cnpm install @xcube/mini -g minidev` 安装成功后就可以在命令行中使用 Xmini 命令了。

- 切换配置 将一个 Web 小程序工程配置切到能兼容原生小程序编译的构建配置。
- 控制台使用 cd 命令切换到小程序工程目录（mini.project.json 所在目录）执行如下命令：`xmini config`
- 如果之前执行过切换操作，现在想要把小程序工程配置切换回之前的模样，可以执行如下命令：`xmini config`
- 构建与上传，使用命令 `xmini build` 进行构建

### 页面目录

```javascript
/**
 * 页面跳转
 * @param {'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab' | 'navigateBack' | number } url 转跳路径
 * @param {String} params 跳转时携带的参数
 * @param {String} type 转跳方式
 **/
let url = '/packages/third-oil-card/pages/my-card/main';
let params = {};
let type = 'navigateTo'; // 默认 uni.navigateTo({});
this.$sKit.layer.useRouter(url, params, type);
```

### 页面下拉刷新组件

```vue

/**
* 常用参数
* @param refreshPullDown 下拉刷新emit分发事件
* @param pullDownRefreshRef 下拉刷新组件ref
* @param {methods} stopRefresh 停止下拉刷新动画
**/
// 组件
<div class="pullDownRefreshView">
  <zj-navbar></zj-navbar>
  <div class="f-1 mh-0">
    <zj-pull-down-refresh
        @refreshPullDown="refreshPullDown"
        ref="pullDownRefreshRef"
    >
      <div></div>
    </zj-pull-down-refresh>
  </div>
</div>

<script>
  // api
  this.$refs.pullDownRefreshRef.stopRefresh();
</script>
```

### 页面列表组件

```vue

/**

* 常用参数
* @param {String} emptyImage 空态图
* @param {String} emptyText 空态提示语
* @param {Boolen} showEmpty 空态标识，true为展示空态内容
* @param {emit} refreshPullDown 下拉刷新emit分发事件
* @param {emit} scrolltolower 上拉加载事件
* @param {methods} stopRefresh 停止下拉刷新动画
* @param {String} loadStatus 列表状态标识，loading 加载中 contentdown 上拉加载更多 nomore 没有更多了
* @param {String} background 列表背景颜色
**/

// 组件
<div class="f-1 mh-0">
  <zj-data-list
      background="#F7F7FB"
      ref="dataList"
      :emptyImage="require('@/static/trirdImage/kt1yhq.png')"
      emptyText="您目前没有任何订单"
      :showEmpty="showEmpty"
      @refreshPullDown="refreshPullDown"
      @scrolltolower="scrolltolower"
  >
    <div>列表数据</div>
  </zj-data-list>
</div>

<script>
  // api
  // 上拉加载
  scrolltolower()
  {
    if (this.$refs.dataList.loadStatus == 'contentdown') {
      this.$refs.dataList.loadStatus = 'loading';
      this.getData();
    }
  }
  // 下拉刷新
  refreshPullDown(e)
  {
    this.getData({isInit: true});
  }
  // 获取数据设置状态
  getData({isInit = false} = {})
  {
    if (isInit) {
      this.$refs.dataList.loadStatus = 'loading';
    }
    // 获取数据成功后
    this.$refs.dataList.stopRefresh()
    // 数据为空
    this.showEmpty = true
    // 页码不为最大值
    this.$refs.dataList.loadStatus = 'contentdown'
    // 页码为最大值
    this.$refs.dataList.loadStatus = 'nomore'
  }
</script>
```

### 页面非列表空态组件

```vue
/** * 常用参数 * @param {String} emptyImage 空态图 * @param {String} emptyText 空态提示语 **/ // 组件
<zj-no-data
    v-if="isShow && amountOfMoneyList.length == 0"
    :emptyImage="require('@/static/trirdImage/kt10qb.png')"
    emptyText="暂未查询到交易明细"
></zj-no-data>
```

### showModal 封装（不推荐使用）

```javascript
/**
 * 常用参数
 * @param {String} title 弹窗标题，默认提示
 * @param {String} content 弹窗内容，默认''
 * @param {String} confirmText 确定按钮文本，默认确定
 * @param {String} cancelText 取消按钮文本，默认取消
 * @param {Boolean} showCancel 是否展示取消按钮，默认false
 * @param {String} confirmColor 确定按钮文字颜色，默认#FF8200
 * @param {String} cancelColor 取消按钮文字颜色，默认#666666
 **/
this.$sKit.layer
  .showModal({
    title: title,
    content: content,
    confirmText: confirmText,
    cancelText: cancelText,
    showCancel: showCancel,
    confirmColor: confirmColor,
    cancelColor: cancelColor,
  })
  .then(() => {
    //点击确认触发方法
  })
  .catch(() => {
    //点击取消触发方法
  });
```

### zj-show-modal 消息弹窗组件（推荐）

```vue
/** * 常用参数 * @param {String} title 弹窗标题，默认"标题" * @param {String} content 弹窗内容，默认'' * @param {String} confirmText
确定按钮文本，默认'确定' * @param {String} cancelText 取消按钮文本，不传不显示按钮 * @param {String} confirmColor
确定按钮文字颜色，默认#333333 * @param {String} cancelColor 取消按钮文字颜色，默认#ED4F4E * @param {String} type
类型，不传为默认弹窗和自定义弹窗，传"http"为接口报错弹窗 * @param {Function} success 自定义回调 **/
<zj-show-modal>
  <div> //如果在插槽中写入自定义内容，则在内容区展示自定义内容
  </div>
  <template v-slot:button> 自定义按钮
    <div></div>
  </template>

</zj-show-modal>

<script>
  this.$store.dispatch('zjShowModal', {
    title: '',
    content: '',
    confirmText: '',
    cancelText: '',
    confirmColor: '',
    cancelColor: '',
    type: '',
    success: res => {
      if (res.confirm) {
        console.log('用户点击确定');
      } else if (res.cancel) {
        console.log('用户点击取消');
      }
    },
  });

  // 自定义按钮关闭弹窗
  this.$refs['xxxxxx'].$refs['popup'].open();
</script>
```

### uni-popup 页面弹窗组件

```vue
/** * 常用参数 * @param {String} background 弹窗背景颜色，默认透明 * @param {String} type 组件弹出位置 * @param {Boolean} maskClick
是否开启点击遮罩关闭弹窗 **/
<uni-popup :maskClick="false" ref="chargePop" type="bottom"></uni-popup>
```

### 获取状态栏 + navbar 高度

```javascript
this.$sKit.layer.systemTopHeight();
```

### 注意事项

- 1.不能用 span
- 2.不要用 uni.showModal，用 zj-show-modal
- 3.不要用￥，用&yen;
- 4.font-wight: 500 要写成 font-wight: bold
- 5.cube 页面中，开启弹性布局默认竖排，横排要加 flex-direction: row;
- 6.不要给组件标签写类名和样式
- 7.所有渐变色改为 background-image
- 8.zj-show-modal 写到页面中
- 9.radio 外层包裹 div 添加点击事件，可以实现选中取消状态
- 10.修改 placeholder 样式，配置 placeholder-class 类名，在无 scoped `<style></style>`中编辑样式

### 临时修改

- 1.微信 sdk n=getApp().globalData 改为 n={};

### 发版注意

- 1.商城订单图片前拼接域名切换为生产
- 2.lcationV3_app.js 里传给地图 xml 文件图片域名将测试环境修改为生产环境
- 3.跳转商城详情 更换域名 emall.95504.net 生产域名 temall.95504.net 测试域名
- 4.电子钱包、加油卡，充值金额最低不小于一元打开
- 5.发 sit 和和灰度时检查一下 cubeMiniAppId 是否正确(生产的不要动)
- 6.发包时在开发工具上检查选择的小程序是不是 mpaas 小程序，检查当前的环境是测试还是生产，点击开发工具最左侧倒数第二个图标(
  mpaas 工具箱)==》设置==》小程序设置，查看下当前包的路径是否正确。
- 7.查看预约加油与 e 站加油距离限制
- 8.打开实时获取位置信息
- 9.发布正式环境时如果存在跳转小程序的功能，需要将 trial 更换为 release
- 10.检查代码中的 my.alert()和$console()
- 11."lastMinute":'10' //TODO 最近 5 分钟的单 后支付加油限制 10 分钟内的订单，生产要打开
- 12.发版时更新静态资源包(字体，图片等)
- 13.链接 app 的生产域名https://app.95504.net
- 14.链接 app 的灰度环境域名https://app.95504.net/gray
- 15.确认两个插件的版本号是否是最新
- 16.流水线发布，运维添加minVersion字段
- 17.微信小程序，支付宝小程序，添加域名白名单
- 18.// fixed,上线前要改正确认版本号 this.closeForm = await this.$cnpcBridge.judgeProtocolCall('3.6.4');
- 19.检查昆仑e享卡实体卡申领入口是否开着
