<template>
    <div class="oil-station-search-area card-default" @click="cellClick" v-if="Object.keys(item).length != 0">
        <div class="border-div" v-if="curIndex == 0">
            <img src="../../image/right-border.png" alt="" class="border-img" />
            <div class="font-13 weight-400 color-E64F22">距离最近</div>
        </div>
        <div class="name-area">
            <img src="../../image/icon-logo-24.png" alt="" class="icon-14" />
            <p class="name">{{ item.orgName }}</p>
            <div class="arroe-right-333"></div>
        </div>
        <div class="location-detail" v-if="item.address != 'null'">{{ item.address || '' }}</div>
        <!-- <p class="font-12 color-E64F22">92#，95#，98#，0#</p> -->
        <div class="fl-row info-area fl-jus-bet fl-al-cen">
            <div class="fl-row fl-al-cen f-1" style="flex-wrap: wrap">
                <div v-if="item.distance">{{ item.distance || '' }}KM</div>
                <div class="bor-1"></div>
                <div v-if="item.businessHoursList.length > 0">{{ item.businessHoursStr }}</div>
                <div v-else>暂无</div>
                <div class="bor-1"></div>
                <div v-if="item.phone != 'null'">电话:{{ item.phone || '' }}</div>
            </div>
            <div class="color-118920" v-if="item.stationStatus == '20' || item.stationStatus == '10'">【正常营业】</div>
            <div class="color-E02020" v-if="item.stationStatus == '30' || item.stationStatus == '50'">【暂停营业】</div>
        </div>
        <div class="icons" :style="item.tagList && item.tagList.length > 0 ? 'padding-top: 8px;' : ''" v-if="item.tagList.length">
            <div class="icon" v-for="(tag, index) in item.tagList" :key="index" alt>
                <div class="item-tag">{{ strReplace(tag) }}</div>
            </div>
        </div>
        <div class="marketing-assistant" v-if="item.marketingAssistant">
            <div class="marketing-text">{{ item.marketingAssistant }}</div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {};
    },
    props: ['item', 'curIndex'],
    onLoad(option) {},
    onShow() {},
    methods: {
        /**
         * @description  : 油站标识字符串拼接
         * @param         {*} str:油站标识
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        /**
         * @description  : 触发选中事件
         * @return        {*}
         */
        cellClick: function () {
            this.$emit('cellClick');
        },
        /**
         * @description  : 查看详情
         * @return        {*}
         */
        toDetail() {
            let url = '/packages/third-oil-charge-payment/pages/oil-station-module/main';
            let params = {};
            this.$sKit.layer.useRouter(url, params);
        },
    },
    computed: {},
    components: {},
};
</script>
<style scoped lang="scss">
.oil-station-search-area {
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
    .border-div {
        width: 93px;
        height: 38px;
        position: absolute;
        top: 0;
        right: 0;
        .border-img {
            width: 93px;
            height: 38px;
        }
        div {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            text-align: center;
            line-height: 32px;
        }
    }

    .name-area {
        display: flex;
        align-items: center;

        .icon-14 {
            width: 14px;
            height: 14px;
        }

        .name {
            font-size: 14px;
            font-weight: 500;
            color: #000000;
            width: 200px;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            display: -webkit-box;
            overflow: hidden;
            white-space: normal !important;
            text-overflow: ellipsis;
            word-wrap: break-word;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-left: 3px;
            margin-right: 5px;
        }
    }
    .location-detail {
        font-family: PingFangSC-Regular, PingFang SC;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #333333;
        flex: 1;
        padding: 8px 0;
    }
    .info-area {
        font-size: 11px;
        font-weight: 500;
        color: #070707;
    }
    .bor-1 {
        position: relative;
        margin: 0 10px;
        &.bor-1::after {
            content: '';
            position: absolute;
            left: 0;
            top: -4px;
            border-right: 1px solid #070707;
            width: 1px;
            height: 10px;
        }
    }

    .icons {
        // padding: 8px 0;
        overflow: hidden;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .item-tag {
            padding: 4px 8px;
            background: #f3f3f6;
            border-radius: 4px;
            font-size: 12px;
            color: #333333;
            text-align: center;
            margin: 0 9px 4px 0;
        }
    }
    .active {
        width: 250px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
.marketing-assistant {
    margin-top: 5px;
    border: 1px solid #c4c4c3;
    background-color: #fef5ee;
    border-radius: 8px 8px;
    padding: 4.5px;

    .marketing-text {
        font-size: 12px;
        color: #fb9463;
        line-height: 17.5px;
        overflow: hidden; /* 隐藏超出的文本 */
        display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
        -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
        -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        word-break: break-all; /* 允许在单词内换行 */
    }
}
</style>
