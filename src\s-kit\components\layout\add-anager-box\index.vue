<template>
    <div class="view" v-if="codeImg">
        <img class="box-bg" v-if="clientCode == 'C10'" src="../../../image/add-anager-box-mpaas.png" alt="" />
        <img class="box-bg" v-if="clientCode == 'C12'" src="../../../image/add-anager-box-wx.png" alt="" />
        <div class="box-content">
            <div class="text1">成为经理好友~</div>
            <div class="text2">享专属服务</div>
            <!-- #ifdef MP-MPAAS -->
            <div class="text3" @click="addAnager">点击添加</div>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <div class="text3">长按识别右侧二维码</div>
            <!-- #endif -->
        </div>
        <!-- #ifdef MP-WEIXIN -->
        <img class="box-code" show-menu-by-longpress :src="codeImg" alt="" />
        <!-- #endif -->
    </div>
</template>

<script>
import { hkyzWxOriginalId, clientCode } from '../../../../../project.config';
import { addStationWechatWork } from '../../../js/v3-http/https3/user';
export default {
    name: 'add-anager-box',
    props: {
        orgCode: {
            type: String,
            default: '',
        },
        orderNo: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            clientCode,
            codeImg: '',
        };
    },
    created() {},
    watch: {
        orgCode: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getCodeImg();
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        async getCodeImg() {
            let res = await addStationWechatWork({ orgCode: this.orgCode });
            if (res && res.success) {
                this.codeImg = res.data?.qrCode || '';
                this.$emit('getCodeImg', this.codeImg);
            } else {
                this.$emit('getCodeImg', false);
            }
        },
        addAnager() {
            let urlObj = {
                home: `/packages/transferAccount/pages/home/<USER>/pages/thirdhome/main?data=${encodeURIComponent(
                    JSON.stringify({ type: 'addAnager', orgCode: this.orgCode }),
                )}`,
                order: `/packages/transferAccount/pages/home/<USER>/packages/third-order/pages/order-detail/main?data=${encodeURIComponent(
                    JSON.stringify({ orderNo: this.orderNo, type: 'addAnager' }),
                )}`,
            };
            this.$cnpcBridge.openModule({
                type: 'weChatMiniprogram',
                id: hkyzWxOriginalId,
                path: urlObj[this.type],
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    width: 688rpx;
    height: 220rpx;
    position: relative;
    margin: 0 auto;
    .box-bg {
        width: 688rpx;
        height: 220rpx;
    }
    .box-content {
        position: absolute;
        left: 40rpx;
        top: 29rpx;

        .text1 {
            font-weight: bold;
            font-size: 36rpx;
            color: #333333;
            line-height: 50rpx;
        }

        .text2 {
            margin-top: 5rpx;
            font-size: 26rpx;
            color: #666666;
            line-height: 37rpx;
        }

        .text3 {
            margin-top: 20rpx;
            width: 252rpx;
            height: 50rpx;
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            border-radius: 25rpx;
            font-weight: bold;
            font-size: 23rpx;
            color: #ffffff;
            line-height: 50rpx;
            text-align: center;
        }
    }
    .box-code {
        position: absolute;
        width: 144rpx;
        height: 144rpx;
        right: 94rpx;
        top: 56rpx;
    }
}
</style>
