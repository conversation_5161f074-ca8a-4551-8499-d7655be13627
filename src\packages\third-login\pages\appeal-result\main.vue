<template>
    <div class="pageView">
        <zj-navbar :border-bottom="false" title="申诉结果"></zj-navbar>
        <div class="content">
            <div class="result" v-if="result">
                <img src="../../images/success.png" alt />
                <div class="title">提交成功</div>
                <div class="text">
                    您的手机号换绑成功
                    <br />请使用新手机号进行登录
                </div>
            </div>
            <div class="result" v-else>
                <img src="../../images/fail.png" alt />
                <div class="title">验证失败</div>
                <div class="text">抱歉，您当前账号资质不符合申诉条件</div>
            </div>
            <div class="btn">我知道了</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'appeal-result',
    data() {
        return {
            result: true,
        };
    },
    mounted() {
        setInterval(() => {
            this.result = false;
        }, 5000);
    },
    onLoad(option) {},

    methods: {},
};
</script>

<style lang="scss" scoped>
.pageView {
    width: 100%;
    height: 100%;
    background: #f7f7fb;

    .content {
        padding: 80rpx 32rpx 32rpx;
        width: 100%;

        .result {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 128rpx;
                height: 128rpx;
            }

            .title {
                margin-top: 32rpx;
                font-size: 32rpx;
                font-weight: bold;
                color: #333333;
                line-height: 46rpx;
                text-align: center;
            }

            .text {
                margin-top: 32rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 36rpx;
                text-align: center;
            }
        }

        .btn {
            margin-top: 80rpx;
            width: 100%;
            height: 88rpx;
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            font-size: 36rpx;
            font-weight: bold;
            color: #ffffff;
            line-height: 88rpx;
            text-align: center;
            border-radius: 8px;
        }
    }
}
</style>
