import { logoutApi } from '../../../../../s-kit/js/v3-http/https3/user.js';
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
export default {
     // #ifdef MP-MPAAS
    mounted() {},
    methods: {
        /**
         * @description     : 退出登录
         * @return        {*}
         */
        async logout() {
            let res = await logoutApi({}, { isCustomErr: true });
            this.$sKit.commonUtil.logoutFun();
        },
        /**
         * @description     : 查看协议点击事件
         * @return        {*}
         */
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('2', 'App隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            this.$cnpcBridge.getLocation(async res => {
                let params = {
                    type: type,
                    cityName: res.cityCode,
                    name: name,
                };
                let userAgreementRes = await userAgreement(params);
                if (userAgreementRes.success) {
                    if (userAgreementRes.data.fileUrl) {
                        this.$cnpcBridge.checkPDF(userAgreementRes.data.fileUrl);
                    }
                } else {
                    uni.showToast({ title: '未找到该协议' });
                }
            });
        },
    },
    // #endif
};
