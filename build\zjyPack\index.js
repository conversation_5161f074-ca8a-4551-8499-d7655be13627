const CLASS_NAME = 'ZjyPack'; // 插件名称
const MAIN_PACKAGE_PATH = 'pages';
const SUB_PACKAGE_PATH = 'subPackages';

const fs = require('fs');
const path = require('path');
// const projectConfig = require('../../src/buildConfig')
let lastPageJson = '';
// 拿到命令中要启动的项目名称
const project = process.argv[process.argv.length - 1];
/**
 * 如果你需要在构建完成后的代码中获取 UNI_PLATFORM，可以考虑在构建过程中将其保存到一个文件中，然后在后续代码中读取该文件
 */
// 获取 UNI_PLATFORM 环境变量
const uniPlatform = process.env.UNI_PLATFORM;

// 将 UNI_PLATFORM 保存到一个文件中
const platformFilePath = path.resolve(__dirname, '../readEnv.json');
fs.writeFileSync(platformFilePath, JSON.stringify({ uniPlatform }));
class ZjyPack {
    constructor() {
        const config = fs.readFileSync(path.resolve(__dirname, '../storage/project.json'));
        this.config = JSON.parse(config.toString());
    }

    // 项目初始化
    static projectInit(pName) {
        let pConfig = projectConfig.find(item => item.config.name == pName);
        console.log(pConfig);
        if (!pConfig) {
            throw '------------------- ' + '没有找到项目' + pName + ' -------------------';
        }
        pConfig = pConfig.config;
        const pagesJson = pConfig.app;
        const pages = pConfig.mainPackages;
        const subPages = pConfig.subPackages;
        if (!pages || pages.length == 0) {
            throw '------------------- ' + '请配置主包页面' + pName + ' -------------------';
        }
        pagesJson.pages = [];
        // 主包处理
        for (let i = 0; i < pages.length; i++) {
            const element = pages[i];
            pagesJson.pages.push(ZjyPack.getMainPageConfig(element));
        }
        console.log('----------------- pagesJson -----------------');
        console.log(pagesJson);
        return pagesJson;
        // throw('停止')
    }
    // 主包路径处理
    static getMainPageConfig(pageName) {
        // 主包路由
        const path = `${MAIN_PACKAGE_PATH}/${pageName}/main`;
        return {
            path,
        };
    }

    // 分包路径处理
    static getSubPageConfig() { }
    apply(compiler) {
        // 初始化
        compiler.hooks.environment.tap(CLASS_NAME, (compilation, callback) => {
            console.log('执行environment调用');

            if (process.env.UNI_PLATFORM == 'mp-weixin') {
                // 写入mpass网关必要文件
                const gwcli1 = fs.readFileSync(path.resolve(__dirname, '../../src/utils/wasm/gwcli1-0-2.wasm.br'));
                let environmentDir = process.env.NODE_ENV == 'production' ? 'build' : 'dev';
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-weixin/utils`));
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-weixin/utils/wasm`));
                fs.writeFileSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-weixin/utils/wasm/gwcli1-0-2.wasm.br`), gwcli1);
            }
            console.log('=============================', process.env.UNI_PLATFORM);

            if (process.env.UNI_PLATFORM == 'mp-alipay' || process.env.UNI_PLATFORM == 'mp-toutiao') {
                //写入xml地图文件
                const gwcli1 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-ios-mpaas.xml'));
                const gwcli2 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-ios.xml'));
                const gwcli3 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-android.xml'));
                const opencjJson = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/safe-password/safe-password.json'));
                const opencjAxml = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/safe-password/safe-password.axml'));
                const opencjs = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/safe-password/safe-password.js'));
                const opencss = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/safe-password/safe-password.acss'));
                const opencAJson = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/account-plugin/account-plugin.json'));
                const opencAAxml = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/account-plugin/account-plugin.axml'));
                const opencAJs = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/account-plugin/account-plugin.js'));
                const opencACss = fs.readFileSync(path.resolve(__dirname, '../../src/opencomponents/account-plugin/account-plugin.acss'));
                // const gwcli4 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-ios-mpaas-site.xml'));
                // const gwcli5 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-ios-site.xml'));
                // const gwcli6 = fs.readFileSync(path.resolve(__dirname, '../../src/xml/oil-map-android-site.xml'));
                let environmentDir = process.env.NODE_ENV == 'production' ? 'build' : 'dev';
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/xml`), { recursive: true });
                fs.writeFileSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/xml/oil-map-ios-mpaas.xml`), gwcli1);
                fs.writeFileSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/xml/oil-map-ios.xml`), gwcli2);
                fs.writeFileSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/xml/oil-map-android.xml`), gwcli3);
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents`), { recursive: true });
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/safe-password`), {
                    recursive: true,
                });
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/safe-password/safe-password.json`),
                    opencjJson,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/safe-password/safe-password.axml`),
                    opencjAxml,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/safe-password/safe-password.js`),
                    opencjs,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/safe-password/safe-password.acss`),
                    opencss,
                );
                fs.mkdirSync(path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/account-plugin`), {
                    recursive: true,
                });
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/account-plugin/account-plugin.json`),
                    opencAJson,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/account-plugin/account-plugin.axml`),
                    opencAAxml,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/account-plugin/account-plugin.js`),
                    opencAJs,
                );
                fs.writeFileSync(
                    path.resolve(__dirname, `../../dist/${environmentDir}/mp-alipay/opencomponents/account-plugin/account-plugin.acss`),
                    opencACss,
                );
            }
        });
        // 文件更新监听
        compiler.hooks.watchRun.tap(CLASS_NAME, watchCompiler => {
            console.log('每次更新执行', process.env.UNI_PLATFORM);
            // 监听配置逻辑 有问题暂时不用
            // let content = JSON.stringify(ZjyPack.projectInit(this.config.name))
            // let fileContent = fs.readFileSync(path.resolve(__dirname, "../../src/pages.json"), 'utf8')
            // if(content !== fileContent) {
            //   fs.writeFileSync(path.resolve(__dirname, "../../src/pages.json"), content)
            // }
        });
    }
}

module.exports = ZjyPack;
