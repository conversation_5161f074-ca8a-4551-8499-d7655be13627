<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" title="选择领卡网点" :border-bottom="false"></zj-navbar>
            <div class="content fl-column f-1 ov-hid">
                <searchText @searchClick="handleInput"></searchText>
                <div class="f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="list"
                        :emptyImage="require('../../images/kt6wd.png')"
                        :emptyText="productList.length > 0 ? '' : '该地区暂未开通本项业务,敬请期待'"
                        :showEmpty="showEmpty"
                        @scrolltolower="scrolltolower"
                        @refreshPullDown="refreshPullDown"
                        @refresh="handleRefresh"
                    >
                        <div class="cells-content" v-if="productList.length > 0">
                            <div
                                class="oil-station-search-area card-default"
                                v-for="(item, index) in productList"
                                :item="item"
                                :curIndex="index"
                                :key="index"
                                @click="cellClick(item)"
                            >
                                <div class="name-area">
                                    <img src="../../images/icon-logo-24.png" alt="" class="icon-14" />
                                    <p class="name">{{ item.orgName }}</p>
                                    <div class="arroe-right-333"></div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
const PAGE_SIZE = 10; // 每页多少个
import searchText from '../../components/search-text.vue';
import { getMemberCardSellSites } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        searchText,
    },
    data() {
        return {
            productList: [],
            // 页码
            page: 1,
            // 加载状态 // 0 不显示 1 显示加载动画 2 显示没有更多
            // loadState: 0,
            showEmpty: false,
        };
    },
    onLoad(option) {
        this.init();
    },
    onShow() {},
    mounted() {},
    methods: {
        /**
         * @description  : 初始化位置信息，获取油站列表，不改变全局油站列表和选中油站
         * @return        {*}
         */
        init() {
            this.$store.dispatch('initLocationV3_app', {
                callback: () => {
                    this.getGoodsListFun({ isInit: true });
                },
                type: 'onlyLocation',
            });
        },
        /**
         * @description  : 下拉刷新触发
         * @return        {*}
         */
        refreshPullDown() {
            this.page = 1;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  : 上拉加载事件
         * @return        {*}
         */
        scrolltolower() {
            if (this.$refs.list.loadStatus !== 'nomore') {
                this.$refs.list.loadStatus = 'loading';
                this.page + 1;
                this.getGoodsListFun();
            }
        },
        /**
         * @description  : 搜索事件
         * @param         {*} data:输入信息
         * @return        {*}
         */
        handleInput(data) {
            this.orgName = data;
            this.getGoodsListFun({ isInit: true });
        },
        /**
         * @description  :选择油站
         * @param         {*} item:油站数据
         * @return        {*}
         */
        cellClick(item) {
            this.$store.commit('setWalletSelectStation', item);
            uni.navigateBack();
        },
        /**
         * @description  : 获取油站列表
         * @param         {*} isInit: 是否重置油站列表
         * @param         {*} item: 油站类型入参
         * @param         {*} orgName:搜索内容
         * @return        {*}
         */
        async getGoodsListFun({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    productList: [],
                    page: 1,
                });
            }

            let { page, productList } = this;
            let params = {
                orgCode: this.walletInfo.addressNo,
                // orgCode: '1-A1103-C004',
                pageNum: page,
                pageSize: PAGE_SIZE,
                orgName: this.orgName,
            };
            this.$refs.list.loadStatus = 'loading';
            let { data } = await getMemberCardSellSites(params);
            console.log('data----', data);
            this.$refs.list.pullDownHeight = 0;
            this.$refs.list.pullingDown = false;

            productList = productList.concat(data.rows || []);
            if (data && page >= data.pageSum) {
                this.$refs.list.loadStatus = 'nomore';
            } else {
                this.$refs.list.loadStatus = 'contentdown';
            }
            Object.assign(this, {
                productList,
                page: Number(page) + 1,
            });
            this.showEmpty = this.productList.length <= 0 ? true : false;
        },
    },
    computed: {
        ...mapGetters(['latV3', 'lonV3', 'walletInfo']),
    },
};
</script>
<style scoped lang="scss">
.content {
    padding: 0 16px;

    .oil-station-search-area {
        padding: 15px;
        margin-bottom: 10px;
        position: relative;

        .name-area {
            display: flex;
            align-items: center;

            .icon-14 {
                width: 14px;
                height: 14px;
            }

            .name {
                font-size: 14px;
                font-weight: 500;
                color: #000000;
                // max-width: 180px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-left: 3px;
                margin-right: 5px;
            }
        }
    }
}
</style>
