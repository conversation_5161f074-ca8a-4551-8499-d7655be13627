<template>
        <div class="content-box-page p-bf ov-hid fl-column bg-fff" :style="oilBg + ';' + `padding-top: ${10 + Number(systemBarNum) + Number(navH)}px`">
        
        <!-- #ifdef MP-WEIXIN -->
        <div class="icon-back-box" @click="goBack">
            <div v-if="tabType" class="icon-back" :style="borderColor"></div>
        </div>
        <!-- #endif -->
        <main-tabs ref="mainTabs" @changeTab="changeTab"></main-tabs>
        <!-- :style="{ left: topTab != 'code' ? 0 : '120%' }" -->
        <div class="oil-tab">
            <map
                id="noLoginMap"
                :style="{ height: mapHeight + 'px', width: mapWidth + 'px' }"
                :scale="Number(scale_app)"
                :markers="showMarkerArrV3_app"
                show-location
                :latitude="newMapCenterLatV3"
                :longitude="mapCenterLonV3"
                @callouttap="clickCallOut"
                :setting="mapSetting"
                @markertap="clickMarker"
                :enable-scroll="(!guideStep && !popupMoving) || (officialAccountParams == 'yt' && ytPhone)"
                :enable-rotate="false"
                :enable-overlooking="false"
            >
                <!-- #ifdef MP-WEIXIN -->
                <cover-view slot="callout" v-if="isCustomCallout">
                    <cover-view
                        class="marker-view fl-row fl-al-cen"
                        :marker-id="item.id"
                        v-for="item in showMarkerArrV3_app"
                        :key="item.id"
                    >
                        <cover-view class="marker-lable-view fl-row fl-al-cen">
                            <cover-image v-if="item.isFirst" class="navigation" :src="mapImgClosest" alt=""></cover-image>
                            <cover-image v-else class="navigation" :src="mapImg" alt=""></cover-image>
                            <cover-view class="fl-column mar-left-4">
                                <cover-view class="marker-name weight-600 color-000">
                                    {{ item.orgName + '　' }}
                                </cover-view>
                                <cover-view class="fl-row pa-t-5 fl-al-cen">
                                    <cover-view class="marker-text font-10 color-333">距离您</cover-view>
                                    <cover-view class="marker-text font-10 color-E64F22">{{ item.distance }}km</cover-view>

                                    <!-- <cover-view class="l-h28 font-10 color-333">， 驾车约</cover-view>
                  <cover-view class="l-h28 font-10 color-E64F22">4min</cover-view>-->
                                </cover-view>
                            </cover-view>
                        </cover-view>
                    </cover-view>
                </cover-view>
                <!-- #endif -->
            </map>
            <!-- 定位悬停按钮 -->
            <div class="location-icon" @click="clickLocation" :style="{ bottom: iconBottom + 'px' }">
                <img :src="icon" alt="" />
            </div>
            <!-- #ifdef MP-WEIXIN -->
            <!-- @click="verificationCodeOrOneClickLogin" -->
            <div class="loginBlock fl-col p-LR-16 fl-column fl-al-jus-cen">
                <img class="avtar" src="./image/defaultAvatar.png" alt="" />
                <div class="text weght-400 font-14 color-333">登录后，{{ textContent }}</div>
                <getPhoneNumber @loginOver='loginOver'>
                    <div
                        class="loginBtn color-fff font-18 btn-44 primary-btn2"
                        :class="{ 'bg-opacity-288': !loginButtonGrayedOut && !token && !token3 }"
                    >
                        立即登录
                    </div>
                </getPhoneNumber>
            </div>
            <loginRiskControl
                v-if="realNameDialogFlag || facePop"
                @verificationPassed="verifSuccess"
                ref="loginRiskControl"
            ></loginRiskControl>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <zfb-login-v3 :layer="true" :silence="false" @success="onLoginAfterEvent">
                <div class="loginBlock fl-col p-LR-16 fl-column fl-al-jus-cen">
                    <img class="avtar" src="./image/defaultAvatar.png" alt="" />
                    <div class="text weght-400 font-14 color-333">登录后，{{ textContent }}</div>
                    <div class="loginBtn color-fff font-18 btn-44 primary-btn2"> 立即登录</div>
                </div>
            </zfb-login-v3>
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO -->
            <div class="loginBlock fl-col p-LR-16 fl-column fl-al-jus-cen">
                <img class="avtar" src="./image/defaultAvatar.png" alt="" />
                <div class="text weght-400 font-14 color-333">登录后，{{ textContent }}</div>
                <div class="loginBtn color-fff font-18 btn-44 primary-btn2" @click="ttCodeOneClickLogin"> 立即登录</div>
            </div>
            <tt-login></tt-login>
            <!-- #endif -->
            
            
        </div>
    </div>
</template>
<script>
import mainTabs from '../../first/third-oil/components/third-main-tabs/main.vue';
// #ifdef MP-WEIXIN
import loginRiskControl from '@/components/loginRiskControl/main.vue';
import getPhoneNumber from '../../../components/loginV3/getPhoneNumber.vue';
// #endif
import platform from '@/s-kit/js/platform';
import { mapState, mapGetters } from 'vuex';
// #ifdef MP-ALIPAY
import zfbLoginV3 from '@/components/zfb-login-v3/zfb-login-v3.vue';
// #endif
// #ifdef MP-TOUTIAO
import ttLogin from '@/components/tt-login/index.vue';
// #endif

export default {
    name: 'Hello-Word',
    components: {
        // #ifdef MP-ALIPAY
        zfbLoginV3,
        // #endif
        // #ifdef MP-TOUTIAO
        ttLogin,
        // #endif
        // 顶部导航栏切换
        mainTabs,
        // #ifdef MP-WEIXIN
        loginRiskControl,
        getPhoneNumber,
        // #endif
    },
    props: {
        systemBarNum: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            scope: "scope.mobile",
            code: '',
            oilHomeBg: require('../../image/oil-home-bg.png'),
            // #ifndef MP-MPAAS
            topTab: 'charge',
            // #endif
            //系统导航栏高度
            systemBar: '',
            // 地图高度
            mapHeight: 0,
            // 地图宽度
            mapWidth: 0,
            // 地图设置开启手指缩放
            mapSetting: {
                // #ifdef MP-MPAAS
                // 手势
                gestureEnable: 1,
                // 比例尺
                showScale: 1,
                // 指南针
                showCompass: 0,
                // 双手下滑
                tiltGesturesEnabled: 1,
                // 交通路况展示
                trafficEnabled: 0,
                // 地图POI信息
                showMapText: 1,
                // #endif
            },
            mapCtx: {},
            navH: 28,
        };
    },
    computed: {
        ...mapGetters(['showMarkerArrV3_app']),
        ...mapState({
            // 地图缩放比例
            scale_app: state => state.locationV3_app.scale_app,
            // 地图中心点纬度
            mapCenterLatV3: state => state.locationV3_app.mapCenterLatV3,
            // 地图中心点经度
            mapCenterLonV3: state => state.locationV3_app.mapCenterLonV3,
            // 选中的油站
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            // 是否展示首次指引  true-是 false-否
            guideStep: state => state.thirdIndex.guideStep,
            // 钱包状态信息
            walletStatus: state => state.wallet.walletStatus,
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            // 手机号
            phone: state => state.phone,
            // 登录按钮标识
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            // #endif
            // #ifdef MP-WEIXIN
            // 优途
            ytPhone: state => state.location.ytPhone,
            // 三方跳转参数
            officialAccountParams: state => state.location.officialAccountParams,
            // 风控弹窗标识
            realNameDialogFlag: state => state.thirdLogin.realNameDialogFlag,
            // 登录临时码
            loginTemporaryCode: state => state.thirdLogin.loginTemporaryCode,
            // 人脸弹窗
            facePop: state => state.thirdIndex.facePop,
            // #endif
            // 2.0token
            token: state => state.token,
            // 3.0token
            token3: state => state.token3,
            // 是否是登录状态
            isLogin: state => state.isLogin,
        }),
        paddingTop() {
            return `padding-top: ${10 + Number(this.systemBar)}px`;
        },
        newMapCenterLatV3() {
            // #ifndef MP-MPAAS
            return Number(this.mapCenterLatV3) - 0.035;
            // #endif
        },
        textContent() {
            return this.topTab == 'charge' ? '即可使用加油服务' : this.topTab == 'reserve' ? '即可使用e享加油' : '即可使用加油服务';
        },
    },
    created() {},
    async mounted() {
        //  获取系统导航看高度
        // this.getTheSystemNavigationBar();
        // 设置地图高度
        this.setMapHeight();
        this.isCustomCallout = true;
    },
    methods: {
        loginOver(res){
            this.$emit('loginOver', res)
        },
        // 暂时不用
        async verificationCodeOrOneClickLogin() {
            if (!this.loginButtonGrayedOut) return;
            this.$store.commit('setLoginButtonGrayedOut', false);
            if (await this.$store.dispatch('callAutomaticLoginAgain')) return;
            // login 区分是自动登录还是手机号验证码登录
            let res = await this.$store.dispatch('init', 'login');
            if (res.result == 'success') {
                // 不能一键登录执行验证码登录
                let url = '/packages/third-new-third-login/pages/login/main';
                let params = {};
                this.$sKit.layer.useRouter(url, params);
                setTimeout(() => {
                    this.$store.commit('setLoginButtonGrayedOut', true);
                }, 1000);
            } else {
                this.$refs.mainTabs.seltab = 'charge';
                this.topTab = 'charge';
            }
        },
        /**
         * @description  : 抖音手机号验证码登录
         * @param         {*}
         * @return        {*}
         */
        ttCodeOneClickLogin() {
            let url = '/packages/third-new-third-login/pages/login/main';
            let params = {};
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 抖音获取系统导航栏
         * @param         {*}
         * @return        {*}
         */
        getTheSystemNavigationBar() {
            
        },
        /**
         * @description  : 导航栏切换
         * @return        {*}
         */
        changeTab(tab) {
            this.$store.commit('setHomepageTags', tab);
            console.log(tab, 'tab是什么');
            // #ifdef MP-WEIXIN
            if (tab == 'code') {
                // this.verificationCodeOrOneClickLogin();
                console.log('跳转到登录页面');
                return;
            }
            // #endif
            this.topTab = tab;
        },
        /**
         * @description  : 获取窗口高度
         * @return        {*}
         */
        setMapHeight() {
            setTimeout(() => {
                uni.createSelectorQuery()
                    .in(this)
                    .select('.oil-tab')
                    .boundingClientRect(data => {
                        console.log('oil-tab', data);
                        this.mapHeight = data.height;
                        this.mapWidth = data.width;
                    })
                    .exec();
            }, 100);
        },
        /**
         * @description  : 地图气泡点击事件
         * @param         {string} latitude - 纬度
         * @param         {string} longitude - 经度
         * @param         {string} name - 油站名称
         * @param         {string} address - 油站地址
         * @return        {*}
         */
        clickCallOut(e) {
            if (this.guideStep || (officialAccountParams == 'yt' && ytPhone)) return;
            let markerId = e.detail.markerId;
            let item = this.showMarkerArrV3_app.find(item => {
                return item.id == markerId;
            });
            // #ifndef MP-MPAAS
            uni.openLocation({
                latitude: Number(this.selectMarkerV3.latitude),
                longitude: Number(this.selectMarkerV3.longitude),
                name: this.selectMarkerV3.orgName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
            });
            // #endif
        },
        /**
         * @description  : 微信小程序端地图marker点击事件
         * @return        {*}
         */
        clickMarker(e) {
            if (this.guideStep) return;
            let markerId = e.detail.markerId;
            console.log(this.showMarkerArrV3_app, '油站列表');
            let index = this.showMarkerArrV3_app.findIndex(item => {
                return item.stationId == markerId;
            });
            this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                marker: this.showMarkerArrV3_app[index],
            });
        },
        /**
         * @description  : 点击重新定位点击事件
         * @return        {*}
         */
        // 点击重新定位点击事件
        clickLocation() {
            this.$store.dispatch('initLocationV3_app', {
                // #ifndef MP-WEIXIN
                callback: res => {
                    this.mapCtx.moveToLocation({ latitude: res.mapCenterLatV3, longitude: res.mapCenterLonV3 });
                },
                type: 'initMap',
                // #endif
            });
        },
        /**
         * @description  : 返回首页和上一个页面
         * @return        {*}
         */
        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        //返回首页
        backHome() {
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
        verifSuccess() {
            console.log('触发了吗===TTTT');
            this.$store.commit('setRealNameDialogFlag', false);
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
        // 支付宝登录成功后回调
        onLoginAfterEvent(type) {
            console.log('登录成功', type);
            this.$sKit.layer.backHomeFun();
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.content-box-page {
    height: 100%;

    .icon-back-box {
        width: 25px;
        height: 20px;

        .icon-back {
            width: 10px;
            height: 10px;
            border-style: solid;
            border-width: 0px 1px 1px 0px;
            display: inline-block;
            padding: 5px;
            transform: rotate(135deg);
            -webkit-transform: rotate(135deg);
            margin-left: 24px;
        }
    }

    .oil-tab {
        width: 100%;
        position: relative;
        flex: 1;
        min-height: 0;
        .bg-img {
            // background:red;
            width: 375px;
            height: 289px;
        }

        map {
            .marker-view {
                position: relative;
                height: 51px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-direction: column;
                width: fit-content;

                .marker-lable-view {
                    padding: 0 8px 0 5px;
                    height: 51px;
                    background: #fff;
                    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
                    border-radius: 8px;
                    width: fit-content;

                    .navigation {
                        width: 39px;
                        height: 39px;
                    }

                    .marker-name {
                        line-height: 18px;
                        font-size: 13px;
                        overflow: visible;
                        width: auto;
                    }

                    .marker-text {
                        line-height: 14px;
                    }

                    .marker-118920 {
                        width: 50px;
                        height: 21px;
                        background: rgba(17, 137, 32, 0.05);
                        border-radius: 4px;
                        line-height: 21px;
                        text-align: center;
                    }

                    .pa-t-5 {
                        padding-top: 5px;
                    }
                }
            }
        }

        .location-icon {
            display: block;
            position: absolute;
            right: 45rpx;
            width: 40px;
            height: 40px;
            z-index: 999;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .loginBlock {
            width: 100%;
            height: 250px;
            background-color: #fff;
            position: absolute;
            bottom: 0;

            .avtar {
                width: 126rpx;
                height: 126rpx;
                margin-top: 67rpx;
            }

            .text {
                height: 50rpx;
                line-height: 50rpx;
                font-style: normal;
                margin-top: 5px;
            }

            .loginBtn {
                width: 360rpx;
                margin-top: 32rpx;
                margin-bottom: 32rpx;
            }
        }
    }
}
</style>
