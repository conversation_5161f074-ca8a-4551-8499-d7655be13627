!function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}r.r(t);for(var i=window.navigator.userAgent,a="-",c=a,u=[{s:"Windows",r:/Windows/},{s:"Android",r:/Android/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone)/},{s:"iPad",r:/(iPad)/},{s:"iPod",r:/(iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"UNIX",r:/UNIX/}],s=0;s<u.length;s+=1){var l=u[s];if(l.r.test(i)){c=l.s;break}}var f=a;switch(c){case"Windows":f=n(/Windows (.*)/.exec(i),2)[1];break;case"Mac OS X":f=n(/Mac OS X (10[._\d]+)/.exec(i),2)[1];break;case"Android":f=n(/Android ([._\d]+)/.exec(i),2)[1];break;case"iOS":case"iPad":case"iPod":f=/OS (\d+)_(\d+)_?(\d+)?/.exec(i),f="".concat(f[1],".").concat(f[2],".").concat(f[3])}var d=c;switch(c){case"Android":d=function(){var e=a,t=i.split(";");if(t.length>0){var r=t.filter(function(e){return e.indexOf("Build")>-1});r.length>0&&(e=n(/\s+(.*)\s+Build/.exec(r[0]),2)[1])}return e}();break;case"iOS":d=function(){var e,t=document.createElement("canvas");if(t){var r=t.getContext("webgl")||t.getContext("experimental-webgl");if(r){var n=r.getExtension("WEBGL_debug_renderer_info");n&&(e=r.getParameter(n.UNMASKED_RENDERER_WEBGL))}}if(window.screen.height/window.screen.width==812/375&&3===window.devicePixelRatio)return"iPhone X";if(window.screen.height/window.screen.width==736/414&&3===window.devicePixelRatio)switch(e){default:return"iPhone";case"Apple A8 GPU":return"iPhone 6 Plus";case"Apple A9 GPU":return"iPhone 6s Plus";case"Apple A10 GPU":return"iPhone 7 Plus";case"Apple A11 GPU":return"iPhone 8 Plus"}else if(window.screen.height/window.screen.width==667/375&&3===window.devicePixelRatio)switch(e){default:return"iPhone";case"Apple A8 GPU":return"iPhone 6 Plus";case"Apple A9 GPU":return"iPhone 6s Plus";case"Apple A10 GPU":return"iPhone 7 Plus";case"Apple A11 GPU":return"iPhone 8 Plus"}else if(window.screen.height/window.screen.width==667/375&&2===window.devicePixelRatio)switch(e){default:return"iPhone";case"Apple A8 GPU":return"iPhone 6";case"Apple A9 GPU":return"iPhone 6s";case"Apple A10 GPU":return"iPhone 7";case"Apple A11 GPU":return"iPhone 8"}else if(window.screen.height/window.screen.width==1.775&&2===window.devicePixelRatio)switch(e){default:return"iPhone";case"PowerVR SGX 543":return"iPhone 5 or 5c";case"Apple A7 GPU":return"iPhone 5s";case"Apple A8 GPU":return"iPhone 6";case"Apple A9 GPU":return"iPhone SE or 6s";case"Apple A10 GPU":return"iPhone 7";case"Apple A11 GPU":return"iPhone 8"}else if(window.screen.height/window.screen.width==1.5&&2===window.devicePixelRatio)switch(e){default:return"iPhone 4 or 4s";case"PowerVR SGX 535":return"iPhone 4";case"PowerVR SGX 543":return"iPhone 4s"}else{if(window.screen.height/window.screen.width!=1.5||1!==window.devicePixelRatio)return"iPhone";switch(e){default:return"iPhone 1, 3G or 3GS";case"ALP0298C05":return"iPhone 3GS";case"S5L8900":return"iPhone 1, 3G"}}}()}var p={isMpaaS:function(){return window.navigator.userAgent.indexOf("NebulaSDK")>-1},jsBridgeReady:function(e){window.AlipayJSBridge?e&&e():document.addEventListener("AlipayJSBridgeReady",e,!1)},encodeStr:function(e){return"string"==typeof e?e.replace(/=|,|\^|\$\$/g,function(e){switch(e){case",":return"%2C";case"^":return"%5E";case"$$":return"%24%24";case"=":return"%3D";case"&&":return"%26%26";default:return" "}}):e},dateFormat:function(e){var t=e.getFullYear(),r=e.getMonth()+1;r=1===r.toString().length?"0".concat(r):r;var n=e.getDate();n=1===n.toString().length?"0".concat(n):n;var o=e.getHours();o=1===o.toString().length?"0".concat(o):o;var i=e.getMinutes();i=1===i.toString().length?"0".concat(i):i;var a=e.getSeconds();a=1===a.toString().length?"0".concat(a):a;var c=e.getMilliseconds();return"".concat(t,"-").concat(r,"-").concat(n," ").concat(o,":").concat(i,":").concat(a,".").concat(c)},detect:{os:c,osVersion:f,brand:d}};function y(e){"@babel/helpers - typeof";return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,w(n.key),n)}}function w(e){var t=function(e,t){if("object"!==y(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===y(t)?t:String(t)}var v=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},b=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.logTag="[mTracker]",this.mtrDebug=!1,this.autoStart=!0,this._extParams={},this._init(t),this._validate(),this._logPv(),this._startAutoClick()}return function(e,t,r){t&&h(e.prototype,t),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1})}(e,[{key:"setUserId",value:function(e){this.userId=e}},{key:"click",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bizType,n=t.ext;this._remoteLog({seedId:e,bizType:r,ext:Object.assign({},this._extParams,n),actionId:"click"})}},{key:"expo",value:function(e,t){var r=t.bizType,n=t.ext;this._remoteLog({seedId:e,bizType:r,ext:Object.assign({},this._extParams,n),actionId:"exposure"})}},{key:"_logPv",value:function(){}},{key:"_validate",value:function(){var e=this._isValid();e.result||console.error(this.logTag,e.message)}},{key:"_init",value:function(e){var t=(e||{}).extendParams;window._to&&(Object.assign(this,window._to),t=window._to.extendParams),t&&("object"===y(t)?this._extParams=t:console.warn("[mTracker] extendParams 格式错误"))}},{key:"_changeExt",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===y(e)?this._extParams=e:console.warn("[mTracker] 扩展参数格式错误")}},{key:"_startAutoClick",value:function(){var e=this;document.addEventListener("click",function(t){for(var r=t.target;r&&"BODY"!==r.tagName;){for(var n=void 0,o=void 0,i={},a=0;a<r.attributes.length;a+=1){var c=r.attributes[a];"data-seed"===c.name?n=v(c.value):/^data-mtr-/.test(c.name)?i[c.name.replace("data-mtr-","")]=v(c.value):"data-biztype"===c.name&&(o=v(c.value))}if(n){e.click(n,{ext:i,bizType:o});break}r=r.parentElement}})}},{key:"_isValid",value:function(){return this.server?this.appId?this.workspaceId?this.h5version?{result:!0}:{result:!1,message:"请配置H5页面版本"}:{result:!1,message:"请配置workspaceId"}:{result:!1,message:"请配置appId"}:{result:!1,message:"请配置mtracker服务接收地址"}}},{key:"_formatExtParams",value:function(t){var r=["userAgent=".concat(p.encodeStr(window.navigator.userAgent)),"fullURL=".concat(p.encodeStr(window.document.URL))];return this.bizScenario&&r.push("mBizScenario=".concat(p.encodeStr(this.bizScenario))),Object.keys(t).forEach(function(e){r.push("".concat(e,"=").concat(p.encodeStr(t[e])))}),r.join(e._getExtJoinChar())}},{key:"_guid",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}},{key:"_remoteLog",value:function(e){var t=this._formatData(e);this.send({url:this.server,data:t})}}],[{key:"_getExtJoinChar",value:function(){return"^"}}]),e}();function g(e){"@babel/helpers - typeof";return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,P(n.key),n)}}function P(e){var t=function(e,t){if("object"!==g(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===g(t)?t:String(t)}function x(e,t){return(x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function S(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=_(e);if(t){var o=_(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===g(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function _(e){return(_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var O=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&x(e,t)}(r,b);var t=S(r);function r(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),t.apply(this,arguments)}return function(e,t,r){t&&m(e.prototype,t),r&&m(e,r),Object.defineProperty(e,"prototype",{writable:!1})}(r,[{key:"_logPv",value:function(){this._remoteLog({seedId:window.location.href,actionId:"auto_openPage",ext:this._extParams})}},{key:"_formatData",value:function(e){var t=e.seedId,r=e.ext,n=void 0===r?{}:r,o=e.actionId,i=e.bizType;return["D-VM",p.dateFormat(new Date),"".concat(this.appId||"","_H5-").concat(this.workspaceId||""),this.h5version,"2","-",this._getSessionId(),this.userId||"-",o,"-","".concat(document.referrer||"-","|"),"-","-","-","-",t,"-",i||"UserBehaviorH5","-","-","-","-",this._formatExtParams(n)||"-","-","-",this._getUUid(),"-","-","-","-","-","-","-",p.detect.brand,p.detect.osVersion,"-","-","-",window.navigator.language||window.navigator.userLanguage||"-","-","-","-","-","-","-","".concat(window.screen.width,"x").concat(window.screen.height),"-","-"]}},{key:"_getSessionId",value:function(){var e=window.sessionStorage,t="mtr-mdap";return e?(e[t]||(e[t]=this._guid()),e[t]):""}},{key:"_getUUid",value:function(){var e=window.localStorage,t="mtr-mdap";return e?(e[t]||(e[t]="".concat(this._guid(),"-").concat(Date.now())),e[t]):""}},{key:"send",value:function(e){var t=e.url,r=e.data,n=window.encodeURIComponent,o=new window.XMLHttpRequest;t&&(this.mtrDebug&&window.console.log(this.logTag,{url:t,data:n(r)}),o.open("POST",t,!0),o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.send("data=".concat(n(r))))}}]),r}();function A(e){"@babel/helpers - typeof";return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,k(n.key),n)}}function k(e){var t=function(e,t){if("object"!==A(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==A(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===A(t)?t:String(t)}function E(e,t){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function T(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=R(e);if(t){var o=R(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===A(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function R(e){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var I=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&E(e,t)}(r,b);var t=T(r);function r(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),t.apply(this,arguments)}return function(e,t,r){t&&j(e.prototype,t),r&&j(e,r),Object.defineProperty(e,"prototype",{writable:!1})}(r,[{key:"_validate",value:function(){}},{key:"_remoteLog",value:function(e){var t=this,r=e.seedId,n=e.ext,o=void 0===n?{}:n,i=e.actionId,a=e.bizType;p.jsBridgeReady(function(){t.mtrDebug&&window.console.log(t.logTag,"remoteLog",{seedId:r,param4:t._formatExtParams(o),actionId:i,bizType:a}),AlipayJSBridge.call("remoteLog",{seedId:r,param4:t._formatExtParams(o),actionId:i,bizType:a})})}}],[{key:"_getExtJoinChar",value:function(){return","}}]),r}();window.initTracker=function(){var e;e=p.isMpaaS()?new I:new O,window.Tracker=e,window.changeTrackerExtendParams=function(t){e._changeExt(t)}},window.notInitTrackerOnStart||window.initTracker()}]);