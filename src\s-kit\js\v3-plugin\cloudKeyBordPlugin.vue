<template>
  <div>
    <password-instance ref="initPassword"></password-instance>
  </div>
</template>

<script>
import Store from '../../../store/index';
import Config from '../third-config.js';
import LAYER from '../layer';
import { logoutApi } from '@/s-kit/js/v3-http/https3/user';
export default {
  data() {
    return {
    }
  },
  props: {
    // showIframe: {
    //   type: Boolean,
    //   default: false,
    // },
    passwordType: {
      type: String,
      default: 'setup',
    },
  },
  created() {
  },
  async mounted() {
  },
  methods: {
    /**
       * appid 必填 对外开放平台分配的应用编码appid
           terminalId 必填 终端唯一ID
          token 必填 网关验证的token
          clientCode 必填 网关对应的渠道
      * */
    initRef() {
      return new Promise(async (resolve, reject) => {
        const { gsmsToken } = uni.getStorageSync('tokenInfo') || {};
        const deviceId = uni.getSystemInfoSync()?.deviceId || ''
        if (!gsmsToken) {
          return
        }

        let params = {
          appid: Config.cloudappId,
          // appid: '327b5a30cd23d08c',
          terminalId: deviceId,
          token: gsmsToken,
          clientCode: Config.clientCode,
          baseType: Config.baseType,
        }
        console.log('plugin---params', params)
        try {
          const passwordInstance = this.$refs.initPassword;
          const result = await passwordInstance.init(params)
          console.log(result, 'result----keyboardPlugin')
          if (Number(result.code) !== 200) {
            uni.showToast({
              title: result.msg,
              icon: 'none',
              duration: 2000,
            });
            return reject(result);
          }
          console.log('Store', Store)
          // Store.commit('setAccountDataPlugin', passwordInstance)
          // Object.assign(this, passwordInstance);
          console.log('cloud keybord初始化成功');
          resolve(passwordInstance);

        } catch (err) {
          console.log('cloud keybord初始化失败', err);
          reject(err);
        }
      });
    },
    // 支付插件所需
    keyboardInstance() {
      // console.log('9999',)
      return this.$refs.initPassword;
    }
  },
  computed: {

  },
  components: {

  },
}
</script>
<style scoped lang='scss'></style>
