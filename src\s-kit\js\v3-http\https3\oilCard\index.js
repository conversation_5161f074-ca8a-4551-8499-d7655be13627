import utils from '../../../../../utils/index';
import { POST, POST2 } from '../../index';
import { api } from '../../../../../../project.config';
// 已绑定的油卡列表
export const cardList = (params, config) => {
    return POST('card.bindCardList', params, config);
};
// 获取用户信息
export const identityAuthInfo = (params, config) => {
    return POST('user.getIdentityAuthInfo', params, config);
};
// 发送验证码
export const sendMessageCode = (params, config) => {
    return POST('card.sendMessageCode', params, config);
};
// 校验短信验证码
export const checkMessageCode = (params, config) => {
    return POST('card.checkMessageCode', params, config);
};
// 初始化实人认证接口
export const initRealPersonIdentify = (params, config) => {
    return POST('user.memberInfo.initRealPersonIdentify', params, config);
};
// 实名认证
export const realNameAuth = (params, config) => {
    return POST('user.realNameAuth', params, config);
};
// 实人认证 --TODO 未通依赖于核验人脸
export const realPersonIdentify = (params, config) => {
    return POST('user.memberInfo.realPersonIdentify', params, config);
};
// 掌纹开卡换取签名
export const generateWxSign = (params, config) => {
    return POST('user.identityAuth.generateWxSign', params, config);
};
// 掌纹开卡认证申请
export const wxPalmprintAuth = (params, config) => {
    return POST('user.identityAuth.wxPalmprintAuth', params, config);
};

// 绑定加油卡
export const bindCard = (params, config) => {
    return POST('card.bindCard', params, config);
};
// 曾用卡列表
export const unbindCardList = (params, config) => {
    return POST('card.unbindCardList', params, config);
};
// 曾用卡绑定
export const fastBindCard = (params, config) => {
    return POST('card.fastBindCard', params, config);
};
// 查询用户详细信息接口
export const basicInfoQuery = (params, config) => {
    return POST('user.basicInfo.query', params, config);
};
// 油卡详情
export const bindCardInfo = (params, config) => {
    return POST('card.bindCardInfo', params, config);
};
// 油卡消费记录 -- 修改需求改为跳转消费订单
export const consumeRecordList = (params, config) => {
    return POST('card.consumeRecordList', params, config);
};
// 资金转出 --TODO 未通
export const rollout = (params, config) => {
    return POST('card.rollout', params, config);
};
// 解除加油卡
export const unBindCard = (params, config) => {
    return POST('card.unBindCard', params, config);
};

// 查询油卡充值记录接口
export const rechargeRecordList = (params, config) => {
    return POST('card.rechargeRecordList', params, config);
};
// 查看隐私协议
export const userAgreement = (params, config) => {
    return POST('user.agreement', params, config);
};
// 查询油卡充值记录接口2.0
export const queryRechargeRecords = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/card/queryRechargeRecords', params, config);
    // #endif
    // #ifdef MP-ALIPAY
    return POST2('/Ali/Api/Card/GetCardTradeList', params, config);
    // #endif
    // #ifdef MP-WEIXIN
    return POST2('/app/json/order2/getRechargeList', params, config);
    // #endif
};
// 查询油卡充值记录详情接口2.0
export const getRechargeDetail = (params, config) => {
    // #ifdef MP-WEIXIN
    return POST2('/app/json/order2/getRechargeDetail', params, config);
    // #endif
};
// 开通昆仑e享卡
export const openAccount = (params, config) => {
    return POST('account.openAccount', params, config);
};
// 查询油卡充值订单
export const orderQuery = (params, config) => {
    return POST2('/app/json/card/orderQuery', params, config);
};
// 查询油卡充值订单昆仑
export const getKLOrderQuery = (params, config) => {
    return POST2('/app/json/card/getKLOrderQuery', params, config);
};
// 查询油卡充值订单
export const queryUnionPayOrder = (params, config) => {
    return POST2('/app/json/card/queryUnionPayOrder', params, config);
};
// 查询油卡充值订单鲲鹏
export const queryKunPengPay = (params, config) => {
    return POST2('/app/json/card/queryKunPengPay', params, config);
};
// 查询油卡充值订单建行
export const queryCCBPay = (params, config) => {
    return POST2('/app/json/card/queryCCBPay', params, config);
};
// 加油卡充卡=》充值卡
export const rechargeByCardPost = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/card/rechargeByCard', params, config);
    // #endif
    // #ifdef MP-WEIXIN
    return POST2('/app/json/third/rechargeByCard', params, config);
    // #endif
    // #ifdef MP-ALIPAY
    return POST2('/Ali/Api/Card/RechargeCardRecharge', params, config);
    // #endif
};
// 油卡充值昆仑支付预下单
export const getKLPayOrderInfo = (params, config) => {
    return POST2('/app/json/card/getKLPayOrderInfo', params, config);
};
// 油卡充值银联支付预下单
export const getUnionPayOrder = (params, config) => {
    return POST2('/app/json/card/getUnionPayOrder', params, config);
};
// 油卡充值鲲鹏支付预下单
export const getKunPengPay = (params, config) => {
    return POST2('/app/json/card/getKunPengPay', params, config);
};
// 油卡充值建行支付预下单
export const getCCBPay = (params, config) => {
    return POST2('/app/json/card/getCCBPay', params, config);
};
// 油卡充值接口
export const paycard = (params, config) => {
    params = {
        ...params,
        nonce: utils.generateUUID(),
        timestamp: new Date().getTime(),
    };
    return POST2('/app/json/cnpc_card/preOrder', params, config);
};

// 获取花呗分期数据 2.0 链路
export const getAliHBPayList = (params, config) => {
    return POST2('/Ali/Api/Alipay/GetAliHBPayConfigList', params, config);
};

// alipay 油卡充值
export const addOrderInfo = (params, config) => {
    return POST2(api + 'apppay/v1/addOrderInfo', params, config);
};

// 获取可售实体卡站点 /card/getMemberCardSellSites
export const getMemberCardSellSites = (params, config) => {
    return POST('card.getMemberCardSellSites', params, config);
};

// 实体卡申领请求接口 /card/submitMemberCardApply
export const submitMemberCardApply = (params, config) => {
    return POST('card.submitMemberCardApply', params, config);
};

// 查询待领取售卡申请或卡信息 /card/getMemberCardApply
export const getMemberCardApply = (params, config) => {
    return POST('card.getMemberCardApply', params, config);
};

// 撤销昆仑e享卡申领请求 /card/cancelMemberCardApply
export const cancelMemberCardApply = (params, config) => {
    return POST('card.cancelMemberCardApply', params, config);
};
// 申请退款接口 /account/applyRefund
export const applyRefund = (params, config) => {
    return POST('account.applyRefund', params, config);
};
// 查看退款信息接口 /account/applyRefund/query
export const applyRefundQuery = (params, config) => {
    return POST('account.applyRefund.query', params, config);
};
// 取消退款接口 /account/applyRefund/cancel
export const applyRefundCancel = (params, config) => {
    return POST('account.applyRefund.cancel', params, config);
};
// 根据证件号查询用户名下未绑定加油卡列表接口 /card/queryUnBindCardList
export const getQueryUnBindCardList = (params, config) => {
    return POST('card.queryUnBindCardList', params, config);
};
// 未绑定实体卡资金归集发送短信验证码接口 /card/message/send
export const messageSend = (params, config) => {
    return POST('card.message.send', params, config);
};
// 未绑定实体卡资金归集接口 /card/unBindCard/fundTransfer
export const unBindCardFundTransfer = (params, config) => {
    return POST('card.unBindCard.fundTransfer', params, config);
};
// 根据卡号查询用户名下未绑定实体卡详情接口 /card/unBindCard/detail
export const unBindCardDetail = (params, config) => {
    return POST('card.unBindCard.detail', params, config);
};
