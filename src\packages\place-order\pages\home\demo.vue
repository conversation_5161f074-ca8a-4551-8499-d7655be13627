<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="refuel-select-oilgun">
        <Navigation v-if="isShowNav" title="加油支付" hasBack="0"></Navigation>
        <div class="content-div" :style="{ top: topHeight + 'px' }">
            <div class="header">
                <img class="full-img" src="@/static/image/refuel/default-GasStation2.png" />
                <div class="tips">温馨提示：加油站请勿下车使用手机!</div>
            </div>

            <div class="panel">
                <div class="info-panel">
                    <div class="info-panel-top">
                        <div class="left">
                            <div class="name">
                                {{ getName(station.stationName) }}
                                <div class="change-btn" @click="changeStation()">更改油站</div>
                            </div>
                            <div class="address">
                                <img src="@/assets/refuel/location-orange.png" />
                                {{ station.address }}
                            </div>
                            <div class="tag-box">
                                <div
                                    class="tag-item"
                                    :class="{ recently: text === '距离最近' }"
                                    v-for="(text, index) in station.serviceImageUrl"
                                    :key="index"
                                    >{{ text }}</div
                                >
                            </div>
                        </div>
                        <div class="right" @click="gotoStation">
                            <img src="@/assets/refuel/position.png" alt />
                            <div class="distance">
                                {{ station.distance * 1 > 1 ? station.distance + 'km' : station.distance * 1000 + 'm' }}
                            </div>
                        </div>
                    </div>
                    <div class="custom-text">
                        <van-notice-bar :scrollable="false" v-if="discountActivities.length != 0">
                            <van-swipe vertical class="areamarket-class" :autoplay="3000" :show-indicators="false">
                                <div v-for="(activity, index) in discountActivities" :key="index" @click="activityClick(activity)">
                                    <van-swipe-item>
                                        <div class="areamarket-item">
                                            <img :src="activity.imgLink" />
                                            {{ activity.name }}
                                        </div>
                                    </van-swipe-item>
                                </div>
                            </van-swipe>
                        </van-notice-bar>
                    </div>
                </div>
                <common-car></common-car>
                <div class="select-panel">
                    <div class="oil-box" v-if="oilList.length > 0">
                        <div class="oil-title">选择油号</div>
                        <div class="oil-list">
                            <div
                                class="oil-item"
                                :class="{ active: oilNoInfo == item }"
                                v-for="(item, index) in oilList"
                                :key="index"
                                @click="oilNoInfo = item"
                                >{{ item.oilProductName }}</div
                            >
                        </div>
                    </div>
                    <div class="gun-box">
                        <div class="gun-title">
                            选择油枪
                            <div class="tips">请向加油员确认枪号</div>
                        </div>
                        <div class="gun-list">
                            <div
                                class="gun-item"
                                :class="{ active: gunNo == item }"
                                v-for="(item, index) in oilGunNoList"
                                :key="index"
                                @click="gunNo = item"
                                >{{ item }}</div
                            >
                            <input
                                class="input-item"
                                type="number"
                                placeholder="手动输入"
                                oninput="if(value.length>2)value=value.slice(0,2)"
                                @input="inputEvent()"
                                v-model="inputGunNo"
                            />
                        </div>
                    </div>

                    <div class="submit-btn" @click="sureEvent()">查询订单</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import { MessageBox, Toast } from 'mint-ui'
import Navigation from '@/components/base/navigation';
import CommonCar from '@/components/Vendor/common-car/index';
import etcUtil from '@/utils/etcUtil.js';

export default {
    name: 'refuel-select-oilgun',
    components: {
        // Toast,
        // MessageBox,
        Navigation,
        CommonCar,
    },
    data() {
        return {
            station: {},
            longitude: '', //经度
            latitude: '', //纬度
            topHeight: 0,
            isShowNav: true,
            isHavePowerForLocation: false,
            oilNoInfo: {},
            gunNo: '',
            inputGunNo: '',
            discountActivities: [],
            oilList: [], //油品列表'95#汽油', '98#汽油', '0#柴油', '-10#柴油', '-20#柴油', '-35#柴油'
            defaultGunNoList: '1号,2号,3号,4号,5号,6号,7号,8号,9号,10号,11号,12号,13号,14号,15号',
            defaultCar: {},
            locationTimer: null,
            isFold: false, //建行联合会员点进来，折叠其他的支付方式
        };
    },
    methods: {
        gotoStation() {
            this.$bridgefunc.getLocation(locationinfo => {
                this.$bridgefunc.baiduNav({
                    startlong: locationinfo.longitude,
                    startlat: locationinfo.latitude,
                    endlong: this.station.posX,
                    endlat: this.station.posY,
                    endName: encodeURIComponent(this.station.stationName),
                    type: 'nav',
                });
            });
        },
        activityClick(item) {
            if (item.activityLink != '')
                this.$bridgefunc.newPage({
                    weburl: item.activityLink,
                    isnativetop: 1,
                });
        },
        inputEvent() {
            this.gunNo = this.inputGunNo;
        },
        changeStation: function () {
            if (!this.isHavePowerForLocation) {
                this.$MessageBox.confirm('您需要开启定位权限，才能使用此功能!', '温馨提示', { confirmButtonText: '去开启' }).then(res => {
                    this.$bridgefunc.openPermissionsSetting();
                });
                return;
            }
            this.$bridgefunc.customPush({
                path: '/refuelstationlist',
                isnativetop: '1',
            });
        },
        sureEvent: function () {
            if (!this.isHavePowerForLocation) {
                this.$MessageBox.confirm('您需要开启定位权限，才能使用此功能!', '温馨提示', { confirmButtonText: '去开启' }).then(res => {
                    this.$bridgefunc.openPermissionsSetting();
                });
                return;
            }
            if (Object.keys(this.oilNoInfo).length == 0) {
                this.$Toast('油号输入有误');
                return;
            }
            if (this.gunNo == '') {
                this.$Toast('枪号输入有误');
                return;
            }
            console.log('this.station', this.station);
            if (!this.station['stationCode']) {
                Toast({
                    message: '请选择油站',
                    position: 'bottom',
                    duration: 2000,
                });
                return;
            }
            // if (this.station.distance > 1) {
            //   this.$MessageBox.alert('距离加油站超过1公里，请进入加油站支付', '提示');
            //   return
            // }
            this.saveStationCaChe();
            console.log('saveStationCaChe-------------------------------');
            // this.$bridgefunc.customPush({
            //   path: '/refuelorder',
            //   isnativetop: '1',
            //   query: {
            //     station: encodeURIComponent(JSON.stringify(this.station)),
            //     gunNo: encodeURIComponent(this.gunNo),
            //     isFold: this.isFold
            //   },
            // })
        },
        saveStationCaChe() {
            this.$bridgefunc.getItem('OilGunCache', res => {
                let stationArr = [];
                if (res && res != '') {
                    console.log('缓存油站1', decodeURIComponent(res));
                    stationArr = JSON.parse(decodeURIComponent(res));
                }
                let index = stationArr.findIndex(item => item.stationCode == this.station.stationCode);
                if (index != -1) {
                    //证明缓存文件中该油站信息，更新一下
                    stationArr.splice(index, 1);
                    stationArr.push({
                        stationCode: this.station.stationCode, //站编码
                        gunNo: this.gunNo || '', //枪号
                        oilCode: this.oilNoInfo.oilCode || '', //油品号
                    });
                } else {
                    //没有缓存该油站信息
                    if (stationArr.length >= 3)
                        //如果大于等于3，删掉第一个元素，默认只保存用户最近的三个
                        stationArr.shift();
                    stationArr.push({
                        stationCode: this.station.stationCode, //站编码
                        gunNo: this.gunNo || '', //枪号
                        oilCode: this.oilNoInfo.oilCode || '', //油品号
                    });
                }
                this.$bridgefunc.setItem('OilGunCache', encodeURIComponent(JSON.stringify(stationArr)));
            });
        },
        getStationList: function () {
            // this.$Loading.open();
            let url = '/app/json/refuel/getStationInfos';
            // let url = '/app/json/refuel/getStationList';
            let _this = this;
            // let params = new FormData();
            let params = {
                lng: _this.longitude,
                lat: _this.latitude,
                token: _this.$store.state.login.token,
                pageIndex: '1',
            };
            this.$http.post(url, params).then(
                res => {
                    _this.$Loading.close();
                    let data = res.data;
                    if (data.status == 0) {
                        _this.station = data.data[0];
                        _this.listDiscountActivities();
                        _this.listOilProduct();
                    } else {
                        Toast({
                            message: data.info ? data.info : '获取附近加油站失败',
                            position: 'bottom',
                            duration: 2000,
                        });
                    }
                },
                error => {
                    _this.$Loading.close();
                    Toast({
                        message: '获取附近加油站失败',
                        position: 'bottom',
                        duration: 2000,
                    });
                },
            );
        },
        listDiscountActivities: function () {
            let url = '/app/json/card/listDiscountActivities';
            let params = {
                token: this.$store.state.login.token,
                stationCode: this.station.stationCode,
            };
            this.$http.post(url, params).then(
                res => {
                    let data = res.data;
                    if (data.status == 0) {
                        this.discountActivities = data.data || [];
                    } else {
                        Toast({
                            message: data.info ? data.info : '获取附近加油站优惠活动失败',
                            position: 'bottom',
                            duration: 2000,
                        });
                    }
                },
                error => {
                    Toast({
                        message: '获取附近加油站优惠活动异常',
                        position: 'bottom',
                        duration: 2000,
                    });
                },
            );
        },
        listOilProduct: function () {
            let url = '/app/json/card/listOilProduct';
            let params = {
                token: this.$store.state.login.token,
                stationCode: this.station.stationCode,
            };
            this.$http.post(url, params).then(
                res => {
                    let data = res.data;
                    if (data.status == 0) {
                        this.oilList = data.data.length > 0 ? data.data : [];
                        if (this.oilList.length > 0) {
                            //根据默认车辆选择油品
                            this.$bridgefunc.getItem('OilGunCache', res => {
                                if (res && res != '') {
                                    let stationArr = JSON.parse(decodeURIComponent(res));
                                    let caCheItem = stationArr.find(item => item.stationCode == this.station.stationCode);
                                    if (caCheItem) {
                                        this.oilNoInfo = this.oilList.find(item => item.oilCode == caCheItem.oilCode) || this.oilList[0];
                                        this.gunNo = caCheItem.gunNo;
                                        console.log(this.oilNoInfo, caCheItem, '======================');
                                        return;
                                    }
                                }
                            });
                            this.defaultCar = this.$store.state.defaultCar;
                            if (this.defaultCar.CarId != '') {
                                this.oilNoInfo = this.oilList.find(item => item.oilCode == this.defaultCar.OilType) || this.oilList[0];
                            } else this.oilNoInfo = this.oilList[0];
                        } else {
                            //没有油品信息，直接默认油枪号
                            this.$set(this.oilNoInfo, 'oilGunNo', this.defaultGunNoList);
                            this.$bridgefunc.getItem('OilGunCache', res => {
                                if (res && res != '') {
                                    let stationArr = JSON.parse(decodeURIComponent(res));
                                    let caCheItem = stationArr.find(item => item.stationCode == this.station.stationCode);
                                    if (caCheItem) {
                                        this.gunNo = caCheItem.gunNo;
                                        return;
                                    }
                                }
                            });
                        }
                    } else {
                        Toast({
                            message: data.info ? data.info : '获取油品号失败',
                            position: 'bottom',
                            duration: 2000,
                        });
                    }
                },
                error => {
                    Toast({
                        message: '获取油品号异常',
                        position: 'bottom',
                        duration: 2000,
                    });
                },
            );
        },
        setData() {
            if (this.$route.query.station) {
                //油站导航列表跳转过来的
                console.log(decodeURIComponent(this.$route.query.station));
                this.station = JSON.parse(decodeURIComponent(this.$route.query.station));
                this.listDiscountActivities();
                this.listOilProduct();
                this.isShowNav = false;
                this.topHeight = 0;
                let _this = this;
                // this.$bridgefunc.registePageShow(function () {
                //   _this.$bridgefunc.vuexRead()
                // })
                // 检查权限是否打开
                // this.$Loading.open()
                this.$bridgefunc.getLocation(function (locationinfo) {
                    _this.$Loading.close();
                    _this.isHavePowerForLocation = locationinfo.state == '1';
                    if (!_this.isHavePowerForLocation) {
                        // _this.$MessageBox.alert('您需要打开定位权限，才能使用此功能!', '提示');
                        return;
                    }
                }, true);
            } else {
                //直接下导航过来的
                this.topHeight = this.$store.state.barHeight + this.$market.getNavHeight();
                this.$bridgefunc.getLocation(locationinfo => {
                    this.isHavePowerForLocation = locationinfo.state == '1';
                    if (!this.isHavePowerForLocation) {
                        // _this.$MessageBox.alert('您需要打开定位权限，才能使用此功能!', '提示');
                        this.startInterval();
                        return;
                    }
                    this.longitude = locationinfo.longitude;
                    this.latitude = locationinfo.latitude;
                    this.getStationList();
                }, true);
            }
        },
        getName(val) {
            if (!val || val.length < 11) {
                return val || '';
            }
            return val.substring(0, 10) + '...';
        },
        startInterval() {
            if (!this.locationTimer)
                this.locationTimer = setInterval(() => {
                    this.$bridgefunc.getLocation(locationinfo => {
                        console.log(locationinfo);
                        if (locationinfo.state == '1') {
                            this.stopInterval();
                            // 定位成功
                            this.setData();
                        }
                    });
                }, 3000);
        },
        stopInterval() {
            if (this.locationTimer) {
                clearInterval(this.locationTimer);
                this.locationTimer = null;
            }
        },
        // 跳转至车辆管理
        goCarManagement: function () {
            this.$bridgefunc.customPush({
                path: '/mycarlist',
                isnativetop: 0,
                query: {
                    carList: encodeURIComponent(JSON.stringify([])),
                },
            });
        },
        isShow(isShow) {
            if (isShow) {
                // 20220602
                // if (!this.$store.state.refuelStation.stationCode) {
                //   this.setData()
                // }
                if (!this.isHavePowerForLocation) this.startInterval();
            } else {
                this.stopInterval();
            }
        },
    },
    watch: {
        '$store.state.refuelStation.stationCode': function (val, oldVal) {
            if (this.$store.state.refuelStation.stationCode != '') {
                this.station = this.$util.cloneDeep(this.$store.state.refuelStation);
                if (this.station.stationName.length > 10) {
                    this.station.stationName = this.station.stationName.substring(0, 10) + '...';
                }
                for (let key in this.$store.state.refuelStation) {
                    this.$store.state.refuelStation[key] = '';
                }
                this.$bridgefunc.vuexStorage();
                this.listDiscountActivities();
                this.listOilProduct();
            }
        },
        '$store.state.defaultCar.CarId': function (val, oldVal) {
            if (this.$store.state.defaultCar.CarId != '') {
                this.defaultCar = this.$store.state.defaultCar;
                if (this.oilList.length > 0)
                    this.oilNoInfo = this.oilList.find(item => item.oilCode == this.defaultCar.OilType) || this.oilList[0];
            } else {
                if (this.oilList.length > 0) this.oilNoInfo = this.oilList[0];
            }
        },
    },
    computed: {
        oilGunNoList() {
            console.log(this.oilNoInfo.oilGunNo, '我是什么');
            if (Object.keys(this.oilNoInfo).length != 0) {
                let oilGunNoListTmp =
                    this.oilNoInfo.oilGunNo.indexOf(',') != -1 ? this.oilNoInfo.oilGunNo.split(',') : [this.oilNoInfo.oilGunNo];
                this.gunNo = oilGunNoListTmp[0];
                return oilGunNoListTmp;
            } else return [];
        },
    },
    mounted() {
        if (this.$route.path != '/common') {
            let _this = this;
            this.$bridgefunc.registePageShow(function () {
                _this.$bridgefunc.vuexRead();
            });
        }
    },
    created() {
        this.isFold = this.$route.query.isFold || false;
        console.log('isFold', this.isFold);
        this.setData();
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="stylus" scoped type="text/stylus">
@import '~@/common/stylus/variable.styl'
.refuel-select-oilgun
  position relative
  width 100%
  height 100%
  background-color $color-background-lll
  overflow auto
.content-div
  position absolute
  bottom 0
  right 0px
  left 0px
  -webkit-overflow-scrolling touch
  overflow-y auto
  overflow-x hidden
  .select-panel
    margin-top 5px
    background #fff
    padding 13px 16px
    margin-bottom 20px
    border-radius 10px
    item()
      width calc(((100% - 18px) / 4))
      height 41px
      line-height 41px
      background #F6E7D5
      border-radius 5px
      text-align center
      font-size 15px
      color #FF6600
      margin-right 6px
      margin-bottom 6px
      &:nth-child(4n+4)
        margin-right 0px
      &.active
        background #F96702
        color #fff
    .oil-box
      .oil-title
        font-size 16px
        color #333333
      .oil-list
        margin-top 8px
        display flex
        flex-wrap wrap
        .oil-item
          item()
    .gun-box
      margin-top 6px
      .gun-title
        font-size 16px
        color #333333
        display flex
        justify-content space-between
        align-items center
        .tips
          font-size 12px
          color #999999
      .gun-list
        margin-top 10px
        display flex
        flex-wrap wrap
        .gun-item
          item()
        .input-item
          width calc(((100% - 18px) / 4))
          height 41px
          line-height 41px
          background #F5F5F5
          border-radius 5px
          text-align center
          font-size 15px
    .submit-btn
      margin-top 3px
      height 44px
      line-height 44px
      background $color-theme-o
      border-radius 5px
      font-size 17px
      font-weight bold
      color #FFFFFF
      text-align center
  .header
    /* position relative */
    /* padding-bottom 178px */
    .full-img
      height 178px
    .tips
      height 20px
      line-height 20px
      background #FF6600
      opacity 0.43
      font-size 12px
      color #FFFFFF
      text-align center
      position absolute
      top 0px
      left 0px
      width 100%
  .car-panel
    margin-top 5px
    height 41px
    background #F7ECE1
    padding 7px 8px 7px 13px
    border-radius 5px
    display flex
    justify-content space-between
    .right
      display flex
      align-items center
      img
        width 17px
        height 15px
      span
        margin-left 4px
        font-size 12px
        color #666666
    .left
      display flex
      align-items center
      .label
        font-size 16px
        font-weight bold
        color #333333
        line-height 35px
      .val
        height 28px
        font-size 15px
        color #333333
        margin-left 11px
        border 1px solid #FF6600
        border-radius 50px
        padding-left 10px
        display flex
        align-items center
        .oil
          padding 0 8px
          height 26px
          line-height 26px
          margin-left 4px
          background #FF6600
          border-radius 0px 13px 13px 13px
          font-size 15px
          color #FFFFFF
  .panel
    padding 0 13px
    position absolute
    width 100%
    top 104px
    .info-panel
      background #fff
      padding 16px
      padding-right 13px
      border-radius 10px
      display flex
      flex-direction column
      justify-content space-between
      .info-panel-top
        display flex
        align-items center
        justify-content space-between
        .right
          padding-left 10px
          flex-shrink 0
          img
            width 48px
            height 45px
          .distance
            text-align center
            font-size 12px
            color #999999
        .left
          .name
            font-size 17px
            color #000000
            display flex
            align-items center
            .change-btn
              margin-left 10px
              padding 0 5px
              height 19px
              border 1px solid #F96702
              border-radius 5px
              font-size 12px
              color #F96702
          .address
            margin-top 10px
            display flex
            align-items center
            font-size 12px
            color $color-text-l
            img
              width 10px
              height 14px
              margin-right 4px
          .tag-box
            margin-top 7px
            display flex
            flex-wrap wrap
            .tag-item
              padding 0 3px
              height 16px
              border 1px solid #ccc
              border-radius 3px
              font-size 12px
              line-height 16px
              color #999999
              text-align center
              margin-right 4px
              margin-bottom 4px
              &.recently
                width 60px
          .discount-info
            margin-top 8px
            display flex
            align-items center
            font-size 14px
            color $color-text-d
            img
              width 20px
              height 20px
              margin-right 5px
            .discount-tag
              flex-shrink 0
              margin-right 5px
              width 20px
              height 20px
              line-height 20px
              background #F96702
              border-radius 5px
              font-size 12px
              color #FEFEFE
              text-align center
      .custom-text
        width 100%
        margin-top 5px
        border-radius 5px
        overflow hidden
        .areamarket-class
          height 30px
          line-height 30px
          .areamarket-item
            display flex
            align-items center
            img
              width 20px
              margin-right 5px
              height 20px
</style>
<style lang="stylus" type="text/stylus">
.refuel-select-oilgun
  .van-notice-bar
    padding 0 5px
    background #FEF3D8
    color #CC782E
    height 35px
    font-size 12px
</style>
