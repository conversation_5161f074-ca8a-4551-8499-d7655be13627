<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas view">
        <zj-navbar title="实体卡申领" :border-bottom="false"></zj-navbar>
        <div class="fl-column fl-column mh-0 padding-16">
            <div class="content">
                <div class="select-box">
                    <div class="font-13 weight-400 color-999">请选择您的昆仑e享卡实体卡领取加油网点</div>
                    <div class="font-14 weight-400 color-333 mar-top-12">您的昆仑e享卡归属地为: {{ walletInfo.addressName }}</div>
                    <div class="substance-tip-right">
                        <div @click="toSelectStation" class="input fl-row fl-jus-bet fl-al-cen border-rad-8">
                            <div>{{ walletSelectStation.orgName || '' }}</div>
                            <img src="../../images/triangle-gray.png" alt="" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-box">
                <div class="btn" @click="submit"> 提交申请 </div>
            </div>
            <div class="bottom-content border-rad-8">
                <div class="font-14 weight-400 color-E64F22">温馨提示：</div>
                <div class="font-12 weight-400 color-333 mar-top-12">
                    <div>1.提交申请后需携带个人有效证件到达选择领取实体卡油站网点进行领取； </div>
                    <div>2.自然年昆仑e享卡注销满5次后，不可以进行实体卡申领；</div>
                    <div>3.账户异常或冻结状态时，不可以进行实体卡申领。</div></div
                >
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { submitMemberCardApply } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {};
    },
    methods: {
        toSelectStation() {
            this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/wallet-select-station/main');
        },
        async submit() {
            if (JSON.stringify(this.walletSelectStation) != '{}') {
                let params = {
                    orgNo: this.walletInfo.addressNo,
                    orgName: this.walletInfo.addressName,
                    mainAccountNo: this.walletInfo.mainAccountNo,
                    stationNo: this.walletSelectStation.orgCode,
                    stationName: this.walletSelectStation.orgName,
                };
                let res = await submitMemberCardApply(params);
                if (res && res.success) {
                    this.$store.dispatch('zjShowModal', {
                        title: '申请成功，请携带个人有效证件前往网点领取',
                        success: res => {
                            uni.navigateBack();
                            this.$store.commit('setWalletSelectStation', {});
                        },
                    });
                }
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: '请选择网点',
                    success: res => {},
                });
            }
        },
    },
    beforeDestroy() {
        this.$store.commit('setWalletSelectStation', {});
    },
    computed: {
        ...mapState({
            walletSelectStation: state => state.wallet.walletSelectStation,
        }),
        ...mapGetters(['walletInfo']),
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #f7f7fb;
    .content {
        .select-box {
            min-height: 256rpx;
            background: #fff;
            padding: 24rpx;
            border-radius: 16rpx;

            .substance-tip-right {
                display: flex;
                align-items: center;
                height: 40px;
                margin-top: 12px;
                .pickerStyle {
                    width: 100%;
                    height: 44px;
                }
                .input {
                    width: 100%;
                    height: 40px;
                    line-height: 40px;
                    background: #f7f7fb;
                    img {
                        width: 8px;
                        height: 6px;
                        margin-right: 12px;
                    }
                }

                .input div:nth-child(1) {
                    margin-left: 12px;
                }
            }
        }
    }

    .btn-box {
        height: 152rpx;
        margin-top: 12px;
        .btn {
            height: 88rpx;
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            text-align: center;
            line-height: 88rpx;
            font-size: 36rpx;
            font-weight: bold;
            color: #ffffff;
            border-radius: 16rpx;
        }
    }
    .bottom-content {
        width: 100%;
        height: 256rpx;
        background: #fff;
        padding: 12px;
    }
}
</style>
