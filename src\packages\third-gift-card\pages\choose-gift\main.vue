<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="选择礼品卡"></zj-navbar>
            <div class="f-1 bg-fff padding-16 p-bf mh-0">
                <zj-data-list
                    background="#F7F7FB"
                    ref="dataList"
                    :showEmpty="showEmpty"
                    :emptyImage="noPicture"
                    @refreshPullDown="refreshPullDown"
                    @scrolltolower="scrolltolower"
                >
                    <div
                        class="coupon-wrap fl-row fl-al-cen"
                        v-for="(item, index) in availableList"
                        :key="index"
                        @click="ticketCellClickAction(item, index)"
                        :style="{ opacity: !item.show && checkSelection ? '0.3' : '1' }"
                    >
                        <div class="select-img-wrap border-rad-8">
                            <img v-if="item.show" src="../../images/successSel.png" alt />
                            <img v-else src="../../images/empty.png" alt />
                        </div>
                        <div class="item-module">
                            <img src="../../images/card-1.png" alt="" class="card-img" />
                            <div class="modole-bg fl-column">
                                <div class="fl-row fl-al-cen fl-jus-bet card-div">
                                    <div class="item-left fl-row fl-al-cen">
                                        <img src="../../images/cnpc-logo.png" alt="" class="logo-img" />
                                        <div class="font-16 color-fff marl5 weight-500">{{ item.giftCardName }}</div>
                                    </div>
                                </div>
                                <div class="fl-row fl-jus-bet padlr15 f-1">
                                    <div class="item-left fl-row fl-al-cen">
                                        <div class="weight-400 font-14 color-fff marr10 mart15">余额</div>
                                        <div class="fl-row color-fff font-18 fl-al-base"
                                            >&yen;<div class="font-40 font-style height57"> {{ item.availableAmount || '0.00' }}</div>
                                        </div>
                                    </div>
                                    <div class="item-right fl-row fl-al-cen color-fff weight-400 font-14">
                                        <div>面值</div>
                                        <div class="marl5">&yen;{{ item.faceAmount || '0.00' }}</div>
                                    </div>
                                </div>
                                <div class="fl-row bottom-area normal padlr15 fl-sp-end fl-al-cen">
                                    <div class="font-14 color-E64F22">
                                        {{ formatTimeFun(item.startDate) }}-{{ formatTimeFun(item.endDate) }}</div
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-data-list>
            </div>
            <div class="primary-btn btn" @click="chooseFinishAction" v-if="availableList.length > 0">确定</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
const currency = require('currency.js');
// 一次请求多少条
const PAGE_SIZE = 20;
import { consumeAvailableList } from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import { clientCode } from '../../../../../project.config';
// import { giftCardList } from '../../../../s-kit/js/v3-http/https3/giftCard/index.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 页码
            page: 1,
            //用户进行消费时可以查询名下可用礼品卡列表
            availableList: [],
            // 总页码
            totalPage: 0,
            // 暂无电子券图片
            noPicture: require('../../images/lpk-no-data.png'),
            // 下单信息
            orderInfo: '',
            // 用户选择可用礼品卡信息
            selectData: [],
            // 是否展示空态标识
            showEmpty: false,
        };
    },

    onLoad(options) {
        this.orderInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : '';
        console.log(this.selectCard, 'selectData');
        // this.$nextTick(() => {
        this.selectData = JSON.parse(JSON.stringify(this.selectCard));
        this.getAvailableList();
        // });
    },
    onShow() {
        // this.getAvailableList()
    },
    methods: {
        // 处理时间只展示年月日
        formatTimeFun(time) {
            if (!time) {
                return;
            }
            let date = time.split(' ')[0];
            date = date.replace(/\-/g, '.');
            return date;
        },
        // 下拉刷新
        refreshPullDown(e) {
            this.getAvailableList({ isInit: true });
        },
        // 上拉加载
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getAvailableList();
            }
        },

        // 选择礼品卡
        ticketCellClickAction(item, index) {
            // 如果未选中,判断金额大小，如果超了，就禁止选择
            if (!item.show) {
                console.log(this.checkSelection, '90----');
                if (this.checkSelection) {
                    return;
                }
            }
            item.show = !item.show;
            let selectIndex = this.selectData.findIndex(selectItem => selectItem.giftCardNo == item.giftCardNo);
            if (selectIndex >= 0) {
                this.selectData.splice(selectIndex, 1);
            } else {
                this.selectData.push(item);
            }
            //   // 计算剩余金额
            console.log('this.selectData', this.selectData);
        },

        /**
         * @description  : 选择礼品卡点击事件
         * @return        {*}
         */
        chooseFinishAction() {
            let arr = this.selectData;
            this.$store.dispatch('selectCardAction', arr);
            this.$sKit.mpBP.tracker('后支付加油', {
                seed: 'hpayoilBiz',
                pageID: 'selectGiftPage',
                refer: this.orderInfo.refer || '',
                channelID: clientCode,
                address: this.cityName,
            });
            // return
            uni.navigateBack({
                delta: 1, //返回的页面数
            });
        },

        // 用户进行消费时可以查询名下可用礼品卡列表接口，支持分页查询。
        async getAvailableList({ isInit = false } = {}) {
            if (isInit) {
                Object.assign(this, {
                    availableList: [],
                    page: 1,
                });
                // 重置入参页码
                this.$refs.dataList.loadStatus = 'loading';
            }
            let { page, availableList, totalPage } = this;
            let params = {
                pageNum: page,
                pageSize: PAGE_SIZE,
                stationCode: this.orderInfo.stationCode, // 油站编号
                orderNo: this.orderInfo.orderNo, //订单编号
                // usedStatus: Number('1'),
            };
            let res = await consumeAvailableList(params, { isCustomErr: true });
            // let res = await giftCardList(params);
            if (res.success) {
                this.$refs.dataList.stopRefresh();
                this.$refs.dataList.pullDownHeight = 0;
                this.$refs.dataList.pullingDown = false;
                let list = res.data.rows || [];
                list.map((val, idx) => {
                    val.show = false;
                });
                console.log('list---', list);
                if (this.selectData.length > 0) {
                    list.forEach(element => {
                        let selectIndex = this.selectData.findIndex(selectItem => selectItem.giftCardNo == element.giftCardNo);
                        if (selectIndex >= 0) {
                            element.show = true;
                        }
                    });
                }
                // 将处理好的数组合并到定义的数组，放到页面渲染
                availableList = availableList.concat(list);
                // 将处理好的数据放置this中
                Object.assign(this, {
                    availableList,
                    page: Number(page) + 1,
                });
                console.log('page', page);
                // 返回总条数
                totalPage = res.data.pageSum;
                if (res.data && page >= totalPage) {
                    // 没有更多了
                    this.$refs.dataList.loadStatus = 'nomore';
                } else {
                    // 上拉加载更多
                    this.$refs.dataList.loadStatus = 'contentdown';
                }
                this.showEmpty = availableList.length <= 0 ? true : false;
            }
        },
    },
    computed: {
        ...mapState({
            selectCard: state => state.thirdIndex.selectCard,
            cityName: state => state.locationV3_app.cityName,
        }),
        // 检查所选礼品卡的总金额是否大于当前订单的金额
        checkSelection() {
            var total = 0;
            // 计算所选礼品卡的总金额
            for (var i = 0; i < this.selectData.length; i++) {
                total = currency(total).add(this.selectData[i].availableAmount);
            }
            console.log('total', total);
            console.log('this.orderInfo.payMoney', this.orderInfo.payMoney);
            if (Number(total) >= Number(this.orderInfo.payMoney)) {
                return true;
            } else {
                return false;
            }
        },
    },
    components: {},
};
</script>
<style scoped lang="scss">
.view {
    .coupon-wrap {
        width: 100%;
        margin-bottom: 11px;

        .select-img-wrap {
            width: 20px;
            height: 20px;
            margin-right: 13.5px;

            img {
                width: 20px;
                height: 20px;
            }
        }

        .item-module {
            position: relative;
            width: 310px;
            height: 175px;
            overflow: hidden;

            .card-img {
                width: 310px;
                height: 175px;
            }

            .modole-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                box-sizing: border-box;

                .card-div {
                    padding: 15px 15px 0;
                }

                .item-left {
                    flex: 1;

                    .logo-img {
                        width: 24px;
                        height: 23px;
                    }
                }

                .bottom-area {
                    width: 100%;
                    height: 40px;
                    line-height: 40px;
                }
            }
        }
    }

    .marl5 {
        margin-left: 5px;
    }

    .marr10 {
        margin-right: 10px;
    }

    .padlr15 {
        padding: 0 15px;
    }

    .mart32 {
        margin-top: 32px;
    }

    .mart20 {
        margin-top: 20px;
    }

    .mart15 {
        margin-top: 15px;
    }

    .btn {
        height: 44px;
        line-height: 44px;
        color: #fff;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
        margin: 0 16px 20px;
    }
}
</style>
