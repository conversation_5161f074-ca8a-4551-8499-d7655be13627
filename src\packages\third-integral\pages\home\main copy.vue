<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <!-- 积分页面 -->
    <div class="pageMpaas">
        <div class="select-region-class bg-F7F7FB" style="height: 100%">
            <zj-navbar :height="44" title="积分明细"></zj-navbar>
            <div class="content">
                <div class="card">
                    <img src="../../images/integral_detail_card.png" alt />
                    <div class="card-title">我的积分</div>
                    <div class="card-num">{{ loyaltyPtsAvailableAmount ? loyaltyPtsAvailableAmount : 0 }}</div>
                    <!-- <div class="card-text">
                    <img src="../../images/integral_detail_lindang.png" alt />
                    <div>7日内即将到期{{ loyaltyPtsAmountExpiresInOneWeek ? loyaltyPtsAmountExpiresInOneWeek : 0 }}</div>
        </div>-->
                </div>

                <div class="box">
                    <div class="data-title">
                        <div class="tabs">
                            <div class="left_tab" :class="{ active: tabNum == 1 }" @click="tabNum = 1">收入明细</div>
                            <div class="right_tab" :class="{ active: tabNum == 2 }" @click="tabNum = 2">消费明细</div>
                        </div>

                        <div class="data-list-title" v-if="tabNum == 1">
                            <div class="data-list-title-left">
                                <div class="dateTime" @click="dateShow1 = true">{{ incomeDate }}</div>
                                <div class="arrow-down-333" @click="dateShow1 = true" style="margin-left: 5px; margin-top: -2px"></div>
                            </div>
                            <div class="data-list-title-right">
                                <span>本月获取：</span>
                                <span>
                                    {{ incomeList ? (incomeList.monthTotalAmount ? incomeList.monthTotalAmount : 0) : 0 }}
                                </span>
                            </div>
                        </div>
                        <div class="data-list-title" v-else>
                            <div class="data-list-title-left">
                                <div class="dateTime" @click="dateShow2 = true">{{ consumedate }}</div>
                                <div class="arrow-down-333" @click="dateShow2 = true" style="margin-left: 5px; margin-top: -2px"></div>
                            </div>
                            <div class="data-list-title-right">
                                <span>本月消费：</span>
                                <span>
                                    {{ consumeList ? (consumeList.monthTotalAmount ? consumeList.monthTotalAmount : 0) : 0 }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="data" ref="data">
                        <!-- 收入明细 -->
                        <div class="data-list" v-if="tabNum == 1">
                            <div class="data-list-list" v-if="incomeList && incomeList.rows.length > 0">
                                <div class="data-list-item" v-for="(i, idx) in incomeList.rows" :key="idx">
                                    <div class="data-list-item-text">
                                        <div class="title">{{ i.activityName }}</div>
                                        <div class="date">{{ i.transactionTime }}</div>
                                    </div>
                                    <div class="data-list-item-num font-style">+{{ i.transactionAmount }}</div>
                                </div>
                            </div>
                            <div class="data-list-list" v-if="LoadingIncome && incomeList && incomeList.rows.length == 0">
                                <zj-no-data :emptyImage="require('../../images/kt8jf.png')" emptyText="暂未查询到积分收入明细"></zj-no-data>
                            </div>
                        </div>
                        <!-- 消费明细 -->
                        <div class="data-list" v-else>
                            <div class="data-list-list" v-if="consumeList && consumeList.rows.length > 0">
                                <div class="data-list-item" v-for="(i, idx) in consumeList.rows" :key="idx">
                                    <!-- <div class="pic">
                  <img :src="i.imgUrl" alt />
                </div> -->
                                    <div class="data-list-item-text">
                                        <div class="title">{{ i.productName }}</div>
                                        <div class="date">{{ i.transactionTime }}</div>
                                    </div>
                                    <div class="data-list-item-num font-style">{{ i.transactionAmount }}</div>
                                </div>
                            </div>
                            <div class="data-list-list" v-if="LoadingConsume && consumeList && consumeList.rows.length == 0">
                                <zj-no-data :emptyImage="require('../../images/kt8jf.png')" emptyText="暂未查询到积分消费明细"></zj-no-data>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <SelectDate v-if="dateShow1" @hideShow="hideShow" @sureSelectDateTime="incomebindDateChange"></SelectDate>
            <SelectDate v-if="dateShow2" @hideShow="hideShow" @sureSelectDateTime="consumebindDateChange"></SelectDate>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userPointIncomeList, userPointConsumeList } from '../../../../s-kit/js/v3-http/https3/integral/index';
import { mapGetters } from 'vuex';
import SelectDate from '../../../../s-kit/components/layout/zj-selectDate/zj-selectDate.vue';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { SelectDate },
    name: 'index',
    data() {
        const incomecurrentDate = this.getDate({
            format: true,
        });
        const consumecurrentDate = this.getDate({
            format: true,
        });
        return {
            active: 0,
            //loyaltyPtsAvailableAmount积分余额
            loyaltyPtsAvailableAmount: 0,

            //incomeList收入
            incomeList: null,
            //consumeList 消费
            consumeList: null,
            //incomeData收入明细传参
            incomeData: {
                pageSize: 999,
                pageNum: 1,
                startDateTime: '',
                endDateTime: '',
            },
            //consumeData消费明细传参
            consumeData: {
                pageSize: 999,
                pageNum: 1,
                startDateTime: '',
                endDateTime: '',
            },
            sortHeight: '',
            incomeDate: incomecurrentDate,
            consumedate: consumecurrentDate,
            tabNum: 1,
            dateShow1: false,
            dateShow2: false,
            LoadingIncome: false,
            LoadingConsume: false,
        };
    },
    computed: {
        //初始化开始时间
        startDate() {
            return this.getDate('start');
        },
        // 初始化结束时间
        endDate() {
            return this.getDate('end');
        },
        ...mapGetters(['memberAccountInfo']),
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'jifenPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
    },
    async mounted() {
        // 获取能源币，油卡，电子券，积分和余额，七日内余额
        await this.$store.dispatch('basicCouponAction');
        this.loyaltyPtsAvailableAmount = this.memberAccountInfo.loyaltyPtsAvailableAmount;
        // console.log(this.$store.state.member.memberAccountInfo.loyaltyPtsAvailableAmount);
        var date = new Date(); //获取当前时间
        var year = date.getFullYear(); //获取当前年份
        var month = date.getMonth() + 1; //获取当前月份
        var day = date.getDate(); //获取当前日期
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        //设置收入明细接口传参开始时间与结束时间
        this.incomeData.endDateTime = year + '-' + month + '-' + day;
        this.incomeData.startDateTime = year + '-' + month + '-' + '01';
        //设置消费明细接口传参开始时间与结束时间
        this.consumeData.endDateTime = year + '-' + month + '-' + day;
        this.consumeData.startDateTime = year + '-' + month + '-' + '01';
        //对接收入明细接口
        this.getuserPointIncomeList();
    },
    watch: {
        tabNum(val) {
            // 切换收入明细与消费明细tab页，消费明细无数据时请求消费明细接口
            if (this.consumeList == null) {
                // 对接消费明细接口
                this.getuserPointConsumeList();
            }
        },
    },
    methods: {
        //收入明细选择时间并请求接口
        incomebindDateChange(time) {
            console.log(time);
            this.dateShow1 = false;
            time.month = time.month > 9 ? time.month : '0' + time.month;
            this.incomeDate = time.year + '年' + time.month + '月';
            var lastDay = new Date(time.year, time.month, 0).getDate();
            this.incomeData.endDateTime = time.year + '-' + time.month + '-' + lastDay;
            this.incomeData.startDateTime = time.year + '-' + time.month + '-' + '01';
            this.getuserPointIncomeList();
        },
        //消费明细选择时间并请求接口
        consumebindDateChange(time) {
            console.log(time);
            this.dateShow2 = false;
            time.month = time.month > 9 ? time.month : '0' + time.month;
            this.consumedate = time.year + '年' + time.month + '月';
            var lastDay = new Date(time.year, time.month, 0).getDate();
            this.consumeData.endDateTime = time.year + '-' + time.month + '-' + lastDay;
            this.consumeData.startDateTime = time.year + '-' + time.month + '-' + '01';
            this.getuserPointConsumeList();
        },

        //获取时间
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            if (type === 'start') {
                month = month - 2;
                if (month <= 0) {
                    month = 12 + month;
                    year = year - 1;
                }
            }

            day = day > 9 ? day : '0' + day;
            month = month > 9 ? month : '0' + month;
            if (type.format) {
                return `${year}年${month}月`;
            } else {
                return `${year}-${month}-${day}`;
            }
        },
        //获取积分收入接口
        async getuserPointIncomeList() {
            console.log(this.incomeData);
            let res = await userPointIncomeList(this.incomeData);
            //
            if (res && res.success) {
                this.incomeList = res.data;
            }
            this.LoadingIncome = true;
        },
        //获取积分消费接口
        async getuserPointConsumeList() {
            console.log(this.consumeData);
            let res = await userPointConsumeList(this.consumeData);
            if (res && res.success) {
                this.consumeList = res.data;
            }
            this.LoadingConsume = true;
        },
        //关闭选择时间弹窗
        hideShow() {
            this.dateShow1 = false;
            this.dateShow2 = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.select-region-class {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;

    .content {
        flex: 1;
        // position: relative;
        padding-top: 10px;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .card {
            width: 345px;
            height: 132px;
            margin: 0 auto;
            border-radius: 8px;
            padding: 0 23px;
            color: #fff;
            position: relative;

            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 345px;
                height: 132px;
            }

            .card-title {
                position: absolute;
                z-index: 1;
                top: 38px;
                font-size: 14px;
                font-weight: bold;
            }

            .card-num {
                font-size: 30px;
                font-weight: bold;
                top: 58px;
                line-height: 34px;
                position: absolute;
                z-index: 1;
            }

            .card-text {
                position: absolute;
                z-index: 1;
                width: 137px;
                height: 20px;
                border-radius: 10px;
                line-height: 20px;
                padding-left: 10px;
                top: 86px;
                font-size: 12px;
                background-color: rgba(255, 255, 255, 0.2);

                img {
                    width: 11px;
                    height: 11px;
                    margin-right: 5px;
                    margin-top: 5px;
                    margin-left: 10px;
                }

                div {
                    display: inline;
                    margin-left: 15px;
                }
            }
        }

        .box {
            flex: 1;
            // height: 65%;
            display: flex;
            flex-direction: column;
            margin-top: 10px;
            overflow: hidden;

            .data-title {
                background: #fff;
                width: 345px;
                border-radius: 8px 8px 0 0;
                margin: 0 auto;

                .tabs {
                    display: flex;
                    text-align: center;
                    line-height: 48px;
                    border-bottom: 1px solid #efeff4;
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                    justify-content: space-around;

                    // .left_tab {
                    //     flex: 1;
                    // }

                    // .right_tab {
                    //     flex: 1;
                    // }

                    .active {
                        font-size: 16px;
                        font-weight: bold;
                        color: #e64f22;
                        position: relative;
                        border-bottom: 2px solid #e64f22;
                    }
                }

                .data-list-title {
                    padding: 0 15px;
                    display: flex;
                    background: #fff;
                    justify-content: space-between;
                    padding-top: 22px;
                    padding-bottom: 18px;

                    // margin-top: 22px;
                    // margin-bottom: 18px;
                    .data-list-title-left {
                        font-size: 14px;
                        font-weight: bold;
                        color: #333333;
                        display: flex;
                        align-items: center;
                        line-height: 14px;

                        img {
                            margin-left: 5px;
                            width: 16px;
                            height: 16px;
                        }
                    }

                    .data-list-title-right {
                        font-size: 14px;
                        font-weight: bold;

                        span:first-child {
                            color: #999999;
                        }

                        span:last-child {
                            color: #333;
                            font-family: 'HarmonyOS_Sans_Condensed_Medium';
                        }
                    }
                }
            }

            .data {
                width: 345px;
                flex: 1;
                margin: 0 auto;
                border-radius: 0 0 8px 8px;
                background: #fff;
                // margin-top: 10px;
                padding-bottom: 15px;
                // margin-bottom: 26px;
                overflow: auto;

                .data-list {
                    height: 100%;
                    padding-bottom: 15px;

                    // overflow: auto;
                    .data-list-list {
                        padding: 0 15px;
                        width: 100%;
                        padding-bottom: 15px;
                        height: 100%;

                        overflow: auto;

                        .data-list-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 25px;

                            .pic {
                                width: 42px;
                                height: 42px;
                                border-radius: 6px;
                                margin-right: 13px;
                                overflow: hidden;
                                background: #999999;

                                img {
                                    width: 100%;
                                    height: 100%;
                                }
                            }

                            .data-list-item-text {
                                .title {
                                    font-size: 14px;
                                    font-weight: 400;
                                    color: #333333;
                                }

                                .date {
                                    font-size: 12px;
                                    font-weight: 400;
                                    color: #999999;
                                    margin-top: 6px;
                                }
                            }

                            .data-list-item-num {
                                font-size: 16px;
                                // font-weight: bold;
                                color: #333333;
                                flex: 1;
                                text-align: right;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
