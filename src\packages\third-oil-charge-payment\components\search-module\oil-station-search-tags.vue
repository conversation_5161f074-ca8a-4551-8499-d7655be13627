<template>
    <div class="oil-station-search-tags">
        <div class="tags-content">
            <div class="tagsImg" @click="screenClick()">
                <img class="screenImg" src="../../image/screen-icon.png" alt>
                <span>筛选</span>
            </div>
        </div>
    </div>
</template>
<script>
import Vue from 'vue';

export default {
    name: 'oil-station-search-tags',
    components: {
    },
    data() {
        return {};
    },
    created() {},
    mounted() {
        
    },
    methods: {        
        // 打开筛选框
        screenClick(item) {
            this.$emit('openScreen')            
        },
    },
};
</script>

<style lang="scss" scoped>
.oil-station-search-tags {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-top: 5px;
    margin-left: 4px;

    .tags-content {
        display: flex;
        align-items: center;
        padding: 10px 0px;
        overflow: hidden;
        width: fit-content;
        .tagsImg {
            display: flex;
            align-items: center;
            justify-content: center;
            .screenImg {
                width: 15px;
                height: 15px;
                margin-right: 5px;
            }
        }
        

        .oil-station-search-tagview {
            height: 30px;
            line-height: 30px;
            border-radius: 4px;
            font-size: 13px;
            padding: 0px 13px;
            margin-right: 10px;
            white-space: nowrap;
            text-align: center;
        }

        .selected {
            background: #ffe9e3;
            border: 1px solid #e64f22;
            color: #e64f22;
        }

        .no-sel {
            border: 1px solid #cdcdcd;
        }
    }
}
</style>
