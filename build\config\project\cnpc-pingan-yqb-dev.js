const { requireMkSync } = require('../utils');
const baseConfig = requireMkSync('./base-dev.js');

module.exports = {
  ...baseConfig,
  _: '对外输出SDK-平安壹钱包',
  name: 'cnpc-pingan-yqb-dev',
  appId: '4274601259717769',
  mpaasAppId: 'PRI2A968C1201501', // mPaas appid
  applicationPluginAppId: 'PRI2A968C1201501', // mPaas appid
  // clientCode:'app-pab',
  clientCode: 'C2948',
  v3sign: 'Vo3XRVEZrarRdpcxM7', // * 微信/支付宝必填 3.0加签key
  // clientCode:'C2930',
  // v3sign: 'jhnejrjrjUUUmmd33',
  platform: 'one',
  secretKey: "a6a798e129189c001535f1706c8ddf91",
  rpcUrl: 'https://hkyzsdknp.kunlunjyk.com:443/gsms/mgw.htm',
  // 埋点
  maa: {
    ...baseConfig.maa,
    id: '3',
  },
  app: {
    ...baseConfig.app,
    mpassplugins: {
      keyboardPlugin: {
          "version": "*", 
          "provider": "2021003174646599"
      },
      payPlugin: {
          "version": "*",
          "provider": "2021003173659887"
      },
    }
  }
};
