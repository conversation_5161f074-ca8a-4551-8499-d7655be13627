import { mapState } from 'vuex';
import post from '../../../../../s-kit/js/v3-http/post';
import projectConfig from '../../../../../../project.config';
export default {
    // #ifdef MP-ALIPAY
    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
        }),
        payPlugin() {
            return this.$sKit.aliPayPlugin;
        },
    },
    mounted() {
        const result = this.$refs['handlePasswordKeyboardRef'];
        this.$sKit.keyBordPlugin.initRef(result);
        this.$store.commit('setAccountDataPlugin', result);
    },
    data() {
        return {};
    },
    methods: {
        /**
         * @description  : 确认支付(选择券与选择支付方式后的确认支付)
         * @param         {String} payOrderNo -支付订单号
         * @param         {String} amount -订单金额
         * @return        {*}
         */
        async confirmPay() {
            let params = {
                payOrderNo: this.payMentInfo.payOrderNo,
                amount: this.actualPayTotalAmount || 0 + '',
                extendFiled: await post.addExtendFiled('plugin'),
            };
            console.log(params, '会员码进行确认支付时的参数');
            // uni.showLoading({
            //     title: '唤起中',
            //     mask: true,
            // });
            this.isOrderShow = true;
            const res = await this.payPlugin.RposPay(params, this.keyBoardRef);

            // uni.hideLoading();
            if (res.code === 'PAY_SUCCESS') {
                console.log('会员码支付成功', JSON.stringify(res));
                this.active = 2;
                // 查询昆仑e享卡余额明细
                this.$store.dispatch('getAccountBalanceAction', {});
                this.isOrderShow = false;
                this.$sKit.mpBP.tracker('会员码', {
                    seed: 'memberCodeBiz',
                    pageID: 'orderCompletePage', // 页面名
                    refer: this.payMentInfo.refer || '', // 来源
                    channelID: projectConfig.clientCode, // C10/C12/C13
                    stationCode: this.payMentInfo.stationCode, //站点编码
                    address: this.cityName,
                });
            } else {
                this.isOrderShow = false;
                let errIndex = res.msg.indexOf(':');
                let errorCode = '';
                let customErr = '';
                if (errIndex !== -1) {
                    errorCode = res.msg.slice(0, errIndex);
                    customErr = res.msg.slice(errIndex + 1, res.msg.length);
                } else {
                    customErr = res.msg;
                }
                this.$store.dispatch('zjShowModal', {
                    title: customErr,
                    content: errorCode,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }
        },
        /**
         * @description  :  返回上一页
         * @return        {*}
         */
        closeEvent() {
            uni.navigateBack({
                delta: 1,
            });
        },
    },
    // #endif
};
