import { Storage } from '../class/storage.js';

export default {
    isAgreePay: new Storage('isAgreePay', false), // 是否同意支付协议
    defaultPersonalInvoice: new Storage('defaultPersonalInvoice', -1), // 默认抬头(个人)id
    defaultEnterpriseInvoice: new Storage('defaultEnterpriseInvoice', -1), // 默认抬头(企业)id
    prevPersonalInvoice: new Storage('prevPersonalInvoice', -1), // 上次填写抬头(个人)id
    prevEnterpriseInvoice: new Storage('prevEnterpriseInvoice', -1), // 上次填写抬头(企业)id
    commonlyUsedOilCard: new Storage('commonlyUsedOilCard', -1), // 常用油卡
    msgAuthorizeTime: new Storage('msgAuthorizeTime', -1), // 消息模板授权时间
    msgAuthorizeList: new Storage('msgAuthorizeList', -1), // 消息模板授权时间
};
