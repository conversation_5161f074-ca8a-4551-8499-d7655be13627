import { mapGetters, mapState } from 'vuex';
import { calculateOrdeDiscountsApi } from '../../../../../s-kit/js/v3-http/https3/oilStationService/index';
import post from '../../../../../s-kit/js/v3-http/post';
import { clientCode } from '../../../../../../project.config';
export default {
    // #ifdef MP-ALIPAY
    computed: {
        ...mapState({
            keyBoardRef: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    mounted() {
        const result = this.$refs['handlePasswordKeyboardRef'];
        this.$sKit.keyBordPlugin.initRef(result);
        this.$store.commit('setAccountDataPlugin', result);
    },
    onShow() {},
    data() {
        return {
            // 支付插件参数
            resData: {},
            // 支付参数
            payParams: {},
            // 微信加券二次支付 payMethod == 11的数据
            order: null,
            wxSecondaryPaymentObj: {},
        };
    },
    methods: {
        //优惠券
        calculateOrdeDiscountsPost(selectTicketsInfo) {
            // let dataParams = JSON.parse(JSON.stringify(this.unPaidInfo));
            let newProductList = this.unPaidInfo.productList;
            let newSelectTicketsInfo = JSON.parse(JSON.stringify(selectTicketsInfo));
            newProductList.forEach(item => {
                if (!item.productType) {
                    delete item.productType;
                }
                item.activityDiscountList.forEach(acItem => {
                    if (!acItem.groupId) {
                        delete acItem.groupId;
                    }
                });
            });
            console.log('newProductList----', newProductList);
            const params = {
                stationCode: this.unPaidInfo.stationCode,
                orderNo: this.unPaidInfo.orderNo,
                channelType: this.unPaidInfo.channelType,
                orderMoney: this.unPaidInfo.orderMoney,
                productInOrderVoList: newProductList,
                businessDay: this.businessDay,
                usedCouponList: newSelectTicketsInfo,
            };
            calculateOrdeDiscountsApi(params)
                .then(res => {
                    if (res.success) {
                        console.log('OrdeDiscounts---', res.data);
                        this.unPaidInfo = { ...res.data, cancel: this.unPaidInfo?.cancel };
                        this.calculateGiftCard();
                        this.isPaymentMethodAvailable();
                    } else {
                        this.getPayChannelInfo();
                    }
                    this.isCanClickPay = true;
                })
                .catch(err => {
                    this.getPayChannelInfo();
                    this.isCanClickPay = true;
                });
        },
        // String stationCode 是 站编码
        // String bizOrderNo 是 业务订单编号
        // String rcvAmt 是 应收总金额
        // String realAmt 是 支付金额
        // String payType 是 支付方式(1 ：微信；2：支付宝；3：银联卡;  4：数字人民币；5：电子账户；
        // String bizDay 否 营业日
        // String extendFiled 是 风控字段（json字符串透传，不校验里面内容）
        // Array insidePayList 是 账户预支付场景：内部支付方式列表(组合支付)
        // String accountNo 否 账户编号，电子账户类型支付时必传
        // 油卡或电子账户支付
        async sdkRealPay(isAuth = false) {
            // uni.showLoading({
            //     title: '支付中',
            //     mask: true,
            // });
            this.isOrderShow = true;
            this.payParams = {
                stationCode: this.unPaidInfo.stationCode,
                bizOrderNo: this.unPaidInfo.orderNo,
                rcvAmt: this.unPaidInfo.orderMoney,
                // realAmt: this.order ? Number(this.order.rcvAmt) : this.unPaidInfo.payMoney,
                realAmt: this.orderPayMoney + '',
                payType: this.curPayChannelInfo.payType,
                bizDay: this.businessDay,
                // 3.0.4风控字段
                extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                insidePayList: [],
            };
            if (this.selectCard.length > 0 && this.orderPayMoney == 0) {
                this.payParams.payType = 5;
                // this.curPayChannelInfo.payType = 5
                let item = this.payList.find(item => item.payType == 5);
                if (item) this.curPayChannelInfo = item;
            }
            if (this.curPayChannelInfo.payType == '6') {
                this.payParams.cardIdx = this.curCardPayInfo.cardSequence + '';
                this.payParams.fuelCardNo = this.curCardPayInfo.fuelCardNo;
            } else if (this.curPayChannelInfo.payType == '5') {
                this.payParams.cardIdx = '1';
                this.payParams.accountNo = this.walletInfo.ewalletNo;
            }
            if (JSON.stringify(this.wxSecondaryPaymentObj) !== '{}') {
            } else {
                if (this.unPaidInfo.couponList.length > 0) {
                    for (let i = 0; i < this.unPaidInfo.couponList.length; i++) {
                        if (this.unPaidInfo.couponList[i].used == 1) {
                            this.payParams.insidePayList.push({
                                payType: '11',
                                payAmt: this.unPaidInfo.couponList[i].couponDiscount + '',
                                couponNo: this.unPaidInfo.couponList[i].couponNo,
                                couponTemplateNo: this.unPaidInfo.couponList[i].couponTemplateNo,
                            });
                        }
                    }
                }
            }
            this.payParams.insidePayList.push(...(await this.calculateGiftCardPay()));
            console.log('油卡或电子账户支付参数', this.payParams);
            console.log('SDK支付方式', this.curPayChannelInfo.payType);

            try {
                this.$sKit.mpBP.tracker('后支付加油', {
                    seed: 'hpayoilBiz',
                    pageID: 'inputPsdbut', // 返回sdk标识
                    refer: this.refer || '',
                    channelID: clientCode,
                    address: this.cityName,
                });
                this.resData = await this.payPlugin.Buy(JSON.parse(JSON.stringify(this.payParams)), this.keyBoardRef);
                console.log('zfb---res---成功', this.resData);
                if (this.resData.code === 'PAY_SUCCESS') {
                    this.beginTime = new Date().getTime();
                    this.$sKit.layer.useRouter(
                        '/packages/third-oil-charge-payment/pages/query-payment-results/main',
                        { ...this.unPaidInfo, refer: this.refer },
                        'redirectTo',
                    );
                } else if (this.resData.code === 'PAY_ERROR_003') {
                    console.log('测试=====');
                    // need risk
                    this.isCanClickPay = true;
                    this.isPaying = false;
                    //需要实人认证
                    const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
                    // res 13 未认证 ，14 实名认证通过，15 实人认证通过
                    if (riskRes == 13) {
                        // 打开实人认证的表单弹窗
                        console.log('测试222222======');
                        this.realNameDialogFlag = true;
                    }
                } else {
                    this.getPayChannelInfo();
                    this.isOrderShow = false;
                    uni.hideLoading();
                    // 截取字符串后面的数据
                    let errIndex = this.resData.msg.indexOf(':');
                    let errorCode = '';
                    let customErr = '';
                    if (errIndex !== -1) {
                        errorCode = this.resData.msg.slice(0, errIndex);
                        customErr = this.resData.msg.slice(errIndex + 1, this.resData.msg.length);
                    } else {
                        customErr = this.resData.msg;
                    }
                    this.$store.dispatch('zjShowModal', {
                        title: customErr,
                        content: `${errorCode}`,
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            } catch (error) {
                uni.hideLoading();
                this.isCanClickPay = true;
                this.isPaying = false;
                console.log(error);
            }
        },
        // 获取支付方式
        async getBuyPayTypeList() {
            let params = {
                stationCode: this.unPaidInfo.stationCode,
                amount: this.unPaidInfo.payMoney,
            };
            console.log('支付宝支付方式获取', params);
            uni.showLoading({
                title: '加载中',
                mask: true,
            });
            const res = await this.payPlugin.GetBuyTypeList(params);
            uni.hideLoading();
            if (res.code === 'PAY_SUCCESS') {
                // this.$store.commit('mSetRefuelingMigrationFlag', false)
                this.payList = res.data;
                this.getRechargeMarketingCopy(this.unPaidInfo);
                console.log('支付方式', this.payList);
                if (this.payList && this.payList.length > 0) {
                    this.curPayChannelInfo = this.payList[0];
                    if (this.payList[0].payType == '6' && this.payList[0].memberAssets) {
                        this.isShowOil = true;
                    } else {
                        this.curCardPayInfo = '';
                        this.isShowOil = false;
                    }
                    this.isPaymentMethodAvailable();
                }
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: res.msg,
                    content: `${res.code}`,
                    confirmText: '确定',
                    success(res) {
                        if (res.confirm) {
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
            }

            console.log('payList---', res);
        },
    },
    // #endif
};
