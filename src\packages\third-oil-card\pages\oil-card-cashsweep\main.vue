<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas bg-F7F7FB">
        <zj-navbar :height="44" title="资金转出"></zj-navbar>
        <div class="p-LR-16 fl-row bg-FFF7DC font-12 weight-400 color-333 tp-pd">
            <div class="tp">温馨提示：</div>
            <div>下方为系统查询到您名下可操作资金转出的加油卡，点击转出进行转出到昆仑e享卡。</div>
        </div>
        <div class="padding-13">
            <div class="input-area">
                <img class="search-icon" src="../../images/icon-serch.png" alt />
                <input type="text" placeholder="请输入卡号" v-model="cardNumber" class="input" />
                <div class="fl-row">
                    <div class="font-14 color-999 bor-seach" @click="queryHandle()">搜索</div>
                </div>
            </div>
            <div class="cardList card-default fl-row fl-jus-bet mar-bom-12" v-for="(item, index) in cardList" :key="index">
                <div style="height: 100%;" class="fl-column fl-jus-cen">
                    <div class="font-16 weight-bold">{{ item.cardNo }}</div>
                    <div class="font-12 nameCard color-80">{{ item.userName }}<span class="namephone">{{ item.phone }}</span></div>
                </div>
                <div class="fl-column fl-al-jus-cen cardList_btn">
                    <div class="btn_style" @click="outBtnHandle(item)">转出</div>
                    <div class="font-16 color-FF0A0A fl-column fl-jus-cen mar-top-15">{{ item.cardBalance }}</div>
                </div>
            </div>         
            <div class="mar-top-8 font-12 color-80">
                <p class="font-13 weight-bold">温馨提示:</p>
                <p class="tp1">1.每张实体卡未绑定状态下只允许迁移一次，后续有迁移需求必须进行绑定后操作。</p>
                <p>2.您也可以手动输入卡号进行资金转出。</p>
            </div>
        </div>
        
    	<zj-show-modal>
            <transferReminder v-if="modalFalgs" ref="transferReminder" :modalData="modalData"></transferReminder>
        </zj-show-modal>
	</div>
</template>
<script>
import { basicInfoQuery, identityAuthInfo, getQueryUnBindCardList, unBindCardDetail } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import transferReminder from '../../components/transferReminder/main.vue'
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            cardNumber: '',
            cardList: [],
            modalData: {},
            modalFalgs: false
        }
    },
    components: {
        transferReminder,
    },
    computed: {
        // #ifdef MP-WEIXIN
        inputStyle() {
            return `box-shadow:${this.inputFlag ? '0px 2px 3px 0px rgba(192,187,187,0.5)' : ''})`;
        },
        // #endif
    },
    mounted() {
        // 获取当前用户信息
        // let res1 = await basicInfoQuery({ isload: true });
        this.getCardList()
    },
    methods: {
        /**
         * @description  : 加载未绑定加油卡列表
         * @return        {*}
         */
         async getCardList () {
            // 当前用户信息（未脱敏）
            let {data} = await identityAuthInfo();
            let res = await getQueryUnBindCardList({idNum: data ? data.identityNo : ''});
            if (res.success) {
                this.cardList = res.data.unBindCardList
            }
        },
        /**
         * @description  : 点击搜索，对输入的卡号进行对比
         * @return        {*}
         */
         async queryHandle () {
            // 校验输入的加油卡号是否是纯数字
            var numRe = new RegExp('^[-\\+]?([0-9]+\\.?)?[0-9]+$')
            // 校验加油卡长度
            var lengtRe = 16
            if (this.cardNumber == '') {
                uni.showToast({
                    title: '请输入加油卡号',
                    icon: 'none',
                    duration: 2000,
                });
                return false
            }
            if (numRe.test(this.cardNumber) && this.cardNumber.length == lengtRe) {
                // 加油卡校验成功，获取当前用户信息
                // let res = await basicInfoQuery({ isload: true });
                // 将信息通过渠道层传输给账户中心，将卡号与卡系统进行对比
                let resData = await unBindCardDetail({cardNo: this.cardNumber})
                if (resData.success) {
                    this.modalData = resData.data
                    this.modalFalgs = true
                    // 成功弹框提醒回的卡号信息
                    this.$store.dispatch('zjShowModal', {
                        confirmText: '允许',
                        cancelText: '不允许',
                        confirmColor: '#E64F22',
                        cancelColor: '#666',
                        success: res => {
                            if (res.confirm) {
                                let url = '/packages/third-oil-card/pages/oil-card-cashsweep-phone/main';
                                let params = {cardNo:resData.data.cardNo, phone: resData.data.phone};;
                                let type = 'navigateTo';
                                this.$sKit.layer.useRouter(url, params, type);
                            }
                        },
                    });                   
                }     
                
            } else {
                uni.showToast({
                    title: '请输入规范的加油卡号',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        /**
         * @description  : 点击转出按钮，跳转到手机号验证界面
         * @return        {*}
         */
        outBtnHandle(row) {
            let url = '/packages/third-oil-card/pages/oil-card-cashsweep-phone/main';
            let params = {cardNo:row.cardNo, phone: row.phone};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        }
    }
}
</script>
<style scoped lang="scss">
.tp-pd {
    padding-top: 8px;
    padding-bottom: 8px;
    .tp {
        width: 100px;
    }
}

.cardList {
    height: 95px;
    padding: 0px 14px 0px 14px;
    .cardList_btn {
        height: 100%;
    }
    .nameCard {
        margin-top: 25px;
        
        .namephone {
            margin-left: 10px;
        }
    }
    .btn_style{
        padding: 14rpx 40rpx;
        border-radius: 6px;
        border: 1px solid #FF6B2C;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        background: #FF6B2C;
    }
}
.tp1 {
    margin-top: 5px;
}

.input-area {
    flex: 1;
    height: 40px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e7e7e7;
    display: flex;
    align-items: center;
    padding: 0px 14px;
    position: relative;
    margin-bottom: 12px;

    .search-icon {
        width: 17px;
        height: 17px;
    }

    .input {
        margin-left: 9px;
        font-size: 14px;
        caret-color: #e64a1d;
        flex: 1;
        height: 40px;
        background: none;
    }

    .bor-seach {
        // width: 40px;
        height: 16px;
        border-left: 1px solid #999;
        padding-left: 9px;
        line-height: 16px;
    }
}

</style>