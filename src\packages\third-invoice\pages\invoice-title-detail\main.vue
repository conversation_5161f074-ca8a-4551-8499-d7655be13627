<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="p-bf fl-column">
            <zj-navbar :border-bottom="false" title="发票抬头详情"></zj-navbar>
            <div class="f-1 mh-0 bg-F7F7FB">
                <!-- <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef"> -->
                <div class="formPage">
                    <div class="content-box">
                        <div class="qrcode-box">
                            <canvas v-if="!imagePath" canvas-id="myCanvas"></canvas>
                            <img v-if="imagePath" :src="imagePath" class="qrcode" />
                            <div>商家扫码获取抬头信息</div>
                        </div>
                        <div class="other-msg">
                            <div class="section-item">
                                <div class="left">名称</div>
                                <div class="right">{{ detail.buyerName || '' }}</div>
                            </div>
                            <div class="section-item">
                                <div class="left">税号</div>
                                <div class="right">{{ detail.buyerTaxId || '' }}</div>
                            </div>
                            <div class="section-item">
                                <div class="left">地址</div>
                                <div class="right">{{ detail.buyerAddr || '' }}</div>
                            </div>
                            <div class="section-item">
                                <div class="left">电话</div>
                                <div class="right">{{ detail.buyerTel || '' }}</div>
                            </div>
                            <div class="section-item">
                                <div class="left">开户银行</div>
                                <div class="right">{{ detail.buyerFinancial || '' }}</div>
                            </div>
                            <div class="section-item">
                                <div class="left">银行帐号</div>
                                <div class="right">{{ detail.buyerAccount || '' }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="action-box fl-row fl-jus-bet">
                        <div class="delete" @click="deleteHeaderDetail">删除</div>
                        <div class="edit" @click="editItem">编辑</div>
                    </div>
                </div>
                <!-- </zj-pull-down-refresh> -->
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import QRCode from 'qrcode';
import { invoiceTitleDetailApi, invoiceTitleDeleteApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'invoiceTitleDetail',
    data() {
        return {
            // 发票抬头详情页面数据
            detail: {},
            // 发票抬头ID
            headerId: '',
            refer: '',
            imagePath: '',
        };
    },

    onLoad(options) {
        //获得列表传来的id并查询详情接口
        console.log(JSON.parse(decodeURIComponent(options.data)), '接收数据');
        let params = JSON.parse(decodeURIComponent(options.data));
        this.headerId = params.headerId;
        if (params.refer) {
            this.refer = params.refer;
        }
        this.$sKit.mpBP.tracker('电子发票', {
            seed: 'invoiceBiz',
            pageID: 'headerDetailPage',
            refer: this.refer,
            channelID: clientCode,
        });
        this.loadDetail();
    },
    methods: {
        generateQRCode(qrCode) {
            console.log(qrCode, 'qrCode=====');
            const canvasId = 'myCanvas';
            const ctx = uni.createCanvasContext(canvasId, this);
            ctx.setFillStyle('#00000000'); // 红色
            ctx.fillRect(0, 0, 300, 300); // 绘制一个红色矩形
            ctx.draw(false); // 提交绘制
            // 生成二维码的文本数据
            QRCode.toString(
                qrCode, // 二维码内容
                { type: 'utf8' }, // 配置选项
                (err, qrCodeText) => {
                    if (err) {
                        console.error('生成二维码失败', err);
                        return;
                    }

                    // 解析二维码文本数据并绘制到 Canvas
                    this.drawQRCodeToCanvas(ctx, qrCodeText, 300, 300);
                },
            );
        },
        drawQRCodeToCanvas(ctx, qrCodeText, width, height) {
            console.log('二维码文本数据:', qrCodeText);
            // 将 SVG 转换为 Base64 格式
            this.imagePath = `data:image/svg+xml;base64,${Buffer.from(qrCodeText).toString('base64')}`;
            console.log(this.imagePath, 'this.imagePath');
        },
        /**
         * @description  : 获取发票抬头详情
         * @return        {*}
         */
        async loadDetail() {
            let res = await invoiceTitleDetailApi({ headerId: this.headerId });
            if (res && res.success) {
                this.detail = res.data;
                this.generateQRCode(res.data.buyerQrcode);
            }
        },
        /**
         * @description  : 编辑跳转，并传type值为edit
         * @return        {*}
         */
        editItem() {
            let params = {
                type: 'edit',
                btnText: '提交',
                title: '修改发票抬头',
                detail: this.detail,
            };
            let url = '/packages/third-invoice/pages/invoice-title-form/main';
            this.$sKit.layer.useRouter(url, params);
        },
        /**
         * @description  : 删除抬头
         * @return        {*}
         */
        async deleteHeaderDetail() {
            this.$sKit.mpBP.tracker('电子发票', {
                seed: 'invoiceBiz',
                pageID: 'deleteHeaderBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.$store.dispatch('zjShowModal', {
                title: '确认删除？',
                confirmText: '确认',
                cancelText: '取消',
                confirmColor: '#333',
                cancelColor: '#666',
                success: async res => {
                    if (res.confirm) {
                        let res = await invoiceTitleDeleteApi({ headerId: this.headerId });
                        if (res.success) {
                            this.$sKit.layer.showToast({ title: '删除成功' });
                            setTimeout(() => {
                                uni.navigateBack({
                                    delta: 1,
                                });
                            }, 500);
                        }
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.formPage {
    padding: 32rpx;

    .content-box {
        border-radius: 16rpx;
        background: #fff;
        padding-top: 32rpx;

        .qrcode-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            border-bottom: 2rpx solid #efeff4;

            .qrcode {
                width: 100%;
                height: 400rpx;
            }

            div {
                margin: 30rpx 0;
            }
        }

        .other-msg {
            padding: 24rpx 28rpx;

            .section-item {
                display: flex;
                font-size: 28rpx;
                margin-bottom: 32rpx;
                justify-content: space-between;

                &:last-child {
                    margin-bottom: 0;
                }

                .left {
                    color: #999999;
                }

                .right {
                    width: 238px;
                    color: #333333;
                    text-align: right;
                    font-weight: 400;
                }
            }
        }
    }

    .action-box {
        margin-top: 32rpx;

        div {
            border-radius: 16rpx;
            text-align: center;
            width: 331rpx;
            height: 88rpx;
            font-size: 32rpx;
            font-weight: 400;
            line-height: 88rpx;
        }

        .edit {
            color: #fff;
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        }

        .delete {
            background: #ffffff;
            border: 2rpx solid #e64f22;
            color: #e64f22;
        }
    }
}
</style>
