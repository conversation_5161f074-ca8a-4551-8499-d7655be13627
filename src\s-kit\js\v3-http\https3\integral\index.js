import { POST } from '../../index';
//积分收入明细
export const userPointIncomeList = (params, config) => {
    return POST('user.point.income.list', params, config);
};
//积分消费明细consume
export const userPointConsumeList = (params, config) => {
    return POST('user.point.consume.list', params, config);
};
//能源币收入明细
export const userPersonalPointIncomeList = async (params, config) => {
    return await POST('user.personalPoint.income.list', params, config);
};
//能源币消费明细
export const userPersonalPointConsumeList = async (params, config) => {
    return await POST('user.personalPoint.consume.list', params, config);
};
