<template>
    <div class="detail-center">
        <div
            class="detail-top"
            :style="{
                backgroundImage: 'url(' + naviBg + ')',
            }"
        >
            <u-navbar
                :back-icon-size="40"
                back-icon-color="#ffffff"
                :height="44"
                :background="{
                    background: 'rgba(255, 255, 255, 0)',
                }"
                :title-color="pageConfig.titleColor"
                back-text="电子发票明细"
                :back-text-style="pageConfig.titleStyle"
                :border-bottom="false"
            ></u-navbar>
        </div>
        <div class="detail-content" :style="{ height: calcConHeight }">
            <scroll-view class="scroll" scroll-y>
                <div class="detail-section">
                    <div class="section-item">
                        <div class="left">发票类型</div>
                        <div class="right">增值税电子普通发票</div>
                    </div>
                    <div class="section-item">
                        <div class="left">开票日期</div>
                        <div class="right">{{ invoiceObj.kprq }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">交易日期</div>
                        <div class="right">{{ invoiceObj.jyrq }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">纳税人识别号</div>
                        <div class="right">{{ invoiceObj.xsfnsrsbh }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">销售方</div>
                        <div class="right">{{ invoiceObj.xsfmc }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">销售方地址</div>
                        <div class="right">{{ invoiceObj.xsfdz }}</div>
                    </div>
                    <div class="my-line"></div>

                    <div class="section-item">
                        <div class="left">购买方</div>
                        <div class="right">{{ invoiceObj.gmfmc }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">购买方识别号</div>
                        <div class="right">{{ invoiceObj.gmfnsrsbh }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">购买方性质</div>
                        <div class="right">{{ invoiceObj.gmfxz == 1 ? '企业' : '非企业' }}</div>
                    </div>
                    <div class="my-line"></div>
                    <div class="section-item">
                        <div class="left">发票代码</div>
                        <div class="right">{{ invoiceObj.fpdm }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">发票号码</div>
                        <div class="right">{{ invoiceObj.fphm }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">合计金额</div>
                        <div class="right">￥ {{ invoiceObj.hjje || '' }}</div>
                    </div>
                    <div class="section-item">
                        <div class="left">合计税额</div>
                        <div class="right">￥ {{ invoiceObj.hjse }}</div>
                    </div>
                </div>
                <div class="detail-download">
                    请复制该链接在电脑浏览器打开进行下载。
                    <p @click="copyPath">复制下载链接</p>
                </div>
            </scroll-view>
        </div>
        <div class="detail-bottom-action">
            <div class="btn" @click="clickLookInvoice">预览</div>
            <div class="btn" v-if="invoiceObj.sfcd == 0" @click="reSendInvoice">重开发票</div>
            <!-- <div class="btn" @click="reSendInvoice">重开发票</div> -->
            <div class="btn full" @click="toPrintAuto">自助打印</div>
        </div>
        <!-- 弹窗-规则提示 -->
        <u-popup v-model="toolTipShow" :mask-close-able="false" border-radius="20" mode="center">
            <div class="toolTipDiv">
                <div class="toolTip_title">重开发票说明</div>
                <div class="toolTip_content">
                    <p>1、成功开具首张发票起的90天内可以重开；</p>
                    <p>2、线上充值、消费订单开具的发票都可以进行重开；</p>
                    <p>3、已重开过的发票不可再次重开；</p>
                    <p>4、开票有效期为退票成功后30天内；</p>
                </div>
                <div class="toolTip_foot">
                    <div @click="toolTipShow = !toolTipShow">取消</div>
                    <div @click="reSendInvoiceFunc">确定</div>
                </div>
            </div>
        </u-popup>
        <!-- <template v-if='skipData.state == 1'>

			企业
			<div v-if="invoiceObj.gmfxz == 1">
				<div class='cell-title'>发票状态</div>
				<div class="list-cell top">
					<div class='list-item'>
						<div class='item-title'>电子发票</div>
						<div class='item-detail color-ff8200' @click='clickLookInvoice'>
							已开票
							<u-icon name="arrow-right" color="#FF8200"></u-icon>
						</div>
						<div v-if="skipData.state == 2" class='item-detail color-ff8200'>
							已重开
						</div>
					</div>
				</div>

				<div class="list-cell top">
					<div class='list-item'>
						<div class='item-title'>申请时间</div>
						<div class='item-detail'>{{invoiceObj.jyrq}}</div>
					</div>
					邮箱暂时删除
					<div class='list-item'>
						<div class='item-title'>预留邮箱</div>
						<div class='item-detail'>{{invoiceObj.email}}</div>
					</div>
				</div>
				<div class='cell-title'>发票信息</div>
				<div class="list-cell x-bottom">
					<div class='list-item line'>
						<div class='item-title'>发票抬头</div>
						<div class='item-detail'>{{invoiceObj.gmfmc}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>纳税人识别号</div>
						<div class='item-detail'>{{invoiceObj.gmfnsrsbh}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>开户行</div>
						<div class='item-detail'>{{invoiceObj.gmfBank}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>账号</div>
						<div class='item-detail'>{{invoiceObj.gmfNumber}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>注册电话</div>
						<div class='item-detail'>{{invoiceObj.gmfPhone}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>注册地址</div>
						<div class='item-detail'>{{invoiceObj.gmfAddress}}</div>
					</div>
					<div class='list-item'>
						<div class='item-title'>开票金额</div>
						<div class='item-detail'>￥{{invoiceObj.hjje}}</div>
					</div>
				</div>
			</div>
			<div v-else>
				<div class='cell-title'>发票状态</div>
				<div class="list-cell top">
					<div class='list-item'>
						<div class='item-title'>电子发票</div>
						<div class='item-detail color-ff8200' @click='clickLookInvoice'>
							已开票
							<u-icon name="arrow-right" color="#FF8200"></u-icon>
						</div>
					</div>
				</div>
				<div class="list-cell top">
					<div class='list-item'>
						<div class='item-title'>申请时间</div>
						<div class='item-detail'>{{invoiceObj.jyrq}}</div>
					</div>
					<div class='list-item'>
						<div class='item-title'>预留邮箱</div>
						<div class='item-detail'>{{invoiceObj.email}}</div>
					</div>
				</div>
				<div class='cell-title'>发票信息</div>
				<div class="list-cell x-bottom">
					<div class='list-item line'>
						<div class='item-title'>个人姓名</div>
						<div class='item-detail'>{{invoiceObj.gmfmc}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>家庭地址</div>
						<div class='item-detail'>{{invoiceObj.gmfAddress}}</div>
					</div>
					<div class='list-item line'>
						<div class='item-title'>联系电话</div>
						<div class='item-detail'>{{invoiceObj.gmfPhone}}</div>
					</div>
					<div class='list-item'>
						<div class='item-title'>开票金额</div>
						<div class='item-detail'>￥{{invoiceObj.hjje}}</div>
					</div>
				</div>
			</div>
			<div class="add-footer">
				<div class='footer-left' @click='clickLookInvoice'>发票预览</div>
				<div class='footer-left' @click='clickReopen'>申请重开</div>
				<div class="footer-btn" @click='clickCFInvoice'>重发发票</div>
				<div class='footer-left' @click='clickDownloadInvoice'>发票下载</div>
			</div>
		</template>
		<div class='problem-view' v-else>
			<img class="problem-image" src="../../static/homeIcon/failPay.png" mode="">
			<div class="problem-title">开票异常</div>
			<div class='problem-text'>{{skipData.errIfno}}</div>

			<div class="problem-info-view">
				<div class="info-text-view">
					<div class="info-title">交易时间</div>
					<div class="info-detail">{{skipData.createTime}}</div>
				</div>
				<div class="info-text-view">
					<div class="info-title">油站地址</div>
					<div class="info-detail">{{skipData.stationName}}</div>
				</div>
			</div>
		</div>
		邮箱编辑弹窗
		<u-popup v-model="isShowEmail" mode="bottom" border-radius='20' safe-area-inset-bottom>
			<div class='pop-view'>
				<div class='pop-title-view'>
					<div class='pop-view-title'>请确认发票接收邮箱</div>
					<div class='pop-view-detail'>邮箱可修改</div>
				</div>
				<div class='pop-cell'>
					<div class='pop-cell-title'>预留邮箱</div>
					<input class='pop-cell-detail' placeholder="请输入您的邮箱" type="text" v-model="inputEmail" />
					<img class='pop-cell-icon' src="@/static/edit-icon.png" mode="">
				</div>
				<div class='pop-btn' @click='clickSend'>确认发送</div>
			</div>
    </u-popup>-->
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getBillingDetails, sendInvoiceAgain, reverseInvoiceApi } from '@/api/my-center.js';
import projectConfig from '../../../../../project.config';
export default {
    name: 'invoice-history',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            projectConfig,
            //隐藏收起判断值
            downshow: true,
            //发票数据
            invoiceObj: null,
            skipData: {}, // 传参数据
            isShowEmail: false,
            inputEmail: '', // 邮箱数据
            downInvoiceUrl: '',

            toolTipShow: false,

            naviBg: projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/bg.png',
        };
    },
    computed: {
        calcConHeight() {
            // 无底部操纵栏
            return `calc(100vh - 88px - 48rpx - 74rpx - env(safe-area-inset-bottom))`;
        },
    },
    async onLoad(options) {
        // this.naviBg = 'data:image/png;base64,' + wx.getFileSystemManager().readFileSync('/static/img/bg.png', 'base64');
        if (options.id) {
            this.getInvoiceDetail(options.id);
        } else {
            let invoiceObj = options.detail ? JSON.parse(decodeURIComponent(options.detail)) : null;
            this.invoiceObj = invoiceObj;
            this.inputEmail = invoiceObj.email;
        }
        this.loadImages();
    },
    methods: {
        async loadImages() {
            this.naviBg = await this.fetchAndConvertToBase64(this.projectConfig.baseImgUrl + '/uniapp/uni-mall/static/img/bg.png');
        },
        async fetchAndConvertToBase64(url) {
            try {
                const res = await uni.request({
                    url: url,
                    responseType: 'arraybuffer',
                });

                if (res[1] && res[1].data) {
                    const base64 = uni.arrayBufferToBase64(res[1].data);
                    return 'data:image/png;base64,' + base64;
                } else {
                    console.error('请求图片失败');
                }
            } catch (error) {
                console.error('请求图片失败:', error);
                return '';
            }
        },
        updownbtn() {
            this.downshow = !this.downshow;
        },
        clickCFInvoice() {
            this.isShowEmail = true;
        },
        async clickSend() {
            if (!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.inputEmail)) {
                return uni.showToast({
                    title: '请输入正确的邮箱号',
                    icon: 'none',
                });
            }
            const res = await sendInvoiceAgain({
                id: this.skipData.id,
                email: this.inputEmail,
            });
            if (res.status != -1) {
                this.isShowEmail = false;
                uni.navigateTo({
                    url: '/packages/invoice-center/pages/send-success/main?src=' + this.invoiceObj.pdfurl,
                });
            } else {
                this.$util.showModal(res.info, true);
            }
        },
        // 预览
        clickLookInvoice() {
            uni.navigateTo({
                url: '/packages/web-view/pages/home/<USER>' + this.invoiceObj.pdfurl,
            });
        },
        //发票下载
        clickDownloadInvoice() {
            let invoiceUrl = this.invoiceObj.pdfurl.split('/');
            this.downInvoiceUrl = 'https://dlj.51fapiao.cn/dlj/v7/selectFpwjImage/' + invoiceUrl[5] + '/0';
            // this.downInvoiceUrl = 'https://digi.aisino.com/dlj/v5/selectFpwjImage/'+invoiceUrl[5]+'/0'
            uni.getSetting({
                success: res => {
                    if (res.authSetting['scope.writePhotosAlbum'] == false || !res.authSetting['scope.writePhotosAlbum']) {
                        //未授权
                        uni.authorize({
                            scope: 'scope.writePhotosAlbum',
                            success(e) {
                                //同意授权
                                this.saveImage();
                            },
                            fail: e => {
                                //未授权
                                uni.showModal({
                                    title: '授权失败',
                                    content: '下载发票需要您的授权',
                                    cancelText: '取消',
                                    confirmText: '去授权',
                                    confirmColor: '#0082e6',
                                    success: rv => {
                                        if (rv.confirm) {
                                            uni.openSetting({
                                                success: e => {
                                                    if (e.authSetting['scope.writePhotosAlbum']) {
                                                        //授权
                                                        this.saveImage();
                                                    }
                                                },
                                            });
                                        }
                                    },
                                });
                            },
                        });
                    } else {
                        //已授权
                        this.saveImage();
                    }
                },
            });
        },
        saveImage() {
            uni.downloadFile({
                url: this.downInvoiceUrl,
                success: res => {
                    uni.showLoading({
                        title: '正在保存',
                    });
                    uni.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: e => {
                            if (e.errMsg == 'saveImageToPhotosAlbum:ok') {
                                uni.hideLoading();
                                uni.showModal({
                                    title: '提示',
                                    content: '发票已保存到相册！',
                                    showCancel: false,
                                    // success:function(){
                                    // }
                                });
                            }
                        },
                    });
                },
            });
        },
        //重开发票
        clickReopen() {
            uni.navigateTo({
                url: `/packages/invoice-center/pages/add-invoice/main?list=${encodeURIComponent(
                    JSON.stringify([this.skipData]),
                )}&ordertype=''&reopen='true'`,
            });
        },

        // 获取详情
        async getInvoiceDetail(id) {
            let { data } = await getBillingDetails({ id });
            this.invoiceObj = data;
        },

        // 复制链接
        copyPath() {
            let url = this.invoiceObj.pdfurl;
            let that = this;
            uni.setClipboardData({
                data: url,
                success() {
                    that.$util.tipsToastNoicon('复制成功');
                },
            });
        },

        // 去自动打印
        toPrintAuto() {
            uni.navigateTo({
                url: '/packages/invoice-center/pages/print-auto/main?id=' + encodeURIComponent(this.invoiceObj.id),
            });
        },

        // 重开
        reSendInvoice() {
            this.toolTipShow = true;
        },
        async reSendInvoiceFunc() {
            this.toolTipShow = false;
            let res = await reverseInvoiceApi({
                id: this.invoiceObj.id,
                type: this.invoiceObj.traceType,
            });
            if (res.status == 0) {
                uni.showModal({
                    content: '发票红冲请求已发送，请您耐心等待',
                    confirmColor: '#1677FF',
                    showCancel: false,
                    success: function (res) {
                        if (res.confirm) {
                            uni.navigateBack();
                        }
                    },
                });
            }
        },
    },
};
</script>

<style scoped lang="scss">
/* .detail-center {
  width: 100%;
  min-height: 100%;
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  // 弹窗
  .pop-view {
    .pop-title-view {
      height: 75px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 25px;
      margin-right: 25px;
      border-bottom: 1rpx solid #bcbcbc;
      .pop-view-title {
        font-size: 18px;
        line-height: 24px;
        color: #333333;
        font-weight: 700;
      }
      .pop-view-detail {
        font-size: 15px;
        line-height: 20px;
        color: #909090;
      }
    }
    .pop-btn {
      margin-top: 10px;
      margin-bottom: 10px;
      line-height: 44px;
      margin-left: 15px;
      width: 345px;
      border-radius: 5px;
      background-color: $btn-color;
      color: #ffffff;
      font-size: 15px;
      font-weight: 700;
      text-align: center;
    }
    .pop-cell {
      display: flex;
      align-items: center;
      height: 60px;
      margin-left: 25px;
      margin-right: 25px;
      .pop-cell-title {
        font-weight: 700;
        font-size: 15px;
        color: #333333;
      }
      .pop-cell-detail {
        font-weight: 700;
        font-size: 15px;
        line-height: 60px;
        flex: 1;
        text-align: right;
        color: #333333;
        margin-left: 15px;
        margin-right: 15px;
      }
      .pop-cell-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
  // 异常订单
  .problem-view {
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;

    .problem-image {
      margin-top: 20px;
      height: 100px;
      width: 100px;
    }

    .problem-title {
      margin-top: 10px;
      font-weight: 700;
      color: #333333;
      font-size: 18px;
    }

    .problem-text {
      margin-top: 10px;
      color: #909090;
      font-size: 14px;
    }

    .problem-detail {
      margin-top: 5px;
      color: #909090;
      font-size: 14px;
    }

    .problem-info-view {
      margin-top: 30px;
      background-color: #ffffff;
      border-radius: 5px;

      .info-text-view {
        display: flex;
        align-items: center;
        height: 50px;
        width: 345px;

        .info-title {
          width: 100px;
          font-size: 14px;
          margin-left: 10px;
          font-weight: 700;
        }

        .info-detail {
          font-size: 14px;
        }
      }
    }
  }

  // 信息cell
  .x-bottom {
    margin-bottom: calc(64px + env(safe-area-inset-bottom));
  }

  .top {
    margin-top: 10px;
  }

  .cell-title {
    font-size: 15px;
    line-height: 30px;
    padding-top: 10px;
    padding-bottom: 5px;
    font-weight: 700;
    margin-left: 15px;
  }

  .list-cell {
    background-color: #ffffff;
    border-radius: 5px;
    margin-left: 15px;
    width: 345px;

    .list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      box-sizing: border-box;
      margin-left: 10px;
      margin-right: 10px;

      .item-title {
        color: #909090;
        font-size: 12px;
      }

      .item-detail {
        color: #333333;
        font-size: 12px;
      }
      .color-ff8200 {
        color: #ff8200;
      }
    }

    .line {
      border-bottom: 0.5px solid #bcbcbc;
    }
  }

  // 下方btn
  .add-footer {
    width: 100vw;
    background: #ffffff;

    position: fixed;
    bottom: 0;
    padding: 10px 15px 10px 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
    display: flex;

    .footer-left {
      flex: 1;
      border-radius: 5px;
      border: 1px solid $btn-color;
      background-color: $btn-mantle-color;
      color: $btn-color;
      line-height: 44px;
      text-align: center;
      font-weight: bold;
      box-sizing: border-box;
      font-size: 15px;
    }

    .footer-btn {
      flex: 1;
      margin-left: 10px;
      height: 44px;
      background: $btn-color;
      border-radius: 5px;
      font-size: 15px;
      text-align: center;
      font-weight: bold;
      color: #ffffff;
      line-height: 44px;
    }
  }
} */

.detail-center {
    width: 100vw;
    height: 100vh;
    background: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);
    .detail-top {
        width: 100%;
        height: 384rpx;
        background-size: 750rpx 384rpx;
        background-position: center center;
        background-repeat: no-repeat;
    }
    .detail-content {
        position: relative;
        top: -184rpx;
        padding: 24rpx;
        .scroll {
            height: 100%;
            box-sizing: border-box;
            .detail-section {
                padding: 24rpx 0;
                border-radius: 16rpx;
                background: #fff;
                margin-bottom: 24rpx;
                .section-item {
                    display: flex;
                    font-size: 28rpx;
                    margin-bottom: 32rpx;
                    padding: 0 24rpx;
                    align-items: center;
                    .left {
                        width: 180rpx;
                        color: #999999;
                    }
                    .right {
                        color: #333333;
                        flex: 1;
                    }
                }
                .my-line {
                    opacity: 0.4;
                    border-bottom: 2px dotted #979797;
                    position: relative;
                    margin: 0 24rpx 32rpx;
                    &::before,
                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        width: 30rpx;
                        height: 30rpx;
                        border-radius: 50%;
                        background: #f5f5f5;
                        top: -13rpx;
                        z-index: 9;
                    }
                    &::before {
                        left: -36rpx;
                    }
                    &::after {
                        right: -36rpx;
                    }
                }
            }
            .detail-download {
                padding: 24rpx;
                border-radius: 16rpx;
                background: #fff;
                color: #999;
                font-size: 26rpx;
                display: flex;
                flex-wrap: wrap;
                p {
                    color: #f96702;
                    text-decoration: underline;
                }
            }
        }
    }
    .detail-bottom-action {
        width: 100%;
        position: fixed;
        // height: calc(110rpx + env(safe-area-inset-bottom) + );
        left: 0;
        bottom: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        background: #fff;
        padding: 24rpx 24rpx calc(env(safe-area-inset-bottom) + 24rpx);
        .btn {
            min-width: 168rpx;
            text-align: center;
            height: 74rpx;
            border: 2rpx solid #f96702;
            color: #f96702;
            border-radius: 8rpx;
            margin-right: 16rpx;
            padding: 0 24rpx;
            line-height: 74rpx;
            &:last-child {
                margin-right: 0;
            }
            &.full {
                background: #f96702;
                color: #fff;
            }
        }
    }
}

.toolTipDiv {
    .toolTip_title {
        text-align: center;
        font-size: 36rpx;
        padding-top: 40rpx;
        margin-bottom: 16rpx;
    }
    .toolTip_content {
        padding: 10rpx 35rpx;
        width: 650rpx;
    }
    .toolTip_foot {
        margin-top: 40rpx;
        text-align: center;
        border-top: 1px solid #f4f4f4;
        display: flex;
        div {
            height: 100rpx;
            line-height: 100rpx;
            width: 50%;
            text-align: center;
            font-size: 36rpx;
            color: #1677ff;
            &:last-child {
                font-weight: 600;
            }
        }
    }
}
</style>
