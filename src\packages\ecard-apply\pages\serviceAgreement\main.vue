<template>
    <div class="agreement">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :background="pageConfig.bgColor"
            :title-color="pageConfig.titleColor"
            :back-text="title"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="agreement-content">
            <template v-for="item in list">
                <u-parse :html="item.content"></u-parse>
            </template>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import { getNewsList } from '@/api/home.js';

export default {
    name: 'main',
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            list: [],
            title: '',
        };
    },
    onLoad() {
        let values = this.$mp.query.value;
        if (values == 17) {
            this.title = '用户协议';
        } else if (values == 63) {
            this.title = '隐私政策';
        } else if (values == 64) {
            this.title = '充值协议';
        } else if (values == 71) {
            this.title = '常见问题';
        } else if (values === 91)
            getNewsList({ values }).then(res => {
                this.list = res.data || [];
            });
    },
    methods: {},
};
</script>

<style scoped lang="scss">
.agreement {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;

    &-content {
        margin: 10px;
        padding: 10px;
        background-color: #ffffff;
        border-radius: 5px;
    }
}
</style>
