<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :border-bottom="false" title="修改支付密码"></zj-navbar>
            <div class="f-1 bg-F7F7FB padding-16">
                <div class="fl-column fl-al-sta">
                    <div class="current border-rad-4" @click="showKeyboard1">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            originalPasswordShow || '请输入原密码'
                        }}</div>
                    </div>
                    <div class="current border-rad-4" @click="showKeyboard2">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            newPasswordShow || '请输入新密码'
                        }}</div>
                    </div>
                    <div class="current border-rad-4" @click="showKeyboard3">
                        <div ref="editableDiv" class="border-box inputBox bg-fff border-rad-4 font-14 color-666">{{
                            confirmNewPasswordShow || '请输入新密码'
                        }}</div>
                    </div>
                </div>
                <div class="footer">
                    <div class="fl-row fl-al-cen fl-jus-bet footer-text">
                        <div class="font-12 color-333 fl-row fl-al-cen">
                            <img src="../../images/tips.png" alt />
                            支付密码为6位数字
                        </div>
                        <div class="color-E64F22 font-14" @click="forgotPassword">忘记密码</div>
                    </div>
                    <div class="footer-text font-13 color-818183"
                        >昆仑e享卡与加油卡移动支付共用同一个密码，修改后加油卡线下支付密码不受影响;</div
                    >
                    <div class="footer-text font-13 color-818183">能源e站App和小程序采用统一的支付密码，修改后同时生效。</div>
                </div>
                <div class="btn primary-btn border-rad-8 shad-ef font-18 color-fff" @click="confirmBtn()">确认修改</div>
            </div>
            <!-- #ifdef MP-WEIXIN -->
            <safe-password id="passwordKeyboardId" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <safe-password id="passwordKeyboardId" ref="handlePasswordKeyboardRef" title="安全键盘"></safe-password>
            <!-- #endif -->
            <!-- #ifdef MP-MPAAS -->
            <div v-if="isHarmony">
                <keyboard-plugin></keyboard-plugin>
            </div>
            <!-- #endif -->
            <!-- #ifdef H5-CLOUD -->
            <cloudKeyBordPlugin ref="cloudKeyboardRef"></cloudKeyBordPlugin>
            <!-- #endif -->
            <zj-show-modal>
                <!-- <div class="show_model_div">修改成功</div> -->
            </zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { passwordModify } from '../../../../s-kit/js/v3-http/https3/wallet.js';
// #ifdef MP-MPAAS
import harmonyMixin from './diff-environment/harmony-change-pay-password.js';
// #endif
// #ifndef MP-MPAAS || H5-CLOUD 
import wxMixin from './diff-environment/wx-change-pay-password.js';
import zfbMixin from './diff-environment/zfb-change-pay-password.js';
// #endif
// #ifdef H5-CLOUD
import cloudForgetPassword from './diff-environment/cloud-change-pay-password.js'
import cloudKeyBordPlugin from '../../../../s-kit/js/v3-plugin/cloudKeyBordPlugin.vue'
// #endif
import { clientCode } from '../../../../../project.config.js';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    name: 'change-payment-password',
    components: {
        // #ifdef H5-CLOUD
        cloudKeyBordPlugin
        // #endif
    },
    props: {},
    data() {
        return {
            fromInput: 'color:#999999;font-size:14px;',
            current: {
                phoneNumber: '',
                passWord: '',
                againPassWord: '',
            },
            isH5CLOUD:false,
            passwordKeyboardRef: '',
            // showPassword1: true,
            // showPassword2: true,
            // showPassword3: true,
            oldPassword: '',
            originalPasswordLength: '',
            originalPasswordShow: '',
            newPassword: '',
            newPasswordLength: '',
            newPasswordShow: '',
            confirmPassword: '',
            confirmNewPasswordLength: '',
            confirmNewPasswordShow: '',
            refer: '',
            passWordOpenVal: {},
        };
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        harmonyMixin,
        // #endif
        // #ifndef MP-MPAAS || H5-CLOUD 
        wxMixin, 
        zfbMixin,
        // #endif
        // #ifdef H5-CLOUD
        cloudForgetPassword
        // #endif
    ],
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    onShow() {},
    async onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.refer) this.refer = params.refer;
        }
        this.$sKit.mpBP.tracker('修改支付密码', {
            seed: 'modifyPsdBiz',
            pageID: 'modifyPsdPage', // 页面名
            refer: this.refer, // 来源
            channelID: clientCode, // C10/C12/C13
        });
        // #ifdef MP-MPAAS
        if (this.isHarmony) {
            this.$nextTick(() => {
                this.getPlugin();
            });
        }
        // #endif
    },
    methods: {
        // async getPlugin() {
        //     // #ifndef H5-CLOUD 
        //     await this.$cnpcBridge.isCutScreen(true);
        //     // #endif
        //     let result = await this.$sKit.keyBordPlugin.initRef();
        //     this.$store.commit('setAccountDataPlugin', result);
        //     this.passwordKeyboardRef = result;
        // },
        // showPwClick (num, val) {
        //   switch (num) {
        //     case 1:
        //       this.showPassword1 = val
        //       break;
        //     case 2:
        //       this.showPassword2 = val
        //       break;
        //     case 3:
        //       this.showPassword3 = val
        //       break;

        //     default:
        //       break;
        //   }
        // },
        //原密码
        showKeyboard1() {
            // #ifdef MP-MPAAS || H5-CLOUD 
            if (this.isHarmony || this.isH5CLOUD) {
                /**
             * keyboardType 必填  number： 数字键盘  letter：字母键盘
                passwordType 必填  payment：支付密码；setup：设置密码；modify：修改密码
                numberPassword 非必填 数字密码的长度，最小为4最大为8，默认为4
                letterPassword 非必填 字母键盘密码长度 最小为4最大为25，默认4-25
                regular 非必填 字母键盘正则校验，只能验证类型，如数字字母特殊符号
                setText 非必填，默认文字为支付
                passwordInputShow 非必填，默认显示。 有且值为0时不显示
                mongolianlayer 非必填，蒙层。 有且值为1显示
             * //回调函数 clickCallback 键盘点击事件输出值
                passwordType  为payment只输入一次密码
                            为setup 输入二次密码
                为modify 输入3次密码

                clickCallback() {
                                passwordInstance.getFirstLength()  //第一次密码
                                passwordInstance.getSecondLength() //第二次密码
                                passwordInstance.getThirdLength() //第三次密码
                }
             * */
                let params = {
                    keyboardType: 'number',
                    passwordType: 'modify',
                    numberPassword: 6,
                    setText: '支付',
                    mongolianlayer: 1,
                };
                console.log('this.passwordKeyboardRef', this.passwordKeyboardRef);
                const passwordInstance = this.passwordKeyboardRef;
                passwordInstance.openKeyboard(
                    params,
                    () => {
                        // const firstLength = this.passwordKeyboardRef.getFirstLength()
                        // console.log(firstLength)
                        this.originalPasswordShow = passwordInstance.getFirstLength();
                        this.newPasswordShow = passwordInstance.getSecondLength();
                        this.confirmNewPasswordShow = passwordInstance.getThirdLength();
                    },
                    val => {
                        console.log('密码参数：', val);
                        this.oldPassword = val.oldcipherText;
                        this.newPassword = val.cipherText;
                        this.confirmPassword = val.cipherText;
                        this.passWordOpenVal = val;
                    },
                );
            }
            // #endif
            // #ifndef MP-MPAAS || H5-CLOUD 
            this.passwordKeyboardRef.openKeyboard(
                'password_unique1',
                6,
                async pwd => {
                    this.originalPasswordLength = await this.passwordKeyboardRef.getLength('password_unique1');
                    this.oldPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique1');
                    this.originalPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘1的关闭函数');
                },
            );
            // #endif
        },
        //新密码
        // #ifndef MP-MPAAS || H5-CLOUD 
        showKeyboard2() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique2',
                6,
                async pwd => {
                    this.newPasswordLength = await this.passwordKeyboardRef.getLength('password_unique2');
                    this.newPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique2');
                    this.newPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘2的关闭函数');
                },
            );
        },
        // #endif
        //确认密码
        // #ifndef MP-MPAAS || H5-CLOUD 
        showKeyboard3() {
            this.passwordKeyboardRef.openKeyboard(
                'password_unique3',
                6,
                async pwd => {
                    this.confirmNewPasswordLength = await this.passwordKeyboardRef.getLength('password_unique3');
                    this.confirmPassword = await this.passwordKeyboardRef.getCipherPWD('password_unique3');
                    this.confirmNewPasswordShow = pwd;
                },
                () => {
                    console.log('密码键盘3的关闭函数');
                },
            );
        },
        // #endif
        // 忘记密码
        forgotPassword() {
            this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/change-password/main', {}, 'navigateTo');
        },

        /**
         * @description  : 确认修改事件
         * @param         {String} oldPassword -原密码
         * @param         {String} newPassword -新密码
         * @param         {String} confirmPassword -确认密码
         * @return        {*}
         */
        async confirmBtn() {
            this.$sKit.mpBP.tracker('修改支付密码', {
                seed: 'modifyPsdBiz',
                pageID: 'comfirmUpdateBut', // 页面名
                refer: this.refer, // 来源
                channelID: clientCode, // C10/C12/C13
            });
            // #ifndef MP-MPAAS || H5-CLOUD 
            let samepassword = this.passwordKeyboardRef.equal('password_unique2', 'password_unique3');
            // #endif
            if (!this.oldPassword) {
                uni.showToast({
                    title: '请输入原密码',
                    icon: 'none',
                });
                return;
            } else {
                // #ifdef MP-MPAAS  || H5-CLOUD 
                if ((this.isHarmony || this.isH5CLOUD) && this.originalPasswordShow.length !== 6) {
                    uni.showToast({
                        title: '请输入6位原密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif

                // #ifndef MP-MPAAS || H5-CLOUD 
                if (this.originalPasswordLength !== 6) {
                    uni.showToast({
                        title: '请输入6位原密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif
            }
            if (!this.newPassword) {
                uni.showToast({
                    title: '请输入新密码',
                    icon: 'none',
                });
                return;
            } else {
                // #ifdef MP-MPAAS || H5-CLOUD 
                if ((this.isHarmony || this.isH5CLOUD) && this.newPasswordShow.length !== 6) {
                    uni.showToast({
                        title: '请输入6位新密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif
                // #ifndef MP-MPAAS || H5-CLOUD 
                if (this.newPasswordLength !== 6) {
                    uni.showToast({
                        title: '请输入6位新密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif
            }
            if (!this.confirmPassword) {
                uni.showToast({
                    title: '请再次输入新密码',
                    icon: 'none',
                });
                return;
            } else {
                // #ifdef MP-MPAAS || H5-CLOUD 
                if ((this.isHarmony || this.isH5CLOUD) && this.confirmNewPasswordShow.length !== 6) {
                    uni.showToast({
                        title: '请输入6位新密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif
                // #ifndef MP-MPAAS || H5-CLOUD 
                if (this.confirmNewPasswordLength !== 6) {
                    uni.showToast({
                        title: '请再次输入6位新密码',
                        icon: 'none',
                    });
                    return;
                }
                // #endif
            }
            // #ifndef MP-MPAAS || H5-CLOUD 
            if (samepassword == 1) {
                this.newPassword = '';
                this.newPasswordShow = '';
                this.confirmPassword = '';
                this.confirmNewPasswordShow = '';
                uni.showToast({
                    title: '两次密码不一致，请重新输入',
                    icon: 'none',
                });
                return;
                // return this.$util.noneToast("两次密码不一致，请重新输入");
            }
            // #endif
            let params = {
                oldPassword: this.oldPassword,
                newPassword: this.newPassword,
                confirmPassword: this.confirmPassword,
            };
            if (this.isHarmony || this.isH5CLOUD) {
                params.deviceId = this.passWordOpenVal.deviceId;
                params.keyboardDataCacheId = this.passWordOpenVal.keyboardDataCacheId;
                params.uniqueId = this.passWordOpenVal.uniqueId;
            }

            console.log('params', params);
            let res = await passwordModify(params);
            console.log(res, 'res======passwordModify');
            if (res.success) {
                this.$sKit.mpBP.tracker('修改支付密码', {
                    seed: 'modifyPsdBiz',
                    pageID: 'settingPsdsuccessToast', // 页面名
                    refer: this.refer, // 来源
                    channelID: clientCode, // C10/C12/C13
                });
                uni.showToast({
                    title: '修改支付密码成功',
                    duration: 2000,
                    icon: 'none',
                });
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 2, //返回层数，2则上上页
                    });
                }, 2000);
            }
        },
    },
    filter: {},
    watch: {},
    async beforeDestroy() {
        // #ifdef MP-MPAAS
        await this.$cnpcBridge.isCutScreen(false);
        // #endif
    },
    async destroyed() {
        // #ifdef MP-MPAAS
        await this.$cnpcBridge.isCutScreen(false);
        // #endif
    },
};
</script>
<style scoped lang="scss">
.view {
    .current {
        width: 100%;
        height: 44px;
        margin-bottom: 16px;
        position: relative;
        .inputBox {
            height: 44px;
            line-height: 44px;
            padding-left: 15px;
        }
        input {
            width: 100%;
            height: 100%;
            padding-left: 15px;
            background: #fff;
        }

        img {
            position: absolute;
            right: 14px;
            top: 50%;
            z-index: 2;
            transform: translateY(-50%);
        }

        .close_div {
            width: 18px;
            height: 9px;
        }

        .open_div {
            width: 22px;
            height: 15px;
        }
    }

    .footer {
        .footer-text {
            margin-bottom: 12px;

            img {
                width: 15px;
                height: 15px;
                margin-right: 5px;
            }
        }
    }

    .btn {
        height: 44px;
        line-height: 44px;
        width: 100%;
        margin-top: 16px;
        margin-bottom: 12px;
    }
}

.show_model_div {
    text-align: center;
}
</style>

<style lang="scss">
.forgot_pas_inp {
    font-size: 14px;
    color: #999999;

    line-height: 20px;
}
</style>
