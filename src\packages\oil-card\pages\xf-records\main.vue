<template>
    <div class="xf-records">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            back-text="消费记录"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="slide_type_list">
            <div
                class="slide_type_list_view"
                v-for="(item, index) in typeList"
                :key="index"
                :class="{ is_selected: active == item.type }"
                @click="changeType(item.type)"
            >
                <div>{{ item.name }}</div>
            </div>
        </div>
        <scroll-view class="content-list" scroll-y @scrolltolower="loadMore">
            <div class="content-box" v-if="dataList.length > 0">
                <div class="list-item" v-for="(item, index) in dataList" :key="index">
                    <div class="title">
                        <img src="@/static/cnpc-logo.png" alt />
                        {{ item.orgName }}
                    </div>
                    <div class="detail">商品内容：{{ item.goodsName }}</div>
                    <div class="detail">创建时间：{{ item.createTime }}</div>
                    <div class="prize" v-if="active == 1">
                        <span>￥</span>
                        <my-price color="#f96702" intFont="40rpx" floatFont="22rpx" :price="item.amount"></my-price>
                    </div>
                    <div class="prize" v-else>
                        <div class="jifen">{{ item.amount }}</div>
                    </div>
                </div>
            </div>
            <div v-if="dataList.length == 0 || !hasMore" class="no-more">
                <u-loadmore status="nomore" />
            </div>
        </scroll-view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig';
import myPrice from '@/components/price/price.vue';
import { getCardConsumeDetailsApi } from '@/api/my-center';
export default {
    components: {
        myPrice,
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            typeList: [
                {
                    name: '资金消费',
                    type: 1,
                },
                {
                    name: '积分消费',
                    type: 2,
                },
            ],
            active: 1,
            dataList: [],
            page: 1,
            cardNo: '',
            hasMore: true,
        };
    },
    onLoad(options) {
        if (options.cardNo) {
            this.cardNo = options.cardNo;
            this.loadData();
        }
    },
    methods: {
        changeType(type) {
            this.page = 1;
            if (this.active != type) {
                this.active = type;
                this.hasMore = true;
                this.dataList = [];
                this.loadData();
            }
        },

        async loadData() {
            // uni.showLoading()
            let params = {
                cardNo: this.cardNo,
                pageNo: this.page,
                accountType: this.active,
                pageSize: 10,
            };
            let res = await getCardConsumeDetailsApi(params);
            if (res.status == 0) {
                this.dataList = this.dataList.concat(res.data);
                if (res.data.length == 0 || res.data.length < 10) {
                    // uni.showModal({
                    //   title: '提示',
                    //   content: '本查询结果只显示12个月内记录，如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                    //   confirmColor: '#FF8200',
                    //   showCancel: false,
                    //   success: () => {
                    //   }
                    // })
                    this.hasMore = false;
                }
                if (res.data.length == 0) {
                    uni.showModal({
                        title: '提示',
                        content: '本查询结果只显示12个月内记录，如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                        confirmColor: '#FF8200',
                        showCancel: false,
                        success: () => {},
                    });
                }
            } else if (res.status == -1) {
                let str = '本查询结果只显示12个月内记录，如需更多查询记录请登录www.95504.net查询。(6100)';
                uni.showModal({
                    title: '提示',
                    content:
                        str != res.info
                            ? res.info
                            : '本查询结果只显示12个月内记录如需更多查询记录请登录https://www.kunlunjyk.com查询。(6100)',
                    confirmColor: '#FF8200',
                    showCancel: false,
                    success: () => {},
                });
            }
            // uni.hideLoading()
        },
        loadMore() {
            if (this.hasMore) {
                this.page++;
                this.loadData();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.xf-records {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);
    .slide_type_list {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 84rpx;
        background: #fff;
        .slide_type_list_view {
            height: 100%;
            font-size: 34rpx;
            line-height: 84rpx;
            color: #333333;
        }

        .is_selected {
            color: #f96702;
            position: relative;
        }

        .is_selected:before {
            content: '';
            position: absolute;
            width: 100%;
            height: 2rpx;
            background: #f96702;
            left: 0;
            bottom: 0;
        }
    }
    .content-list {
        min-height: 0;
        flex: 1;
        .content-box {
            padding: 24rpx;
            .list-item {
                padding: 24rpx;
                border-radius: 16rpx;
                background: #fff;
                margin-bottom: 24rpx;
                .title {
                    font-size: 26rpx;
                    img {
                        display: inline-block;
                        height: 36rpx;
                        width: 36rpx;
                        margin-right: 16rpx;
                    }
                }
                .detail {
                    font-size: 22rpx;
                    color: #999999;
                    margin-top: 15rpx;
                }
                .prize {
                    margin-top: 25rpx;
                    display: flex;
                    align-items: baseline;
                    font-size: 40rpx;
                    color: #f96702;
                    .jifen {
                        font-size: 30rpx;
                    }
                    span {
                        font-size: 22rpx;
                    }
                }
            }
        }
        .no-more {
            padding: 24rpx 0;
            text-align: center;
            ::v-deep .u-load-more-wrap {
                background-color: transparent !important;
                view {
                    background-color: transparent !important;
                }
            }
        }
    }
}
</style>
