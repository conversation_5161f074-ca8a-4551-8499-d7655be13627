<template>
    <div class="payment-password">
        <!-- 自定义密码输入弹窗 -->
        <u-popup v-model="isShow" mode="center" negative-top="200" border-radius="20" :mask="true" :mask-close-able="false">
            <div class="payment-box">
                <img @click="close" class="payment-box-close" src="~@/static/black-close.png" />
                <div class="payment-box-title">
                    <span class="payment-title-text">请输入验证码</span>
                </div>
                <div class="remind">{{ reservedPhoneNumber }}</div>
                <div class="payment-box-footer">
                    <!-- <input type="text" /> -->
                    <!-- <u-input v-model="value" :focus="true" :type="type" :border="border" /> -->
                    <u-message-input
                        ref="uMessageInputRef"
                        :maxlength="maxlength"
                        :width="100"
                        :breathe="false"
                        :focus="inputShowed"
                        :value="password"
                        @change="change"
                        @finish="finish"
                    ></u-message-input>
                </div>
            </div>
        </u-popup>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
.l
<script>
export default {
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        maxlength: {
            // 密码最大长度
            type: Number,
            default: 4,
        },
        reservedPhoneNumber: {
            type: String,
        },
    },
    data() {
        return {
            isShowKeyboard: false, // 是否显示自定义键盘
            password: '', // 支付需要输入的密码
            showErrorBox: this.showError,
            inputShowed: false,
        };
    },
    watch: {
        isShow() {
            console.log(this.isShow);
            setTimeout(() => {
                this.inputShowed = this.isShow;
            }, 300);
        },
        password(val) {
            if (val.length === this.maxlength) {
                this.$emit('migrationVerificationCode', val);
                this.inputShowed = false;
                this.isShow = false;
            }
            // this.$emit("input", val)
        },
    },
    methods: {
        finish(e) {
            // console.log('finishfinishfinishfinish', e);
            // this.$emit('migrationVerificationCode', e)
            // this.inputShowed = false
            // this.isShow = false
        },
        change(e) {
            if (this.password.length !== this.maxlength) {
                this.password = e;
            }
            console.log('内容改变，当前值为：' + e);
        },
        // 关闭密码框
        close() {
            this.password = '';
            // this.isShow = false
            this.$emit('close');
        },
    },
};
</script>
<style lang="scss" scoped>
.payment-password {
    position: absolute;
    // top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    // 自定义支付密码弹窗
    .payment-box {
        position: relative;
        width: 330px;
        height: 351rpx;
        overflow: hidden;
        .payment-box-close {
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 20px;
            padding: 10px;
        }
        .payment-box-title {
            margin-top: 20px;
            text-align: center;
            .payment-title-text {
                color: #333333;
                font-size: 18px;
                font-weight: bold;
            }
        }
        .remind {
            text-align: center;
        }
        .payment-box-content {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 15px;
            .payment-content-merchant {
                color: #909090;
                font-size: 15px;
                font-weight: 400;
            }
            .payment-content-amount {
                line-height: 42px;
                color: #333;
                font-weight: bold;
                .payment-amount-sign {
                    font-size: 15px;
                }
                .payment-amount-text {
                    font-size: 30px;
                }
            }
        }
        .payment-box-footer {
            height: 50px;
            margin: 20px 15px 0;
            & ::v-deep .u-char-item {
                margin: 0 -1px -1px 0; /** 避免左右边框重叠 */
                border-color: #dcdcdc !important;
                border-width: 2px;
            }
        }
    }
    .password-keyboard {
        background: red;
        ::v-deep .u-keyboard {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: #fff;
            padding-bottom: env(safe-area-inset-bottom);
            z-index: 100003;
        }
    }
    .payment-error {
        width: 100%;
        padding: 49px 93px;
        text-align: center;
        font-size: 16px;
        color: #333;
        font-weight: bold;
    }
}
</style>
