<template>
    <div class="one_lick_login">
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>
        <div class="padding_input">
            <div class="main_heading marign_bottom">设置密码</div>
            <input
                type="text"
                maxlength="11"
                class="input_div"
                placeholder-class="phone_input"
                placeholder="请输入密码（6～20位字母数字组合）"
            />
            <input type="text" maxlength="11" class="input_div" placeholder-class="phone_input" placeholder="请再次输入密码" />
        </div>
        <div class="primary-btn btn" v-if="type1">完成</div>
        <div class="primary-btn btn" v-else>跳过设置密码，完成注册</div>
        <div class="other_login_methods_btn" v-if="type2">密码设置，完成注册</div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'one-click-login',
    data() {
        return {
            select: false,
            type1: false,
            type2: false,
        };
    },
    onLoad(option) {
        let routerInfo = JSON.parse(decodeURIComponent(option.data));
        this.type1 = routerInfo.type1 || false;
        this.type2 = routerInfo.type2 || false;
    },
    methods: {
        loginBtn() {
            this.$sKit.layer.showToast({
                title: '一键登录失败，跳转至普通登录',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../style/style.scss';

.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
}

.padding_input {
    padding: 16px;
}

.operator {
    margin-bottom: 16px;
}

input {
    height: 51px;
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    font-size: 21px;
    font-weight: 600;
    color: #333333;
    line-height: 30px;
    margin-bottom: 12px;
    padding-left: 20px;
    box-sizing: border-box;
}

.agreement_div {
    margin-bottom: 16px;
    color: #999999;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;

    img {
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
    }
}

.argreement_rules {
    color: #e64f22;
}

.btn {
    width: 343px;
    height: 44px;
    line-height: 44px;
    border-radius: 8px;
    margin: 0 auto;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
}
</style>

<style lang="scss">
.phone_input {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
}
</style>
