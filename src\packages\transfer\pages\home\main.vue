<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <view class="page-union">
        <image class="transferAccountBg_style" :src="transferAccountBg" alt=""></image>
        <view class="content_div">
            <view class="container">
                <view class="logo_div">
                    <image class="logo_style" :src="logo"></image>
                    <view class="">
                        <view class="title">能源e站</view>
                        <view class="text">e享加油生活</view>
                    </view>
                </view>
            </view>
            <view class="agreement_div">
                <view class="select_div">
                    <image class="" :src="empty" v-if="agree" @click="agree = false"></image>
                    <image class="" :src="successSel" @click="agree = true" v-else></image>
                </view>
                <view class="agreement_text">
                    <view class="font-12 agreement_name weight-400" @click="agree = !agree">我已阅读并同意能源e站</view>
                    <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(17)"> 《用户协议》 </view>
                    <view class="font-12 agreement_name">和</view>

                    <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view>
                </view>
            </view>
            <view class="auth-box" v-if="!isLogin">
                <view v-if="!agree">
                    <!-- #ifdef MP-ALIPAY || H5 -->
                    <ZfbLoginV3 :autoV3="autoV3" :layer="true" :silence="false" @success="onLoginAfterEvent">
                        <button class="primary-btn2">授权登录</button>
                    </ZfbLoginV3>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN || H5 -->
                    <getPhoneNumber v-if="!select" @loginOver="loginOver">
                        <div class="primary-btn2 btnStyle">授权登录</div>
                    </getPhoneNumber>
                    <!-- #endif -->
                </view>
                <button v-if="agree" class="primary-btn2" @click="onCheckAgree()">授权登录</button>
            </view>

            <view class="loading-box" v-if="isLogin">
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
            </view>
        </view>

        <view class="footer">如需帮助，可联系：956100</view>
        <zj-show-modal>
            <div style="display: inline">
                <view class="agreement_name font-12 weight-400">登录能源e站，请先阅读并同意能源e站</view>
                <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(17)">《用户协议》</view
                ><view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view></div
            >
        </zj-show-modal>
    </view>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import pagesJson from '@/pages.json';
import ZfbLoginV3 from '@/components/zfb-login-v3/zfb-login-v3.vue';
import empty from '@/static/empty.png';
import logo from '@/static/img/cnpc-logo.png';
import successSel from '@/static/successSel.png';
import transferAccountBg from '@/static/transferAccount-bg.png';
import projectConfig from '../../../../../project.config';
import getPhoneNumber from '../../../../components/loginV3/getPhoneNumber.vue';
export default {
    mixins: [publicMixinsApi],
    components: {
        // #ifdef MP-ALIPAY
        ZfbLoginV3,
        // #endif
        // #ifdef MP-WEIXIN
        getPhoneNumber,
        // #endif
    },
    data() {
        return {
            query: {},
            isLogin: false,
            autoV3: '', // 非3.0账号自动升级为3.0账号
            agree: true,
            successSel,
            empty,
            transferAccountBg,
            logo,
            // 鉴权页面集合
            authentication: {
                // 开通昆仑e享卡的用户才能进入的页面列表
                eCard: [
                    // e享卡充值页面
                    '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                    // 我的页面 点击 我的钱包
                    '/packages/third-my-wallet/pages/home/<USER>',
                    // 钱包设置页面(修改支付密码，更换归属地......)
                    '/packages/third-my-wallet/pages/wallet-setting/main',
                ],
            },
        };
    },
    computed: {},
    onLoad(query) {
        this.query = query;
        console.log(query, 'query====');
        // 如果扫码(存在q)进行直接赋值或者解构
        // query = {
        //     page: '分包名/页面名  ||   主包名',  // 有page跳page
        //     url: 'h5路径',     // 跳h5页面只传url编码后的url即可
        //     login: '0',   // 是否需要登录，0不需要1需要，不传默认为1。
        //     xxxx: ''     // page跳转，参数放在对象中传入即可
        // h5跳转，h5的参数跟在url后一同编码处理，给中转页的参数放在对象中传入即可
        // 自定义参数不得与page，url，login同名
        // }
        // 因下划线与中线这些在分包名，页面名中常用的符号在路由中符合规则，以及考虑到跳转路由整体的复杂度，所以query不进行url编码，特殊参数特殊处理
        // 例：/pages/unionTransfer/main?page=third-order/order_detail&a=1
        // 例：/pages/unionTransfer/main?page=thirdHome&b=2
        // 例：/pages/unionTransfer/main?url=www.baidu.com%3fc%3d3&d=4
        // this.judgeLogin();
    },
    mounted() {
        // 获取跳转过来的参数
        console.log(this.$mp.query, 'this.$mp.query====');
        this.query = { ...this.$mp.query };
        console.log(this.query, '测试q=====');
        /**
         * 1、有q page=thirdMarketing/luckyDraw&q=https://marketingnp.kunlunjyk.com/randomDraw/main?flowNo=a98110a962e740d79d95d12dbbef9ffe&scancode_time=1736479313
         * 2、跳转首页四个页面传参 page=thirdHome?curTabIndex= 1(钱包)/2(订单)/3(我的)，不传curTabIndex默认挑战到加油页面
         * 3、跳转除了主包外的其他页面 page: '分包名/页面名'&key=value&key2=value2
         * 4、扫码助力裂变通跳转普通分包相同，看3,如果跳转过来的参数有http在生成二维码前进行 url编码(百度搜url编码)
         * */
        // TEST
        this.judgeLogin();
    },
    onShow() {},
    methods: {
        // 登录结束
        async loginOver(res) {
            // 对外开放平台登录，后续补充
            if (res.success) {
                this.isLogin = true;
                await this.handleQuery();
            } else {
                this.$store.commit('setLoginButtonGrayedOut', true);
                this.isLogin = false;
                this.$store.commit('setLoginLoading', false);
            }
            let userId = uni.getStorageSync('userId');
            let tokenInfo = uni.getStorageSync('tokenInfo');
            if (!res.success) {
                obj.flag = JSON.stringify(res);
            }
            console.log(obj, 'obj====');
        },
        judgeLogin() {
            // 判断跳转页面是否需要登录，如果需要先解析参数得到跳转路由后才可以判断，可以先走解析路由和参数的逻辑，待补充
            // 0代表不需要登录，不传值，为空或者其他值是需要登录
            if (this.query?.login === '0') {
                this.handleQuery();
            } else {
                // 获取token，判断是否登录
                let tokenInfo = uni.getStorageSync('tokenInfo');
                if (tokenInfo.accessToken) {
                    this.handleQuery();
                } else {
                    this.isLogin = false;
                    this.login();
                }
            }
        },
        async login() {
            // 走多端自动登录逻辑，在登录成功回调里调handleQuery，失败终止
            // 待补充推荐注册逻辑
            // 支付宝登录

            // 微信登录
            // #ifdef MP-WEIXIN
            let res = await this.$store.dispatch('init', { type: '4' });
            await this.loginOver(res);
            // #endif
        },
        async handleQuery() {
            if (this.query.page) {
                // 匹配页面，获取跳转地址
                let routerUrl = await this.handlePage();
                // 当前路由是否需要进行校验开卡或者登录或者其他的校验
                if (this.authentication.eCard.some(item => item === routerUrl)) {
                    console.log(routerUrl, '需要进行开卡检验');
                    let res = await this.checkWalletStatus();
                    await this.setTimeOutFun();
                    // 如果等于false是代表获取昆仑e享卡状态失败，默认回到首页
                    if (!res) {
                        routerUrl = '/pages/thirdHome/main';
                    }
                }
                // 特殊逻辑根据页面判断添加参数
                let params = this.query;
                // 跳转
                this.$sKit.layer.useRouter(routerUrl, params, 'reLaunch');
            } else if (this.query.url) {
                let routerUrl = `/packages/web-view/pages/home/<USER>
                // 特殊逻辑根据页面判断添加参数
                uni.reLaunch({
                    url: routerUrl,
                });
            }
        },
        // 检查开卡状态失败，让showToast弹窗展示出来
        setTimeOutFun() {
            return new Promise(async (resolve, reject) => {
                setTimeout(() => {
                    resolve(true);
                }, 2000);
            });
        },
        // 检查是否需要检测是否开卡
        checkWalletStatus() {
            return new Promise(async (resolve, reject) => {
                let res = await this.$store.dispatch('getSetWalletStatus');
                console.log(res, '测试');
                if (res && res.success && !res.data.status) {
                    await this.$store.dispatch('zjShowModal', {
                        title: '',
                        content: '您还没有开通昆仑e享卡，是否开通？',
                        confirmText: '去开通',
                        cancelText: '取消',
                        confirmColor: '',
                        cancelColor: '',
                        type: '',
                        success: res => {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                                let params = { refer: 'r08' };
                                let type = 'reLaunch'; // 默认  uni.navigateTo({})
                                this.$sKit.layer.useRouter(url, params, type);
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                                uni.reLaunch({
                                    url: '/pages/thirdHome/main',
                                });
                            }
                        },
                    });
                } else {
                    uni.showToast({
                        title: '检查开卡状态失败',
                        icon: 'none',
                        duration: 2000,
                        mask: true,
                    });
                    resolve(false);
                }
            });
        },

        handlePage() {
            return new Promise((resolve, reject) => {
                const subPackageIndex = this.query.page.indexOf('/');
                console.log(subPackageIndex, 'subPackageIndex====');
                let routerUrl = '';
                if (subPackageIndex > -1) {
                    // 有“/”跳转分包页面
                    const subPackagesArr = pagesJson.subPackages;
                    const subPackage = this.query.page.slice(0, subPackageIndex);
                    // 用包含查找可能会出现以下问题，找order匹配到orderList，所以采用拼接字符串加全等的方式。
                    // 项目中页面只有main.vue文件，后续严格控制页面vue文件起名为main
                    const subPackageObj = subPackagesArr.find(item => item.root === 'packages/' + subPackage);
                    const page = this.query.page.slice(subPackageIndex + 1);
                    const pageObj = subPackageObj.pages.find(item => item.path === `pages/${page}/main`);
                    routerUrl = `/${subPackageObj.root}/${pageObj.path}`;
                    console.log(routerUrl, '匹配到的分包路由页面');
                } else {
                    // 没有“/”跳转主包页面
                    const pagesArr = pagesJson.pages;
                    const pageObj = pagesArr.find(item => item.path === `pages/${this.query.page}/main`);
                    routerUrl = `/${pageObj.path}`;
                }
                resolve(routerUrl);
            });
        },
        // 登录成功后逻辑
        onLoginAfterEvent(type) {
            console.log('登录成功后逻辑', type);
            // this.cshLogic();
            this.handleQuery();
        },
        // 车生活联跳逻辑
        async cshLogic() {
            const { cppeiLoginInfo: userInfo } = uni.getStorageSync('tokenInfo') || {};
            let v3token = uni.getStorageSync('v3token');
            if (!userInfo?.phoneToken && !v3token) {
                this.isLogin = false;
                return;
            }
            const vKey = v3token ? 'v3' : 'v2';
            const qKey = await this.checkWalletStatus(this.query.qKey, vKey);
            this.query.qKey = qKey;
            if (!qKey || !CSH[qKey] || !CSH[qKey][vKey]) {
                if (vKey == 'v3') {
                    uni.reLaunch({
                        url: `${projectConfig?.subPath || ''}/pages/thirdHome/main`,
                    });
                } else if (vKey == 'v2') {
                    uni.reLaunch({
                        url: `/pages/index/index`,
                    });
                }
                return console.warn('联跳参数错误', qKey, vKey);
            }
            let url = '';
            // 跳转到电子券或者电子券使用记录
            if (qKey == 'couponList' && vKey == 'v3') {
                let type;
                try {
                    type = this.query?.type.replace(/'/g, ''); // 去除所有单引号
                } catch (error) {
                    type = '1'; // 设置默认的 type 值
                }

                const urlMap = {
                    '': '/packages/third-coupon-module/pages/coupon-list/main',
                    1: '/packages/third-coupon-module/pages/coupon-list/main',
                    2: '/packages/third-coupon-module/pages/coupon-list/main',
                    10: '/packages/third-coupon-module/pages/coupon-used-record/main',
                    20: '/packages/third-coupon-module/pages/coupon-used-record/main',
                };
                this.query.refer = 'r41';
                url = (projectConfig?.subPath || '') + (urlMap[type] || '/pages/thirdHome/main'); // 默认 URL，如果没有匹配的 type
            } else {
                if (vKey == 'v3') {
                    if (qKey == 'walletOpen') this.query.refer = 'r06';
                    if (qKey == 'wallet') this.query.refer = 'r02';
                    if (qKey == 'walletRecharge') this.query.refer = 'r19';
                    if (qKey == 'invoiceList') this.query.refer = 'r36';
                    if (qKey == 'oilCardList') this.query.refer = 'r47';
                    if (qKey == 'orderDetail' || qKey == 'orderList') this.query.refer = 'r51';
                }
                url = CSH[qKey][vKey];
            }
            const toUrl = url + '?data=' + encodeURIComponent(JSON.stringify(this.query));
            let tokenInfo = uni.getStorageSync('tokenInfo');
            // 埋点
            this.$sKit.mpBP.tracker('车生活跳转区分2.0和3.0会员', {
                seed: 'car_life_member_type',
                member_type: vKey,
                member_no: tokenInfo?.memberNo,
                openid: tokenInfo?.openId,
                toUrl: toUrl,
            });

            console.log('车生活联跳：', this.query, toUrl);
            my.reLaunch({
                url: toUrl,
            });
        },
        // 检查是否同意协议
        onCheckAgree() {
            // my.showToast({
            //     content: '请先同意用户授权协议',
            // });
            this.modalClick();
        },
        modalClick() {
            this.$store.dispatch('zjShowModal', {
                confirmText: '同意',
                cancelText: '我再想想',
                cancelColor: '#666666',
                confirmColor: '#FF3E00',
                success: async res => {
                    if (res.confirm) {
                        this.agree = false;
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('1', '支付宝隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
        async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
    },
};
</script>

<style scoped lang="scss">
.page-union {
    width: 100%;
    background: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

.logo {
    width: 140rpx;
    height: 140rpx;
    margin: 200rpx auto 30rpx;
    display: block;
}

.btn-auth-login {
    color: #fff;
    background-color: rgb(51, 107, 225);
    border-radius: 60rpx;

    &:active {
        opacity: 0.7;
    }
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
}

.subtitle {
    font-size: 28rpx;
    color: #888;
    text-align: center;
    margin: 20rpx 0 80rpx;
}

.agreement-box {
    margin-top: 30rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    white-space: nowrap;
    margin-left: -10px;
    margin-bottom: -1px;

    .link {
        color: #1d77cd;
    }
}

.loading-box {
    padding-top: 80rpx;
    display: flex;
    justify-content: center;
    gap: 10rpx;

    .loading-dot {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: #888;
        animation: bolling 1s alternate infinite;
    }
}

.footer {
    position: fixed;
    bottom: 50rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    width: 100%;
}
.agreement_name {
    display: inline;
}
@keyframes bolling {
    0% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(5px);
    }
}

.transferAccountBg_style {
    width: 100%;
    height: 365px;
    position: absolute;
    z-index: 0;
}
.content_div {
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 152px;
    padding: 0 63px;
}
.logo_style {
    width: 47px;
    height: 45px;
    margin-right: 14px;
}
.container {
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    margin-bottom: 43px;
}

.logo_div {
    display: flex;
}

.title {
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
}
.text {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    letter-spacing: 1px;
}
.agreement_div {
    margin-bottom: 16px;
    display: flex;
    .select_div {
        display: inline-block;
        margin-right: 5px;
        image {
            width: 17px;
            height: 17px;
        }
    }
}
.agreement_text {
    width: 94%;
}
.agreement_name {
    display: inline;
}
</style>
