const payPlugin = requirePlugin('pay-plugin');
import post from '../../../../../s-kit/js/v3-http/post';
export default {
    // #ifdef MP-WEIXIN
    mounted() {
        if (this.isLogin) {
            this.$sKit.wxPayPlugin.initPayPlugin();
        }
    },
    methods: {
        initiatePaymentWx(params = {}, isAuth) {
            return new Promise(async (resolve, reject) => {
                params.extendFiled = await post.addExtendFiled('plugin', { isAuth });
                this.hideLoading();
                await payPlugin
                    .QryPreOrder(params, this.accountDataPlugin)
                    .then(async res => {
                        resolve(res);
                    })
                    .catch(err => {
                        resolve(err);
                    });
            });
        },
    },
    // #endif
};
