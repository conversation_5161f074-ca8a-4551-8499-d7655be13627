// 支付宝账户插件

import { mapState } from 'vuex';

export default {
    // #ifdef MP-ALIPAY
    computed: {
        ...mapState({
            securityPlugin: state => state.thirdIndex.accountDataPlugin,
        }),
    },
    mounted() {
        console.log('zfb securityPlugin mounted');
        const result = this.$refs['handlePasswordKeyboardRef'];
        if (!result) return;
        this.$sKit.keyBordPlugin.initRef(result);
        this.$store.commit('setAccountDataPlugin', result);
    },
    methods: {},
    // #endif
};
