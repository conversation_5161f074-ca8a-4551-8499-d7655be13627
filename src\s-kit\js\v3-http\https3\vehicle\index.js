import { POST } from '../../index';
// 车辆列表
export const plateList = (params, config) => {
    return POST('user.plate.list', params, config);
};
// 车牌颜色
export const plateEnumList = (params, config) => {
    return POST('user.plate.enumList', params, config);
};
// 车牌颜色
export const plateFuelInfo = (params, config) => {
    return POST('oilstation.station.fuelInfo', params, config);
};
// 车牌详情
export const plateDetail = (params, config) => {
    return POST('user.plate.detail', params, config);
};
// 新增或修改车辆
export const plateModifyOrCreate = (params, config) => {
    return POST('user.plate.modifyOrCreate', params, config);
};
// 删除车辆
export const plateDelete = (params, config) => {
    return POST('user.plate.delete', params, config);
};
//
export const queryDictItemListByCode = (params, config) => {
    return POST('oilstation.station.queryDictItemListByCode', params, config);
};
// 获取车辆型号、年份
export const queryBrands = (params, config) => {
    return POST('user.plate.queryBrands', params, config);
};
