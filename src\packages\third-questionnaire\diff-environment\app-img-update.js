import { imageUpload, putUpload, imageDelete, imageDownload, imageGetTempPostInfo } from '../../../s-kit/js/v3-http/https3/user.js';
import { baseType } from '../../../../project.config.js';
export default {
    computed: {},

    data() {
        return {
            previewImageList: [],
        };
    },

    onShow() {},
    methods: {
        // 上传图片点击图片
        handlerUpload(type, commentIndex) {
            // 获取当前时间戳
            const timeData = new Date().getTime();
            uni.chooseImage({
                count: 1,
                sizeType: ['original'], // original 原图，compressed 压缩图，默认二者都有 ,这里使用原图是因为选择压缩图，选择图片时会有白条闪一下
                success: async res => {
                    console.log(res, '=====uni.chooseImage');
                    // 存储图片push进相应的数组进行页面回显
                    let srcImg = res.tempFilePaths[0];
                    // 定义一个接收图片大小的变量
                    let size = '';

                    console.log(srcImg, 'srcImg=====');
                    // 当前判断
                    // res.tempFiles.length 判断当前拍摄或者选择图片时数量不为空
                    // res.tempFiles[0].size 判断拍摄图片时如果size不存在的情况
                    //  如果成立则代表是拍摄图片，反之是选择图片
                    // if (!(res.tempFilePaths.length > 0 && res.tempFiles[0].size)) {
                    if (res.scene == 'camera') {
                        console.log('拍摄照片回来的path=====', res.tempFiles[0].path);
                        // obtainTheSizeOfThePhotographedImage  拍摄图片获取size方法
                        size = await this.obtainTheSizeOfThePhotographedImage(res.tempFilePaths[0]);
                        console.log(size, '拍摄照片回来的size=====');
                    } else {
                        size = res.tempFiles[0].size;
                        console.log(size, '选择相册回来的size=====');
                    }

                    console.log('size---', size);
                    // 10485760 = 10M
                    // 判断当亲图片是否存在size和是否超过10MB
                    if (!size || Number(size) >= 10485760) {
                        uni.showToast({
                            title: '图片文件大小超限，最大10M，请重新上传',
                            icon: 'none',
                        });
                        return
                    }
                    /**
                     * 
                     * 上传场景：
                        1—订单评价、扫码评价图片上传；
                        2—调查问卷图片上传；
                        如果该字段未传值，默认为1，后续可能会增加其他上传场景，也需要区分上传目录
                     * */ 
                    // 如果成立则代表是拍摄图片，反之是选择图片
                    let res3 = await imageUpload({
                        contentType: 'image/png',
                        fileName: timeData,
                    });
                    uni.showLoading({
                        title: '上传中...',
                        mask: true,
                        type:Number(2)
                    });
                    if (res3.success) {
                        // 上传图片的方法
                        this.getImageUpload(res3.data.objectKey, srcImg).then(async data => {
                            // let srcImg = res.tempFilePaths[0];
                            // if (this.evaluateType == 'order') {
                            //     this.questionListDataRows[commentIndex].picList.push(srcImg);
                            //     this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
                            // } else {
                            //     let newObj = this.commentConfigListData;
                            //     newObj.rows[commentIndex].picList.push(srcImg);
                            //     this.questionListDataRows = newObj.rows;
                            //     this.$set(this.questionListDataRows, commentIndex, newObj.rows[commentIndex]);
                            // }
                            //后端接口需要
                            console.log(res3.data.objectKey, 'res3.data.objectKey');
                            uni.hideLoading();
                            if (!this.$sKit.test.isAndroidClient()) {
                                let downRes = await imageDownload({ objectKey: res3.data.objectKey,type:Number(2) });
                                if (this.evaluateType == 'order') {
                                    this.questionListDataRows[commentIndex].picList.push(downRes.data.downloadUrl);
                                    // this.previewImageList = [downRes.data.downloadUrl];
                                    this.previewImageList = this.questionListDataRows[commentIndex].picList;
                                    this.questionListDataRows[commentIndex].images.push(res3.data.objectKey);
                                    this.questionListDataRows[commentIndex].imagesUrl.push(res.data.tempPath);
                                    this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
                                } else {
                                    // this.previewImageList = [downRes.data.downloadUrl];
                                    let newObj = this.commentConfigListData;
                                    newObj.rows[commentIndex].picList.push(downRes.data.downloadUrl);
                                    newObj.rows[commentIndex].images.push(res3.data.objectKey);
                                    newObj.rows[commentIndex].imagesUrl.push(res3.data.tempPath);
                                    this.previewImageList = newObj.rows[commentIndex].picList;
                                    this.questionListDataRows = newObj.rows;
                                    this.$set(this.questionListDataRows, commentIndex, newObj.rows[commentIndex]);
                                    console.log('this.questionListDataRows----', this.questionListDataRows);
                                }
                            } else {
                                if (this.evaluateType == 'order') {
                                    this.questionListDataRows[commentIndex].picList.push(srcImg);
                                    this.questionListDataRows[commentIndex].images.push(res3.data.objectKey);
                                    this.questionListDataRows[commentIndex].imagesUrl.push(res3.data.tempPath);
                                    this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
                                } else {
                                    let newObj = this.commentConfigListData;
                                    newObj.rows[commentIndex].images.push(res3.data.objectKey);
                                    newObj.rows[commentIndex].imagesUrl.push(res3.data.tempPath);
                                    newObj.rows[commentIndex].picList.push(srcImg);
                                    this.questionListDataRows = newObj.rows;
                                    this.$set(this.questionListDataRows, commentIndex, newObj.rows[commentIndex]);
                                    console.log('this.questionListDataRows----', this.questionListDataRows);
                                }
                            }
                        });
                    }
                },
            });
            console.log('datat--55555555', this.questionListDataRows);
        },
        async getImageUpload(objectKey, srcImg) {
            /**
             * ​https://oss-gsmsb-dev.kunlunjyk.com  ： dev
               ​https://oss-gsmsb-qas.kunlunjyk.com   :   sit/qas
               ​https://oss-gsmsb-pre.kunlunjyk.com   ： pre
               https://oss-gsmsb.kunlunjyk.com   :  prd
             */
            let host;
            if (baseType == 'qas' || baseType == 'sit') {
                host = 'https://oss-gsmsb-qas.kunlunjyk.com';
            } else if (baseType == 'prd') {
                host = 'https://oss-gsmsb.kunlunjyk.com';
            } else if (baseType == 'dev') {
                host = 'https://oss-gsmsb-dev.kunlunjyk.com';
            }
            let key = objectKey;
            const imgTemp = await imageGetTempPostInfo({
                objectKey: objectKey,
                contentType: 'image/jpeg',
            });
            if (imgTemp.success) {
                let policy = imgTemp.data.policy;
                let signature = imgTemp.data.signature;
                let OSSAccessKeyId = imgTemp.data.accessKeyID;
                return new Promise((resolve, reject) => {
                    my.uploadFile({
                        // url: 'https://oss-dev-app-soti.kunlunjyk.com',
                        url: host,
                        fileType: 'image',
                        fileName: 'file',
                        filePath: srcImg,
                        formData: {
                            key,
                            policy,
                            OSSAccessKeyId: OSSAccessKeyId,
                            signature,
                            success_action_status: '200',
                        },
                        header: {
                            'Content-Type': 'image/jpeg',
                        },
                        success: res => {
                            console.log(res, 'res========success');
                            // 将上传成功状态码设置为200，默认状态码为204。
                            if (res.statusCode === 200) {
                                console.log('上传成功');
                                uni.showToast({
                                    title: '上传成功',
                                    icon: 'none',
                                    duration: 2000,
                                });
                                resolve();
                            } else {
                                uni.showToast({
                                    title: '上传失败',
                                    icon: 'none',
                                    duration: 2000,
                                });
                                reject();
                            }
                        },
                        fail: err => {
                            uni.showToast({
                                title: '上传失败',
                                icon: 'none',
                                duration: 2000,
                            });
                            console.log(err);
                            reject();
                        },
                    });
                });
            }
        },
        /**
         * 预览图片
         */
        async previewImage(imgIndex, commentIndex) {
            let urls;
            // console.log(this.questionListDataRows[commentIndex].images);
            if (!this.$sKit.test.isAndroidClient()) {
                // 判断ios系统
                // if (
                //     this.questionListDataRows[commentIndex].images.length > 0 &&
                //     this.questionListDataRows[commentIndex].images[imgIndex]
                // ) {
                //     let downRes = await imageDownload({ objectKey: this.questionListDataRows[commentIndex].images[imgIndex] });
                //     urls = [downRes.data.downloadUrl];
                // } else {
                //     return;
                // }
                urls = this.previewImageList;
            } else {
                urls = this.questionListDataRows[commentIndex].picList;
            }
            uni.previewImage({
                current: imgIndex,
                urls: urls,
            });
        },
    },
};
