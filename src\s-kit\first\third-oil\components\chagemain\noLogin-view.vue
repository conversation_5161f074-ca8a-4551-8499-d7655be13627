<template>
    <div class="charge-content-view fl-column bg-linear">
        <div class="top-area bg-fff" ref="toparea" v-if="locationState">
            <div class="stationinfo">
                <div class="fl-row fl-jus-bet">
                    <div class="detail f-1" v-if="selectMarkerV3 && selectMarkerV3.orgCode">
                        <div class="name-area fl-row fl-al-cen">
                            <!-- #ifndef MP-ALIPAY -->
                            <div
                                :class="{ 'scrolling-text': selectMarkerV3.orgName.length > 11 }"
                                class="name font-16 weight-bold color-000 f-1"
                            >
                                {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                            </div>
                            <!-- #endif -->
                            <!-- #ifdef MP-ALIPAY -->
                            <div class="name font-16 weight-bold color-000 f-1">
                                {{ selectMarkerV3.orgName || '暂未查询到网点' }}
                            </div>
                            <!-- #endif -->
                        </div>
                        <div class="fl-row fl-al-cen mart12">
                            <div
                                v-if="selectMarkerV3.stationStatus == 30 || selectMarkerV3.stationStatus == 50"
                                class="weight-400 font-12 color-6A6A6A time-icon"
                                >暂停营业
                            </div>
                            <div
                                v-if="selectMarkerV3.stationStatus == 20 || selectMarkerV3.stationStatus == 10"
                                class="weight-400 font-12 color-118920 marker-118920"
                                >正常营业
                            </div>
                        </div>
                        <div v-if="selectMarkerV3.address != 'null'" class="font-12 weight-400 color-333 oil-address"
                            >{{ selectMarkerV3.address || '' }}
                        </div>
                    </div>
                    <div class="detail f-1" v-else>
                        <div class="name-area fl-row fl-al-cen">
                            <div class="name font-16 weight-bold color-999">暂未查询到网点</div>
                        </div>
                        <div class="font-12 weight-bold color-999 oil-address">暂无地址</div>
                    </div>
                    <div class="fl-row">
                        <div class="changestation font-10 color-E64F22" @click.stop="toLoginPage">更多网点</div>
                        <!-- #ifndef H5-CLOUD -->
                        <img alt class="navt-to" src="../../image/stationinfo-icon-location.png" @click.stop="clickNaviStateion" />
                        <!-- #endif -->
                    </div>
                </div>
                <div class="center-area" v-if="selectMarkerV3.tagList && selectMarkerV3.tagList.length > 0">
                    <div class="fl-row fl-al-cen con-list">
                        <div class="item bg-F3F3F6 te-center" v-for="(tag, index) in selectMarkerV3.tagList" :key="index">
                            <div class="item-cell">{{ strReplace(tag) }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="center-area">
                <div class="stationCard" v-if="JSON.stringify(selectMarkerV3) == '{}'">
                    <img class="stationImg2" mode="widthFix" src="../../image/no-station.png" alt="" />
                    <div class="topText">附近暂未查询到网点</div>
                    <div class="botText">请您稍后查询</div>
                </div>
                <div class="stationCard" v-else-if="selectMarkerV3.storeOnlyFlag == 1">
                    <img class="stationImg" mode="widthFix" src="../../image/store.png" alt="" />
                    <div class="topText">此网点为便利店</div>
                    <div class="botText">暂不支持加油服务</div>
                </div>
                <div v-else-if="selectMarkerV3.stationStatus == 20 || selectMarkerV3.stationStatus == 10" class="stationCard">
                    <img class="stationImg" mode="widthFix" src="../../image/station.png" alt="" />
                    <div class="topText">此网点为加油站</div>
                    <div class="botText">登录即可使用加油服务</div>
                    <div class="tags">
                        <div class="tag" v-if="selectMarkerV3.bookingRefueling == 1 && topTab == 'reserve'">e享加油</div>
                        <div class="tag" v-else-if="topTab == 'charge'">不下车加油</div>
                        <div class="tag">不下车开票</div>
                        <div class="tag">不下车充值</div>
                    </div>
                </div>
                <div v-else-if="selectMarkerV3.stationStatus == 30 || selectMarkerV3.stationStatus == 50" class="stationCard">
                    <img class="stationImg" mode="widthFix" src="../../image/stationStop.png" alt="" />
                    <div class="topText">此网点为加油站</div>
                    <div class="botText">暂不支持加油服务</div>
                </div>
            </div>
            <div class="noLoginBtn3" :class="{ 'bg-opacity-288': !loginButtonGrayedOut }" @click="toLoginPage">我要加油</div>
        </div>
        <div v-if="!locationState" class="noLoginBox fl-column fl-al-cen">
            <div class="noLoginText"
                >还不知道您在哪里，开启定位权限后<br />为您推荐附近油站，享受{{ topTab == 'reserve' ? 'e享加油' : '不下车加油' }}服务</div
            >
            <div class="noLoginLocation">
                <div :class="{ 'bg-opacity-288': !loginButtonGrayedOut }" class="noLoginBtn noLoginBtn1" @click="toLoginPage">我要加油</div>
                <div class="noLoginBtn noLoginBtn2" @click="noLoginOpenLocation">开启定位</div>
            </div>
        </div>
        <custom-popup ref="popDialogFlag" type="center">
            <div class="_modal">
                <div class="iol-pop">
                    <div v-if="oilDialogFlagType == 'stationStatus'" class="fl-column fl-al-jus-cen font-16 color-333 weight-bold content">
                        <div>网点暂未营业，是否需要导航到此网点</div>
                    </div>
                </div>
                <view class="fl-row slot-btn-box">
                    <view v-if="cancelText" :style="{ color: cancelColor }" class="btn cancel_btn" @click.stop="clickBtn('cancel')">
                        {{ cancelText }}
                    </view>
                    <view
                        v-if="confirmText"
                        :style="{
                            color: confirmColor,
                            background: confirmBackgroundColor,
                        }"
                        class="btn confirm"
                        @click.stop="clickBtn('confirm')"
                        >{{ confirmText }}
                    </view>
                </view>
            </div>
        </custom-popup>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
export default {
    name: 'noLogin-view',
    props: {
        topTab: String,
    },
    computed: {
        ...mapState({
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
            locationState: state => state.locationV3_app.locationState,
            // 登录按钮标识
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
        }),
    },
    data() {
        return {
            oilDialogFlagType: '',
            confirmText: '',
            confirmColor: '',
            confirmBackgroundColor: '',
            cancelText: '',
        };
    },
    created() {
        this.toLoginPage = this.$sKit.commonUtil.throttleUtil(this.toLoginPage);
    },
    methods: {
        /**
         * @description : 打开导航
         * @return        {*}
         */
        clickBtn(data) {
            this.$refs.popDialogFlag.close();
            if (data == 'confirm') {
                if (this.confirmText == '导航到站') {
                    // #ifdef MP-MPAAS
                    this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                        if (res) {
                            this.$cnpcBridge.openLocation({
                                latitude: this.selectMarkerV3.latitude,
                                longitude: this.selectMarkerV3.longitude,
                                name: this.selectMarkerV3.orgName,
                                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                            });
                        }
                    });
                    // #endif
                    // #ifndef MP-MPAAS
                    uni.openLocation({
                        latitude: Number(this.selectMarkerV3.latitude),
                        longitude: Number(this.selectMarkerV3.longitude),
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                    // #endif
                    // this.clickNaviStateion()
                }
            } else {
                if (this.oilDialogFlagType == 'stationStatus') {
                    this.$refs.popDialogFlag.close();
                }
            }
        },
        toLoginPage() {
            if (!this.loginButtonGrayedOut) return;
            let url = '';
            // #ifdef MP-WEIXIN
            url = '/packages/transferAccount/pages/home/<USER>';
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/union/main?qKey=oil';
            // #endif
            // #ifdef H5-CLOUD
            url = '/packages/third-cloud-login/pages/home/<USER>';
            // #endif
            this.$sKit.layer.useRouter(url);
        },
        async noLoginOpenLocation() {
            // #ifdef MP-WEIXIN
            // 这里调用init传privacyAgreement是用于在用户拒绝授权隐私保护授权时,点击未登录页面的开启定位,需要先让用户同意授权隐私协议,在跳转到设置页面开启位置授权
            let res = await this.$store.dispatch('init', { type: 'privacyAgreement' });
            if (res.result !== 'success') return;
            this.$store.dispatch('openLocationMiniApp');
            // #endif
            // #ifdef MP-ALIPAY
            this.$store.dispatch('openLocationMiniApp');
            // #endif
            // #ifdef H5-CLOUD
            this.$store.dispatch('initLocationV3_app', {
                type: 'initMap',
            });
            // #endif
        },
        /**
         * @description : 不显示"站"字
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        clickNaviStateion() {
            if (this.selectMarkerV3) {
                if (this.selectMarkerV3.stationStatus != 20 && this.selectMarkerV3.stationStatus != 10) {
                    this.oilDialogFlagType = 'stationStatus';
                    this.$refs.popDialogFlag.open();
                    this.confirmText = '导航到站';
                    this.cancelText = '稍后使用';
                    this.confirmColor = '#000';
                } else {
                    // #ifdef MP-MPAAS
                    this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                        if (res) {
                            this.$cnpcBridge.openLocation({
                                latitude: this.selectMarkerV3.latitude,
                                longitude: this.selectMarkerV3.longitude,
                                name: this.selectMarkerV3.orgName,
                                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                            });
                        }
                    });
                    // #endif
                    // #ifndef MP-MPAAS
                    uni.openLocation({
                        latitude: Number(this.selectMarkerV3.latitude),
                        longitude: Number(this.selectMarkerV3.longitude),
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                    // #endif
                }
            }
        },
    },
    watch: {
        // 监听selectMarkerV3.orgCode变化 实现页面变化
        '$store.state.locationV3_app.selectMarkerV3.orgCode': {
            handler: async function (newValue, oldValue) {
                if (newValue) {
                }
            },
            immediate: true,
            deep: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.page-view {
    width: 100%;
    border-radius: 10px 10px 0px 0px;
    flex: 1;
    overflow-y: scroll;
}

.zjMarketBox {
    transform: translateY(5px);
}

.charge-content-view {
    border-radius: 10px 10px 0px 0px;
    /* #ifdef MP-MPAAS */
    padding-bottom: 40rpx;
    /* #endif */
    box-sizing: border-box;
    /* #ifndef MP-MPAAS */
    // padding-bottom: 32rpx;
    /* #endif */
    width: 100%;
    // margin-top: -5px;
    position: relative;

    .top-area {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: 0 56rpx;
        // align-items: center;

        .stationinfo {
            padding: 12px 0;

            .detail {
                overflow: hidden;

                // #ifndef MP-ALIPAY
                .name-area {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 180px;

                    .name {
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // white-space: nowrap;
                    }
                }

                // #endif
                // #ifdef MP-ALIPAY
                .name-area {
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 180px;
                    }
                }

                // #endif
                .pad-t-6 {
                    padding-top: 8px;
                }

                .oil-address {
                    padding-top: 12px;
                }

                .time-icon {
                    width: 52px;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                    background: #efeded;
                    border-radius: 4px;
                    margin: 0 4px;
                }
            }

            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                margin-left: 20px;
                display: block;
            }

            .center-area {
                padding-top: 12px;

                .con-list {
                    justify-content: flex-start;
                    width: 100%;
                    flex-wrap: wrap;

                    .item {
                        padding: 8rpx 16rpx;
                        background: #f3f3f6;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        color: #333333;
                        text-align: center;
                        margin: 0 18rpx 8rpx 0;
                    }

                    // .item:nth-of-type(4n + 0) {
                    //     margin-right: 0px;
                    // }
                }
            }
        }

        .changestation {
            margin-left: 12px;
            width: 56px;
            height: 17px;
            line-height: 34rpx;
            align-items: center;
            justify-content: center;
            display: flex;
            padding: 2px 0;
            text-align: center;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            border-radius: 4px;
            border: 1px solid #e64f22;
            box-sizing: border-box;
        }

        .margin-8-l-r {
            margin: 0 8px;
        }

        .center-area {
            padding: 33rpx 67rpx;

            .width107 {
                width: 107px;
            }

            .width75 {
                width: 75px;
            }

            .height35 {
                height: 35px;
                line-height: 35px;
            }

            .mar-r-8 {
                margin-right: 8px;
            }

            .pdtl4 {
                padding-top: 4px;
                padding-left: 4px;
                box-sizing: border-box;
            }

            .stationCard {
                display: flex;
                flex-direction: column;
                align-items: center;

                .stationImg {
                    width: 100%;
                }

                .stationImg2 {
                    width: 201rpx;
                    margin-bottom: 19rpx;
                }

                .topText {
                    color: #333333;
                    font-size: 32rpx;
                    line-height: 40rpx;
                    text-align: center;
                    font-weight: bold;
                }

                .botText {
                    color: #666666;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    text-align: center;
                }

                .tags {
                    margin-top: 19rpx;
                    display: flex;
                    width: 100%;

                    .tag {
                        width: 155rpx;
                        height: 52rpx;
                        background: #f3f3f6;
                        border-radius: 8rpx;
                        font-size: 26rpx;
                        color: #333333;
                        line-height: 52rpx;
                        text-align: center;
                        margin-right: 20rpx;

                        &:nth-last-of-type(1) {
                            margin-right: 0;
                        }
                    }
                }
            }
        }

        .card-nav {
            padding: 0 12px 19px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .marl5 {
                margin-left: 5px;
            }
        }

        .card-nav-no {
            padding: 0 12px 19px;
            box-sizing: border-box;

            .marl5 {
                margin-left: 5px;
            }
        }

        .noLoginBtn3 {
            width: 100%;
            height: 88rpx;
            border-radius: 16rpx;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 88rpx;
            text-align: center;
            background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
            color: #ffffff;
            margin-top: 18px;
            margin-bottom: 15px;
        }
    }

    .mart12 {
        margin-top: 12px !important;
    }

    .marl10 {
        margin-left: 10px;
    }

    .equity-div {
        margin: 0px 15px;
        height: 33px;
        border-radius: 0px 0px 8px 8px;
        line-height: 33px;
        box-sizing: border-box;
    }

    .btn-44 {
        margin: 0 16px;
        height: 44px;
        margin-top: 16px;
        border-radius: 8px;
        line-height: 44px;
    }

    .market-div {
        margin: 12px 16px 0;
    }

    .advertisingPopups {
        position: fixed;
        bottom: 0;
        top: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        z-index: 998;
        background-color: rgba(0, 0, 0, 0.6);
    }

    .noLoginBox {
        padding: 29px 31px 27px;

        .noLoginText {
            text-align: center;
        }

        .noLoginLocation {
            margin-top: 15px;
            display: flex;
            justify-content: space-around;
            width: 100%;

            .noLoginBtn {
                width: 300rpx;
                height: 88rpx;
                border-radius: 16rpx;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 88rpx;
                text-align: center;
            }

            .noLoginBtn1 {
                background: #ffffff;
                color: #e64f22;
                border: 1rpx solid #e64f22;
            }

            .noLoginBtn2 {
                background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
                color: #ffffff;
            }
        }
    }
}

.no-reserve {
    width: 71px !important;
    height: 59px;
    display: block;
}

.marl12 {
    margin-left: 12px;
}

.mart12 {
    margin-top: 12px !important;
}

.mat5 {
    margin-top: 5px;
}

.market-div {
    margin: 12px 16px 0;
}

.height12 {
    width: 100%;
    height: 12px;
    background-color: #fbfbfd;
}

.marb6 {
    margin-bottom: 6px;
}

.marlr15 {
    margin: 0px 15px;
}

._modal {
    flex: none;
    width: 560rpx;
    min-height: 207rpx;
    background: #ffffff;
    overflow: hidden;
    border-radius: 10px;
}

.iol-pop {
    padding: 38rpx 0 0;

    .content {
        padding: 0 20rpx;
        margin-bottom: 34rpx;
        width: 100%;
        min-height: 68rpx;
    }

    .price-input-area {
        border-radius: 10px;
        height: 44px;

        .price-input {
            width: 100%;
            height: 100% !important;
            background: #f7f7fb;
            border-radius: 8px;
            text-align: center;
            box-sizing: border-box;
        }
    }
}

.slot-btn-box {
    width: 100%;
    border-top: 1px solid #efeff4;

    .btn {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 90rpx;

        &:active {
            background-color: #f7f7f7;
        }
    }

    .cancel_btn {
        border-right: 2rpx solid #efeff4;
    }

    .confirm {
        font-weight: bold;
    }
}

.mar-6-l {
    margin-left: 6px;
}

.mart5 {
    margin-top: 5px;
}

.marl12 {
    margin-left: 12px;
}

.pad-t-10 {
    padding-top: 10px;
}

.padding12 {
    padding: 12px;
    box-sizing: border-box;
}

.marl13 {
    margin-left: 13px;
}

.mart16 {
    margin-top: 16px;
}

.marlr16 {
    margin: 0 16px;
}

.padt16 {
    padding-top: 16px;
}

.padb12 {
    padding-bottom: 12px;
}

.marb12 {
    margin-bottom: 12px;
}

.marb4 {
    margin-bottom: 4px;
}

.pay-btn {
    // padding: 0 16px;
    background-color: #f7f7fb;

    .pay-submit {
        padding: 12px 0 16px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;

        .btn-44 {
            height: 44px;
            border-radius: 8px;
            line-height: 44px;
        }
    }
}

.model-div {
    background: #f7f7fb;
    border-radius: 10px 10px 0px 0px;

    .marlr16 {
        margin: 0 16px;
    }

    .marb16 {
        margin-bottom: 16px;
    }

    .pad12-7 {
        padding: 12px 7px 0;
    }

    .padlt127 {
        padding: 12px 7px;
    }

    .marb12 {
        margin-bottom: 12px;
    }

    .padtb16 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .close {
        padding: 10px;

        img {
            width: 13px;
            height: 13px;
        }
    }

    .con-list {
        justify-content: flex-start;
        width: 100%;
        flex-wrap: wrap;

        .item {
            width: 78px;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            font-size: 14px;
            color: #333333;
            margin-bottom: 12px;
            margin-left: 6px;
            box-sizing: border-box;
        }

        .item:nth-of-type(4n + 0) {
            margin-right: 0px;
        }

        .oil-type-sel {
            background: rgba(230, 79, 34, 0.16);
            border-radius: 4px;
            border: 1px solid #e64f22;
            color: #e64f22;
        }
    }
}

.hieght100 {
    height: 120px;
}
</style>
