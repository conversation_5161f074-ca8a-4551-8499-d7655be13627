let events = require('events');
let fs = require('fs');
const addComponentStr = (pageStr, componentName) => {
    let labelqIndex = componentName.lastIndexOf('</');
    let labelhIndex = componentName.lastIndexOf('>');
    let componentTitle = componentName.slice(labelqIndex, labelhIndex + 1);
    // console.log(componentTitle)
    if (!componentTitle) throw '未检测到组件名称';
    if (pageStr.indexOf(componentTitle) == -1) {
        // 将组件插入vue文件中
        let index = pageStr.lastIndexOf('</template>');
        let newStr = pageStr.slice(0, index);
        let newIndex = newStr.lastIndexOf('</');
        let newStr1 = `${newStr.slice(0, newIndex)}\t${componentName}\n\t${newStr.slice(newIndex)}`;
        let newStr2 = newStr1 + pageStr.slice(index);
        return newStr2;
    } else {
        return pageStr;
    }
};

const addFirstComponentStr = (pageStr, componentName) => {
    let labelqIndex = componentName.lastIndexOf('</');
    let labelhIndex = componentName.lastIndexOf('>');
    let componentTitle = componentName.slice(labelqIndex, labelhIndex + 1);
    // console.log(componentTitle)
    if (!componentTitle) throw '未检测到组件名称';
    if (pageStr.indexOf(componentTitle) == -1) {
        // 将组件插入vue文件中
        let firstIndex = pageStr.indexOf('>');
        if (firstIndex === -1) {
            // 如果没有找到第一个 >，直接返回
            return pageStr;
        }

        // let index = pageStr.lastIndexOf('</template>');
        let newStr = pageStr.slice(0, firstIndex);
        let newIndex = firstIndex + componentTitle.length;
        let newStr1 = `${newStr}\n\t\t${componentName}\t${newStr.slice(newIndex)}`;
        let newStr2 = newStr1 + pageStr.slice(firstIndex);
        return newStr2;
    } else {
        return pageStr;
    }
};

const addComponent = new events.EventEmitter();
addComponent.on('add', (pageStr, componentName) => {
    let root = './src/';
    let pageObj = JSON.parse(pageStr);
    // console.log('pageObj--',pageObj.subPackages)
    let pages = pageObj.pages;
    let subPackages = pageObj.subPackages;
    // 主包页面修改
    for (let i = 0; i < pages.length; i++) {
        let path = `${root}${pages[i].path}.vue`;
        let vueStr = fs.readFileSync(path, 'utf-8');
        vueStr = addComponentStr(vueStr, componentName);
        fs.writeFileSync(path, vueStr);
        // console.log(vueStr)
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // 分包页面修改
    for (let i = 0; i < subPackages.length; i++) {
        let subItem = subPackages[i];
        for (let j = 0; j < subItem.pages.length; j++) {
            let subPages = subItem.pages[j];
            let path = `${root}${subItem.root}/${subPages.path}.vue`;
            let vueStr = fs.readFileSync(path, 'utf-8');
            vueStr = addComponentStr(vueStr, componentName);
            fs.writeFileSync(path, vueStr);
            // console.log(vueStr)
        }
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // console.log(pageObj)
});

addComponent.on('addFirst', (pageStr, componentName) => {
    let root = './src/';
    let pageObj = JSON.parse(pageStr);
    // console.log('pageObj--',pageObj.subPackages)
    let pages = pageObj.pages;
    let subPackages = pageObj.subPackages;
    // 主包页面修改
    for (let i = 0; i < pages.length; i++) {
        let path = `${root}${pages[i].path}.vue`;
        let vueStr = fs.readFileSync(path, 'utf-8');
        vueStr = addFirstComponentStr(vueStr, componentName);
        fs.writeFileSync(path, vueStr);
        // console.log(vueStr)
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // 分包页面修改
    for (let i = 0; i < subPackages.length; i++) {
        let subItem = subPackages[i];
        for (let j = 0; j < subItem.pages.length; j++) {
            let subPages = subItem.pages[j];
            let path = `${root}${subItem.root}/${subPages.path}.vue`;
            let vueStr = fs.readFileSync(path, 'utf-8');
            vueStr = addFirstComponentStr(vueStr, componentName);
            fs.writeFileSync(path, vueStr);
            // console.log(vueStr)
        }
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
    }
    // console.log(pageObj)
});
module.exports = addComponent;
