<!--created by lq 2023/1/19-->
<template>
    <div v-if="isShowOrderInfo">
        <div class="oil-charge-bill-info" v-if="o2oGoodList.length > 0">
            <div class="cell">
                <div class="left">订单编号</div>
                <div class="right">{{ resultData.orderNo }}<div class="copy" @click="copyProductNo(resultData.orderNo)">复制</div></div>
            </div>
            <div v-if="isShowMore">
                <div class="cell">
                    <div class="left">枪号</div>
                    <div class="right">{{ resultData.gunNo }}号枪</div>
                </div>
                <div class="cell">
                    <div class="left">油品名称</div>
                    <div class="right">{{ resultData.productName }}</div>
                </div>
                <div class="cell">
                    <div class="left">单价</div>
                    <div class="right">{{ resultData.unitPrice }}元/升</div>
                </div>
                <div class="cell">
                    <div class="left">升数</div>
                    <div class="right">{{ resultData.productQty }}升</div>
                </div>
                <div class="cell">
                    <div class="left">订单金额</div>
                    <div class="right">{{ resultData.orderTotalAmount }}元</div>
                </div>
                <div class="cell">
                    <div class="left">支付时间</div>
                    <div class="right">{{ resultData.payConfirmationTime }}</div>
                </div>
                <div class="cell">
                    <div class="left">优惠金额</div>
                    <div class="right">{{ resultData.discountTotalAmount }}元</div>
                </div>
                <div class="cell" v-for="(item, index) in resultData.discountList" :key="index">
                    <div class="left gray" style="margin-left: 20px">{{ item.payMethodName }}</div>
                    <div class="right gray">{{ item.payAmount }}元</div>
                </div>
                <div class="cell">
                    <div class="left">实付金额</div>
                    <div class="right">{{ resultData.actualPayTotalAmount }}元</div>
                </div>
                <div class="cell" v-for="(item, index) in resultData.payDetailList" :key="index">
                    <div class="left gray" style="margin-left: 20px">{{ item.payMethodName }}</div>
                    <div class="right gray">{{ item.payAmount }}元</div>
                </div>
            </div>
            <div class="more-btn" v-if="isShowMore" @click="changeIsShowMore(!isShowMore)">
                <div class="btnText">收起</div>
                <img src="../../../image/up.png" alt="" />
            </div>
            <div class="more-btn" v-else @click="changeIsShowMore(!isShowMore)">
                <div class="btnText">全部订单信息</div>
                <img src="../../../image/down.png" alt="" />
            </div>
        </div>
        <div class="oil-charge-bill-info" v-else>
            <div class="cell">
                <div class="left">订单编号</div>
                <div class="right">{{ resultData.orderNo }}<div class="copy" @click="copyProductNo(resultData.orderNo)">复制</div></div>
            </div>
            <div class="cell">
                <div class="left">枪号</div>
                <div class="right">{{ resultData.gunNo }}号枪</div>
            </div>
            <div class="cell">
                <div class="left">油品名称</div>
                <div class="right">{{ resultData.productName }}</div>
            </div>
            <div class="cell">
                <div class="left">单价</div>
                <div class="right">{{ resultData.unitPrice }}元/升</div>
            </div>
            <div class="cell">
                <div class="left">升数</div>
                <div class="right">{{ resultData.productQty }}升</div>
            </div>
            <div class="cell">
                <div class="left">订单金额</div>
                <div class="right">{{ resultData.orderTotalAmount }}元</div>
            </div>
            <div class="cell">
                <div class="left">支付时间</div>
                <div class="right">{{ resultData.payConfirmationTime }}</div>
            </div>
            <div class="cell">
                <div class="left">优惠金额</div>
                <div class="right">{{ resultData.discountTotalAmount }}元</div>
            </div>
            <div class="cell" v-for="(item, index) in resultData.discountList" :key="index">
                <div class="left gray" style="margin-left: 20px">{{ item.payMethodName }}</div>
                <div class="right gray">{{ item.payAmount }}元</div>
            </div>
            <div class="cell">
                <div class="left">实付金额</div>
                <div class="right">{{ resultData.actualPayTotalAmount }}元</div>
            </div>
            <div class="cell" v-for="(item, index) in resultData.payDetailList" :key="index">
                <div class="left gray" style="margin-left: 20px">{{ item.payMethodName }}</div>
                <div class="right gray">{{ item.payAmount }}元</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'oil-charge-bill-info',
    components: {},
    props: {
        resultData: Object,
        o2oGoodList: Array,
    },
    computed: {},
    data() {
        return {
            isShowOrderInfo: false,
            isShowMore: false,
        };
    },
    created() {},
    mounted() {},
    watch: {
        resultData: {
            handler: function (newVal, oldVal) {
                console.log(newVal, oldVal, '订单信息组件');
                if (Object.keys(newVal).length > 0) {
                    this.isShowOrderInfo = true;
                } else {
                    this.isShowOrderInfo = false;
                }
            },
            deep: true,
            immediate: true,
        },
        o2oGoodList: {
            handler: function (newVal, oldVal) {
                if (newVal.length > 0) {
                    this.isShowMore = false;
                } else {
                    this.isShowMore = true;
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 复制
        copyProductNo(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        changeIsShowMore(boolean) {
            this.isShowMore = boolean;
        },
    },
};
</script>

<style lang="scss" scoped>
.oil-charge-bill-info {
    border-radius: 8px;
    background-color: white;
    padding: 10px 15px 12px 15px;
    margin-bottom: 24rpx;

    .cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;

        div {
            line-height: 16px;
            font-size: 14px;
        }

        .left {
            color: #333333;
            flex-shrink: 0;
        }

        .right {
            display: flex;
            color: #999999;
            align-items: center;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;

            .copy {
                width: 29px;
                height: 16px;
                background: #ffffff;
                border-radius: 2px;
                border: 1px solid #999999;
                text-align: center;
                line-height: 16px;
                color: #666;
                font-size: 10px;
                margin-left: 5px;
            }
        }

        .gray {
            color: #666;
        }
    }
    .more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        .btnText {
            font-size: 24rpx;
            color: #666666;
            line-height: 33rpx;
            margin-right: 9rpx;
        }
        img {
            width: 11rpx;
            height: 7rpx;
        }
    }
    .more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        .btnText {
            font-size: 24rpx;
            color: #666666;
            line-height: 33rpx;
            margin-right: 9rpx;
        }
        img {
            width: 11rpx;
            height: 7rpx;
        }
    }
}
</style>
