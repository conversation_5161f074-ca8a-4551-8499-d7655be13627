<template>
    <div class="page-view">
        <div class="charge-content-view fl-column bg-fff">
            <div class="top-area bg-fff">
                <div class="stationinfo fl-row fl-jus-bet">
                    <div class="detail f-1">
                        <div class="name-area fl-row fl-al-cen">
                            <!-- #ifndef MP-ALIPAY -->
                            <p
                                :class="{ 'scrolling-text': selectMarkerV3.orgName.length > 15 }"
                                class="name font-16 weight-600 color-000"
                                >{{ selectMarkerV3.orgName }}</p
                            >
                            <!-- #endif -->
                            <!-- #ifdef MP-ALIPAY -->
                            <div class="name font-16 weight-bold color-000 f-1">
                                {{ selectMarkerV3.orgName }}
                            </div>
                            <!-- #endif -->
                        </div>
                        <div class="pad-t-6 start-time">
                            <div class="time font-12 weight-400 color-333" v-if="selectMarkerV3.businessHoursList.length > 0">{{
                                selectMarkerV3.businessHoursStr
                            }}</div>
                            <div
                                class="icon"
                                :class="
                                    selectMarkerV3.stationStatus == '20' || selectMarkerV3.stationStatus == '10' ? 'bg-118920' : 'bg-E02020'
                                "
                                >{{ getStatus(selectMarkerV3) }}</div
                            >
                        </div>
                        <div class="font-12 weight-400 color-333 pad-t-6">{{ selectMarkerV3.address || '' }}</div>
                        <!-- 滚动可用前暂时这样处理 -->
                        <div class="font-12 marketTemp color-E64F22 fl-row fl-al-cen pad-t-6">
                            <div v-for="(item, index) in fuelData" :key="index" class="paddrr6">{{ item.fuelName }}</div>
                        </div>
                    </div>
                    <div @click.stop="clickNaviStateion">
                        <img alt class="navt-to" src="../../image/stationinfo-icon-location.png" />
                    </div>
                </div>
            </div>
            <div class="line_bottom_dashed margin-8-l-r"></div>
            <div class="center-area palr16">
                <div class="fl-row fl-al-cen con-list">
                    <div class="item bg-F3F3F6 te-center" v-for="(tag, index) in selectMarkerV3.tagList" :key="index">
                        <div class="item-cell">{{ strReplace(tag) }}</div>
                    </div>
                </div>
            </div>
            <div class="phone-area margin-8-l-r" v-if="selectMarkerV3.phone">
                <div class="fl-row input-area fl-al-cen fl-jus-bet" @click="boda">
                    <div class="font-15 color-000 f-1">联系电话 {{ selectMarkerV3.phone || '' }}</div>
                    <div class="fl-row">
                        <div class="font-14 color-E64F22 bor-seach">立即拨打</div>
                    </div>
                </div>
            </div>
            <!-- <div class="active-area margin-8-l-r">
                <div class="font-13 color-000 weight-500 padb11">今日促销活动</div>
                <div class="fl-row">
                    <div class="logo">
                        <img src="../../image/logo-bg.png" alt="" />
                    </div>
                    <div class="font-14 color-666 weight-400 marl-17 cx-text"
                        >今日促销：{{ selectMarkerV3.activityList[0].activityName || '' }}</div
                    >
                </div>
            </div> -->
            <div class="bottom-area fl-row fl-jus-bet palr16" v-if="!hideRefuel">
                <div class="color-fff font-16 weight-500 primary-btn te-center border-rad-8" @click="goToOil">去加油</div>
                <div class="color-E64F22 font-16 weight-500 btn-plain te-center border-rad-8" @click="changeStationAction">更多网点</div>
            </div>
            <div v-if="hideRefuel" @click="changeStationAction()" class="addCard fl-row fl-al-jus-cen border-rad-8">
                <div class="color-E64F22 font-16 weight-500 btn-plain te-center border-rad-8">更多网点</div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    data() {
        return {};
    },
    props: {
        hideRefuel: Boolean,
    },
    async created() {
        if (this.selectMarkerV3 && this.selectMarkerV3.orgCode) {
            await this.$store.dispatch('getFuelGunByOrgCodePost');
        }
    },
    mounted() {
        this.getIconBottom();
    },
    computed: {
        ...mapState({
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
        }),
    },
    methods: {
        getStatus(item) {
            let timeText;
            if (item.stationStatus == 20 || item.stationStatus == 10) {
                timeText = '正常营业';
            } else {
                timeText = '暂停营业';
            }
            return timeText;
        },
        // 缺注释
        getIconBottom() {
            this.$nextTick(() => {
                setTimeout(() => {
                    const query = uni.createSelectorQuery().in(this);
                    query
                        .select('.page-view')
                        .boundingClientRect(data => {
                            this.$emit('getIconBottom', data.height + 30);
                        })
                        .exec();
                }, 300);
            });
        },
        // 点击导航
        clickNaviStateion() {
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$cnpcBridge.openLocation({
                        latitude: this.selectMarkerV3.latitude,
                        longitude: this.selectMarkerV3.longitude,
                        name: this.selectMarkerV3.orgName,
                        address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
                    });
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            uni.openLocation({
                latitude: Number(this.selectMarkerV3.latitude),
                longitude: Number(this.selectMarkerV3.longitude),
                name: this.selectMarkerV3.orgName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.orgName,
            });
            // #endif
        },
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        boda() {
            uni.makePhoneCall({
                phoneNumber: this.selectMarkerV3.phone,
            });
        },
        // 去加油
        goToOil() {
            let URL = '/packages/third-scan-code-payment/pages/home-code/main';
            let params = {
                noInitLocation: true,
                tabType: 'charge',
                refer: 'r29',
            };
            let type = 'reLaunch';
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$sKit.layer.useRouter(URL, params, type);
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, type);
            // #endif
        },
        //更换油站
        changeStationAction() {
            let URL = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {};
            let type = 'navigateTo';
            // #ifdef MP-MPAAS
            this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                if (res) {
                    this.$sKit.layer.useRouter(URL, params, type);
                }
            });
            // #endif
            // #ifndef MP-MPAAS
            this.$sKit.layer.useRouter(URL, params, type);
            // #endif
        },
    },

    components: {},
    watch: {
        // 监听selectMarkerV3.orgCode变化 实现页面变化
        '$store.state.locationV3_app.selectMarkerV3.orgCode': {
            handler: function (newValue, oldValue) {
                if (newValue) {
                    // if (this.bookingRefueling == "") {
                    this.$store.dispatch('getFuelGunByOrgCodePost');
                    this.getIconBottom();
                    // }
                }
            },
            immediate: true,
            deep: true,
        },
    },
};
</script>
<style lang="stylus" scoped>
@import '~assets/stylus/index.styl'
</style>
<style scoped lang="scss">
@import '../../../../s-kit/css/index.scss';
.page-view {
    height: 100%;
    width: 100%;
}
.palr16 {
    padding: 0 16px;
}
.margin-8-l-r {
    margin: 0 16px;
}
.marl-17 {
    margin-left: 17px;
}
.padb11 {
    padding-bottom: 11px;
    font-weight: 700;
}

.charge-content-view {
    height: 100%;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 16px;
    width: 100vw;
    .top-area {
        padding: 12px 12px 0 12px;
        overflow: hidden;
        margin: 0px 15px;

        .stationinfo {
            padding-bottom: 12px;
            .detail {
                overflow: hidden;
                // #ifndef MP-ALIPAY
                .name-area {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 265px;

                    .name {
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // white-space: nowrap;
                    }
                }
                // #endif
                // #ifdef MP-ALIPAY
                .name-area {
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 265px;
                    }
                }
                // #endif

                .start-time {
                    display: flex;
                    align-items: center;
                    .time {
                        margin-right: 6rpx;
                    }
                    .icon {
                        width: 100rpx;
                        height: 42rpx;
                        font-size: 20rpx;
                        font-weight: bold;
                        color: #118920;
                        line-height: 42rpx;
                        border-radius: 8rpx;
                        text-align: center;
                    }
                }
                .marketTemp {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .pad-t-6 {
                    padding-top: 6px;
                }
            }

            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                margin-left: 20px;
                display: block;
            }
        }
        .margin-8-l-r {
            margin: 0 8px;
        }
    }

    .center-area {
        padding-top: 12px;
        .con-list {
            justify-content: flex-start;
            width: 100%;
            flex-wrap: wrap;
            .item {
                /*padding: 8rpx 16rpx;*/
                /*width: 78px;*/
                height: 40px;
                line-height: 40px;
                border-radius: 4px;
                font-size: 14px;
                color: #333333;
                padding-right: 16rpx;
                padding-left: 16rpx;
                margin-bottom: 12px;
                margin-left: 6px;
                box-sizing: border-box;
            }
            .item:nth-of-type(4n + 0) {
                margin-right: 0px;
            }
            .oil-type-sel {
                background: rgba(230, 79, 34, 0.16);
                border-radius: 4px;
                border: 1px solid #e64f22;
                color: #e64f22;
            }
        }
    }
    .phone-area {
        padding: 12px 0 16px 0;
        .input-area {
            height: 40px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #dddddd;
            padding: 0px 12px;
        }
        .bor-seach {
            // width: 40px;
            height: 16px;
            border-left: 1px solid #999;
            padding-left: 9px;
            line-height: 16px;
        }
    }
    .active-area {
        padding-bottom: 20px;
        .logo {
            img {
                width: 46px;
                height: 46px;
            }
        }

        .cx-text {
            line-height: 23px;
        }
    }
    .bottom-area {
        // padding-top: 40px;
        div {
            height: 44px;
            width: 166px;
            line-height: 44px;
        }
    }
    .addCard {
        height: 44px;
        line-height: 44px;
        margin-bottom: 12px;
        width: 100%;
        div {
            width: 343px;
            margin: 0 auto;
        }
    }
    .paddrr6 {
        padding-right: 6px;
    }
}
</style>
