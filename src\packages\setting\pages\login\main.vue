<template>
    <div class="signin">
        <zj-navbar
            :border-bottom="false"
            :background="pageConfig.bgColor"
            :back-icon-size="44"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            :homeIcon="homeIcon"
            :customBack="goBack"
        ></zj-navbar>
        <!-- <div :class="screenWidth > 600 ? 'setting_pwd header' : 'header'"> -->
        <div class="header">
            <div class="app-name-div">
                <span>欢迎来到</span>
                <span>能源e站</span>
            </div>
            <div class="logo">
                <img class="login-image" src="@/static/cnpc-logo.png" />
            </div>
        </div>
        <div class="login-content-class box-shadow-base">
            <div class="username_style line_bottom">
                <input type="number" placeholder="手机号码" v-model="phone" maxlength="11" />
            </div>
            <div class="username_style line_bottom">
                <input type="text" v-model="imgVerifyCode" placeholder="请输入右侧图片校验码" />
                <img class="verify_img" :src="imgcode" @click="getVerifyImg" />
            </div>
            <div class="username_style line_bottom">
                <input type="number" maxlength="4" v-model="smsVerifyCode" :disabled="smsInputState" placeholder="短信验证码" />
                <div class="right-code" @click="getVerificationCode" v-show="numShow">获取验证码</div>
                <div class="right-code" v-show="!numShow">{{ count }}s</div>
            </div>
            <div class="privacy_agreement_class">
                <checkbox-group @change="agreement" class="popup-content-agree">
                    <checkbox value="agreement" :checked="agreeCheckBox" class="popup-content-agree-checkBox" />
                    <div class="popup-content-agree-text">
                        我已阅读并同意能源e站
                        <span @click="clickXieyi(17)">《用户协议》</span>和
                        <span @click="clickXieyi(63)">《隐私政策》</span>
                    </div>
                </checkbox-group>
            </div>
            <div class="login_style login_style2" @click="loginClick">登录/注册</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getVerifyImgApi, memberSendVerifyCode } from '@/api/home.js';
import pageConfig from '@/utils/pageConfig.js';
export default {
    data() {
        return {
            agreeCheckBox: false,
            phone: '',
            imgVerifyCode: '',
            smsVerifyCode: '',
            imgcode: '',
            count: 60,
            numShow: true, // 验证码
            timer: null,
            uuid: this.$util.generateUUID(), //随机字串
            imgVerifyCodeSuc: false, //校验验证码
            overtime: false,
            pageConfig,
            homeIcon: require('@/static/oilcharge/back_home.png'),
            loginBackParams: {},
        };
    },
    computed: {
        ...mapState({
            province: state => state.location.province, // 省
        }),
        ...mapGetters(['isLogin']),
    },
    props: {
        btnType: {
            default: 'normal',
            type: String,
        },
        params: {
            default: {},
            type: Object,
        },
    },
    onLoad(options) {
        this.loginBackParams = JSON.parse(options.params);
        console.log(this.loginBackParams, '1234567');
        this.loginClick = this.$util.throttleUtil(this.loginClick);
        this.getVerificationCode = this.$util.throttleUtil(this.getVerificationCode);
        if (this.timer) {
            clearInterval(this.timer);
        }
        this.getVerifyImg();
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
    mounted() {},
    methods: {
        goBack() {
            uni.reLaunch({
                url: '/pages/home/<USER>',
            });
        },
        // 获取验证码
        async getVerificationCode() {
            if (this.phone == '' || !this.$test.checkMobilePhoneNumber(this.phone)) {
                uni.showToast({
                    title: '请输入正确的手机号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (this.imgVerifyCode === '') {
                uni.showToast({
                    title: '请输入图片验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return false;
            }
            let params = {
                uuid: this.uuid, //随机字串
                phone: this.phone,
                // channelCode: "1005",
                securityCode: this.imgVerifyCode,
            };
            wx.showLoading({
                mask: true,
            });
            let res = await memberSendVerifyCode(params);
            if (res.status === 0) {
                wx.showLoading({
                    mask: true,
                });
                uni.showToast({
                    title: '验证码发送成功',
                    icon: 'none',
                    duration: 2000,
                });
                this.countdown();
                this.overtime = false;
            } else {
                wx.hideLoading();
                uni.showToast({
                    title: res.info,
                    icon: 'none',
                    duration: 2000,
                });
                this.getVerifyImg();
                // this.$util.showModal(res.info, true, '#333333', '', '确定', '')
            }
        },
        // 验证码倒计时
        countdown() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.numShow = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.numShow = true;
                        clearInterval(this.timer);
                        this.timer = null;
                        this.getVerifyImg();
                        this.overtime = true;
                    }
                }, 1000);
            }
        },
        // 获取图片验证码
        async getVerifyImg() {
            let res = await getVerifyImgApi({
                uuid: this.uuid, //随机字串
            });
            if (res.status == 0) {
                this.imgcode = res.data;
            } else {
                uni.showToast({
                    title: res.info ? res.info : '图片验证码加载失败',
                    icon: 'none',
                });
                return;
            }
        },
        agreement(e) {
            if (e.detail.value[0] == 'agreement') {
                this.agreeCheckBox = true;
            } else {
                this.agreeCheckBox = false;
            }
        },
        clickXieyi(type) {
            uni.navigateTo({
                url: `/packages/setting/pages/agreement/main?value=${type}`,
            });
        },
        //登录
        loginClick() {
            if (this.phone == '' || !this.$test.checkMobilePhoneNumber(this.phone)) {
                uni.showToast({
                    title: '请输入正确的手机号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }

            if (this.imgVerifyCode === '') {
                uni.showToast({
                    title: '请输入图片验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return false;
            }
            if (this.smsVerifyCode === '') {
                uni.showToast({
                    title: '请输入短信验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return false;
            }
            // this.checkVerifyCodePost()
            if (this.overtime === true) {
                uni.showToast({
                    title: '短信验证码超时，请重新获取。',
                    icon: 'none',
                    duration: 2000,
                });
                return false;
            }
            if (!this.agreeCheckBox) {
                uni.showToast({
                    title: '请阅读并勾选用户协议和隐私政策',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            wx.showLoading({
                title: '加载中',
                mask: true,
            });
            //可以登录了

            let params = {
                phone: this.phone,
                verifyCode: this.smsVerifyCode,
                // channelCode: 1005
            };

            this.$wxLogin
                .registerByAuthPhoneNumber(params, 'smsLogin')
                .then(async result => {
                    await this.$store.dispatch('card/getAllCardList');
                    console.log(result, 'result');
                    this.loginBackParams.isLogin = true;
                    let pages = getCurrentPages(); // 当前页面
                    let beforePage = pages[pages.length - 2]; // 上一页
                    console.log(beforePage,'登lu回调=====beforePage');
                    beforePage.$vm.codeLoginFlag = true;
                    beforePage.$vm.codeLoginObj = this.loginBackParams;
                    // 返回上一页 delta返回的页面数 如果delta大于现有页面数，则返回首页
                    uni.navigateBack({ delta: 1 });
                    wx.hideLoading();
                })
                .catch(err => {
                    this.getVerifyImg();
                    wx.hideLoading();
                });
        },
    },
};
</script>
<style scoped lang="scss">
/* @import url(); 引入css类 */
.signin {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 50px 15px 0;

    .header {
        display: flex;
        width: 100%;
        justify-content: space-between;
        flex-direction: row;
        align-items: flex-end;
        .logo {
            display: flex;
            justify-content: center;
            margin-right: 30px;
            .login-image {
                height: 70px;
                width: 70px;
            }
        }

        .app-name-div {
            display: flex;
            margin-left: 15px;
            flex-direction: column;
        }

        span {
            display: flex;
            justify-content: flex-start;
            color: #333;
            font-size: 21px;
        }

        span:last-child {
            font-weight: bold;
        }
    }
    .setting_pwd {
        padding-bottom: 10px;
    }
    .login-content-class {
        margin-top: 30px;
        flex: 1;

        .username_style {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 16px;
            margin: 5px 15px;

            input {
                flex: 1;
                background: none;
                max-width: 60%;
            }
            .verify_img {
                position: relative;
                margin-top: -10px;
                width: 80px;
                height: 30px;
                margin-right: 0px;
            }
            .right-code {
                min-width: 140rpx;
                background: #f96702;
                border-radius: 8rpx;
                font-size: 24rpx;
                line-height: 52rpx;
                text-align: center;
                color: #fff;
            }
        }
        .privacy_agreement_class {
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #999999;
            width: 90%;
            margin: 10px 0 10px 10px;
            .popup-content-agree {
                display: flex;
                align-items: center;
                text-align: start;

                .popup-content-agree-checkBox {
                    transform: translate(-4px) scale(0.6);
                }

                .popup-content-agree-text {
                    flex-shrink: 0;
                    font-size: 24rpx;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 33rpx;
                    transform: translate(-5rpx);
                    span {
                        color: #e64f22;
                    }
                }
            }
        }
    }
    .box-shadow-base {
        box-shadow: 2px 2px 10px #e0e0e0;
        border-radius: 5px;
    }
    .login_style {
        margin: 0px 15px;
        text-align: center;
        border-radius: 5px;
        height: 45px;
        font-size: 16px;
        line-height: 45px;
        background-color: #e0e0e0;
        color: #ffffff;
    }

    .login_style2 {
        background-color: #f96702;
    }

    .line_bottom {
        position: relative;
        &.line_bottom:before {
            content: ' ';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 1px;
            border-top: 1px solid #f3f3f3;
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }
    }
}
</style>
