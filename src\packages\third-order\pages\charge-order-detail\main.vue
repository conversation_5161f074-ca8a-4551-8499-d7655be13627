<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 订单详情页面 -->
        <div class="select-region-class">
            <zj-navbar title="订单明细"></zj-navbar>
            <div class="content-view mh-0" v-if="Object.keys(pageInfo).length > 0">
                <div class="commodity-order-box">
                    <div class="gas-station">
                        <img class="gas-station-img" src="../../image/chargeCheer.png" alt />
                        <div class="gas-station-text">{{ pageInfo.stationName || '中国石油加油站' }}</div>
                    </div>
                    <div class="commodity-order">
                        <div class="information">
                            <div class="information-data">
                                <div class="information-data-left">充电枪编号</div>
                                <div class="information-data-right">
                                    {{ pageInfo.chargingGunNo }}
                                </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">充电电量</div>
                                <div class="information-data-right"> {{ pageInfo.chargeQty ? pageInfo.chargeQty + '度' : '-' }} </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">充电时长</div>
                                <div class="information-data-right"> {{ pageInfo.chargeTime ? pageInfo.chargeTime + '分钟' : '-' }} </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">充电开始时间</div>
                                <div class="information-data-right"> {{ pageInfo.chargeStartTime || '-' }} </div>
                            </div>
                            <div class="information-data">
                                <div class="information-data-left">充电结束时间</div>
                                <div class="information-data-right"> {{ pageInfo.chargeEndTime || '-' }} </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="details-price">
                    <div class="gas-station">
                        <div class="gas-station-text">订单信息</div>
                    </div>
                    <template v-if="pageParams.orderNo && pageParams.orderStatus != 1">
                        <div class="details-price-item">
                            <div class="item-left">订单编号</div>
                            <div class="item-right">
                                <div class="item-right-text">{{ pageInfo.orderNo || '' }}</div>
                                <div class="copy" @click="copyProductNo(pageInfo.orderNo)">复制</div>
                            </div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">结算时间</div>
                            <div class="item-right">{{ pageInfo.payConfirmationTime }}</div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">电费</div>
                            <div class="item-right">&yen;{{ pageInfo.electricity }}</div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">服务费</div>
                            <div class="item-right">&yen;{{ pageInfo.serviceCharge || 0 }}</div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">订单总额</div>
                            <div class="item-right">&yen;{{ pageInfo.orderTotalAmount }}</div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">优惠</div>
                            <div class="item-right text">&yen;{{ pageInfo.discountTotalAmount || 0 }}</div>
                        </div>
                        <div class="details-price-item pay-list" v-for="(item, index) in pageInfo.discountList" :key="index">
                            <div class="item-left gray">{{ item.payMethodName || '' }}</div>
                            <div class="item-right text gray">
                                {{ item.payMethodName == '积分' ? item.payAmount : item.payAmount + '元' }}
                            </div>
                        </div>
                        <div class="details-price-item">
                            <div class="item-left">实付金额</div>
                            <div class="item-right">&yen;{{ pageInfo.actualPayTotalAmount || 0 }}</div>
                        </div>
                        <div class="details-price-item pay-list" v-for="(item, index) in pageInfo.payDetailList" :key="index">
                            <div class="item-left gray">{{ item.payMethodName || '' }}</div>
                            <div class="item-right text gray">
                                {{ item.payMethodName == '积分' ? item.payAmount : item.payAmount + '元' }}
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="empty-box">
                            <div class="empty-text empty-text1">订单信息获取中...</div>
                            <div class="empty-text empty-text2">上游充电服务商同步订单信息可能有延迟，请稍后再看</div>
                        </div>
                    </template>
                </div>
                <div class="details-btns">
                    <div v-if="pageInfo.invoiceFlag == '0'" class="btn btn2" @click="nextStep">去开票</div>
                    <div v-if="pageInfo.invoiceFlag == '2'" class="btn btn2" @click="seeInvoice">查看发票</div>
                </div>
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import QRCode from 'qrcode';
import { chargeOrderDetails, getInvoiceByOrderNoApi } from '../../../../s-kit/js/v3-http/https3/order/index';
import { invoiceListApi, chargeBeforeMakeInvoiceCheckApi } from '../../../../s-kit/js/v3-http/https3/invoice/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import clock from '../../components/clock.vue';
export default {
    mixins: [publicMixinsApi],
    components: {
        clock,
    },
    data() {
        return {
            // 订单信息是否展示更多按钮绑定值，通过该字段判断
            orderDetailStatus: true,
            pageParams: {},
            pageInfo: {},
            isFirstPost: true,
            addAnagerPopupShow: false,
            codeImg: true,
            imagePath: '',
            timeout: true,
        };
    },
    onLoad(option) {
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endIf
        this.pageParams = JSON.parse(decodeURIComponent(option.data));
        if (this.pageParams.type == 'addAnager') {
            this.addAnagerPopupShow = true;
        }
        // 增加支付宝消息跳转来直接去评价的标识;
        this.$sKit.mpBP.tracker('我的消费订单', {
            seed: 'xfOrderBiz',
            pageID: 'xfOrder_des_page',
            refer: this.pageParams.refer,
            channelID: clientCode,
        });
        this.toEvaluate = this.$sKit.commonUtil.throttleUtil(this.toEvaluate);
        this.nextStep = this.$sKit.commonUtil.throttleUtil(this.nextStep);
        if (this.pageParams.orderNo) {
            this.orderDetailPost();
        } else {
            this.pageInfo = this.pageParams.pageInfo;
        }
    },
    onShow() {
        if (!this.isFirstPost) {
            this.orderDetailPost();
        }
    },
    methods: {
        /**
         * @description  : 跳转去评价页面
         * @return        {*}
         */
        toEvaluate() {
            let params = {
                evaluateType: 'order',
                ...this.pageInfo,
            };
            this.$sKit.layer.useRouter('/packages/third-evaluate/pages/home/<USER>', params);
        },
        /**
         * @description  : 匹配回显订单发票状态文本
         * @param         {*} flag:
         * @return        {*}
         */
        getInvoiceFlag(flag) {
            if (flag == 0) {
                return '未开票';
            } else if (flag == 1) {
                return '已开票';
            } else if (flag == 2) {
                return '已提交';
            } else if (flag == 3) {
                return '不可开票';
            } else if (flag == 4) {
                return '不可开票';
            } else if (flag == 5) {
                return '不可开票';
            } else if (flag == 6) {
                return '不可开票';
            } else if (flag == 7) {
                return '不可开票';
            } else {
                return '';
            }
        },
        /**
         * @description  : 匹配回显订单类型
         * @param         {*} flag:
         * @return        {*}
         */
        getOrderType(flag) {
            if (flag == 11) {
                return 'e享加油';
            } else if (flag == 12) {
                return '加油卡预授权加油';
            } else if (flag == 13) {
                return '后支付加油';
            } else if (flag == 23) {
                return 'e享购';
            } else if (flag == 22) {
                return '积分换购';
            } else if (flag == 14) {
                return '室内付款';
            } else if (flag == 15) {
                return '异常订单补录';
            } else if (flag == 20) {
                return '洗车服务';
            } else if (flag == 47) {
                return '洗车服务';
            } else if (flag == 37) {
                return '加油机器人';
            } else if (flag == 48 || flag == 49) {
                return '能源锦鲤商城';
            } else if (flag == 55) {
                return '散装油';
            } else {
                return '';
            }
        },
        // 获取订单详情，并把油品和非油品商品数据分为两个数组便于展示
        async orderDetailPost() {
            if (JSON.stringify(this.pageParams) == '{}') return;
            let params = {
                orderNo: this.pageParams.orderNo,
                orderDetailStatus: this.orderDetailStatus,
            };
            let res = await chargeOrderDetails(params);
            if (res.success == true) {
                this.pageInfo = res.data;
                this.pageInfo.payChannelStr = this.pageInfo.payChannel.join(',') || '';
                // 增加支付宝消息跳转来直接去评价的标识;
                if (this.pageParams.routerFrom && this.pageParams.routerFrom == 'messageEvaluate' && this.isFirstPost) {
                    if (this.pageInfo.commentFlag == 1) {
                        this.toEvaluate();
                    }
                }
                this.isFirstPost = false;
            }
        },
        // 复制
        copyProductNo(value) {
            uni.setClipboardData({
                data: value, //要被复制的内容
                success: () => {
                    this.$sKit.layer.showToast({
                        title: '复制成功',
                    });
                },
            });
        },
        // 申请开票
        async nextStep() {
            this.$sKit.mpBP.tracker('我的消费订单', {
                seed: 'xfOrderBiz',
                pageID: 'invoicingBut',
                refer: this.pageParams.refer,
                channelID: clientCode,
            });
            let apiParams = {
                orderList: [
                    {
                        stationCode: this.pageInfo.stationCode,
                        businessDay: this.pageInfo.businessDay,
                        orderNo: this.pageParams.orderNo,
                    },
                ],
            };
            let res = await chargeBeforeMakeInvoiceCheckApi(apiParams, {
                handleErrorFn: () => {
                    this.orderDetailPost();
                },
            });
            if (res && res.success) {
                if (res.data.flag) {
                    let goods = this.pageInfo.chargeOrderItemList
                        .map(goodsItem => {
                            return goodsItem.productName;
                        })
                        .join(',');
                    let url = '/packages/third-invoice/pages/invoice-form/main';
                    let params = {
                        type: 'invoice',
                        orderType: 'charge',
                        orderNoList: [this.pageParams.orderNo],
                        checkAllAmount: this.pageInfo.actualPayTotalAmount,
                        createTime: this.pageInfo.createTime,
                        orgName: this.pageInfo.stationName,
                        goods: goods,
                        refer: 'r32',
                    };
                    this.$sKit.layer.useRouter(url, params);
                }
            }
        },
        // 查看发票
        async seeInvoice() {
            let params = {
                orderNum: this.pageParams.orderNo,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.select-region-class {
    background: #f7f7fb;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .content-view {
        width: 100%;
        flex: 1;
        padding: 32rpx;
        overflow-y: auto;
        overflow-x: hidden;
        .wxCode-box {
            margin-bottom: 20rpx;
        }

        .gas-station {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            .gas-station-img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
            }

            .gas-station-text {
                width: 200px;
                font-size: 14px;
                font-weight: bold;
                color: #000;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .oil-order {
            width: 100%;
            margin: 0 auto;
            // height: 257px;
            background: #ffffff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .commodity-order-box {
            width: 100%;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;

            .commodity-order {
                margin-top: 15px;

                .item-bottom {
                    height: 35px;
                    line-height: 35px;
                    text-align: center;
                    color: #666;
                }
            }
        }

        .details-price {
            width: 100%;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            padding: 23rpx 28rpx 29rpx;

            .gas-station {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;

                .gas-station-img {
                    width: 16px;
                    height: 16px;
                    margin-right: 5px;
                }

                .gas-station-text {
                    width: 200px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .details-price-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .item-left {
                    flex-shrink: 0;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 67rpx;
                }

                .item-right {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 67rpx;
                    word-wrap: break-word;
                    word-break: break-all;
                    overflow: hidden;

                    .item-right-text {
                        word-break: break-all;
                        max-width: 420rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 28rpx;
                        word-wrap: break-word;
                        word-break: break-all;
                        overflow: hidden;
                    }

                    .copy {
                        width: 29px;
                        height: 16px;
                        background: #ffffff;
                        border-radius: 2px;
                        border: 1px solid #999999;
                        text-align: center;
                        line-height: 16px;
                        color: #666;
                        font-size: 10px;
                        margin-left: 5px;
                    }
                }

                .gray {
                    color: #666666;
                }

                .item-right-button {
                    padding: 0 20rpx;
                    height: 48rpx;
                    border-radius: 4rpx;
                    border: 1rpx solid #333333;
                    line-height: 48rpx;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #333333;
                    text-align: center;
                }

                .item-right-text {
                    line-height: 50rpx;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #333333;
                }

                .color {
                    color: #e64f22;
                    border: 1rpx solid #e64f22;
                }
            }

            .empty-box {
                height: 230px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .empty-text {
                    font-weight: 400;
                    color: #666666;
                    line-height: 48rpx;
                }

                .empty-text1 {
                    font-size: 28rpx;
                }
                .empty-text2 {
                    font-size: 24rpx;
                }
            }
        }

        .commodity {
            display: flex;

            .commodity-img {
                width: 50px;
                height: 50px;
                border-radius: 5px;
                overflow: hidden;
                margin-right: 10px;
            }

            .detail-left-number {
                width: 100rpx;
                height: 100rpx;
                border-radius: 5px;
                margin-right: 10px;
                background-color: #f5c41b;
                line-height: 100rpx;
                text-align: center;
                font-weight: bold;
                font-size: 40rpx;

                span {
                    vertical-align: text-top;
                    font-size: 24rpx;
                    line-height: 20rpx;
                }
            }

            .detail-left-img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 5px;
                margin-right: 10px;
            }

            .commodity-title {
                flex: 1;
                color: #333;
                font-size: 14px;
            }

            .commodity-price {
                text-align: right;

                .unit-price {
                    font-size: 14px;
                    color: #333;
                    line-height: 20px;
                }

                .num {
                    font-size: 10px;
                    color: #666;
                    line-height: 20px;
                }

                .total-price {
                    font-size: 14px;
                    color: #333;
                    font-weight: bold;
                }
            }
        }

        .pay-list {
            padding-left: 30rpx;
        }

        .information {
            margin-top: 30rpx;
        }
    }
}

.information-data {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15rpx;
    flex-wrap: wrap;

    &:nth-of-type(1) {
        margin-top: 0;
    }

    .information-data-left {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 48rpx;
    }

    .information-data-right {
        text-align: right;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-end;
        line-height: 48rpx;

        .copy {
            width: 29px;
            height: 16px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #999999;
            text-align: center;
            line-height: 16px;
            color: #666;
            font-size: 10px;
            margin-left: 5px;
        }
    }

    .text {
        color: #e64f22;
    }
}

.details-btns {
    margin-top: 32rpx;
    display: flex;
    justify-content: center;

    .btn {
        width: 241px;
        height: 88rpx;
        border-radius: 16rpx;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 88rpx;
        text-align: center;
    }
    .btn1 {
        background: #ffffff;
        border: 1px solid #e64f22;
        color: #e64f22;
    }
    .btn2 {
        background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        color: #ffffff;
    }
}
</style>
