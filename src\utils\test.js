// util.showModal('暂时无法获取定位，已为您选择默认城市', true, '#333333', '', '确定', '')
import util from './index';
/**
 * 验证电子邮箱格式
 */
function email(value) {
    return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value);
}

/**
 * 验证手机格式
 */
function mobile(value) {
    return /^1[23456789]\d{9}$/.test(value);
}

/**
 * 验证URL格式
 */
function url(value) {
    return /^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(
        value,
    );
}

/**
 * 验证日期格式
 */
function date(value) {
    return !/Invalid|NaN/.test(new Date(value).toString());
}

/**
 * 验证ISO类型的日期格式
 */
function dateISO(value) {
    return /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(value);
}

/**
 * 验证十进制数字
 */
function number(value) {
    return /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(value);
}

/**
 * 验证整数
 */
function digits(value) {
    return /^\d+$/.test(value);
}

/**
 * 验证身份证号码
 */
function idCard(value) {
    return /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(value);
}

/**
 * 是否车牌号
 */
function carNo(value) {
    // 新能源车牌
    const xreg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
    // 旧车牌
    const creg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
    if (value.length === 7) {
        return creg.test(value);
    } else if (value.length === 8) {
        return xreg.test(value);
    } else {
        return false;
    }
}
/**
 * 3.0是否车牌号
 */
function carNoV3(value) {
    const reg =
        /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z](?:((\d{5}[A-HJK])|([A-HJK][A-HJ-NP-Z0-9][0-9]{4}))|[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/;
    return reg.test(value);
    return;
    // 新能源车牌
    const xreg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[A-Z]$)|([A-Z][A-HJ-NP-Z0-9][0-9]{4}$))/;
    // 旧车牌
    const creg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
    if (value.length === 7) {
        return creg.test(value);
    } else if (value.length === 8) {
        return xreg.test(value);
    } else {
        return false;
    }
}

/**
 * 金额,只允许2位小数
 */
function amount(value) {
    //金额，只允许保留两位小数
    return /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(value);
}

/**
 * 中文
 */
function chinese(value) {
    let reg = /^[\u4e00-\u9fa5]+$/gi;
    return reg.test(value);
}

/**
 * 只能输入字母
 */
function letter(value) {
    return /^[a-zA-Z]*$/.test(value);
}

/**
 * 只能是字母或者数字
 */
function enOrNum(value) {
    //英文或者数字
    let reg = /^[0-9a-zA-Z]*$/g;
    return reg.test(value);
}
// 只能输入数字
function checkJYK(str) {
    let reg = /^\d{16}$/;
    return reg.test(str);
}
function checkS(str) {
    // 判断字符串是否为数字和字母组合
    var zg = /^[0-9A-Z]*$/;
    if (!zg.test(str)) {
        return false;
    } else {
        return true;
    }
}
/**
 * 只能是字母或者中文
 */
function checkSAndE(str) {
    if (!str) {
        return true;
    }
    var zg = /^[\u0391-\uFFE5A-Za-z\·]{2,20}$/g;
    if (zg.test(str)) {
        return true;
    } else {
        return false;
    }
}

/**
 * 验证是否包含某个值
 */
function contains(value, param) {
    return value.indexOf(param) >= 0;
}

/**
 * 验证一个值范围[min, max]
 */
function range(value, param) {
    return value >= param[0] && value <= param[1];
}

/**
 * 验证一个长度范围[min, max]
 */
function rangeLength(value, param) {
    return value.length >= param[0] && value.length <= param[1];
}

/**
 * 是否固定电话
 */
function landline(value) {
    let reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/;
    return reg.test(value);
}

/**
 * 判断是否为空
 */
function empty(value) {
    switch (typeof value) {
        case 'undefined':
            return true;
        case 'string':
            if (value.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
            break;
        case 'boolean':
            if (!value) return true;
            break;
        case 'number':
            if (0 === value || isNaN(value)) return true;
            break;
        case 'object':
            if (null === value || value.length === 0) return true;
            for (var i in value) {
                return false;
            }
            return true;
    }
    return false;
}

/**
 * 是否json字符串
 */
function jsonString(value) {
    if (typeof value == 'string') {
        try {
            var obj = JSON.parse(value);
            if (typeof obj == 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }
    return false;
}

/**
 * 是否数组
 */
function array(value) {
    if (typeof Array.isArray === 'function') {
        return Array.isArray(value);
    } else {
        return Object.prototype.toString.call(value) === '[object Array]';
    }
}

/**
 * 是否对象
 */
function object(value) {
    return Object.prototype.toString.call(value) === '[object Object]';
}

function isEmojiCharacter(substring) {
    for (var i = 0; i < substring.length; i++) {
        var hs = substring.charCodeAt(i);
        if (0xd800 <= hs && hs <= 0xdbff) {
            if (substring.length > 1) {
                var ls = substring.charCodeAt(i + 1);
                var uc = (hs - 0xd800) * 0x400 + (ls - 0xdc00) + 0x10000;
                if (0x1d000 <= uc && uc <= 0x1f77f) {
                    return true;
                }
            }
        } else if (substring.length > 1) {
            var ls = substring.charCodeAt(i + 1);
            if (ls == 0x20e3) {
                return true;
            }
        } else {
            if (0x2100 <= hs && hs <= 0x27ff) {
                return true;
            } else if (0x2b05 <= hs && hs <= 0x2b07) {
                return true;
            } else if (0x2934 <= hs && hs <= 0x2935) {
                return true;
            } else if (0x3297 <= hs && hs <= 0x3299) {
                return true;
            } else if (
                hs == 0xa9 ||
                hs == 0xae ||
                hs == 0x303d ||
                hs == 0x3030 ||
                hs == 0x2b55 ||
                hs == 0x2b1c ||
                hs == 0x2b1b ||
                hs == 0x2b50
            ) {
                return true;
            }
        }
    }
}

/**
 * 身份证精确验证
 */
function idCardComplex(str) {
    var myreg =
        /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/;
    if (!str || !myreg.test(str)) {
        return false;
    } else {
        var city = {
            11: '北京',
            12: '天津',
            13: '河北',
            14: '山西',
            15: '内蒙古',
            21: '辽宁',
            22: '吉林',
            23: '黑龙江 ',
            31: '上海',
            32: '江苏',
            33: '浙江',
            34: '安徽',
            35: '福建',
            36: '江西',
            37: '山东',
            41: '河南',
            42: '湖北 ',
            43: '湖南',
            44: '广东',
            45: '广西',
            46: '海南',
            50: '重庆',
            51: '四川',
            52: '贵州',
            53: '云南',
            54: '西藏 ',
            61: '陕西',
            62: '甘肃',
            63: '青海',
            64: '宁夏',
            65: '新疆',
            71: '台湾',
            81: '香港',
            82: '澳门',
            91: '国外 ',
        };
        if (!city[str.substr(0, 2)]) {
            return false;
        }
        let sBirthday = str.substr(6, 4) + '-' + Number(str.substr(10, 2)) + '-' + Number(str.substr(12, 2));
        var d = new Date(sBirthday.replace(/-/g, '/'));
        if (sBirthday != d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()) {
            //alert("非法生日");
            return false;
        }
        let now = new Date();
        if (d > now || d == now) {
            return false;
        }
        //18位身份证需要验证最后一位校验位
        let strArr = str.split('');
        //∑(ai×Wi)(mod 11)
        //加权因子
        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        //校验位
        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
        var sum = 0;
        var ai = 0;
        var wi = 0;
        for (var i = 0; i < 17; i++) {
            ai = strArr[i];
            wi = factor[i];
            sum += ai * wi;
        }
        if (parity[sum % 11] != strArr[17]) {
            return false;
        }
        return true;
    }
}

/**
 * 护照验证
 */
function checkPassport(code) {
    return !(!code || !/^[0-9A-Za-z]{9}$|^[0-9A-Za-z]{8}$/.test(code));
}

/**
 * 港澳通行证验证
 */
function checkGangAoTraffic(code) {
    return !(!code || !/^[C|c]{1}\d{8}$|^[H|h]\d{8}$|^[W|w]\d{8}$|^[C|c][A-Za-z]\d{7}$/.test(code));
}

/**
 * 台胞证验证
 */
function checkTaiWanTraffic(code) {
    return !(!code || !/(^[a-zA-Z][0-9]{9}$)|(^[0-9]{18}$)|(^[0-9]{8}$)/.test(code));
}

// 新增
// 校验抬头
function checkNormal(str) {
    let reg = /^[a-zA-Z0-9\u4E00-\u9FA5\(\)\(\)]*$/g;
    // let reg = /^[a-zA-Z0-9\u4E00-\u9FA5]*$/g
    return reg.test(str);
}

// 个人抬头税号，不校验必填
function checkShuiHao_individual(taxId) {
    if (taxId == '' || !taxId) {
        return true;
    }
    //税号长度7-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{6,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        util.showModal('纳税人识别号请输入6-20位的数字或大写字母的组合', true, '#333333', '', '确定', '');
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                util.showModal('纳税人识别号前两位是数字的话，3-6位也必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                util.showModal('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号前两位均为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                util.showModal('纳税人识别号第1位为字母，则第2-16位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                util.showModal('纳税人识别号第1位为数字，则前6位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                util.showModal('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    return true;
}
// 个人抬头税号3.0，不校验必填
function checkShuiHao_individual_third(taxId) {
    if (taxId == '' || !taxId) {
        return true;
    }
    //税号长度15-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{15,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        util.showModal('纳税人识别号请输入15-20位的数字或大写字母的组合', true, '#333333', '', '确定', '');
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                util.showModal('纳税人识别号前两位是数字的话，3-6位也必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                util.showModal('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号前两位均为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                util.showModal('纳税人识别号第1位为字母，则第2-16位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                util.showModal('纳税人识别号第1位为数字，则前6位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                util.showModal('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    return true;
}

function checkShuiHao_New(taxId) {
    // 非空校验
    if (taxId == '') {
        util.showModal('纳税人识别号不能为空', true, '#333333', '', '确定', '');
        return false;
    }
    //税号长度7-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{6,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        util.showModal('纳税人识别号请输入6-20位的数字或大写字母的组合', true, '#333333', '', '确定', '');
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                util.showModal('纳税人识别号前两位是数字的话，3-6位也必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                util.showModal('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号前两位均为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                util.showModal('纳税人识别号第1位为字母，则第2-16位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                util.showModal('纳税人识别号第1位为数字，则前6位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                util.showModal('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    return true;
}
// 3.0税号
function checkShuiHao_New_third(taxId) {
    if (taxId == '') {
        util.showModal('纳税人识别号不能为空', true, '#333333', '', '确定', '');
        return false;
    }
    //税号长度15-20位，数字或大写字母的组合
    var str = /^[A-Z0-9]{15,20}$/; //
    if (!str.test(taxId)) {
        // toast('纳税人识别号请输入7到20位的数字或大写字母的组合');
        util.showModal('纳税人识别号请输入15-20位的数字或大写字母的组合', true, '#333333', '', '确定', '');
        return false;
    }
    //如果长度等于16-18位
    str = /^[A-Z0-9]{16,18}$/;
    if (str.test(taxId)) {
        //当前是16-18
        console.log('16---18');
        //如果前2位是全数字,则要求3-6位也必须是数字,其他位可以是任意数字或大写字母；
        str = /^([0-9]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前2位是数字');
            str = /^(\d{6})+([A-Z0-9]{10,12})$/;
            console.log(str.test(taxId));
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位是数字的话，3-6位也必须为数字');
                util.showModal('纳税人识别号前两位是数字的话，3-6位也必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字、第2位为字母，则要求3-8位为必须为数字，其他位可以是任意数字或大写字母
        str = /^(\d[A-Z])+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字、第2位为字母');
            str = /^(\d[A-Z])+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号第1位为数字、第2位为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为字母、第2位为数字，则要求3-7位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]\d+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母、第2位为数字');
            str = /^([A-Z]\d)+(\d{5})+([A-Z0-9]{9,11})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字');
                util.showModal('纳税人识别号第1位为字母、第2位为数字，则3-7位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果前两位均为字母，则要求3-8位必须为数字，其他位可以是任意数字或大写字母。
        str = /^([A-Z]{2})+([A-Z0-9]{14,16})$/;
        if (str.test(taxId)) {
            console.log('前两位均为字母');
            str = /^([A-Z]{2})+(\d{6})+([A-Z0-9]{8,10})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号前两位均为字母，则3-8位必须为数字');
                util.showModal('纳税人识别号前两位均为字母，则3-8位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    //如果长度等于19、20位
    str = /^[A-Z0-9]{19,20}$/;
    if (str.test(taxId)) {
        console.log('19-20位');
        //如果第1位为字母，则要求第2-16位必须为数字，其他位可以是任意数字或大写字母
        str = /^[A-Z]+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为字母');
            str = /^[A-Z]\d{15}[A-Z0-9]{3,4}$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为字母，则第2-16位必须为数字');
                util.showModal('纳税人识别号第1位为字母，则第2-16位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第1位为数字，则要求前6位必须为数字,其他位可以是任意数字或大写字母
        str = /^\d+([A-Z0-9]{18,19})$/;
        if (str.test(taxId)) {
            console.log('第1位为数字');
            str = /^(\d{6})+([A-Z0-9]{13,14})$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第1位为数字，则前6位必须为数字');
                util.showModal('纳税人识别号第1位为数字，则前6位必须为数字', true, '#333333', '', '确定', '');
                return false;
            }
        }
        //如果第7-15位均为数字时，其他位可以是任意数字或大写字母；
        str = /^([A-Z0-9]{6})+(\d{9})+([A-Z0-9]{4,5})$/;
        if (str.test(taxId)) {
            console.log('第7-15位均为数字时');
        } else {
            //如果第7-15位中含有字母，则要求税号中的字母不能含有ISZO中的任意一个
            console.log('第7-15位中含有字母');
            str = /^[^ISZO\x22]+$/;
            if (!str.test(taxId)) {
                // toast('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个');
                util.showModal('纳税人识别号第7-15位中含有字母的话，税号不能含有ISZO字母中的任意一个', true, '#333333', '', '确定', '');
                return false;
            }
        }
    }
    return true;
}

function checkAddress(str) {
    let reg = /^[a-zA-Z0-9\u4E00-\u9FA5@\(\)\,\s]*$/g;
    return reg.test(str);
}

function checkTelPhone(str) {
    if (!str) {
        return true;
    }
    // let reg = /^[0-9\-\s]{0,20}$/g
    let reg = /^[0-9\-\s]{0,20}$/g;
    // let reg = /^((\+86)|(86))?(1)\d{10}$/.test(value) || /^0[0-9-]{10,13}$/g
    return reg.test(str);
}

// packages/place-order/pages/confirm-order/main
function validator(str) {
    let reg = new RegExp(/\s/g); //清空字符串中所有空格
    console.log(reg.test(str));
    return reg.test(str);
}

function checkBankName(str) {
    let reg = /^[\u4E00-\u9FA50-9a-zA-Z\-]{0,40}$/;
    return reg.test(str);
}

function checkBankCount(str) {
    if (!str) {
        return true;
    }
    let reg = /^[0-9]{0,20}$/g;
    return reg.test(str);
}

function newCheckBankCount(str) {
    if (!str) {
        return true;
    }
    let reg = /^[0-9]{0,30}$/g;
    return reg.test(str);
}

// 检验名字
function checkName(str) {
    if (!str) {
        return true;
    }
    let reg = /^[\u4e00-\u9fa5\·\u3400-\u4DBF\u20000-\u2A6D6\u2A700-\u2B73F]{2,20}$/g;
    return reg.test(str);
}
// 检验
// function checkName (str) {
//   if (!str) {
//     return true
//   }
//   let reg = /^[A-Z0-9]{7-20}$/g
//   return reg.test(str)
// }

// 校验身份证 ,
function checkIdCard(str) {
    if (!str) {
        return true;
    }
    let reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X)$)/g;
    return reg.test(str);
}
function newCheckIdNumber(str) {
    if (!str) {
        return true;
    }
    let reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return reg.test(str);
}
// 校验手机号 ,
function checkMobilePhoneNumber(str) {
    if (!str) {
        return true;
    }
    let reg = /^1[0-9]{10}$/g;
    return reg.test(str);
}

function changeTreeDate(data, newKey, oldKey) {
    let str = JSON.stringify(data);
    let reg = new RegExp(oldKey, 'g');
    let newStr = str.replace(reg, newKey);
    return JSON.parse(newStr);
}

function checkIDType(str, type) {
    // let typeData = {
    //   '1': /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    //   '2': /^[\u4E00-\u9FA5]{3}[0-9]{8}号$/,
    //   '3': /(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/,
    //   '4': /^[0-9]{18}$/,  //营业执照
    //   '5': /(^\d{15}$)|(^\d{18}$)/, //驾驶证 位数15位或18位
    //   '6': /^[0-9A-Z]{8}-[0-9A-Z]{1}$/, /// 6、组织机构代码证规则：由八位数字(或大写字母)+连字符+一位数字(或大写字母)组成
    //   '7': /^C[0-9A-Z]{1}[0-9]{7}$/, //7、港澳通行证规则： 大写字母C+8位数字，或大写字母C+一个大写字母+7位数字
    //   '8': /^[0-9]{10}[a-zA-Z]{1}/   //台胞证规则：10个数字+1个英文字母
    // }
    let typeData = {
        1: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        2: /^.{5,20}$/,
        3: /^.{5,20}$/,
        4: /^.{5,20}$/,
        5: /^.{5,20}$/,
        6: /^.{5,20}$/,
        7: /^.{5,20}$/,
        8: /^.{5,20}$/,
    };
    let reg = typeData[type];
    return reg.test(str);
}
function checkPhone2(value) {
    if (value === '') {
        return false;
    } else {
        let regPone = null;
        let mobile = /^((13|14|15|16|17|18|19)[0-9]{1}\d{8})$/; //最新16手机正则
        let tel = /^((0\d{2,3}-\d{8})|(1[3584]\d{9-20}))$/; //座机
        if (value.charAt(0) == 0) {
            // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
            regPone = tel;
        } else {
            regPone = mobile;
        }

        if (!regPone.test(value)) {
            return false;
        } else {
            return true;
        }
    }
}
// function backPage () {

//   let pages = getCurrentPages(); // 当前页面
//   let beforePage = pages[pages.length - 2]; // 上一页
//   beforePage.$vm.listRefreshId = true;
//   // 返回上一页 delta返回的页面数 如果delta大于现有页面数，则返回首页
//   uni.navigateBack({
//     delta: 3
//   })
// }

function isChinese(str) {
    const re = /^[\u4e00-\u9fa5]+$/;
    return re.test(str);
}
export default {
    checkPhone2,
    email,
    mobile,
    url,
    date,
    dateISO,
    number,
    digits,
    idCard,
    idCardComplex,
    carNo,
    carNoV3,
    amount,
    chinese,
    letter,
    enOrNum,
    checkJYK,
    contains,
    range,
    rangeLength,
    empty,
    isEmpty: empty,
    jsonString,
    landline,
    object,
    array,
    isEmojiCharacter,
    checkPassport,
    checkGangAoTraffic,
    checkTaiWanTraffic,
    checkNormal,
    checkShuiHao_individual,
    checkShuiHao_New,
    checkShuiHao_New_third,
    checkShuiHao_individual_third,
    checkAddress,
    checkTelPhone,
    validator,
    checkBankName,
    checkBankCount,
    changeTreeDate,
    checkName,
    checkIdCard,
    checkMobilePhoneNumber,
    checkIDType,
    checkS,
    isChinese,
    checkSAndE,
    newCheckBankCount,
    newCheckIdNumber,
};
