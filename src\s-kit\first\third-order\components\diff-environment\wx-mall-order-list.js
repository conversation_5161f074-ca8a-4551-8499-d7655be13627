import { baseMallUrl } from '../../../../../../project.config';
import { encrytRoutingInfo } from '../../../../js/v3-http/https3/user';
export default {
    methods: {
        /*跳转详情示例
        payStatus="UNPAID"
        https://域名/b2c_pub/enloading?flag=7&subFlag=encodeURI(/usmile/order/detail/${orderId}/${orderStatus})

        payStatus="PAID"
        https://域名/b2c_pub/enloading?flag=7&subFlag=encodeURI(/usmile/order/detail/${mySOrderInfoList[0].orderId}/${mySOrderInfoList[0].orderStatus})
        未支付的情况下，给主订单id和状态，已支付的情况下，给子订单的id和状态

        emall.95504.net 生产域名
        temall.95504.net 测试域名


        */
        /*   orderDetail (id, bigId, status) {
          if (status === 'SUBMITTED' || status === 'SUBMITED') {
            this.$router.push({name: 'OrderDetail', params: { orderId: bigId, orderStatus: 'SUBMITED' }})
          } else {
            this.$router.push({name: 'OrderDetail', params: { orderId: id, orderStatus: status }})
          }
        },

        id是子订单id(mySOrderListInfo[0].orderId)，bigId是主订单id(最外层的orderId)，status是主订单状态(最外层的订单状态)
        要分两种场景，status === 'SUBMITTED' || status === 'SUBMITED'时，orderId是用主订单id；其他状态，orderId是子订单id(mySOrderListInfo[0].orderId)

        */
        //前往订单详情
        /*id:子订单信息
        bigId：最外层订单详情
        status：最外层订单状态
        */
        /**
         * @description  : 前往订单详情
         * @param         {*} id:子订单信息
         * @param         {*} bigId:最外层订单详情
         * @param         {*} status:最外层订单状态
         * @return        {*}
         */
        async toDetail(id, bigId, status) {
            return
            let url;
            if (status === 'SUBMITTED' || status === 'SUBMITED') {
                let params = `/usmile/order/detail/${bigId.orderId}/${status}@backFlag=none`;
                url = baseMallUrl + 'b2c_pub/enloading?flag=6&subFlag=' + encodeURIComponent(params);
            } else {
                let params = `/usmile/order/detail/${id.orderId}/${status}@backFlag=none`;
                url = baseMallUrl + 'b2c_pub/enloading?flag=6&subFlag=' + encodeURIComponent(params);
            }
            console.log('地址', url);
            let splitStr = `gsmsToken=${this.$store.state.token3}&gsmsFlag=true&clientCode=C12`;
            url = url.indexOf('?') > -1 ? `${url}&${splitStr}` : `${url}?${splitStr}`;
            this.getSignForNotVuePage(url).then(res => {
                uni.navigateTo({
                    url: '/packages/web-view/pages/home/<USER>' + encodeURIComponent(res),
                });
            });
        },
        getSignForNotVuePage(redirectUrl) {
            return new Promise(async (resolve, reject) => {
                let params = {
                    redirectUrl: redirectUrl, //encodeURIComponent(redirectUrl)
                };
                let res = await encrytRoutingInfo(params);
                if (res && res.success) {
                    resolve(res.data);
                }
            });
        },
    },
};
