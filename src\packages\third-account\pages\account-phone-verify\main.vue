<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <!-- 更换手机号页面 -->
        <div class="bg-FFFFFF" style="height: 100%">
            <zj-navbar :height="44" title="更换绑定手机号"></zj-navbar>
            <div class="content">
                <div class="text">
                    <div
                        >没有查到您的实人信息，需要对您的原手机号进行验证<div class="phone">{{ phoneNum }}</div>
                    </div>
                </div>
                <!-- <div v-else class="text">
                <div class="phone">{{ phoneNum }}</div>
            </div> -->
                <div class="line">
                    <input
                        type="text"
                        class="input_div"
                        placeholder-class="phone_input"
                        v-model="verificationCode"
                        placeholder="请输入短信验证码"
                    />
                    <div class="div" v-if="show" @click="getuserNewPhoneVerifyCodeNend">获取验证码</div>
                    <div class="div" v-else>{{ count }}秒后重新获取</div>
                </div>
                <div class="btn" @click="getuserBasicInfoModify">确认</div>
                <!-- <div class="btn" @click="sureHandle" v-else>确认注销</div> -->
            </div>
            <zj-show-modal v-if="cancelStatus == 'success'"></zj-show-modal>
            <zj-show-modal v-if="cancelStatus == 'fail'"></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { userBasicInfoQuery } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { messageCodeSend, messageCodeVerify } from '../../../../s-kit/js/v3-http/https3/wallet';
import { cancelAccountApi } from '../../../../s-kit/js/v3-http/https3/user.js';

import { mapGetters, mapState } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            cancelStatus: 'success',
            verificationCode: '',
            phone: '',
            phoneNum: '',
            type: 0,
            show: true,
            count: 60,
            timer: null,
            imageCheckShow: false,
            verifyType: 'accountCancel',
            authInfo: '', //短信验证码校验接口或者实人认证接口返回的RSA校验信息authInfo
            messageType: false,
        };
    },
    onLoad(option) {
        this.type = JSON.parse(decodeURIComponent(option.data)).type;
        this.verifyType = JSON.parse(decodeURIComponent(option.data)).verifyType;
    },
    async mounted() {
        // 客户名称，手机号，会员等级等基本信息和能源币，油卡，电子券，积分和余额。
        await this.$store.dispatch('basicCouponAction');
        this.phoneNum = this.memberBaseInfo.phone;
        // this.getuserBasicInfoQuery()//查询用户详细信息--获取用户手机号
    },
    computed: {
        ...mapGetters(['walletStatus', 'memberAccountInfo', 'memberBaseInfo']),
    },
    watch: {},
    methods: {
        //查询用户详细信息--获取用户手机号
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.phone = res.data.mobile;
                this.phoneNum = this.phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
            }
        },
        //获取验证码
        async getuserNewPhoneVerifyCodeNend() {
            let params = {
                messageType: 6,
                mobile: this.phone,
            };
            let res = await messageCodeSend(params);
            if (res && res.success) {
                uni.showToast({
                    title: '验证码已发送',
                    icon: 'none',
                    duration: 2000,
                });
            } else {
                uni.showToast({
                    title: res.message,
                    icon: 'none',
                    duration: 2000,
                });
            }
            this.messageType = true;
            this.countFn();
        },
        //获取手机号验证码按钮倒计时
        countFn() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.show = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.show = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        // 获取验证码
        async getuserBasicInfoModify() {
            if (!this.messageType) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            let params = {
                type: 6,
                mobile: this.phone,
                messageCode: this.verificationCode,
            };
            let res = await messageCodeVerify(params);
            if (res && res.success) {
                this.$sKit.layer.useRouter(
                    '/packages/third-account/pages/account-change-phone/main',
                    { type: 2, authInfo: res.data.authInfo },
                    'navigateTo',
                );
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 16px;

    .line {
        height: 44px;
        background: #f7f7fb;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        border-radius: 4px;
        overflow: hidden;

        .input_div {
            flex: 1;
            height: 100%;
            padding-left: 12px;
            background: #f7f7fb;
        }

        .div {
            height: 30px;
            line-height: 30px;
            width: 94px;
            text-align: center;
            color: #e64f23;
            border-left: 1px solid #d3d3d3;
        }
    }

    .text {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 30px;
        padding: 18px;

        .phone {
            display: inline;
            font-size: 21px;
            font-weight: bold;
            color: #333333;
            vertical-align: bottom;
        }
    }

    .btn {
        height: 44px;
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
        line-height: 44px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
    }
}
</style>
