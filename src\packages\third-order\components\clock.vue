<template>
    <div class="top-view">
        <div class="system-time">
            <div class="countdown-bg1 font-style">{{ hour }}</div
            >: <div class="countdown-bg2 font-style">{{ minutes }} </div>: <div class="countdown-bg3 font-style">{{ seconds }}</div
            >:
            <div class="countdown-bg4 font-style">{{ count }}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: {},
    data() {
        return {
            timer: null,
            // 倒计时展示绑定值
            hour: '00',
            minutes: '00',
            seconds: '00',
            count: '00',
        };
    },
    created() {},
    mounted() {
        this.goto();
    },
    // 销毁计时器
    beforeDestroy() {
        clearInterval(this.timer);
    },
    methods: {
        // 时钟
        goto() {
            clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.count = Number(this.count);
                if (this.count < 99) {
                    this.count = this.count + 1;
                } else {
                    this.count = 0;
                }
                if (this.count < 10) {
                    this.count = '0' + this.count;
                }
                var date = new Date();
                this.hour = date.getHours(); // 时
                if (this.hour >= 0 && this.hour <= 9) {
                    this.hour = '0' + this.hour;
                }
                this.minutes = date.getMinutes(); // 分
                if (this.minutes >= 0 && this.minutes <= 9) {
                    this.minutes = '0' + this.minutes;
                }
                this.seconds = date.getSeconds(); //秒
                if (this.seconds >= 0 && this.seconds <= 9) {
                    this.seconds = '0' + this.seconds;
                }
            }, 10);
        },
    },
};
</script>

<style lang="scss" scoped>
.top-view {
    width: 100%;
    background: #ffffff;

    .system-time {
        width: 100%;
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #e64f22;
        font-size: 16px;
        font-weight: bold;
        padding: 0 70px;

        div {
            width: 44px;
            height: 44px;
            color: #ffffff;
            border-radius: 10px;
            text-align: center;
            line-height: 44px;
            font-size: 18px;
            box-shadow: 0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.07);
        }
    }
}
</style>
