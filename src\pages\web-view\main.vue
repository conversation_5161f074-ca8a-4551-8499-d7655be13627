<template>
    <div>
        <web-view :src="src" @message="handleWebviewMessage" :update-title="false" @onPostMessage="handlePostMessage"></web-view>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    // props: {
    //     src: String
    // }
    data() {
        return {
            src: '',
        };
    },
    onLoad(options) {
        this.src = options.src ? decodeURIComponent(options.src) : '';
        console.log(this.src, '链接标识');
        setTimeout(() => {
            wx.hideHomeButton(); // 隐藏返回首页图标
        }, 500);
    },
    computed: {
        ...mapState({
            token: state => state.token,
            token3: state => state.token3,
        }),
    },
    methods: {
        handleWebviewMessage(e) {
            console.log(e, 'webview返回方法触发了吗');
            if (e.detail.data === 'backHome') {
                if (this.token && this.token3 == '') {
                    uni.reLaunch({
                        url: '/page/home/<USER>',
                    });
                } else if (this.token == '') {
                    uni.reLaunch({
                        url: '/packages/setting/pages/login/main',
                    });
                } else if (this.token3) {
                    uni.reLaunch({
                        url: '/page/thirdHome/main',
                    });
                }
            }
        },
        handleWebviewMessage(event) {
            // 处理 web-view 组件的 message 事件
            console.log('接收到的 web-view 消息：', event.detail);
        },
    },
};
</script>
