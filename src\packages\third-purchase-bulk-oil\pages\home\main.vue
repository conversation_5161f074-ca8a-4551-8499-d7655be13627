<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf bg-F7F7FB">
            <zj-navbar :height="44" title="预约列表"></zj-navbar>
            <div class="f-1 mh-0 padding-16">
                <div v-if="Object.keys(subscribeInfo).length === 0" class="picture">
                    <img :src="noPicture" alt="" />
                    <div class="te-center color-999 weight-400 font-12">您目前没有任何预约</div>
                </div>
                <div v-else :class="containerClass">
                    <div class="fl-row wrap padding-16 fl-jus-bet" @click="appointmentDetails">
                        <div class="wrapLeft fl-column fl-jus-bet">
                            <div class="weight-500 color-333 font-14 te-left titleLeft">{{ subscribeInfo.stationName }}</div>
                            <div class="weight-400 color-999 font-12 te-left">
                                <div class="text">购油品种 {{ subscribeInfo.productName }}</div>
                                <div>有效期至 {{ subscribeInfo.validTime }}</div>
                            </div>
                        </div>
                        <div class="fl-column te-right fl-jus-bet">
                            <div :class="statusClass" class="font-14 weight-400">{{ statusText }}</div>
                            <div :class="cancelButtonClass" class="borderStyle fl-row fl-al-jus-cen" @click.stop="cancelReservation">
                                <div class="te-center font-13">{{ cancelButtonText }}</div>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="appointmentStatus === 3"
                        :class="borderClass"
                        class="bg-288-E64F22 te-center goOilBtn color-fff weight-500 font-18"
                        @click="goRefuel"
                        >去加油
                    </div>
                </div>
            </div>
            <div class="btn_div p-LR-16 fl-jus-bet">
                <div class="finish_verification btn-plain color-E64F22 font-16" @click="viewAppointmentRecords">查看预约记录</div>
                <div
                    :class="payingFlag ? 'btnColor' : 'bg-opacity-288'"
                    class="finish_verification color-fff font-16"
                    @click="reserveBulkOil"
                    >预约</div
                >
            </div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import { bulkOilRegisterList, bulkOilRegisterCancel } from '../../../../s-kit/js/v3-http/https3/bulkOil/index';
export default {
    mixins: [publicMixinsApi],
    name: 'third-purchase-bulk-oil',
    data() {
        return {
            // 油站预约数据
            subscribeInfo: {
                // stationName: '31321',
                // productNo: '333',
                // validTime: '323242',
                // applyStatus: 2,
            },
            // 在数据图片
            noPicture: require('../../images/notBooked.png'),
            // 埋点参数
            refer: '',
            pageNum: 1,
            pageSize: 10,
            // 预约信息状态
            appointmentStatus: 1,
            payingFlag: false,
        };
    },
    onShow() {
        console.log(this.refreshBulkOilListFlag, '是否需要刷新');
        if (this.refreshBulkOilListFlag) {
            // 获取预约列表
            this.bulkOilReservationList();
        }
    },
    mounted() {},
    onLoad() {
        // 油站列表区分是否是散装油标识
        this.$store.commit('setIsBulkOilStationFlag', 1);
        // 取消预约
        this.cancelReservation = this.$sKit.commonUtil.throttleUtil(this.cancelReservation);
        // 查看预约记录
        this.viewAppointmentRecords = this.$sKit.commonUtil.throttleUtil(this.viewAppointmentRecords);
        // 查看详情
        this.appointmentDetails = this.$sKit.commonUtil.throttleUtil(this.appointmentDetails);
        // 预约散装油
        this.reserveBulkOil = this.$sKit.commonUtil.throttleUtil(this.reserveBulkOil);
        // 去加油
        this.goRefuel = this.$sKit.commonUtil.throttleUtil(this.goRefuel);
        // 获取油站列表
        this.$store.dispatch('initLocationV3_app', {
            callback: () => {
                // 获取预约列表
                this.bulkOilReservationList();
            },
        });
        // 获取预约散装油状态
        // this.getTheStatusOfBulkOilReservation()
    },
    computed: {
        containerClass() {
            return this.appointmentStatus !== 3 ? 'bg-fff applyBorder' : 'bg-288-FF3E00 adoptBorderTop';
        },
        statusClass() {
            switch (this.appointmentStatus) {
                case 2:
                    return 'color-F7B500';
                case 3:
                    return 'color-118920';
                case 4:
                    return 'color-E02020';
                default:
                    return '';
            }
        },
        statusText() {
            switch (this.appointmentStatus) {
                case 2:
                    return '申请中';
                case 3:
                    return '已通过';
                case 4:
                    return '未通过';
                default:
                    return '';
            }
        },
        cancelButtonClass() {
            return this.appointmentStatus === 4 ? 'bg-EDEDF5 color-666' : 'border-FF6B2C color-FF6B2C';
        },
        cancelButtonText() {
            return this.appointmentStatus === 4 ? '已拒绝' : '取消预约';
        },
        borderClass() {
            return this.appointmentStatus === 1 ? 'applyBorder' : 'adoptBorderBottom';
        },
        ...mapState({
            pullDownToSelectAnArray: state => state.bulkOil.pullDownToSelectAnArray,
            refreshBulkOilListFlag: state => state.bulkOil.refreshBulkOilListFlag,
        }),
    },
    methods: {
        /**
         * @description 获取预约列表
         * @param {Integer} pageNum 当前页码（必填）
         * @param {Integer} pageSize 每页大小（必填）
         * @param {Array<Number>} [applyStatus] 申请状态列表（可选），传空值表示查询所有状态
         *   1-创建
         *   2-审核中
         *   3-审核通过
         *   4-审核拒绝
         *   5-已提油
         *   6-拒绝提油
         *   7-已失效
         *   8-已取消
         * @returns {*}
         */
        async bulkOilReservationList() {
            let params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                applyStatus: [1, 2, 3],
            };
            const value = uni.getStorageSync('bulkParamFlag');
            // 散装油查询四种状态标识，值为false不查审核拒绝的数据
            if (value) {
                delete params.applyStatus;
                params.applyStatus = [1, 2, 3];
            }
            let res = await bulkOilRegisterList(params);
            await this.handleResponse(res);
        },
        async handleResponse(res) {
            // 检查响应是否成功且包含有效数据
            if (res.success) {
                if (res.data?.rows?.length > 0) {
                    // 关闭刷新散装油列表的标志
                    await this.$store.commit('setRefreshBulkOilList', false);
                    // 获取第一条订阅信息
                    this.subscribeInfo = res.data.rows[0];
                    // 获取申请状态
                    const applyStatus = this.subscribeInfo.applyStatus;

                    // 根据申请状态和订阅信息是否存在进行不同处理
                    if (applyStatus === 4 && this.hasValidSubscribeInfo()) {
                        this.handleApplyStatus4();
                    } else if (this.hasValidSubscribeInfo() && applyStatus !== 4) {
                        this.handleApplyStatusNot4();
                    }

                    // 记录申请单状态
                    this.appointmentStatus = applyStatus;
                    console.log('申请单数据', this.subscribeInfo);

                    // 当申请单状态为已通过且有可用油站列表时，进行油站匹配
                    if (this.subscribeInfo.stationCode && applyStatus === 3 && this.pullDownToSelectAnArray.length > 0) {
                        this.matchStation();
                    }
                } else {
                    // 设置支付标志为 true
                    this.payingFlag = true;
                    this.subscribeInfo = {};
                }
            } else {
                // 处理无有效数据的情况
                this.handleNoValidData();
            }
        },

        // 检查订阅信息是否有效
        hasValidSubscribeInfo() {
            return Object.keys(this.subscribeInfo).length > 0;
        },

        // 处理申请状态为 4 的情况(审核拒绝，清空列表，用户可以点击预约按钮)
        handleApplyStatus4() {
            // 设置散装油查询标识，不查审核拒绝的数据
            uni.setStorageSync('bulkParamFlag', 1);
            // 延迟 2 秒清空订数据
            setTimeout(() => {
                this.subscribeInfo = {};
                this.payingFlag = true;
            }, 2000);
        },

        // 处理申请状态不为 4 的情况
        handleApplyStatusNot4() {
            // 设置预约标志为 true
            this.payingFlag = false;
            // 设置散装油查询标识，查所有状态的数据
            uni.removeStorage({
                key: 'bulkParamFlag',
            });
        },

        // 处理请求失败的情况
        handleNoValidData() {
            // 移除散装油查询标识，查所有状态的数据
            uni.removeStorage({
                key: 'bulkParamFlag',
            });
            // 关闭支付标志
            this.payingFlag = false;
            // 清空订阅信息
            this.subscribeInfo = {};
        },

        // 匹配油站信息
        matchStation() {
            // 查找与申请单中油站代码匹配的油站信息
            const station = this.pullDownToSelectAnArray?.find(item => item.orgCode === this.subscribeInfo.stationCode);
            console.log(station, '匹配的油站数据');
            // 存储匹配的油站信息到状态管理
            this.$store.commit('setSelectBulkOilStationList', [station]);
            this.$store.commit('setSelectBulkOilStationObject', station);
        },

        /**
         * @description  : 获取预约散装油状态
         * @return        {*}
         */
        getTheStatusOfBulkOilReservation() {
            this.appointmentStatus = 0;
        },
        /**
         * @description  : 取消预约
         * @return        {*}
         */
        cancelReservation() {
            this.$store.dispatch('zjShowModal', {
                title: '确认取消预约的散装油服务吗？',
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                confirmColor: '#333333',
                success: async res => {
                    if (res.confirm) {
                        this.cancelReserveOrder();
                    } else if (res.cancel) {
                    }
                },
            });
        },
        /**
         * @description  : 取消预约散装油订单
         * @applyId    {String} 预约登记id (必填)
         * @stationCode    {String} 油站编码 (必填)
         * @return        {*}
         */
        async cancelReserveOrder() {
            let params = {
                applyId: this.subscribeInfo.applyId,
                stationCode: this.subscribeInfo.stationCode,
            };
            let res = await bulkOilRegisterCancel(params);
            if (res.success) {
                this.payingFlag = true;
                this.subscribeInfo = {};
                // 设置散装油查询标识，不查审核拒绝的数据
                uni.setStorageSync('bulkParamFlag', 1);
                this.bulkOilReservationList();
                uni.showToast({
                    title: '取消成功',
                    mask: true,
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
        /**
         * @description  : 去加油
         * @return        {*}
         */
        goRefuel() {
            let url = '/packages/third-purchase-bulk-oil/pages/purchaseOfBulkOil/main';
            this.$sKit.layer.useRouter(url, this.subscribeInfo);
        },
        /**
         * @description  : 预约散装油
         * @return        {*}
         */
        reserveBulkOil() {
            if (!this.payingFlag) return;
            let url = '/packages/third-purchase-bulk-oil/pages/addReserveBulkOil/main';
            this.$sKit.layer.useRouter(url);
        },
        /**
         * @description  : 查看预约记录
         * @item    {Object} 当前选中的预约散装油订单信息
         * @return        {*}
         */
        viewAppointmentRecords() {
            let url = '/packages/third-purchase-bulk-oil/pages/bulkOilReservationRecord/main';
            this.$sKit.layer.useRouter(url);
        },
        /**
         * @description  : 预约详情
         * @return        {*}
         */
        appointmentDetails() {
            let url = '/packages/third-purchase-bulk-oil/pages/bulkOilReservationDetail/main';
            this.$sKit.layer.useRouter(url, this.subscribeInfo);
        },
    },
    beforeDestroy() {
        // 油站列表区分是否是散装油标识
        this.$store.commit('setIsBulkOilStationFlag', 0);
    },
};
</script>
<style lang="scss" scoped>
.view {
    .wrap {
        width: 100%;
        height: 200rpx;
        .wrapLeft {
            width: 75%;
        }
        .borderStyle {
            width: 140rpx;
            line-height: 48rpx;
            border-radius: 8rpx;
        }
        .titleLeft {
            white-space: nowrap; /* 文本强制不换行 */
            text-overflow: ellipsis; /* 文本溢出显示省略号 */
            overflow: hidden; /* 溢出的部分隐藏 */
        }
        .text {
            margin-bottom: 6rpx;
        }
    }
    .goOilBtn {
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
    }
    .btnColor {
        background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
    }
    .btn_div {
        margin-top: 20px;
        font-size: 15px;
        display: flex;
        flex-direction: row;
        margin-bottom: 10px;
        .finish_verification {
            width: 48%;
            text-align: center;
            border-radius: 8px;
            height: 44px;
            line-height: 44px;
        }

        .finish_verification2 {
            width: 100%;
            text-align: center;
            border-radius: 8px;
            height: 44px;
            line-height: 44px;
        }
    }
    .picture {
        width: 500rpx;
        height: 514rpx;
        margin: 121rpx auto 0 auto;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .applyBorder {
        border-radius: 16rpx;
    }
    .adoptBorderBottom {
        border-radius: 0 0 16rpx 16rpx;
    }
    .adoptBorderTop {
        border-radius: 16rpx 16rpx 0 0;
    }
}
</style>
