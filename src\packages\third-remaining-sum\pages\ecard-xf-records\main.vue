<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <!-- TODAY -->
        <div class="wallet_details fl-column p-bf bg-F7F7FB">
            <zj-navbar
                :titleSize="36"
                :height="44"
                :titleFontWeight="500"
                title="电子卡消费明细"
                class="zj_navbar"
                :border-bottom="false"
            ></zj-navbar>
            <div class="wallet_details_content fl-column f-1 mh-0">
                <div class="screen_div" @click="selectShow = true">
                    <div class="selectTime_div"
                        >{{ currentTime
                        }}<div class="arrow-down-333" @click="selectShow = true" style="margin-left: 5px; margin-top: -2px"></div
                    ></div>
                </div>
                <div class="f-1 mh-0 dataList_box">
                    <!-- :emptyImage="require('../../images/wallet')" -->
                    <!-- emptyText="暂未查询到昆仑e享卡明细" -->
                    <!-- <zj-data-list
                        ref="dataList"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                        :showEmpty="showEmpty"
                        :emptyImage="require('../../images/kt10qb.png')"
                        emptyText="暂未查询到电子卡消费明细"
                    > -->
                    <div class="content_div" v-if="!showEmpty">
                        <div v-for="(item, index) in detailsList" :key="index" class="item_div" @click="toDetails(item)">
                            <div class="item_left">
                                <div class="left_title">{{ item.consumeName }}</div>
                                <div class="left_time">{{ item.creatTime }}</div>
                            </div>
                            <div class="item_right fl-row font-style" :class="item.amountChange > 0 ? 'color-E64F22' : 'reduce_class'">
                                <div class="right_type" v-if="item.payAmount > 0">+</div>
                                <div class="right_amount">{{ item.payAmount }}</div>
                            </div>
                        </div>
                    </div>
                    <div v-if="showEmpty" class="empty_div">
                        <div>
                            <img src="../../images/kt10qb.png" />
                            <div class="text">暂未查询到电子卡消费明细</div>
                        </div>
                    </div>
                    <!-- </zj-data-list> -->
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
            <SelectDate
                v-if="selectShow"
                @hideShow="hideShow"
                @sureSelectDateTime="selectDateTime"
                :dateYearsOrMonth="dateYearsOrMonth"
                :inDate="inDate"
            ></SelectDate>
        </div>
    </div>
</template>

<script>
import { cardOldConsumeRecordList } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import SelectDate from '../../../../s-kit/components/layout/zj-setSelectDate/zj-setSelectDate.vue';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: { SelectDate },
    name: 'third-transaction-details',
    data() {
        const selectTime = this.getDate({
            format: true,
        });
        return {
            selectId: 0,
            diffHeight: 0,
            detailsList: [],
            pageNum: 1,
            pageSize: 10,
            bottomBounce: false,
            val: 'null',
            showEmpty: false,
            endTimeShow: '',
            startTimeShow: '',
            selectShow: false,
            dateYearsOrMonth: [],
            endTime: '',
            startTime: '',
            currentTime: '',
        };
    },
    mounted() {
        this.getCurrenTime();
        this.dateYearsOrMonth = this.getYearsAndMonths();
        this.selectTypeClick();
    },

    onLoad(option) {},
    methods: {
        // 计算指定年份和月份的结束天数
        getDaysInMonth(year, month) {
            // 月份从0开始，所以要加1
            return new Date(year, month, 0).getDate();
        },
        selectDateTime(time) {
            // yyyy-MM-dd HH:mm:ss
            this.selectShow = false;
            let currentDate = new Date();
            let currentDay = currentDate.getDate();
            let currentHour = currentDate.getHours();
            let currentMinute = currentDate.getMinutes();
            let currentSecond = currentDate.getSeconds();
            let num = this.getDaysInMonth(time.year, time.month);
            this.startTime = time.year + '-' + time.month.toString().padStart(2, '0') + '-' + '01 00:00:00';
            this.endTime =
                time.year + '-' + time.month.toString().padStart(2, '0') + '-' + num.toString().padStart(2, '0') + ' ' + '24:00:00';
            this.currentTime = time.year + '年' + time.month + '月';
            this.selectTypeClick();
            console.log(time, this.startTime, this.endTime, 'time======');
        },
        getYearsAndMonths() {
            // 创建一个表示当前时间的Date对象
            let result = [];
            let arr = [];
            let currentDate = new Date();
            // 获取当前时间的年、月
            let currentYear = currentDate.getFullYear();
            let currentMonth = currentDate.getMonth() + 1; // 月份从0开始，需要加1
            // 将初始时间改为2018年对应的Date对象
            let initialDate = new Date(2018, 0); // 这里月份设为0，代表1月
            // 获取2018年的年、月
            let initialYear = initialDate.getFullYear();
            let initialMonth = initialDate.getMonth() + 1; // 月份从0开始，需要加1
            for (let year = initialYear; year <= currentYear; year++) {
                let startMonth = year === initialYear ? initialMonth : 1;
                let endMonth = year === currentYear ? currentMonth : 12;

                for (let month = startMonth; month <= endMonth; month++) {
                    arr.push({ month: month, year: year });
                }
            }
            console.log(arr, '日期数据');
            return arr;
        },
        /**
         * @description : 获取时间
         * @return        {*}
         */
        getDate(type) {
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            if (type === 'start') {
                month = month - 2;
                if (month <= 0) {
                    month = 12 + month;
                    year = year - 1;
                }
            }
            day = day > 9 ? day : '0' + day;
            month = month > 9 ? month : '0' + month;
            return `${year}年${month}月`;
        },
        //关闭选择时间弹窗
        hideShow() {
            this.selectShow = false;
        },
        getCurrenTime() {
            // 创建一个表示当前时间的 Date 对象
            let currentDate = new Date();

            // 获取当前时间的年、月、日
            let currentYear = currentDate.getFullYear();
            let currentMonth = currentDate.getMonth() + 1;
            let currentDay = currentDate.getDate();
            let currentHour = currentDate.getHours();
            let currentMinute = currentDate.getMinutes();
            let currentSecond = currentDate.getSeconds();
            // 计算一个月以前的时间
            // let oneMonthAgo = new Date(currentYear, currentMonth - 1, currentDay);
            // 获取一个月以前的年、月、日
            // let yearOneMonthAgo = oneMonthAgo.getFullYear();
            // let monthOneMonthAgo = oneMonthAgo.getMonth(); // 月份从0开始，需要加1
            // let dayOneMonthAgo = oneMonthAgo.getDate();

            // 将日期格式化为 yyyy-MM-dd 格式
            // :${minuteOneMonthAgo.toString().padStart(2, '0')}:${secondOneMonthAgo.toString().padStart(2, '0')}
            // let formattedOneMonthAgo = `${yearOneMonthAgo}-${monthOneMonthAgo.toString().padStart(2, '0')}-${dayOneMonthAgo
            //     .toString()
            //     .padStart(2, '0')}`;
            // this.startTimeShow = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${currentDay.toString().padStart(2, '0')}`;
            // this.endTimeShow = formattedOneMonthAgo;
            this.startTime = currentYear + '-' + currentMonth.toString().padStart(2, '0') + '-' + '01 00:00:00';

            this.endTime =
                currentYear +
                '-' +
                currentMonth.toString().padStart(2, '0') +
                '-' +
                currentDay.toString().padStart(2, '0') +
                ' ' +
                currentHour.toString().padStart(2, '0') +
                ':' +
                currentMinute.toString().padStart(2, '0') +
                ':' +
                currentSecond.toString().padStart(2, '0');
            this.currentTime = currentYear + '年' + currentMonth + '月';
        },
        //前往详情页
        toDetails(item) {
            this.$sKit.layer.useRouter(
                '/packages/third-remaining-sum/pages/third-billing-details/main',
                {
                    orderNo: item.orderNo,
                    amount: item.amountChange,
                },
                'navigateTo',
            );
        },
        //加载
        scrolltolower() {
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.selectTypeClick();
            }
        },

        //刷新
        refreshPullDown() {
            this.selectTypeClick(this.selectId, this.val, { isInit: true });
        },
        //筛选
        async selectTypeClick(id, val, { isInit = false } = {}) {
            // if (isInit) {
            //     this.pageNum = 1;
            //     this.detailsList = [];
            // }
            // if (this.selectId != id) {
            //     this.selectId = id;
            //     this.pageNum = 1;
            //     this.detailsList = [];
            // }
            // this.val = val;
            // 获取钱包明细列表
            let params = {
                endTime: this.endTime,
                startTime: this.startTime,
            };
            let res = await cardOldConsumeRecordList(params);
            // this.$refs.dataList.stopRefresh();
            console.log(res, 'res========');
            if (res && res.success) {
                // return
                this.detailsList = res.data.rows || [];
                // this.pageNum++;
                // if (this.pageSize > res.data.rows.length) {
                //     this.$refs.dataList.loadStatus = 'nomore'; // 没有更多了
                // } else {
                //     this.$refs.dataList.loadStatus = 'contentdown'; // 上拉加载更多
                // }
            }
            if (res.data.rows.length <= 0) {
                this.showEmpty = true; // 显示暂无数据图片
            } else {
                this.showEmpty = false; // 显示暂无数据图片
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background: $page-base-bg-color;
        .select_div {
            display: flex;
            width: 100%;
            height: 44px;
            background: #fff;
            flex-direction: row;
            justify-content: space-between;

            .select_item {
                width: 33%;
                text-align: center;

                div {
                    font-size: 14px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 44px;
                }
            }

            .selected_class {
                div {
                    font-size: 16px;
                    font-weight: 500;
                    color: #e64f22;
                    line-height: 43px;
                    border-bottom: 2px solid #e64f22;
                    display: inline-block;
                }
            }
        }

        .content_div {
            padding: 0 15px;
            position: relative;

            .item_div {
                display: flex;
                background: #fff;
                border-radius: 8px;
                margin-top: 10px;
                padding: 15px;
                width: 100%;
                align-items: center;
                flex-direction: row;
                justify-content: space-between;

                .item_left {
                    width: 70%;
                    display: flex;
                    flex-direction: column;

                    .left_title {
                        font-size: 14px;
                        font-weight: 400;
                        color: #333333;
                        line-height: 20px;
                    }

                    .left_time {
                        font-size: 12px;
                        font-weight: 400;
                        color: #999999;
                        line-height: 17px;
                        margin-top: 9px;
                    }
                }

                .item_right {
                    justify-content: right;

                    .right_type {
                        font-size: 16px;
                    }

                    .right_amount {
                        font-size: 16px;
                    }
                }

                .reduce_class {
                    color: #333;
                }
            }
        }
    }

    .btn_box {
        position: absolute;
        bottom: -87px;
        width: 343px;
        padding-bottom: 15px;

        .btn {
            width: 343px;
            // margin: 0 auto;
            height: 44px;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            line-height: 44px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            border-radius: 8px;
        }
    }

    .btn_box_bottom {
        padding-bottom: 15px;

        .btn {
            width: 343px;
            margin: 0 auto;
            height: 44px;
            background: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            line-height: 44px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            border-radius: 8px;
        }
    }
}
.screen_div {
    width: 100%;
    display: flex;
    padding: 10px 12px;
    align-items: center;
    background: #fff;
    justify-content: space-between;
    .time_div {
        width: 45%;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: #fff;
        font-size: 14px;
    }
}
.empty_div {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    height: calc(100% - 50px);
    img {
        width: 250px;
        height: 225px;
    }
}
.text {
    text-align: center;
    margin-top: 25rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
    line-height: 40rpx;
    display: block;
}
.selectTime_div {
    display: flex;
    align-items: center;
}
</style>
