<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div style="height: 100%; overflow: hidden">
            <div class="backdrop">
                <img v-if="current == 0" src="../../images/yingbigbg.png" alt="" />
                <img v-else-if="current == 1" src="../../images/huangbigbg.png" alt="" />
                <img v-else src="../../images/baibigbg.png" alt="" />
                <zj-navbar
                    :height="44"
                    title="会员权益"
                    :background="{ background: 'rgba(255, 255, 255, 0)' }"
                    :borderBottom="false"
                    :titleColor="'#FFF'"
                    :titleFontWeight="bold"
                    :titleSize="36"
                ></zj-navbar>
                <div class="swiper_card">
                    <swiper class="swiper" :autoplay="false" :duration="500" @change="change" :current="membershipLevel">
                        <swiper-item v-for="(item, index) in gradeList" :key="index">
                            <view class="swiper-item" :class="{ huangjin: index == 1, baijin: index == 2, zhuanshi: index == 3 }">
                                <img class="swiper-item-backdrop" v-if="index == 0" src="../../images/yingbg.png" alt="" />
                                <img class="swiper-item-backdrop" v-else-if="index == 1" src="../../images/huangbg.png" alt="" />
                                <img class="swiper-item-backdrop" v-else-if="index == 2" src="../../images/baibg.png" alt="" />
                                <img class="swiper-item-backdrop" v-else src="../../images/zhuanshi.png" alt="" />
                                <!-- #ifdef MP-WEIXIN -->
                                <div
                                    v-if="membershipLevel <= index"
                                    class="member_status"
                                    :class="{ huangjin: index == 1, baijin: index == 2, zhuanshi: index == 3 }"
                                    :style="{ marginTop: index == 2 || index == 3 ? '2px' : '1px' }"
                                >
                                    <div v-if="membershipLevel < index"><img src="../../images/suo.png" alt="" /></div>
                                    <div>{{ membershipLevel >= index ? (membershipLevel == 3 ? '已达成' : '当前等级') : '未达成' }} </div>
                                </div>
                                <!-- #endif -->
                                <!-- #ifndef MP-WEIXIN -->
                                <div
                                    v-if="membershipLevel <= index"
                                    class="member_status"
                                    :class="{ huangjin: index == 1, baijin: index == 2, zhuanshi: index == 3 }"
                                    :style="{ marginTop: index == 2 || index == 3 ? '1px' : '0px' }"
                                >
                                    <div v-if="membershipLevel < index"><img src="../../images/suo.png" alt="" /></div>
                                    <div>{{ membershipLevel >= index ? (membershipLevel == 3 ? '已达成' : '当前等级') : '未达成' }} </div>
                                </div>
                                <!-- #endif -->
                                <div class="member_name">{{ item.levelName }}</div>
                                <div class="member_time"
                                    >{{ membershipLevel == index ? getCurrentGPdata.levelExpireTime + '到期' : '' }}
                                </div>
                                <div class="member_points">
                                    <div class="current_points"
                                        >当前成长值: <div> {{ getCurrentGPdata.growthPoint }} 成长值</div>
                                    </div>
                                    <div class="future_points" v-if="membershipLevel == index">
                                        <div v-if="membershipLevel == 3"> 已是 <div>最高等级</div> </div>
                                        <div v-else>
                                            再积
                                            <div>
                                                {{
                                                    parseFloat(
                                                        index == gradeList.length - 1 ? item.growthPoint : gradeList[index + 1].growthPoint,
                                                    ) - getCurrentGPdata.growthPoint
                                                }}
                                            </div>
                                            升级至
                                            <div>
                                                {{ index == gradeList.length - 1 ? item.levelName : gradeList[index + 1].levelName }}</div
                                            >
                                        </div>
                                    </div>
                                    <div class="future_points" v-else-if="membershipLevel < index + 1" style="margin-left: 12px"
                                        >{{ ' 成长值达到 ' + item.growthPoint + ' 可升级 ' }}
                                    </div>

                                    <div class="future_points" v-else style="margin-left: 12px">已高于该等级</div>
                                </div>
                                <div class="card-line" v-if="membershipLevel == index">
                                    <div
                                        class="line"
                                        :style="{
                                            width:
                                                (parseFloat(getCurrentGPdata.growthPoint) /
                                                    parseFloat(
                                                        index == gradeList.length - 1 ? item.growthPoint : gradeList[index + 1].growthPoint,
                                                    )) *
                                                    100 +
                                                '%',
                                        }"
                                    ></div>
                                </div>
                                <div class="member_address">
                                    <div class="have_address" v-if="localInfo">{{ localInfo }}</div>
                                    <div class="no_address" v-else @click="goAdrress">请完善常用地></div>
                                </div>
                            </view>
                        </swiper-item>
                    </swiper>
                </div>
            </div>
            <div class="equity">
                <div class="equity_title">
                    <img src="../../images/xing.png" alt="" />
                    <div>权益</div>
                    您的专属会员权益~
                </div>
                <div class="equity_bottom">
                    <div class="equity_list">
                        <div class="equity_list_item" v-for="(item, index) in iconArray" :key="index" @click="iconArrayClick(item)">
                            <div class="pic" v-if="item.flag == 1">
                                <img v-if="current == 0" src="../../images/group_icon1.png" alt />
                                <img v-if="current == 1" src="../../images/group_icon2.png" alt />
                                <img v-if="current == 2" src="../../images/icon-group3.png" alt />
                                <img v-if="current == 3" src="../../images/icon-group4.png" alt />
                            </div>
                            <div class="pic" v-else>
                                <img :src="item.frontendImgUrl" alt />
                            </div>
                            <div
                                class="equity_list_item_title ellipsis"
                                :style="{
                                    color: current == 0 ? '#163A5B' : current == 1 ? '#5B4416' : current == 2 ? '#16215B' : '#16215B',
                                }"
                            >
                                {{ item.flag == 1 ? item.groupName : item.frontendTitle }}</div
                            >
                            <div
                                class="equity_list_item_connent ellipsis"
                                :style="{
                                    color: current == 0 ? '#8B9CA6' : current == 1 ? '#A6A08B' : current == 2 ? '#8B93A6' : '#8B93A6',
                                }"
                            >
                                {{
                                    item.flag == 1
                                        ? item.groupDescribe
                                            ? item.groupDescribe
                                            : ''
                                        : item.frontendSubTitle
                                        ? item.frontendSubTitle
                                        : ''
                                }}</div
                            >
                        </div>
                    </div>
                    <div class="equity_detail" v-if="iconShow">
                        <div
                            class="tips"
                            :style="{
                                background: current == 0 ? '#E7E8ED' : current == 1 ? '#FFF7DC' : current == 2 ? '#E0EAF4' : '#D2CCF8',
                            }"
                        >
                            结算时会根据您的支付金额自动匹配最优优惠
                        </div>
                        <div class="equity_detail_title">{{ iconObj.flag == 1 ? iconObj.groupName : iconObj.frontendTitle }} </div>
                        <div class="equity_detail_content">
                            <div class="equity_detail_content_content"
                                >权益内容：{{
                                    iconObj.flag == 1
                                        ? iconObj.groupDescribe
                                            ? iconObj.groupDescribe
                                            : ''
                                        : iconObj.frontendSubTitle
                                        ? iconObj.frontendSubTitle
                                        : ''
                                }}</div
                            >
                            <div class="equity_detail_content_time" v-if="iconObj.flag == 1"
                                >有效期：{{ iconObj.effectEndDate ? iconObj.effectEndDate : '' }}
                            </div>
                        </div>
                    </div>
                    <div class="growth_value" v-else>
                        <div
                            class="tips"
                            :style="{
                                background: current == 0 ? '#E7E8ED' : current == 1 ? '#FFF7DC' : current == 2 ? '#E0EAF4' : '#D2CCF8',
                            }"
                        >
                            <img src="../../images/zuyi.png" alt="" />
                            <div>温馨提示：结算时会根据您的实付金额自动匹配最优优惠权益</div>
                        </div>
                        <div class="growth_value_title">成长值攻略</div>
                        <div class="growth_growUpL">
                            <div v-for="(item, index) in growUpL" :key="index">
                                <div class="growth_value_content_title">{{ index + 1 }}、{{ item.ruleName }}</div>
                                <!-- <div class="growth_value_content_content" >{{ removeHTMLTag(item.content) }}</div> -->
                                <u-parse :html="item.content" :show-with-animation="true"></u-parse>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import {
    userInterestsGradeList,
    userInterests,
    userBasicGetCurrentGP,
    userBasicInfoQuery,
    userGroupInterestsGroup,
    userMemberSystemInfo,
} from '../../../../s-kit/js/v3-http/https3/classInterest/index';
// import { userAgreement } from "../../../../s-kit/js/v3-http/https3/user"
import { mapGetters } from 'vuex';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // backdropImg: '../../images/yingbigbg.png'
            current: 0,
            gradeList: [], //会员等级列表
            //interestsData：会员对应权益
            interestsData: {},
            //getCurrentGPdata：会员成长值
            getCurrentGPdata: {},
            levelRuleItemId: 1,
            membershipLevel: 0,
            usedPlace: '',
            localInfo: null,
            interestsGroup: null, //群组权益
            iconArray: [],
            iconShow: false,
            iconObj: {},
            growUpL: [],
            // url:''
        };
    },
    computed: {
        ...mapGetters(['memberBaseInfo']),
    },
    async mounted() {
        await this.$store.dispatch('memberBaseInfoAction');
        this.levelRuleItemId = this.memberBaseInfo.levelRuleItemId || '';
        this.usedPlace = this.memberBaseInfo.usedPlace || '';
        this.getData();
    },
    methods: {
        // 获取用户基本信息
        async getData() {
            let res = await userBasicGetCurrentGP(); //获取用户成长值，到期时间
            if (res && res.success) {
                this.getCurrentGPdata = res.data;
            }
            this.getuserGroupInterestsGroup(); //获取群组权益
            this.getuserMemberSystemInfo(); //会员体系简介--成长值攻略
            this.getuserInterestsGradeList(); //会员等级列表
            this.getuserBasicInfoQuery(); //查询用户详细信息--获取用户常用地
        },
        //查询用户详细信息--获取用户常用地
        async getuserBasicInfoQuery() {
            let res = await userBasicInfoQuery();
            if (res && res.success) {
                this.localInfo = res.data.address;
            }
        },
        //获取群组权益
        async getuserGroupInterestsGroup() {
            let res = await userGroupInterestsGroup();
            if (res && res.success) {
                res.data.forEach(i => {
                    i.flag = 1;
                });
                this.interestsGroup = res.data;
            }
        },
        //会员体系简介--成长值攻略
        async getuserMemberSystemInfo() {
            let res = await userMemberSystemInfo();
            if (res && res.success) {
                this.growUpL = res.data;
            }
        },
        //会员等级列表
        async getuserInterestsGradeList() {
            let res = await userInterestsGradeList();
            if (res && res.success) {
                this.gradeList = res.data;
                this.gradeList.forEach((item, idx) => {
                    if (this.levelRuleItemId == item.id) {
                        this.membershipLevel = idx;
                        this.getInterests(this.gradeList[this.membershipLevel]);
                        return;
                    }
                });
            }
        },
        //会员等级所对应的权益
        async getInterests(item) {
            if (!item.interests || item.interests.length === 0) {
                console.log(this.usedPlace);
                let res = await userInterests({ levelRuleDetailId: item.id, usedPlace: this.usedPlace });
                if (res && res.success) {
                    res.data.forEach(i => {
                        i.flag = 2;
                    });
                    this.$set(item, 'interests', res.data);
                    this.interestsData = item.interests;
                    this.mergedArray();
                }
            } else {
                this.interestsData = item.interests;
                this.mergedArray();
            }
        },
        //跳转至常用地维护页面
        goAdrress() {
            let url = '/packages/third-electronic-wallet/pages/wallet-select-area/main';
            let params = { flag: 'selectArea' };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        //切换会员等级
        change(obj) {
            this.current = obj.detail.current;
            this.iconShow = false;
            this.getInterests(this.gradeList[this.current]);
        },
        //切换会员等级切换对应权益
        mergedArray() {
            this.iconArray = this.interestsGroup.concat(this.interestsData);
        },
        //点击对应权益，下方成长值攻略显示对应权益
        iconArrayClick(item) {
            this.iconObj = item;
            this.iconShow = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.backdrop {
    background: #000;
    position: relative;
    width: 375px;
    height: 317px;

    img {
        position: absolute;
        top: 0;
        left: 0;
        width: 375px;
        height: 317px;
        z-index: 1;
    }

    .swiper_card {
        position: absolute;
        z-index: 2;
        width: 337px;
        height: 159px;
        top: 93px;
        left: 19px;
        border-radius: 8px;
        overflow: hidden;

        .swiper {
            width: 100%;
            height: 100%;

            .swiper-item {
                width: 95%;
                height: 100%;
                margin: 0 auto;
                position: relative;

                .swiper-item-backdrop {
                    position: absolute;
                    left: 0;
                    top: 0;
                    z-index: 1;
                    width: 100%;
                    height: 100%;
                }

                .member_status {
                    color: #fff;
                    font-size: 9px;
                    width: 54px;
                    height: 16px;
                    line-height: 16px;
                    font-weight: 500;
                    color: #ffffff;
                    background: #9da3bb;
                    position: absolute;
                    top: 11px;
                    left: 0;
                    z-index: 2;
                    border-radius: 8px 0 8px 0;
                    text-align: center;
                    display: flex;
                    align-items: center;

                    img {
                        margin-top: 9rpx;
                        margin-left: 9px;
                        // margin-right: 9px;
                        width: 7px;
                        height: 8px;
                    }

                    div {
                        height: 16px;
                        line-height: 16px;
                        margin-top: 1px;
                        margin-left: 9px;
                    }
                }

                .member_name {
                    position: absolute;
                    z-index: 2;
                    top: 41px;
                    left: 22px;
                    background-image: linear-gradient(135deg, #747b86 0%, #a6adc6 38%, #747b86 72%, #a6adc6 100%);
                    -webkit-background-clip: text;
                    color: transparent;
                    font-size: 18px;
                    font-weight: bold;
                }

                .member_time {
                    position: absolute;
                    z-index: 2;
                    left: 22px;
                    top: 69px;
                    font-size: 10px;
                    font-weight: 500;
                    color: #888d9b;
                    line-height: 12px;
                }

                .member_points {
                    position: absolute;
                    width: calc(100% - 36px);
                    z-index: 2;
                    left: 18px;
                    top: 101px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 10px;
                    font-weight: 500;
                    color: #888d9b;

                    .current_points {
                        div {
                            display: inline;
                            color: #79808c;
                        }
                    }

                    .future_points {
                        div {
                            display: inline;
                            color: #79808c;
                        }
                    }
                }

                .card-line {
                    position: absolute;
                    width: calc(100% - 36px);
                    z-index: 2;
                    height: 3px;
                    background: #fff;
                    top: 123px;
                    left: 18px;
                    border-radius: 1px;
                    overflow: hidden;

                    .line {
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 3px;
                        background: linear-gradient(270deg, #8a94b5 0%, #c8cdde 100%);
                    }
                }

                .member_address {
                    position: absolute;
                    z-index: 2;
                    // left: 259px;
                    text-align: right;
                    top: 130px;
                    font-size: 10px;
                    font-weight: 500;
                    color: #79808c;
                    width: 100%;
                    right: 20px;
                }
            }

            .huangjin {
                .member_status {
                    background: #cb8010;
                }

                .member_name {
                    background-image: linear-gradient(135deg, #c5781e 0%, #d9a43d 38%, #c5781e 72%, #d9a43d 100%);
                }

                .member_time {
                    color: #a3742e;
                }

                .member_points {
                    color: #b67f40;

                    .current_points {
                        div {
                            color: #a3742e;
                        }
                    }

                    .future_points {
                        div {
                            color: #a3742e;
                        }
                    }
                }

                .card-line {
                    .line {
                        background: linear-gradient(270deg, #f4b438 0%, #fec85f 100%);
                    }
                }

                .member_address {
                    color: #a3742e;
                }
            }

            .baijin {
                .member_status {
                    background: #a9b7e9;
                }

                .member_name {
                    background-image: linear-gradient(135deg, #444bf7 0%, #86c5fe 38%, #4153f7 72%, #84defb 100%);
                }

                .member_time {
                    color: #7792f2;
                }

                .member_points {
                    color: #7792f2;

                    .current_points {
                        div {
                            color: #606ff4;
                        }
                    }

                    .future_points {
                        div {
                            color: #606ff4;
                        }
                    }
                }

                .card-line {
                    .line {
                        background: linear-gradient(270deg, #9086ff 0%, #90d1ff 100%);
                    }
                }

                .member_address {
                    color: #606ff4;
                }
            }

            .zhuanshi {
                .member_status {
                    background: #aa95df;
                }

                .member_name {
                    background-image: linear-gradient(135deg, #9444f7 0%, #be86fe 38%, #9f41f7 73%, #be84fb 100%);
                }

                .member_time {
                    color: #8651d8;
                }

                .member_points {
                    color: #9677f2;

                    .current_points {
                        div {
                            color: #4f26c5;
                        }
                    }

                    .future_points {
                        div {
                            color: #4f26c5;
                        }
                    }
                }

                .card-line {
                    .line {
                        background: linear-gradient(270deg, #c8c4ff 0%, #b890ff 100%);
                    }
                }

                .member_address {
                    color: #4f26c5;
                }
            }
        }
    }
}

.equity {
    position: relative;
    top: -61px;
    z-index: 3;
    background: #fff;
    padding: 16px;
    height: calc(100vh - 256px);

    display: flex;
    flex-direction: column;

    .equity_title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 400;
        color: #999999;

        img {
            width: 15px;
            height: 16px;
            margin-right: 11px;
        }

        div {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            margin-right: 6px;
        }
    }

    .equity_bottom {
        flex: 1;
        overflow: hidden;
        display: flex;
        min-height: 0;
        flex-direction: column;
    }

    .equity_list {
        display: flex;
        flex-wrap: wrap;

        .equity_list_item {
            width: 25%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 8px;

            .pic {
                width: 39px;
                height: 39px;
                margin-bottom: 8px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .equity_list_item_title {
                width: 100%;
                font-size: 12px;
                font-weight: bold;
                color: #5b4416;
                margin-bottom: 2px;
                text-align: center;
            }

            .equity_list_item_connent {
                width: 100%;
                font-size: 10px;
                font-weight: 400;
                color: #a6a08b;
                text-align: center;
            }
        }
    }

    .equity_detail {
        flex: 1;
        min-height: 0;

        .tips {
            // width: 344px;
            height: 33px;
            // background: #FFF7DC;
            border-radius: 0px 0px 2px 2px;
            line-height: 33px;
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            color: #333333;
            margin-bottom: 12px;
        }

        .equity_detail_title {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 10px;
        }

        .equity_detail_content {
            padding: 12px;
            font-size: 12px;
            font-weight: 400;
            color: #333333;
            background: #f7f7fb;
            border-radius: 8px;

            .equity_detail_content_content {
                padding-bottom: 8px;
            }
        }
    }

    .growth_value {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;

        .tips {
            height: 25px;
            background: #ffecc6;
            border-radius: 8px;
            line-height: 25px;
            margin-left: 8px;
            font-size: 10px;
            font-weight: 400;
            color: #333333;
            margin-bottom: 16px;
            padding-left: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;

            img {
                width: 10px;
                height: 10px;
                margin-right: 6px;
            }
        }

        .growth_growUpL {
            flex: 1;
            min-height: 0;
            overflow: auto;
            padding-bottom: 15px;
        }

        .growth_value_title {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
        }

        .growth_value_content_title {
            font-size: 12px;
            font-weight: bold;
            color: #333333;
            margin-top: 10px;
            margin-bottom: 8px;
        }

        .growth_value_content_content {
            font-size: 10px;
            font-weight: 400;
            color: #666666;
            line-height: 14px;
        }

        .intro {
            font-size: 10px;
            font-weight: bold;
            color: #e64f22;
            margin-top: 10px;
        }
    }
}
</style>
