<template>
    <div class="remaining_div fl-column bg-fff">
        <div class="remaining_div_top">
            <div class="card_bg">
                <img class="card_bg_img" :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt />
                <!-- <img class="card_bg_img" v-else src="../../images/card-default.png" alt /> -->
                <img class="mask_img" src="./images/mask2.png" />
            </div>
            <zj-navbar
                :background="{ background: 'rgba(255, 255, 255, 0)' }"
                back-icon-color="#fff"
                titleColor="#fff"
                title="我的钱包"
                :border-bottom="false"
                v-if="pageType == 'page'"
            >
                <!-- #ifdef MP-ALIPAY || H5 -->
                <img
                    @click="backThirdHome"
                    v-if="showBackHomeIcon"
                    class="title_img"
                    slot="title_img"
                    src="./images/backHomeIcon.png"
                    alt=""
                />
                <!-- #endif -->
            </zj-navbar>
            <div class="state-view" :style="{ paddingTop: 10 + Number(topNavbarHeight) + 'px' }" v-else></div>
        </div>
        <div class="scroll_div" :style="{ height: Number(topNavbarHeight) + 'px' }"></div>
        <div class="f-1 fl-column mh-0">
            <zj-pull-down-refresh
                class="mh-0"
                style="z-index: 10; height: 100%"
                @refreshPullDown="refreshPullDown"
                ref="pullDownRefreshRef"
                :refresherDisable="!isLogin"
            >
                <div class="content_div fl-column">
                    <div class="card_top">
                        <div class="card_div">
                            <div class="card_div_top">
                                <img class="card_img" :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt />
                                <div
                                    v-if="walletInfo.ecardNo && isLogin"
                                    class="cardInfo fl-row fl-jus-bet fl-al-cen cardNum font-16 weight-bold color-fff"
                                    >{{ $sKit.layer.splitFourString(walletInfo.ecardNo) }}</div
                                >
                                <div
                                    v-else-if="!isLogin"
                                    @click="toLoginPage"
                                    class="cardInfo fl-row fl-jus-bet fl-al-cen cardNum font-16 weight-bold color-fff"
                                    >请先登录/注册</div
                                >
                            </div>

                            <div class="card_info border_bottom bg-fff">
                                <!-- <div class="card_info_top fl-row fl-jus-bet fl-al-cen">
                                        <div class="card_name font-18 color-333 weight-bold fl-row"
                                            >昆仑e享卡
                                            <div class="text color-666 font-12">(电子卡)</div>
                                        </div>
                                    </div> -->
                                <!-- #ifndef MP-TOUTIAO -->
                                <div class="card_info_bot fl-row fl-al-cen fl-jus-bet">
                                    <div class="fl-column fl-wrap">
                                        <div class="font-12 color-999 title">余额(元)</div>
                                        <div class="font-20 color-333 amount font-style"
                                            >{{ isLogin ? walletInfo.walletBalance || 0 : '**' }}
                                        </div>
                                    </div>
                                    <div class="fl-column fl-wrap freeze_div">
                                        <div class="frozen_amount font-12 color-999 fl-row">冻结金额(元)</div>
                                        <div class="font-20 color-333 amount font-style"
                                            >{{ isLogin ? walletInfo.freezeBalance || 0 : '**' }}
                                        </div>
                                    </div>

                                    <div class="recharge_btn font-16" @click="rechargeClick()">充值</div>
                                </div>
                                <!-- #endif -->
                                <!-- #ifdef MP-TOUTIAO -->
                                <div class="card_info_bot fl-row">
                                    <div class="fl-column fl-wrap">
                                        <div class="font-12 color-999 title">余额(元)</div>
                                        <div class="font-20 color-333 amount font-style">暂未开通 </div>
                                    </div>
                                    <div class="fl-column fl-wrap freeze_div">
                                        <div class="frozen_amount font-12 color-999 fl-row">冻结金额(元)</div>
                                        <div class="font-20 color-333 amount font-style"></div>
                                    </div>
                                </div>
                                <!-- #endif -->

                                <div class="card_top_bot border_bottom fl-row fl-al-cen fl-jus-bet bg-fff">
                                  <!-- TODO @version? -->
                                  <div v-if="0" class="item_div fl-al-cen fl-column" @click.stop="toDetailPage(2)">
                                      <div class="oil_card_div">
                                          <img class="card_icon_img" src="./images/icon-oil-card.png" alt />
                                          <!-- #ifndef H5-CLOUD -->
                                            <img class="recharge_img" @click.stop="toDetailPage(6)" src="./images/torecharge.png" alt />
                                            <!-- #endif -->
                                        </div>
                                        <div class="title font-15 color-666">实体卡</div>
                                        <div class="val font-20 color-333 font-style">{{
                                            isLogin ? memberAccountInfo.fuelCardCount || 0 : '**'
                                        }}</div>
                                    </div>
                                    <div class="item_div fl-al-cen fl-column" @click="toDetailPage(1)">
                                        <img class="card_icon_img" src="./images/icon-coupon.png" alt />
                                        <div class="title font-15 color-666">优惠券</div>
                                        <div class="val font-20 color-333 font-style">{{
                                            isLogin ? memberAccountInfo.couponsCount || 0 : '**'
                                        }}</div>
                                    </div>

                                    <!-- <div class="item_div fl-al-cen fl-column" @click="toDetailPage(3)">
                                        <img class="card_icon_img" src="./images/icon-coin.png" alt />
                                        <div class="title font-15 color-666">能源币</div>
                                        <div class="val font-20 color-333 font-style">{{ memberAccountInfo.ptsAvailableAmount || 0 }}</div>
                                    </div> -->
                                    <div v-if="0" class="item_div border_bottom fl-al-cen fl-column" @click="toDetailPage(4)">
                                        <img class="card_icon_img" src="./images/icon-point.png" alt />
                                        <div class="title font-15 color-666">积分</div>
                                        <div class="val font-20 color-333 font-style">{{
                                            isLogin ? memberAccountInfo.loyaltyPtsAvailableAmount || 0 : '**'
                                        }}</div>
                                    </div>
                                    <!-- #ifndef MP-TOUTIAO -->
                                    <div v-if="0" class="item_div border_bottom fl-al-cen fl-column" @click="toDetailPage(5)">
                                        <img class="card_icon_img" src="./images/icon-Frame.png" alt />
                                        <div class="title font-15 color-666">我的皮肤</div>
                                        <div class="val font-20 color-333 font-style">{{ isLogin ? mySkin || 0 : '**' }}</div>
                                    </div>
                                    <!-- #endif -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card_list fl-column">
                        <div class="fl-row fl-jus-bet fl-al-cen more_div">
                            <div class="font-16 color-666 weight-bold">昆仑e享卡交易明细</div>
                            <div class="data-list-title-left" @click="checkMore()">
                                更多
                                <div class="arrow_down_div">
                                    <img src="./images/arrow_down.png" alt />
                                </div>
                            </div>
                        </div>
                        <div class="bg-fff border-rad-8 info_list">
                            <div class="info_list_scroll" v-if="list.length > 0">
                                <div v-for="(item, index) in list" :key="index" class="fl-row fl-wrap info_div" @click="toDetails(item)">
                                    <div class="info_left fl-column">
                                        <div class="info_text fl-jus-bet fl-row">
                                            <div class="info_title font-15 color-333">{{ item.flowDirection }}</div>
                                            <div
                                                class="info_amount font-15 fl-row font-style"
                                                :style="{ color: item.amountChange > 0 ? '#FF6B2C' : '#333' }"
                                            >
                                                <div v-if="item.amountChange > 0">+</div>
                                                {{ item.amountChange }}
                                            </div>
                                        </div>
                                        <div class="info_time font-12 color-999">{{ item.time }}</div>
                                    </div>
                                </div>
                            </div>
                            <zj-no-data
                                v-if="list.length == 0"
                                :emptyImage="require('./images/kt10qb.png')"
                                emptyText="暂未查询到昆仑e享卡明细"
                            ></zj-no-data>
                        </div>
                    </div>
                    <div class="null_div"></div>
                </div>
            </zj-pull-down-refresh>
        </div>
        <div class="setting_div">
            <div class="setting_btn" @click="toSetting()">设置</div>
        </div>
        <zj-old-account v-if="isTransfer"></zj-old-account>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { detailList } from '../../js/v3-http/https3/wallet.js';
import { cardList } from '../../js/v3-http/https3/oilCard/index.js';
import projectConfig from '../../../../project.config';
export default {
    name: 'third-remaining-sum',
    props: {
        // 页面类型 cube App一级web小程序页面   home 小程序一级页面   page二级页面
        pageType: {
            type: String,
            default: '',
        },
        // 页面来源
        refer: {
            type: String,
            default: '',
        },
        // 页面来源
        myRefer: {
            type: String,
            default: '',
        },
        isLogin: {
            type: Boolean,
            default: true,
        },
        systemBarNum: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            systemInfo: '',
            topNavbarHeight: '',
            statusBarHeight: '',
            list: [],
            pageSize: 6,
            pageNum: 1,
            walletBalance: '', //钱包余额
            freezeBalance: '', //冻结余额
            thirdCardList: '',
            isFirst: true,
            showBackHomeIcon: false,
        };
    },
    created() {
        this.toLoginPage = this.$sKit.commonUtil.throttleUtil(this.toLoginPage);
    },
    mounted() {
        uni.hideLoading();
        // #ifdef MP-ALIPAY
        if (getCurrentPages().length == 1) {
            this.showBackHomeIcon = true;
        }
        // #endif
        // 获取系统信息
        this.bgHrightStyle();
        if (!this.isLogin) {
            console.log(this.walletSkin, 'walletSkin======');
            return;
        }
        // #ifdef MP-WEIXIN
        //微信小程序进入该页面有可能是扫码推荐开卡进来的，要清除一下开卡标识
        this.$store.commit('setLoginThirdFlag', false);
        this.$store.dispatch('setOfficialAccountParams', '');
        // #endif
        const value = uni.getStorageSync('refreshWalletBlanaceFlag');
        uni.removeStorageSync('refreshWalletBlanaceFlag');

        if (value) {
            this.$store.dispatch('getAccountBalanceAction');
            this.initData();
        }

        this.refreshPullDown();
        if (this.myRefer != '') {
            this.$sKit.mpBP.tracker('我的页面', {
                seed: 'minePageBiz',
                pageID: 'walletPage',
                refer: this.myRefer,
                channelID: projectConfig.clientCode,
            });
        }
    },
    computed: {
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            staffStationId: state => state.staffStationId, // 邀请开卡接口传值
            walletStatus: state => state.wallet.walletStatus, // 昆仑e享卡状态
            loginButtonGrayedOut: state => state.thirdLogin.loginButtonGrayedOut,
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        ...mapGetters(['memberAccountInfo', 'walletSkin', 'walletInfo', 'mySkin']),
    },
    methods: {
        //前往交易明细详情页
        toDetails(item) {
            let params = item;
            params.walletDetail = true;
            if (!params.sourceOrderNo) return;
            if (params.sourceChannel == '能源e站-能源商城') return;
            params.orderNo = params.sourceOrderNo;
            if (item.amountChange > 0) {
                this.$sKit.layer.useRouter('/packages/third-order/pages/wallet-order-detail/main', params);
            } else {
                if (params.type == 18) {
                    this.$sKit.layer.useRouter('/packages/third-order/pages/charge-order-detail/main', params);
                }else if(params.tradeMode == 10 || params.tradeMode == 3 || params.type == 10 || params.type == 11){
                    return
                } else {
                    this.$sKit.layer.useRouter('/packages/third-order/pages/order-detail/main', params);
                }
            }
        },
        toLoginPage() {
            if (!this.loginButtonGrayedOut) return;
            let url = '';
            // #ifdef MP-WEIXIN
            url = '/packages/transferAccount/pages/home/<USER>/pages/thirdHome/main&curTabIndex=1';
            // #endif
            // #ifdef MP-ALIPAY
            url = '/pages/union/main?qKey=wallet';
            // #endif
            // #ifdef H5-CLOUD
            url = '/packages/third-cloud-login/pages/home/<USER>';
            // #endif
            this.$sKit.layer.useRouter(url);
        },
        backThirdHome() {
            this.$sKit.layer.backHomeFun();
        },
        //前往设置页
        toSetting() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/wallet-setting/main', {}, 'navigateTo');
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                isEnter: 1,
                freeze: 1,
                walletAddParams: {
                    refer: 'r10',
                },
            });
        },
        //前往其他页面
        toDetailPage(val) {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            let url = '';
            let params = {};
            switch (val) {
                case 1:
                    url = '/packages/third-coupon-module/pages/coupon-list/main';
                    params = { refer: 'r40' };
                    break;

                case 3:
                    url = '/packages/third-hospitality-coin/pages/home/<USER>';
                    break;

                case 4:
                    url = '/packages/third-integral/pages/home/<USER>';
                    break;
                case 5:
                    url = '/packages/third-skin-replacement/pages/home/<USER>';
                    break;

                default:
                    break;
            }
            if (val == 6 || val == 2) {
                this.$sKit.commonUtil.eWalletNormal({
                    nextFun: () => {
                        this.toCardRecharge(val);
                    },
                    freezeReasonArr: [],
                    cancelCallback: res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    isEnter: 1,
                    walletAddParams: {
                        refer: 'r10',
                    },
                });
                return;
            }
            this.$sKit.layer.useRouter(url, params, 'navigateTo');
        },
        //前往油卡页面
        toCardRecharge(val) {
            let url = '';
            let params = {};
            let type = 'navigateTo';
            if (val == 2) {
                if (this.thirdCardList.length == 1) {
                    url = '/packages/third-oil-card/pages/oil-card-management/main';
                    params = this.thirdCardList[0];
                } else {
                    url = '/packages/third-oil-card/pages/my-card/main';
                    params = { refer: 'r44' };
                }
            } else if (val == 6) {
                if (this.thirdCardList.length == 1) {
                    url = '/packages/third-oil-card/pages/oil-card-recharge/main';
                    params = { ...this.thirdCardList[0], refer: 'r10' };
                } else {
                    url = '/packages/third-oil-card/pages/my-card/main';
                    params = { refer: 'r44' };
                }
            }
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 获取油卡列表长度
        getThirdCardList({ isInit = false } = {}) {
            cardList({}, { isload: false, isCustomErr: true }).then((res, err) => {
                if (res && res.success) {
                    this.thirdCardList = res.data.rows;
                }
            });
        },
        // 下拉刷新
        async refreshPullDown(type) {
            if (type == 'onShow' && this.isFirst) return;
            // this.$store.commit('setLoadingStatus', false);
            this.$store.dispatch('basicCouponAction');
            this.$store.dispatch('getMySkin');
            this.$store.dispatch('getCurrentImg', {
                callback: () => {
                    this.$refs.pullDownRefreshRef.stopRefresh();
                },
            });
            this.$refs.pullDownRefreshRef.stopRefresh();
            this.initData();
            this.getThirdCardList();
            let res = await this.$store.dispatch('getAccountBalanceAction');
            if (this.isFirst) {
                this.isFirst = false;
                this.$sKit.mpBP.tracker('充值', {
                    seed: 'rechargeBiz',
                    pageID: 'myWallet', // 页面名
                    refer: this.refer || '', // 来源
                    channelID: projectConfig.clientCode, // C10/C12/C13
                    address: res?.addressName || '',
                });
            }
        },
        /**
         * @description  :  昆仑e享卡消费明细
         * @param         {String} pageSize -页大小
         * @param         {String} pageNum -当前页
         * @param         {String} filter -筛选条件（null-全部；10-充值；1-支出）
         * @return        {*}
         */
        async initData() {
            let res = await detailList({ pageSize: this.pageSize, pageNum: this.pageNum, filter: 'null' }, { isload: false });

            if (res && res.success) {
                if (res.data && res.data.accountInfoList.length > 0) {
                    this.list = res.data.accountInfoList;
                }
            }
            console.log(res, '======detailList');
        },
        // 查看更多
        checkMore() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/third-transaction-details/main', {}, 'navigateTo');
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                walletAddParams: {
                    refer: 'r10',
                },
            });
        },
        // 前往昆仑e享卡充值
        rechargeClick() {
            if (!this.isLogin) {
                this.toLoginPage();
                return;
            }
            this.$sKit.commonUtil.eWalletNormal({
                nextFun: () => {
                    this.$sKit.layer.useRouter(
                        '/packages/third-remaining-sum/pages/third-wallet-recharge/main',
                        { refer: 'r10' },
                        'navigateTo',
                    );
                },
                freezeReasonArr: [9, 10],
                cancelCallback: res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
                walletAddParams: {
                    refer: 'r10',
                },
            });
            // #ifndef MP-MPAAS
            this.$sKit.layer.requestSubscribeMessage();
            // #endif
        },
        // 计算路由高度
        bgHrightStyle() {
            // 获取系统宽高信息，根据公式转化为rpx
            // #ifndef H5-CLOUD
            let systemInfo = uni.getSystemInfoSync();
            // #endif
            // #ifdef MP-ALIPAY
            this.topNavbarHeight = systemInfo.statusBarHeight + systemInfo.titleBarHeight;
            // #endif
            // #ifndef H5-CLOUD
            this.topNavbarHeight = systemInfo.statusBarHeight + 44;
            console.log(systemInfo, this.topNavbarHeight, 'systemInfo=======');
            // #endif
            // #ifdef H5-CLOUD
            let upaskSystem = getApp().globalData.systemBar;
            this.topNavbarHeight = Number(upaskSystem) + Number(44);
            // #endif
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;

    // overflow: hidden;
    .remaining_div_top {
        height: 255px;
        width: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;

        .card_bg {
            height: 255px;
            // overflow: hidden;

            .card_bg_img {
                width: 140%;
                height: 145%;
                -webkit-filter: blur(28.5px);
                filter: blur(28.5px);
                position: absolute;
                top: -50px;
                left: -90px;
            }

            .mask_img {
                width: 100%;
                height: 100.5%;
                position: relative;
                top: 0;
            }
        }

        .title_img {
            width: 17px;
            height: 17px;
            position: absolute;
            top: 24%;
            left: -80%;
        }
    }

    .content_div {
        // position: absolute;
        // height: 100%;
        width: 100%;
        z-index: 10;

        .card_top {
            .card_div {
                padding-top: 15px;

                .card_div_top {
                    width: 100%;
                    // padding: 0 18px;
                    height: 216px;
                    position: relative;
                    display: flex;
                    justify-content: center;
                    .card_img {
                        width: 342px;
                        height: 216px;
                        border-radius: 16px;
                    }

                    .cardInfo {
                        width: 342px;
                        height: 44px;
                        background: rgba(0, 0, 0, 0.2);
                        border-radius: 0px 0px 16px 16px;
                        backdrop-filter: blur(10px);
                        position: absolute;
                        bottom: 0;
                        padding-left: 15px;
                        line-height: 44px;
                    }
                }

                .card_info {
                    padding: 14px 24px 19px 24px;

                    .card_info_top {
                        .card_name {
                            line-height: 25px;
                            align-items: baseline;
                            .text {
                                line-height: 21px;
                                margin-left: 5px;
                                margin-bottom: 2px;
                            }
                        }
                    }

                    .card_info_bot {
                        margin-top: 12px;

                        div {
                            .title {
                                line-height: 21px;
                            }

                            .amount {
                                line-height: 28px;
                                margin-top: 3px;
                            }
                        }

                        .frozen_amount {
                            line-height: 20px;

                            div {
                                margin-left: 8px;
                            }
                        }
                    }
                }
            }
        }

        .card_list {
            padding: 0 12px 10px 12px;
            width: 100%;
            padding-top: 18px;
            background: #f7f7fb;

            .more_div {
                margin-bottom: 8px;
                padding: 0 12px 0 6px;
            }

            .info_list {
                height: 414px;
                margin-bottom: 10px;

                .info_list_scroll {
                    // overflow-y: scroll;
                    height: 100%;
                    padding: 0 12px;

                    .info_div {
                        padding: 32rpx 0 27rpx;
                        border-bottom: 1px solid #eee;

                        .info_left {
                            width: 100%;

                            .info_text {
                                .info_title {
                                    line-height: 21px;
                                }
                            }

                            .info_time {
                                line-height: 17px;
                                margin-top: 2px;
                            }
                        }
                    }

                    .info_div:last-child {
                        border: none;
                    }
                }
            }
        }
    }
}

.data-list-title-left {
    font-size: 13px;
    color: #999;
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: 14px;

    img {
        margin-left: 5px;
        width: 16px;
        height: 16px;
    }
}

.arrow_down_div {
    width: 16px;
    height: 16px;
}

.card_top_bot {
    margin-top: 22px;
    width: 100%;

    .item_div {
        .card_icon_img {
            width: 34px;
            height: 34px;
            margin-bottom: 8px;
        }

        .title {
            line-height: 22px;
            text-align: center;
        }

        .val {
            line-height: 22px;
            margin-top: 5px;
            text-align: center;
        }
    }
}

.card_recharge {
    width: 100%;
    text-align: center;
    color: #ff6b2c;
    text-decoration: underline;
    margin-top: 20px;
}

// .freeze_div {
//   margin-left: 40px;
// }

.oil_card_div {
    position: relative;

    .card_icon_img {
        width: 34px;
        height: 34px;
        margin-bottom: 8px;
    }
    .card_icon_img:first-child {
        margin-bottom: 5px;
    }

    .recharge_img {
        position: absolute;
        left: 50%;
        top: -15px;
        width: 38px;
        height: 20px;
    }
}

.border_bottom {
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
}

.setting_div {
    width: 100%;
    bottom: 0;
    left: 0;
    background: #fff;
    padding: 13px 15px 10px 15px;
    z-index: 999;
    position: absolute;
}

.scroll_div {
    width: 100%;
}

.recharge_btn {
    width: 102px;
    height: 42px;
    border-radius: 200px;
    // border: 1px solid #ff6b2c;
    color: #fff;
    background: linear-gradient(135deg, #ff4000 0%, #ff6a00 100%);
    text-align: center;
    line-height: 42px;
}

.setting_btn {
    width: 100%;
    height: 50px;
    color: #ff6b2c;
    font-size: 16px;
    text-align: center;
    line-height: 50px;
    border-radius: 8px;
    border: 1px solid #ff6b2c;
}

.null_div {
    width: 100%;
    height: 70px;
}
</style>
