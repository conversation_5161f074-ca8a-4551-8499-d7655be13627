<template>
    <div class="p-bf fl-column ov-hid">
        <zj-navbar :border-bottom="false" :is-back="false" title="能源e站" :height="44"></zj-navbar>
        <div class="oil-tab fl-1 mh-0">
            <!-- 地图 -->
            <!-- <map
        :style="{ height: mapHeight + 'px' }"
        :scale="Number(scale_app)"
        show-location
        :markers="showMarkerArrV3_app"
        :latitude="Number(latV3) - 0.02"
        :longitude="lonV3"
        @callouttap="clickCallOut"
        @markertap="clickMarker"
        @maptap="mapTap"
      >
        <cover-view slot="callout">
          <cover-view
            class="marker-view fl-row fl-al-cen"
            :marker-id="item.id"
            v-for="item in showMarkerArr"
            :key="item.id"
          >
            <cover-view class="marker-lable-view">
              <cover-view class="navigation">
                <cover-image class="marker-navi-img" :src="oilIcon"></cover-image>
                <cover-view class="distance">{{ item.distance }}km</cover-view>
              </cover-view>
              <cover-view class="fl-column">
                <cover-view class="marker-name">{{ item.orgName }}</cover-view>
                <cover-view class="marker-time">
                  营业时间{{ timeSplit(item.businessStartTime) }}-{{
                  timeSplit(item.businessEndTime) }}
                </cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>-->
            <notOpenedWallet></notOpenedWallet>
            <!-- <Dialog></Dialog> -->
            <!-- <zj-unrealized-authentication  @realNameDialogClose="realNameDialogClose" @realNameInfo="realNameInfo"> </zj-unrealized-authentication> -->
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import notOpenedWallet from '../../../packages/notOpenedWallet/pages/main.vue';
import { oilCardRecharge } from '../../../s-kit/js/v3-http/https3/user.js';
// import { openWalletPost } from '../../../js/v3-http/https3/open-wallet/index.js'
export default {
    components: {
        notOpenedWallet,
        // Dialog
    },
    computed: {
        ...mapGetters(['lonV3', 'latV3']),
        ...mapState({
            // marker数据
            showMarkerArrV3_app: state => state.locationV3_app.showMarkerArrV3_app,
            // 地图缩放比例
            scale_app: state => state.locationV3_app.scale_app,
        }),
    },
    data() {
        return {
            topTab: 'navigation',
            mapHeight: 0,
            oilIcon: require('../../../../s-kit/image/homeImg/aircraft.png'),
            showMarkerArr: [],
        };
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
        // this.$store.dispatch('initLocationV3_app', () => {});
        let systemInfo = uni.getSystemInfoSync();
        this.system = systemInfo.system.indexOf('iOS') > -1;
        this.navH = 44 + systemInfo.statusBarHeight;
        let name = 'iPhone X';
        if (systemInfo.model.indexOf(name) > -1) {
            this.isIphoneX = true;
        }
        // 查询昆仑e享卡开通状态
        // this.getOpeningStatus()
    },
    //生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
        this.setMapHeight();
        // console.log(this.showMarkerArrV3_app, 'showMarkerArrV3_app');
        // console.log(getApp(), '首页测试getApp');
        // this.$RealNameAuth.open({
        //     successEvent: (name, idNumber) => {
        //         _this.$RealNameAuth.close();
        //         // commonUtil.triggerRiskAuth(name, idNumber).then(() => {
        //         //     resolve();
        //         // }).catch(error => {
        //         //     cnpcBridge.showToast(error)
        //         //     reject()
        //         // });
        //     }
        // })
    },
    methods: {
        // 获取地区充值方式
        async getAreaRecharge() {
            let params = {
                token: 'c:app:A6F62332D1124961AF35908A08A2F912',
                provinceName: '北京市',
                cityName: '昌平区',
                province: '北京市',
                city: '昌平区',
            };
            let res = await oilCardRecharge(params);
            console.log(res, '获取地区充值方式');
        },
        timeSplit(data) {
            try {
                return data.substring(0, 5);
            } catch (error) {}
        },

        setMapHeight() {
            // 获取窗口高度
            uni.createSelectorQuery()
                .in(this)
                .select('.oil-tab')
                .boundingClientRect(data => {
                    this.mapHeight = data.height;
                })
                .exec();
        },
        // 查询昆仑e享卡开通状态
        async getOpeningStatus() {
            let params = {};
            // 昆仑e享卡错回调
            let electronicWalletErrorFn = result => {
                console.log(result, '报错函数的回调');
            };
            let data = await openWalletPost(params, {
                isCustomErr: false,
                handleErrorFn: electronicWalletErrorFn,
            });
            console.log(data, '===昆仑e享卡开通状态');
        },
        // 地图marker点击事件
        clickMarker(e) {
            console.log('e-----', e);
            let markerId = e.detail.markerId;
            let index = this.showMarkerArrV3.findIndex(item => {
                return item.stationId == markerId;
            });
            this.$store.dispatch('setSelectMarkerToMapCenterV3', {
                marker: this.showMarkerArrV3[index],
            });
            this.$store.commit('setScale', 13);
        },
    },
};
</script>
<style scoped lang="scss">
/* @import url(); 引入css类 */
.oil-tab {
    position: relative;
    flex: 1;
}

// 地图
map {
    width: 100%;
    height: 100%;
}

.marker-view {
    // position: relative;
    font-size: 24rpx;
    height: 60px;
    color: #333;
    // margin-bottom: -10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    background: transparent;

    .marker-lable-view {
        border-radius: 100px;
        background: #ffffff;
        box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
        height: 60px;
        padding: 0 20px 0 5px;
        display: flex;
        align-items: center;
        justify-content: center;

        .navigation {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            position: relative;
            background: #e64f22;
            height: 50px;
            width: 50px;
            border-radius: 50%;

            .distance {
                font-size: 12px;
                font-weight: 600;
                color: #ffffff;
                background: transparent;
                margin-bottom: 5px;
                // position: absolute;
                // bottom: 16px;
                // left: 50%;
                // transform: translateX(-50%);
            }

            .marker-navi-img {
                width: 20px;
                height: 20px;
                margin-top: 5px;
            }
        }

        .marker-name {
            font-size: 24rpx;
            font-weight: 500;
            color: #333333;
            margin-left: 5px;
            margin-right: 10px;
        }

        .marker-time {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            margin-left: 5px;
            margin-right: 10px;
            margin-top: 5px;
        }
    }
}

/* #ifdef MP-ALIPAY */
.cover-view {
    position: absolute;
    top: calc(50% - 250rpx);
    left: calc(50% - 150rpx);
    border-radius: 100px;
}

/* #endif */
</style>
