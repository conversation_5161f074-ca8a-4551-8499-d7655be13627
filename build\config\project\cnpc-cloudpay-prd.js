const { requireMkSync } = require('../utils');
const baseConfig = requireMkSync('./base-prd.js');

module.exports = {
  ...baseConfig,
  _: '云闪付小程序',
  name: 'cnpc-cloudpay-prd',
  cloudappId: '7d6768a4a0232176',
  clientCode: 'C21',
  v3sign: '3rkc5dJq5IQe5P1MlFCx', // * 微信/支付宝必填 3.0加签key
  platform: 'union',
  miniappVersion: '1.0.0',
  baseType: 'prd',
  originReturnUrl: 'https://hkyzh5.kunlunjyk.com/h5/index.html#/pages/hkyz/authResult',  //人脸认证结果所需URL
  // 埋点
  // maa: {
  //     ...baseConfig.maa,
  //     id: '30',
  // },
  app: {
    ...baseConfig.app,
  },
};
