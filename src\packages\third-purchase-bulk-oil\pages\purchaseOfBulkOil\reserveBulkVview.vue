<template>
    <div class="page-view bg-fff">
        <div>
            <div class="charge-content-view p-LR-16 border-rad-8 fl-column bg-fff">
                <div ref="toparea" class="top-area bg-fff padding12">
                    <div class="stationinfo">
                        <div class="fl-row fl-jus-bet">
                            <div v-if="selectBulkOilStationObject && selectBulkOilStationObject.orgCode" class="detail f-1">
                                <div class="name-area fl-row fl-al-cen">
                                    <!-- #ifndef MP-ALIPAY -->
                                    <div
                                        :class="{ 'scrolling-text': selectBulkOilStationObject.orgName.length > 11 }"
                                        class="name font-16 weight-bold color-000 f-1"
                                    >
                                        {{ selectBulkOilStationObject.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                    <!-- #ifdef MP-ALIPAY -->
                                    <div class="name font-16 weight-bold color-000 f-1">
                                        {{ selectBulkOilStationObject.orgName || '暂未查询到网点' }}
                                    </div>
                                    <!-- #endif -->
                                </div>
                                <div class="normalOperation">
                                    <img alt="" src="../../images/normalOperation.png" />
                                </div>
                                <div v-if="selectBulkOilStationObject.address != 'null'" class="font-12 weight-400 color-333 oil-address">{{
                                    selectBulkOilStationObject.address || ''
                                }}</div>
                            </div>
                            <div class="fl-row paddingB24">
                                <img
                                    v-if="!isHarmony"
                                    alt
                                    class="navt-to"
                                    src="../../images/stationinfo-icon-location.png"
                                    @click.stop="clickNaviStateion"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="line_bottom_dashed mar-top-12"></div>
                    <div class="fl-column">
                        <div class="fl-row fl-jus-bet fl-al-cen marginB10 mar-top-12">
                            <div class="font-14 color-333 weight-600">身份证号：{{ applicationFormInformation.idNum }}</div>
                            <div class="font-14 color-333 weight-600">{{ applicationFormInformation.userName }}</div>
                        </div>
                        <div :class="margins" class="fl-row bg-F7F6FB">
                            <div class="fl-row fl-al-cen p-TB-16 width100">
                                <div class="fl-column fl-jus-bet f-1 fl-al-cen">
                                    <div class="font-12 color-333 weight-400 marginB5">个人用户</div>
                                    <div class="font-12 color-999 weight-400">用户类型</div>
                                </div>
                                <div class="fl-column fl-jus-bet f-1 fl-al-cen">
                                    <div class="font-12 color-333 weight-400 marginB5"
                                        >{{ $sKit.layer.getOilNum(applicationFormInformation.productName, 1) }}号/{{
                                            applicationFormInformation.oilQuantity
                                        }}L</div
                                    >
                                    <div class="font-12 color-999 weight-400">油品号/数量</div>
                                </div>
                                <div class="fl-column fl-jus-bet f-1 fl-al-cen">
                                    <div class="fl-row fl-al-cen marginB5">
                                        <div class="font-14 color-333 marr3">&yen;</div>
                                        <div class="font-12 color-333 weight-400">{{ oilProductAmount || '' }}</div>
                                    </div>
                                    <div class="font-12 color-999 weight-400">加油金额</div>
                                </div>
                            </div>
                        </div>
                        <div v-if="circularOrder === 1" class="width100 bg-FFF7DC border-rad-BL-8 marginB17">
                            <div class="p-TB-16 te-center">
                                <div class="color-5D2D14 weight-400 font-12 marginB3">支付成功，请到加油站室内获取加油码</div>
                                <div class="color-5D2D14 weight-400 font-12">请60分钟内到站加油，超时自动取消，需重新下单</div>
                            </div>
                        </div>
                        <div v-if="circularOrder === 3" class="width100 border-rad-BL-8 marginB17">
                            <Loading></Loading>
                            <div class="frozen">
                                <div class="font-16 weight-bold color-333 te-center">本次加油已冻结{{ oilProductAmount }}元</div>
                                <div class="font-12 weight-400 color-999 te-center">加油完成按实际加油金额扣款加油结束后请核对加油信息</div>
                            </div>
                        </div>
                        <div v-if="circularOrder === 0" class="fl-row fl-al-cen fl-jus-bet marginB17">
                            <div class="fl-row fl-al-cen">
                                <div class="font-14 color-666 weight-500">余额</div>
                                <div class="font-24 color-333 marl10 fl-row fl-al-base">
                                    <div class="font-14 color-333 marr3">&yen;</div>
                                    {{ walletInfo.walletBalance || '0' }}
                                </div>
                            </div>
                            <div class="fl-row fl-al-cen" @click.stop="rechargeClick">
                                <div class="font-15 weight-500 color-FF6B2C">充值</div>
                                <div class="arroe-right-E64F22 marl5"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-if="circularOrder === 0 || circularOrder === 1"
                    class="color-fff font-18 btn-44 primary-btn"
                    @click.stop="submitEvent()"
                    >{{ text }}</div
                >
            </div>
        </div>
        <zj-old-account v-if="isTransfer"></zj-old-account>
        <zj-unrealized-authentication v-if="realNameDialogFlag" @realNameDialogClose="realNameDialogClose" @realNameInfo="realNameInfo">
        </zj-unrealized-authentication>
        <zj-agreement @cancelClick="cancelClick" @enterNavEvent="enterNavEvent"></zj-agreement>
    </div>
</template>

<script>
// 定义常量
const PAY_SUCCESS = 'PAY_SUCCESS';
const PAY_ERROR_003 = 'PAY_ERROR_003';
const PAY_ERROR_004 = 'PAY_ERROR_004';
const currency = require('currency.js');
import { mapState, mapGetters } from 'vuex';
import {
    preAuthOrderApi,
    generateOrder,
    oilPriceApi,
    preAuthCancel,
    getPreAuthOrder,
} from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import Loading from '../../components/loading/loading.vue';
// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-resver.js';
// #endif
// #ifndef  MP-MPAAS
import wxMixin from './diff-environment/wx-resever.js';
import zfbMixin from './diff-environment/zfb-resver.js';
// #endif
import { clientCode } from '../../../../../project.config';
export default {
    name: 'reserveBulkVview',
    components: {
        Loading,
    },
    mixins: [
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS
        wxMixin,
        zfbMixin,
        // #endif
    ],
    computed: {
        ...mapGetters(['walletInfo', 'walletStatus', 'latV3', 'lonV3', 'tabIndex']),
        ...mapState({
            selectBulkOilStationObject: state => state.bulkOil.selectBulkOilStationObject,
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
            accountDataPlugin: state => state.thirdIndex.accountDataPlugin,
            riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
            riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
            isHarmony: state => state.thirdIndex.isHarmony,
            memberBaseInfo: state => state.member.memberBaseInfo,
        }),
        isWalletStatusCard() {
            if (!this.walletStatus.status) {
                return true;
            } else if (this.walletStatus.status && this.walletStatus.accountStatus == 3) {
                return true;
            } else {
                return false;
            }
        },
        margins() {
            return this.circularOrder === 1 ? 'border-rad-TR-8' : 'marginB17 border-rad-8';
        },
        text() {
            return this.circularOrder === 1 ? '取消订单' : '提交订单';
        },
    },
    props: {
        refer: {
            type: String,
            default: '',
        },
        applicationFormInformation: {
            type: Object,
        },
    },
    data() {
        return {
            //是否存在预授权订单
            circularOrder: 0,
            //未实人认证加油时需要弹出输入身份证号和姓名的弹窗
            realNameDialogFlag: false,
            // 下单成功的数据
            preOrderData: {},
            // 订单信息
            dataInfo: {},
            // 油品金额
            oilProductAmount: '',
            // 定时器执行间隔时间
            pollTime: 10000,
            // 控制预约加油重复跳转到加油服务账单页面
            evaluationControlFlag: true,
        };
    },
    created() {},
    watch: {},
    mounted() {
        // 获取电子钱包金额
        this.$store.dispatch('getAccountBalanceAction');
        // 获取电子钱包状态
        this.$store.dispatch('getSetWalletStatus');
        // 查询是否存在预授权订单
        this.getPreAuthorizationDetails();
        console.log(this.memberBaseInfo, '用户基本信息');
        console.log(this.selectBulkOilStationObject, '油站信息');
        console.log(this.applicationFormInformation, '申请单信息====加油组件');
        setTimeout(() => {
            // 获取油品单价
            this.getOilPrice();
        }, 100);
        // 确认提交
        this.submitEvent = this.$sKit.commonUtil.throttleUtil(this.submitEvent, 3000);
    },
    methods: {
        /**
         * @description : 获取油品单价
         * @return        {*}
         */
        async getOilPrice() {
            let params = {
                orgCode: this.applicationFormInformation.stationCode, //站点编码
                productNoList: [this.applicationFormInformation.productNo], //油品编号
            };
            let res = await oilPriceApi(params, { isload: false });
            if (res.success && res.data?.length > 0) {
                console.log(res.data[0], '获取油品单价');
                if (res.data[0].listedPrice) {
                    console.log(res.data[0].listedPrice, this.applicationFormInformation.oilQuantity, '单价===升数');
                    // 使用 multiply 方法进行乘法运算
                    this.oilProductAmount = currency(res.data[0].listedPrice).multiply(this.applicationFormInformation.oilQuantity).value;
                    console.log('listedPrice===', this.oilProductAmount);
                } else {
                    uni.showToast({
                        title: '获取加油金额失败',
                        icon: 'none',
                        mask: true,
                        duration: 2000,
                    });
                }
            }
        },
        /**
         * @description : 查询预授权订单详情
         * data : 预授权订单状态1创建；5已取消；3开始加油；4加油结束；
         * @return        {*}
         */
        async getPreAuthorizationDetails() {
            let params = {
                orderSubType: '55',
            };
            let res = await preAuthOrderApi(params, { isload: false });
            if (res.success) {
                if (res.data) {
                    this.dataInfo = res.data;
                    this.circularOrder = res.data.preOrderStatus;
                    // 如果没有预约码，代码没有支付密码 执行取消订单操作
                    if (!res.data.fuelCode) {
                        await this.cancelAction();
                    }
                    console.log('查询预授权订单详情', res.data);
                    await this.getRefuelingStatus();
                } else {
                    this.circularOrder = 0;
                }
            } else {
                this.circularOrder = 0;
            }
        },
        // 提交e享加油订单
        async submitEvent() {
            // 如果是存在订单，按钮是取消订单文字，调用取消订单接口并返回
            if (this.circularOrder === 1) {
                await this.$store.dispatch('zjShowModal', {
                    title: '您是否取消订单？',
                    confirmText: '确认',
                    cancelText: '取消',
                    cancelColor: '#666666',
                    confirmColor: '#333333',
                    success: async res => {
                        if (res.confirm) {
                            await this.cancelAction();
                        } else if (res.cancel) {
                        }
                    },
                });

                return;
            }
            try {
                // 检查钱包状态
                await this.checkWalletStatus({
                    nextFun: async () => {
                        // 存在加油金额并且钱包余额大于加油金额
                        if (Number(this.walletInfo.walletBalance) > Number(this.oilProductAmount)) {
                            this.showLoading();
                            await this.preAuthorizedRefueling();
                        } else {
                            // 显示余额不足提示框
                            await this.showInsufficientBalanceModal();
                        }
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: () => this.$store.dispatch('getIdentityInformationAndElectronicCardData'),
                });
            } catch (error) {
                console.error('提交e享加油订单时出现错误:', error);
            }
        },
        // 充值和去开通跳转
        async rechargeClick() {
            if (!this.isWalletStatusCard) {
                // 去充值
                this.checkWalletStatus({
                    nextFun: () => {
                        this.navigateToRechargePage();
                    },
                    freezeReasonArr: [9, 10],
                    cancelCallback: () => this.$store.dispatch('getIdentityInformationAndElectronicCardData'),
                    isEnter: 1,
                    walletAddParams: {
                        refer: 'r02',
                    },
                });
            } else {
                // 去开通
                this.navigateToWalletAddPage();
            }
        },
        // 封装检查钱包状态的逻辑
        async checkWalletStatus(options) {
            return await this.$sKit.commonUtil.eWalletNormal(options);
        },
        // 显示余额不足提示框的逻辑
        async showInsufficientBalanceModal() {
            await this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '您的钱包余额不足',
                confirmText: '确认',
                confirmColor: '#000',
                cancelText: '取消',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        this.navigateToRechargePage();
                    }
                },
            });
        },
        // 跳转到充值页面的逻辑
        async navigateToRechargePage(refer = '') {
            const url = '/packages/third-remaining-sum/pages/third-wallet-recharge/main';
            const params = refer ? { refer } : {};
            const type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 跳转到开通钱包页面的逻辑
        async navigateToWalletAddPage(refer = '') {
            const url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
            const params = refer ? { refer } : {};
            const type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        // 调用e享加油接口
        async preAuthorizedRefueling() {
            let params = {
                stationCode: this.selectBulkOilStationObject.orgCode, //网点编码
                stationName: this.selectBulkOilStationObject.orgName, //网点名称
                preAuthzAccount: this.walletInfo.ewalletNo, //昆仑e享卡账号（e享加油必填）
                productNo: this.applicationFormInformation.productNo, //商品编码（油品编码)
                productName: this.applicationFormInformation.productName, //商品名称（油品名称）
                preAuthzAmount: this.oilProductAmount, //预授权金额（e享加油必填，单位：元）
                usedInterestsAccount: '0', // 是否使用权益账户（e享加油，默认使用）1—是；0—否,// 是否使用权益账户（e享加油，默认使用）1—是；0—否
                applyNo: this.applicationFormInformation.applyNo, // 申请单号(散装油预约加油)
                orderSubType: '55',
            };
            console.log(params, '------generateOrder----params------');
            let res = await generateOrder(params, { isload: false });
            console.log(res, 'generateOrder----res');
            if (res.success) {
                this.preOrderData = res.data;
                console.log('接口预下单成功', res.data);
                this.dataInfo = res.data;
                // 调用插件进行预下单
                await this.preLicensingPlaceAnOrder();
            } else {
                this.hideLoading();
            }
        },
        // 调用插件进行预下单
        async preLicensingPlaceAnOrder(isAuth = false) {
            // 构建请求参数
            const params = this.buildPreOrderParams(isAuth);
            console.log(params, '插件加油预下单请求参数');
            console.log(this.accountDataPlugin, '插件实例');

            try {
                let res = {};
                // #ifdef MP-MPAAS
                res = await this.initiatePaymentAppAndHs(params, isAuth);
                // #endif
                // #ifdef MP-ALIPAY
                res = await this.initiatePaymentAli(params, isAuth);
                // #endif
                // #ifdef MP-WEIXIN
                res = await this.initiatePaymentWx(params, isAuth);
                // #endif
                console.log(res, '插件加油预下单');
                // 根据不同的响应码进行处理
                await this.handlePreOrderResponse(res);
            } catch (error) {
                // 处理请求异常
                console.error('插件加油预下单请求异常:', error);
                const customErr = '请求异常，请稍后重试';
                const errorCode = 'REQUEST_ERROR';
                await this.showErrorModal(customErr, errorCode);
                await this.cancelAction();
            } finally {
                this.hideLoading();
            }
        },
        // 构建预下单请求参数
        buildPreOrderParams(isAuth) {
            return {
                areaCode: this.walletInfo.addressNo,
                bizOrderNo: this.dataInfo.preAuthzOrderNo,
                stationCode: this.dataInfo.stationCode,
                // extendFiled: await post.addExtendFiled('plugin', { isAuth }),
                accountPreType: '3',
                amount: this.oilProductAmount,
                accountNo: this.walletInfo.ewalletNo,
            };
        },
        // 处理支付响应
        async handlePreOrderResponse(res) {
            switch (res.code) {
                case PAY_SUCCESS:
                case PAY_ERROR_004:
                case '0':
                    await this.handleSuccessResponse();
                    break;
                case PAY_ERROR_003:
                    await this.handleRiskResponse(res);
                    break;
                default:
                    await this.handleErrorResponse(res);
                    break;
            }
        },
        // 处理支付成功响应
        async handleSuccessResponse() {
            this.circularOrder = 1;
            this.hideLoading();
            // 获取加油状态
            this.getRefuelingStatus();
            // 获取电子钱包金额
            await this.$store.dispatch('getAccountBalanceAction');
        },
        // 处理支付风险响应（需要实人认证）
        async handleRiskResponse() {
            this.isCanClickPay = true;
            this.isPaying = false;
            // 需要实人认证
            const riskRes = await this.$sKit.commonUtil.oilTriggerRisk();
            // res 13 未认证 ，14 实名认证通过，15 实人认证通过
            if (riskRes === 13) {
                // 打开实人认证的表单弹窗
                this.realNameDialogFlag = true;
            }
        },
        // 处理支付错误响应
        async handleErrorResponse(res) {
            const { customErr, errorCode } = await this.parseErrorMsg(res.msg, res.code);
            this.hideLoading();
            await this.showErrorModal(customErr, errorCode);
            await this.cancelAction();
        },
        // 解析支付错误信息
        async parseErrorMsg(msg, code) {
            let errIndex = msg.indexOf(':');
            let errorCode = '';
            let customErr = '';
            if (errIndex !== -1) {
                errorCode = msg.slice(0, errIndex);
                customErr = msg.slice(errIndex + 1, msg.length);
            } else {
                customErr = msg;
                errorCode = code;
            }
            console.log(customErr, errorCode, '解析错误信息');
            return { customErr, errorCode };
        },
        // 显示支付错误提示模态框
        async showErrorModal(customErr, errorCode) {
            await this.$store.dispatch('zjShowModal', {
                // type: 'http',
                title: customErr,
                content: errorCode,
                confirmText: '确认',
                cancelColor: '#666',
                success: res => {},
            });
        },
        // 支付实人认证弹窗的确认事件
        async realNameInfo(val) {
            this.$sKit.commonUtil
                .triggerRiskAuth(val.name, val.idNumber)
                .then(res => {
                    uni.showLoading({
                        mask: true,
                    });
                    this.realNameDialogFlag = false;
                    this.preLicensingPlaceAnOrder(this.preOrderData, true);
                })
                .catch(err => {
                    uni.showToast({ title: err });
                });
        },
        // 支付关闭实人认证弹窗
        async realNameDialogClose() {
            this.realNameDialogFlag = false;
        },
        // 支付关闭人脸认证协议弹窗
        async enterNavEvent() {
            // 关闭人脸认证协议弹窗
            this.$store.dispatch('changeFacePop', false);
            this.$sKit.commonUtil.nextOilTriggerRisk().then(
                res => {
                    this.preLicensingPlaceAnOrder(this.preOrderData, true);
                },
                () => {
                    this.cancelAction();
                },
            );
        },
        // 点击打开导航 传入起始点startlong startlat 和终点的经纬度 endlong  endlat
        async clickNaviStateion() {
            if (this.selectBulkOilStationObject) {
                if (this.selectBulkOilStationObject.stationStatus != 20 && this.selectBulkOilStationObject.stationStatus != 10) {
                    this.oilDialogFlagType = 'stationStatus';
                    this.$refs['popDialogFlag'].open();
                    this.confirmText = '导航到站';
                    this.cancelText = '稍后使用';
                    this.confirmColor = '#000';
                } else {
                    // #ifdef MP-MPAAS
                    this.$sKit.commonUtil.judgeLocationAuth().then(res => {
                        if (res) {
                            this.$cnpcBridge.openLocation({
                                latitude: this.selectBulkOilStationObject.latitude,
                                longitude: this.selectBulkOilStationObject.longitude,
                                name: this.selectBulkOilStationObject.orgName,
                                address: this.selectBulkOilStationObject.address || this.selectBulkOilStationObject.orgName,
                            });
                        }
                    });
                    // #endif
                    // #ifndef MP-MPAAS
                    uni.openLocation({
                        latitude: Number(this.selectBulkOilStationObject.latitude),
                        longitude: Number(this.selectBulkOilStationObject.longitude),
                        name: this.selectBulkOilStationObject.orgName,
                        address: this.selectBulkOilStationObject.address || this.selectBulkOilStationObject.orgName,
                    });
                    // #endif
                }
            }
        },
        // 获取加油状态
        async getRefuelingStatus() {
            // 构建请求参数
            const params = await this.buildRequestParams();
            // 发起预授权订单查询请求
            getPreAuthOrder(params, { isload: false }).then(
                async res => {
                    if (res.data) {
                        this.circularOrder = res.data.preOrderStatus;
                        console.log('getPreAuthOrder----res.data', res.data);
                        // 更新预授权订单信息
                        this.dataInfo = res.data;
                        console.log(this.evaluationControlFlag, 'this.evaluationControlFlag');
                        // 防止出现两次订单评价页面
                        if (this.evaluationControlFlag) {
                            // 根据订单状态进行不同处理
                            await this.handleOrderStatus();
                        }
                    }
                },
                error => {
                    // 处理请求错误
                    this.handleRequestError();
                },
            );
        },
        // 构建请求参数
        async buildRequestParams() {
            console.log(this.dataInfo, 'this.dataInfo===');
            return new Promise((resolve, reject) => {
                let params = {
                    preAuthzOrderNo: this.dataInfo.preAuthzOrderNo,
                    stationCode: this.dataInfo.stationCode,
                };
                resolve(params);
            });
        },
        // 根据订单状态进行不同处理
        async handleOrderStatus() {
            const orderStatus = this.dataInfo.preOrderStatus;
            switch (orderStatus) {
                case 1:
                    this.circularOrder = 1;
                    await this.scheduleNextRequest();
                    break;
                case 3:
                    this.circularOrder = 3;
                    await this.scheduleNextRequest();
                    break;
                case 5:
                    this.circularOrder = 0;
                    break;
                case 4:
                    await this.handleOrderCompleted();
                    break;
                default:
                    break;
            }
        },
        // 记录油服务页面事件
        trackOilServicePage() {
            // this.$sKit.mpBP.tracker('', {
            //     seed: 'exoilBiz',
            //     pageID: 'oilServicePage',
            //     refer: this.details.refer,
            //     channelID: clientCode,
            //     djAmount: this.dataInfo.preAuthzAmount,
            //     address: this.cityName,
            // });
        },
        // 安排下一次请求
        async scheduleNextRequest() {
            this.clearTimer();
            this.requestStatusInterval = setTimeout(() => {
                this.getRefuelingStatus();
            }, this.pollTime);
        },
        // 处理订单完成状态
        async handleOrderCompleted() {
            this.clearTimer();
            // 跳转到支付结果页面
            await this.navigateToPayResultPage();
            this.evaluationControlFlag = false;
            // 加油结束，获取钱包明细
            this.$store.dispatch('getAccountBalanceAction');
            // 获取能源币，油卡，电子券，积分和余额，七日内余额
            this.$store.dispatch('basicCouponAction');
        },
        // 跳转到支付结果页面
        async navigateToPayResultPage() {
            const URL = `/packages/third-oil-charge-payment/pages/oil-charge-pay-result/main`;
            // 由于是关闭当前页面跳转的，安卓左滑会回到列表页面需要进行刷新
            this.$store.commit('setRefreshBulkOilList', true);
            const params = { ...this.dataInfo };
            const type = 'redirectTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        // 处理请求错误
        async handleRequestError() {
            this.clearTimer();
            this.requestStatusInterval = setTimeout(() => {
                this.getRefuelingStatus();
            }, this.pollTime);
        },
        // 取消订单
        async cancelAction() {
            let params = {
                preAuthOrderNo: this.dataInfo.preAuthzOrderNo,
                stationCode: this.dataInfo.stationCode,
            };
            let res = await preAuthCancel(params);
            if (res.success) {
                await this.$store.commit('setRefreshBulkOilList', true);
                uni.showToast({
                    title: '订单取消成功',
                    icon: 'none',
                    mask: true,
                    duration: 2000,
                });

                await this.$store.dispatch('getAccountBalanceAction');
                await this.$store.dispatch('getSetWalletStatus');
                this.circularOrder = 0;
            }
        },
        // 清除定时器
        clearTimer() {
            if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
        },
        showLoading() {
            uni.showLoading({
                title: '加载中...',
                mask: true,
                icon: 'none',
            });
        },
        hideLoading() {
            uni.hideLoading();
        },
    },
    beforeDestroy() {
        this.clearTimer();
        this.$store.dispatch('zjHideModal');
    },
};
</script>

<style lang="scss" scoped>
.page-view {
    width: 100%;
    flex: 1;
    position: relative;
    box-sizing: border-box;
    overflow-y: hidden;
}
.charge-content-view {
    border-radius: 10px 10px 0px 0px;
    /* #ifdef MP-MPAAS */
    padding-bottom: 40rpx;
    /* #endif */
    width: 100%;
    // margin-top: -5px;
    position: relative;
    .top-area {
        box-sizing: border-box;
        .stationinfo {
            .detail {
                overflow: hidden;
                // #ifndef MP-ALIPAY
                .name-area {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 180px;
                    .name {
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // white-space: nowrap;
                    }
                }
                // #endif
                // #ifdef MP-ALIPAY
                .name-area {
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 180px;
                    }
                }
                // #endif
                .normalOperation {
                    width: 100rpx;
                    height: 42rpx;
                    margin: 3px 0;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
                .pad-t-6 {
                    padding-top: 8px;
                }

                .oil-address {
                    //padding-top: 12px;
                }
                .time-icon {
                    width: 52px;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                    background: #efeded;
                    border-radius: 4px;
                    margin: 0 4px;
                }
            }
            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                margin-left: 20px;
                display: block;
            }
            .center-area {
                padding-top: 12px;

                .con-list {
                    justify-content: flex-start;
                    width: 100%;
                    flex-wrap: wrap;

                    .item {
                        padding: 8rpx 16rpx;
                        background: #f3f3f6;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        color: #333333;
                        text-align: center;
                        margin: 0 18rpx 8rpx 0;
                    }

                    // .item:nth-of-type(4n + 0) {
                    //     margin-right: 0px;
                    // }
                }
            }

            .marketing-assistant {
                margin-top: 5px;
                border: 1px solid #c4c4c3;
                background-color: #fef5ee;
                border-radius: 8px 8px;
                padding: 4.5px;

                .marketing-text {
                    font-size: 12px;
                    color: #fb9463;
                    line-height: 17.5px;
                    overflow: hidden; /* 隐藏超出的文本 */
                    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
                    -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
                    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                    word-break: break-all; /* 允许在单词内换行 */
                }
            }
        }
        .changestation {
            margin-left: 12px;
            width: 56px;
            height: 17px;
            line-height: 34rpx;
            align-items: center;
            justify-content: center;
            display: flex;
            padding: 2px 0;
            text-align: center;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            border-radius: 4px;
            border: 1px solid #e64f22;
            box-sizing: border-box;
        }
        .margin-8-l-r {
            margin: 0 8px;
        }

        .center-area {
            position: relative;
            padding-top: 16px;
            box-sizing: border-box;

            .width107 {
                width: 107px;
            }

            .width75 {
                width: 75px;
            }

            .height35 {
                height: 35px;
                line-height: 35px;
            }

            .mar-r-8 {
                margin-right: 8px;
            }

            .pdtl4 {
                padding-top: 4px;
                padding-left: 4px;
                box-sizing: border-box;
            }
        }

        .card-nav {
            padding: 0 12px 19px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .marl5 {
                margin-left: 5px;
            }
        }

        .card-nav-no {
            padding: 0 12px 19px;
            box-sizing: border-box;

            .marl5 {
                margin-left: 5px;
            }
        }
    }
    .mart12 {
        margin-top: 12px !important;
    }
    .marl10 {
        margin-left: 10px;
    }

    .equity-div {
        margin: 0px 15px;
        height: 33px;
        border-radius: 0px 0px 8px 8px;
        line-height: 33px;
        box-sizing: border-box;
    }

    .btn-44 {
        height: 44px;
        border-radius: 8px;
        line-height: 44px;
        margin: 0 12px 16px 12px;
    }
    .market-div {
        margin: 12px 16px 0;
    }

    .advertisingPopups {
        position: fixed;
        bottom: 0;
        top: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        z-index: 998;
        background-color: rgba(0, 0, 0, 0.6);
    }
}
.no-reserve {
    width: 71px !important;
    height: 59px;
    display: block;
}
.frozen {
    padding: 0 94rpx;
}
</style>
