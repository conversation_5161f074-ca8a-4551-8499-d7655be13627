<template>
    <div class="remaining_div fl-column bg-F7F7FB">
        <img src="../../images/<EMAIL>" alt class="bg-page" />
        <div class="bg-F7F7FB f-1 mh-0 fl-column">
            <zj-navbar
                :background="{ background: 'transparent' }"
                back-icon-color="#fff"
                titleColor="#fff"
                title="我的钱包"
                :border-bottom="false"
            ></zj-navbar>
            <div class="f-1 mh-0">
                <zj-pull-down-refresh @refreshPullDown="refreshPullDown" ref="pullDownRefreshRef">
                    <div class="content_div">
                        <div class="card_top">
                            <div class="card_top_div">
                                <div class="my_blance fl-row fl-al-cen">
                                    <div class="font-15 color-999" @click="toRemainingClick">我的余额(元)</div>
                                    <img @click="showInfoClick()" v-if="showInfo" src="../../images/eyes_open.png" alt />
                                    <img @click="showInfoClick()" v-else src="../../images/close_img.png" alt />
                                </div>
                                <div v-if="showInfo" class="blance_amount font-style font-24 color-333" @click="toRemainingClick">{{
                                    walletInfo.walletBalance || 0
                                }}</div>
                                <div v-else class="blance_amount font-style font-24 color-333" @click="toRemainingClick">****</div>
                            </div>
                            <div class="card_top_bot fl-row fl-jus-bet">
                                <div class="item_div fl-column" @click="toDetailPage(1)">
                                    <div class="title font-15 color-666">优惠券</div>
                                    <div class="val font-19 color-333 font-style">{{ memberAccountInfo.couponsCount || 0 }}</div>
                                </div>
                                <div class="item_div fl-column" @click="toDetailPage(2)">
                                    <div class="title font-15 color-666">实体卡</div>
                                    <div class="val font-19 color-333 font-style">{{ memberAccountInfo.fuelCardCount || 0 }}</div>
                                </div>
                                <div class="item_div fl-column" @click="toDetailPage(3)">
                                    <div class="title font-15 color-666">能源币</div>
                                    <div class="val font-19 color-333 font-style">{{ memberAccountInfo.ptsAvailableAmount || 0 }}</div>
                                </div>
                                <div class="item_div fl-column" @click="toDetailPage(4)">
                                    <div class="title font-15 color-666">积分</div>
                                    <div class="val font-19 color-333 font-style">{{
                                        memberAccountInfo.loyaltyPtsAvailableAmount || 0
                                    }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="set_div bg-fff border-rad-8">
                            <div
                                class="fl-row fl-jus-bet fl-al-cen set_div_item padding_12 line_bottom"
                                :class="{ disabled: walletInfo.leftChangeCount ? false : true }"
                                @click="toDetail(1)"
                            >
                                <div class="set_div_item_left fl-row fl-al-cen">
                                    <div class="font-15 item_title">更换常用地</div>
                                    <div v-if="walletInfo.leftChangeCount == 0" class="font-12 color-999">您的变更次数已达当年上限</div>

                                    <div v-else class="font-12 color-999">昆仑e享卡常用地</div>
                                </div>
                                <div class="right_btn_div fl-row fl-al-cen">
                                    <div class="color-999 font-12">{{ walletInfo.addressName }}</div>
                                    <div class="right_btn"></div>
                                </div>
                            </div>
                            <div
                                v-if="supportType !== 0"
                                class="fl-row fl-jus-bet fl-al-cen set_div_item padding_6 line_bottom"
                                :class="{ disabled: !supportType }"
                            >
                                <!--  -->
                                <div class="set_div_item_left fl-row fl-al-cen">
                                    <!-- v-if="$store.state.localAuthInfo.localAuthType||$store.state.localAuthInfo.denied" -->
                                    <div class="font-15 color-333 item_title">{{ supportType === 1 ? '指纹支付' : '人脸支付' }}</div>
                                    <div class="font-12 color-999">昆仑e享卡与实体卡{{ supportType === 1 ? '指纹支付' : '人脸支付' }}</div>
                                </div>
                                <div class="right_btn_div" style="margin-right: -10px">
                                    <!-- <switch color="#FF6133" @change="switch2Change" style="transform:scale(0.8)" /> -->
                                    <!--:checked="switchChecked"-->
                                    <img class="img_style" src="../../images/switch_on.png" v-if="switchChecked" @click="switch2Change" />
                                    <img class="img_style" src="../../images/switch_off.png" v-else @click="switch2Change" />
                                </div>
                            </div>
                            <div class="fl-row fl-jus-bet fl-al-cen set_div_item padding_12 line_bottom" @click="changePassword()">
                                <div class="set_div_item_left fl-row fl-al-cen">
                                    <div class="font-15 item_title">修改支付密码</div>
                                    <div class="font-12 color-999">昆仑e享卡与实体卡支付密码</div>
                                </div>
                                <div class="right_btn_div">
                                    <div class="right_btn"></div>
                                </div>
                            </div>
                            <div class="fl-row fl-jus-bet fl-al-cen set_div_item padding_12 line_bottom" @click="toDetailPW()">
                                <div class="set_div_item_left fl-row fl-al-cen">
                                    <div class="font-15 item_title">忘记支付密码</div>
                                    <div class="font-12 color-999">昆仑e享卡与实体卡支付密码</div>
                                </div>
                                <div class="right_btn_div">
                                    <div class="right_btn"></div>
                                </div>
                            </div>
                            <div
                                class="fl-row fl-jus-bet fl-al-cen set_div_item padding_12"
                                :class="{ disabled: cancelInfo.leftCancelTimes ? false : true }"
                                @click="toDetail(4)"
                            >
                                <div class="set_div_item_left fl-row fl-al-cen">
                                    <div class="font-15 item_title">昆仑e享卡注销</div>
                                    <!-- <div
                    v-if="cancelInfo.leftCancelTimes  != 0 "
                    class="font-10 color-999"
                  ></div>-->
                                    <div
                                        v-if="cancelInfo.leftCancelTimes == 0 && walletStatus.status && walletStatus.accountStatus != '3'"
                                        class="font-12 color-999"
                                        >您的注销次数已达到当年上限</div
                                    >
                                    <!-- <div v-else class="font-12 color-999">昆仑e享卡注销后，您的加油卡将自动解绑</div> -->
                                </div>
                                <div class="right_btn_div">
                                    <div class="right_btn"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </zj-pull-down-refresh>
            </div>
        </div>
        <zj-show-modal>
            <div class="tc_div" v-if="showModalType == 1">
                <div class="title">温馨提示</div>
                <div class="text">支付密码为6位数字;</div>
                <div class="text">昆仑e享卡与实体卡移动支付共用同一个支付密码，修改后实体卡线下支付密码不受影响;</div>
                <div class="text">能源e站App和小程序采用统一的支付密码，修改后同时生效。</div>
                <div style="float: right; overflow: hidden; color: #e64f22" @click="toDetailPW()">忘记密码</div>
            </div>

            <div class="tc_div" v-else>
                <div class="title">常用地由“{{ walletInfo.addressName }}”变更为 “{{ choseCity }}”</div>
                <div class="number_of_times fl-row">
                    每年可更换5次，还剩
                    <div>{{ walletInfo.leftChangeCount }}</div
                    >余次
                </div>
                <div class="text text_title">更换后：</div>
                <div class="text">您当前电子券只能在当前常用地使用，不会随着常用地变更而改变;</div>
                <div class="text">您会员权益（包括会员折扣、积分、能源币等权益）将随你变更归属地而变更，原有会员折扣会失效。</div>
            </div>
        </zj-show-modal>
        <SelectCity
            v-if="showPopup"
            :provinceCityArray="provincesAndCitiesList"
            :show="showPopup"
            @sureSelectArea="sureSelectArea"
            @hideShow="hideShow"
        ></SelectCity>
        <zj-old-account v-if="isTransfer"></zj-old-account>
    </div>
</template>

<script>
import SelectCity from '../../../../s-kit/components/layout/zj-selectCity/zj-selectCity.vue';
import { provinceAndCityList } from '../../../../s-kit/js/v3-http/https3/openingAWallet/index';
import { balance, addressModify, accountLeftCancelTimesQuery } from '../../../../s-kit/js/v3-http/https3/wallet.js';
import { mapState, mapGetters } from 'vuex';

// import ZjOldAccount from '../../../../s-kit/components/layout/zj-old-account'
import Vue from 'vue';

export default {
    name: 'third-my-wallet',
    components: {
        SelectCity,
        // ZjOldAccount
    },
    data() {
        return {
            areaName: '',
            showInfo: false,
            showPopup: false,
            choseCity: '',
            switchChecked: false,
            supportType: 0, //设备支持的指纹或人脸类型 1指纹2人脸
            provincesAndCitiesList: [],
            cancelInfo: {
                leftCancelTimes: 0,
                limitCancelTimes: 0,
            },
            addressName: '',
            showModalType: 0,
            cancelBtnShow: true,
            changePlace: true,
        };
    },
    onLoad() {
        // 获取省市地区
        this.getProvincesCities();
    },
    created() {},
    async mounted() {
        this.refreshPullDown();
        this.supportType = await this.$cnpcBridge.getSupportType();
        if (this.supportType) {
            this.switchChecked = await this.$sKit.commonUtil.judgeBiometricPayStatus();
        }
    },
    computed: {
        ...mapGetters(['memberAccountInfo', 'walletInfo', 'walletStatus']),
        ...mapState({
            isTransfer: state => state.wallet.isTransfer, //是否显示弹窗
        }),
    },

    methods: {
        toRemainingClick() {
            this.$sKit.commonUtil.eWalletNormal(
                () => {
                    this.$sKit.layer.useRouter('/packages/third-remaining-sum/pages/home/<USER>', {}, 'navigateTo');
                },
                [9, 10],
                res => {
                    this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                },
            );
        },

        //前往其他页面
        toDetailPage(val) {
            let url = '';
            switch (val) {
                case 1:
                    url = '/packages/third-coupon-module/pages/coupon-list/main';
                    break;

                // case 2:
                //   url = '/packages/third-oil-card/pages/my-card/main'
                //   break;

                case 3:
                    url = '/packages/third-hospitality-coin/pages/home/<USER>';
                    break;

                case 4:
                    url = '/packages/third-integral/pages/home/<USER>';
                    break;

                default:
                    break;
            }
            if (val == 2) {
                this.$sKit.commonUtil.eWalletNormal(
                    () => {
                        this.$sKit.layer.useRouter('/packages/third-oil-card/pages/my-card/main', {}, 'navigateTo');
                    },
                    [9, 10],
                    res => {
                        this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                    },
                    1,
                );
            }
            this.$sKit.layer.useRouter(url, {}, 'navigateTo');
        },
        //刷新
        refreshPullDown() {
            //会员的油卡，电子券，积分和余额信息，七日内余额等信息
            this.$store.dispatch('basicCouponAction');
            this.$store.dispatch('getAccountBalanceAction');
            this.$store.dispatch('getSetWalletStatus');
            this.getaccountLeftCancelTimesQuery();
            // this.initDate()
        },
        //获取信息
        async initDate() {
            let res = await balance();
            if (res && res.success) {
                this.walletInfo = res.data;
                console.log(res, 'res============balance');
            }
        },
        //获取注销次数
        async getaccountLeftCancelTimesQuery() {
            let res = await accountLeftCancelTimesQuery({}, { isCustomErr: true });
            this.$refs.pullDownRefreshRef.stopRefresh();
            if (res && res.success) {
                this.cancelInfo = res.data;
                // this.cancelInfo.leftCancelTimes = 0
            }
        },
        //获取城市列表
        async getProvincesCities() {
            let res = await provinceAndCityList();
            if (res.success) {
                let arr2 = res.data.map(item => {
                    let newItem = {
                        name: item.parentAreaName,
                        code: item.parentAreaCode,
                        city: item.areaList,
                    };
                    return newItem;
                });
                this.provincesAndCitiesList = arr2;
            }
        },
        //开通指纹/人脸
        async openOrUpdateBiometricCode() {
            uni.showLoading();
            let commonArgs = await this.$cnpcBridge.getCommonArgs();
            this.$accountCenter.openOrUpdateBiometricCode(
                {
                    biometricCode: commonArgs.deviceId,
                    type: this.supportType,
                },
                res => {
                    uni.hideLoading();
                    if (res.isSuccessed) {
                        uni.showToast({
                            title: '开通成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        this.switchChecked = true;
                    } else {
                        // this.$toast(res.desString || '开通失败')
                        uni.showToast({
                            title: res.desString || '开通失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                },
            );
        },
        closeBiometricCode() {
            this.$accountCenter.closeBiometricCode({ type: this.supportType }, res => {
                if (res.isSuccessed) {
                    // this.$toast('关闭成功')
                    uni.showToast({
                        title: '关闭成功',
                        icon: 'none',
                        duration: 2000,
                    });
                    this.switchChecked = false;
                } else {
                    // this.$toast(res.desString || '关闭失败')
                    uni.showToast({
                        title: res.desString || '关闭失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        },
        //人脸或指纹
        async operatePayState() {
            let denied = await this.$cnpcBridge.getSupportType(true);
            if (!this.$sKit.test.isAndroidClient() && denied) {
                this.$store.dispatch('zjShowModal', {
                    content: '请检查"能源e站"是否开启使用面容ID权限。',
                    confirmText: '去检查',
                    cancelText: '取消',
                    cancelColor: '#666666',
                    success: res => {
                        if (res.confirm) {
                            this.$cnpcBridge.openPermissionsSetting();
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }
            //调起验证
            this.$cnpcBridge.localAuthVerifica(res => {
                if (res && Number(res)) {
                    //验证成功
                    if (!this.switchChecked) {
                        setTimeout(() => {
                            this.openOrUpdateBiometricCode();
                        }, 1000);
                    } else this.closeBiometricCode();
                } else {
                    if (!this.switchChecked) {
                        uni.showToast({
                            title: '开通失败,请在30秒后重新开通',
                            icon: 'none',
                            duration: 2000,
                        });
                    } else {
                        uni.showToast({
                            title: '关闭失败,请在30秒后重新关闭',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                }
            });
        },
        selectPlaceOfDeposit() {
            this.showPopup = true;
        },
        async addressModifyClick(code) {
            let res = await addressModify({ city: code });

            if (res && res.success) {
                console.log('用户点击确定');
                this.areaName = this.choseCity;
                await this.$store.dispatch('getAccountBalanceAction');
                this.$sKit.layer.showToast({
                    title: '更换成功',
                });
            } else {
                //给个提示
                this.$sKit.layer.showToast({
                    title: res.message,
                });
            }
        },
        // 修改支付密码
        changePassword() {
            this.showModalType = 1;

            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                confirmColor: '#E64F22',
                success: res => {
                    if (res.confirm) {
                        uni.showLoading();
                        this.$sKit.commonUtil.cardOperationPwd(res => {
                            //验证通过，进一步操作
                            this.$accountCenter.changePW(res => {
                                uni.hideLoading();
                                if (res.isSuccessed) {
                                    uni.showToast({
                                        title: '密码修改成功',
                                        icon: 'none',
                                        duration: 2000,
                                    });
                                } else {
                                    uni.showToast({
                                        title: res.desString || '密码修改失败',
                                        icon: 'none',
                                        duration: 2000,
                                    });
                                }
                            });
                        });
                    } else if (res.cancel) {
                    }
                },
            });
        },
        sureSelectArea(val) {
            this.showPopup = false;
            this.showModalType = 0;
            this.choseCity = val.detail.province + ' ' + val.detail.city;
            if (this.showModalType) {
                return;
            }
            this.$store.dispatch('zjShowModal', {
                confirmText: '确认',
                cancelText: '取消',
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        this.addressModifyClick(val.detail.cityCode);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        hideShow() {
            this.showPopup = false;
        },

        showInfoClick(val) {
            this.showInfo = !this.showInfo;
        },
        toDetail(num) {
            if (num == 4 && !this.cancelInfo.leftCancelTimes) {
                return;
            }

            if (!this.walletInfo.leftChangeCount && num == 1) {
                return;
            }
            switch (num) {
                case 1:
                    this.$sKit.commonUtil.eWalletNormal(res => {
                        this.selectPlaceOfDeposit();
                    }, []);
                    // if (this.walletStatus.status && this.walletStatus.accountStatus != '3') { }
                    break;
                case 2:
                    this.$sKit.mixApi.mixChangePW();
                    break;
                case 4:
                    this.$sKit.commonUtil.eWalletNormal(
                        res => {
                            this.$sKit.layer.useRouter(
                                '/packages/third-my-wallet/pages/card_cancel/main',
                                { cancelInfo: this.cancelInfo },
                                'navigateTo',
                            );
                        },
                        [9],
                        res => {
                            this.$store.dispatch('getIdentityInformationAndElectronicCardData');
                        },
                    );
                    break;
                default:
                    break;
            }
        },

        //前往忘记密码页面
        toDetailPW() {
            this.$sKit.layer.useRouter('/packages/third-my-wallet/pages/forgot-password/main', { isClose: 1 }, 'navigateTo');
        },
        switch2Change: function (e) {
            if (this.supportType) {
                this.operatePayState();
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.remaining_div {
    width: 100%;
    position: relative;
    height: 100%;

    // display: flex;
    // flex-direction: column;
    .bg-page {
        width: 100%;
        height: 476px;
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        // z-index: 1;
    }

    // overflow: hidden;
    // .remaining_div_top {
    //   height: 407px;
    //   width: 100%;
    //   position: absolute;
    //   top: 0;

    //   .card_bg {
    //     height: 407px;
    //     // overflow: hidden;
    //     background-image: linear-gradient(180deg,
    //         #ff9764 -53%,
    //         #ff4e0d 53%,
    //         rgba(255, 78, 13, 0) 100%);

    //     .mask_div {
    //       height: 100%;
    //       // background-image: linear-gradient(0deg, #ffffff, #ffffff8a);
    //       // background: linear-gradient(180deg, #f2f3f5 50%, #F7F7FB 100%);
    //       background-image: linear-gradient(to top,
    //           #f7f7fb 0%,
    //           rgb(255 255 255 / 10%) 70%);
    //       // opacity: 0.5;
    //       top: 0;
    //       position: absolute;
    //       z-index: 2;
    //     }
    //   }
    // }

    .content_div {
        // flex: 1;
        // position: absolute;
        // width: 100%;
        // z-index: 10;
        padding-top: 15px;
        padding: 16px 16px 0 16px;

        // background: #f2f3f5;
        .card_top {
            height: 153px;
            background: #ffffff;
            box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.07);
            border-radius: 8px;
            padding: 16px 13px;
            .card_top_div {
                .my_blance {
                    img {
                        width: 17px;
                        height: 11px;
                        margin-left: 6px;
                    }
                    div {
                        line-height: 21px;
                    }
                }
                .blance_amount {
                    line-height: 34px;
                }
            }
            .card_top_bot {
                margin-top: 19px;
                box-sizing: border-box;
                .item_div {
                    width: 25%;
                    .title {
                        line-height: 22px;
                        text-align: center;
                    }
                    .val {
                        line-height: 22px;
                        margin-top: 5px;
                        text-align: center;
                    }
                }
            }
        }
        .set_div {
            margin-top: 12px;
            padding: 4px 12px;
            .set_div_item {
                width: 100%;
                //  这个样式为bug表中要求的"提示信息左对齐"的效果
                .set_div_item_left {
                    width: 93%;
                    .item_title {
                        width: 110px;
                        color: #333333;
                    }
                }
                .right_btn_div {
                    .right_btn {
                        width: 8px;
                        height: 8px;
                        box-sizing: border-box;
                        border: solid #999;
                        border-width: 0 1px 1px 0;
                        transform: rotate(-45deg);
                    }
                }
            }
        }
    }

    // .zj-pull-down-refresh {
    //   position: absolute;
    //   top: 0;
    // }
}

.div_bot {
    padding: 32px 0 22px 0;

    div {
        text-align: center;
    }
}

.disabled {
    .set_div_item_left {
        .item_title {
            color: #999999 !important;
        }
    }
}

.tc_div {
    .title {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        line-height: 23px;
        padding: 0 30px;
        text-align: center;
        margin-top: 6px;
    }

    .number_of_times {
        font-size: 10px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        flex-wrap: wrap;
        margin-bottom: 16px;
        justify-content: center;

        div {
            color: #e64f22;
        }
    }

    .text {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
    }
}

.guide {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.66);

    .heightLight {
        position: absolute;
        width: 100%;

        .guideImg {
            position: absolute;
            top: 67rpx;
            left: 0;
            width: 100%;
            height: 400rpx;
        }

        .guideButton {
            position: absolute;
            top: 490rpx;
            left: 546rpx;
            font-size: 28rpx;
            line-height: 51rpx;
            text-align: center;
            width: 165rpx;
            height: 51rpx;
            background: #ffffff;
            border-radius: 34rpx;
            border: 2rpx solid #ffffff;
        }
    }
}
.img_style {
    width: 42px;
    height: 23px;
    margin-right: 5px;
}
.remaining_content_div {
    min-height: 0;
}
.text_title {
    padding-bottom: 5px;
}
.padding_12 {
    padding: 12px 0;
}
.padding_6 {
    padding: 8px 0;
}
</style>
