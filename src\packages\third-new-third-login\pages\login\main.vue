<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="one_lick_login">
        <img class="header_icon" :style="TOP" src="../../images/back_x.png" @click="back()" alt />
        <div>
            <img class="login_header" src="../../images/login_header.png" alt />
        </div>
        <div class="padding_input">
            <!-- <div class="main_heading font-18 weight-bold color-333">请输入手机号</div> -->
            <input type="tel" v-model="phone" maxlength="11" class="input_div border-rad-4 bg-fff" placeholder="请输入手机号" />
            <div class="agreement_div color-999 font-12 fl-row">
                <div class="imgWrap fl-al-cen fl-row">
                    <img @click="changeSelect" class="unSelect" :src="isItSelected" alt />
                </div>
                <div class="textWrap">
                    <div class="font-12" @click="changeSelect"> 我已阅读并同意能源e站 </div>
                    <div class="color-E64F22 font-12" @click="clickXieyi(17)">《用户协议》</div>和
                    <div class="color-E64F22 font-12" @click="clickXieyi(63)">《隐私政策》</div>, 未注册的手机号将自动注册能源e站
                </div>
            </div>
            <div class="primary-btn2 btnCode" @click="obtainVerificationCode()">获取验证码</div>
        </div>
        <ImageVerCode v-if="sliderIsShow" :phone="phone" @changeShow="changeShow" @sendSuccess="sendSuccess"></ImageVerCode>
        <zj-show-modal>
            <div style="display: inline"
                >登录能源e站，请先阅读并同意能源e站<view
                    class="font-12 color-E64F22 weight-400 agreement_name"
                    @click="clickXieyi(17)"
                    >《用户协议》</view
                ><view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view></div
            >
        </zj-show-modal>
    </div>
</template>
<script>
import platform from '@/s-kit/js/platform';
import ImageVerCode from '../../components/Image-verification-code/main.vue';
import projectConfig from '../../../../../project.config';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'Hello-Word',
    components: {
        ImageVerCode,
    },
    props: {},
    data() {
        return {
            //系统导航栏高度
            systemBar: '',
            // 是否同意协议
            isAgree: false,
            // 手机号
            phone: '',
            // 是否显示滑块验证弹窗
            sliderIsShow: false,
            // 未勾选图片
            isItSelected: require('../../images/not_selected.png'),
            // 勾选图片
            select: require('../../images/selected.png'),
            unSelect: require('../../images/not_selected.png'),
        };
    },
    computed: {
        TOP() {
            return `margin-top: ${14 + Number(this.systemBar)}px`;
        },
    },
    created() {},
    mounted() {
        // #ifndef MP-MPAAS
        let systemInfo = uni.getSystemInfoSync();
        console.log('systemInfo---', systemInfo);
        // 判断运行的环境
        if (platform.isAlipay || platform.isTouTiao) {
            console.log('systemInfo.statusBarHeight----', systemInfo.statusBarHeight);
            this.systemBar = systemInfo.statusBarHeight + Number(this.navH);
        } else {
            this.systemBar = systemInfo.statusBarHeight;
        }
        // #endif
        this.$sKit.mpBP.tracker('登录/注册', {
            seed: 'loginOrRegistBiz',
            pageID: 'SmsVerCodePage', // 页面名
            channelID: projectConfig.clientCode, // C10/C12/C13
        });
    },
    methods: {
        /**
         * @description  : 是否勾选协议
         * @param         {Boolean} -isAgree 勾选协议标识
         * @return        {*}
         */
        changeSelect() {
            this.isAgree = !this.isAgree;
            if (this.isAgree) {
                this.isItSelected = this.select;
            } else {
                this.isItSelected = this.unSelect;
            }
        },
        /**
         * @description  : 发送验证码
         * @param         {String} -phone 手机号
         * @param         {Boolean} -isAgree 勾选用户协议和隐私政策
         * @return        {*}
         */
        async obtainVerificationCode() {
            if (this.phone == '') {
                uni.showToast({
                    title: '手机号不能为空',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.$test.checkMobilePhoneNumber(this.phone)) {
                uni.showToast({
                    title: '请输入正确的手机号码',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.isAgree) {
                this.$store.dispatch('zjShowModal', {
                    confirmText: '同意',
                    cancelText: '我再想想',
                    cancelColor: '#666666',
                    confirmColor: '#FF3E00',
                    success: async res => {
                        if (res.confirm) {
                            this.isAgree = true;
                            this.isItSelected = this.select;
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    },
                });
                return;
            }
            this.$sKit.mpBP.tracker('登录/注册', {
                seed: 'loginOrRegistBiz',
                pageID: 'SmsVerCodeBut', // 页面名
                channelID: projectConfig.clientCode, // C10/C12/C13
            });
            this.sliderIsShow = true;
        },

        /**
         * @description  : 关闭滑块验证
         * @param         {*}
         * @return        {*}
         */
        changeShow() {
            this.sliderIsShow = false;
        },
        /**
         * @description  : 验证码发送成功
         * @param         {String} -mobile 手机号
         * @param         {String} -messageCode 验证码
         * @return        {*}
         */
        sendSuccess() {
            let params = {
                mobile: this.phone, // 手机号
                messageCode: this.smsVerifyCode,
            };
            let url = `/packages/third-new-third-login/pages/enter-verification-code/main`;
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 返回上一页
         * @param         {*}
         * @return        {*}
         */
        back() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                wx.exitMiniProgram({
                    success: res => {
                        console.log(res);
                    },
                });
            } else {
                uni.navigateBack();
            }
        },
        /**
         * @description  : 查看协议
         * @param         {*}
         * @return        {*}
         */
        clickXieyi(values) {
            if (values == 17) {
                this.getAgreeOn('5', 'App用户使用协议');
            } else if (values == 63) {
                this.getAgreeOn('1', '微信小程序隐私协议');
            }
        },
        /**
         * 隐私政策/用户协议
         * @param type 1.服务协议 2.隐私协议协议 3.授权协议 4.业务协议 5.能源e站APP用户
         * @param name App用户使用协议 App隐私协议 电子钱包开通协议 App充值协议
         * @returns {Promise<void>}
         */
         async getAgreeOn(type, name) {
            let params = {
                type: type,
                cityName: '全国',
                name: name,
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    // 打开PDF
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: '未找到该协议' });
            }
        },
    },
    filter: {},
    watch: {},
    destroyed() {
        this.$store.commit('setLoginLoading', false);
    },
};
</script>
<style scoped lang="scss">
.one_lick_login {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
    .header_icon {
        position: absolute;
        width: 16px;
        height: 16px;
        left: 16px;
    }
    .padding_input {
        padding: 16px;
        .main_heading {
            text-align: center;
            margin-bottom: 6px;
        }
        .input_div {
            height: 101rpx;
            width: 100%;
            margin-top: 32rpx;
            padding-left: 16px;
            box-sizing: border-box;
        }

        input::placeholder {
            text-indent: 20px; /* 设置缩进为20px */
        }
        .agreement_div {
            margin-top: 30rpx;
            .imgWrap {
                height: 17px;
            }
            .textWrap {
                width: 96%;
                line-height: 17px;
                div {
                    display: inline-block;
                }
            }
            img {
                width: 26rpx;
                height: 26rpx;
                margin-right: 10rpx;
            }
        }
        .btnCode {
            margin-top: 32rpx;
        }
    }
    .agreement_name {
        display: inline;
    }
}
</style>
