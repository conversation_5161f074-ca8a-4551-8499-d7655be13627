const MAIN_PACKAGE_PATH = 'pages';
const SUB_PACKAGE_PATH = 'packages';
const SRC_PATH = '../src';
const BUILD_PATH = '../build';

const fs = require('fs');
const path = require('path');
const projectConfig = require('./config');
const manifestConfig = requireMkSync('.manifest.json');
const compileMode = require('./compile-mode/compile-mode.js');

function getFullPath(_path, _root = SRC_PATH) {
    return path.resolve(__dirname, _root, _path);
}

function requireMkSync(_path, _root) {
    const fullPath = getFullPath(_path, _root);
    if (fs.existsSync(fullPath)) {
        return require(fullPath);
    } else {
        return {};
    }
}

function stringifyFormat(data) {
    return JSON.stringify(data, null, 4);
}

function writeFileWithDirectory(filePath, data) {
    const directory = path.dirname(filePath);
    if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
    }
    fs.writeFileSync(filePath, data);
}

// 页面配置插入
// type 为 main 时pageConfig传入页面配置
// type 为 sub 时pageConfig传入带root的页面配置
const insertPageConfig = (pageConfig, type = 'main') => {

    let pagePath;
    if (type == 'main') {
        pagePath = path.resolve(__dirname, SRC_PATH, pageConfig.path + '.json');
    } else if (type == 'sub') {
        pagePath = path.resolve(__dirname, SRC_PATH, pageConfig.root, pageConfig.path + '.json');
        delete pageConfig.root;
    }
    try {
        const style = fs.readFileSync(pagePath, 'utf8');
        if (process.env.UNI_PLATFORM === 'mp-mpaas') {
            // 修改JSON数据
            pageConfig.style = JSON.parse(style);
            // 假设我们要添加一个新的usingComponents
            delete pageConfig.style['mp-alipay'].usingComponents['safe-password'];
            delete pageConfig.style['mp-alipay'].usingComponents['ecard-bind'];
            console.log('usingComponents')
            pageConfig.style['mp-alipay'].usingComponents['keyboard-plugin'] = '/opencomponents/safe-password/safe-password';
            pageConfig.style['mp-alipay'].usingComponents['account-plugin'] = '/opencomponents/account-plugin/account-plugin';
        } else if (process.env.UNI_PLATFORM != 'mp-mpaas') {
            pageConfig.style = JSON.parse(style);
        }
    } catch (e) { }
};

// 主包配置处理
const getMainPageConfig = pageName => {
    const path = `${MAIN_PACKAGE_PATH}/${pageName}/main`;
    const pageConfig = {
        path,
    };
    insertPageConfig(pageConfig);
    return pageConfig;
};

// 分包页面处理
const getSubPageConfig = (pageName, root) => {
    // console.log('pageName',pageName)
    const path = `pages/${pageName}/main`;
    const pageConfig = {
        path,
        root,
    };
    insertPageConfig(pageConfig, 'sub');
    return pageConfig;
};

// 分包配置处理
const getSubPackageConfig = packageName => {
    let nameArr = packageName.split('@');
    const packageConfig = {};
    if (nameArr.length > 1) {
        if (nameArr[1] == 'main') {
            packageConfig.root = `${MAIN_PACKAGE_PATH}/${nameArr[0]}`;
            packageConfig.pages = [];
            const pageC = {
                root: packageConfig.root,
                path: 'main',
            };
            insertPageConfig(pageC, 'sub');
            packageConfig.pages.push(pageC);
        }
    } else {
        // console.log('nameArr这里？',)
        try {
            const subPath = path.resolve(__dirname, SRC_PATH, SUB_PACKAGE_PATH, nameArr[0], 'pages');
            // console.log('subPath',subPath)
            const subArr = fs.readdirSync(subPath);
            // console.log('subArr',subArr)
            packageConfig.root = `${SUB_PACKAGE_PATH}/${nameArr[0]}`;
            packageConfig.pages = [];
            for (let i = 0; i < subArr.length; i++) {
                const element = subArr[i];
                packageConfig.pages.push(getSubPageConfig(element, packageConfig.root));
            }
        } catch (err) {
            if (err != 'normal') {
                throw '------------------- ' + '不存在' + packageName + '的分包 -------------------';
            }
        }
    }
    return packageConfig;
};

const pName = process.argv[process.argv.length - 1];
console.log('UNI_PLATFORM==pName==' + pName);
let pConfig = projectConfig.find(item => item.config.name == pName);
if (!pConfig) {
    throw '------------------- ' + '没有找到项目' + pName + ' -------------------';
}
// console.log('pConfig----', pConfig)
pConfig = pConfig.config;
const pagesJson = pConfig.app;
const permission = pConfig.app.permission;
const zfbplugins = pConfig.app.zfbplugins;
const mpassplugins = pConfig.app.mpassplugins;
const wxplugins = pConfig.app.wxplugins;
const wxSubPlugins = pConfig.app.wxSubPlugins;
const pages = pConfig.mainPackages;
const subPages = pConfig.subPackages;
const getFileContentBase = filePath => fs.readFileSync(filePath, 'utf8');
let baseFontStyl = getFileContentBase('./src/assets/stylus/fontCopy.styl');
let overFont = baseFontStyl.replace('baseFontFamily', `${pConfig.baseImgUrl}/uniapp/HarmonyOS_Sans_Condensed_Medium.ttf`);
fs.writeFileSync('./src/assets/stylus/font.styl', overFont);

if (!pages || pages.length == 0) {
    throw '------------------- ' + '请配置主包页面' + pName + ' -------------------';
}

pagesJson.pages = [];
// 主包处理
for (let i = 0; i < pages.length; i++) {
    const element = pages[i];

    // if(element.style)
    pagesJson.pages.push(getMainPageConfig(element));
    let commonPages = pagesJson.pages;
    // if (pConfig.name.includes('zfb')) {
    //   commonPages.forEach(item => {
    //     item.style = {
    //       "mp-alipay": {
    //         "pullRefresh": false,
    //         "defaultTitle": "",
    //         "allowsBounceVertical": "NO",
    //         "transparentTitle": "always",
    //         "titlePenetrate": "YES",
    //       }
    //     }
    //   })
    // }
    // if(process.env.UNI_PLATFORM != 'mp-mpaas'){
    //     commonPages.forEach(item => {
    //         item.style={
    //             "mp-alipay":{
    //                 "pullRefresh": false,
    //                 "defaultTitle": "",
    //                 "allowsBounceVertical": "YES",
    //                 "transparentTitle": "always",
    //                 "titlePenetrate": "YES",
    //             }
    //         }
    //      })
    // }
}
pagesJson.subPackages = [];
// 分包处理
for (let i = 0; i < subPages.length; i++) {
    const element = subPages[i];
    let subPackageItem = getSubPackageConfig(element);
    if (process.env.UNI_PLATFORM === 'mp-weixin') {
        if (element === 'third-helpShare') {
            subPackageItem.plugins = wxSubPlugins.sendCoupon;
        } else if (element === 'third-invoice') {
            subPackageItem.plugins = wxSubPlugins.wxInvoice;
        }
    }
    pagesJson.subPackages.push(subPackageItem);
    // if (pConfig.name.includes('zfb')) {
    //     // console.log('subPages---',pagesJson.subPackages)
    //     let commonSubPackages = pagesJson.subPackages;
    //     commonSubPackages.forEach(item => {
    //         for (let item2 of item.pages) {
    //             item2.style = {
    //                 'mp-alipay': {
    //                     component: true,
    //                     pullRefresh: false,
    //                     defaultTitle: '',
    //                     allowsBounceVertical: 'NO',
    //                     transparentTitle: 'always',
    //                     titlePenetrate: 'YES',
    //                 },
    //             };
    //         }
    //     });
    // }
}
// console.log('----------------- pagesJson -----------------')
// console.log(pagesJson)

/**
 * 写入对应文件
 */
// 设置页面配置
fs.writeFileSync(path.resolve(__dirname, SRC_PATH, 'pages.json'), stringifyFormat(pagesJson));

// 设置微信开启分包优化 (第一步)
manifestConfig['mp-weixin'].optimization = {
    subPackages: true,
};
manifestConfig['mp-weixin'].appid = pConfig.appId;
//处理定位权限
manifestConfig['mp-weixin'].permission = permission;
//处理支付宝插件
console.log('process.env.UNI_PLATFORM:', process.env.UNI_PLATFORM);
// if (process.env.UNI_PLATFORM != 'mp-mpaas') {
//     //   // console.log('=============================')
if (process.env.UNI_PLATFORM == 'mp-mpaas') {
    manifestConfig['mp-alipay'].plugins = mpassplugins;
    manifestConfig['mp-weixin'].plugins = {};
} else {
    manifestConfig['mp-alipay'].plugins = zfbplugins;
    manifestConfig['mp-weixin'].plugins = wxplugins;
}

// 设置uniapp各个端配置
fs.writeFileSync(path.resolve(__dirname, SRC_PATH, 'manifest.json'), stringifyFormat(manifestConfig));
// 设置项目中使用的配置项
fs.writeFileSync(
    path.resolve(__dirname, SRC_PATH, 'project.json'),
    stringifyFormat({
        name: pConfig.name,
        appId: pConfig.appId,
        baseUrls: pConfig.baseUrls,
    }),
);
//导出项目所需配置
fs.writeFileSync('./project.config.js', `module.exports = ${stringifyFormat(pConfig)}`);
// 追加全局组件脚本
let pageStr = fs.readFileSync('./src/pages.json', 'utf-8');
let componentName = '<zj-show-modal></zj-show-modal>';
const addComponent = require('./script/all-page-add-component.js');
addComponent.emit('add', pageStr, componentName);

// let componentName2 = '<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>';
// addComponent.emit('addFirst', pageStr, componentName2);

const mixinsName = 'publicMixinsApi';
const addMixins = require('./script/all-page-add-mixins.js');
addMixins.emit('add', pageStr, mixinsName);
const removeDist = require('./script/removeDist.js')
try {
    removeDist();
} catch (error) {
    console.error(error.message);
}
// 页面镜像 复制页面用
// if(pConfig.mirror) {
//     const entry = pConfig.mirror.entry
//     const output = pConfig.mirror.output
//     for (const key in entry) {
//         if (Object.hasOwnProperty.call(entry, key)) {
//             const inE = entry[key];
//             const outE = output[key];
//             const fileContent = fs.readFileSync(inE ,"utf-8");
//             try {
//                 fs.writeFileSync(outE, fileContent)
//             } catch(e) {
//                 if(e.code == 'ENOENT') {
//                     console.log(outE)
//                     let dirPath = outE.split('\\')
//                     dirPath.pop()
//                     dirPath = dirPath.join('\\')
//                     fs.mkdirSync(dirPath, {recursive: true})
//                     fs.writeFileSync(outE, fileContent)
//                 } else {
//                     throw(e)
//                 }
//             }

//         }
//     }
// }
// 写入项目名称 webpack使用
writeFileWithDirectory(
    path.resolve(__dirname, BUILD_PATH, './storage/project.json'),
    stringifyFormat({
        name: pName,
    }),
);
// 将项目名称写入代码提示
compileMode.wcodePrompt(pName);
process.exit(0);
