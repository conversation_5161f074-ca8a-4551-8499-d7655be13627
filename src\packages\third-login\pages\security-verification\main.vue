<template>
    <div class="security_div">
        <zj-navbar title="安全验证" :border-bottom="false"></zj-navbar>
        <div class="security_content">
            <div class="securityVer_div">
                <img src="../../images/securityVer.png" alt />
                <div class="text">
                    <div class>安全验证</div>
                    <span class="text_span">您正在一台新设备登录</span>
                    <span class>为了您的账号安全，请进行安全验证</span>
                </div>
            </div>
            <div class="primary-btn sign_in" @click="toStart">开始验证</div>
        </div>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'security-verification',
    data() {
        return {};
    },
    methods: {
        getCode() {
            console.log('获取验证码');
        },
        toStart() {
            this.$sKit.layer.useRouter(
                '/packages/third-login/pages/enter-verification-code/main',
                { type1: true, type2: true },
                'navigateTo',
            );
        },
    },
};
</script>

<style lang="scss" scoped>
.security_div {
    background: #f7f7fb;
    .security_content {
        padding: 0 15px;
        margin-top: 16px;
    }
}
.securityVer_div {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    img {
        width: 481rpx;
        height: 408rpx;
    }
    .text {
        display: flex;
        flex-direction: column;
        width: 100%;

        text-align: center;
        div {
            font-weight: 600;
            color: #333333;
            // font-size $font-size-large
            margin-top: 16px;
            font-size: 18px;
        }
        span {
            // color $color-tex-l
            // font-size $font-size-small
            line-height: 17px;
            font-size: 12px;
            color: #999;
        }
        .text_span {
            margin-top: 5px;
        }
        .tips {
            margin-top: 5px;
        }
    }
}
.sign_in {
    margin-top: 16px;
    height: 44px;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    line-height: 44px;
    border-radius: 8px;
}
</style>
