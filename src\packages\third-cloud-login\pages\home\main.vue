<template>
    <view class="page-union">
        <img v-if="numberOfPages != 1" :style="TOP" alt class="header_icon" src="../../../../static/black-close.png"
            @click="back()" />
        <image :src="transferAccountBg" alt="" class="transferAccountBg_style"></image>
        <div class="content_div">
            <view class="container">
                <image :src="logo" class="logo_style"></image>
                <view class="">
                    <view class="title">能源e站</view>
                    <view class="text">e享加油生活</view>
                </view>
            </view>
            <view class="agreement_div">
                <view class="select_div">
                    <image v-if="select" :src="empty" class="" @click="select = false"></image>
                    <image v-else :src="successSel" class="" @click="select = true"></image>
                </view>
                <view class="agreement_text" @click="select = !select">我已阅读并同意能源e站<view
                        class="font-12 color-E64F22 weight-400 agreement_name" @click.stop="clickXieyi(17)">《用户协议》</view>和
                    <view class="font-12 color-E64F22 weight-400 agreement_name" @click.stop="clickXieyi(63)">《隐私政策》</view>
                </view>
            </view>
            <div v-if="!isLogin">
                <view v-if="!select">
                    <cloudLogin @loginOver="loginOver" @click="onCheckAgree()"></cloudLogin>
                </view>
                <button v-if="select" class="primary-btn2" @click="onCheckAgree()">授权登录</button>
            </div>
        </div>
        <zj-show-modal>
            <div style="display: inline">登录能源e站，请先阅读并同意能源e站<view class="font-12 color-E64F22 weight-400 agreement_name"
                    @click="clickXieyi(17)">《用户协议》</view>
                <view class="font-12 color-E64F22 weight-400 agreement_name" @click="clickXieyi(63)">《隐私政策》</view>
            </div>
        </zj-show-modal>
        <view class="footer">如需帮助，可联系：956100</view>
    </view>
</template>

<script>
const app = getApp();
import { mapGetters, mapState } from 'vuex';
import logo from '@/static/img/cnpc-logo.png';
import cloudLogin from '@/components/cloudLogin/cloud-login.vue';
import transferAccountBg from '@/static/transferAccount-bg.png';
// import { setCloud } from '@/s-kit/js/setCloud'
import empty from '@/static/empty.png';
import successSel from '@/static/successSel.png';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    components: {
        cloudLogin,
    },
    data() {
        return {
            logo,
            isLogin: false,
            transferAccountBg,
            systemBar: '',
            numberOfPages: -1,
            empty,
            successSel,
            select: true,
            curTabIndex: '',
        };
    },

    onLoad(options) {
        console.log(options, '云闪付登录接值');
        this.curTabIndex = options.curTabIndex;
    },
    mounted() {
        setTimeout(() => {
            upsdk.pluginReady(() => {
                try {
                    // 调用 upsdk 的 setTitleStyle 方法，设置标题栏样式
                    upsdk.setTitleStyle({
                        appletTitleBarVisible: Number(false),
                        backBtnVisible: Number(false)
                    });
                } catch (error) {
                    // 捕获并处理代码执行过程中的错误，拒绝 Promise 并传递错误信息
                    console.log(`Error in upsdk operations: ${error.message}`);

                    // reject(new Error(`Error in upsdk operations: ${error.message}`));
                }
            });

        }, 2000);
        this.systemBar = app.globalData.systemBar;
        this.numberOfPages = getCurrentPages().length;
    },
    onShow() { },
    methods: {
        async automaticLogon() {
            let res = await this.$store.dispatch('thirdLoginFun', { type: '4' });
            this.loginOver(res);
        },
        // 检查是否同意协议
        onCheckAgree() {
            // my.showToast({
            //     content: '请先同意用户授权协议',
            // });
            this.modalClick();
        },
        modalClick() {
            this.$store.dispatch('zjShowModal', {
                confirmText: '同意',
                cancelText: '我再想想',
                cancelColor: '#666666',
                confirmColor: '#FF3E00',
                success: async res => {
                    if (res.confirm) {
                        this.select = false;
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        loginOver(res) {
            console.log('res--', res);
            if (res.success) {
                if (res.data.newMember) {
                    let url = '/pages/thirdHome/main';
                    let params = { curTabIndex: this.curTabIndex };
                    this.$sKit.layer.useRouter(url, params, 'reLaunch');
                }
            } else {
                this.$store.commit('setLoginButtonGrayedOut', true);
            }
        },
        back() {
            if (this.numberOfPages.length <= 1) {
            } else {
                console.log('2222');
                uni.navigateBack();
            }
        },
        // 勾选协议
        clickXieyi(type) {
            uni.navigateTo({
                url: `/packages/setting/pages/agreement/main?value=${type}`,
            });
        },
    },
    computed: {
        ...mapState({
            // upaskSystem: state => state.thirdIndex.upaskSystem
        }),
        TOP() {
            return `margin-top: ${14 + Number(this.systemBar)}px`;
        },
    },
};
</script>

<style scoped lang="scss">
.page-union {
    width: 100%;
    background: #ffffff;
    overflow-x: hidden;
    // min-height: 100vh;
    height: 100%;

    .header_icon {
        position: fixed;
        width: 16px;
        height: 16px;
        left: 16px;
        top: 16px;
        z-index: 1;
    }
}

.btnStyle {
    width: 100%;
}

.logo {
    width: 140rpx;
    height: 140rpx;
    margin: 200rpx auto 30rpx;
    display: block;
}

.btn-auth-login {
    color: #fff;
    background-color: rgb(51, 107, 225);
    border-radius: 60rpx;

    &:active {
        opacity: 0.7;
    }
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
}

.subtitle {
    font-size: 28rpx;
    color: #888;
    text-align: center;
    margin: 20rpx 0 80rpx;
}

.agreement-box {
    margin-top: 30rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    white-space: nowrap;
    margin-left: -10px;
    margin-bottom: -1px;

    .link {
        color: #1d77cd;
    }
}

.loading-box {
    padding-top: 80rpx;
    display: flex;
    justify-content: center;
    gap: 10rpx;

    .loading-dot {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: #888;
        animation: bolling 1s alternate infinite;
    }
}

.footer {
    position: fixed;
    bottom: 50rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
    width: 100%;
}

@keyframes bolling {
    0% {
        transform: translateY(-5px);
    }

    100% {
        transform: translateY(5px);
    }
}

.transferAccountBg_style {
    width: 100%;
    height: 365px;
    position: absolute;
    z-index: 0;
}

.content_div {
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 152px;
    padding: 0 63px;
    box-sizing: border-box;
}

.logo_style {
    width: 47px;
    height: 45px;
    margin-right: 14px;
}

.container {
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    margin-bottom: 43px;
}

.title {
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
}

.text {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    letter-spacing: 1px;
}

.agreement_div {
    margin-bottom: 16px;
    display: flex;

    .select_div {
        display: inline-block;
        margin-right: 5px;

        image {
            width: 13px;
            height: 13px;
        }
    }
}

.agreement_text {
    width: 94%;
}

.agreement_name {
    display: inline;
}
</style>
