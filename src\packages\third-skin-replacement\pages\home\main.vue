<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="card_skin">
            <zj-navbar :border-bottom="false" title="设置皮肤"></zj-navbar>
            <div class="current_skin" id="current_skin" ref="current_skin_height">
                <!-- <img v-else mode="widthFix" src="../../images/card-default.png" alt class="this_current_skin_img" /> -->
                <div class="image_div">
                    <img :src="walletSkin.extFileUrl ? walletSkin.extFileUrl : walletSkin.fileUrl" alt class="this_current_skin_img" />
                    <img src="../../images/current_skin.png" alt class="this_current_skin" />
                </div>
            </div>
            <div class="card_skin_list">
                <div ref="card_skin_div" id="skin_tab" :class="{ skin_tab: true }">
                    <div :class="{ select_div: active == 1 }" @click="changeTab(1)">
                        <div class="title">我的皮肤</div>
                    </div>
                    <div :class="{ select_div: active == 2 }" @click="changeTab(2)">
                        <div class="title">免费专区</div>
                    </div>
                </div>
                <div :class="{ 'data-list': true, noDataFlex1: true }">
                    <zj-data-list
                        ref="list"
                        @refreshPullDown="refreshPullDown"
                        @scrolltolower="scrolltolower"
                        emptyText="暂未获取任何皮肤"
                        :showEmpty="showEmpty"
                        :emptyImage="require('../../images/kt12pf.png')"
                    >
                        <div class="skin_list" v-if="active == 1">
                            <div class="list_div" v-for="(item, index) in mySkinList" :key="index">
                                <img :src="item.extFileUrl ? item.extFileUrl : item.fileUrl" alt />
                                <div class="content_div">
                                    <div class="title">{{ item.skinTitle }}</div>
                                    <div class="endTime">有效期至 {{ item.effectEndDate.slice(0, 10) }}</div>

                                    <div v-if="walletSkin.skinId != item.skinId" class="useskinBtn" @click="useskin(item, false)"
                                        >使用皮肤</div
                                    >
                                    <div v-else class="useskinBtn defaultskinBtn" @click="defaultskin">恢复默认</div>
                                </div>
                            </div>
                        </div>
                        <div class="skin_list" v-else>
                            <div class="list_div" v-for="(item, index) in freeSkinList" :key="index">
                                <img :src="item.extFileUrl ? item.extFileUrl : item.fileUrl" alt />
                                <div class="content_div">
                                    <div class="title">{{ item.skinTitle }}</div>
                                    <div v-if="walletSkin.skinId != item.id" class="useskinBtn" @click="useskin(item, true)">使用皮肤</div>
                                    <div v-else class="useskinBtn defaultskinBtn" @click="defaultskin">恢复默认</div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
            </div>
            <div v-if="toastShow" class="toast_div fl-row fl-al-jus-cen">
                <u-icon name="checkmark-circle-fill" size="30" color="#0EB375"></u-icon>
                <div>设置皮肤成功</div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import Vue from 'vue';
import {
    skinSet,
    skinSetDefault,
    skinList,
    queryFreeApi,
    skinDetailApi,
    skinQueryFree,
    currentUsed,
} from '../../../../s-kit/js/v3-http/https3/user';
import CONFIG from '../../../../s-kit/js/third-config.js';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'setSkin',
    data() {
        return {
            active: 1,
            // 页码
            page: 1,
            mySkinList: [],
            freeSkinList: [],
            tabTop: 0,
            isTop: false,
            showEmpty: false,
            isRefresh: true,
            toastShow: false,
            currentUsed: '',
            currentUsedImg: '',
            currentUsedId: '',
            skinCurrentId: [],
        };
    },
    onLoad(options) {
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'skinPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
    },
    created() {
        // 计算吸顶top位置
        this.bgTopStyle();
    },
    mounted() {
        // // 获取我的皮肤列表
        // this.getSkinList({ isInit: true });
        // // #ifdef MP-MPAAS
        // this.$store.dispatch('getCurrentImg');
        // // #endif
        this.refreshPullDown();
    },
    computed: {
        ...mapGetters(['walletSkin']),
    },
    methods: {
        // 获取皮肤列表
        async getSkinList({ isInit = false } = {}) {
            if (this.active == 1) {
                let res = await skinList({});
                // uni.hideLoading();
                if (res.success) {
                    console.log(res, 'skinList=====');
                    if (res.data.length) {
                        this.mySkinList = res.data;
                        res.data.forEach(item => {
                            this.mySkinList.forEach(infoItem => {
                                if (item.skinId == infoItem.skinId) {
                                    infoItem.effectEndDate = item.effectEndDate;
                                }
                            });
                        });
                    }
                    this.$refs.list.loadStatus = 'nomore';
                    if (this.mySkinList.length > 0) {
                        this.showEmpty = false;
                    } else {
                        this.showEmpty = true;
                    }
                    return;
                    uni.hideLoading();
                    if (res.data.length > 0) {
                        let skinListIds = res.data.map(item => {
                            return item.skinId;
                        });
                        // #ifdef MP-MPAAS
                        skinListIds = skinListIds;
                        // #endif
                        // #ifdef MP-ALIPAY
                        // #endif
                        // #ifdef MP-WEIXIN
                        skinListIds = skinListIds.join(',');
                        // #endif
                        let infoRes = await skinDetailApi({
                            skinIds: skinListIds,
                            path: 'PUBLIC_CLOUD',
                        });
                        // #ifndef MP-MPAAS
                        // TODO 支付宝获取皮肤列表数据字段映射
                        if (JSON.stringify(CONFIG.name).includes('-zfb')) {
                            infoRes.result = infoRes.InfoCode == 1 ? 'success' : 'fail';
                            infoRes.data = infoRes.Data;
                        }
                        // #endif
                        this.$refs.list.stopRefresh();
                        uni.hideLoading();
                        if (infoRes.result == 'success') {
                            this.mySkinList = infoRes.data;
                            res.data.forEach(item => {
                                this.mySkinList.forEach(infoItem => {
                                    if (item.skinId == infoItem.skinId) {
                                        infoItem.effectEndDate = item.effectEndDate;
                                    }
                                });
                            });
                            this.$refs.list.loadStatus = 'nomore';
                        }
                        uni.hideLoading();
                    }
                }
            } else {
                if (isInit) {
                    Object.assign(this, {
                        freeSkinList: [],
                        page: 1,
                    });
                    this.$refs.list.loadStatus = 'loading';
                }
                console.log('this--freeSkinList',this.freeSkinList)
                let params = {
                    pageNo: this.page,
                    pageSize: 10,
                };
                let res = await skinQueryFree(params);
                if (res.success) {
                    console.log(res, 'skinQueryFree=====');
                    if (res.data.dataList.length) {
                        this.freeSkinList = this.freeSkinList.concat(res.data.dataList);
                    }
                    this.page = Number(this.page) + 1;
                    if (this.page > res.data.totalPages) {
                        this.$refs.list.loadStatus = 'nomore';
                    } else {
                        this.$refs.list.loadStatus = 'contentdown';
                    }
                    if (this.freeSkinList.length > 0) {
                        this.showEmpty = false;
                    } else {
                        this.showEmpty = true;
                    }
                }
                return;
                // #ifndef MP-MPAAS
                // TODO 支付宝获取免费皮肤列表数据字段映射
                if (JSON.stringify(CONFIG.name).includes('-zfb')) {
                    res.result = res.InfoCode == 1 ? 'success' : 'fail';
                    res.data = res.Data;
                }
                // #endif
                console.log('res.data--',res.data)
                if (res.result == 'success' && res.data) {
                    // #ifdef MP-MPAAS
                    this.freeSkinList = this.freeSkinList.concat(res.data.dataList);
                    // #endif
                    // #ifdef MP-ALIPAY
                    this.freeSkinList = this.freeSkinList.concat(res.data.dataList);
                    // #endif
                    // #ifdef MP-WEIXIN
                    this.freeSkinList = this.freeSkinList.concat(res.data.skinList);
                    // #endif
                }

                this.page = Number(this.page) + 1;
                if (this.page > res.data.totalPages) {
                    this.$refs.list.loadStatus = 'nomore';
                } else {
                    this.$refs.list.loadStatus = 'contentdown';
                }
                if (this.freeSkinList.length > 0) {
                    this.showEmpty = false;
                } else {
                    this.showEmpty = true;
                }
                uni.hideLoading();
            }
        },
        //加载
        scrolltolower() {
            if (this.$refs.list.loadStatus == 'contentdown') {
                this.$refs.list.loadStatus = 'loading';
                this.getSkinList();
            }
        },
        //刷新
        refreshPullDown() {
            this.$store.dispatch('getCurrentImg', {
                callback: () => {
                    this.$refs.list.stopRefresh();
                },
            });
            this.getSkinList({ isInit: true });
        },
        // 更新当前使用皮肤
        refreshSkin() {
            this.$store.dispatch('getCurrentImg', () => {});
        },
        // 计算吸顶top位置
        bgTopStyle() {
            // 获取系统宽高信息
            let systemInfo = uni.getSystemInfoSync();
            this.tabTop = systemInfo.statusBarHeight + 44;
        },
        //tab按钮切换
        changeTab(num) {
            this.active = num;

            this.getSkinList({ isInit: true });
            this.backTop();
        },
        // 将页面滚动到目标位置
        backTop() {
            uni.pageScrollTo({
                scrollTop: 0,
                duration: 300,
            });
        },
        // 获取tab按钮元素距离顶部高度，控制样式
        watchTabTop() {
            const query = uni.createSelectorQuery().in(this);
            query
                .select('#skin_tab')
                .boundingClientRect(data => {
                    if (data.top == this.tabTop) {
                        this.isTop = true;
                    } else {
                        this.isTop = false;
                    }
                })
                .exec();
        },
        // 获取皮肤主图元素距离顶部高度，控制样式
        watchImgTop() {
            const query = uni.createSelectorQuery().in(this);
            query
                .select('#current_skin')
                .boundingClientRect(data => {
                    if (data.top >= this.tabTop) {
                        this.isRefresh = true;
                    } else {
                        this.isRefresh = false;
                    }
                })
                .exec();
        },
        //设置皮肤
        async useskin(item, freeSkin) {
            let res = await skinSet({
                skinId: this.active === 1 ? item.skinId : item.id,
                freeSkin: freeSkin,
            });
            if (res.success) {
                this.refreshSkin();
                this.toastShow = true;
                this.$store.dispatch('selectWalletSkinAction', item);
                setTimeout(() => {
                    this.toastShow = false;
                }, 3000);
            }
        },
        //恢复默认皮肤
        async defaultskin() {
            let res = await skinSetDefault();
            if (res.success) {
                this.refreshSkin();
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.card_skin {
    height: 100%;
    width: 100%;
    background: #f7f7fb;
    display: flex;
    flex-direction: column;

    .current_skin {
        padding: 30rpx 0;
        width: 100%;
        // position: relative;
        text-align: center;
        .image_div {
            position: relative;
            width: 343px;
            left: 50%;
            transform: translateX(-50%);
            .this_current_skin_img {
                width: 343px;
                height: 217px;
                border-radius: 8px;
            }

            .this_current_skin {
                width: 130rpx;
                height: 24px;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
    }

    .card_skin_list {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;

        .skin_tab {
            height: 88rpx;
            width: 100%;
            display: flex;
            flex-direction: row;
            // position: sticky;
            align-items: center;
            z-index: 99;

            div {
                width: 100%;
                padding: 6px 0 12px 0;
                text-align: center;
                font-size: 14px;
                font-weight: 400;
                color: #333333;
            }

            .select_div {
                .title {
                    padding: 4px;
                    color: #e64f22;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                }

                .title:after {
                    display: block;
                    position: relative;
                    top: 4px;
                    left: 50%;
                    transform: translateX(-50%);
                    content: '';
                    width: 28%;
                    height: 0px;
                    border-bottom: 2px solid #e64f22;
                }
            }
        }

        .skin_list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            width: 100%;
            padding: 0px 15px;
            // overflow: scroll;
            justify-content: space-between;

            .list_div {
                width: 48%;
                background: #fff;
                border-radius: 8px;
                margin-bottom: 15px;
                padding-bottom: 17px;

                img {
                    width: 100%;
                    height: 104px;
                    border-radius: 5px;
                }

                .content_div {
                    padding: 0 11px;

                    .title {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 14px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 20px;
                        display: block;
                        margin-top: 10px;
                    }

                    .endTime {
                        width: 100%;
                        height: 33rpx;
                        font-size: 12px;
                        font-weight: 400;
                        color: #999999;
                        line-height: 33rpx;
                        margin-top: 5px;
                    }

                    .useskinBtn {
                        height: 28px;
                        background: #e64f22;
                        border-radius: 5px;
                        color: #fff;
                        font-size: 12px;
                        color: #ffffff;
                        line-height: 28px;
                        text-align: center;
                        margin-top: 10px;
                    }

                    .defaultskinBtn {
                        color: #e64f22;
                        background: #ffffff;
                        border: 1rpx solid #e64f22;
                    }
                }
            }
        }
    }
}

.noDataFlex1 {
    min-height: 0;
    flex: 1;
}

.whitBg {
    background: #fff;
}

.toast_div {
    padding: 12px 25px;
    background: #000000;
    border-radius: 10px;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 100;
    transform: translate(-50%, -50%);

    div {
        width: 90px;
        height: 20px;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        margin-left: 8px;
    }
}
</style>
