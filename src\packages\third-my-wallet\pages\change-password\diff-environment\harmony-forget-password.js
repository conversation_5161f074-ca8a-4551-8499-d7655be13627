import { initRealPersonIdentify, realPersonIdentify,realNameAuth } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
export default {
  // #ifdef MP-MPAAS
   onLoad () {
    if(this.isHarmony){
      this.$nextTick(async () => {
        // 获取键盘的实例
        let result = await this.$sKit.keyBordPlugin.initRef();
        // console.log('result',result)
        this.$store.commit('setAccountDataPlugin', result);
        this.passwordKeyboardRef = result;
      });
    }
  },
  mounted () { },
  methods: {

    // 确认修改
    async confirmBtn () {
      if (!this.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none',
        });
        return;
      } else {
        // #ifdef MP-MPAAS
        if (this.isHarmony && this.newPasswordShow.length !== 6) {
          uni.showToast({
              title: '请输入6位新密码',
              icon: 'none',
          });
          return;
        }
        // #endif
        // #ifndef MP-MPAAS
      if (this.newPasswordLength !== 6) {
          uni.showToast({
              title: '请输入6位新密码',
              icon: 'none',
          });
          return;
      }
      // #endif
      }
      if (!this.confirmPassword) {
        uni.showToast({
          title: '请再次输入新密码',
          icon: 'none',
        });
        return;
      } else {
      // #ifdef MP-MPAAS
      if (this.isHarmony && this.confirmNewPasswordShow.length !== 6) {
          uni.showToast({
              title: '请输入6位新密码',
              icon: 'none',
          });
          return;
      }
      // #endif
      // #ifndef MP-MPAAS
      if (this.confirmNewPasswordLength !== 6) {
          uni.showToast({
              title: '请再次输入6位新密码',
              icon: 'none',
          });
          return;
      }
      // #endif
      }
      // if (samepassword == 1) {
      //   this.newPassword = '';
      //   this.newPasswordShow = '';
      //   this.confirmNewPassword = '';
      //   this.confirmNewPasswordShow = '';
      //   uni.showToast({
      //     title: '两次密码不一致，请重新输入',
      //     icon: 'none',
      //   });
      //   return;
      // }
      //   let paramsdata = {
      //     realName: this.personInfo.name,
      //     idType: '1',
      //     idNo: this.personInfo.idNo,
      //     authInfo: '',
      //     type: '2',
      //   };
      //   let res = await realNameAuth(paramsdata);
      //   if (res && res.success) {
      //     this.authInfo = res.data.authInfo;
          // 打开人脸认证协议弹窗
          this.$store.dispatch('changeFacePop', true)
      // }
      
    },
    enterNavEvent () {
      // 关闭人脸认证协议弹窗
      this.$store.dispatch('changeFacePop', false)
      // 支付宝小程序实名--初始化实人认证--人脸验证-实名认证方法
      // #ifdef MP-MPAAS
      if(this.isHarmony){
        this.$sKit.harmonyRealPersonAuthentication.startVerification(this.personInfo).then(response => {
          console.log('链路通过，调用卡通电子账户接口', response);
          if (response) {
            this.submitForgotPassword(response.data.authInfo);
          } else {
          }
        });
      }
      // #endif
    },

  },
  // #endif
};
