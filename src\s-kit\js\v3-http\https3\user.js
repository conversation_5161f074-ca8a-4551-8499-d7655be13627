
import { POST, POST2, PUT } from '../index';
import { api, apiGsms } from '../../../../../project.config';

// 查询当前使用的皮肤
export const currentUsed = (params, config) => {
    return POST('user.skin.currentUsed', params, config);
};
// alipay.mcdp.space.initSpaceInfo 展位初始化接⼝
export const alipayQuerySpaceInfo = (params, config) => {
    return POST('alipay.mcdp.space.querySpaceInfo', params, config);
};

// 获取已领取的皮肤列表
export const skinList = (params, config) => {
    return POST('user.skin.list', params, config);
};
// 设置皮肤
export const skinSet = (params, config) => {
    return POST('user.skin.set', params, config);
};
// 恢复默认皮肤
export const skinSetDefault = (params, config) => {
    return POST('user.skin.setDefault', params, config);
};
// 刷新Token
export const refreshTokenApi = (params, config) => {
    return POST('user.refreshToken', params, config);
};

// 获取隐私协议及条款
export const userAgreement = (params, config) => {
    return POST('user.agreement', params, config);
};

// 获取免费皮肤列表
export const queryFreeApi = (params, config) => {
    // return POST2('/app/json/app_third/skinFree', params, config)

    // #ifdef MP-MPAAS
    return POST2('/app/json/app_third/skinFree', params, config);
    // #endif

    // #ifdef MP-TOUTIAO
    return POST2('/app/json/app_third/skinFree', params, config);
    // #endif

    // #ifdef MP-ALIPAY
    return POST2(api + apiGsms + '/v1/skin/list/queryFree', params, config);
    // #endif

    // #ifdef MP-WEIXIN
    return POST2('/app/json/third/skinQueryFree', params, config);
    // #endif
};
export const appjsonusermodifyPwd = (params, config) => {
    return POST2('/app/json/user/modifyPwd', params, config);
};

// 查询皮肤详情
export const skinDetailApi = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/app_third/skinDetail', params, config);
    // #endif
    // #ifdef MP-ALIPAY || MP-TOUTIAO
    return POST2(api + apiGsms + '/v1/skin/list/detail', params, config);
    // #endif
    // #ifdef MP-WEIXIN
    return POST2('/app/json/third/skinDetail', params, config);
    // #endif
};


// 油卡充值方式
export const oilCardRecharge = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/app_vue_page/getAreaRecharge', params, config);
    // #endif
};
// sdk访问2.0App
export const getCppeiLoginInfoApi = (params, config) => {
    return POST('user.getCppeiLoginInfo', params, config);
};
export const cardRecharge = (params, config) => {
    // #ifdef MP-MPAAS
    return POST2('/app/json/card/recharge', params, config);
    // #endif
};

// /app/json/user/modifyHeadImg 修改用户头像

export const modifyHeadImgApi = (params, config) => {
    return POST2('/app/json/user/modifyHeadImg', params, config);
};
// /app/json/user/modifyUserInfo 修改用户信息

// 用户管理_上传用户头像
// export const imageUpload = (params, config) => {
//     return POST('user.imageUpload.tempPath', params, config);
// };

// 获取图片上传临时url接口 /order/image/getTempUploadUrl
export const imageUpload = (params, config) => {
    return POST('order.image.getTempUploadUrl', params, config);
};
// 上传oss
export const putUpload = (url, params, config) => {
    console.log('config---s', config);
    return PUT(url, params, config);
};
// 获取图片临时认证信息接口 /order/image/getTempPostInfo
export const imageGetTempPostInfo = (params, config) => {
    return POST('order.image.getTempPostInfo', params, config);
};
//[获取删除图片临时url接口 /order/image/getTempDeleteUrl]
export const imageDelete = (params, config) => {
    return POST('order.image.getTempDeleteUrl', params, config);
};
//获取图片下载临时url接口 /order/image/getTempDownloadUrl]
export const imageDownload = (params, config) => {
    return POST('order.image.getTempDownloadUrl', params, config);
};
export const modifyUserInfoApi = (params, config) => {
    return POST('user.basicInfo.modify', params, config);
};
// user.logout  退出登录
export const logoutApi = (params, config) => {
    return POST('user.logout', params, config);
};
// 查看登录设备
export const queryLoginDevicesApi = (params, config) => {
    return POST2('/app/json/login/queryLoginDevices', params, config);
};

// 会员登录设备查询接口
export const queryLoginRecordApi = (params, config) => {
    return POST('user.queryLoginRecord', params, config);
};
// 用户管理_用户注销
export const cancelAccountApi = (params, config) => {
    return POST('user.cancelAccount', params, config);
};
// 生物识别开通状态
export const biometricPayStatus = (params, config) => {
    return POST('account.biometricPayStatus', params, config);
};

// 在线客服

export const aiClient = (params, config) => {
    return POST2('/app/json/home/<USER>', params, config);
};
// 开通人脸指纹登录
export const openLocalAuthLogin = (params, config) => {
    return POST2('/app/json/login/openLocalAuth', params, config);
};
// 关闭人脸指纹登录
export const closeLocalAuthLogin = (params, config) => {
    return POST2('/app/json/login/closeLocalAuth', params, config);
};

export const loginRegisterByPhone = (params, config) => {
    // #ifdef MP-ALIPAY
    return POST2(api + 'user/v1/alipay/getRegisterInfoByPhone', params, config);
    // #endif
};

export const loginBindAccount = (params, config) => {
    // #ifdef MP-ALIPAY
    return POST2('/Ali/Api/Alipay/BindPortalAccount', params, config);
    // #endif
};

export const loginToGsms = (params, config) => {
    // #ifdef MP-ALIPAY
    return POST2(api + apiGsms + '/v1/loginToGsms', params, config);
    // #endif
};

// 全渠道登录3.0
export const userLogin = (params, config) => {
    return POST('user.login', params, config);
};

// 支付宝小程序获取手机号
export const userPhone = (params, config) => {
    return POST('user.getDesensitizedPhone', params, config);
};

// 跳转第三方链接加密 user.encrytRoutingInfo
export const encrytRoutingInfo = (params, config) => {
    return POST('user.encrytRoutingInfo', params, config);
};

// 修改用户登录密码
export const passwordUpdate = (params, config) => {
    return POST('user.password.update', params, config);
};
// 设置登录密码
export const setLoginPassword = (params, config) => {
    return POST('user.setLoginPassword', params, config);
};
// 开通指纹人脸登录接口
export const openFingerOrFaceLogin = (params, config) => {
    return POST('user.openFingerOrFaceLogin', params, config);
};
// 支付宝小程序升级3.0
export const upgradeV3 = (params, config) => {
    return POST2(api + apiGsms + '/v1/memberMigration', params, config);
};
// 支付宝小程序升级3.0状态
export const upgradeV3status = (params, config) => {
    return POST2(api + apiGsms + '/v1/getMemberMigrateFlag', params, config);
};
// [获取支付宝用户信息接口 /user/getAlipayUserInfo]
export const getAlipayUserInfo = (params, config) => {
    return POST('user.getAlipayUserInfo', params, config);
};
// 设置登录密码
export const setPasswordByLogin = (params, config) => {
    return POST('user.setPasswordByLogin', params, config);
};
// 获取2.0token信息接口
export const getCppeiLoginInfo = (params, config) => {
    return POST('user.getCppeiLoginInfo', params, config);
};
// 获取建行加密手机号
export const getEncryptInfo = (params, config) => {
    return POST('user.getEncryptInfo', params, config);
};

// 非登录态获取微信用户openId接口 /user/nonLogin/getWechatUserOpenId
export const getWechatUserOpenId = (params, config) => {
    return POST('user.nonLogin.getWechatUserOpenId', params, config);
};

// 非登录态获取支付宝用户openId接口 /user/nonLogin/getAlipayUserOpenId
export const getAlipayUserOpenId = (params, config) => {
    return POST('user.nonLogin.getAlipayUserOpenId', params, config);
};

// 查询免费皮肤列表接口
export const skinQueryFree = (params, config) => {
    return POST('user.skin.queryFree', params, config);
};
// 获取经理企业微信二维码
export const addStationWechatWork = (params, config) => {
    return POST('account.addStationWechatWork', params, config);
};
// 获取会员码code
export const getPersMiniProgramQrCode = (params, config) => {
    return POST('route.srvsecurity.huePersQrCode.getPersMiniProgramQrCode.h5', params, config);
};