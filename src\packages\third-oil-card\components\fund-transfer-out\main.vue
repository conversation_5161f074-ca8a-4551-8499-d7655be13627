<template>
    <view class="uni-popupWrap bg-fff fl-column border-rad-8" style>
        <div class="title te-center font-18 color-333 weight-bold">资金转出前提条件</div>
        <div class="explain font-12 color-333 weight-400">
            1. 该卡的备用金可用余额转入昆仑e享卡余额后，昆仑e享卡的备用金余额不可以大于5000元。
            <br />2. 该卡开票类型必须为消费开票，如不是消费开票，请您前往柜台变更加油卡的开票类型，使其与昆仑e享卡账户开票类型保持一致。
            <br />3. 如该卡片常用地与昆仑e享卡的常用地不一致，请您变更昆仑e享卡常用地，或前往柜台变更加油卡常用地，使其保持一致。
            <br />
        </div>
        <div class="tips bg-F5F5F5">
            <div class="tips-content">
                <div class="reminder color-333 weight-bold font-12">温馨提示：</div>
                <br />
                <div class="explain2 color-333 font-12">
                    卡号为{{ cardDetailsObj.cardNo }}中，备用金余额
                    <!-- <span class="span1 color-E65023 font-12"
            >&yen;{{ cardDetailsObj.cash || 0 }}</span
          >-->
                    <span class="span1 color-E65023 font-12">&yen;{{ cardDetailsObj.cash }}</span>
                    ，将转入到您的昆仑e享卡余额中，
                    <span class="span2 color-E64F22 font-12">转出后将不能撤回</span>
                    <!-- 备用金积分余额
          <span class="span2 color-E64F22 font-12">
            {{
            cardDetailsObj.credit || 0
            }}
          </span>， 将转入您的昆仑e享卡积分账户中。-->
                </div>
            </div>
        </div>
    </view>
</template>
<script>
export default {
    name: 'fund-transfer-out',
    components: {},
    props: {
        cardDetailsObj: Object,
        default: () => {
            return {};
        },
    },
    data() {
        return {};
    },
    computed: {},
    onLoad() {},
    mounted() {},
    methods: {
        // TODO html测试数据
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.uni-popupWrap {
    background-color: #fff;
    // padding: 15px;
    // width: 75%;
    // height: ;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    border-radius: 8px;
    .title {
        margin-top: 19px;
        margin-bottom: 14px;
        text-align: center;
        height: 23px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }
    .explain {
        // text-align: center;
        margin: 0 auto;
        // width: 85%;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        // margin-right: 20px;
        // margin-left: 20px;
        // margin-bottom: 19px;
    }
    .tips {
        background: #f5f5f5;
        // width: 90%;
        margin: 23rpx auto;
        .tips-content {
            padding: 9px 10px;
            .reminder {
                color: #333333;
                font-weight: bold;
                font-size: 12px;
            }
            .explain2 {
                color: #333333;
                font-size: 12px;
                .span1 {
                    color: #e65023;
                    font-size: 12px;
                }
                .span2 {
                    color: #e64f22;
                    font-size: 12px;
                }
            }
        }
    }
    .btn {
        width: 100%;
        border-top: 1px solid #eee;
        height: 44px;
        border: 0 0 8px 8px;
        display: flex;
        margin-top: 10px;
        flex: 1;
        .allow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #333333;
        }
        .notAllow {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            border-right: 1px solid #eee;
        }
    }
}
</style>
