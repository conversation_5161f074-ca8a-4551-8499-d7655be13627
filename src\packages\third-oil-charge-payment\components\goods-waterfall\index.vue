<template>
    <my-waterfall v-model="dataList" add-time="100" :idKey="idKey" ref="myWaterfall">
        <template v-slot:left="{ leftList }">
            <my-goods-card
                v-for="(item, index) in leftList"
                :key="index"
                :goodsItem="item"
                @goodsClick="goodsClick"
                @goodsCountChange="goodsCountChange"
            ></my-goods-card>
        </template>
        <template v-slot:right="{ rightList }">
            <my-goods-card
                v-for="(item, index) in rightList"
                :key="index"
                :goodsItem="item"
                @goodsClick="goodsClick"
                @goodsCountChange="goodsCountChange"
            ></my-goods-card>
        </template>
    </my-waterfall>
</template>

<script>
/**
 * 商品瀑布流列表   u-view
 */
import myGoodsCard from '../goods-card/index.vue';
import myWaterfall from '../my-water-fall/index.vue';

export default {
    props: {
        //商品列表
        dataList: {
            type: Array,
            default: [],
        },
        idKey: {
            type: String,
            default: '',
        },
    },
    watch: {
        dataList: {
            handler: function (nVal, oVal) {
                console.log('dataList值改变了', nVal, oVal);
            },
            deep: true,
        },
    },
    methods: {
        goodsClick(goodItem) {
            this.$emit('goodsClick', goodItem);
        },
        goodsCountChange(goodObj) {
            console.log('走了goodsCountChange', goodObj);
            this.$emit('goodsCountChange', goodObj);
        },
        modify(id, key, value) {
            this.$refs.myWaterfall.modify(id, key, value);
        },
    },
    components: {
        myGoodsCard,
        myWaterfall,
    },
};
</script>

<style lang="scss" scoped>
.my-water-fall .u-column {
    width: 50% !important;
}
</style>
