<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="select-region-class bg-F7F7FB" style="height: 100%">
            <zj-navbar :height="44" title="群组权益"></zj-navbar>
            <div class="content" v-if="num == 0">
                <div class="content-pic">
                    <img src="../../images/group_hui.png" alt />
                </div>
                <div class="content-text">
                    <img src="../../images/group_icon_hui.png" alt />
                    <div>您暂时还没有加入任何群组</div>
                </div>
            </div>
            <div class="content" v-if="num == 1">
                <div class="content-pic">
                    <img src="../..//images/group_cheng.png" alt />
                </div>
                <div class="content-list">
                    <div class="content-list-item" v-for="(item, index) in list" :key="index">
                        <div class="item-title">
                            <img src="../../images/group_icon_cheng.png" alt />
                            <div style="font-weight: bold">{{ item.groupName }}</div>
                        </div>
                        <div class="item_div line_top" :class="showIndex == index + 1 && showValue ? 'item_div_show' : ''">
                            <div class="item-text" v-for="(i, ind) in item.rows" :key="ind">
                                <span>群组权益:</span>
                                {{ i.rightsName }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'index',
    data() {
        //num-为0的时候表示没有加入群组展示没有加入群组的页面为1的时候展示加入群组的页面
        //showIndex-每个列表中的下拉和收起判断点击的是哪一个
        //showValue-判断是下拉还是收起
        return {
            num: 1,
            showIndex: 0,
            showValue: false,
            list: [],
        };
    },
    mounted() {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.select-region-class {
    .content {
        position: relative;
        padding-top: 14px;

        .content-pic {
            width: 153px;
            height: 158px;
            margin: 0 auto;
            margin-bottom: 16px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .content-text {
            display: flex;
            align-items: center;
            width: 345px;
            margin: 0 auto;
            border-radius: 8px;
            height: 57px;
            background: #ffffff;
            padding-left: 20px;
            font-size: 15px;
            font-weight: bold;
            color: #333333;

            img {
                width: 16px;
                height: 16px;
                margin-right: 10px;
            }
        }

        .content-list {
            padding: 0 15px;
            zoom: 1;
            overflow: hidden;

            .content-list-item {
                width: 345px;
                // height: 137px;
                overflow: hidden;
                background: #ffffff;
                padding: 0 15px;
                padding-top: 13px;
                padding-bottom: 10px;
                margin-bottom: 10px;
                border-radius: 8px;
                box-sizing: border-box;

                .item-title {
                    display: flex;
                    align-items: center;
                    font-size: 16px;
                    font-weight: bold;
                    color: #333333;
                    margin-bottom: 10px;

                    img {
                        width: 16px;
                        height: 16px;
                        margin-right: 10px;
                    }
                }

                .item-line {
                    width: 100%;
                    height: 1px;
                    background: #eeeeee;
                    margin-bottom: 12px;
                }

                .item-text:last-child {
                    margin-bottom: 0;
                }

                .item_div_show {
                    height: auto !important;
                    max-height: 600px !important;
                    transform-origin: center top;
                    transition: all 1s;
                }

                .item_div {
                    padding-top: 12px;

                    // height: 20px;
                    // max-height: 50px;
                    .item-text {
                        margin-bottom: 10px;
                        font-size: 14px;
                        font-weight: bold;
                        color: #666666;

                        span {
                            color: #333333;
                        }
                    }
                }
            }
        }

        .item_btn_show {
            // position: absolute;
            // top: 24px;
        }

        .item-btn {
            z-index: 100;
            text-align: center;
            background: #fff;
            padding: 10px 0;
            width: 100%;
            position: relative;
            bottom: -10px;

            div {
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 8px;
                    height: 6px;
                    margin-left: 5px;
                }
            }
        }
    }
}
</style>
