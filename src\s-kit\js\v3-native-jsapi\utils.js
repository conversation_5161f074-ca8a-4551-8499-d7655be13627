const CryptoJS = require('crypto-js'); //引用AES源码js
// 加密算法类库（2.0使用）
var utils = {
    aesEn(string, isEasy) {
        // isEasy简单模式不加时间戳，默认false
        let lastStr = '';
        let key = '30313233343536373839414243444546';
        key = CryptoJS.enc.Hex.parse(key);
        let iv = CryptoJS.enc.Hex.parse('30313233343536373839414243444546');
        var time = '0000000' + new Date().getTime();
        let srcs = CryptoJS.enc.Utf8.parse(string + (!isEasy ? time : ''));
        // let srcs = CryptoJS.enc.Utf8.parse(string)
        let encrypted = CryptoJS.AES.encrypt(srcs, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7,
        });
        lastStr = encrypted.ciphertext.toString();
        return lastStr;
    },
    encryption(string) {
        return utils.aesEn(string);
    },
    encryptionEasy(string) {
        return utils.aesEn(string, true);
    },
};
export default utils;
