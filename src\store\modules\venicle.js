import { getCarList } from '../../api/home.js';
import util from '../../utils/index.js';

export default {
    state: {
        venicleList: [], // 车辆列表信息 (选中在前)
        defaultVenicleList: [], // 车辆列表信息 (未作处理)
    },
    mutations: {
        // 设置车辆列表信息 (选中在前)
        setVenicleList(state, list) {
            state.venicleList = list;
        },
        // 设置车辆列表信息 (未作处理)
        setDefaultVenicleList(state, list) {
            state.defaultVenicleList = list;
        },
    },
    actions: {
        // 更新车辆列表
        async uploadVenicleList({ state, commit, dispatch }) {
            let res = await getCarList();
            console.log(res.data);
            commit('setDefaultVenicleList', res.data);
            let arr = util.deepClone(res.data);
            let index = arr.findIndex(item => {
                return item.isDefault;
            });
            if (index > 0) {
                arr = util.swapArray(arr, 0, index);
            }
            commit('setVenicleList', arr);
        },
    },
};
