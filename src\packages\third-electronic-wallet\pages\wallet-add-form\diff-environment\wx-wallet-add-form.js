import { wxPalmprintAuth, generateWxSign } from '../../../../../s-kit/js/v3-http/https3/oilCard/index';
import { userAgreement } from '../../../../../s-kit/js/v3-http/https3/user';
const { clientCode, baseImgUrl, baseType } = require('../../../../../../project.config');
export default {
    // #ifdef MP-WEIXIN
    methods: {
        async agreeToAuthorize() {
            // let extraData = {
            //     api_version: '1.0',
            //     appid: 'wx368676e079814986',
            //     credential_type: 'MAINLAND_ID',
            //     mch_id: '1226183401',
            //     nonce_str: '79410813316749080183580901',
            //     openid: 'o4f4N5kYxkW0jVjdpOyTtZ_Pslq4',
            //     response_type: 'code',
            //     scope: 'pay_realname',
            //     sign: '04D756F52BF5EED95CB6488E277BE9B1E868318098F3894A951B5A27ADBF71C4',
            //     sign_type: 'HMAC-SHA256',
            // };
            this.$sKit.mpBP.tracker('e享卡开通', {
                seed: 'eCardActiveBiz',
                pageID: 'wxagreeBut', // 页面名
                refer: this.refer.match(/^[^_]*/)[0], // 来源
                channelID: clientCode, // C10/C12/C13
            });
            if (!this.refer.includes('_')) {
                this.refer = this.refer + '_1';
            }
            let extraData = {
                api_version: '1.0',
                appid: baseType == 'prd' ? 'wx7cd1712834749dcb' : 'wx368676e079814986',
                credential_type: 'MAINLAND_ID',
                mch_id: '1226183401',
                nonce_str: '',
                openid: this.openId,
                response_type: 'code',
                scope: 'pay_realname',
                sign: '',
                sign_type: 'HMAC-SHA256',
            };
            function generateRandomString(length) {
                var randomString = '';
                var validChars = '0123456789';
                for (var i = 0; i < length; i++) {
                    var randomIndex = Math.floor(Math.random() * validChars.length);
                    randomString += validChars.charAt(randomIndex);
                }
                return randomString;
            }
            var maxAllowedLength = 32;
            var randomStringLength = Math.floor(Math.random() * maxAllowedLength) + 1; // 生成一个随机长度，在0到31之间
            var randomString = generateRandomString(randomStringLength);
            console.log(randomString, 'randomString'); // 输出生成的随机字符串
            extraData.nonce_str = randomString.toString();
            // 拼接所有参数
            let stringA = `api_version=1.0&appid=${extraData.appid}&credential_type=MAINLAND_ID&mch_id=1226183401&nonce_str=${extraData.nonce_str}&openid=${extraData.openid}&response_type=code&scope=pay_realname&sign_type=HMAC-SHA256`;
            // // // 注：key为商户平台设置的密钥key
            // // let stringSignTemp = stringA + '&key=';
            // // console.log(stringSignTemp,'stringSignTempA');
            // // //注：MD5签名方式
            // // // let str = this.$sKit.layer.md5(stringSignTemp).toUpperCase();
            // // // 此方法为HMAC-SHA256的签名方式
            // // let test = CryptoJS.HmacSHA256('sha256', stringSignTemp, '');
            // // // 使用16进制的方法加密，输出字符串
            // // let testStr = CryptoJS.enc.Hex.stringify(test).toUpperCase();
            // // console.log(testStr, testStr.length, 'testStr====');
            // // extraData.sign = testStr;
            let params = {
                stringSign: stringA,
            };
            let res = await generateWxSign(params);
            console.log(res, '调用掌纹开卡获取签名接口返回值');
            if (res.success && res.data) {
                extraData.sign = res.data;
                console.log(extraData, stringA, 'extraData==全部的参数');
                wx.navigateToMiniProgram({
                    appId: 'wx88736d7d39e2eda6',
                    path: 'pages/oauth_confirm/oauth_confirm',
                    envVersion: 'release',
                    extraData: extraData,
                    success: res => {
                        this.wxPalmprintAuthFlag = true;
                        if (this.officialAccountParams == 'brushPalm') {
                            // 掌纹开卡和普通开卡，开卡成功后跳转不同页面标识
                            this.current.isPalmPay = true;
                            // 用户是使用微信认证小程序开卡的情况需要传true
                            this.current.isWxAuth = true;
                            // 区分普通开卡和掌纹开卡，掌纹开卡时为true
                            this.current.isOpenPalmPay = true;
                        } else {
                            // 非掌纹开卡但是使用了微信认证小程序
                            this.current.isWxAuth = true;
                            this.current.isOpenPalmPay = false;
                        }
                        this.$store.dispatch('setOfficialAccountParams', '');
                        console.log(res, '===res===');
                    },
                    fail(err) {
                        console.log(err, '失败的fail');
                    },
                });
            }
            this.$refs.oilPopup.close();
        },
        cancle() {
            this.wxPalmprintAuthFlag = false;
            this.$refs.oilPopup.close();
            if (this.officialAccountParams === 'brushPalm') {
                // 掌纹开卡和普通开卡，开卡成功后跳转不同页面标识
                this.current.isPalmPay = true;
                // 用户是使用微信认证小程序开卡的情况需要传true
                this.current.isWxAuth = false;
                // 区分普通开卡和掌纹开卡，掌纹开卡时为true
                this.current.isOpenPalmPay = true;
            } else {
                this.$store.dispatch('setOfficialAccountParams', '');
            }
            this.$sKit.mpBP.tracker('e享卡开通', {
                seed: 'eCardActiveBiz',
                pageID: 'wxcancleBut', // 页面名
                refer: this.refer.match(/^[^_]*/)[0], // 来源
                channelID: clientCode, // C10/C12/C13
            });
            if (!this.refer.includes('_')) {
                this.refer = this.refer + '_2';
            }
        },
        /**
         * @description  : 掌纹开卡认证申请
         * @param         {Boolean} openId -微信用户openid
         * @param         {Object} detail -微信小程序实名授权获取的Code
         * @return        {*}
         */
        async palmprintCertificationApplication(params) {
            let res = await wxPalmprintAuth(params);
            // this.current.name = '王玉磊'
            // this.current.idNo = '152326199810034276';
            // this.current.openid = this.openId;
            this.$refs.oilPopup.close();

            this.wxPalmprintAuthFlag = false;
            if (res.success) {
                this.current.name = res.data.name;
                this.current.idNo = res.data.idCardNo;
                this.$refs.oilPopup.close();
                console.log('wxPalmprintAuth ==  调用成功');
            }
        },
        /**
         * @description  : 选择加油站
         * @param         {Object} params -路由跳转参数对象
         * @param         {String} type -路由跳转参数,用来后续功能限制
         * @return        {*}
         */
        async selectStation() {
            let url = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {
                type: 'ekl', //昆仑e享卡
            };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 点击查看协议
         * @param         {string} type - 协议类型
         * @param         {string} cityName - 城市编码
         * @param         {string} name - 协议名称
         * @return        {*}
         */
        async getAgreeOn() {
            //  type   1. 服务协议  2.隐私协议协议 3.授权协议  4.业务协议  5.能源e站APP用户
            // name传
            /*
                  App用户使用协议
                  App隐私协议
                  电子钱包开通协议
                  App充值协议
              */
            let params = {
                type: '1',
                cityName: '全国',
                name: '昆仑e享卡开通协议',
            };
            let userAgreementRes = await userAgreement(params);
            if (userAgreementRes.success) {
                if (userAgreementRes.data.fileUrl) {
                    this.$sKit.layer.viewPdfAgreement(userAgreementRes.data.fileUrl);
                }
            } else {
                uni.showToast({ title: userAgreementRes.message || '未找到该协议' });
            }
        },
    },
    // #endif
};
