<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view fl-column p-bf">
            <zj-navbar :height="44" title="确认订单"></zj-navbar>
            <div class="f-1 bg-F7F7FB padding-16">
                <div class="info-wrap font-14 weight-400 color-999">商品信息</div>
                <div class="p-LR-16 bg-fff sp-info">
                    <div class="fl-column fl-jus-sta" v-for="(item, index) in orderInfoObject.productList" :key="index">
                        <div class="fl-row fl-jus-bet info-item-top">
                            <div class="font-14 weight-400 color-333 item-oil-name">{{ item.productName }}</div>
                            <div class="font-16 weight-500 color-333">{{ `&yen;${item.realAmount}` }}</div>
                        </div>
                        <div class="fl-row fl-jus-bet">
                            <div class="font-12 weight-400 color-666">数量：{{ item.num }}升</div>

                            <div class="strikethrough font-12 weight-400 color-999">{{ `&yen;${item.amount}` }}</div>
                        </div>
                        <div class="line_bottom"></div>
                    </div>
                </div>
                <div
                    class="font-12 weight-400 color-999 te-center bg-fff line-text"
                    v-if="orderInfoObject.productList && orderInfoObject.productList.length > 4"
                    >上划查看更多</div
                >
                <div class="info-wrap2 font-14 weight-400 color-999">订单信息</div>
                <div class="p-LR-16 bg-fff order-info border-rad-8">
                    <div class="fl-column fl-sp-sta">
                        <div class="fl-row fl-jus-bet fl-al-cen info-item">
                            <div class="font-14 weight-400 color-333">订单总额</div>
                            <div class="font-14 weight-500 color-E64F22">&yen;{{ orderInfoObject.orderAmount || 0 }}</div>
                        </div>
                        <div class="fl-row fl-jus-bet fl-al-cen info-item">
                            <div class="font-14 weight-400 color-333">商品总优惠</div>
                            <div class="font-14 weight-500 color-E64F22">&yen;{{ orderInfoObject.singlePriceDiscount || 0 }} </div>
                        </div>
                        <!-- <div class="fl-row fl-jus-bet fl-al-cen info-item">
            <div class="font-14 weight-400 color-333">支付总优惠</div>
            <div class="font-14 weight-500 color-E64F22">&yen;{{orderInfoObject.paymentDiscount || 0}}</div>
          </div> -->
                        <div class="line_bottom"></div>
                    </div>
                </div>
                <div class="p-LR-16 fl-row fl-al-cen fl-jus-bet p-bf bg-fff coupon-info border-rad-8" @click="selectCoupon">
                    <div class="fl-row fl-al-cen">
                        <div class="coupon-img fl-row">
                            <img src="../../images/coupon.png" alt />
                        </div>
                        <div class="coupon-text">优惠券</div>
                    </div>
                    <div class="fl-row fl-al-cen font-14 weight-500 color-E64F22 right_expRemDiv">
                        <div class="item_expRemDiv">
                            <div class="coupon_text" :style="{ marginTop: expRemText ? ' 13px' : '' }">
                                <span v-if="selectedCouponInfo && selectedCouponInfo.length > 0 && selectTicketDefaultArr[0].direction != 0"
                                    >-&yen;</span
                                >
                                {{ getSelectedCouponName() }}
                            </div>
                            <p class="expRemText">{{ expRemText }}</p>
                        </div>
                        <div class="arroe-right-small" v-if="selectedCouponInfo && selectedCouponInfo.length > 0"></div>
                    </div>
                </div>
                <div class="p-LR-16 fl-row fl-jus-bet fl-al-cen p-bf bg-fff coupon-info border-rad-8" @click="selectPay">
                    <div class="fl-row fl-al-cen">
                        <img src="../../images/fuelCard.png" class="fuel-img" alt />
                        <div class="coupon-text">{{ paymentSelectItem.value }}</div>
                    </div>
                    <div class="fl-row fl-al-cen font-14 weight-500 color-E64F22">
                        <div>剩余</div>
                        <div>&yen;{{ paymentSelectItem.label || 0 }}</div>
                        <div class="arroe-right-small"></div>
                    </div>
                </div>
            </div>
            <div class="bg-fff button-wrap">
                <div class="btn_div fl-row fl-jus-bet fl-al-cen p-LR-16">
                    <div class="finish_verification_price fl-row border-rad-8">
                        <div class="color-E64F22 font-18 weight-600">&yen;</div>
                        <div class="color-E64F22 font-20 weight-600">{{ orderInfoObject.payMoney }}</div>
                    </div>
                    <div class="finish_verification fl-row fl-sp-end">
                        <div class="btn-plain color-E64F22 border-rad-8" @click="cancellationOfOrder">取消订单</div>
                        <div
                            class="te-center primaryBtn color-fff font-16 border-rad-8"
                            :class="{ btnColor: isCanClickPay_paymentCode, 'bg-opacity-288': !isCanClickPay_paymentCode }"
                            @click="getCharge()"
                            >确认支付</div
                        >
                    </div>
                </div>
            </div>
            <UniPopup ref="codePopup" background="#f7f7fb" type="bottom">
                <div class="model-div">
                    <div class="marlr16 padtb16 fl-column">
                        <div class="fl-row fl-jus-bet fl-al-cen marb16">
                            <div class="font-16 weight-600 color-000 fl-al-cen">请您选择当前的状态</div>
                            <div class="close" @click="closepop">
                                <img src="../../images/close.png" alt />
                            </div>
                        </div>
                        <div class="payment-mode">
                            <div
                                class="payment-item fl-row fl-jus-bet fl-al-sta line_bottom"
                                v-for="(item, index) in paymentModeArr"
                                @click="selectPayMethods(item, index)"
                                :key="index"
                            >
                                <div class="payment-item-left fl-al-cen fl-row" v-if="item.value">
                                    <div class="payment-img">
                                        <img :src="item.img" alt />
                                    </div>
                                    <div class="payment-title">{{ item.value }}</div>
                                </div>
                                <div class="select-img fl-row fl-al-jus-cen">
                                    <div class="select-amount">剩余 &yen; {{ item.label || 0 }}</div>
                                    <div class="select_div" v-if="paymentSelectIndex == index">
                                        <img src="../../images/select.png" alt />
                                    </div>
                                    <div class="select_div" v-else> </div>
                                </div>
                            </div>
                        </div>
                        <div @click="confirmPayment(item)" class="primary-btn fl-row fl-al-jus-cen confirmPayment border-rad-8">
                            <div class="color-fff">确认支付</div>
                        </div>
                    </div>
                </div>
            </UniPopup>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import UniPopup from '@/s-kit/components/layout/uni-popup/uni-popup.vue';
import { cardList } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import {
    queryUnusedCouponList,
    cancelOrderApi,
    queryUnpaymentOrderByMemberNo,
    calculateOrdeDiscountsApi,
} from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import { mapState, mapGetters } from 'vuex';

import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'confir-order',
    components: {
        UniPopup,
    },
    props: {},
    data() {
        return {
            // 付款码订单信息
            orderInfoObject: {},
            // 支付方式数组
            paymentModeArr: [],
            // 选中的支付方式的下标
            paymentSelectIndex: 0,
            // 默认选中的支付方式
            paymentSelectItem: {},
            // 默认选中的支付方式
            payTypeInfo: {},
            // 营业日
            businessDay: '',
            // couponList: [],
            // 选中的电子券
            selectedCouponInfo: [],
            // 默认选中的电子券
            selectTicketDefaultArr: [],
            // 是否能点击确认支付
            isCanClickPay_paymentCode: false,
            expRemText: '', //优惠券提示文案
            expRemIdList: [],
        };
    },
    computed: {
        ...mapGetters(['walletStatus', 'walletInfo', 'isToSelectTicketPage']),
        ...mapState({
            // 付款码订单页面是否执行onshow标识
            isToSelectTicketPage: state => state.thirdIndex.isToSelectTicketPage,
            // 默认券的信息
            selectTickets: state => state.thirdIndex.selectTickets,
        }),
    },
    created() {},
    onShow() {
        if (this.selectTickets && this.isToSelectTicketPage) {
            this.isCanClickPay_paymentCode = false;
            // 选券后的券信息
            this.selectedCouponInfo = JSON.parse(decodeURIComponent(this.selectTickets));
            // 默认券信息
            this.selectTicketDefaultArr = JSON.parse(decodeURIComponent(this.selectTickets));
            // 选择优惠券后重新生成相应的订单
            this.getUnpaymentOrderByMemberNo();
            // 是否执行onshow标识
            this.$store.dispatch('setIsToSelectTicketPageAction', false);
            // 将券信息置空
            this.$store.dispatch('selectTicketsAction', undefined);
        } else {
            this.isCanClickPay_paymentCode = true;
        }
    },
    async onLoad(options) {
        // 设置节流，防止用户在短时间内频繁点击
        this.getCharge = this.$sKit.commonUtil.throttleUtil(this.getCharge);
        // 付款码订单信息
        this.orderInfoObject = JSON.parse(decodeURIComponent(options.data));
        console.log('订单页面', this.orderInfoObject);
        this.businessDay = this.orderInfoObject.businessDay;
        // 刷新钱包信息
        await this.$store.dispatch('getAccountBalanceAction');
        // 获取加油卡信息
        await this.getCardList();
        // 选择默认支付方式
        this.paymentSelectItem = this.paymentModeArr[0];
        //请求营销中心标识true-是  false-否
        if (this.orderInfoObject.reqOnline) {
            this.getTicket();
        }
    },
    methods: {
        /**
         * @description  : 获取加油卡
         * @return        {*}
         */
        async getCardList() {
            this.dataList = [];
            let _this = this;
            // 设置默认支付方式为昆仑e享卡
            this.paymentModeArr.push({
                img: require('../../images/e_account_icon.png'),
                value: '昆仑e享卡',
                label: this.walletInfo.walletBalance,
                payType: 5,
                cardSequence: 1,
            });
            let res = await cardList();
            if (res && res.success) {
                if (res.data.rows.length > 0) {
                    // 如果存在多张油卡，遍历将每条数据放到支付方式的数组中
                    let arr = res.data.rows.map(item => {
                        let cardNo = item.cardNo.slice(-4);
                        item.value = '加油卡' + ' ' + cardNo;
                        item.label = item.cardBalance;
                        item.payType = 6;
                        item.img = require('../../images/jiayouka.png');
                        return item;
                    });
                    this.paymentModeArr.push(...arr);
                    // 选择默认支付方式
                    this.payTypeInfo = this.paymentModeArr[0];
                }
            }
        },
        /**
         * @description  : 选择电子券
         * @return        {*}
         */
        selectCoupon() {
            if (!this.selectedCouponInfo.length) {
                return;
            }
            this.isCanClickPay_paymentCode = false;
            //  付款码订单页面是否执行onshow标识
            this.$store.dispatch('setIsToSelectTicketPageAction', false);
            let url = '/packages/third-scan-code-payment/pages/choose-coupon/main';
            // 选择券是携带的参数
            let params = { couponList: this.selectedCouponInfo };
            let type = 'navigateTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 点击选择支付方式拉起弹窗
         * @return        {*}
         */
        selectPay() {
            this.$refs.codePopup.open();
        },
        /**
         * @description  : 弹窗的确认支付
         * @return        {*}
         */
        confirmPayment() {
            this.payTypeInfo = this.paymentSelectItem;
            this.$refs['codePopup'].close();
        },
        /**
         * @description  : 关闭支付方式弹窗
         * @return        {*}
         */
        closepop() {
            // this.paymentModeArr.forEach((item, index) => {
            //     if (item.payType == 1 && this.paymentSelectItem.payType == 1) {
            //         this.paymentSelectItem = this.paymentModeArr[index];
            //         this.payTypeInfo = this.paymentModeArr[index];
            //         this.paymentSelectIndex = index;
            //     } else if (item.cardNo == this.paymentSelectItem.cardNo) {
            //         this.paymentSelectItem = this.paymentModeArr[index];
            //         this.payTypeInfo = this.paymentModeArr[index];
            //         this.paymentSelectIndex = index;
            //     }
            // });
            // 如果选择的支付方式和弹窗确认支付的支付方式不同的的话，点击关闭使用this.payTypeInfo的支付方式
            if (this.payTypeInfo.payType != this.paymentSelectItem.payType) {
                this.paymentModeArr.forEach((item, index) => {
                    if (item.payType == this.payTypeInfo.payType) {
                        this.paymentSelectItem = this.paymentModeArr[index];
                        this.payTypeInfo = this.paymentModeArr[index];
                        this.paymentSelectIndex = index;
                    }
                });
            }
            // 如果用户点击过了确认支付，再次打开然后点取消
            this.$refs.codePopup.close();
        },
        /**
         * @description  : 选择弹窗内的支付方式
         * @param         {Object} item -选择的支付方式
         * @param         {Number} item -选择的支付方式的下标
         * @return        {*}
         */
        selectPayMethods(item, index) {
            console.log('切换支付方式', item, index);
            this.paymentSelectIndex = index;
            this.paymentSelectItem = item;
        },
        /**
         * @description  : 确认支付(选择券与选择支付方式后的确认支付)
         * @param         {Object} payTypeInfo -支付方式
         * @param         {Object} orderInfo -支付信息
         * @return        {*}
         */
        getCharge() {
            if (!this.isCanClickPay_paymentCode) return;
            this.isCanClickPay_paymentCode = false;
            if (!this.paymentModeArr.length) {
                uni.showToast({ title: '暂无支付方式' });
                return;
            }
            let url = '/packages/third-scan-code-payment/pages/confir-code-order/main';
            // 当前参数是支付信息和订单信息
            let params = {
                payTypeInfo: JSON.stringify(this.payTypeInfo) == '{}' ? this.paymentModeArr[0] : this.payTypeInfo,
                orderInfo: this.orderInfoObject,
            };
            let type = 'redirectTo'; // 默认  uni.navigateTo({})
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 取消订单
         * @param         {String} stationCode -站编码
         * @param         {String} orderNo -订单号
         * @return        {*}
         */
        cancellationOfOrder() {
            this.$store.dispatch('zjShowModal', {
                title: '您是否要取消订单？',
                confirmText: '确认',
                cancelText: '取消',
                confirmColor: '#333',
                cancelColor: '#666',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let params = {
                            stationCode: this.orderInfoObject.stationCode,
                            orderNo: this.orderInfoObject.orderId,
                        };
                        cancelOrderApi(params).then(res => {
                            if (res.success) {
                                uni.showToast({
                                    title: '订单取消成功',
                                    icon: 'none',
                                    duration: 2000,
                                });
                                // uni.navigateBack({ detail: 1 })
                                this.closeEvent();
                                // setTimeout(() => {
                                //   let url = ''
                                //   let params = {}
                                //   let type = "navigateBack";
                                //   let prevParams = {
                                //     // 得到上一页面的实例需要传2 若要返回上上页面的实例就传3，以此类推
                                //     delta: 1,
                                //   }
                                //   this.$sKit.layer.useRouter(url, params, type, prevParams);
                                // }, 2000);
                            }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        // 关闭小程序
        closeEvent() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {});
                // #endif
                // #ifndef MP-MPAAS
                this.$sKit.layer.backHomeFun();
                // uni.reLaunch({
                //     url: '/pages/thirdHome/main',
                // });
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        /**
         * @description  : 根据优惠券查询订单信息
         * @param         {Array} couponList -电子券
         * @return        {*}
         */
        getUnpaymentOrderByMemberNo() {
            let params = {
                couponList: this.selectedCouponInfo || [],
            };
            queryUnpaymentOrderByMemberNo(params)
                .then(
                    res => {
                        if (res.success) {
                            if (res.data) {
                                // 获取回来的订单信息重新赋值
                                this.orderInfoObject = res.data;
                                this.isCanClickPay_paymentCode = true;
                                this.getTicket();
                            }
                        }
                    },
                    error => {},
                )
                .catch(err => {
                    this.isCanClickPay_paymentCode = true;
                });
        },
        /**
         * @description  : 获取优惠券
         * @param         {String} orderId -订单号
         * @param         {String} orderChannel -订单渠道编码
         * @param         {String} stationCode -油站code
         * @param         {String} totalAmount -订单优惠前金额（元）
         * @param         {String} couponList -电子券
         * @return        {*}
         */
        getTicket() {
            let params = {
                orderId: this.orderInfoObject.orderId,
                orderChannel: this.orderInfoObject.orderChannel,
                stationCode: this.orderInfoObject.stationCode,
                totalAmount: this.orderInfoObject.orderAmount,
                couponList: this.selectedCouponInfo,
                // orderId: '2204121556170000000104003',
                // orderChannel: 15,
                // stationCode: '1-A5001-C005-5094',
                // totalAmount: 17.90,
                // couponList: this.selectedCouponInfo
            };
            queryUnusedCouponList(params).then(res => {
                if (res.success) {
                    if (res.data) {
                        this.selectedCouponInfo = res.data.couponList;
                        this.judgeExpRem();
                    } else {
                        this.selectedCouponInfo = [];
                    }
                } else {
                    // this.$Toast(res.message ? res.message : '获取优惠券列表失败')
                    uni.showToast({ title: '获取优惠券列表失败' });
                }
                // 判断当前是否存在电子券
                this.getSelectedCouponName();
            });
        },
        /*判断电子券临期
         */
        judgeExpRem() {
            if (this.selectedCouponInfo.length > 0) {
                // 遍历券
                this.expRemIdList = [];
                for (let i = 0; i < this.selectedCouponInfo.length; i++) {
                    let item = this.selectedCouponInfo[i];
                    if (item.expRem) {
                        this.expRemIdList.push(item.couponNo);
                    }
                }
            }
        },
        /**
         * @description  : 判断当前是否存在电子券
         * @return        {*}
         */
        getSelectedCouponName() {
            this.orderInfoObject.couponList = [];
            // 判断当前是否存在电子券
            if (this.selectedCouponInfo.length > 0) {
                // 遍历券
                for (let i = 0; i < this.selectedCouponInfo.length; i++) {
                    let item = this.selectedCouponInfo[i];
                    // used为1的券是当前订单计算时用的券，有的话支付的时候才会传进去
                    if (item.used == 1) {
                        this.orderInfoObject.couponList = [item];
                        if (this.expRemIdList.length > 0 && !this.expRemIdList.includes(item.couponNo)) {
                            this.expRemText = '您有其他电子券即将到期';
                        } else {
                            this.expRemText = '';
                        }
                        return item.couponDiscount;
                    }
                }
            }
            if (this.selectedCouponInfo.length == 0) {
                return '无可用';
            } else {
                this.expRemText = this.expRemIdList.length > 0 ? '您有其他电子券即将到期' : '';
                return '请选择优惠券';
            }
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.view {
    .info-wrap {
        margin-bottom: 12px;
    }

    .info-wrap2 {
        margin-top: 12px;
        margin-bottom: 12px;
    }

    .sp-info {
        width: 100%;
        max-height: 285px;
        overflow-y: scroll;
        border-radius: 8px 8px 0 0;

        .info-item-top {
            margin-top: 12px;

            .item-oil-name {
                width: 143px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .line_bottom {
            &.line_bottom:before {
                border-top: 1px solid #eee;
            }
        }

        .strikethrough {
            text-decoration-line: line-through;
            text-decoration-style: #999;
            margin-bottom: 12px;
        }
    }

    .line-text {
        width: 100%;
        height: 40px;
        line-height: 40px;
        border-radius: 0 0 8px 8px;
    }

    .order-info {
        width: 100%;
        height: 141.5px;

        .info-item {
            height: 48px;
        }
    }

    .coupon-info {
        width: 100%;
        height: 52px;
        margin-top: 12px;

        .coupon-img {
            width: 19px;
            height: 14.5px;
            margin-right: 10px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .fuel-img {
            width: 22px;
            height: 22px;
            margin-right: 10px;
        }

        // .coupon-text {
        //   line-height: 20px;
        // }
        .coupon-text {
            line-height: 55px;
        }

        .arroe-right-small {
            margin-left: 5px;
        }
    }

    .button-wrap {
        width: 100%;
        // height: 76px;

        .btn_div {
            width: 100%;
            line-height: 76px;

            .finish_verification_price {
                min-width: 30%;
                height: 44px;
                line-height: 44px;
                margin-top: 16px;
                margin-bottom: 16px;
                align-items: baseline;

                .text {
                    margin-right: 5px;
                }
            }

            .finish_verification {
                width: 70%;
                height: 44px;
                line-height: 44px;
                margin-top: 16px;
                margin-bottom: 16px;

                .btn-plain {
                    margin-right: 10px;
                    width: 50%;
                }

                .primaryBtn {
                    width: 50%;
                    // background-color: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
                    text-align: center;
                }
                .btnColor {
                    background-image: linear-gradient(288deg, #ff3e00 0%, #ff7b33 100%);
                }

                .confirmPaymentBg {
                    background-color: lightgray;
                }
            }
        }
    }

    .model-div {
        background: #f7f7fb;
        border-radius: 10px 10px 0px 0px;

        .marlr16 {
            margin: 0 16px;
        }

        .marb16 {
            margin-bottom: 16px;
        }

        .payment-mode {
            margin-top: 15px;
            // height: 130.5px;
            overflow-y: scroll;

            .payment-item {
                height: 32px;
                margin-top: 12px;

                .payment-item-left {
                    .payment-title {
                        margin-left: 10px;
                    }

                    .payment-img {
                        width: 20px;
                        height: 20px;

                        img {
                            width: 20px;
                            height: 20px;
                        }
                    }
                }

                .select-img {
                    .select-amount {
                        margin-right: 10px;
                    }

                    .select_div {
                        width: 17.5px;
                        height: 17.5px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
        }

        .confirmPayment {
            height: 44px;
            margin-bottom: 20px;
            margin-top: 16px;
        }

        .marb12 {
            margin-bottom: 12px;
        }

        .padtb16 {
            padding-top: 16px;
            padding-bottom: 16px;
        }

        .close {
            img {
                width: 13px;
                height: 13px;
            }
        }
    }
}
.right_expRemDiv {
    width: 70%;
    flex-wrap: wrap;
    justify-content: flex-end;
}
.item_expRemDiv {
    width: 90%;
    flex-wrap: wrap;
    justify-content: flex-end;
    display: flex;
    padding-right: 5px;
}
.expRemText {
    width: 100%;
    font-size: 10px;
    font-weight: 400;
    text-align: right;
}
</style>
