import store from '../../../store/index';
import { singleton } from '../singleton';
import { app, baseType } from '../../../../project.config';
import LAYER from '../../../s-kit/js/layer';
import { logoutApi } from '@/s-kit/js/v3-http/https3/user';

class aliPayPlugin {
    // #ifdef MP-ALIPAY
    constructor() {
        this.init();
    }

    async init() {
        const { gsmsToken, openId, accessToken } = uni.getStorageSync('tokenInfo') || {};
        if (!gsmsToken || !openId) {
            console.error('gsmsToken或者openId不存在，插件未登录');
            const page = getCurrentPages();
            console.log(
                page.length > 0,
                !page[page.length - 1].$page.fullPath.includes('/pages/union/main'),
                page.length > 0 && !page[page.length - 1].$page.fullPath.includes('/pages/union/main'),
            );
            if (page.length > 0 && !page[page.length - 1].$page.fullPath.includes('/pages/union/main')) {
                // // 需添加新的重新登陆逻辑
                if (accessToken) {
                    let res = await logoutApi();
                    if (res.success) {
                        store.commit('setLongTimeNotLogin', null);
                    }
                }
                uni.clearStorageSync();
                LAYER.backHomeFun();
            }
            return;
        }
        return new Promise((resolve, reject) => {
            my.loadPlugin({
                plugin: `${app.zfbplugins.payPlugin.provider}@*`,
                success: async () => {
                    try {
                        let params = {
                            token: gsmsToken || '',
                            data: {},
                            baseType: baseType,
                            openId: openId || '',
                        };
                        const payPlugin = requirePlugin(`dynamic-plugin://${app.zfbplugins.payPlugin.provider}`);
                        let initResult = await payPlugin.InitPay(params);
                        console.log('payPlugin初始化', initResult, payPlugin);
                        if (initResult.code === 'PAY_SUCCESS') {
                            console.log('支付插件初始化-succ', this, initResult);
                            Object.assign(this, payPlugin);
                            resolve(payPlugin);
                        } else {
                            console.log('支付插件初始化-fail', initResult);
                            // 截取字符串后面的数据
                            let errIndex = initResult.msg.indexOf(':');
                            let errorCode = '';
                            let customErr = '';
                            if (errIndex !== -1) {
                                errorCode = initResult.msg.slice(0, errIndex);
                                customErr = initResult.msg.slice(errIndex + 1, initResult.msg.length);
                            } else {
                                customErr = initResult.msg;
                            }
                            store.dispatch('zjShowModal', {
                                title: customErr,
                                content: `错误码：${errorCode}`,
                                confirmText: '确定',
                                success(res) {
                                    if (res.confirm) {
                                        console.log('用户点击确定');
                                    } else if (res.cancel) {
                                        console.log('用户点击取消');
                                    }
                                },
                            });
                        }
                    } catch (err) {
                        console.log('payPlugin初始化失败', err);
                        reject(err);
                    }
                },
            });
        });
    }
    // #endif
}

export default singleton(aliPayPlugin);
