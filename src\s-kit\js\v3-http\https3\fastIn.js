import { POST, POST2, PUT } from '../index';
import { api, apiGsms } from '../../../../../project.config';

export const getCustomer = (params, config) => {
    return POST2('/app/json/refuel/getCustomer', params, config);
};
export const customerArrived = (params, config) => {
    return POST2('/app/json/refuel/customerArrived', params, config);
};
export const customerRegist = (params, config) => {
    return POST2('/app/json/refuel/customerRegist', params, config);
};
export const customerModify = (params, config) => {
    return POST2('/app/json/refuel/customerModify', params, config);
};
