import Store from '../../store/index';
import Layer from './layer';
// import configInfo from './../config/configInfo.js'
// import http from "../../../utils/http";
// import { refreshToken, realNameAuth, getIdentityAuthInfo } from './../api/account.js'
import { identityAuthInfo } from './v3-http/https3/oilCard/index.js'; //获取用户信息
import { biometricPayStatus } from './v3-http/https3/user.js';
import { accountStatus } from './v3-http/https3/wallet.js';
import { realNameAuth, initRealPersonIdentify, realPersonIdentify, basicInfoQuery } from './v3-http/https3/oilCard/index.js';
// import { biometricPayStatus, migrateECard, realPersonIdentify, initRealPersonIdentify } from "../api/wallet";
import sha256 from 'crypto-js/sha256';
import accountCenter from './v3-native-jsapi/accountCenter.js';
import cnpcBridge from './v3-native-jsapi/cnpcBridge';
import wxRealPersonAuthentication from './wx-real-person-authentication';
import zfbRealPersonAuthentication from './zfb-real-person-authentication';
import harmonyRealPersonAuthentication from './harmony-real-person-authentication';
import h5RealPersonAuthentication from './h5-real-person-authentication';
//未开通电子钱包的提示
async function notOpenEwallet({ nextFun = () => {}, callback = () => {}, walletAddParams = {} } = {}) {
    let res = await accountStatus();
    if (res.success) {
        let walletInfo = res.data;
        if (!walletInfo.status) {
            Store.dispatch('zjShowModal', {
                title: '',
                content: '您还没有开通昆仑e享卡，是否开通？',
                confirmText: '去开通',
                cancelText: '取消',
                confirmColor: '',
                cancelColor: '',
                type: '',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                        let params = walletAddParams;
                        let type = 'navigateTo'; // 默认  uni.navigateTo({})
                        Layer.useRouter(url, params, type);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                        if (callback) {
                            callback();
                        }
                    }
                },
            });
        } else {
            if (nextFun) nextFun();
        }
    }
}

/**
 * 判断钱包状态，给出提示。
 * @param nextFun //正常状态下回调
 * @param freezeReasonArr  //冻结状态下是否需要处理冻结原因(传递数组，为空代表不需要特殊处理，)
 * 冻结原因：1—正式挂失；2—临时挂失；3—管理员冻结；4—无卡注销；5—系统自动冻结；6—系统资金冻结；7—系统积分冻结；8—冷静期冻结；
 * 9—电子卡未迁移冻结；10-未设置密码  99—其他；12-退款时冻结 注：只有账户状态为冻结，该字段才有值，否则为空
 * @param cancelCallback 钱包注销回调
 */
async function eWalletNormal({
    nextFun = () => {},
    freezeReasonArr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 99, 12],
    cancelCallback = () => {},
    isEnter = 0,
    freeze = 0,
    cancelFun = () => {},
    walletAddParams = {},
} = {}) {
    //当是开通情况下并且开通状态为 1 正常
    let res = await accountStatus({}, { isload: false });
    if (res.success) {
        Store.commit('setWalletStatus', res.data);
        let walletInfo = res.data;
        if (walletInfo.status && walletInfo.accountStatus == '1') {
            if (nextFun) nextFun();
        } else if (walletInfo.status && walletInfo.accountStatus == '2') {
            //当是开通情况下并且开通状态为 2 冻结
            if (freezeReasonArr.length > 0) {
                if (freezeReasonArr.indexOf(walletInfo.accountStatusReason) > -1) {
                    if (walletInfo.accountStatusReason == 9) {
                        cancelCallback();
                    } else if (walletInfo.accountStatusReason == 10) {
                        //冻结原因  未设置密码
                        Store.dispatch('zjShowModal', {
                            content: '您的昆仑e享卡未设置支付密码，为保障您的账户安全，请先设置',
                            confirmText: '去设置',
                            cancelText: '取消',
                            showCancel: true,
                            confirmColor: '',
                            cancelColor: '',
                            success: res => {
                                if (res.confirm) {
                                    console.log('用户点击确定');
                                    let url;
                                    // #ifdef MP-MPAAS
                                    url = '/packages/third-my-wallet/pages/forgot-password/main';
                                    // #endif
                                    // #ifndef MP-MPAAS
                                    url = '/packages/third-my-wallet/pages/change-password/main';
                                    // #endif
                                    let params = {};
                                    let type = 'navigateTo'; // 默认  uni.navigateTo({})
                                    Layer.useRouter(url, params, type);
                                    cancelFun();
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                    if (cancelFun) {
                                        cancelFun();
                                    }
                                }
                            },
                        });
                    } else {
                        //当是冻结情况下报联系客服的提示
                        Store.dispatch('zjShowModal', {
                            content: '您的昆仑e享卡已被冻结，请联系客服解冻',
                            confirmText: '确定',
                            showCancel: true,
                            confirmColor: '',
                            success: res => {
                                if (res.confirm) {
                                    cancelFun();
                                    console.log('您的昆仑e享卡已被冻结，请联系客服解冻===点击确定');
                                }
                            },
                        });
                    }
                } else if (freeze == '1') {
                    nextFun();
                } else {
                    //当是冻结情况下报联系客服的提示
                    Store.dispatch('zjShowModal', {
                        content: '您的昆仑e享卡已被冻结，请联系客服解冻',
                        confirmText: '确定',
                        showCancel: true,
                        confirmColor: '',
                        success: res => {
                            if (res.confirm) {
                                cancelFun();
                                console.log('您的昆仑e享卡已被冻结，请联系客服解冻===点击确定');
                            }
                        },
                    });
                }
            } else {
                if (nextFun) nextFun();
            }
        } else if (!walletInfo.status) {
            // 未开通电子钱包的提示
            Store.dispatch('zjShowModal', {
                title: '',
                content: '您还没有开通昆仑e享卡，是否开通？',
                confirmText: '去开通',
                cancelText: '取消',
                confirmColor: '',
                showCancel: true,
                cancelColor: '',
                type: '',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                        let params = walletAddParams;
                        let type = 'navigateTo'; // 默认  uni.navigateTo({})
                        Layer.useRouter(url, params, type);
                        cancelFun();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                        if (cancelFun) {
                            cancelFun();
                        }
                    }
                },
            });
        } else {
            // isEnter 注销状态下是否能进入当前功能的二级页面
            if (isEnter) {
                //钱包注销状态下可以进入电子钱包页，但是余额、注销和更换开户地用不了
                nextFun();
            } else {
                //当是开通情况下并且开通状态为 3 注销 或是 未开通的情况下显示
                Store.dispatch('zjShowModal', {
                    title: '',
                    content: '您还没有开通昆仑e享卡，是否开通？',
                    confirmText: '去开通',
                    cancelText: '取消',
                    confirmColor: '',
                    showCancel: true,
                    cancelColor: '',
                    type: '',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                            let params = walletAddParams;
                            let type = 'navigateTo'; // 默认  uni.navigateTo({})
                            Layer.useRouter(url, params, type);
                            cancelFun();
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            if (cancelFun) {
                                cancelFun();
                            }
                        }
                    },
                });
            }
        }
    } else {
        if (cancelCallback) {
            cancelCallback();
        }
    }
}

/**未设置支付密码 */

async function setPassword(nextFun) {
    let res = await accountStatus();
    if (res.success) {
        let walletInfo = res.data;
        if (walletInfo.status && walletInfo.accountStatus == '2' && walletInfo.accountStatusReason == 10) {
            //冻结原因  未设置密码
            Store.dispatch('zjShowModal', {
                content: '您的昆仑e享卡未设置支付密码，为保障您的账户安全，请先设置',
                confirmText: '去设置',
                cancelText: '取消',
                showCancel: true,
                confirmColor: '',
                cancelColor: '',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        let url;
                        // #ifdef MP-MPAAS
                        url = '/packages/third-my-wallet/pages/forgot-password/main';
                        // #endif
                        // #ifndef MP-MPAAS
                        url = '/packages/third-my-wallet/pages/change-password/main';
                        // #endif
                        let params = { isClose: 1 }; //修改后自动返回上一页
                        let type = 'navigateTo'; // 默认  uni.navigateTo({})
                        Layer.useRouter(url, params, type);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        } else {
            nextFun();
        }
    }
}

/**
 * 加油卡操作的时候，如果钱包是冻结状态，并且冻结原因是10未设置密码，给出提示。
 * @param nextFun //正常状态下回调
 * 冻结原因：1—正式挂失；2—临时挂失；3—管理员冻结；4—无卡注销；5—系统自动冻结；6—系统资金冻结；7—系统积分冻结；8—冷静期冻结；9—电子卡未迁移冻结；10-未设置密码  99—其他；注：只有账户状态为冻结，该字段才有值，否则为空
 */
async function cardOperationPwd({ nextFun = () => {}, cancelFun = () => {}, walletAddParams = {} } = {}) {
    let walletInfo = await accountStatus();
    let isHarmony = Store.state.thirdIndex.isHarmony;
    console.log('isHarmony', isHarmony);
    if (!walletInfo.data.status) {
        //未开通电子钱包的提示
        Store.dispatch('zjShowModal', {
            title: '',
            content: '您还没有开通昆仑e享卡，是否开通？',
            confirmText: '去开通',
            cancelText: '',
            confirmColor: '',
            showCancel: true,
            cancelColor: '',
            type: '',
            success: res => {
                if (res.confirm) {
                    console.log('用户点击确定');
                    let url = '/packages/third-electronic-wallet/pages/wallet-add-form/main';
                    let params = walletAddParams;
                    let type = 'navigateTo'; // 默认  uni.navigateTo({})
                    Layer.useRouter(url, params, type);
                    cancelFun();
                } else if (res.cancel) {
                    cancelFun();
                    console.log('用户点击取消');
                }
            },
        });
    } else {
        if (walletInfo.data.accountStatus == '2') {
            //当是开通情况下并且开通状态为 2 冻结
            if (walletInfo.data.accountStatusReason == 10) {
                //冻结原因  未设置密码
                Store.dispatch('zjShowModal', {
                    title: '',
                    content: '您的昆仑e享卡未设置支付密码，为保障您的账户安全，请先设置',
                    confirmText: '去设置',
                    cancelText: '取消',
                    confirmColor: '',
                    showCancel: true,
                    cancelColor: '',
                    type: '',
                    success: res => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let url;
                            // #ifdef MP-MPAAS
                            url = !isHarmony
                                ? '/packages/third-my-wallet/pages/forgot-password/main'
                                : '/packages/third-my-wallet/pages/change-password/main';
                            // #endif
                            // #ifndef MP-MPAAS
                            url = '/packages/third-my-wallet/pages/change-password/main';
                            // #endif
                            let params = {};
                            let type = 'navigateTo'; // 默认  uni.navigateTo({})
                            Layer.useRouter(url, params, type);
                            cancelFun();
                        } else if (res.cancel) {
                            cancelFun();
                            console.log('用户点击取消');
                        }
                    },
                });
            } else {
                if (nextFun) nextFun();
            }
        } else {
            //当是开通情况下并且开通状态为 1 正常
            if (nextFun) nextFun();
        }
    }
}

// async function queryECardByIdNo (callback) {
//   return new Promise((async (resolve, reject) => {
//     let identityInfo = await identityAuthInfo({}, { is_error: true });
//     if (identityInfo.success) {
//       queryECardInfoByIdNo({ encryptIdNo: identityInfo.data.identityNo }).then(res => {

//         let data = res.data;
//         if (res.status == 0) {
//           if (data && data.cardStatus == '2') {

//             // 1—未激活；2—正常；3—黑名单，即冻结状态；4—注销；6—拒绝；7—已迁移

//             this.$store.commit('mSetIsTransfer', { falg: true })

//           }
//         } else {

//         }
//       }, error => {

//       });
//     } else
//   }));
// }

/**
 * 刷新钱包信息
 * @param callback
 * @returns {Promise<void>}
 */
async function refreshWallet(callback) {
    await Store.dispatch('basicCouponAction');
    await Store.dispatch('getAccountBalanceAction');
    if (callback) callback();
}
// 清空所有缓存数据
async function clearData() {
    Store.state.login.token = '';
    Store.state.login.token3 = '';
    Store.state.login.gsmsToken = '';
    Store.state.login.refreshToken = '';
    Store.state.login.expiresIn = '';
    Store.state.login.needBindPhone = '';
    Store.state.isThirdVer = false;
    //清空电子钱包状态
    Store.commit('wallet/setwalletInfo', {
        status: false, //电子账户开通状态(true—已开通；false—未开通)
        accountStatus: 0, //账户状态 1-正常 2-冻结 3-注销
    });
    Store.commit('wallet/setWalletInfo', '');
    //清空会员的油卡，电子券，积分和余额信息，七日内余额等信息
    Store.commit('memberModule/setMemberAccountInfo', {
        ptsAvailableAmount: '', //客户能源币数量
        fuelCardCount: 0, //油卡数量
        couponsCount: 0, //电子券可使用张数
        loyaltyPtsAvailableAmount: '0', //积分余额
        eWalletAvailableAmount: '0', //电子钱包余额（元）
        ptsAmountExpiresInOneWeek: '', //7日内过期的能源币数量
        loyaltyPtsAmountExpiresInOneWeek: '', //7日内过期的积分数值
        localInfo: '', //开户地信息
    });
    //清空用户车辆信息
    Store.commit('wallet/setDefaultPlate', '');
    Store.commit('wallet/setOneKeyOilPlate', '');
    Store.commit('wallet/setPayOilPlate', '');
    Store.commit('wallet/setReserveOilPlate', '');
    Store.commit('wallet/setPlateList', []);
    //清空卡信息
    Store.commit('cardModule/setBindCardInfo', '');
}

/**
 * 判断有没有开通生物识别支付
 * @returns {Promise<any>}
 */
async function judgeBiometricPayStatus() {
    return new Promise(async (resolve, reject) => {
        //
        try {
            let supprtType = await cnpcBridge.getSupportType();
            if (!supprtType) {
                //设备不支持人脸或者指纹
                return resolve(false);
            }

            const bioRes = await biometricPayStatus({ biometricType: supprtType }, { isload: false });

            if (!bioRes) {
                return resolve(false);
            }

            if (!bioRes.success) {
                return resolve(false);
            }

            if (bioRes.data.status != 1) {
                return resolve(false);
            }

            //接口层面已开通
            accountCenter.hasBiometricCode(result => {
                if (result.isSuccessed) {
                    //开通了
                    resolve(true);
                } else {
                    if (result.code == 'P_B07_701000') {
                        // this.$Toast(result.desString || '数据准备中，请稍后');
                        resolve(false);
                    } else {
                        //接口层面开通，但是本地没有开通，（卸载重装、切换设备、等情况），手动关闭一下。
                        accountCenter.closeBiometricCode({ type: supprtType }, () => {
                            resolve(false);
                        });
                    }
                }
            });
        } catch (error) {
            return resolve(false);
        }
    });
}

/**
 * 生物识别验证
 * @returns {Promise<any>}
 * isAuth  是否需要拉起指纹或者人脸，默认需要
 */
async function biometricPay(orderNo, amount, isAuth = true) {
    //
    return new Promise(async (resolve, reject) => {
        try {
            let state = await judgeBiometricPayStatus();
            //
            if (state) {
                //验证成功
                let commonArgs = await cnpcBridge.getCommonArgs();
                let supprtType = await cnpcBridge.getSupportType();
                if (isAuth) {
                    cnpcBridge.localAuthVerifica(async res => {
                        if (res && Number(res)) {
                            let signature = `${orderNo};${handleDecimals(amount)};${supprtType};${commonArgs.deviceId}`;
                            resolve({
                                bioType: supprtType + '',
                                bioCode: commonArgs.deviceId,
                                signature: sha256(signature).toString(),
                            });
                        } else {
                            //验证失败
                            setTimeout(() => {
                                resolve({});
                            }, 1000);
                        }
                    });
                } else {
                    let signature = `${orderNo};${handleDecimals(amount)};${supprtType};${commonArgs.deviceId}`;
                    resolve({
                        bioType: supprtType + '',
                        bioCode: commonArgs.deviceId,
                        signature: sha256(signature).toString(),
                    });
                }
            } else {
                setTimeout(() => {
                    resolve({});
                }, 1000);
            }
        } catch (error) {
            console.log(error);
            resolve({});
        }
    });
}
// 组合参数
function handleDecimals(data) {
    if (String(data).indexOf('.') > -1) {
        let arr = String(data).split('.');
        if (arr[1].length > 4) return arr[0] + '.' + arr[1].substring(0, 4);
        else return arr[0] + '.' + arr[1].padEnd(4, 0);
    } else {
        //不包含小数点
        return Number(data).toFixed(4);
    }
}
// 环境判断
async function judgeEnv() {
    return process.env.ENV_CONFIG !== 'dev' && process.env.ENV_CONFIG !== 'qas' && process.env.ENV_CONFIG !== 'sit';
}
/**
 * 判断资源是否是http,没有的话进行拼接域名
 */
async function judgeHttp(url) {
    if (url && url.length > 8) {
        console.log(
            url.substr(0, 7).toLowerCase() === 'http://' || url.substr(0, 8).toLowerCase() === 'https://'
                ? url
                : configInfo.baseImgURL + url,
        );
        return url.substr(0, 7).toLowerCase() === 'http://' || url.substr(0, 8).toLowerCase() === 'https://'
            ? url
            : configInfo.baseImgURL + url;
    } else return url;
}
// 后支付加油、预约加油、会员码、付款码触发风控
async function oilTriggerRisk(accessToken) {
    return new Promise(async (resolve, reject) => {
        let userBasicInfo = await basicInfoQuery({}, { accessToken });
        if (userBasicInfo.success) {
            // 13 未认证 ，14 实名认证通过，15 实人认证通过
            if (userBasicInfo.data.identityAuthStatus == 13) {
                console.log('oilTriggerRisk===', userBasicInfo);
                //未认证，
                //需要弹出输入身份证和姓名弹窗
                resolve(userBasicInfo.data.identityAuthStatus);
            } else {
                //需要实人
                Store.dispatch('changeFacePop', true);
            }
        } else {
            reject();
        }
    });
}
function nextOilTriggerRisk(type = 11, riskType) {
    return new Promise(async (resolve, reject) => {
        let identityInfo = await identityAuthInfo({}, { is_error: true });
        console.log(identityInfo, 'identityInfo====');
        let isHarmony = Store.state.thirdIndex.isHarmony;
        if (identityInfo.success) {
            // #ifdef MP-MPAAS
            // "identityAuthStatus": 15, "identityNo": "xxxxxxxx", "realName": "", "identityType": 1
            if (!isHarmony) {
                triggerRiskAuth(identityInfo.data.realName, identityInfo.data.identityNo, type)
                    .then(res => {
                        resolve(res);
                    })
                    .catch(error => {
                        cnpcBridge.showToast(error);
                        reject();
                    });
            } else {
                harmonyRealPersonAuthentication
                    .startVerification({ name: identityInfo.data.realName, idNo: identityInfo.data.identityNo, type })
                    .then(response => {
                        if (response.success) {
                            resolve(response);
                        } else {
                            reject();
                        }
                    });
            }
            // #endif
            // #ifdef MP-WEIXIN
            wxRealPersonAuthentication
                .startVerification({ name: identityInfo.data.realName, idNo: identityInfo.data.identityNo, type })
                .then(response => {
                    if (response.success) {
                        resolve(response);
                    } else {
                        reject();
                    }
                });
            // #endif
            // #ifdef MP-ALIPAY || MP-TOUTIAO
            zfbRealPersonAuthentication
                .startVerification({ name: identityInfo.data.realName, idNo: identityInfo.data.identityNo, type })
                .then(response => {
                    if (response.success) {
                        resolve(response);
                    } else {
                        reject();
                    }
                });
            // #endif
            // #ifdef H5-CLOUD
            h5RealPersonAuthentication
                .startVerification({ name: identityInfo.data.realName, idNo: identityInfo.data.identityNo, type })
                .then(response => {
                    if (response) {
                        resolve(response);
                    } else {
                        reject();
                    }
                });
            // #endif
        } else reject();
    });
}
// 未使用
async function getMetaInfo() {
    return new Promise((resolve, reject) => {
        cnpcBridge.aliMetaInfo(metaInfo => {
            resolve(metaInfo);
        });
    });
}
// 触发实名、实人认证、进行人脸采集
async function triggerRiskAuth(name, idNumber, type = 11) {
    return new Promise(async (resolve, reject) => {
        function realNameFun() {
            return new Promise(async (realNameResolve, realNameReject) => {
                //先实名、后实人
                let params = {
                    realName: name,
                    idType: '1',
                    idNo: idNumber,
                    type: type, //场景类型： 7-绑卡 1-电子账户注册 2-忘记密码 11-风控校验 13-昆仑e享卡挂失
                };

                let realNameInfo = await realNameAuth(params, { is_error: true });
                if (realNameInfo.success) {
                    realNameResolve(realNameInfo);
                } else {
                    realNameReject(realNameInfo.message);
                }
            });
        }
        function RealPersonIdentifyFun(realNameInfo) {
            return new Promise(async (RealPersonResolve, RealPersonReject) => {
                // #ifdef MP-MPAAS
                let initFaceAuthParams = {
                    returnUrl: '',
                    metaInfo: await cnpcBridge.aliMetaInfo(),
                    verifyMode: '1',
                };

                let initRealPersonInfo = await initRealPersonIdentify(initFaceAuthParams, { is_error: true });

                if (initRealPersonInfo.success) {
                    cnpcBridge.aliFaceCollec(initRealPersonInfo.data.certifyId, async result => {
                        if (result.status) {
                            //采集成功
                            let params = {
                                type: type,
                                verifyUnique: initRealPersonInfo.data.verifyUnique,
                                certifyId: initRealPersonInfo.data.certifyId,
                                verifyMode: '1',
                            };
                            if (realNameInfo) {
                                params.authInfo = realNameInfo.data.authInfo;
                            }
                            let realPersonInfo = await realPersonIdentify(params, { is_error: true });
                            realPersonInfo.success ? RealPersonResolve(realPersonInfo) : RealPersonReject(realPersonInfo.message);
                        } else {
                            RealPersonReject(result.msg || '采集失败');
                        }
                    });
                } else {
                    RealPersonReject(initRealPersonInfo.message);
                }
                // #endif
                // #ifdef MP-WEIXIN
                // let identityInfo = await identityAuthInfo({}, { is_error: true });
                wxRealPersonAuthentication.startVerification({ name: name, idNo: idNumber, type }).then(response => {
                    if (response.success) {
                        resolve(response);
                    } else {
                        reject();
                    }
                });
                // #endif
                // #ifdef MP-ALIPAY
                zfbRealPersonAuthentication.startVerification({ name: name, idNo: idNumber, type }).then(response => {
                    if (response.success) {
                        resolve(response);
                    } else {
                        reject();
                    }
                });
                // #endif
                // #ifdef H5-CLOUD
                h5RealPersonAuthentication.startVerification({ name: name, idNo: idNumber, type })
                    .then(response => {
                        if (response) {
                            resolve(response);
                        } else {
                            reject();
                        }
                    });
                // #endif
            });
        }
        if (type == 6 || type == 9 || type == 13) {
            RealPersonIdentifyFun()
                .then(res => {
                    resolve(res);
                })
                .catch(error => {
                    reject(error);
                });
        } else {
            realNameFun()
                .then(res => {
                    RealPersonIdentifyFun(res)
                        .then(res2 => {
                            resolve(res2);
                        })
                        .catch(error2 => {
                            reject(error2);
                        });
                })
                .catch(error => {
                    reject(error);
                });
        }
    });
}
// 暂时没用
async function dialogCustomStyle(msg, code) {
    let codeText = '';
    if (code) {
        codeText =
            `<div style='height: 34px;width: 100%;margin-top:5px;background: linear-gradient(270deg, rgba(245,245,245,0) 0%, #F7F7F7 50%, rgba(245,245,245,0) 100%);font-size: 12px;color: #000000;line-height:34px;border-radius: 4px;'>错误码：` +
            code +
            `</div>`;
    }
    return `<div style='width: 100%;' ><div style='font-size: 14px;color: #333333;line-height: 18px;'>${msg}</div>${codeText}</div>`;
}
// 节流
function throttleUtil(func, wait = 1000, type = 1) {
    //节流， type 1马上执行 2 隔一段时间执行
    let previous = type == 1 ? 0 : Date.now();
    return function () {
        let now = Date.now();
        let context = this;
        let args = arguments;
        if (now - previous > wait) {
            func.apply(context, args);
            previous = now;
        }
    };
}

function debounce(func, wait = 1000) {
    let timer;
    return function () {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            func.apply(this, arguments);
        }, wait);
    };
}

/**
 * 退出登錄
 */
function logoutFun() {
    cnpcBridge.removeValueToNative('UserTokenInfo', async () => {
        let vuexObj = await cnpcBridge.getVuex2Info();
        vuexObj.login.token = '';
        vuexObj.login.token3 = '';
        vuexObj.login.gsmsToken = '';
        vuexObj.login.refreshToken = '';
        vuexObj.login.expiresIn = '';
        vuexObj.login.needBindPhone = '';
        vuexObj.isThirdVer = false;
        vuexObj.lastJumpVersion = '';
        cnpcBridge.setValueToNative('vuex', encodeURIComponent(JSON.stringify(vuexObj)), () => {
            // #s-ifndef cnpc-harmony
            accountCenter.deleteAllMember(() => {});
            // #s-endif
            cnpcBridge.loginOut();
        });
    });
}
// 将queryString解析成对象
function parseQueryString(queryString) {
    const params = {};
    const pairs = queryString.split('&');

    for (let pair of pairs) {
        const [key, value] = pair.split('=');
        params[key] = decodeURIComponent(value);
    }
    return params;
}
// 提供一个对象转换为字符串的方法
function obj2str(obj) {
    let str = '';
    for (let key in obj) {
        str += key + '=' + obj[key] + '&';
    }
    return str.substring(0, str.length - 1);
}
/**
 * 微信-支付宝退出登录
 */
//  function miniLogoutFun() {

//  }
/**
 * @description  : 截取时间字符串
 * @param         {*} data:时间字符串
 * @return        {*}
 */
function timeSplit(data) {
    return data ? String(data).substring(0, 5) : '';
}
/**
 *
 */
async function judgeLocationAuth() {
    return new Promise(async (resolve, reject) => {
        let info = await cnpcBridge.checkPermission();
        if (info && info.appStatus) {
            resolve(true);
        } else {
            cnpcBridge
                .openPermissions({
                    code: 'location',
                    explain: '位置权限使用说明',
                    detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
                })
                .then(value => {
                    if (value) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                });
        }
    });
}
// 截取参数
function parseUrl(url, key) {
    let arr = url.split('?');
    let querys = arr[1];
    let params = querys.split('&');
    for (let i = 0; i < params.length; i++) {
        let vars = params[i];
        let arr2 = vars.split('=');
        if (arr2[0] == key) {
            return arr2[1];
        }
    }
    return '';
}
function extractKeyValuePairs(url) {
    const keyValuePairs = {};
    const queryString = url.split('?')[1]; // 获取问号后面的部分，即查询字符串部分

    if (queryString) {
        const pairs = queryString.split('&');

        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            keyValuePairs[key] = value;
        });
    }

    return keyValuePairs;
}
/**
 * @description  :  将JSON变为字符串返回
 * @param         {*}
 * @return        {*}
 */
function convertToRouteString(obj) {
    let routeString = '';

    // 遍历对象的键值对
    for (let key in obj) {
        if (typeof obj[key] === 'string') {
            // 如果值是字符串，直接添加到路由字符串中
            routeString += `${key}=${encodeURIComponent(obj[key])}&`;
        } else {
            // 其他类型的值可能需要进一步处理，这里假设直接转换为字符串
            routeString += `${key}=${encodeURIComponent(String(obj[key]))}&`;
        }
    }

    // 去除最后一个&符号
    routeString = routeString.slice(0, -1);

    return routeString;
}
/**
 * @description  :  递归遍历深拷贝
 * 首先判断传入的对象 obj 是否为基本类型（不是对象或者为 null），如果是则直接返回该对象，因为基本类型不需要进行深拷贝。
 * 如果是数组，就创建一个新的空数组 copy，然后遍历原数组中的每个元素，通过递归调用 deepCopy 函数对每个元素进行深拷贝并放入新数组中。
 * 如果是普通对象，创建一个新的空对象 copy，然后遍历原对象的每个属性，同样通过递归调用 deepCopy 函数对每个属性进行深拷贝并放入新对象中。
 * @param         {*}
 * @return        {*}
 */
function deepCopy(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    let copy;
    if (Array.isArray(obj)) {
        copy = [];
        for (let i = 0; i < obj.length; i++) {
            copy[i] = deepCopy(obj[i]);
        }
    } else {
        copy = {};
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                copy[key] = deepCopy(obj[key]);
            }
        }
    }

    return copy;
}

export default {
    notOpenEwallet,
    eWalletNormal,
    cardOperationPwd,
    biometricPay,
    oilTriggerRisk,
    judgeBiometricPayStatus,
    judgeHttp,
    throttleUtil,
    logoutFun,
    triggerRiskAuth,
    setPassword,
    refreshWallet,
    parseQueryString,
    obj2str,
    timeSplit,
    nextOilTriggerRisk,
    judgeLocationAuth,
    parseUrl,
    extractKeyValuePairs,
    convertToRouteString,
    deepCopy,
    debounce,
};
