<template>
    <div>
        <!--自定义积分能源币时间选择-->
        <div class="cc_area_mask" v-if="show == true"></div>
        <div :class="'cc_area_view ' + (show ? 'show' : 'hide')">
            <div class="cc_area_view_btns">
                <text class="cc_area_view_btn_cancle font_color" @tap="handleNYZAreaCancle">取消</text>
                <text>选择时间</text>
                <text class="cc_area_view_btn_sure font_color" @tap="handleNYZAreaSelect">确定</text>
            </div>
            <picker-view class="cc_area_pick_view" indicator-style="height: 50px;" @change="handleNYZAreaChange" :value="value">
                <picker-view-column>
                    <view class="item" v-for="(item, index) in years" :key="index">{{ item }}年</view>
                </picker-view-column>
                <picker-view-column>
                    <view class="item" v-for="(item, index) in months" :key="index">{{ item }}月</view>
                </picker-view-column>
            </picker-view>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        const date = new Date();
        const years = [];
        const year = date.getFullYear();
        const months = [];
        const month = date.getMonth() + 1;

        for (let i = month <= 2 ? date.getFullYear() - 1 : date.getFullYear(); i <= date.getFullYear(); i++) {
            years.push(i);
        }
        for (let i = month <= 2 ? 1 : month - 2; i <= month; i++) {
            months.push(i);
        }

        return {
            title: 'picker-view',
            years,
            year,
            months,
            month,
            value: [years.length - 1, months.length - 1],
            index: [years.length - 1, months.length - 1],
            show: true,
            // 是否滑动
            slidingOrNotFlag: false,
            yearnum: '',
            monthnum: '',
            // echoCityIndex: 0,
            // echoProvinceIndex: 0
        };
    },

    components: {},

    mounted() {},
    methods: {
        handleNYZAreaChange: function (e) {
            this.slidingOrNotFlag = true;
            var that = this;
            var value = e.detail.value;
            /**
             * 滚动的是省
             * 省改变 市、区都不变
             */
            if (this.index[0] != value[0]) {
                this.index = [value[0], 0];
                if (this.month <= 2) {
                    if (value[0] == 0) {
                        this.months = [];
                        if (this.month == 1) {
                            for (let i = 11; i <= 12; i++) {
                                this.months.push(i);
                            }
                        } else if (this.month == 2) {
                            for (let i = 12; i <= 12; i++) {
                                this.months.push(i);
                            }
                        }
                    } else if (value[0] == 1) {
                        this.months = [];
                        for (let i = 1; i <= this.month; i++) {
                            this.months.push(i);
                        }
                    }
                }
                this.yearnum = this.years[this.index[0]];
                this.monthnum = this.months[this.index[1]];
                return;
            } else if (this.index[1] != value[1]) {
                /**
                 * 市改变了 省不变 区变
                 */
                this.index = [value[0], value[1]];
                this.yearnum = this.yearnum ? this.yearnum : this.years[this.value[0]];
                this.monthnum = this.months[this.index[1]];
            }
            // if(!this.yearnum){
            //     this.yearnum = this.years[this.index[0]]
            // }
            // if(!this.monthnum){
            //     this.monthnum = this.months[this.index[1]]
            // }
        },
        handleNYZAreaSelect: function (e) {
            //
            // return
            this.$emit('sureSelectDateTime', {
                year: this.yearnum ? this.yearnum : this.years[this.index[0]],
                month: this.monthnum ? this.monthnum : this.months[this.index[1]],
            });
        },

        /**
         * 取消按钮的点击事件
         */
        handleNYZAreaCancle: function (e) {
            this.$emit('hideShow');
        },
    },
};
</script>
<style scoped lang="scss">
.cc_area_view {
    width: 100%;
    position: fixed;
    bottom: -1000px;
    left: 0px;
    background-color: #fff;
    z-index: 21;
    transition: all 0.3s;
}

.cc_area_pick_view {
    height: 200px;
    text-align: center;
    width: 100%;
}

.cc_area_colum_view {
    line-height: 35px;
    text-align: center;
    font-size: 28upx;
}

.hide {
    bottom: -1000upx;
    transition: all 0.3s;
}

.show {
    bottom: 0upx;
    transition: all 0.3s;
}

.cc_area_view_btns {
    background-color: #fff;
    border-bottom: 1px solid #eeeeee;
    font-size: 30upx;
    padding: 18upx 0upx;
    display: flex;
    justify-content: space-between;
}

.cc_area_view_btns > text {
    // display: inline-block;
    // word-spacing: 4upx;
    // letter-spacing: 4upx;
}

.cc_area_view_btn_cancle {
    color: #939393;
    padding-right: 20upx;
    padding-left: 25upx;
}

.cc_area_view_btn_sure {
    float: right;
    padding-left: 20upx;
    padding-right: 25upx;
}

.cc_area_mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(28, 28, 28, 0.6);
    position: absolute;
    top: 0upx;
    left: 0upx;
    z-index: 20;
}

.font_color {
    color: #e64f22;
}

.picker-view {
    width: 100%;
    height: 300px;
    margin-top: 10px;
}

.item {
    line-height: 50px;
    text-align: center;
}
</style>
