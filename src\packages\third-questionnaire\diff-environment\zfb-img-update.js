import { imageUpload, putUpload, imageDelete, imageDownload, imageGetTempPostInfo } from '../../../s-kit/js/v3-http/https3/user.js';
import { pathToBase64, base64ToPath } from 'image-tools';

export default {
    // #ifdef MP-ALIPAY
    computed: {},

    data() {
        return {};
    },

    onShow() {},
    methods: {
        // 上传图片点击图片
        handlerUpload(type, commentIndex) {
            // 获取当前时间戳
            const timeData = new Date().getTime();
            // 从本地相册选择图片或使用相机拍照。
            uni.chooseImage({
                count: 1,
                sizeType: ['original'], // original 原图，compressed 压缩图，默认二者都有 ,这里使用原图是因为选择压缩图，选择图片时会有白条闪一下
                success: async res => {
                    console.log(res, '=====uni.chooseImage');
                    console.log(res.scene, '=====tempFiles');
                    // 存储图片push进相应的数组进行页面回显
                    let srcImg = res.tempFilePaths[0];

                    // 定义一个接收图片大小的变量

                    let size = '';
                    // 当前判断
                    // res.tempFiles.length 判断当前拍摄或者选择图片时数量不为空
                    // res.tempFiles[0].size 判断拍摄图片时如果size不存在的情况
                    // 如果成立则代表是拍摄图片，反之是选择图片
                    if (res.scene == 'camera') {
                        console.log('拍摄照片回来的path=====', res.tempFiles[0].path);
                        // obtainTheSizeOfThePhotographedImage  拍摄图片获取size方法
                        size = await this.obtainTheSizeOfThePhotographedImage(res.tempFiles[0].path);
                        console.log(size, '拍摄照片回来的size=====');
                    } else {
                        size = res.tempFiles[0].size;
                        console.log(size, '选择相册回来的size=====');
                    }

                    console.log('size---', size);
                    // 10485760 = 10M
                    // 判断当亲图片是否存在size和是否超过10MB
                    if (!size || Number(size) >= 10485760) {
                        uni.showToast({
                            title: '图片文件大小超限，最大10M，请重新上传',
                            icon: 'none',
                        });
                        return
                    }
                    // 将上面的图片通过index push进相应的数组
                    this.questionListDataRows[commentIndex].picList.push(srcImg);
                    this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
                    /**
                     * 
                     * 上传场景：
                        1—订单评价、扫码评价图片上传；
                        2—调查问卷图片上传；
                        如果该字段未传值，默认为1，后续可能会增加其他上传场景，也需要区分上传目录
                     * */ 
                    // 转换成base64上传到oss桶里
                    pathToBase64(srcImg).then(async data => {
                        let res3 = await imageUpload({
                            contentType: 'image/png',
                            fileName: timeData,
                            type:Number(2)
                        });
                        uni.showLoading({
                            title: '上传中...',
                            mask: true,
                        });
                        console.log('data---', data);
                        console.log('res---', res3.data);
                        const arrayBuffer = uni.base64ToArrayBuffer(data.split(',')[1]);
                        // const arrayBuffer = this.base64ToBlob(data);
                        console.log('arrayBuffer---', arrayBuffer);
                        // 上传图片的方法
                        this.imageUpload(res3.data.tempPath, arrayBuffer, 'PUT', 'image/png').then(async data => {
                            //后端接口需要
                            uni.hideLoading();
                            console.log(res3.data.objectKey, 'res3.data.objectKey');
                            this.questionListDataRows[commentIndex].images.push(res3.data.objectKey);
                            this.questionListDataRows[commentIndex].imagesUrl.push(res3.data.tempPath);
                            this.$set(this.questionListDataRows, commentIndex, this.questionListDataRows[commentIndex]);
                        });
                    });
                },
            });
            console.log('datat--55555555', this.questionListDataRows);
        },
        imageUpload(resData, arrayBuffer, method, ContentType) {
            return new Promise((resolve, reject) => {
                my.request({
                    url: resData,
                    method: method,
                    header: {
                        'Content-Type': ContentType,
                    },
                    data: arrayBuffer,
                    success: res => {
                        resolve(res);
                        uni.showToast({
                            title: '上传成功',
                            icon: 'none',
                            duration: 2000,
                        });
                        console.log('res--', res);
                        resolve(res);
                    },
                    fail: err => {
                        uni.showToast({
                            title: '上传失败',
                            icon: 'none',
                            duration: 2000,
                        });
                        console.log('reserr--', err);
                        reject(err);
                    },
                });
            });
        },

        /**
         * 预览图片
         */

        previewImage(imgIndex, commentIndex) {
            this.questionListDataRows = this.questionListDataRows.map((element, index) => {
                if (index == commentIndex) {
                    uni.previewImage({
                        current: imgIndex,
                        urls: element.picList,
                    });
                }
                return element;
            });
        },
    },
    // #endif
};
