<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view-page">
            <img src="../../image/<EMAIL>" alt class="bg-page" />
            <div class="page-wrap pullDownRefreshView">
                <zj-navbar
                    :border-bottom="false"
                    title="设置"
                    height="44"
                    titleColor="#fff"
                    back-icon-color="#fff"
                    :background="{ background: 'transparent' }"
                ></zj-navbar>
                <div class="f-1 mh-0 connent">
                    <div class="fl-row fl-jus-bet marrl-18-16">
                        <div class="header_box fl-row fl-al-cen f-1">
                            <div class="header-div">
                                <img class="head_style" src="../../image/header.png" />
                            </div>
                            <div class="font-18 color-fff weight-bold marl10">{{ memberData.alias || '会员名称' }}</div>
                        </div>
                    </div>
                    <div class="card-default mar12">
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="jumpWay('account')">
                            <div class="f-1">账号与安全</div>
                            <div class="arroe-right-333"></div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 marlr12" @click="jumpWay('profile')">
                            <div class="f-1">个人资料</div>
                            <div class="arroe-right-333"></div>
                        </div>
                    </div>

                    <div class="card-default mar12">
                        <!-- #ifdef MP-MPAAS  -->
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="cacheClear">
                            <div class="f-1">清理缓存</div>
                            <div class="fl-row fl-al-cen" v-if="cacheSize">
                                <div class="font-14 color-999 weight-400 marr6">缓存{{ cacheSize }}</div>
                                <div class="arroe-right-333"></div>
                            </div>
                            <div class="fl-row fl-al-cen" v-else>
                                <div class="arroe-right-333"></div>
                            </div>
                        </div>
                        <div class="fl-row fl-al-cen padd12 line_bottom marlr12" @click="checkUpdateApp()">
                            <div class="f-1">版本号</div>
                            <div class="fl-row fl-al-cen" v-if="appVersion">
                                <div class="font-14 color-999 weight-400">V{{ appVersion }}</div>
                                <div class="font-10 color-E64F22 weight-400 border-E64F22" v-if="updataVersion">是否为最新版本 </div>
                            </div>
                        </div>
                        <!-- #endif -->

                        <div class="fl-row fl-al-cen padd12 marlr12" @click="jumpWay('about')">
                            <div class="f-1">关于我们</div>
                            <div class="arroe-right-333"></div>
                        </div>
                    </div>

                    <div class="card-default back-btn" @click="userOut">退出登录</div>
                </div>
                <div class="fl-row fl-al-jus-cen font-12 cololr-999 weight-400 absolute-bottom">
                    <div @click="clickXieyi(63)">《隐私政策》</div>
                    <div @click="clickXieyi(17)">《用户协议》</div>
                    <div v-if="baseType == 'sit' && isHarmony">{{miniVersion}}</div>
                </div>
            </div>

            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { mapGetters,mapState } from 'vuex';
import { basicInfoQuery } from '../../../../s-kit/js/v3-http/https3/oilCard/index';
import { memberInfo } from '../../../../s-kit/js/v3-http/https3/classInterest/index';
import { logoutApi } from '../../../../s-kit/js/v3-http/https3/user.js';

// #ifdef MP-MPAAS
import appMixin from './diff-environment/app-logout.js';
// #endif
// #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
import wxMixin from './diff-environment/wx-logout.js';
import zfbMixin from './diff-environment/zfb-logout.js';
// #endif
// #ifdef MP-TOUTIAO
import ttMixin from './diff-environment/tt-logout.js';
// #endif
// #ifdef H5-CLOUD
import cloudMixin from './diff-environment/cloud-logout.js';
// #endif
import { clientCode,appId,baseType} from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    data() {
        return {
            headImg: require('../../image/head.png'),
            facelogin: true,
            appVersion: '',
            updataVersion: false,
            cacheSize: '',
            memberData: '',
            miniVersion:'',
            baseType:''
        };
    },
    mixins: [
        publicMixinsApi,
        // #ifdef MP-MPAAS
        appMixin,
        // #endif
        // #ifndef MP-MPAAS || MP-TOUTIAO || H5-CLOUD
        wxMixin,
        zfbMixin,
        // #endif
        // #ifdef MP-TOUTIAO
        ttMixin,
        // #endif
        // #ifdef H5-CLOUD
        cloudMixin
        // #endif
    ],
    onLoad(options) {
        // console.log(this.$cnpcBridge, '00000000-cnpcBridge');
        if (Object.keys(options).length != 0 && options.data) {
            let params = JSON.parse(decodeURIComponent(options.data));
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'settingPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
    },
    async onShow() {
        // #ifdef MP-MPAAS
        // 获取设备公共参数
        let data = await this.$cnpcBridge.getCommonArgs();
        console.log(appId,'appId---')
        this.miniVersion = await this.$cnpcBridge.getMiniVersion(appId)
        this.baseType = baseType
        this.appVersion = data.appVersion;
        this.$cnpcBridge.getCacheSize(data => {
            console.log(data);
            let obj = data;
            this.cacheSize = data.result;
        });
        // #endif
        // this.viewCache()
        // 查询用户详细信息接口(使用脱敏身份证号字段)
        this.getBasicInfoQuery();
    },
    mounted() {},
    methods: {
        // viewCache() {
        //   let res = uni.getStorageInfoSync();
        //   let cacheSize = res.keys.length > 0 ? res.currentSize : 0;
        //   // this.cacheSize = Number(res.currentSize) + Number(cacheSize)
        // },
        /**
         * @description     : 清除缓存
         * @return        {*}
         */
        clearCache() {
            uni.clearStorageSync();
            this.cacheSize = '';
            this.viewCache();
        },
        /**
         * @description     : 退出登录
         * @return        {*}
         */
        async userOut() {
            let that = this;
            this.$store.dispatch('zjShowModal', {
                title: '提示',
                content: '登出后，下次使用中石油加油服务需要再次登录',
                confirmText: '退出登录',
                cancelText: '暂不退出',
                cancelColor: '#666666',
                confirmColor: '#FF6B2C',
                success: async res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        // #ifdef MP-MPAAS
                        that.$cnpcBridge.setValueToNative('Exit_login_identity', true);
                        // #endif
                        that.logout();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });

            // this.$sKit.layer.useRouter('1', '', 'navigateBack')
        },
        /**
         * @description     : 更新版本号
         * @return        {*}
         */
        checkUpdateApp() {
            this.$cnpcBridge.checkUpdate();
        },
        /**
         * @description     : 页面跳转处理
         * @return        {*}
         */
        jumpWay(data) {
            let url;
            if (data == 'account') {
                url = '/packages/third-account/pages/account-security/main';
            } else if (data == 'profile') {
                url = '/packages/third-my-center/pages/profile/main';
            } else if (data == 'about') {
                url = '/packages/third-account/pages/about-us/main';
            }
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description     : 清除缓存
         * @return        {*}
         */
        cacheClear() {
            this.$store.dispatch('zjShowModal', {
                content: '是否确认清除缓存？',
                confirmText: '确认',
                cancelText: '取消',
                showCancel: true,
                confirmColor: '#333333',
                cancelColor: '#666666',
                success: res => {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        this.$cnpcBridge.clearCache(data => {
                            let obj = data;
                            if (data.result) {
                                this.cacheSize = '';
                            }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                },
            });
        },
        /**
         * @description     ：// 查询用户详细信息接口(使用脱敏身份证号字段)
         * @return        {*}
         */
        async getBasicInfoQuery() {
            let res = await basicInfoQuery();
            if (res.success) {
                console.log(res.data, '查询支用户脱敏身份证信息');
                this.memberData = res.data;
            }
        },
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
        // ...mapGetters(['memberAccountInfo','memberBaseInfo'])
    },
    beforeDestroy() {
        this.$store.dispatch('zjHideModal');
    },
};
</script>
<style scoped lang="scss">
.view-page {
    width: 100%;
    height: 100%;
    background: #f7f7fb;
    position: relative;

    .bg-page {
        width: 100%;
        height: 476px;
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        // z-index: 1;
    }

    .connent {
        position: relative;
        z-index: 1;
    }

    .page-wrap {
        .mart23 {
            margin-top: 23px;
        }

        .head_style {
            width: 48px;
            height: 48px;
            border-radius: 50% 50%;
            border: 1px solid #ffffff;
        }

        .border-E64F22 {
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #e64f22;
            line-height: 21px;
            height: 21px;
            width: 78px;
            text-align: center;
            margin-left: 6px;
        }

        .back-btn {
            height: 44px;
            line-height: 44px;
            font-size: 18px;
            font-weight: 500;
            color: #e64f22;
            text-align: center;
            margin: 16px 16px 0;
        }

        .absolute-bottom {
            position: absolute;
            bottom: 30px;
            left: 0;
            right: 0;
            z-index: 2;
        }
    }

    .marrl-18-16 {
        margin: 23px 18px 0 16px;
    }

    .mar12 {
        margin: 12px 12px 0;
    }

    .padd12 {
        padding: 14px 0;
    }

    .marlr12 {
        margin: 0 12px;
    }

    .marr6 {
        margin-right: 6px;
    }

    .marl10 {
        margin-left: 10px;
    }
}
</style>
