<template>
    <div class="page-view">
        <div class="charge-content-view fl-column bg-fff">
            <div class="top-area bg-fff">
                <div class="stationinfo fl-row fl-jus-bet">
                    <div class="detail f-1">
                        <div class="name-area fl-row fl-al-cen">
                            <p class="name font-16 weight-600 color-000">{{ selectMarkerV3.orgName }}</p>
                        </div>
                        <div class="font-12 weight-400 color-333 pad-t-6">{{ selectMarkerV3.address }}</div>
                        <div class="font-12 color-E64F22 fl-row fl-al-cen pad-t-6">
                            <div v-for="(item, index) in fuelData" :key="index" class="">{{ item.fuelName }}</div>
                        </div>
                    </div>
                    <img class="navt-to" @click="clickNaviStateion" src="../../image/stationinfo-icon-location.png" alt />
                </div>
            </div>
            <div class="line_bottom_dashed margin-8-l-r"></div>
            <div class="center-area palr16">
                <div class="fl-row fl-al-cen con-list">
                    <div class="item bg-F3F3F6 te-center" v-for="(tag, index) in selectMarkerV3.tagList" :key="index">
                        <div class="item-cell">{{ strReplace(tag) }}</div>
                    </div>
                </div>
            </div>
            <div class="phone-area margin-8-l-r">
                <div class="fl-row input-area fl-al-cen fl-jus-bet" @click="boda">
                    <div class="font-15 color-000 f-1">联系电话 {{ selectMarkerV3.phone || '' }}</div>
                    <div class="fl-row">
                        <div class="bor-1"></div>
                        <div class="font-14 color-E64F22">立即拨打</div>
                    </div>
                </div>
            </div>
            <!-- <div class="active-area margin-8-l-r">
                <div class="font-14 color-000 weight-500 padb11">今日促销活动</div>
                <div class="fl-row fl-al-cen">
                    <div class="logo">
                        <img src="../../image/logo-bg.png" alt="" />
                    </div>
                    <div class="font-14 color-666 weight-400 marl-17"
                        >今日促销：{{ selectMarkerV3.activityList[0].activityName || '' }}</div
                    >
                </div>
            </div> -->
            <div class="bottom-area fl-row fl-jus-bet palr16">
                <div class="color-fff font-16 weight-500 primary-btn te-center border-rad-8" @click="goToOil">去加油</div>
                <div class="color-E64F22 font-16 weight-500 btn-plain te-center border-rad-8" @click="changeStationAction">更多网点</div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';

export default {
    data() {
        return {};
    },
    async created() {
        /**
         * @description : 获取油品集合
         * @return        {*}
         */
        if (this.selectMarkerV3 && this.selectMarkerV3.orgCode) {
            await this.$store.dispatch('getFuelGunByOrgCodePost');
        }
    },
    mounted() {},
    computed: {
        ...mapState({
            // 获取该网点的油品编码集合
            fuelData: state => state.locationV3_app.fuelData,
            selectMarkerV3: state => state.locationV3_app.selectMarkerV3,
        }),
    },
    methods: {
        // 点击导航
        clickNaviStateion() {
            // if (this.selectMarkerV3.openState != 1) {
            //   return uni.showModal({
            //     title: "提示",
            //     content: `该油站暂未开通线上加油功能，是否继续前往？`,
            //     confirmColor: '#FF8200',
            //     cancelText: "更换油站",
            //     // confirmText: "去导航",
            //     success: (res) => {
            //       if (res.confirm) {
            //         uni.openLocation({
            //           latitude: Number(this.selectMarkerV3.latitude),
            //           longitude: Number(this.selectMarkerV3.longitude),
            //           name: this.selectMarkerV3.stationName,
            //           address: this.selectMarkerV3.address || this.selectMarkerV3.stationName,
            //         });
            //       }
            //     },
            //   });
            // } else {
            // #ifdef MP-MPAAS
            this.$cnpcBridge.openLocation({
                latitude: Number(this.selectMarkerV3.latitude),
                longitude: Number(this.selectMarkerV3.longitude),
                name: this.selectMarkerV3.stationName,
                address: this.selectMarkerV3.address || this.selectMarkerV3.stationName,
            });
            // #endif
            // }
        },
        /**
         * @description : 不显示"站"字
         * @return        {*}
         */
        strReplace(str) {
            return str ? str.replace(/站/g, '') : '';
        },
        /**
         * @description : 拨打电话
         * @return        {*}
         */
        boda() {
            uni.makePhoneCall({
                phoneNumber: this.selectMarkerV3.phone,
            });
        },
        /**
         * @description : 去加油
         * @return        {*}
         */
        goToOil() {
            let URL = '/packages/third-scan-code-payment/pages/home-code/main';
            let params = {
                tabType: 1,
            };
            let type = 'reLaunch';
            this.$sKit.layer.useRouter(URL, params, type);
        },
        /**
         * @description : 更换油站
         * @return        {*}
         */
        changeStationAction() {
            let URL = '/packages/third-oil-charge-payment/pages/oil-station-search/main';
            let params = {};
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(URL, params, type);
        },
    },

    components: {},
    watch: {
        // 监听selectMarkerV3.orgCode变化 实现页面变化
        '$store.state.locationV3_app.selectMarkerV3.orgCode': {
            handler: function (newValue, oldValue) {
                if (newValue) {
                    // if (this.bookingRefueling == "") {
                    this.$store.dispatch('getFuelGunByOrgCodePost');
                    // }
                }
            },
            immediate: true,
            deep: true,
        },
    },
};
</script>
<style scoped lang="scss">
.page-view {
    height: 100%;
    width: 100%;
}
.palr16 {
    padding: 0 16px;
}
.margin-8-l-r {
    margin: 0 16px;
}
.marl-17 {
    margin-left: 17px;
}
.padb11 {
    padding-bottom: 11px;
}

.charge-content-view {
    height: 100%;
    border-radius: 10px 10px 0px 0px;
    padding-bottom: 16px;

    .top-area {
        padding: 12px 12px 0 12px;
        overflow: hidden;
        margin: 0px 15px;

        .stationinfo {
            padding-bottom: 12px;
            .detail {
                .name-area {
                    .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .pad-t-6 {
                    padding-top: 6px;
                }
            }

            .navt-to {
                width: 120rpx;
                height: 145rpx;
                border-radius: 8px;
                margin-left: 20px;
                display: block;
            }
        }
        .margin-8-l-r {
            margin: 0 8px;
        }
    }

    .center-area {
        padding-top: 12px;
        .con-list {
            justify-content: flex-start;
            width: 100%;
            flex-wrap: wrap;
            .item {
                width: 78px;
                height: 40px;
                line-height: 40px;
                border-radius: 4px;
                font-size: 14px;
                color: #333333;
                margin-bottom: 12px;
                margin-left: 6px;
                box-sizing: border-box;
            }
            .item:nth-of-type(4n + 0) {
                margin-right: 0px;
            }
            .oil-type-sel {
                background: rgba(230, 79, 34, 0.16);
                border-radius: 4px;
                border: 1px solid #e64f22;
                color: #e64f22;
            }
        }
    }
    .phone-area {
        padding: 12px 0 16px 0;
        .input-area {
            height: 40px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #dddddd;
            padding: 0px 12px;
        }
        .bor-1 {
            position: relative;
            margin: 0 10px 0 0;

            &.bor-1::after {
                content: '';
                position: absolute;
                left: 0;
                top: 2px;
                border-right: 1px solid #999;
                width: 1px;
                height: 15px;
            }
        }
    }
    .active-area {
        padding-bottom: 20px;
        .logo {
            img {
                width: 28px;
                height: 28px;
            }
        }
    }
    .bottom-area {
        // padding-top: 40px;
        div {
            height: 44px;
            width: 166px;
            line-height: 44px;
        }
    }
}
</style>
