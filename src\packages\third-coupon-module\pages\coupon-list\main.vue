
<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="view fl-column mh-0 p-bf">
            <zj-navbar :height="44" title="我的电子券"></zj-navbar>
            <div class="f-1 fl-column mh-0">
                <div class="tabs_style p-LR-16 mh-0 fl-row fl-al-cen">
                    <div
                        v-for="(tab, index) in tabs"
                        :key="index"
                        :class="{ 'color-E64F22': selectId == tab.id }"
                        class="tab_style"
                        @click="selectClick(tab, index)"
                    >
                        <div class="font-15 weight-500">{{ tab.title }}({{ tab.quantity || 0 }})</div>
                    </div>
                </div>
                <div class="line_bottom"></div>
                <div class="type-sort p-LR-16 fl-row mh-0 fl-al-cen font-13 weight-400 color-333">
                    <div
                        v-for="(item, index) in typeSortArray"
                        :key="index"
                        class="sort-item bg-F3F3F6 border-rad-4"
                        :class="{ selectSorted: sortId === item.id }"
                        @click="selectSortClick(item, index)"
                        >{{ item.content }}</div
                    >
                </div>
                <div
                    class="reminder p-LR-16 fl-row fl-al-cen bg-FFF7DC font-12 weight-400 color-333"
                    :style="{ marginBottom: showEmpty ? '200rpx' : '0rpx' }"
                    >温馨提示：现金券仅可在室内扫码支付，
                    {{
                        oilCouponLimit && noilCouponLimit
                            ? `当前地区每人每日仅可使用油品券 ${oilCouponLimit} 张,非油券 ${noilCouponLimit} 张。`
                            : `当前地区油品券每人每日限用1张,非油券每人每日限用5张。`
                    }}
                </div>
                <div class="coupon-wrap f-1 mh-0">
                    <zj-data-list
                        background="#F7F7FB"
                        ref="dataList"
                        emptyText="暂无电子券"
                        :showEmpty="showEmpty"
                        :emptyImage="noPicture"
                        @scrolltolower="scrolltolower"
                        @refreshPullDown="refreshPullDown"
                    >
                        <div class="padding-16">
                            <div v-for="(item, index) in couponArray" :key="index">
                                <div
                                    class="bg-coupon fl-column border-rad-8"
                                    :class="{
                                        'bg-coupon': item.bgColor,
                                        toTakeEffect: !item.bgColor,
                                    }"
                                >
                                    <!-- 左上角券类型 -->
                                    <div
                                        class="upperLeft"
                                        :class="{
                                            'bg-ff6133': item.bgColor,
                                            'bg-999': !item.bgColor,
                                        }"
                                        >{{ differentiationType(item.kind) }}</div
                                    >
                                    <div class="content-wrap fl-column">
                                        <div class="content-wrap-header fl-row fl-al-cen" @click="unusedDetails(item, index)">
                                            <div class="content-left">
                                                <div v-if="!item.templatePicOss" class="content-left-price">
                                                    <div
                                                        :class="{
                                                            'color-E64F22': item.bgColor,
                                                            'color-666': !item.bgColor,
                                                        }"
                                                        class="price fl-row fl-al-base te-center fl-jus-cen"
                                                    >
                                                        <div
                                                            v-if="item.couponType && item.couponType != '40'"
                                                            class="symbol font-14 weight-400"
                                                            >&yen;</div
                                                        >
                                                        <div class="font-28 weight-600 te-center">
                                                            {{
                                                                item.couponType && item.couponType != '40'
                                                                    ? item.amount
                                                                    : item.discountValue
                                                            }}
                                                        </div>
                                                        <div
                                                            v-if="item.couponType && item.couponType == '40'"
                                                            class="symbol font-14 weight-400"
                                                            >折</div
                                                        >
                                                    </div>
                                                    <div
                                                        :class="{
                                                            'color-EB5130': item.bgColor,
                                                            'color-666': !item.bgColor,
                                                        }"
                                                        class="font-13 te-center weight-400"
                                                        >{{ thresholdAmount(item) }}</div
                                                    >
                                                </div>

                                                <img class="img-size" v-if="item.templatePicOss" :src="item.templatePicOss" alt="" />
                                            </div>
                                            <div class="content-sx"></div>
                                            <div class="content-right f-1 fl-column fl-jus-bet">
                                                <div class="title-wrap fl-row fl-jus-bet">
                                                    <div class="coupon-title f-1 font-14 color-1E1E1E weight-600">{{ item.title }}</div>
                                                    <!-- <img class="right-img" src="../../images/almostExpired.png" alt="" /> -->
                                                </div>
                                                <div class="certificate-time fl-row fl-jus-bet fl-al-end">
                                                    <div class="fl-column fl-jus-bet">
                                                        <div
                                                            class="type-cou bg-transparent btn-plain-tran border-rad-2 font-9 weight-400"
                                                            :class="{
                                                                'color-FA6400 border-fa6400': item.bgColor,
                                                                'color-666 border-999': !item.bgColor,
                                                            }"
                                                            >{{ item.type == 1 ? '油品券' : '非油券' }}</div
                                                        >
                                                        <div class="time font-10 color-999 weight-400">{{ screen(item) }}</div>
                                                    </div>
                                                    <div
                                                        v-if="item.kind === 1"
                                                        :class="{
                                                            'bg-90': item.bgColor,
                                                            'bg-D0D0D0': !item.bgColor,
                                                        }"
                                                        class="useBtn border-rad-4 te-center color-fff"
                                                        >{{ buttonText(selectId, item) }}</div
                                                    >
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            class="content-wrap-footer"
                                            :style="{
                                                backgroundImage: background(item.bgColor),
                                                transition: 'all 0.3s ease-out',
                                            }"
                                        >
                                            <div class="custom-collapse">
                                                <div
                                                    class="collapse-header"
                                                    :style="{
                                                        backgroundImage: background(item.bgColor),
                                                    }"
                                                    
                                                >                                                   
                                                 <div class="describe-header" @click.stop="toggleCollapse(index)">
                                                        <div class="header-title color-333 font-12"
                                                            >使用说明{{ item.kind === 1 ? '(需持核销码进行核销)' : '' }}</div
                                                        >
                                                        <img
                                                            class="arrow"
                                                            :class="{ rotate: activeIndexes.includes(index) }"
                                                            src="../../images/pull.png"
                                                            alt="箭头"
                                                        />
                                                    </div>                                                    
                                                    <div class="nearby-branches-header font-12" @click.stop="outletsOpenHandle(item,index)">
                                                        附近可用网点 
                                                        <img
                                                            class="arrow"
                                                            :class="{ rotate: activeOutletsIndexes.includes(index) }"
                                                            src="../../images/pull.png"
                                                            alt="箭头"
                                                        /></div>
                                                </div>                                                
                                                <div class="collapse-wrapper">
                                                    <div :class="{ expanded: activeIndexes.includes(index) }" class="collapse-content">
                                                        <div class="content">
                                                            <div class="rulrText">{{ item.rule }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </zj-data-list>
                </div>
                <div class="btnWrap p-LR-16">
                    <div class="weight-500 btn-plain color-E64F22 font-15 border-rad-8" @click="couponAusageRecord">电子券使用记录</div>
                </div>
            </div>
            <custom-popup ref="outletsPopup" :maskClick="true" @maskClick="maskClose" type="bottom">
                <outletsList ref="outlesList"></outletsList>
            </custom-popup>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
// 一次请求多少条
import cnpcBridge from '../../../../s-kit/js/v3-native-jsapi/cnpcBridge';

const PAGE_SIZE = 20;
import { mapGetters, mapState } from 'vuex';
import { couponList, couponAmount } from '../../../../s-kit/js/v3-http/https3/conpon/index';
import { clientCode } from '../../../../../project.config';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
import ZjPopup from '../../../../s-kit/components/layout/uni-popup/uni-popup.vue';
import outletsList from '../../components/outlets-list/outlets-list.vue';
export default {
    mixins: [publicMixinsApi],
    data() {
        return {
            // 一级筛选条件
            tabs: [
                { id: 20, title: '全部', quantity: '' },
                { id: 40, title: '油品券', quantity: '' },
                { id: 70, title: '非油券', quantity: '' },
            ],
            // 油品、非油、全部、默认选中的值
            selectId: '20',
            // 二级筛选条件
            typeSortArray: [
                { content: '现金券', id: '1' },
                { content: '优惠券', id: '2' },
                { content: '快过期', id: '3' },
                { content: '金额从大到小', id: '4' },
            ],
            // 全排序类型默认值
            sortId: '0',
            // 电子券数组
            couponArray: [],
            // 页码
            page: 1,
            // 油品非油品
            categoryType: '',
            // 现金券优惠券
            kind: '',
            // 排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
            sortType: 'distTime',
            // 排序方式：1-升序2-降序3-默认排序
            orderWay: '3',
            // 暂无电子券图片
            noPicture: require('../../images/kt1yhq.png'),
            // 是否展示空态标识
            showEmpty: false,
            // 总条数
            totalPage: 0,
            // 油品券使用的数量,
            oilCouponLimit: '',
            // 非油券使用的数量
            noilCouponLimit: '',
            refer: '',
            bizContent: '',
            activeIndexes: [], // 当前展开的折叠面板的索引，初始为 null 表示全部折叠
            activeOutletsIndexes: [], // 
            outletsIndex: 0, // 点击当前附近网点的索引
            masShow: false, // 是否加载附近网点列表
        };
    },
    computed: {
        ...mapState({
            // #ifdef MP-WEIXIN
            phone: state => state.phone,
            zfPhone: state => state.location.zfPhone,
            officialAccountParams: state => state.location.officialAccountParams,
            // #endif
            cityCode: state => state.locationV3_app.cityCode,
        }),

        //  'linear-gradient(90deg, #ffe2c3 0%, #fff2e3 100%)' : 'linear-gradient( 90deg, #DDDDDD 0%, #F1F1F1 100%)'
    },
    async onLoad(query) {
        if (Object.keys(query).length) {
            let params = JSON.parse(decodeURIComponent(query.data));
            if (params.refer) {
                this.refer = params.refer;
            }
            if (params.type) {
                this.selectId = params.type == '' ? '20' : params.type == 1 ? '40' : params.type == 2 ? '70' : 20;
                this.categoryType = params.type;
            }
            if (params.myRefer) {
                this.$sKit.mpBP.tracker('我的页面', {
                    seed: 'minePageBiz',
                    pageID: 'couponPage',
                    refer: params.myRefer,
                    channelID: clientCode,
                });
            }
        }
        // #ifdef MP-ALIPAY
        my.hideBackHome();
        // #endif
        // 获取电子券数量
        this.getCouponQuantity();
        // #ifdef MP-WEIXIN
        console.log(this.zfPhone, 'this.zfPhone====支付活动跳转获取的手机号');
        if (this.officialAccountParams == 'dzq' && this.zfPhone) {
            this.comparePhoneNumbers(this.phone, this.zfPhone);
        }
        // #endif
        // 获取电子券列表
        await this.getCouponList();
        this.couponBiz();
    },
    async mounted() {
        // 获取电子券列表
        await this.getCouponList();
        this.couponBiz();
    },
    methods: {
        // 点击打开附近可用网点列表
        outletsOpenHandle(item, index) {
            this.$refs.outletsPopup.open();            
            this.masShow = true
            this.outletsIndex = index;            
            this.activeOutletsIndexes.push(index);
            this.$nextTick(() => {
                this.$refs.outlesList.selectAction([], true, item.couponTemplateNo);
            })            
        },
        maskClose() {
            this.activeOutletsIndexes = this.activeOutletsIndexes.filter(i => i !== this.outletsIndex);
            this.masShow = false
        },
        background(bgColor) {
            return bgColor ? 'linear-gradient(90deg, #ffe2c3 0%, #fff2e3 100%)' : 'linear-gradient( 90deg, #DDDDDD 0%, #F1F1F1 100%)';
        },
        toggleCollapse(index) {
            if (this.activeIndexes.includes(index)) {
                this.activeIndexes = this.activeIndexes.filter(i => i !== index);
            } else {
                // 如果当前面板未展开，则添加到展开的面板列表中
                this.activeIndexes.push(index);
            }
        },
        /**
         * @description  : 获取电子券数量
         * @return        {*}
         */
        getCouponQuantity() {
            couponAmount().then(res => {
                if (res.success) {
                    // 定义一个对象，将需要赋值的数据放入其中
                    let quantityData = {
                        20: res.data.unUsedAmount,
                        40: res.data.unUsedOilAmount,
                        70: res.data.unUsedOtherAmount,
                        // 其他需要赋值的数据
                    };
                    // 使用循环遍历对象，将值赋给对应的属性
                    for (let key in quantityData) {
                        // 这里使用parseInt是为了解决for..in..循环中数字类型的key变为字符串的问题，这里也可以不使用严格判断来解决此处的问题
                        let tab = this.tabs.find(tab => tab.id === parseInt(key));
                        if (tab !== undefined) {
                            tab.quantity = quantityData[key];
                        }
                    }
                }
            });
        },
        /**
         * @description  : 选择一级油品非油的类型
         * @return        {*}
         */
        selectClick(tab, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.selectId === tab.id) return;
            // 将选中的id赋值在页面上高亮展示
            this.selectId = tab.id;
            // 券品类：1-油品券2-非油券（不传参默认查询全部券）
            const categoryTypes = {
                0: '',
                1: '1',
                2: '2',
            };
            // 使用index下标取Objct中某一项
            this.categoryType = categoryTypes[index] || '';
            console.log(this.categoryType, 'this.categoryType');
            // 将券类型置空：0—优惠券；1—现金券；（不传参默认查询全部券）
            this.kind = '';
            // 将排序字段按照快过期优先展示
            this.sortType = 'distTime';
            // 将排序方式设置为默认：
            this.orderWay = 3;
            // 将二级高亮选中取消
            this.sortId = '';
            // 获取电子券列表
            this.getCouponList({ isInit: true });
            this.couponBiz();
        },
        couponBiz() {
            if (this.selectId == 20) {
                let bizParams = {
                    seed: 'couponBiz',
                    pageID: 'couponListPage',
                    refer: this.refer,
                    channelID: clientCode,
                };
                if (this.bizContent) {
                    bizParams.content = this.bizContent;
                    bizParams.dataType = 'exposure';
                }
                this.$sKit.mpBP.tracker('优惠券', bizParams);
            }
            if (this.selectId == 40) {
                this.$sKit.mpBP.tracker('优惠券', {
                    seed: 'couponBiz',
                    pageID: 'oilCouponPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
            if (this.selectId == 70) {
                this.$sKit.mpBP.tracker('优惠券', {
                    seed: 'couponBiz',
                    pageID: 'nonOil_couponPage',
                    refer: this.refer,
                    channelID: clientCode,
                });
            }
        },
        /**
         * @description  : 选择二级筛选条件
         * @return        {*}
         */
        selectSortClick(item, index) {
            // 如果点击的是相同的筛选类型阻止向下调用接口
            if (this.sortId == item.id) {
                // 值相同取消高亮
                this.sortId = 0;
                // 如果1级筛选存在值就赋值不存在就置为空
                this.categoryType = this.categoryType == '' ? '' : this.categoryType.toString();
                // 将券类型置空：0—优惠券；1—现金券；（不传参默认查询全部券）
                this.kind = '';
                // 将排序字段按照快过期优先展示
                this.sortType = 'distTime';
                // 将排序方式设置为默认：
                this.orderWay = 3;
            } else {
                console.log('11111');
                // 如果点击的是非相同的筛选类型赋值页面变为高亮
                this.sortId = item.id;
                // 定义的二级筛选类型数组
                const sortTypes = [
                    { kind: 1, sortType: 'distTime', orderWay: 3 },
                    { kind: 0, sortType: 'distTime', orderWay: 3 },
                    { kind: '', sortType: 'endDate', orderWay: 1 },
                    { kind: '', sortType: 'faceValue', orderWay: 2 },
                ];
                // 通过index在数组中选择相应的筛选条件
                const sortType = sortTypes[index];
                // 如果存在值就赋值，调用接口进行条件筛选
                if (sortType) {
                    this.kind = sortType.kind;
                    this.sortType = sortType.sortType;
                    this.orderWay = sortType.orderWay;
                }
            }
            this.$sKit.mpBP.tracker('优惠券', {
                seed: 'couponBiz',
                pageID: 'coupon_screenBut',
                refer: this.refer,
                channelID: clientCode,
            });
            this.getCouponList({ isInit: true });
        },
        /**
         * @description  : 电子券类型
         * @return        {*}
         */
        getCouponType(val) {
            console.log(val, '电子券类型');
            return val == 10 ? '满减券' : val == 20 ? '记次券' : val == 30 ? '兑换券' : val == 40 ? '折扣券' : '';
        },
        /**
         * @description  : 区分券类型
         * @return        {*}
         */
        differentiationType(val) {
            console.log(val, '油品非油品------');
            // 券可用品类 1 油品 2 非油品
            return val == 1 ? '现金券' : '优惠券';
        },
        /**
         * @description  : 电子券未使用详情
         * @param        {Object} item -选中的券信息
         * @return        {*}
         */
        unusedDetails(item, index) {
            if (item.kind === 0) return;
            if (item.bgColor) {
                let url = `/packages/third-coupon-module/pages/conopn-unuse-detail/main`;
                let params = {
                    ...item,
                    refer: this.refer,
                };
                let type = 'navigateTo';
                this.$sKit.layer.useRouter(url, params, type);
            }
        },
        /**
         * @description  : 上拉加载
         * @return        {*}
         */
        scrolltolower() {
            console.log('上拉触底事件');
            if (this.$refs.dataList.loadStatus == 'contentdown') {
                this.$refs.dataList.loadStatus = 'loading';
                this.getCouponList();
            }
        },
        /**
         * @description  : 下拉刷新
         * @return        {*}
         */
        refreshPullDown(e) {
            // 重置数据获取电子券列表
            this.getCouponList({ isInit: true });
            // 获取电子券数量
            this.getCouponQuantity();
        },
        /**
         * @description  : 获取电子券列表
         * @param         {*} isInit 等于true的时候重置数据
         * @param         {*} couponArray:电子券数组
         * @param         {*} page:页码
         * @param         {*} totalPage:返回数据的条数
         * @param         {*} pageSize:每页10条
         * @param         {*} couponStatus:查询状态：20-未使用，40-已使用，70-已过期
         * @param         {*} categoryType:券品类：1-油品券2-非油券（不传参默认查询全部券）
         * @param         {*} kind:券类型：0—优惠券；1—现金券；（不传参默认查询全部券）
         * @param         {*} sort:排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
         * @param         {*} order:排序方式：1-升序2-降序3-默认排序
         * @return        {*}
         */
        async getCouponList({ isInit = false } = {}) {
            return new Promise(async (resolve, reject) => {
                if (isInit) {
                    Object.assign(this, {
                        couponArray: [],
                        page: 1,
                    });
                    // 重置入参页码
                }
                let { page, couponArray, totalPage } = this;
                let proCode = this.cityCode
                    ? this.cityCode.toString().substring(0, this.cityCode.toString().length - 4) + '0000'
                    : '110000';
                let params = {
                    pageNum: this.page,
                    pageSize: PAGE_SIZE,
                    couponStatus: '20', // 券类型：优惠券类型必须为10满减券  20记次券 30兑换券 40折扣券"
                    categoryType: this.categoryType, //券品类 券品类：1-油品券2-非油券（不传参默认查询全部券）
                    kind: this.kind, //券类型：0—优惠券；1—现金券；（不传参默认查询全部券）
                    sort: this.sortType, //排序字段：faceValue（券金额），endDate（券有效期，筛选条件中“快过期”传值），checkTime（电子券核销时间，筛选条件中“最近使用”传值），distTime（电子券派发时间，查询全部未使用电子券时默认按照派发时间降序排列）
                    order: this.orderWay, //排序方式：1-升序2-降序3-默认排序
                    areaCode: proCode,
                };
                console.log(params, 'params=====电子券列表');
                let res = await couponList(params);
                if (res.success) {
                    let list = res.data.rows;
                    const currentTimestamp = new Date().getTime();
                    // 遍历电子券数组 将返回的时间转成时间戳，与当前时间对比，给券标记未到期标识
                    list = list.map(item => {
                        // item.templatePicOss = null;
                        var handelTime = new Date(item.startTime.replace(/-/g, '/'));
                        if (currentTimestamp > Date.parse(handelTime)) {
                            // 到使用日期
                            item.bgColor = true;
                        } else {
                            // 未到使用日期
                            item.bgColor = false;
                        }
                        return item;
                    });
                    this.oilCouponLimit = res.data.oilCouponLimit;
                    this.noilCouponLimit = res.data.noilCouponLimit;
                    // 将处理好的数组合并到定义的数组，放到页面渲染
                    couponArray = couponArray.concat(list);
                    // 将处理好的数据放置this中
                    Object.assign(this, {
                        couponArray,
                        page: Number(page) + 1,
                    });
                    this.bizContent = this.couponArray
                        .map(item => {
                            // 10一满减券;20一记次券;30-兑换券;40-折扣券
                            let text = '';
                            if (item.couponType == 10) {
                                text = '满减券';
                            } else if (item.couponType == 20) {
                                text = '记次券';
                            } else if (item.couponType == 30) {
                                text = '兑换券';
                            } else if (item.couponType == 40) {
                                text = '折扣券';
                            }
                            return text;
                        })
                        .join('-');
                    totalPage = res.data.pageSum;
                }
                this.$nextTick(() => {
                    setTimeout(() => {
                        console.log('page', page);
                        // 返回总条数
                        if (res.data && page >= totalPage) {
                            // 没有更多了
                            this.$refs.dataList.loadStatus = 'nomore';
                        } else {
                            // 上拉加载更多
                            this.$refs.dataList.loadStatus = 'contentdown';
                        }
                        this.showEmpty = couponArray.length <= 0 ? true : false;
                        this.$refs.dataList.stopRefresh();
                    }, 100);
                });
                resolve();
            });
        },
        buttonText(id, item) {
            if (item.bgColor) {
                if (item.kind === 1) {
                    return '立即使用';
                }
            } else {
                return '未生效';
            }
        },
        /**
         * @description  : 处理折和元
         * @return        {*}
         */
        faceValueFilter(item) {
            if (item.couponType && item.couponType == '40') {
                return item.discountValue + '<span style="font-size: 12px;">折</span>';
            } else {
                return (
                    '<span class="font-style" style="font-size: 12px">&yen;</span>' + `<span style="font-size: 28px;">${item.amount}</span>`
                );
            }
        },
        /**
         * @description  : 满减券和折扣券说明
         * @return        {*}
         */
        thresholdAmount(item) {
            if (item.thresholdAmount) {
                if (item.couponType && item.couponType == '40') {
                    return '满' + item.thresholdAmount + '元可用';
                } else {
                    return '满' + item.thresholdAmount + '减' + item.amount;
                }
            } else {
                return '无金额门槛';
            }
        },
        /**
         * @description  : 处理时间(10.08与产品确定列表只展示年月日，详情展示时分秒)
         * @return        {*}
         */
        screen(item) {
            let text = '';
            if (item.bgColor) {
                text = '有效期至：' + item.endTime.slice(0, 10);
            } else {
                text = '生效日期：' + item.startTime.slice(0, 10);
            }

            return text;
        },
        /**
         * @description  : 电子券使用记录
         * @return        {*}
         */
        couponAusageRecord() {
            let url = `/packages/third-coupon-module/pages/coupon-used-record/main`;
            let params = { refer: this.refer };
            let type = 'navigateTo';
            this.$sKit.layer.useRouter(url, params, type);
        },

        // #ifdef MP-WEIXIN
        //  // 支付活动跳转进当前页面手机号不一致提示
        comparePhoneNumbers(phone1, phone2) {
            // 提取手机号的前三位和后四位
            const phone1Prefix = phone1.substring(0, 3);
            const phone1Suffix = phone1.substring(phone1.length - 4);
            const phone2Prefix = phone2.substring(0, 3);
            const phone2Suffix = phone2.substring(phone2.length - 4);

            // 比较前三位和后四位是否相等
            if (phone1Prefix === phone2Prefix && phone1Suffix === phone2Suffix) {
                return;
            } else {
                this.$store.dispatch('zjShowModal', {
                    title: `当前登录手机号${phone1.substring(0, 3) + '****' + phone1.substring(7)}与活动${
                        this.zfPhone
                    }不一致,请您确认是否需要重新登录`,
                    confirmText: '我知道了',
                    cancelText: '',
                    confirmColor: '#333',
                    maskClosable: true, // 不允许点击遮罩层关闭弹窗
                    success: async res => {
                        if (res.confirm) {
                            this.$store.commit('setZfPhone', '');
                            this.$store.dispatch('setOfficialAccountParams', '');
                        } else if (res.cancel) {
                        }
                    },
                });
            }
        },
        // #endif
    },
    components: {
        ZjPopup,
        outletsList
    },
};
</script>

<style scoped lang="scss">
.view {
    .tabs_style {
        width: 100%;
        height: 44px;
        line-height: 20px;
        .tab_style {
            margin-right: 22px;
            line-height: 20px;
        }
        .selected {
            color: #e64f22;
        }
    }
    .type-sort {
        width: 100%;
        height: 44px;
        .sort-item {
            height: 30px;
            line-height: 30px;
            margin-right: 12px;
            padding: 0 6.5px;
        }
        .selectSorted {
            color: #e64f22;
        }
    }
    .reminder {
        width: 100%;
        height: 40px;
        word-wrap: break-word;
        word-break: normal;
    }
    .coupon-wrap {
        .bg-coupon {
            width: 100%;
            position: relative;
            background-repeat: no-repeat;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #ffeedd 0%, rgba(255, 244, 233, 0.4) 100%);

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 50px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0px 8px 0px;
                padding: 1px 8px;
                text-align: center;
                font-size: 10px;
                font-weight: bold;
                color: white;
            }

            .content-wrap {
                width: 100%;
                padding: 20rpx;
                position: relative;

                .content-wrap-header {
                    margin-bottom: 10px;

                    .content-left {
                        .content-left-price {
                            min-width: 100px;
                        }
                        .img-size {
                            width: 65px;
                            height: 65px;
                            line-height: 65px;
                            margin-top: 23rpx;
                        }
                    }

                    .content-sx {
                        height: 136rpx;
                        width: 1px;
                        opacity: 0.5;
                        border: 1px solid;
                        border-image: linear-gradient(180deg, rgba(235, 123, 69, 0), rgba(235, 123, 69, 1), rgba(235, 123, 69, 0)) 1 1;
                        margin: 15px 16rpx 0 23rpx;
                    }

                    .content-right {
                        .coupon-title {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            line-height: 1.2;
                            max-height: 2.4em;
                        }

                        .title-wrap {
                            &::after {
                                content: '';
                                display: block;
                                width: 73rpx;
                                height: 39rpx;
                                visibility: hidden;
                            }
                        }

                        .right-img {
                            position: absolute;
                            right: 0;
                            width: 73rpx;
                            height: 39rpx;
                        }

                        .certificate-time {
                            margin-top: 32rpx;

                            .useBtn {
                                width: 77.5px;
                                height: 27px;
                                line-height: 27px;
                            }

                            .time {
                                margin-top: 5px;
                            }
                        }
                    }
                }

                .content-wrap-footer {
                    border-radius: 4px;
                    width: 100%;
                    background-image: linear-gradient(90deg, #ffe2c3 0%, #fff2e3 100%);
                    transform-origin: top;
                    transition: transform 0.2s ease-out;

                    .custom-collapse {
                        overflow: hidden;

                        .collapse-header {
                            height: 54rpx;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding: 16rpx 24rpx;
                            border-radius: 4px;
                            cursor: pointer;
                            transition: background 0.3s;
                            user-select: none;
                            -webkit-tap-highlight-color: transparent;
                            .describe-header {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                width: 100%;
                            }
                            .nearby-branches-header {
                                width: 180px;
                                text-align: right;
                                color: #A6763E;
                            }
                            .arrow {
                                width: 17rpx;
                                height: 10rpx;
                                transition: transform 0.25s ease;
                                transform: rotate(0deg);
                                margin-left: 10px;

                                &.rotate {
                                    transform: rotate(-180deg);
                                }
                            }
                        }

                        .collapse-content {
                            max-height: 0;
                            overflow: hidden;
                            transition: max-height 0.3s ease-out;

                            &.expanded {
                                max-height: 100%;
                            }

                            .content {
                                padding: 12px;
                                opacity: 0;
                                transform: translateY(-10px);
                                transition: all 0.2s ease-out;
                            }

                            &.expanded .content {
                                opacity: 1;
                                transform: translateY(0);
                            }
                        }
                    }
                }
            }
            .type-cou {
                width: 36px;
                padding: 2px 0;
            }
        }

        .toTakeEffect {
            width: 100%;
            position: relative;
            background-repeat: no-repeat;
            margin-bottom: 12px;
            background-image: linear-gradient(90deg, #e8e8e8 0%, #f8f8f8 100%);
            opacity: 0.6;

            &::before {
                content: '';
                width: 100%;
                height: 100%;
                position: absolute;
                background-image: radial-gradient(circle at 95px top, #fff, #fff 5px, transparent 6px),
                    radial-gradient(circle at 95px bottom, #fff, #fff 5px, transparent 6px);
            }

            .upperLeft {
                position: absolute;
                top: 0;
                left: 0;
                width: 45.5px;
                height: 16px;
                line-height: 16px;
                border-radius: 8px 0 0 0;
            }

            .content-left {
                .content-left-price {
                    min-width: 100px;
                }
                img {
                    width: 65px;
                    height: 65px;
                    line-height: 65px;
                    margin-top: 23rpx;
                }
            }

            .content-sx {
                height: 136rpx;
                opacity: 0.3;
                border: 1px solid;
                border-image: linear-gradient(180deg, rgba(102, 102, 102, 0), rgba(102, 102, 102, 1), rgba(102, 102, 102, 0)) 1 1;
                margin: 15px 8px 0 0;
            }

            .content-wrap {
                height: 100%;

                .left-wrap {
                    width: 30%;
                }

                .right-wrap {
                    width: 70%;
                    height: 100%;

                    .title {
                        margin: 12px 22px 0 0;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }

                    .title2 {
                        margin-bottom: 12px;
                    }

                    .useBtn {
                        width: 77.5px;
                        height: 27px;
                        line-height: 27px;
                        margin-right: 12px;
                    }

                    .time {
                        margin-top: 5px;
                    }

                    .type-cou {
                        width: 36px;
                        padding: 2px 0;
                    }
                }
            }
        }
    }

    .btnWrap {
        width: 100%;
        height: 44px;
        line-height: 44px;
        margin: 0 auto 10px;
    }
}
</style>
