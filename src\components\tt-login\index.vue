<template>
  <div>
    <button
      v-if="layer"
      open-type="getUserInfo"
      bindgetuserinfo="getUserInfo"
      class="layer-btn"
    >
    </button>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  props: {
        // 作为button组件层包裹使用形式
        layer: {
            type: Boolean,
            default: false,
        },
        // 静默一键登录
        silence: {
            type: Boolean,
            default: false,
        }
    },
  created() {

  },
  mounted() {

  },
  methods: {
    getUserInfo(res) {
      const { userInfo, errMsg, rawData } = res.detail;
      console.log(res)
  }
  },
  computed: {

  },
  components: {

  },
}
</script>
<style scoped lang='scss'>
.layer-btn {
    background: none;
    border: none;
    display: initial;

    &:active {
        background: none;
        border: none;
        opacity: 1;
    }
}
</style>