import Vue from 'vue';

(function () {
    const isObject = function (obj) {
        if (obj === undefined || obj === null) {
            return false;
        } else {
            return toString.call(obj) == '[object Object]';
        }
    };
    // 劫持Component
    const _componentProto_ = Component;
    Component = function (options) {
        //options.methods内有uniapp注入的事件处理器__e及mpHook
        if (options.methods) {
            Object.keys(options.methods).forEach(methodName => {
                if (methodName == '__e') {
                    //劫持事件处理器
                    eventProxy(options.methods, methodName);
                }
            });
        }

        _componentProto_.apply(this, arguments);
    };

    function eventProxy(methodList, methodName) {
        const _funcProto_ = methodList[methodName];
        methodList[methodName] = function () {
            _funcProto_.apply(this, arguments);
            let prop = {};
            if (isObject(arguments[0])) {
                if (Object.keys(arguments[0]).length > 0) {
                    //记录触发页面信息
                    const pages = getCurrentPages();
                    const currentPage = pages[pages.length - 1];
                    prop['$page_path'] = currentPage.route; //页面路径
                    prop['$page_query'] = currentPage.options || {}; //页面携带的query参数
                    const type = arguments[0]['type'];
                    const current_target = arguments[0].currentTarget || {};
                    const dataset = current_target.dataset || {};
                    prop['$event_type'] = type;
                    prop['$event_timestamp'] = Date.now();
                    prop['$element_id'] = current_target.id;
                    const eventDetail = arguments[0].detail;
                    prop['$event_detail'] = eventDetail;
                    if (!!dataset.eventOpts && type) {
                        // 点击事件
                        if (type == 'tap') {
                            const event_opts = dataset.eventOpts;
                            if (Array.isArray(event_opts) && event_opts[0].length === 2) {
                                let eventFunc = [];
                                event_opts[0][1].forEach(event => {
                                    eventFunc.push({
                                        name: event[0],
                                        params: event[1] || '',
                                    });
                                });
                                prop['$event_function'] = eventFunc;
                            }
                            let params = prop.$event_function[0].params;

                            let lParams = params[params.length - 1];
                            if (typeof lParams == 'string') {
                                let lArrP = lParams.split('@');
                                if (lArrP[0] == 'CLICK') {
                                    let md_key = lArrP[1];
                                    console.log('用户点击', md_key);
                                    postWeData(prop); //在此处上传记录的事件数据
                                }
                            }
                        }
                    }
                }
            }
        };
    }

    const postWeData = function (data) {
        //埋点上传器
        // console.log(Vue.prototype.$Maa)
        // console.log(data)
        // Vue.prototype.$Maa.tracker({
        //   eventName: 'region_selection', // 事件名
        //   eventData: {
        //     "seed":"region_selection", // 事件ID
        //     "referpageName_var" : "首页", // 页面访问时上一个页面名称。
        //     "pageName_var" : "我的", // 页面访问时当前页面名称。
        //     "iop_tacticsId_var" : "xxxx", // 页面访问时当前页面对应的IOP策略ID号。（物品模型）
        //     "referFlowId_var" : "xxxx", // 页面访问时来源入口位置ID。（物品模型<url参数解析>）
        //     }, // 事件数据
        // });
    };
})();
