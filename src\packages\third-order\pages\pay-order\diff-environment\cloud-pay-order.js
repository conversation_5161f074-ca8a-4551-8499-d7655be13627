// #ifdef H5-CLOUD
import { mapGetters, mapState } from 'vuex';
import * as paysdk from "@petro-gsms/paysdk-js";
console.log('Plugin---', paysdk)
export default {
  methods: {
    /**
       * 发起充值支付
       * @param areaCode 地区编码
       * @param bizOrderNo 业务订单编号
       * @param rcvAmt 应收总金额
       * @param realAmt 支付金额
       */
    async callPlugIn(paramsData) {
      console.log('11111111111111', paysdk)
      let params = {
        areaCode: paramsData.areaCode,
        bizOrderNo: paramsData.bizOrderNo,
        rcvAmt: Number(paramsData.rcvAmt),
        realAmt: Number(paramsData.realAmt),
        // 3.0.4风控字段
        extendFiled: JSON.stringify({
          dfp: '',
          gps:
            this.riskManagementLonV3 && this.riskManagementLatV3
              ? `(${this.riskManagementLonV3},${this.riskManagementLatV3})`
              : '',
          gpsProvince: '',
          gpsCity: '',
          gpsArea: '',
        }),
        payType: paramsData.payType

      };
      console.log('发起充值支付---------', params)
      const res = await paysdk.RechargePay(params);
      console.log('发起充值支付成功结果', res)
      uni.hideLoading();
      this.payingFlag = false;
      if (res.code === 'PAY_SUCCESS') {
        this.otherAmount = '';
        this.$sKit.layer.useRouter(
          '/packages/third-remaining-sum/pages/third-charge-result/main',
          { orderId: paramsData.bizOrderNo, payType: 'orderPay', refer: 'r26', addressName: this.walletInfo.addressName },
          'redirectTo',
      );
      } else {
        this.isCanPay = true;
        // 截取字符串后面的数据
        let errIndex = res.msg.indexOf(':');
        let errorCode = '';
        let customErr = '';
        if (errIndex !== -1) {
          errorCode = res.msg.slice(0, errIndex);
          customErr = res.msg.slice(errIndex + 1, res.msg.length);
        } else {
          customErr = res.msg;
        }
        this.$store.dispatch('zjShowModal', {
          title: customErr,
          content: `${errorCode}`,
          confirmText: '确定',
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定');
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          },
        });
      }
    },

  },
  computed: {

    ...mapState({
      riskManagementLatV3: state => state.locationV3_app.riskManagementLatV3, //风控纬度
      riskManagementLonV3: state => state.locationV3_app.riskManagementLonV3, //风控经度
    }),
  },
}
// #endif
