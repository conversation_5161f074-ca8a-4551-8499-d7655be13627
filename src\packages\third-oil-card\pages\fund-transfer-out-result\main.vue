<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="view p-bf bg-F7F7FB fl-column">
            <zj-navbar :height="44" title="资金转出"></zj-navbar>
            <div class="f-1" v-if="success">
                <div class="success-wrap fl-row fl-jus-cen">
                    <img src="../../images/oil-success.png" alt />
                </div>
                <div class="text font-16 fl-row fl-jus-cen weight-500 color-333">资金转出成功！</div>
                <div class="btn_div fl-row fl-jus-bet p-LR-16">
                    <div class="btn-item btn-plain color-E64F22 font-16 border-rad-8" @click="queryElectronicAccount()"> 查询电子账户</div>
                    <div class="btn-item primary-btn color-fff font-16 border-rad-8" @click="physicalCardDetails()">返回实体卡详情页 </div>
                </div>
            </div>
            <div class="f-1" v-else-if="!success">
                <div class="success-wrap fl-row fl-jus-cen">
                    <img src="../../images/oil-fail.png" alt />
                </div>
                <div class="text font-16 fl-row fl-jus-cen weight-500 color-333">转出失败原因！</div>
                <div class="fail-text fl-row te-center">
                    <span class="weight-400 color-999 font-12">
                        该卡的备用金转入电子账户后，电子账户的余额将超出
                        <span class="color-000">&yen;{{ 5000 }}</span>
                        的最大限制
                    </span>
                </div>
                <div class="btn_div fl-row fl-jus-bet p-LR-16">
                    <div class="btn-item btn-plain color-E64F22 font-16 border-rad-8" @click="returnToHomePage()">返回首页</div>
                    <div class="btn-item primary-btn color-fff font-16 border-rad-8" @click="physicalCardDetails()">返回实体卡详情页 </div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>
<script>
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'fund-transfer-out-result',
    components: {},
    props: {
        // success: Boolean
    },
    data() {
        return {
            success: true,
        };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
        /**
         * @description  : 返回实体卡详情页
         * @return        {*}
         */
        physicalCardDetails() {
            uni.navigateBack({ detail: 1 });
            // 存储本地的用来刷新卡信息的标识
            uni.setStorageSync('refreshCardManagement', true);
        },
        /**
         * @description  : 查询电子账户
         * @return        {*}
         */
        queryElectronicAccount() {
            // packages/third-remaining-sum/pages/home/<USER>
            let url = '/packages/third-remaining-sum/pages/home/<USER>';
            let params = {};
            let type = 'redirectTo';

            this.$sKit.layer.useRouter(url, params, type);
        },
        /**
         * @description  : 返回首页
         * @return        {*}
         */
        returnToHomePage() {
            let url = '/pages/thirdhome/main';
            let params = {};
            let type = 'redirectTo';

            this.$sKit.layer.useRouter(url, params, type);
        },
    },
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
@import '../../../../s-kit/css/index.scss';
.view {
    .success-wrap {
        width: 100%;
        margin-top: 40px;

        img {
            width: 64px;
            height: 64px;
        }
    }

    .text {
        margin-top: 16px;
    }

    .btn_div {
        width: 100%;
        height: 44px;
        margin-top: 40px;
        box-sizing: border-box;
        .btn-item {
            width: 48%;
            height: 100%;
            line-height: 44px;
        }

        &:last-child {
        }
    }

    .fail-text {
        margin-top: 16px;
        margin-left: 66px;
        margin-right: 66px;
    }
}
</style>
