<template>
    <div
        class="mask"
        @touchmove.stop.prevent
        @click.stop="
            e => {
                e.stopPropagation();
                return;
            }
        "
    >
    </div>
</template>
<script>
export default {
    name: '',
    components: {},
    props: {},
    data() {
        return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    filter: {},
    watch: {},
};
</script>
<style scoped lang="scss">
.mask {
    width: 100%;
    height: 100vh;
    position: fixed;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
</style>
