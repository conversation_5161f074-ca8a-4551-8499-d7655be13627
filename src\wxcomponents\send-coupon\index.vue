<template>
<uni-shadow-root class="send-coupon-index"><view>
    <send-coupon @sendcoupon="sendcoupon" @userconfirm="userconfirm" :send_coupon_params="send_coupon_params" :sign="sign" :send_coupon_merchant="send_coupon_merchant" :suggest_immediate_use="suggest_immediate_use">
        <slot></slot>
    </send-coupon>
</view></uni-shadow-root>
</template>

<script>
import SendCoupon from 'plugin://sendCoupon/send-coupon.vue'
global['__wxVueOptions'] = {components:{'send-coupon': SendCoupon}}

global['__wxRoute'] = 'send-coupon/index'
Component({
    properties: {
        // 这里定义了innerText属性，属性值可以在组件使用时指定
        send_coupon_params: {
            type: Array,
            default: [],
        },
        sign: {
            type: String,
            default: '',
        },
        send_coupon_merchant: {
            type: String,
            default: '',
        },
        suggest_immediate_use: {
            type: Boolean,
            default: false,
        },
    },
    data: {},
    methods: {
        // 这里是一个自定义方法
        sendcoupon: function (res) {
            this.triggerEvent('sendcoupon', res.detail);
        },
        userconfirm: function (res) {
            this.triggerEvent('userconfirm', res.detail);
        },
    },
});
export default global['__wxComponents']['send-coupon/index']
</script>
<style platform="mp-weixin">

</style>