<template>
    <div>
        <div class="list-card" v-for="(item, index) in orderList" :key="index">
            <div class="oreder-content" @click="toDetail(item)">
                <div class="detail-top">
                    <div class="name fl-row">
                        <img mode="scaleToFill" src="../image/chargeCheer.png" alt class="name-img" />
                        <div class="title">{{ item.stationName }}</div>
                        <div class="name-arrow-right"></div>
                    </div>
                    <div :class="{ invoice: true, gray: true }">{{ getOrderStatus(item) }}</div>
                </div>
                <!-- <div class="detail-contetn" v-for="(goodItem, idx) in item.chargeOrderItemList" :key="idx"> </div> -->
                <div class="relevantInfo">
                    <div class="infoItem">
                        <div class="itemLeft">充电电量</div>
                        <div class="itemRight">{{ item.chargeQty ? item.chargeQty + '度' : '-' }}</div>
                    </div>
                    <div class="infoItem">
                        <div class="itemLeft">充电时长</div>
                        <div class="itemRight">{{ item.chargeTime && Number(item.chargeTime > 0) ? item.chargeTime + '分钟' : '-' }}</div>
                    </div>
                    <div class="infoItem">
                        <div class="itemLeft">充电开始时间</div>
                        <div class="itemRight">{{ item.chargeStartTime ? item.chargeStartTime : item.createTime }}</div>
                    </div>
                </div>
                <div class="detail-bottom">
                    <!-- <div class="countdown" v-if="item.timer && item.orderStatus == 1">
                        <div>{{ item.time }}</div>
                        <div>后自动取消</div>
                    </div>
                    <div v-if="item.orderStatus == 1" class="bottom-button-pay" @click.stop="payment(item)">立即支付</div> -->
                    <!-- #ifndef H5-CLOUD -->
                    <!-- <div v-if="evaluateButFlag(item)" class="bottom-button-open" @click.stop="toEvaluate(item)">去评价</div> -->
                    <!-- #endif -->
                    <div v-if="invoiceButFlag(item)" class="bottom-button-open" @click.stop="invoice(item)">开具发票</div>
                    <div v-if="item.invoiceFlag == 2" class="bottom-button-see" @click.stop="seeInvoice(item)">查看发票</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { chargeOrderList, unPaidOrderApi, getInvoiceByOrderNoApi } from '../../../js/v3-http/https3/order/index';
import { chargeBeforeMakeInvoiceCheckApi } from '../../../js/v3-http/https3/invoice/index';
import { mapState } from 'vuex';
import { clientCode } from '../../../../../project.config';
export default {
    name: 'orderList',
    props: {},
    data() {
        return {
            // 列表页数
            pageNum: 1,
            // 列表页码
            pageSize: 10,
            // 订单列表
            orderList: [],
            // 接口入参
            listParams: {},
        };
    },
    mounted() {
        // 为付款逻辑添加节流防抖
        this.payment = this.$sKit.commonUtil.throttleUtil(this.payment);
        this.toEvaluate = this.$sKit.commonUtil.throttleUtil(this.toEvaluate);
        this.invoice = this.$sKit.commonUtil.throttleUtil(this.invoice);
    },
    beforeDestroy() {
        // 销毁页面前清除订单数据中的定时器
        this.orderList.forEach(item => {
            if (item?.timer) {
                clearInterval(item.timer);
            }
        });
    },
    computed: {
        ...mapState({
            isHarmony: state => state.thirdIndex.isHarmony,
        }),
    },
    methods: {
        // 判断是否可评价
        evaluateButFlag(item) {
            let flag = false;
            if (item.orderStatus == 4 && item.commentFlag == 1) {
                flag = true;
            }
            return flag && item.orderNo.length >= 20;
        },
        // 判断是否展示开票
        invoiceButFlag(item) {
            if (item.invoiceFlag == 0 && item.orderStatus == 4) {
                return true;
            } else {
                return false;
            }
        },
        /**
         * @description  : 匹配回显订单类型
         * @param         {*} flag:
         * @return        {*}
         */
        getOrderType(flag) {
            if (flag == 11) {
                return 'e享加油';
            } else if (flag == 12) {
                return '加油卡预授权加油';
            } else if (flag == 13) {
                return '后支付加油';
            } else if (flag == 23) {
                return 'e享购';
            } else if (flag == 22) {
                return '积分换购';
            } else if (flag == 14) {
                return '室内付款';
            } else if (flag == 15) {
                return '异常订单补录';
            } else if (flag == 20) {
                return '洗车服务';
            } else if (flag == 47) {
                return '洗车服务';
            } else if (flag == 37) {
                return '加油机器人';
            } else if (flag == 48 || flag == 49) {
                return '能源锦鲤商城';
            } else if (flag == 55) {
                return '散装油';
            } else {
                return '';
            }
        },
        /**
         * @description  : 跳转去评价页面
         * @return        {*}
         */
        toEvaluate(item) {
            let params = {
                evaluateType: 'order',
                ...item,
                refer: this.refer,
            };
            if (item.orderType == 3) {
                params.evaluateType = 'o2oOrder';
            }
            if (this.pageType == 'cube') {
                params.isOil = '1';
            }
            this.$sKit.layer.useRouter('/packages/third-evaluate/pages/home/<USER>', params);
        },
        /**
         * @description  : 调用订单列表查询接口和待支付订单查询接口，处理获取数据
         * @param         {Boolean} isInit: 是否初始化订单数据
         * @param         {Object} listParams: 查询订单接口入参
         * @return        {*}
         */
        async getOrderList({ isInit = false, listParams = {} } = {}) {
            // 重置页码
            this.listParams = listParams;
            if (isInit) {
                this.orderList.forEach(item => {
                    if (item?.timer) {
                        clearInterval(item.timer);
                        item.timer == null;
                    }
                });
                this.orderList = [];
                this.pageNum = 1;
            }
            this.$emit('loadStatusChange', 'loading');
            let params = {
                orderStatus: listParams.secondNavActive,
                startTime: listParams.timeObj.startTime + ' 00:00:00',
                endTime: listParams.timeObj.endTime + ' 23:59:59',
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };
            if (listParams.invoiceStatusActive == 1) {
                params.invoiceFlag = '';
            } else if (listParams.invoiceStatusActive == 3) {
                params.invoiceFlag = '0';
            } else {
                params.invoiceFlag = listParams.invoiceStatusActive.toString();
            }
            let res = await chargeOrderList(params, { isload: false });
            this.$emit('stopRefresh');
            if (res && res.success && res.data.rows) {
                let arr = res.data.rows || [];
                arr.forEach(item => {
                    if (item.orderExpirationTime && item.orderExpirationTime > 0) {
                        this.getCountdown(item);
                    }
                });
                if (res.data.pageNum == 1) {
                    this.orderList = arr;
                } else {
                    this.orderList = this.orderList.concat(arr || []);
                }
                if (listParams.secondNavActive == 1 || listParams.secondNavActive == 8) {
                    this.$emit('loadStatusChange', 'nomore');
                } else {
                    if (this.pageSize > res.data.rows.length) {
                        this.$emit('loadStatusChange', 'nomore');
                    } else {
                        this.$emit('loadStatusChange', 'contentdown');
                    }
                }
                this.pageNum++;
            }
            if (this.orderList.length == 0) {
                this.$emit('showEmptyChange', true);
            } else {
                this.$emit('showEmptyChange', false);
            }
        },
        /**
         * @description  : 判断订单类型 全部=0，已完成=4，已取消=5，待支付=1，待提货=8，已退款=9
         * @param         {String/Number} flag:orderStatus的值
         * @return        {String} orderStatus对应的文案
         */
        getOrderStatus(item) {
            if (item.orderStatus == 1) {
                return '待结算';
            } else if (item.orderStatus == 2) {
                return '充电中';
            } else if (item.orderStatus == 3) {
                return '已取消';
            } else if (item.orderStatus == 4) {
                return '已完成';
            } else {
                return '';
            }
        },
        /**
         * @description  : 将秒转换为时分秒
         * @param         {Number} milliseconds: 秒
         * @return        {Object} milliseconds: 剩余秒数   time: 时分秒展示用字符串
         */
        getDowmTime(milliseconds) {
            let seconds = Math.floor(milliseconds);
            let minutes = Math.floor(seconds / 60);
            let hours = Math.floor(minutes / 60);
            seconds %= 60;
            minutes %= 60;
            let time = `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(seconds)}`;
            return time;
        },
        /**
         * @description  : 将倒计时定时器添加到待支付订单数据中
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        getCountdown(item) {
            if (item.orderExpirationTime == 0) {
                item.time = '00:00:00';
                item.timer = null;
                return;
            }
            let milliseconds = item.orderExpirationTime;
            item.time = this.getDowmTime(milliseconds);
            let timer = setInterval(() => {
                milliseconds -= 1;
                item.time = this.getDowmTime(milliseconds);
                if (milliseconds < 0) {
                    clearInterval(item.timer);
                    item.timer = null;
                }
            }, 1000);
            item.timer = timer;
        },
        /**
         * @description  : 函数用于在数字不足两位数时，在前面补零
         * @param         {Number} value: 数字
         * @return        {*}
         */
        padZero(value) {
            return String(value).padStart(2, '0');
        },
        /**
         * @description  : 根据不同状态，订单详情跳转不同页面，如果订单数据中有倒计时，走待支付跳转逻辑
         * @param         {Object} item: 订单数据
         * @return        {*}
         */
        toDetail(item) {
            // if (item.orderStatus == 1) {
            //     this.payment(item);
            //     return;
            // }
            let params = { ...item, refer: this.refer };
            if (item.orderStatus == 4) {
                this.$sKit.layer.useRouter('/packages/third-order/pages/charge-order-detail/main', params);
            } else if (item.orderStatus == 1) {
                if (!item.orderNo) {
                    this.$store.dispatch('zjShowModal', {
                        title: '充电桩运营商暂未返回结算信息，请稍后再试',
                        success: res => {
                            if (res.confirm) {
                            }
                        },
                    });
                } else {
                    this.$sKit.layer.useRouter('/packages/third-order/pages/charge-cancelled-order-detail/main', params);
                }
            }
        },
        /**
         * @description  : 待支付订单跳转逻辑
         * @param         {Object} item: 订单数据
         * @return        {*}
         */
        async payment(item) {
            this.$sKit.mpBP.tracker('我的消费订单', {
                seed: 'xfOrderBiz',
                pageID: 'payBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let res = await unPaidOrderApi({});
            if (res && res.success) {
                if (res.data) {
                    // 待支付订单
                    let params = { unPaidInfo: { ...res.data, cancel: true } };
                    params.stationType = res.data.newStationFlag == 0 ? 1 : 0;
                    // 支付宝referer过长兼容
                    params.zfbRefererMax = true;
                    params.refer = 'r31';
                    let url = '/packages/third-oil-charge-payment/pages/oil-charge-payment/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        title: '订单已失效，不可支付',
                        success: res => {
                            if (res.confirm) {
                                this.getOrderList({ isInit: true, listParams: this.listParams });
                            }
                        },
                    });
                }
            }
        },
        /**
         * @description  : 开票跳转逻辑
         * @param         {Object} item:订单数据
         * @param         {String} type: 开票页面逻辑类型 'invoice'为3.0开票
         * @param         {Number} checkAllAmount: 开票总金额
         * @param         {Array} orderNoList: 开票订单号列表
         * @return        {*}
         */
        async invoice(item) {
            this.$sKit.mpBP.tracker('我的消费订单', {
                seed: 'xfOrderBiz',
                pageID: 'invoicingBut',
                refer: this.refer,
                channelID: clientCode,
            });
            let apiParams = {
                orderList: [
                    {
                        stationCode: item.stationCode,
                        businessDay: item.businessDay,
                        orderNo: item.orderNo,
                    },
                ],
            };
            let res = await chargeBeforeMakeInvoiceCheckApi(apiParams, {
                handleErrorFn: () => {
                    this.getOrderList({ isInit: true, listParams: this.listParams });
                },
            });
            if (res && res.success) {
                if (res.data.flag) {
                    let goods = item.chargeOrderItemList
                        .map(goodsItem => {
                            return goodsItem.productName;
                        })
                        .join(',');
                    let params = {
                        type: 'invoice',
                        orderType: 'charge',
                        checkAllAmount: item.actualPayTotalAmount,
                        orderNoList: [item.orderNo],
                        createTime: item.createTime,
                        orgName: item.stationName,
                        goods: goods,
                        refer: 'r32',
                    };
                    this.$sKit.layer.useRouter('/packages/third-invoice/pages/invoice-form/main', params);
                }
            }
        },
        /**
         * @description  : 查看发票逻辑，根据订单号查发票列表取第一张，因混合订单可开具油品非油两张发票，产品设计只能跳转查看一张发票，当订单中有油品筛选油品发票，默认展示油品发票
         * @param         {Object} item:订单数据
         * @return        {*}
         */
        async seeInvoice(item) {
            let params = {
                orderNum: item.orderNo,
            };
            let res = await getInvoiceByOrderNoApi(params);
            if (res && res.success) {
                if (res.data.length > 0) {
                    let params = res.data[0];
                    let url = '/packages/third-invoice/pages/invoice-detail/main';
                    this.$sKit.layer.useRouter(url, params);
                } else {
                    this.$store.dispatch('zjShowModal', {
                        content: '请前往我的-电子发票-开票历史里查看发票',
                        confirmText: '确定',
                        success(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        },
                    });
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.list-card {
    width: 100%;
    height: 100%;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    .choice {
        width: 73rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .oreder-content {
        background: #ffffff;
        padding: 16rpx 30rpx 27rpx;
        box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(0, 0, 0, 0.07);
        border-radius: 16rpx;
        width: 100%;
        height: 100%;

        .detail-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40rpx;

            .name {
                white-space: nowrap; //禁止换行
                text-overflow: ellipsis; //...
                display: flex;
                align-items: center;

                .name-img {
                    width: 16px;
                    height: 16px;
                }

                .title {
                    max-width: 182px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000000;
                    margin-left: 10rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                }
            }

            .invoice {
                font-size: 24rpx;
                font-weight: 400;
                color: #e64f22;
            }

            .gray {
                color: #999999 !important;
            }
        }

        .detail-contetn {
            margin-top: 16rpx;

            display: flex;
            justify-content: space-between;

            .detail-left {
                display: flex;

                .detail-left-img {
                    width: 100rpx;
                    height: 100rpx;
                }

                .order-name {
                    max-width: 380rpx;
                    padding-top: 2px;
                    margin-left: 20rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .detail-price {
                text-align: right;

                .unitPrice {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }

                .litre {
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 40rpx;
                }
            }
        }

        .paymentAmount {
            height: 25px;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            margin-top: 13rpx;

            div {
                line-height: 25px;
                font-size: 14px;
            }

            div:nth-child(1) {
                color: #666666;
                font-family: PingFangSC-Regular, PingFang SC;
            }

            div:nth-child(2) {
                color: #e64f22;
                font-family: PingFangSC-Medium, PingFang SC;
            }

            div:nth-child(3) {
                color: #e64f22;
                font-weight: bold;
                font-family: PingFangSC-Medium, PingFang SC;
            }
        }

        .relevantInfo {
            margin-top: 28rpx;

            .infoItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                div {
                    font-size: 26rpx;
                    color: #888888;
                    line-height: 42rpx;
                }
            }
        }

        .countdown {
            display: flex;
            align-items: center;

            div {
                &:nth-of-type(1) {
                    font-size: 36rpx;
                    font-weight: 600;
                    color: #e64f22;
                    line-height: 80rpx;
                }

                &:nth-of-type(2) {
                    margin-left: 10rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #888888;
                    line-height: 80rpx;
                }
            }
        }

        .detail-center {
            margin-top: 20rpx;
        }

        .detail-bottom {
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            div {
                margin-left: 25rpx;
            }

            .bottom-button-pay {
                text-align: center;
                width: 209rpx;
                height: 80rpx;
                background-image: linear-gradient(288deg, #e64f22 0%, #ff7b33 100%);
                border-radius: 16rpx;
                font-size: 30rpx;
                font-weight: 400;
                color: #ffffff;
                line-height: 80rpx;
            }

            .bottom-button-open {
                text-align: center;
                width: 209rpx;
                height: 80rpx;
                border-radius: 16rpx;
                border: 1px solid #e64f22;
                font-size: 30rpx;
                color: #e64f22;
                line-height: 80rpx;
            }

            .bottom-button-cancel {
                text-align: center;
                width: 209rpx;
                height: 80rpx;
                border-radius: 16rpx;
                border: 1px solid #333333;
                font-size: 30rpx;
                color: #333333;
                line-height: 80rpx;
            }

            .bottom-button-see {
                text-align: center;
                font-size: 30rpx;
                font-weight: 400;
                color: #333333;
                line-height: 80rpx;
            }
        }
    }
}
</style>
