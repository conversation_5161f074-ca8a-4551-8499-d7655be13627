<!--created by lq 2023/1/19-->
<template>
    <div class="oil-charge-bill-time">
        <div v-if="resultData.orderSubType == 13" class="show-title">请向加油员出示账单</div>
        <div class="price-content">
            <div class="price-title">实付金额</div>
            <div class="real-p">
                <span class="small">&yen;</span>
                <span class="big font-style">{{ resultData.actualPayTotalAmount }}</span>
            </div>
        </div>
        <div class="time-info">
            <p class="time-num countdown-bg1">{{ hour }}</p>
            <p class="time-sep">:</p>
            <p class="time-num countdown-bg2">{{ minutes }}</p>
            <p class="time-sep">:</p>
            <p class="time-num countdown-bg3">{{ seconds }}</p>
            <p class="time-sep">:</p>
            <p class="time-num countdown-bg4">{{ count }}</p>
        </div>
    </div>
</template>

<script>
export default {
    name: 'oil-charge-bill-time',
    components: {},
    props: {
        resultData: Object,
    },
    data() {
        return {
            timer: null,
            // 倒计时展示绑定值
            hour: '00',
            minutes: '00',
            seconds: '00',
            count: '00',
        };
    },
    created() {
        console.log(this.resultData);
    },
    mounted() {
        clearInterval(this.timer);
        // this.goto();
    },
    methods: {
        // 时钟
        goto() {
            this.timer = setInterval(() => {
                this.count = Number(this.count);
                if (this.count < 99) {
                    this.count = this.count + 1;
                } else {
                    this.count = 0;
                }
                if (this.count < 10) {
                    this.count = '0' + this.count;
                }
                var date = new Date();
                this.hour = date.getHours(); // 时
                if (this.hour >= 0 && this.hour <= 9) {
                    this.hour = '0' + this.hour;
                }
                this.minutes = date.getMinutes(); // 分
                if (this.minutes >= 0 && this.minutes <= 9) {
                    this.minutes = '0' + this.minutes;
                }
                this.seconds = date.getSeconds(); //秒
                if (this.seconds >= 0 && this.seconds <= 9) {
                    this.seconds = '0' + this.seconds;
                }
            }, 10);
        },
        //销毁定时器
        clearDate() {
            clearInterval(this.timer);
            this.timer = null;
        },
    },
    // 销毁计时器
    beforeDestroy() {
        clearInterval(this.timer);
        this.timer = null;
    },
    destroyed() {
        clearInterval(this.timer);
        this.timer = null;
    },
};
</script>

<style lang="scss" scoped>
.oil-charge-bill-time {
    border-radius: 8px;
    background-color: white;
    padding: 20px 20px 35px 20px;

    .show-title {
        font-size: 24px;
        color: #333;
        font-weight: bold;
        margin-top: 10rpx;
        margin-bottom: 5rpx;
        text-align: center;
        line-height: 66rpx;
    }

    .price-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .price-title {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin-top: 10px;
        }

        .real-p {
            font-weight: 600;
            color: #333333;

            .small {
                font-weight: bold;
                font-size: 24px;
            }

            .big {
                // font-weight: bold;
                font-size: 36px;
            }
        }
    }

    .time-info {
        margin-top: 28px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        .time-num {
            width: 44px;
            height: 44px;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.07);
            line-height: 44px;
            text-align: center;
            font-weight: bold;
            color: #ffffff;
            border-radius: 6px;
        }

        .time-sep {
            font-size: 16px;
            font-weight: bold;
            color: #e64f22;
            margin: 0px 7px;
        }
    }
}
</style>
