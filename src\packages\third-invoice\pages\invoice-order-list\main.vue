<template>
    <page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>
    <div class="pageMpaas">
        <div class="fl-column" style="height: 100%">
            <zj-navbar :border-bottom="false" title="查看发票"></zj-navbar>
            <div class="content f-1 mh-0 bg-F7F7FB">
                <div class="title">
                    发票代码：{{ invoiceCode }}包含以下 <div>{{ list.length }}</div
                    >个消费订单
                </div>
                <div class="time">开票时间：{{ issueDate }}</div>
                <template v-if="orderType == 'charge'">
                    <div class="card" v-for="(item, index) in list" :key="index">
                        <div class="detail-top">
                            <div class="name">
                                <img mode="scaleToFill" src="../../image/chargeCheer.png" alt class="name-img" />
                                <div class="title">{{ item.stationName }}</div>
                                <div class="name-arrow-right"></div>
                            </div>
                            <div :class="{ invoice: true, gray: true }">{{ getInvoiceFlag(item.orderStatus) }}</div> </div
                        ><div class="relevantInfo">
                            <div class="infoItem">
                                <div class="itemLeft">充电电量</div>
                                <div class="itemRight">{{ item.chargeQty + '度' || '' }}</div>
                            </div>
                            <div class="infoItem">
                                <div class="itemLeft">实付总额</div>
                                <div class="itemRight">&yen;{{ item.actualPayTotalAmount || 0 }}</div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="card" v-for="(item, index) in list" :key="index">
                        <div class="detail-top">
                            <div class="name">
                                <img mode="scaleToFill" src="../../image/cheer.png" alt class="name-img" />
                                <div class="title">{{ item.stationName }}</div>
                                <div class="name-arrow-right"></div>
                            </div>
                            <div :class="{ invoice: true, gray: true }">{{ getInvoiceFlag(item.orderStatus) }}</div>
                        </div>
                        <!-- 将商品分为油品和非油品 -->
                        <div class="detail-contetn" v-for="(goodItem, idx) in item.orderItems" :key="idx">
                            <div class="detail-left">
                                <img v-if="goodItem.productType == 1" class="detail-left-img" src="../../image/order_oils.png" alt />
                                <img class="detail-left-img" v-else :src="goodItem.imgUrl" alt />
                                <div class="order-name">{{ goodItem.productName }}</div>
                            </div>
                            <div class="detail-price">
                                <div class="unitPrice">&yen;{{ goodItem.unitPrice }}</div>
                                <div class="litre">{{ 'x ' + goodItem.productQty + goodItem.productUnit }}</div>
                            </div>
                        </div>
                        <div class="paymentAmount">
                            <div class="totalPayment-hint">实付总额：</div>
                            <div class="totalPayment-sum">&yen;</div>
                            <div class="chars font-style">{{ item.actualPayTotalAmount || 0 }}</div>
                        </div>
                    </div>
                </template>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { detailList, chargeDetailList } from '../../../../s-kit/js/v3-http/https3/order/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'invoice-order-list',
    data() {
        return {
            list: [],
            invoiceCode: '',
            issueDate: '',
            orderType: '',
        };
    },
    onLoad(options) {
        let params = JSON.parse(decodeURIComponent(options.data));
        this.orderNum = params.orderNum;
        this.invoiceCode = params.invoiceCode;
        this.issueDate = params.issueDate;
        this.orderType = params.orderType || '';
        this.orderDetailPost();
    },
    methods: {
        /**
         * @description  : 订单发票状态
         * @param         {*} flag:发票状态值
         * @return        {*}
         */
        getInvoiceFlag(flag) {
            if (flag == 4) {
                return '已完成';
            } else {
                return '';
            }
        },
        /**
         * @description  : 获取订单列表
         * @return        {*}
         */
        async orderDetailPost() {
            let params = {};
            let postFun = () => {};
            if (this.orderType == 'charge') {
                params = {
                    orderNoList: this.orderNum,
                };
                postFun = chargeDetailList;
            } else {
                params = {
                    orderNum: this.orderNum,
                };
                postFun = detailList;
            }
            let res = await postFun(params);
            if (res && res.success) {
                this.list = res.data.rows;
            }
        },
    },
};
</script>
<style lang="stylus" scoped>
@import '~assets/stylus/index.styl'
</style>
<style lang="scss" scoped>
@import '../../../../s-kit/css/index.scss';
.content {
    padding: 32rpx;

    .title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        line-height: 40rpx;
        display: flex;

        span {
            color: #e75b31;
        }
    }

    .time {
        font-size: 28rpx;
        font-weight: 400;
        color: #666666;
        line-height: 40rpx;
        margin-top: 24rpx;
    }

    .card {
        width: 100%;
        box-sizing: border-box;
        background: #ffffff;
        border-radius: 16rpx;
        margin-top: 24rpx;
        padding: 16rpx 30rpx 27rpx;
        box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(0, 0, 0, 0.07);

        .detail-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40rpx;

            .name {
                white-space: nowrap; //禁止换行
                text-overflow: ellipsis; //...
                display: flex;
                align-items: center;

                .name-img {
                    width: 16px;
                    height: 16px;
                }

                .title {
                    max-width: 182px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #000000;
                    margin-left: 10rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    white-space: nowrap;
                    display: block;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                }
            }
        }

        .relevantInfo {
            margin-top: 28rpx;

            .infoItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                div {
                    font-size: 26rpx;
                    color: #888888;
                    line-height: 42rpx;
                }
            }
        }

        .detail-contetn {
            margin-top: 16rpx;

            display: flex;
            justify-content: space-between;

            .detail-left {
                display: flex;

                .detail-left-number {
                    width: 100rpx;
                    height: 100rpx;
                    background-color: #f5c41b;
                    line-height: 100rpx;
                    text-align: center;
                    font-weight: bold;
                    font-size: 40rpx;

                    span {
                        vertical-align: text-top;
                        font-size: 24rpx;
                        line-height: 20rpx;
                    }
                }

                .detail-left-img {
                    width: 100rpx;
                    height: 100rpx;
                }

                .order-name {
                    padding-top: 2px;
                    margin-left: 20rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .detail-price {
                text-align: right;

                .unitPrice {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }

                .litre {
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 40rpx;
                }
            }
        }

        .paymentAmount {
            height: 25px;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            margin-top: 13rpx;

            div {
                line-height: 25px;
                font-size: 14px;
            }

            div:nth-child(1) {
                color: #666666;
                font-family: PingFangSC-Regular, PingFang SC;
            }

            div:nth-child(2) {
                color: #e64f22;
                font-family: PingFangSC-Medium, PingFang SC;
            }

            div:nth-child(3) {
                color: #e64f22;
                font-weight: bold;
                font-family: PingFangSC-Medium, PingFang SC;
            }
        }
    }
}

.order-item {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    padding: 29rpx 27rpx 29rpx 29rpx;
    display: flex;
    flex-direction: column;

    &:nth-last-of-type(1) {
        margin-bottom: 0;
    }

    .item-top {
        display: flex;
        justify-content: space-between;

        .item-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .title {
                display: flex;
                align-items: center;

                .title-text {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #333333;
                    line-height: 33rpx;
                }

                .name-arrow-right {
                    box-sizing: border-box;
                    width: 15rpx;
                    height: 15rpx;
                    margin-top: -3rpx;
                    border: solid #000;
                    border-width: 0 3rpx 3rpx 0;
                    transform: rotate(-45deg);
                    margin-left: 15rpx;
                }
            }

            .time {
                margin-top: 10rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #999999;
                line-height: 33rpx;
            }
        }

        .item-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .state {
                font-size: 24rpx;
                font-weight: bold;
                color: #333333;
                line-height: 33rpx;
            }

            .amount {
                margin-top: 10rpx;
                font-size: 24rpx;
                font-weight: bold;
                color: #e64f22;
                line-height: 33rpx;
            }
        }
    }

    .countdown {
        display: flex;
        align-items: center;

        div {
            &:nth-of-type(1) {
                font-size: 36rpx;
                font-weight: 600;
                color: #e64f22;
                line-height: 80rpx;
            }

            &:nth-of-type(2) {
                margin-left: 10rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #888888;
                line-height: 80rpx;
            }
        }
    }
}
</style>
