<template>
	<page-meta :root-font-size="rootFontSize" @resize="pageMetaResize"></page-meta>	
    <div class="pageMpaas">
        <div class="wallet_details p-bf bg-F7F7FB">
            <zj-navbar title="支付订单查询" :border-bottom="false" @click="goBack"></zj-navbar>
            <div class="wallet_details_content">
                <div class="header_div bg-fff">
                    <!-- <div class="fl-column fl-al-jus-cen" v-if="status == 'success'">
                        <img src="../../image/success_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">您的操作已完成</div>
                        <div class="font-12 color-999 text">如已支付，请查看充值是否到账</div>
                    </div> -->
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'fail'">
                        <img src="../../images/fail_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold"
                            >暂未查询到该笔订单的支付结果，稍后可到订单页查看，若仍未查到，请与站内工作人员沟通</div
                        >
                        <!-- <div class="amount font-16 color-333 weight-bold">建议您联系956100客服</div> -->
                    </div>
                    <div class="fl-column fl-al-jus-cen" v-if="status == 'waiting'">
                        <img src="../../images/wait_img.png" alt />
                        <div class="amount font-16 color-333 weight-bold">正在查询支付结果</div>
                        <span class="loading">正在加载中<div class="dot">...</div> </span>
                    </div>
                </div>
                <div class="btn_div" v-if="status == 'fail'">
                    <div class="finish_verification2 color-E64F22 bg-fff" @click="backClick">返回</div>
                </div>
            </div>
            <zj-show-modal></zj-show-modal>
        </div>
    </div>
</template>

<script>
import { statuslOrderApi, savedOrderApi } from '../../../../s-kit/js/v3-http/https3/oilStationService/index';
import publicMixinsApi from '@/s-kit/js/public-mixins-api.js';
export default {
    mixins: [publicMixinsApi],
    name: 'third-charge-result',
    data() {
        return {
            status: 'waiting',
            pollTime: 3000,
            requestStatusInterval: null,
            beginTime: 0,
            // 阻止递归标识
            stopRecursionFlag: true,
            codeOrderInfo: {},
        };
    },
    onLoad(option) {
        this.codeOrderInfo = JSON.parse(decodeURIComponent(option.data));
        console.log(this.codeOrderInfo, 'this.codeOrderInfo');
        setTimeout(() => {
            this.beginTime = new Date().getTime();
            this.queryOrderPayStatus();
        }, this.pollTime);
    },
    mounted() {},
    methods: {
        /**
         * @description  : 查询订单状态
         * @param         {String} orderNo -打开获关闭弹窗
         * @param         {String} stationCode -油站编码
         * @return        {*}
         */
        queryOrderPayStatus() {
            // uni.showLoading({
            //     title: '加载中',
            //     mask: true,
            // });
            if (this.stopRecursionFlag) {
                statuslOrderApi(
                    {
                        stationCode: this.codeOrderInfo.stationCode,
                        orderNo: this.codeOrderInfo.orderId,
                    },
                    { isload: false },
                )
                    .then(result => {
                        if (result.success) {
                            if (result.data.orderPayStatus == 4) {
                                //订单支付状态 1待支付；2支付失败；3部分支付；4全部支付  13 部分退款  14 全部退款
                                //成功要跳转
                                this.getSavedOrderDetail();
                            } else {
                                // 清除定时器
                                if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                                // 查询一分钟后如果未查询到订单弹出提示
                                if (new Date().getTime() - this.beginTime > 60 * 1000) {
                                    // this.$LoadingNew.close();
                                    // this.$dialog.alert({ message: '未查询到支付结果，请去我的订单中查看订单状态！' });
                                    uni.showToast({ title: '未查询到支付结果，请去我的订单中查看订单状态！' });
                                    uni.hideLoading();
                                    return;
                                }
                                this.requestStatusInterval = setTimeout(() => {
                                    this.queryOrderPayStatus();
                                }, this.pollTime);
                            }
                        } else {
                            if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                            this.requestStatusInterval = setTimeout(() => {
                                this.queryOrderPayStatus();
                            }, this.pollTime);
                        }
                    })
                    .catch(error => {
                        if (this.requestStatusInterval) clearTimeout(this.requestStatusInterval);
                        // 查询一分钟后如果未查询到订单弹出提示
                        if (new Date().getTime() - this.beginTime > 60 * 1000) {
                            // this.$LoadingNew.close();
                            // this.$dialog.alert({ message: '未查询到支付结果，请去我的订单中查看订单状态！' });
                            uni.hideLoading();
                            uni.showToast({ title: '未查询到支付结果，请去我的订单中查看订单状态！' });
                        } else {
                            this.requestStatusInterval = setTimeout(() => {
                                this.queryOrderPayStatus();
                            }, this.pollTime);
                        }
                    });
            } else {
                uni.hideLoading();
            }
        },
        /**
         * @description  : 查询已支付完成的订单详情
         * @param         {String} orderNo -打开获关闭弹窗
         * @return        {*}
         */
        getSavedOrderDetail() {
            let params = {
                orderNo: this.codeOrderInfo.orderId,
            };
            savedOrderApi(params, { isload: false }).then(res => {
                if (res.data) {
                    // 查询到支付结果后变为false 防止递归继续查询
                    this.stopRecursionFlag = false;
                    this.orderDetail = res.data;
                    let url = '/packages/third-scan-code-payment/pages/code-order-payment-code-results/main';
                    let params = { ...res.data, refer: this.codeOrderInfo.refer };
                    let type = 'redirectTo'; // 默认  uni.navigateTo({})
                    this.$sKit.layer.useRouter(url, params, type);
                    uni.hideLoading();
                } else {
                    uni.hideLoading();
                }
            });
        },
        /**
         * @description  : 点击左上角返回
         * @param
         * @return        {*}
         */
        leftGoBack() {
            const pages = getCurrentPages();
            if (this.status == 'waiting') {
                this.$store.dispatch('zjShowModal', {
                    title: '',
                    content: '支付结果仍在查询中，返回后可到订单页查看，若仍未查到，请与站内工作人员沟通，确定返回吗？',
                    confirmText: '确认',
                    cancelText: '再等等',
                    confirmColor: '#000',
                    cancelColor: '#666',
                    success: res => {
                        if (res.confirm) {
                            this.goBack();
                        }
                    },
                });
            } else {
                uni.navigateBack();
            }
        },
        //返回
        backClick() {
            this.goBack();
        },
        /**
         * @description  : 返回首页和上一个页面
         * @return        {*}
         */
        goBack() {
            const pages = getCurrentPages();
            if (pages.length <= 1) {
                // #ifdef MP-MPAAS
                this.$cnpcBridge.closeMriver(res => {
                    uni.showToast({
                        title: JSON.stringify(res),
                        icon: 'none',
                        duration: 2000,
                    });
                });
                // #endif
                // #ifndef MP-MPAAS
                this.backHome();
                // #endif
            } else {
                uni.navigateBack();
            }
        },
        //返回首页
        backHome() {
            uni.reLaunch({
                url: '/pages/thirdHome/main',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet_details {
    .wallet_details_content {
        // background $page-base-bg-color
        padding: 16px;

        .header_div {
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-bottom: 60px;
            padding-top: 60px;
            border-radius: 8px;

            img {
                width: 64px;
                height: 64px;
                margin-bottom: 12px;
            }

            .amount {
                line-height: 23px;
                padding-left: 16px;
                padding-right: 16px;
            }
            .loading {
                display: flex; /*让dot和文字在同一行*/
                align-items: center;
            }
            .dot {
                /*让点垂直居中*/
                height: 1em;
                line-height: 1;
                /*让点垂直排列*/
                display: flex;
                flex-direction: column;
                /*溢出部分的点隐藏*/
                overflow: hidden;
                font-size: 28px;
                margin-bottom: 5px;
            }
            .dot::before {
                /*三行三种点，需要搭配white-space:pre使用才能识别\A字符*/
                content: '...\A..\A.';
                white-space: pre-wrap;
                animation: div 3s infinite step-end; /*step-end确保一次动画结束后直接跳到下一帧而没有过渡*/
            }
            @keyframes div {
                33% {
                    transform: translateY(-2em);
                }
                66% {
                    transform: translateY(-1em);
                }
            }

            .text {
                line-height: 23px;
            }
        }
    }
}

.btn_div {
    margin-top: 20px;
    font-size: 15px;
    display: flex;
    flex-direction: row;

    .finish_verification {
        width: 48%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }

    .finish_verification2 {
        width: 100%;
        text-align: center;
        border-radius: 8px;
        height: 44px;
        line-height: 44px;
    }
}
</style>
