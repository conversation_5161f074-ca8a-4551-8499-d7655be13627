<template>
    <div class="view">
        <u-navbar
            :back-icon-size="40"
            :height="44"
            :background="pageConfig.bgColor"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :title-color="pageConfig.titleColor.color"
            :custom-back="clickCustomBackBtn"
            back-text="支付方式选择"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        >
        </u-navbar>
        <!-- 主页面 -->
        <div class="content-view">
            <div class="top-price-view">
                <div class="price-icon">￥</div>
                <div class="price-title">{{ orderData.amount }}</div>
            </div>
            <div class="down-view">
                <div class="down-title">请在</div>
                <u-count-down @end="downEnd" class="down-view" font-size="28" bg-color="#f6f6f6" :timestamp="300"></u-count-down>
                <div class="down-title">时间内完成支付</div>
            </div>
            <!-- 订单列表 -->
            <div class="order-list">
                <div class="order-title-text">加油订单</div>
                <div class="order-item">
                    <div class="order-title">车牌号码</div>
                    <div class="order-detail">{{ orderData.carCode }}</div>
                </div>
                <div class="order-item">
                    <div class="order-title">油号/油枪</div>
                    <div class="order-detail">
                        {{ orderData.ypCode + ' / ' + orderData.oilGun }}
                    </div>
                </div>
                <div class="order-item">
                    <div class="order-title">加油量</div>
                    <div class="order-detail">{{ orderData.quantity }}</div>
                </div>
                <div class="order-item">
                    <div class="order-title">应付金额</div>
                    <div class="order-detail">￥{{ orderData.amount }}</div>
                </div>
            </div>
            <!-- 优惠模块 -->
            <div class="order-list">
                <div class="order-title-text">加油优惠</div>
                <div class="order-item" @click="clickOpenSelectCoupon">
                    <div class="order-title">优惠券</div>
                    <div class="order-detail" :class="{ light: selectCouponIndex !== -1 }">
                        {{ selectCouponTip }}
                    </div>
                </div>
            </div>
            <!-- 支付选择模块 -->
            <div class="chiose-pay-view">
                <div @click="clickChoisePayType(0)" class="pay-item">
                    <img class="pay-icon" src="@/static/pay-wx.png" mode="" />
                    <div class="pay-title">微信支付</div>
                    <img class="pay-dui" :src="selectPayIndex == 0 ? '/static/select-icon.png' : '/static/selected-icon.png'" mode="" />
                </div>
                <div class="pay-item" v-if="selectMarker.cardPay == 1">
                    <div @click="clickChoiseOilCard" class="pay-item-left flex-align-center">
                        <img class="pay-icon" src="/static/pay-oilcard.png" mode="" />
                        <div class="pay-title">
                            油卡支付{{ oilCardArr.length ? '(' + oilCardArr[selectOilCardIndex].afterNum + ')' : '' }}
                        </div>
                    </div>
                    <div @click="clickChoisePayType(1)" class="pay-item-right flex-align-center">
                        <div class="pay-balance">
                            {{ oilCardArr.length ? '卡余额：￥' + oilCardArr[selectOilCardIndex].balance : '暂无油卡' }}
                        </div>
                        <img class="pay-dui" :src="selectPayIndex == 1 ? '/static/select-icon.png' : '/static/selected-icon.png'" mode="" />
                    </div>
                </div>
            </div>
        </div>
        <!-- 下方功能区 -->
        <div class="btn-view">
            <div class="btn-content-view">
                <div class="btn-title">合计：</div>
                <div class="btn-price-view">
                    <div class="actual-view">
                        <div class="btn-price-icon">￥</div>
                        <div class="btn-price-text">{{ calPrice }}</div>
                    </div>
                    <div class="discount-price">
                        优惠：￥{{ selectCouponIndex == -1 ? '0.00' : couponArr[selectCouponIndex].faceValue }}
                    </div>
                </div>
                <div class="btn" @click="clickPayBtn">立即支付</div>
            </div>
        </div>
        <!-- 支付失败弹窗 -->
        <uni-pop ref="failpopup" type="center" :maskClick="false">
            <div class="suc-pop-view">
                <div class="suc-pop-content">
                    <img class="suc-pop-icon" src="@/static/homeIcon/failPay.png" />
                    <div class="suc-pop-title">支付失败</div>
                </div>
                <u-icon
                    @click="clickClosefailPop"
                    name="close-circle-fill"
                    :custom-style="{
                        marginTop: '40rpx',
                    }"
                    color="#ffffff"
                    size="92"
                ></u-icon>
            </div>
        </uni-pop>
        <!-- 优惠券选择弹窗 -->
        <u-popup v-model="isShowCoupon" mode="bottom" border-radius="20" safe-area-inset-bottom>
            <div class="pop-view">
                <div class="pop-title-view">
                    <div class="pop-title-text">可用电子券</div>
                    <div class="close-btn" @click="clickCloseCouponPop(true)">
                        <div class="close-text">暂不使用</div>
                        <u-icon class="pop-close-btn" size="22" name="close" color="#979797"></u-icon>
                    </div>
                </div>
                <scroll-view class="coupon-scroll-view" :scroll-y="true" :enhanced="true" :show-scrollbar="false">
                    <coupon-list :datalist="couponArr" ispop @click="clickItem" :index="selectCouponIndex"></coupon-list>
                </scroll-view>
            </div>
        </u-popup>
        <!-- 油卡选择弹窗 -->
        <u-popup v-model="isShowOilCard" mode="bottom" border-radius="20" safe-area-inset-bottom>
            <div class="pop-view">
                <div class="pop-title-view">
                    <div class="pop-title-text">可用油卡</div>
                    <div class="close-btn" @click="clickCloseOilCard">
                        <u-icon class="pop-close-btn" size="22" name="close" color="#979797"></u-icon>
                    </div>
                </div>
                <scroll-view class="coupon-scroll-view" :scroll-y="true" :enhanced="true" :show-scrollbar="false">
                    <oil-card-list :datalist="oilCardArr" @click="clickCardItem" :index="selectOilCardIndex"></oil-card-list>
                </scroll-view>
            </div>
        </u-popup>
        <!-- 支付密码弹窗 -->
        <payment-password
            :show="isShowPasswordBox"
            :showError="isShowPaymentPasswordErrorBox"
            :amount="calPrice"
            @reEnter="handleReEnterPaymentPassword"
            @forgetPwd="handleForgetPaymentPassword"
            @close="handleClosePasswordBox"
            @completed="handleCompletePaymentPasswordInput"
            @update:showError="this.isShowPaymentPasswordErrorBox = $event"
        />
        <zj-show-modal></zj-show-modal>
    </div>
</template>

<script>
import pageConfig from '@/utils/pageConfig.js';
import uniPop from '@/components/uni-popup/uni-popup.vue';
import paymentPassword from '@/components/payment-password/payment-password.vue';
import {
    getTradeList,
    getOilGunsByStationCode,
    preOrder,
    orderDetail,
    getUnusedList,
    getCardDetails,
    cardListPost,
    cancleOrder,
    waitPayOrder,
    cardPay,
    payAuthentication,
    getUseableList,
} from '@/api/home.js';
import couponList from '@/components/coupon-list/coupon-list.vue';
import oilCardList from '@/components/oil-card-list/oil-card-list.vue';
import { mapState, mapGetters } from 'vuex';

const decimalSubtract = (a, b) => (a * 1000 - b * 1000) / 1000;
const minPayAmount = 0.01; // 最小支付金额
const payModeWX = 'wx';
const payModeOilCard = 'oilCard';

export default {
    components: {
        couponList,
        uniPop,
        oilCardList,
        paymentPassword,
    },
    computed: {
        ...mapGetters(['isPassword']),
        calPrice() {
            const couponIdx = this.selectCouponIndex;
            const originAmount = this.orderData.amount;
            if (couponIdx !== -1) {
                const couponAmount = this.couponArr[couponIdx].faceValue;
                return Math.max(decimalSubtract(originAmount, couponAmount), minPayAmount);
            }
            return originAmount;
        },
        ...mapState({
            selectMarker: state => state.location.selectMarker, // 选中的marker数据
        }),
        selectCouponTip() {
            const selCouponIdx = this.selectCouponIndex;
            return selCouponIdx == -1
                ? this.notInUseCoupon
                    ? '暂不使用'
                    : '暂无可使用优惠券'
                : '-￥' + this.couponArr[selCouponIdx].faceValue;
        },
    },
    data() {
        return {
            pageConfig: pageConfig, // 页面配置
            orderData: '', // 订单信息
            isShowCoupon: false, // 是否显示优惠券弹窗
            isShowOilCard: false, // 是否展示油卡弹窗
            couponArr: [], // 优惠券列表
            oilCardArr: [], // 油卡列表
            selectOilCardIndex: 0, // 选中的油卡
            selectPayIndex: 0, // 选中的支付方式
            selectCouponIndex: -1, // 选中的优惠券
            isShowPasswordBox: false, // 是否显示自定义密码输入弹窗
            isShowPaymentPasswordErrorBox: false, // 是否显示支付密码输入错误弹窗
            notInUseCoupon: false, // 暂不使用优惠券
        };
    },
    async onLoad(option) {
        this.orderData = JSON.parse(option.orderdata);
        console.log(this.orderData);
        await this.handleGetUseableList();
        let oilCardRes = await cardListPost();
        for (let i = 0; i < oilCardRes.data.length; i++) {
            let oilCard = await getCardDetails({ cardNo: oilCardRes.data[i].cardNo });
            oilCardRes.data[i] = oilCard.data;
            oilCardRes.data[i].afterNum = oilCard.data.cardNo.substring(oilCard.data.cardNo.length - 4, oilCard.data.cardNo.length);
        }
        this.oilCardArr = oilCardRes.data;
    },
    async onUnload() {},
    methods: {
        // 获取可使用的优惠券
        async handleGetUseableList() {
            const payIndex = this.selectPayIndex;
            try {
                let couponRes = await getUseableList({
                    orderNo: this.orderData.orderNo,
                    payMode: [payModeWX, payModeOilCard][payIndex] || payModeWX,
                });
                this.couponArr = couponRes.data;
                // 默认选中第一个优惠券
                this.selectCouponIndex = this.couponArr.length > 0 ? 0 : -1;
            } catch (err) {
                console.warn('handleGetUseableList => err => ', err);
            }
        },
        // 完成支付密码输入
        async handleCompletePaymentPasswordInput({ value }) {
            try {
                const encryptedPassword = this.$util.payPassEncryption(value);
                const { idNum, idType, orderNo, cardNo } = (this._cardPayRes || {}).data;
                const { status, info } = await cardPay(
                    {
                        cardNo,
                        idNum, //证件号
                        idType, //证件类型
                        orderNo, //订单号
                        payPassword: encryptedPassword,
                        timestamp: Date.now(), //时间戳
                        nonce: this.$util.generateUUID(), //随机字串
                    },
                    null,
                    () => {
                        this.handlePayFail(payModeOilCard);
                    },
                );
                if (status === 0) {
                    // 验证成功
                    this.handlePaySuccess(this._cardPayRes);
                } else {
                    // 验证失败
                    if (info == '密码错误') {
                        this.isShowPaymentPasswordErrorBox = true;
                    } else {
                        // 不是密码错误
                        this.$util.showModal(info, true).then(() => {
                            this.handlePayFail(payModeOilCard);
                        });
                    }
                }
                this.isShowPasswordBox = false;
            } catch (err) {
                console.warn('handleCompletePaymentPasswordInput => err', err);
                this.isShowPasswordBox = false;
            }
        },
        /**
         * 关闭密码框
         */
        handleClosePasswordBox() {
            this.isShowPasswordBox = false;
            console.log('关闭密码框 => handleClosePasswordBox');
            this.handlePayFail(payModeOilCard);
        },
        // 重新输入支付密码
        handleReEnterPaymentPassword() {
            this.isShowPasswordBox = true;
        },
        // 忘记支付密码
        handleForgetPaymentPassword() {
            uni.navigateTo({
                url: '/packages/password/pages/edit-password/main?isfor=1',
            });
        },
        // 返回按钮点击事件
        async clickCustomBackBtn() {
            await this.$util.showModal('您确认要放弃支付么');
            await cancleOrder({ orderNo: this.orderData.orderNo });
            uni.$emit('confirmBack');
            uni.navigateBack();
        },
        // 立即支付点击事件
        async clickPayBtn() {
            const realAmt = this.selectCouponIndex == -1 ? this.orderData.amount : this.calPrice; // 实收金额
            const params = {
                orderNo: this.orderData.orderNo,
                carNumber: this.orderData.carCode,
                payMode: [payModeWX, payModeOilCard][this.selectPayIndex], // 微信支付
                realAmt, // 实收金额
            };

            // 处理电子券
            if (this.selectCouponIndex != -1) {
                const { voucher, faceValue } = this.couponArr[this.selectCouponIndex];
                Object.assign(params, {
                    authCode: voucher,
                    otherAmt: faceValue, // 电子券面额
                });
            }

            // 处理加油卡支付
            if (params.payMode == payModeOilCard) {
                // 判断是否设置支付密码
                const isSetPassword = this.isPassword;
                if (!isSetPassword) {
                    wx.showModal({
                        title: '提示',
                        content: '您尚未设置支付密码',
                        confirmText: '去设置',
                        confirmColor: '#FF8200',
                        showCancel: true,
                        success: function (res) {
                            if (res.confirm) {
                                uni.navigateTo({
                                    url: '/packages/password/pages/edit-password/main?isfor=0',
                                });
                            } else if (res.cancel) {
                                // 目前什么也不做处理
                            }
                        },
                    });
                    return;
                }

                const selectedOilCard = this.oilCardArr[this.selectOilCardIndex];
                if (selectedOilCard !== null && typeof selectedOilCard === 'object') {
                    params.cardNo = selectedOilCard.cardNo;
                } else {
                    await this.$util.showModal('您尚未绑定油卡', true);
                    return;
                }
            }

            // 微信支付
            const wxPay = res => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: res.data.timeStamp,
                    nonceStr: res.data.nonceStr,
                    package: res.data.prepay,
                    signType: res.data.signType,
                    paySign: res.data.paySign,
                    success: () => {
                        this.handlePaySuccess(res);
                    },
                    fail: () => {
                        this.handlePayFail(payModeWX);
                    },
                });
            };

            let res = await preOrder(params);
            if (res.status != -1) {
                if (params.payMode == payModeWX) {
                    // 微信支付
                    wxPay(res);
                } else {
                    // 油卡支付
                    // 尚未设置密码
                    this.isShowPasswordBox = true;
                    this._cardPayRes = res;
                }
            } else {
                await this.$util.showModal(res.info, true);
                await cancleOrder({ orderNo: this.orderData.orderNo });
                uni.$emit('confirmBack');
                uni.navigateBack();
            }
        },

        // 处理支付成功
        handlePaySuccess(res) {
            uni.redirectTo({
                url: '/packages/place-order/pages/pay-result/main?orderid=' + res.data.orderNo,
            });
        },
        // 处理支付失败
        async handlePayFail(payMode) {
            let res = await waitPayOrder();
            await cancleOrder({ orderNo: this.orderData.orderNo });
            if (res.data) {
                uni.redirectTo({
                    url: `/packages/place-order/pages/await-pay/main?data=${encodeURIComponent(
                        JSON.stringify(res.data),
                    )}&payMode=${payMode}&payAmount=${this.calPrice}`,
                });
            } else {
                uni.$emit('confirmBack');
                uni.navigateBack();
            }
        },
        // 点击选择油卡事件
        clickChoiseOilCard() {
            this.isShowOilCard = true;
        },
        // 油卡选择点击事件
        clickCardItem(index) {
            this.selectOilCardIndex = index;
            this.isShowOilCard = false;
        },
        clickCloseOilCard() {
            this.isShowOilCard = false;
        },
        // 支付方式选择点击事件
        clickChoisePayType(e) {
            if (e == this.selectPayIndex) return;
            this.selectPayIndex = e;
            this.handleGetUseableList();
        },
        // 定时器结束事件
        async downEnd() {
            await this.$util.showModal('支付超时', true);
            await cancleOrder({ orderNo: this.orderData.orderNo });
            uni.$emit('confirmBack');
            uni.navigateBack();
        },
        // 支付失败弹窗点击事件
        clickClosefailPop() {
            this.$refs.failpopup.close();
            //uni.navigateBack()
        },
        // 优惠券弹窗关闭点击事件
        // notInUse: 暂不使用
        clickCloseCouponPop(notInUse = false) {
            this.notInUseCoupon = notInUse;
            this.selectCouponIndex = -1;
            this.isShowCoupon = false;
        },
        clickOpenSelectCoupon() {
            this.isShowCoupon = true;
        },
        // 优惠券点击事件
        clickItem(e) {
            this.notInUseCoupon = false;
            this.selectCouponIndex = e;
            this.isShowCoupon = false;
        },
    },
};
</script>

<style scoped lang="scss">
.solid {
    border: 1px solid red;
}
.view {
    width: 100%;
    min-height: 100%;
    background-color: #f6f6f6;
    overflow: hidden;
    position: relative;
}
.btn-view {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #ffffff;
    .btn-content-view {
        display: flex;
        height: 64px;
        align-items: center;
        .btn-title {
            color: $btn-color;
            margin-left: 15px;
            margin-bottom: 5px;
            font-size: 15px;
            font-weight: 700;
        }
        .btn {
            line-height: 44px;
            text-align: center;
            width: 120px;
            border-radius: 5px;
            background-color: $btn-color;
            font-weight: 700;
            font-size: 15px;
            color: #ffffff;
            margin-right: 15px;
        }
        .btn-price-view {
            flex: 1;
            margin-top: 5px;
            .discount-price {
                margin-top: -3px;
                font-weight: 700;
                font-size: 12px;
                color: #333333;
            }
            .actual-view {
                display: flex;
                align-items: flex-end;
                .btn-price-icon {
                    font-size: 15px;
                    font-weight: 700;
                    color: $btn-color;
                    margin-bottom: 5px;
                }
                .btn-price-text {
                    font-size: 28px;
                    font-weight: 700;
                    color: $btn-color;
                }
            }
        }
    }
}
.content-view {
    padding-bottom: calc(64px + env(safe-area-inset-bottom));
    overflow: hidden;
    .top-price-view {
        margin-top: 10px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        .price-icon {
            color: $btn-color;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .price-title {
            color: $btn-color;
            line-height: 50px;
            font-size: 37px;
            font-weight: 700;
        }
    }
    .down-view {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        .down-title {
            font-size: 12px;
            color: #333333;
        }
        .down-view {
            margin-top: 1px;
            font-weight: 700;
        }
    }
    .order-list {
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;
        border-radius: 5px;
        background-color: #ffffff;
        padding-bottom: 5px;
        .order-title-text {
            line-height: 44px;
            font-size: 15px;
            width: 100%;
            padding-left: 10px;
            font-size: 15px;
            font-weight: 700;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            height: 30px;
            align-items: center;
            .order-title {
                font-size: 12px;
                color: #333333;
                margin-left: 10px;
            }
            .order-detail {
                font-size: 12px;
                color: #333333;
                margin-right: 10px;
                &.light {
                    color: $btn-color;
                }
            }
        }
    }
    .chiose-pay-view {
        margin-top: 10px;
        margin-left: 15px;
        width: 345px;
        border-radius: 5px;
        background-color: #ffffff;
        .line {
            border-bottom: 0.5px solid #bcbcbc;
        }
        .pay-item {
            display: flex;
            height: 50px;
            margin-left: 10px;
            margin-right: 10px;
            box-sizing: border-box;
            align-items: center;

            .pay-icon {
                width: 17px;
                height: 15px;
            }
            .pay-title {
                line-height: 50px;
                margin-left: 10px;
                flex: 1;
                color: #333333;
                font-size: 12px;
            }
            .pay-balance {
                color: #333333;
                font-size: 12px;
            }
            .pay-dui {
                width: 20px;
                height: 20px;
                padding-left: 10px;
                padding-top: 10px;
                padding-bottom: 10px;
            }
            .flex-align-center {
                display: flex;
                align-items: center;
            }
            .pay-item-right {
                flex: 1;
                justify-content: flex-end;
            }
        }
    }
}
// 底部弹窗
.pop-view {
    background-color: #f6f6f6;
    min-height: 160px;
    .pop-title-view {
        display: flex;
        height: 50px;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 0.5px solid #dcdddd;
        .pop-title-text {
            margin-left: 15px;
            color: #222222;
            font-size: 15px;
            font-weight: 700;
            flex: 1;
        }
        .close-btn {
            display: flex;
            align-items: center;
            .close-text {
                color: #909090;
                font-size: 12px;
            }
            .pop-close-btn {
                margin-left: 5px;
                padding-right: 10px;
                padding-top: 10px;
                padding-bottom: 10px;
                margin-right: 10px;
            }
        }
    }
    .coupon-scroll-view {
        height: 230px;
    }
}
// 支付失败弹窗
.suc-pop-view {
    display: flex;
    flex-direction: column;
    align-items: center;

    .suc-pop-content {
        border-radius: 10px;
        width: 345px;
        height: 235px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;

        .suc-pop-icon {
            width: 90px;
            height: 90px;
        }

        .suc-pop-title {
            margin-top: 15px;
            line-height: 36px;
            font-size: 24px;
            color: #141414;
            font-weight: 700;
        }

        .suc-pop-detail {
            line-height: 36px;
            font-size: 12px;
            color: #141414;
            font-weight: 700;
        }
    }
}
</style>
