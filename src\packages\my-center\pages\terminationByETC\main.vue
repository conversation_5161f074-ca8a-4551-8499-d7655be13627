<template>
    <div class="terminationByETC">
        <u-navbar
            :background="pageConfig.bgColor"
            :back-icon-size="40"
            :back-icon-color="pageConfig.titleColor.backIconColor"
            :height="44"
            :title-color="pageConfig.titleColor.color"
            back-text="ETC签约详情"
            :back-text-style="pageConfig.titleStyle"
            :border-bottom="false"
        ></u-navbar>
        <div class="terminationByETC-wrap">
            <div class="terminationByETC-content-wrap">
                <div class="title-left">车牌号</div>
                <div class="title-right">{{ carData.carNo }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">车牌颜色</div>
                <div class="title-right">{{ carNoColor }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">车辆品牌</div>
                <div class="title-right">{{ carData.carBrand }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">车辆颜色</div>
                <div class="title-right">{{ carData.carColor }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">ETC用户姓名</div>
                <div class="title-right">{{ carData.userName }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">身份证号码</div>
                <div class="title-right">{{ carData.idNo }}</div>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">预授权油品</div>
                <div class="title-right">
                    <input
                        @click="clickAuthorizedProducts"
                        class="center"
                        maxlength="0"
                        disabled
                        placeholder="请选择预授权油品"
                        v-model="authorizedProductsLabel"
                    />
                </div>
                <u-select
                    v-model="preAuthorizedProductsShow"
                    mode="mutil-column-auto"
                    :list="preAuthorizedProducts"
                    @confirm="authorizedProductsConfirm"
                ></u-select>
                <u-icon
                    class="right-icon uicon-arrow-right"
                    name="arrow-right"
                    @click="clickAuthorizedProducts"
                    v-if="preAuthorizedProductsShow === false"
                ></u-icon>
                <u-icon
                    class="right-icon uicon-arrow-down"
                    name="arrow-down"
                    @click="clickAuthorizedProducts"
                    v-if="preAuthorizedProductsShow"
                ></u-icon>
            </div>
            <div class="terminationByETC-content-wrap">
                <div class="title-left">预授权金额</div>
                <div class="title-right">
                    <input
                        @click="clickAuthorizedAmount"
                        class="center"
                        maxlength="0"
                        disabled
                        placeholder="请选择预授权金额"
                        v-model="authorizedAmountLabel"
                    />
                </div>
                <u-select
                    v-model="authorizedAmountShow"
                    mode="mutil-column-auto"
                    :list="preAuthorizationAmount"
                    @confirm="authorizedAmountConfirm"
                ></u-select>
                <u-icon class="right-icon uicon-arrow-right" name="arrow-right" v-if="authorizedAmountShow === false"></u-icon>
                <u-icon class="right-icon uicon-arrow-down" name="arrow-down" v-if="authorizedAmountShow"></u-icon>
            </div>
        </div>
        <view class="addBtn-wrap">
            <div class="addBtn" @click="removeETC">解除签约</div>
        </view>
        <zj-show-modal></zj-show-modal>
    </div>
</template>
<script>
import pageConfig from '@/utils/pageConfig.js';
import { etcUpdate, etcRemove } from '@/api/ETC.js';
export default {
    name: 'terminationByETC',
    data() {
        return {
            pageConfig,
            preAuthorizedProducts: [
                {
                    value: '92#汽油',
                    label: '92#汽油',
                },
                {
                    value: '95#汽油',
                    label: '95#汽油',
                },
                {
                    value: '98#汽油',
                    label: '98#汽油',
                },
                {
                    value: '0#柴油',
                    label: '0#柴油',
                },
                {
                    value: '-10#柴油',
                    label: '-10#柴油',
                },
                {
                    value: '-20#柴油',
                    label: '-20#柴油',
                },
                {
                    value: '-35#柴油',
                    label: '-35#柴油',
                },
            ], // 预授权油品
            preAuthorizationAmount: [
                { value: '200', label: '200' },
                { value: '400', label: '400' },
                { value: '600', label: '600' },
                { value: '800', label: '800' },
                { value: '1000', label: '1000' },
                { value: '1200', label: '1200' },
                { value: '1400', label: '1400' },
                { value: '1600', label: '1600' },
                { value: '1800', label: '1800' },
                { value: '2000', label: '2000' },
            ], // 预授权金额
            authorizedAmountShow: false,
            preAuthorizedProductsShow: false,
            carData: {},
            licensePlateColorList: [
                {
                    value: '0',
                    label: '蓝色',
                },
                {
                    value: '1',
                    label: '黄色',
                },
                {
                    value: '2',
                    label: '黑色',
                },
                {
                    value: '3',
                    label: '白色',
                },
                {
                    value: '4',
                    label: '渐变绿色',
                },
                {
                    value: '5',
                    label: '黄绿双拼色',
                },
                {
                    value: '6',
                    label: '蓝白渐变色',
                },
                {
                    value: '7',
                    label: '绿色',
                },
                {
                    value: '8',
                    label: '红色',
                },
                {
                    value: '9',
                    label: '未确定',
                },
            ], // 车牌颜色
            carNoColor: '', // 车牌颜色
            authorizedProductsLabel: '', // 预授权油品
            authorizedAmountLabel: '', // 预授权金额
            authorizedProductsValue: '', // 预授权油品
            authorizedAmountValue: '', // 预授权金额
        };
    },
    onLoad(options) {
        this.carData = JSON.parse(options.carData);
        console.log(this.carData, 'this.carData');
        this.authorizedProductsLabel = this.carData.agreementOil;
        this.authorizedAmountLabel = this.carData.agreementAmount;
        for (const key in this.carData) {
            this.carData[key] = this.carData[key] || '无';
        }
        this.echoData();
    },
    mounted() {},
    methods: {
        echoData() {
            // 回显车牌
            this.licensePlateColorList.forEach(element => {
                element.value === this.carData.carNoColor ? (this.carNoColor = element.label) : '无';
            });
        },
        authorizedProductsConfirm(e) {
            uni.showModal({
                title: '提示',
                content: `您确定切换预授权油品为${e[0].value}吗`,
                confirmColor: '#FF8200',
                showCancel: true,
                success: res => {
                    if (res.confirm) {
                        this.updateCarEtcData(e, 'oli');
                    } else if (res.cancel) {
                        return;
                    }
                },
            });
        },
        authorizedAmountConfirm(e) {
            uni.showModal({
                title: '提示',
                content: `您确定切换预授权金额为${e[0].value}吗`,
                confirmColor: '#FF8200',
                showCancel: true,
                success: res => {
                    if (res.confirm) {
                        this.updateCarEtcData(e, 'amount');
                    } else if (res.cancel) {
                        return;
                    }
                },
            });
        },
        updateCarEtcData(e, flag) {
            // 修改油品或金额
            let params = {
                carNo: this.carData.carNo, // 车牌号
                agreementAmount: flag === 'amount' ? e[0].value : this.authorizedAmountLabel, // 预约金额
                agreementOil: flag === 'oli' ? e[0].value : this.authorizedProductsLabel, // 预约油品
                carNoColor: this.carData.carNoColor, // 车牌颜色
            };
            etcUpdate(params).then(res => {
                if (res.status === 0) {
                    if (flag === 'oli') {
                        this.authorizedProductsLabel = e[0].label;
                        this.authorizedProductsValue = e[0].value;
                    } else if (flag === 'amount') {
                        this.authorizedAmountLabel = e[0].label;
                        this.authorizedAmountValue = e[0].value;
                    }
                }
            });
        },
        clickAuthorizedProducts() {
            this.preAuthorizedProductsShow = !this.preAuthorizedProductsShow;
        },
        clickAuthorizedAmount() {
            this.authorizedAmountShow = !this.authorizedAmountShow;
        },
        removeETC() {
            // 解约ETC
            /**
       * agreementAmount: 800
         agreementOil: "-20#柴油"
         carBrand: "无"
         carColor: "无"
         carNo: "冀A45678"
         carNoColor: "0"
         idNo: "15042***********13"
         userName: "*占强"
       */
            let params = {
                carNo: this.carData.carNo,
                carNoColor: this.carData.carNoColor,
                carColor: this.carData.carColor,
            };
            uni.showModal({
                title: '提示',
                content: `您确定要解约当前车辆的ETC吗`,
                confirmColor: '#FF8200',
                showCancel: true,
                success: res => {
                    if (res.confirm) {
                        etcRemove(params).then(res => {
                            if (res.status === 0) {
                                uni.navigateBack({
                                    delta: 1, //返回层数，2则上上页
                                });
                            }
                        });
                    } else if (res.cancel) {
                        return;
                    }
                },
            });
        },
    },
    components: {},
    filter: {
        // licensePlateColor (val) {
        //   switch (val) {
        //     case "":
        //       //这里是指定值对应的处理
        //       break
        //     case "":
        //       //这里是指定值对应的处理
        //       break
        //     default:
        //       //这里是没有找到对应的值处理
        //       break
        //   }
        // }
    },
};
</script>
<style scoped lang="scss">
.terminationByETC {
    width: 100%;
    height: 100vh;
    background-color: #efefef;
    .terminationByETC-wrap {
        margin: 15px;
        background: #fff;
        .terminationByETC-content-wrap {
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: 15px;
            padding-right: 15px;
            .title-left {
                width: 100%;
            }
            .title-right {
                width: 100%;
                text-align: right;
            }
            .center {
                text-align: right;
            }
        }
    }
    .addBtn-wrap {
        // position: absolute;
        bottom: 0;
        width: 100%;
        padding: 10px;
        border-radius: 4px;
        .addBtn {
            width: 100%;
            height: 46px;
            margin: 0 auto;
            font-size: 16px;
            background-color: #fff;
            color: #f96702;
            text-align: center;
            line-height: 46px;
            margin-bottom: 10px;
            border-radius: 8px;
        }
    }
    /**
  
  */
}
</style>
