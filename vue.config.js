const path = require('path');
const ZjyPack = require('./build/zjyPack');
const prd = process.env.NODE_ENV === 'production';
const { baseImgUrl, baseType } = require('./project.config');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

let UniPackageOptimization;
if (process.env.UNI_PLATFORM !== 'h5' && process.env.UNI_PLATFORM === 'mp-weixin') {
    UniPackageOptimization = require('subpackage-optimize');
}

module.exports = {
    publicPath: prd ? './' : '/',
    assetsDir: 'static',
    devServer: {
        host: '0.0.0.0',
        port: 8080,
        public: '***************:8080', //设置访问ip端口
        // public: '************:8080', //设置访问ip端口
        // proxy: {
        // 	"/api": {
        // 		target: "https://hkyznp.kunlunjyk.com:10443/gsms/mgw.htm",
        // 		changeOrigin: true, //允许跨域
        // 		pathRewrite: {
        //     '^/api': ''
        //             },
        // 	},
        // },
    },
    productionSourceMap: false,
    configureWebpack: {
        resolve: {
            alias: {
                assets: '@/assets',
                components: '@/components',
                libs: '@/libs',
                views: '@/views',
            },
        },

        plugins: [
            prd
                ? new UglifyJsPlugin({
                    // parallel: true,
                    uglifyOptions: {
                        // ie8: false,
                        // ecma: 6,
                        // warnings: false,
                        // mangle: true,
                        // output: {
                        //     comments: false,
                        //     beautify: false,
                        //     // debug true
                        // },
                        compress: {
                            drop_console: baseType === 'prd', // 生产包移除console
                            collapse_vars: true,
                            reduce_vars: true,
                        },
                    },
                })
                : () => { },
            new ZjyPack(),
            UniPackageOptimization ? new UniPackageOptimization(['s-kit','components']) : () => { },
        ],
    },

    transpileDependencies: ['uni-simple-router', 'webpack-dev-server/client', 'uview-ui'],

    chainWebpack: config => {
        // config.plugin('define').tap(args => {
        //     args[0]['process.env'].VUE_APP_TEST = '';
        //     return args;
        // });
        const includeDir = path.resolve(__dirname, 'node_modules');
        config.module
            .rule('string-replace-loader')
            // .test(/\.css$|\.scss$|\.js$|\.vue$|\.ts$/)
            // .exclude(/node_modules/)
            // .include(includeDir)
            .enforce('pre')
            .resource(_path => {
                return _path.includes(includeDir);
            })
            .use('string-replace-loader')
            .loader('string-replace-loader')
            .tap(_ => {
                return {
                    // 暂时固定写死，可做自定义平台替换优化
                    search: 'MP-ALIPAY',
                    replace: (match, p1, offset, string) => {
                        return `MP-ALIPAY || MP-MPAAS`;
                    },
                    flags: 'g',
                };
            });
        config.module
            .rule('compile-mode-loader')
            .test(/\.css$|\.scss$|\.js$|\.vue$|\.ts$/)
            .use('./build/loader/compile-mode-loader.js')
            .loader('./build/loader/compile-mode-loader.js');
        //  config
        // 	.module
        // 	.rule('pack-loader')
        // 	.test(/\.css$|\.scss$|\.js$|\.vue$/)
        // 	.use('./build/loader/packLoader.js')
        // 	.loader('./build/loader/packLoader.js')
        config.module
            .rule('ts-loader')
            .test(/\.(tsx|ts)$/)
            .use('ts-loader')
            .loader('ts-loader')
            .options({
                appendTsSuffixTo: [/\.vue$/],
                transpileOnly: true, // 只做语言转化，而不做类型检验
                configFile: path.resolve(__dirname, './tsconfig.json'),
            });
        config.module
            .rule('vue')
            .use('vue-loader')
            .loader('vue-loader')
            .tap(options => {
                options.transformAssetUrls = {
                    audio: 'src',
                };
                return options;
            });
        config.module
            .rule('scss')
            .oneOf('vue')
            .use('px2rpx-loader')
            .loader('px2rpx-loader')
            .options({
                baseDpr: 1,
                rpxUnit: 0.5,
            })
            .before('postcss-loader')
            .end();

        config.module
            .rule('images')
            .use('url-loader')
            .loader('url-loader')
            .tap(options => {
                options.limit = 1;
                if (
                    (process.env.UNI_PLATFORM === 'mp-weixin' && prd) ||
                    process.env.UNI_PLATFORM === 'mp-alipay' ||
                    process.env.UNI_PLATFORM === 'h5'
                ) {
                    options.esModule = false;
                    options.fallback = {
                        loader: 'file-loader',
                        options: {
                            name(file) {
                                return 'static/img/[name].[hash:8].[ext]';
                            },
                            emitFile: false,
                            publicPath: baseImgUrl + '/uniapp/uni-mall',
                        },
                    };
                }
                return options;
            });
    },
};
